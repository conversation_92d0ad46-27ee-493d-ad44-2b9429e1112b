"""
Database connection pool management for async operations
Optional: Use for direct async database access
"""

import asyncio
import logging
from contextlib import asynccontextmanager

import asyncpg
from django.conf import settings

from ..config import DB_COMMAND_TIMEOUT, DB_POOL_MAX_SIZE, DB_POOL_MIN_SIZE

logger = logging.getLogger(__name__)

class AsyncDatabasePool:
    """
    Async database connection pool manager
    Use when Django ORM is not sufficient
    """
    
    def __init__(self) -> None:
        self.pool: asyncpg.Pool | None = None
        self._lock = asyncio.Lock()
    
    async def initialize(self):
        """Initialize the connection pool"""
        if self.pool is not None:
            return
        
        async with self._lock:
            if self.pool is not None:
                return
            
            # Get database config from Django
            db_config = settings.DATABASES["default"]
            
            # Create pool
            self.pool = await asyncpg.create_pool(
                host=db_config.get("HOST", "localhost"),
                port=db_config.get("PORT", 5432),
                user=db_config["USER"],
                password=db_config["PASSWORD"],
                database=db_config["NAME"],
                min_size=DB_POOL_MIN_SIZE,
                max_size=DB_POOL_MAX_SIZE,
                command_timeout=DB_COMMAND_TIMEOUT,
                statement_cache_size=0,  # Disable for compatibility
            )
            
            logger.info(
                f"✅ Async database pool initialized "
                f"(min={DB_POOL_MIN_SIZE}, max={DB_POOL_MAX_SIZE})"
            )
    
    async def close(self):
        """Close the connection pool"""
        if self.pool:
            await self.pool.close()
            self.pool = None
            logger.info("Database pool closed")
    
    @asynccontextmanager
    async def acquire(self):
        """Acquire a connection from the pool"""
        if not self.pool:
            await self.initialize()
        
        async with self.pool.acquire() as connection:
            yield connection
    
    async def execute(self, query: str, *args, timeout: float | None = None):
        """Execute a query"""
        async with self.acquire() as conn:
            return await conn.execute(query, *args, timeout=timeout)
    
    async def fetch(self, query: str, *args, timeout: float | None = None):
        """Fetch multiple rows"""
        async with self.acquire() as conn:
            return await conn.fetch(query, *args, timeout=timeout)
    
    async def fetchrow(self, query: str, *args, timeout: float | None = None):
        """Fetch a single row"""
        async with self.acquire() as conn:
            return await conn.fetchrow(query, *args, timeout=timeout)
    
    async def fetchval(self, query: str, *args, timeout: float | None = None):
        """Fetch a single value"""
        async with self.acquire() as conn:
            return await conn.fetchval(query, *args, timeout=timeout)

# Global pool instance
_db_pool = AsyncDatabasePool()

async def get_db_pool() -> AsyncDatabasePool:
    """Get the global database pool instance"""
    if not _db_pool.pool:
        await _db_pool.initialize()
    return _db_pool