"""
Database helper functions with proper decorators
These should be used instead of direct ORM calls
"""


from ..decorators.database import async_db_operation, track_sync_calls

# Instagram helpers

@async_db_operation
@track_sync_calls
def get_instagram_profile(username: str):
    """Get Instagram profile by username"""
    from instagram_manager.models import InstagramProfile
    return InstagramProfile.objects.filter(
        username__iexact=username
    ).first()

@async_db_operation
@track_sync_calls
def list_instagram_profiles(
    limit: int = 20,
    offset: int = 0,
    **filters
) -> tuple[list, int]:
    """List Instagram profiles with pagination"""
    from instagram_manager.models import InstagramProfile
    
    queryset = InstagramProfile.objects.all()
    
    # Apply filters
    if filters.get("is_active") is not None:
        queryset = queryset.filter(is_active=filters["is_active"])
    if filters.get("is_verified") is not None:
        queryset = queryset.filter(is_verified=filters["is_verified"])
    
    # Get total count
    total = queryset.count()
    
    # Get paginated results
    profiles = list(queryset[offset:offset + limit])
    
    return profiles, total

@async_db_operation
@track_sync_calls
def get_instagram_posts(
    username: str,
    limit: int = 20,
    offset: int = 0,
    **filters
) -> list:
    """Get posts for Instagram profile"""
    from instagram_manager.models import InstagramPost
    
    queryset = InstagramPost.objects.filter(
        profile__username__iexact=username
    ).select_related("profile").prefetch_related("media", "hashtags")
    
    # Apply filters
    if filters.get("post_type"):
        queryset = queryset.filter(post_type=filters["post_type"])
    if filters.get("posted_at__gte"):
        queryset = queryset.filter(posted_at__gte=filters["posted_at__gte"])
    if filters.get("posted_at__lte"):
        queryset = queryset.filter(posted_at__lte=filters["posted_at__lte"])
    
    # Order and paginate
    queryset = queryset.order_by("-posted_at")
    
    return list(queryset[offset:offset + limit])

# Telegram helpers

@async_db_operation
@track_sync_calls
def list_telegram_chats(
    limit: int = 20,
    offset: int = 0,
    **filters
) -> tuple[list, int]:
    """List Telegram chats with pagination"""
    from telegram_manager.models import TelegramChat
    
    queryset = TelegramChat.objects.all()
    
    # Apply filters
    if filters.get("chat_type"):
        queryset = queryset.filter(chat_type=filters["chat_type"])
    
    # Get total count
    total = queryset.count()
    
    # Get paginated results
    chats = list(queryset[offset:offset + limit])
    
    return chats, total

@async_db_operation
@track_sync_calls  
def get_telegram_messages(
    chat_id: int,
    limit: int = 50,
    offset: int = 0,
    **filters
) -> list:
    """Get messages from Telegram chat"""
    from telegram_manager.models import TelegramMessage
    
    queryset = TelegramMessage.objects.filter(
        chat__chat_id=chat_id
    ).select_related("chat", "from_user")
    
    # Apply filters
    if filters.get("date__gte"):
        queryset = queryset.filter(date__gte=filters["date__gte"])
    if filters.get("date__lte"):
        queryset = queryset.filter(date__lte=filters["date__lte"])
    
    # Order and paginate
    queryset = queryset.order_by("-date")
    
    return list(queryset[offset:offset + limit])

# Task helpers

@async_db_operation
@track_sync_calls
def get_task_result(task_id: str):
    """Get task result by ID"""
    from core.models import TaskResult
    return TaskResult.objects.filter(task_id=task_id).first()

@async_db_operation
@track_sync_calls
def list_task_results(
    status: str | None = None,
    task_type: str | None = None,
    limit: int = 20,
    offset: int = 0
) -> tuple[list, int]:
    """List task results with filtering"""
    from core.models import TaskResult
    
    queryset = TaskResult.objects.all()
    
    if status:
        queryset = queryset.filter(status=status)
    if task_type:
        queryset = queryset.filter(task_type=task_type)
    
    # Order by priority and creation date
    queryset = queryset.order_by("-priority", "-created_at")
    
    total = queryset.count()
    tasks = list(queryset[offset:offset + limit])
    
    return tasks, total

@async_db_operation
@track_sync_calls
def update_task_status(task_id: str, status: str, **kwargs):
    """Update task status"""
    from core.models import TaskResult
    
    task = TaskResult.objects.get(task_id=task_id)
    task.status = status
    
    # Update optional fields
    for key, value in kwargs.items():
        if hasattr(task, key):
            setattr(task, key, value)
    
    task.save()
    return task