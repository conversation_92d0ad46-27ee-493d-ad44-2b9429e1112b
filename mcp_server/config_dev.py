"""
Development configuration overrides
"""

from .config import *  # noqa: F403, F405

# Override for development
DEBUG = True
LOG_LEVEL = "DEBUG"

# Use local Django settings
import os  # noqa: E402

os.environ["DJANGO_SETTINGS_MODULE"] = "SocialManager.settings.local"

# Development server settings
MCP_SERVER_HOST = "127.0.0.1"

# Disable some validations for faster startup
VALIDATE_ON_STARTUP = False

# More verbose logging
SLOW_QUERY_THRESHOLD = 0.05  # 50ms for development