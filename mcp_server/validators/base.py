"""
Base validation models and utilities
"""

from datetime import datetime

from pydantic import BaseModel, ConfigDict, Field, field_validator


class BaseTaskParams(BaseModel):
    """Base class for all task parameter models"""
    model_config = ConfigDict(
        str_strip_whitespace=True,
        validate_assignment=True,
        use_enum_values=True,
        json_schema_extra={"example": {}}
    )

class PaginationParams(BaseModel):
    """Common pagination parameters"""
    limit: int = Field(20, ge=1, le=100, description="Number of items to return")
    offset: int = Field(0, ge=0, description="Number of items to skip")

class DateRangeParams(BaseModel):
    """Common date range parameters"""
    start_date: datetime | None = Field(None, description="Start date (inclusive)")
    end_date: datetime | None = Field(None, description="End date (inclusive)")
    
    @field_validator("end_date")
    @classmethod
    def validate_date_range(cls, v: datetime | None, info) -> datetime | None:
        """Ensure end_date is after start_date"""
        if v and info.data.get("start_date"):
            if v < info.data["start_date"]:
                raise ValueError("end_date must be after start_date")
        return v

def normalize_username(username: str) -> str:
    """Normalize social media username"""
    return username.strip().lower().lstrip("@")

def validate_username_list(usernames: list[str]) -> list[str]:
    """Validate and normalize list of usernames"""
    if not usernames:
        raise ValueError("At least one username is required")
    
    # Normalize all usernames
    normalized = [normalize_username(u) for u in usernames]
    
    # Remove duplicates while preserving order
    seen = set()
    unique = []
    for u in normalized:
        if u not in seen:
            seen.add(u)
            unique.append(u)
    
    return unique