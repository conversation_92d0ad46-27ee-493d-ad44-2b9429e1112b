"""
Task validation models for MCP server
"""

from datetime import datetime
from typing import Any

from pydantic import Field, field_validator

from .base import BaseTaskParams, normalize_username, validate_username_list
from .roast import RoastProfileTaskParams


class TaskCreateParams(BaseTaskParams):
    """Task creation parameters"""
    task_type: str = Field(..., description="Task type identifier")
    parameters: dict[str, Any] = Field(..., description="Task-specific parameters")
    priority: str = Field(default="normal", pattern="^(low|normal|high)$")
    
    @field_validator("task_type")
    @classmethod
    def validate_task_type(cls, v: str) -> str:
        """Validate task type against registry"""
        valid_types = [
            "instagram.profile",
            "instagram.posts", 
            "instagram.batch_posts",
            "instagram.comments",
            "instagram.followers",
            "instagram.roast_profile",
            "telegram.chats",
            "telegram.messages",
            "telegram.users"
        ]
        if v not in valid_types:
            raise ValueError(f"Invalid task type: {v}. Valid types: {', '.join(valid_types)}")
        return v


class TaskListParams(BaseTaskParams):
    """Task listing parameters"""
    status: str | None = Field(None, pattern="^(pending|running|completed|failed|cancelled|retry)$")
    task_type: str | None = None
    limit: int = Field(default=20, ge=1, le=100)
    offset: int = Field(default=0, ge=0)
    created_after: datetime | None = None
    created_before: datetime | None = None
    
    @field_validator("created_before")
    @classmethod
    def validate_date_range(cls, v: datetime | None, info) -> datetime | None:
        """Ensure created_before is after created_after"""
        if v and info.data.get("created_after"):
            if v < info.data["created_after"]:
                raise ValueError("created_before must be after created_after")
        return v


# Task-specific parameter validators

class InstagramProfileTaskParams(BaseTaskParams):
    """Instagram profile import task parameters"""
    username: str = Field(..., min_length=1, max_length=30)
    include_posts: bool = Field(default=True)
    include_stories: bool = Field(default=False)
    
    @field_validator("username")
    @classmethod
    def normalize_username(cls, v: str) -> str:
        """Normalize Instagram username"""
        return normalize_username(v)


class InstagramPostsTaskParams(BaseTaskParams):
    """Instagram posts import task parameters"""
    username: str = Field(..., min_length=1, max_length=30)
    limit: int = Field(default=50, ge=1, le=200)
    include_comments: bool = Field(default=True)
    include_media: bool = Field(default=True)
    
    @field_validator("username")
    @classmethod
    def normalize_username(cls, v: str) -> str:
        """Normalize Instagram username"""
        return normalize_username(v)


class InstagramBatchPostsTaskParams(BaseTaskParams):
    """Instagram batch posts import task parameters"""
    usernames: list[str] = Field(..., min_length=1, max_length=20)
    limit_per_user: int = Field(default=50, ge=1, le=100)
    include_comments: bool = Field(default=True)
    include_media: bool = Field(default=True)
    
    @field_validator("usernames")
    @classmethod
    def validate_usernames(cls, v: list[str]) -> list[str]:
        """Validate and normalize username list"""
        return validate_username_list(v)


class InstagramCommentsTaskParams(BaseTaskParams):
    """Instagram comments import task parameters"""
    username: str = Field(..., min_length=1, max_length=30)
    post_limit: int = Field(default=10, ge=1, le=50)
    comment_limit_per_post: int = Field(default=100, ge=1, le=500)
    include_replies: bool = Field(default=True)
    
    @field_validator("username")
    @classmethod
    def normalize_username(cls, v: str) -> str:
        """Normalize Instagram username"""
        return normalize_username(v)


class InstagramFollowersTaskParams(BaseTaskParams):
    """Instagram followers import task parameters"""
    username: str = Field(..., min_length=1, max_length=30)
    limit: int = Field(default=1000, ge=1, le=10000)
    include_following: bool = Field(default=True)
    
    @field_validator("username")
    @classmethod
    def normalize_username(cls, v: str) -> str:
        """Normalize Instagram username"""
        return normalize_username(v)


class TelegramChatsTaskParams(BaseTaskParams):
    """Telegram chats import task parameters"""
    limit: int = Field(default=50, ge=1, le=200)
    include_channels: bool = Field(default=True)
    include_groups: bool = Field(default=True)
    include_private: bool = Field(default=False)
    only_active: bool = Field(default=True)


class TelegramMessagesTaskParams(BaseTaskParams):
    """Telegram messages import task parameters"""
    chat_id: int = Field(..., description="Telegram chat ID")
    limit: int = Field(default=100, ge=1, le=1000)
    offset_id: int | None = Field(None, description="Message ID to start from")
    start_date: datetime | None = None
    end_date: datetime | None = None
    skip_user_fetch: bool = Field(default=False, description="Skip fetching user details")
    
    @field_validator("end_date")
    @classmethod
    def validate_date_range(cls, v: datetime | None, info) -> datetime | None:
        """Ensure end_date is after start_date"""
        if v and info.data.get("start_date"):
            if v < info.data["start_date"]:
                raise ValueError("end_date must be after start_date")
        return v


class TelegramUsersTaskParams(BaseTaskParams):
    """Telegram users import task parameters"""
    chat_id: int = Field(..., description="Telegram chat ID")
    limit: int = Field(default=100, ge=1, le=500)
    only_active: bool = Field(default=True, description="Import only active users")
    include_bots: bool = Field(default=False, description="Include bot accounts")


# Parameter validation registry
TASK_PARAM_VALIDATORS = {
    "instagram.profile": InstagramProfileTaskParams,
    "instagram.posts": InstagramPostsTaskParams,
    "instagram.batch_posts": InstagramBatchPostsTaskParams,
    "instagram.comments": InstagramCommentsTaskParams,
    "instagram.followers": InstagramFollowersTaskParams,
    "instagram.roast_profile": RoastProfileTaskParams,
    "telegram.chats": TelegramChatsTaskParams,
    "telegram.messages": TelegramMessagesTaskParams,
    "telegram.users": TelegramUsersTaskParams,
}


def validate_task_parameters(task_type: str, parameters: dict) -> dict[str, Any]:
    """
    Validate task parameters using appropriate validator
    
    Args:
        task_type: Type of task
        parameters: Raw parameters dict
        
    Returns:
        Validated parameters dict
        
    Raises:
        ValueError: If task type is unknown
        ValidationError: If parameters are invalid
    """
    if task_type not in TASK_PARAM_VALIDATORS:
        raise ValueError(f"Unknown task type: {task_type}")
    
    validator_class = TASK_PARAM_VALIDATORS[task_type]
    validated = validator_class(**parameters)
    return validated.model_dump()  # type: ignore[no-any-return]