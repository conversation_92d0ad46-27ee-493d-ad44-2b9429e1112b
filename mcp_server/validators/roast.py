"""
Validators for Instagram roast functionality
"""

import re

from pydantic import Field, field_validator

from .base import BaseTaskParams


class RoastProfileTaskParams(BaseTaskParams):
    """Parameters for roast Instagram profile task"""
    
    profile_url: str = Field(
        ..., 
        pattern=r"^https://www\.instagram\.com/[a-zA-Z0-9_.]+/?$",
        description="Instagram profile URL"
    )
    post_count: int = Field(
        default=10, 
        ge=1, 
        le=100,
        description="Number of posts to analyze"
    )
    
    @field_validator("profile_url")
    @classmethod
    def validate_and_extract_username(cls, v: str) -> str:
        """Validate URL format and return as-is"""
        # Pattern validation is already done by Field
        # Just return the URL as-is
        return v
    
    def extract_username(self) -> str:
        """Extract username from URL"""
        match = re.search(r"instagram\.com/([a-zA-Z0-9_.]+)", self.profile_url)
        if match:
            return match.group(1).lower()
        raise ValueError("Invalid Instagram URL")