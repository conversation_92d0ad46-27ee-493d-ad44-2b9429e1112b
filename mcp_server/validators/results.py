"""
Results validation models
"""

from datetime import datetime
from typing import Literal

from pydantic import BaseModel, Field, field_validator


class ResultsExportParams(BaseModel):
    """Export parameters validation"""
    task_ids: list[str] = Field(..., min_length=1, max_length=100)
    format: Literal["json"] = Field(default="json")
    include_metadata: bool = Field(default=True)
    compress: bool = Field(default=False)
    
    @field_validator("task_ids")
    @classmethod
    def validate_task_ids(cls, v):
        # Validate UUID format
        import uuid
        for task_id in v:
            try:
                uuid.UUID(task_id)
            except ValueError:
                raise ValueError(f"Invalid task ID format: {task_id}")
        return v


class ResultsSearchParams(BaseModel):
    """Search parameters validation"""
    query: str = Field(..., min_length=2, max_length=200)
    task_type: str | None = None
    status: Literal["completed", "failed", "cancelled"] | None = None
    date_from: datetime | None = None
    date_to: datetime | None = None
    limit: int = Field(default=20, ge=1, le=100)