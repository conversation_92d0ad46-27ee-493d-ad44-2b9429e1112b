"""
Telegram-specific parameter validation models
"""

from datetime import datetime

from pydantic import Field, field_validator, model_validator

from .base import BaseTaskParams


class TelegramChatsTaskParams(BaseTaskParams):
    """Parameters for importing Telegram chats"""
    chat_limit: str = Field(
        "all",
        description="Number of chats to import ('all' or number)"
    )
    custom_limit: int | None = Field(
        None,
        ge=1,
        description="Custom limit when chat_limit is 'custom'"
    )
    include_private: bool = Field(True, description="Include private chats")
    include_groups: bool = Field(True, description="Include group chats")
    include_supergroups: bool = Field(True, description="Include supergroups")
    include_channels: bool = Field(True, description="Include channels")
    
    @field_validator("custom_limit")
    @classmethod
    def validate_custom_limit(cls, v: int | None, info) -> int | None:
        """Validate custom limit when required"""
        if info.data.get("chat_limit") == "custom" and not v:
            raise ValueError("custom_limit required when chat_limit is 'custom'")
        return v

class TelegramMessagesTaskParams(BaseTaskParams):
    """Parameters for importing Telegram messages"""
    category: str = Field(
        "all",
        pattern="^(all|today|week|month|custom)$",
        description="Time range category"
    )
    date_from: datetime | None = Field(None, description="Start date for custom range")
    date_to: datetime | None = Field(None, description="End date for custom range")
    message_limit: int = Field(
        1000,
        ge=1,
        le=100000,
        description="Maximum messages per chat"
    )
    skip_user_fetch: bool = Field(
        False,
        description="Skip fetching user details"
    )
    
    @model_validator(mode="after")
    def validate_dates(self):
        """Validate dates for custom category"""
        if self.category == "custom":
            if not self.date_from or not self.date_to:
                raise ValueError("Both date_from and date_to required for custom category")
        return self

class TelegramUsersTaskParams(BaseTaskParams):
    """Parameters for importing Telegram users"""
    extract_from_messages: bool = Field(
        False,
        description="Extract users from existing messages"
    )
    from_chat: int | None = Field(
        None,
        description="Import users from specific chat ID"
    )
    update_existing: bool = Field(
        False,
        description="Update existing user records"
    )
    limit: int | None = Field(
        None,
        ge=1,
        description="Maximum users to import"
    )


class ChatTypeValidator:
    """Validator for Telegram chat types"""
    
    def __init__(self):
        self.allowed_types = ["private", "group", "supergroup", "channel"]
    
    def validate_value(self, value: str) -> bool:
        """Check if the chat type is valid"""
        return value in self.allowed_types