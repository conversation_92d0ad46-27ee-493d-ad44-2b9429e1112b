"""
Instagram-specific parameter validation models
"""

from datetime import datetime

from pydantic import BaseModel, Field, field_validator

from .base import BaseTaskParams, normalize_username, validate_username_list

# Instagram username constraints
INSTAGRAM_USERNAME_MIN = 1
INSTAGRAM_USERNAME_MAX = 30
INSTAGRAM_MAX_BATCH_SIZE = 50

class InstagramProfileTaskParams(BaseTaskParams):
    """Parameters for importing a single Instagram profile"""
    username: str = Field(
        ..., 
        min_length=INSTAGRAM_USERNAME_MIN,
        max_length=INSTAGRAM_USERNAME_MAX,
        description="Instagram username to import"
    )
    
    @field_validator("username")
    @classmethod
    def normalize_username(cls, v: str) -> str:
        return normalize_username(v)

class InstagramPostsTaskParams(BaseTaskParams):
    """Parameters for importing posts from a single profile"""
    username: str = Field(
        ...,
        min_length=INSTAGRAM_USERNAME_MIN,
        max_length=INSTAGRAM_USERNAME_MAX,
        description="Instagram username"
    )
    limit: int | None = Field(100, ge=1, le=1000, description="Maximum posts to import")
    start_date: datetime | None = Field(None, description="Import posts from this date")
    end_date: datetime | None = Field(None, description="Import posts until this date")
    post_types: list[str] | None = Field(
        default_factory=list,
        description="Filter by post types (photo, video, album, reel)"
    )
    
    @field_validator("username")
    @classmethod
    def normalize_username(cls, v: str) -> str:
        return normalize_username(v)
    
    @field_validator("post_types")
    @classmethod
    def validate_post_types(cls, v: list[str]) -> list[str]:
        valid_types = {"photo", "video", "album", "reel", "story"}
        for post_type in v:
            if post_type not in valid_types:
                raise ValueError(f"Invalid post type: {post_type}")
        return v

class InstagramBatchPostsTaskParams(BaseTaskParams):
    """Parameters for batch importing posts from multiple profiles"""
    usernames: list[str] = Field(
        ...,
        min_length=1,
        max_length=INSTAGRAM_MAX_BATCH_SIZE,
        description="List of Instagram usernames"
    )
    batch_size: int = Field(
        10,
        ge=1,
        le=20,
        description="Number of profiles to process in parallel"
    )
    limit: int | None = Field(
        None,
        ge=1,
        le=1000,
        description="Maximum posts per profile"
    )
    start_date: datetime | None = None
    end_date: datetime | None = None
    post_types: list[str] | None = Field(default_factory=list)
    import_comments: bool = Field(False, description="Import comments for posts")
    skip_media_download: bool = Field(False, description="Skip downloading media files")
    save_media_to_gcs: bool = Field(False, description="Save media to Google Cloud Storage")
    import_all_users: bool = Field(False, description="Import all mentioned users")
    
    @field_validator("usernames")
    @classmethod
    def validate_usernames(cls, v: list[str]) -> list[str]:
        return validate_username_list(v)

class InstagramCommentsTaskParams(BaseTaskParams):
    """Parameters for importing comments for posts"""
    post_id: int = Field(..., gt=0, description="Instagram post ID")
    limit: int | None = Field(100, ge=1, le=1000, description="Maximum comments to import")
    include_replies: bool = Field(True, description="Include comment replies")
    
class PostCommentParams(BaseModel):
    """Parameters for posting a comment"""
    username: str = Field(..., min_length=1, description="Instagram account username")
    password: str = Field(..., min_length=1, description="Instagram account password")
    post_url: str = Field(..., description="Instagram post URL")
    comment_text: str = Field(
        ...,
        min_length=1,
        max_length=2200,
        description="Comment text to post"
    )
    
    @field_validator("post_url")
    @classmethod
    def validate_post_url(cls, v: str) -> str:
        if not v.startswith("https://www.instagram.com/"):
            raise ValueError("Invalid Instagram post URL")
        return v


class InstagramValidator:
    """Instagram-specific validation methods"""
    
    @staticmethod
    def validate_username(username: str) -> str:
        """Validate and normalize Instagram username"""
        return normalize_username(username)
    
    @staticmethod
    def validate_post_url(url: str) -> str:
        """Validate Instagram post URL"""
        if not url.startswith("https://www.instagram.com/"):
            raise ValueError("Invalid Instagram post URL")
        return url


class CommentFilterParams(BaseModel):
    """Parameters for comment filtering"""
    post_id: int = Field(..., gt=0)
    limit: int = Field(default=50, ge=1, le=100)
    offset: int = Field(default=0, ge=0)
    include_replies: bool = Field(default=True)
    order_by: str = Field(default="-commented_at")
    
    @field_validator("order_by")
    @classmethod
    def validate_order_by(cls, v: str) -> str:
        valid_fields = ["-commented_at", "commented_at", "-like_count", "like_count"]
        if v not in valid_fields:
            raise ValueError(f"Invalid order_by field: {v}")
        return v


class CommentParams(BaseModel):
    """Parameters for posting a comment"""
    username: str = Field(..., min_length=1, max_length=150)
    password: str = Field(..., min_length=1)
    post_url: str = Field(...)
    comment_text: str = Field(..., min_length=1, max_length=2200)
    reply_to_comment_id: int | None = Field(default=None, gt=0)
    
    @field_validator("post_url")
    @classmethod
    def validate_post_url(cls, v: str) -> str:
        return InstagramValidator.validate_post_url(v)


class BulkCommentParams(BaseModel):
    """Parameters for bulk comment posting"""
    username: str = Field(..., min_length=1, max_length=150)
    password: str = Field(..., min_length=1)
    comments: list[dict] = Field(..., min_length=1, max_length=50)
    delay_seconds: int = Field(default=30, ge=10, le=300)
    
    @field_validator("comments")
    @classmethod
    def validate_comments(cls, v: list[dict]) -> list[dict]:
        """Validate each comment"""
        for comment in v:
            if "post_url" not in comment or "comment_text" not in comment:
                raise ValueError("Each comment must have 'post_url' and 'comment_text'")
            InstagramValidator.validate_post_url(comment["post_url"])
            if len(comment["comment_text"]) > 2200:
                raise ValueError("Comment text cannot exceed 2200 characters")
        return v


class CommentStatsParams(BaseModel):
    """Parameters for comment statistics"""
    post_id: int = Field(..., gt=0)