"""
Central registry for all parameter validators
Maps task types to their validation models
"""


from pydantic import BaseModel

# Import all validators
from .instagram import (
    InstagramBatchPostsTaskParams,
    InstagramCommentsTaskParams,
    InstagramPostsTaskParams,
    InstagramProfileTaskParams,
)
from .telegram import TelegramChatsTaskParams, TelegramMessagesTaskParams, TelegramUsersTaskParams

# Parameter validator registry
PARAM_VALIDATORS: dict[str, type[BaseModel]] = {
    # Instagram validators
    "instagram.profile": InstagramProfileTaskParams,
    "instagram.posts": InstagramPostsTaskParams,
    "instagram.batch_posts": InstagramBatchPostsTaskParams,
    "instagram.comments": InstagramCommentsTaskParams,
    
    # Telegram validators
    "telegram.chats": TelegramChatsTaskParams,
    "telegram.messages": TelegramMessagesTaskParams,
    "telegram.users": TelegramUsersTaskParams,
}

def get_validator(task_type: str) -> type[BaseModel]:
    """
    Get validator for task type
    
    Args:
        task_type: Task type identifier
        
    Returns:
        Validator class
        
    Raises:
        KeyError: If no validator found
    """
    if task_type not in PARAM_VALIDATORS:
        raise KeyError(f"No validator found for task type: {task_type}")
    return PARAM_VALIDATORS[task_type]

def validate_task_params(task_type: str, params: dict) -> dict:
    """
    Validate parameters for a task
    
    Args:
        task_type: Task type identifier
        params: Raw parameters dict
        
    Returns:
        Validated parameters dict
        
    Raises:
        ValidationError: If validation fails
        KeyError: If no validator found
    """
    validator_class = get_validator(task_type)
    validated = validator_class(**params)
    return validated.dict(exclude_unset=True)