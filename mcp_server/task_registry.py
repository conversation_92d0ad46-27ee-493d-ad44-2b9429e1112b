"""
Static task registry for MCP server
CRITICAL: All tasks must be imported statically - NO dynamic imports
"""


from core.tasks.base import BaseTask

# Import all task classes
from instagram_manager.tasks.import_tasks import (
    ImportBatchPostsTask,
    ImportInstagramCommentsTask,
    ImportInstagramPostsTask,
    ImportInstagramProfileTask,
)
from instagram_manager.tasks.roast_tasks import RoastProfileTask
from telegram_manager.tasks.import_tasks import (
    ImportTelegramChatsTask,
    ImportTelegramMessagesTask,
    ImportTelegramUsersTask,
)

# Task registry with full type annotations
TASK_REGISTRY: dict[str, type[BaseTask]] = {
    # Instagram tasks
    "instagram.profile": ImportInstagramProfileTask,
    "instagram.posts": ImportInstagramPostsTask,
    "instagram.batch_posts": ImportBatchPostsTask,
    "instagram.comments": ImportInstagramCommentsTask,
    "instagram.roast_profile": RoastProfileTask,
    
    # Telegram tasks
    "telegram.chats": ImportTelegramChatsTask,
    "telegram.messages": ImportTelegramMessagesTask,
    "telegram.users": ImportTelegramUsersTask,
}

# Task descriptions for documentation
TASK_DESCRIPTIONS = {
    "instagram.profile": "Import Instagram profile information",
    "instagram.posts": "Import posts from a single Instagram profile",
    "instagram.batch_posts": "Import posts from multiple Instagram profiles in batch",
    "instagram.comments": "Import comments for Instagram posts",
    "instagram.roast_profile": "Analyze Instagram profile and upload photos to GCS",
    "telegram.chats": "Import Telegram chats/channels",
    "telegram.messages": "Import messages from Telegram chats",
    "telegram.users": "Import users from Telegram chats",
}

def get_task_class(task_type: str) -> type[BaseTask]:
    """
    Get task class by type
    
    Args:
        task_type: Task type identifier
        
    Returns:
        Task class
        
    Raises:
        KeyError: If task type not found
    """
    if task_type not in TASK_REGISTRY:
        raise KeyError(f"Unknown task type: {task_type}")
    return TASK_REGISTRY[task_type]

def list_task_types() -> list[str]:
    """Get list of all available task types"""
    return list(TASK_REGISTRY.keys())

def validate_task_type(task_type: str) -> bool:
    """Check if task type is valid"""
    return task_type in TASK_REGISTRY