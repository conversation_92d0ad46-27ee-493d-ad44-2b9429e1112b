import os

# MCP Server Configuration
MCP_SERVER_NAME = "SocialManager MCP"
MCP_SERVER_VERSION = "1.0.0"
MCP_SERVER_HOST = os.getenv("MCP_HOST", "0.0.0.0")
MCP_SERVER_PORT = int(os.getenv("MCP_PORT", 8000))

# Debug mode
DEBUG = os.getenv("DEBUG", "False").lower() == "true"

# Logging configuration
LOG_LEVEL = os.getenv("MCP_LOG_LEVEL", "INFO" if not DEBUG else "DEBUG")

# Performance settings
SLOW_QUERY_THRESHOLD = 0.1  # 100ms
MAX_CONCURRENT_TASKS = 100

# Connection pool settings
DB_POOL_MIN_SIZE = 10
DB_POOL_MAX_SIZE = 20
DB_COMMAND_TIMEOUT = 60

# Task registry validation
VALIDATE_ON_STARTUP = True