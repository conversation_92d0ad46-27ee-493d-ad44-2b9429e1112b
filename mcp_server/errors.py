"""
Unified error handling for MCP server
All errors follow consistent format for client predictability
"""

import logging
import traceback
from datetime import datetime
from typing import Any

from pydantic import BaseModel, Field, field_serializer

logger = logging.getLogger(__name__)

class MCPError(BaseModel):
    """
    Unified error response format
    All MCP errors must use this format
    """
    error_type: str = Field(..., description="Error category")
    message: str = Field(..., description="Human-readable error message")
    details: dict[str, Any] | None = Field(None, description="Additional error details")
    traceback: str | None = Field(None, description="Stack trace (only in debug mode)")
    timestamp: datetime = Field(default_factory=datetime.now)
    request_id: str | None = Field(None, description="Request correlation ID")
    
    @field_serializer("timestamp")
    def serialize_timestamp(self, value: datetime, _info) -> str:
        return value.isoformat()

# Standard error types
class ErrorTypes:
    """Standard error type constants"""
    VALIDATION_ERROR = "validation_error"
    NOT_FOUND = "not_found"
    AUTHENTICATION_ERROR = "authentication_error"
    PERMISSION_DENIED = "permission_denied"
    INTERNAL_ERROR = "internal_error"
    TASK_ERROR = "task_error"
    INVALID_STATE = "invalid_state"
    RATE_LIMIT = "rate_limit_exceeded"
    TIMEOUT = "timeout"
    CONNECTION_ERROR = "connection_error"
    CONFIGURATION_ERROR = "configuration_error"
    INVALID_TASK_TYPE = "invalid_task_type"
    RETRY_LIMIT_EXCEEDED = "retry_limit_exceeded"

class ValidationErrorDetail(BaseModel):
    """Detail for validation errors"""
    field: str
    message: str
    type: str
    input: Any

def create_error_response(
    error_type: str,
    message: str,
    details: dict[str, Any] | None = None,
    include_traceback: bool = False,
    request_id: str | None = None
) -> dict[str, Any]:
    """
    Create standard error response
    
    Args:
        error_type: One of ErrorTypes constants
        message: Human-readable error message
        details: Additional error details
        include_traceback: Whether to include stack trace
        request_id: Request correlation ID
        
    Returns:
        Error response dict
    """
    error = MCPError(
        error_type=error_type,
        message=message,
        details=details,
        request_id=request_id
    )
    
    if include_traceback:
        error.traceback = traceback.format_exc()
    
    return error.model_dump()

def handle_validation_error(exc: Exception) -> dict[str, Any]:
    """
    Convert Pydantic ValidationError to standard format
    """
    from pydantic import ValidationError
    
    if isinstance(exc, ValidationError):
        details = []
        for error in exc.errors():
            details.append({
                "field": ".".join(str(loc) for loc in error["loc"]),
                "message": error["msg"],
                "type": error["type"],
                "input": error.get("input")
            })
        
        return create_error_response(
            error_type=ErrorTypes.VALIDATION_ERROR,
            message="Invalid parameters provided",
            details={"errors": details}
        )
    
    # Not a validation error
    return create_error_response(
        error_type=ErrorTypes.INTERNAL_ERROR,
        message=str(exc)
    )