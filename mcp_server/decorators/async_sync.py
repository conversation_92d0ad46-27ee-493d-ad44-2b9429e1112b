"""
Decorators for handling async/sync boundaries in Django
Ensures safe ORM operations from async context
"""

import logging
import time
from collections.abc import Callable, Coroutine
from contextvars import ContextVar
from datetime import datetime
from functools import wraps
from typing import Any, ParamSpec, TypeVar

from asgiref.sync import sync_to_async
from django.db import connection, transaction

from ..config import DEBUG, SLOW_QUERY_THRESHOLD

logger = logging.getLogger(__name__)

# Type variables for proper type hints
P = ParamSpec("P")
T = TypeVar("T")

# Context variables for tracking
db_operation_context: ContextVar[str] = ContextVar("db_operation", default="unknown")
db_call_count: ContextVar[int] = ContextVar("db_call_count", default=0)
db_total_time: ContextVar[float] = ContextVar("db_total_time", default=0.0)

def async_db_operation(func: Callable[P, T]) -> Callable[P, Coroutine[Any, Any, T]]:
    """
    Decorator for safe async/sync bridge for Django ORM operations
    
    Features:
    - Automatic transaction management
    - Performance tracking
    - Query time monitoring
    - Connection cleanup
    
    Usage:
        @async_db_operation
        def get_user(user_id: int) -> User:
            return User.objects.get(id=user_id)
    
    Then call from async context:
        user = await get_user(123)
    """
    
    @wraps(func)
    def sync_wrapper(*args: P.args, **kwargs: P.kwargs) -> T:
        """Sync wrapper with transaction and monitoring"""
        operation_name = f"{func.__module__}.{func.__name__}"
        db_operation_context.set(operation_name)
        
        start_time = time.time()
        
        try:
            # Use atomic transaction
            with transaction.atomic():
                logger.debug(
                    f"[TRANSACTION START] Starting atomic transaction in async_db_operation: {operation_name}"
                )
                
                result = func(*args, **kwargs)
                
                logger.debug(
                    f"[TRANSACTION END] Successfully completed transaction in async_db_operation: {operation_name}"
                )
                
            # Track performance
            elapsed = time.time() - start_time
            
            # Update counters
            count = db_call_count.get()
            db_call_count.set(count + 1)
            
            total_time = db_total_time.get()
            db_total_time.set(total_time + elapsed)
            
            # Log slow queries
            if elapsed > SLOW_QUERY_THRESHOLD:
                logger.warning(
                    f"Slow DB operation: {operation_name} "
                    f"took {elapsed:.3f}s"
                )
            
            if DEBUG:
                logger.debug(
                    f"DB operation: {operation_name} "
                    f"completed in {elapsed:.3f}s"
                )
            
            return result
            
        except Exception as e:
            logger.error(
                f"[TRANSACTION ERROR] DB operation failed in transaction: {operation_name} - {e}"
            )
            raise
            
        finally:
            # Ensure connection is closed properly
            connection.close()
    
    # Convert to async
    async_wrapper = sync_to_async(sync_wrapper, thread_sensitive=True)
    
    # Preserve metadata
    async_wrapper.__name__ = func.__name__
    async_wrapper.__doc__ = func.__doc__
    async_wrapper.__module__ = func.__module__
    
    return async_wrapper


def track_sync_calls(func: Callable[P, T]) -> Callable[P, T]:
    """
    Decorator to track synchronous calls for performance monitoring
    
    Features:
    - Call counting
    - Time tracking
    - Performance warnings
    
    Usage:
        @track_sync_calls
        def process_data(data: dict) -> dict:
            # Heavy synchronous processing
            return processed_data
    """
    
    @wraps(func)
    def wrapper(*args: P.args, **kwargs: P.kwargs) -> T:
        operation_name = f"{func.__module__}.{func.__name__}"
        
        start_time = time.time()
        
        try:
            result = func(*args, **kwargs)
            
            elapsed = time.time() - start_time
            
            # Track in context
            count = db_call_count.get()
            db_call_count.set(count + 1)
            
            total_time = db_total_time.get()
            db_total_time.set(total_time + elapsed)
            
            if DEBUG:
                logger.debug(
                    f"Sync operation: {operation_name} "
                    f"completed in {elapsed:.3f}s"
                )
            
            # Warn on slow sync operations in async context
            if elapsed > 0.1:  # 100ms
                logger.warning(
                    f"Slow sync operation in async context: "
                    f"{operation_name} took {elapsed:.3f}s"
                )
            
            return result
            
        except Exception as e:
            logger.error(
                f"Sync operation failed: {operation_name} - {e}"
            )
            raise
    
    return wrapper


def get_db_stats() -> dict:
    """
    Get current database operation statistics
    
    Returns:
        Dictionary with call count and total time
    """
    return {
        "call_count": db_call_count.get(),
        "total_time": db_total_time.get(),
        "current_operation": db_operation_context.get(),
        "timestamp": datetime.now().isoformat()
    }


def reset_db_stats():
    """Reset database operation statistics"""
    db_call_count.set(0)
    db_total_time.set(0.0)
    db_operation_context.set("unknown")


# Example usage functions for documentation
if __name__ == "__main__":
    # Example 1: Django ORM operation
    from django.contrib.auth.models import User
    
    @async_db_operation
    def get_user_by_username(username: str) -> User:
        """Get user by username with full select_related"""
        return User.objects.select_related("profile").get(username=username)
    
    # Example 2: Complex query
    @async_db_operation
    def get_recent_posts(limit: int = 10):
        """Get recent posts with optimized queries"""
        from instagram_manager.models import InstagramPost
        
        return list(
            InstagramPost.objects
            .select_related("profile", "media")
            .prefetch_related("comments", "hashtags")
            .order_by("-created_at")[:limit]
        )
    
    # Example 3: Write operation
    @async_db_operation
    def create_profile(username: str, full_name: str):
        """Create new Instagram profile"""
        from instagram_manager.models import InstagramProfile
        
        return InstagramProfile.objects.create(
            username=username,
            full_name=full_name
        )