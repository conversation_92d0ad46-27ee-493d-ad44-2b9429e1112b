"""
Error handling decorator for MCP tools
Ensures all errors follow unified format
"""

import logging
from collections.abc import Callable
from contextvars import ContextV<PERSON>
from functools import wraps
from typing import Any

from pydantic import ValidationError

from ..config import DEBUG
from ..errors import ErrorTypes, create_error_response, handle_validation_error

logger = logging.getLogger(__name__)

# Context for tracking tool calls
tool_context: ContextVar[str] = ContextVar("tool", default="unknown")
request_id_context: ContextVar[str] = ContextVar("request_id", default=None)

def handle_tool_errors(func: Callable) -> Callable:
    """
    Decorator for unified error handling across all MCP tools
    
    Features:
    - Catches all exceptions and converts to standard format
    - Logs errors with context
    - Includes traceback only in debug mode
    - Tracks tool execution context
    """
    @wraps(func)
    async def wrapper(*args, **kwargs) -> dict[str, Any]:
        tool_name = func.__name__
        tool_context.set(tool_name)
        
        try:
            # Log tool invocation
            logger.debug(f"Tool invoked: {tool_name} with args: {kwargs}")
            
            # Execute tool
            result = await func(*args, **kwargs)
            
            # If result is already an error, return as-is
            if isinstance(result, dict) and "error_type" in result:
                return result
            
            return result  # type: ignore[no-any-return]
            
        except ValidationError as e:
            # Handle validation errors specially
            logger.warning(f"Validation error in {tool_name}: {e}")
            return handle_validation_error(e)
            
        except Exception as e:
            # Log the full exception
            logger.exception(f"Error in tool {tool_name}")
            
            # Determine error type
            error_type = ErrorTypes.INTERNAL_ERROR
            
            if "DoesNotExist" in str(type(e)):
                error_type = ErrorTypes.NOT_FOUND
            elif "PermissionDenied" in str(type(e)):
                error_type = ErrorTypes.PERMISSION_DENIED
            elif "Timeout" in str(type(e)):
                error_type = ErrorTypes.TIMEOUT
            
            # Create error response
            return create_error_response(
                error_type=error_type,
                message=str(e),
                include_traceback=DEBUG,
                request_id=request_id_context.get()
            )
    
    return wrapper

def set_request_id(request_id: str):
    """Set request ID for current context"""
    request_id_context.set(request_id)

def get_current_tool() -> str:
    """Get current tool being executed"""
    return tool_context.get()