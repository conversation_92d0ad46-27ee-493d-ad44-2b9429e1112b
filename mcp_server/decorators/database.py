"""
Database operation decorators for safe async/sync handling
CRITICAL: All Django ORM operations must use these decorators
"""

import logging
import time
from collections.abc import Callable, Coroutine
from contextvars import ContextVar
from functools import wraps
from typing import Any, TypeVar

from asgiref.sync import sync_to_async
from django.db import transaction

from ..config import DEBU<PERSON>, SLOW_QUERY_THRESHOLD

logger = logging.getLogger(__name__)

# Context variables for tracking
db_operation_context: ContextVar[str] = ContextVar("db_operation", default="unknown")
query_count_context: ContextVar[int] = ContextVar("query_count", default=0)

T = TypeVar("T")

def async_db_operation(func: Callable[..., T]) -> Callable[..., Coroutine[Any, Any, T]]:
    """
    Decorator to safely convert sync Django ORM operations to async
    
    CRITICAL: Use this for ALL database operations in MCP tools
    
    Features:
    - Thread-safe async conversion
    - Automatic transaction handling
    - Performance tracking
    - Context preservation
    """
    @wraps(func)
    async def wrapper(*args, **kwargs) -> T:
        operation_name = f"{func.__module__}.{func.__name__}"
        db_operation_context.set(operation_name)
        
        try:
            # Convert to async with thread sensitivity
            result = await sync_to_async(func, thread_sensitive=True)(*args, **kwargs)
            return result
            
        except Exception:
            logger.error(f"Database operation failed: {operation_name}", exc_info=True)
            raise
        finally:
            # Reset context
            db_operation_context.set("unknown")
    
    return wrapper

def track_sync_calls(func: Callable[..., T]) -> Callable[..., T]:
    """
    Decorator for tracking synchronous database calls
    
    Features:
    - Performance monitoring
    - Slow query detection
    - Query counting
    - Debug logging
    """
    @wraps(func)
    def wrapper(*args, **kwargs) -> T:
        start_time = time.time()
        operation = db_operation_context.get()
        
        # Increment query count
        current_count = query_count_context.get()
        query_count_context.set(current_count + 1)
        
        try:
            # Execute the function
            result = func(*args, **kwargs)
            
            # Calculate duration
            duration = time.time() - start_time
            
            # Log slow queries
            if duration > SLOW_QUERY_THRESHOLD:
                logger.warning(
                    f"Slow DB operation in {operation}: "
                    f"{func.__name__} took {duration:.2f}s"
                )
            elif DEBUG:
                logger.debug(
                    f"DB operation {operation}: "
                    f"{func.__name__} completed in {duration:.3f}s"
                )
            
            return result
            
        except Exception:
            duration = time.time() - start_time
            logger.error(
                f"DB operation failed in {operation}: "
                f"{func.__name__} after {duration:.2f}s",
                exc_info=True
            )
            raise
    
    return wrapper

def transactional(func: Callable[..., T]) -> Callable[..., T]:
    """
    Decorator to wrap function in database transaction
    
    Use for operations that need atomic execution
    """
    @wraps(func)
    def wrapper(*args, **kwargs) -> T:
        operation_name = f"{func.__module__}.{func.__name__}"
        
        with transaction.atomic():
            logger.debug(
                f"[TRANSACTION START] Starting atomic transaction in {operation_name}"
            )
            
            try:
                result = func(*args, **kwargs)
                
                logger.debug(
                    f"[TRANSACTION END] Successfully completed transaction in {operation_name}"
                )
                
                return result
                
            except Exception as e:
                logger.error(
                    f"[TRANSACTION ERROR] Transaction failed in {operation_name}: {e}"
                )
                raise
    
    return wrapper

def async_transactional(func: Callable[..., T]) -> Callable[..., Coroutine[Any, Any, T]]:
    """
    Async version of transactional decorator
    """
    @wraps(func)
    async def wrapper(*args, **kwargs) -> T:
        @transactional
        def sync_func(*args, **kwargs):
            return func(*args, **kwargs)
        
        result = await sync_to_async(sync_func, thread_sensitive=True)(*args, **kwargs)
        return result  # type: ignore[no-any-return]
    
    return wrapper