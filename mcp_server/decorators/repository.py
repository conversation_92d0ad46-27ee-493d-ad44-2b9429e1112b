"""
Repository-specific decorators for optimized queries
"""

import logging
from collections.abc import Callable
from functools import wraps

from django.db.models import QuerySet

logger = logging.getLogger(__name__)

def prefetch_related(*lookups: str):
    """
    Decorator to add prefetch_related to queryset
    Reduces N+1 queries
    """
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(self, *args, **kwargs):
            queryset = func(self, *args, **kwargs)
            if isinstance(queryset, QuerySet):
                return queryset.prefetch_related(*lookups)
            return queryset
        return wrapper
    return decorator

def select_related(*fields: str):
    """
    Decorator to add select_related to queryset
    Reduces query count for foreign keys
    """
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(self, *args, **kwargs):
            queryset = func(self, *args, **kwargs)
            if isinstance(queryset, QuerySet):
                return queryset.select_related(*fields)
            return queryset
        return wrapper
    return decorator

def cached_property(func: Callable) -> property:
    """
    Decorator for caching property results
    Useful for expensive calculations
    """
    attr_name = f"_cached_{func.__name__}"
    
    @wraps(func)
    def wrapper(self):
        if not hasattr(self, attr_name):
            setattr(self, attr_name, func(self))
        return getattr(self, attr_name)
    
    return property(wrapper)

def bulk_operation(chunk_size: int = 1000):
    """
    Decorator for bulk database operations
    Automatically chunks large datasets
    """
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(self, items: list, *args, **kwargs):
            if not items:
                return []
            
            results = []
            total = len(items)
            
            for i in range(0, total, chunk_size):
                chunk = items[i:i + chunk_size]
                logger.debug(
                    f"Processing chunk {i//chunk_size + 1} "
                    f"({len(chunk)} items)"
                )
                chunk_results = func(self, chunk, *args, **kwargs)
                results.extend(chunk_results)
            
            return results
        return wrapper
    return decorator