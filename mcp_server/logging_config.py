import logging
import os

from .config import LOG_LEVEL


def setup_logging():
    """Configure logging for MCP server"""

    # Create formatter
    formatter = logging.Formatter(
        "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        datefmt="%Y-%m-%d %H:%M:%S",
    )

    # Configure root logger
    root_logger = logging.getLogger()
    root_logger.setLevel(getattr(logging, LOG_LEVEL))

    # Console handler
    # console_handler = logging.StreamHandler(sys.stdout)
    # console_handler.setFormatter(formatter)
    # root_logger.addHandler(console_handler)

    # File handler (optional)
    if os.getenv("MCP_LOG_FILE"):
        file_handler = logging.FileHandler(os.getenv("MCP_LOG_FILE"))
        file_handler.setFormatter(formatter)
        root_logger.addHandler(file_handler)

    # Silence noisy libraries
    logging.getLogger("uvicorn.access").setLevel(logging.WARNING)
    logging.getLogger("httpx").setLevel(logging.WARNING)
    logging.getLogger("sentry_sdk.errors").setLevel(logging.WARNING)

    return root_logger
