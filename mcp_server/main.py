import os
import sys
from pathlib import Path

# Add project root to Python path BEFORE any imports
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# Django setup - MUST be before any Django imports
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "SocialManager.settings.production")

# Debug database configuration
print(f"DATABASE_URL: {os.environ.get('DATABASE_URL', 'NOT SET')}")
print(f"DJANGO_SETTINGS_MODULE: {os.environ.get('DJANGO_SETTINGS_MODULE', 'NOT SET')}")

import django  # noqa: E402

django.setup()

# Print database config after setup
from django.conf import settings  # noqa: E402

print(f"Database config: {settings.DATABASES['default']}")
print(f"Database HOST specifically: {settings.DATABASES['default']['HOST']}")
print(f"Settings file: {settings.__module__}")

# Now we can import Django-dependent modules
from contextvars import ContextVar  # noqa: E402

from fastmcp import FastMCP  # noqa: E402

# Import task classes - STATIC imports only
from instagram_manager.tasks.import_tasks import (  # noqa: E402
    ImportBatchPostsTask,
    ImportInstagramCommentsTask,
    ImportInstagramPostsTask,
    ImportInstagramProfileTask,
)
from instagram_manager.tasks.roast_tasks import RoastProfileTask  # noqa: E402
from telegram_manager.tasks.import_tasks import (  # noqa: E402
    ImportTelegramChatsTask,
    ImportTelegramMessagesTask,
    ImportTelegramUsersTask,
)

# Import configuration
from .config import DEBUG, MCP_SERVER_NAME, MCP_SERVER_VERSION  # noqa: E402
from .logging_config import setup_logging  # noqa: E402

# Setup logging
logger = setup_logging()

# Initialize MCP server
# Using stateless HTTP for work with uvicorn multiple workers
mcp: FastMCP = FastMCP(MCP_SERVER_NAME, version=MCP_SERVER_VERSION, stateless_http=True)

# Context variable for tracking DB calls
db_call_context: ContextVar[str] = ContextVar("db_call", default="unknown")

# CRITICAL: Static task registry - NO dynamic imports
TASK_REGISTRY = {
    "instagram.profile": ImportInstagramProfileTask,
    "instagram.posts": ImportInstagramPostsTask,
    "instagram.batch_posts": ImportBatchPostsTask,
    "instagram.comments": ImportInstagramCommentsTask,
    "instagram.roast_profile": RoastProfileTask,
    "telegram.chats": ImportTelegramChatsTask,
    "telegram.messages": ImportTelegramMessagesTask,
    "telegram.users": ImportTelegramUsersTask,
}

# Import all tools to register them with FastMCP
# This must happen after mcp is initialized but before starting the server
from starlette.middleware.cors import CORSMiddleware  # noqa: E402

# Add health check endpoint for Docker
from starlette.requests import Request  # noqa: E402
from starlette.responses import JSONResponse  # noqa: E402

from mcp_server.tools import *  # noqa: F403, E402


@mcp.custom_route("/health", methods=["GET"])
async def health_endpoint(request: Request) -> JSONResponse:
    """Simple health check endpoint for Docker"""
    return JSONResponse({"status": "healthy", "service": MCP_SERVER_NAME})


# Log server initialization
logger.info(f"🚀 {MCP_SERVER_NAME} v{MCP_SERVER_VERSION} initialized")
logger.info(f"📋 Registered {len(TASK_REGISTRY)} task types")
logger.info(f"🐛 Debug mode: {DEBUG}")

# Export app for uvicorn
# Create app with MCP path (not root) to allow other endpoints
app = mcp.http_app(path="/mcp/", transport="streamable-http")

# Add debug logging for requests
from starlette.middleware.base import BaseHTTPMiddleware  # noqa: E402
from starlette.requests import Request  # noqa: E402


class LoggingMiddleware(BaseHTTPMiddleware):
    async def dispatch(self, request: Request, call_next):
        logger.info(
            f"Incoming request: {request.method} {request.url} Headers: {dict(request.headers)}"
        )
        response = await call_next(request)
        logger.info(f"Response status: {response.status_code}")
        return response


app.add_middleware(LoggingMiddleware)

# Add CORS middleware for MCP Inspector
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Allow all origins for development
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)
