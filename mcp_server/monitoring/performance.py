"""
Performance monitoring for database operations
"""

import logging
import time
from contextlib import contextmanager
from dataclasses import dataclass, field
from datetime import datetime

logger = logging.getLogger(__name__)

@dataclass
class QueryStats:
    """Statistics for a single query"""
    operation: str
    duration: float
    timestamp: datetime
    slow: bool = False

@dataclass
class PerformanceMetrics:
    """Performance metrics collector"""
    queries: list[QueryStats] = field(default_factory=list)
    total_queries: int = 0
    slow_queries: int = 0
    total_duration: float = 0.0
    
    def add_query(self, operation: str, duration: float, threshold: float = 0.1):
        """Add query statistics"""
        slow = duration > threshold
        
        stat = QueryStats(
            operation=operation,
            duration=duration,
            timestamp=datetime.now(),
            slow=slow
        )
        
        self.queries.append(stat)
        self.total_queries += 1
        self.total_duration += duration
        
        if slow:
            self.slow_queries += 1
    
    def get_summary(self) -> dict:
        """Get performance summary"""
        return {
            "total_queries": self.total_queries,
            "slow_queries": self.slow_queries,
            "total_duration": round(self.total_duration, 3),
            "average_duration": round(
                self.total_duration / self.total_queries if self.total_queries > 0 else 0,
                3
            ),
            "slow_query_percentage": round(
                (self.slow_queries / self.total_queries * 100) if self.total_queries > 0 else 0,
                1
            )
        }
    
    def reset(self):
        """Reset metrics"""
        self.queries.clear()
        self.total_queries = 0
        self.slow_queries = 0
        self.total_duration = 0.0

# Global metrics instance
_metrics = PerformanceMetrics()

@contextmanager
def track_query(operation: str):
    """Context manager for tracking query performance"""
    start = time.time()
    try:
        yield
    finally:
        duration = time.time() - start
        _metrics.add_query(operation, duration)

def get_performance_metrics() -> dict:
    """Get current performance metrics"""
    return _metrics.get_summary()

def reset_metrics():
    """Reset performance metrics"""
    _metrics.reset()