"""
Instagram comment management tools for MCP server
"""

from typing import Any

from mcp_server.decorators.database import async_db_operation, track_sync_calls
from mcp_server.decorators.error_handler import handle_tool_errors
from mcp_server.main import mcp
from mcp_server.validators.instagram import (
    BulkCommentParams,
    CommentFilterParams,
    CommentParams,
    CommentStatsParams,
)


# Database helper functions
@async_db_operation
@track_sync_calls
def get_comments_for_post(
    post_id: int,
    limit: int = 50,
    offset: int = 0,
    include_replies: bool = True,
    order_by: str = "-commented_at",
):
    """Get comments for a specific post"""
    from instagram_manager.services import CommentService, PostService

    # Verify post exists
    post_service = PostService()
    post = post_service.get_by_id(post_id)
    if not post:
        raise ValueError(f"Post with ID {post_id} not found")

    service = CommentService()
    queryset = service.repository.get_for_post(post_id)

    # Filter replies if needed
    if not include_replies:
        queryset = queryset.filter(reply_to__isnull=True)

    # Apply ordering
    if order_by:
        queryset = queryset.order_by(order_by)

    # Get total count before pagination
    total = queryset.count()

    # Apply pagination
    comments = list(queryset[offset : offset + limit])

    return {
        "post_id": post_id,
        "comments": [
            {
                "id": comment.id,
                "comment_id": comment.comment_id,
                "username": comment.author_username,
                "text": comment.text,
                "like_count": comment.like_count,
                "commented_at": comment.commented_at.isoformat() if comment.commented_at else None,
                "parent_comment_id": comment.reply_to_id if comment.reply_to else None,
                "is_reply": bool(comment.reply_to_id if comment.reply_to else None),
                "reply_count": comment.replies.count() if hasattr(comment, "replies") else 0,
            }
            for comment in comments
        ],
        "total": total,
        "limit": limit,
        "offset": offset,
    }


@async_db_operation
@track_sync_calls
def post_single_comment(
    username: str,
    password: str,
    post_url: str,
    comment_text: str,
    reply_to_comment_id: int | None = None,
):
    """Post a single comment to Instagram"""
    from instagram_manager.management.commands.instagram_post_comment import Command as PostCommentCommand

    # Create command instance
    command = PostCommentCommand()

    # Parse arguments
    options = {
        "username": username,
        "password": password,
        "post_url": post_url,
        "comment_text": comment_text,
        "reply_to": str(reply_to_comment_id) if reply_to_comment_id else None,
        "use_playwright": True,  # Use Playwright by default for reliability
    }

    # Execute command
    try:
        command.handle(**options)
        return {
            "status": "success",
            "message": f"Comment posted successfully to {post_url}",
            "post_url": post_url,
            "comment_text": comment_text,
            "reply_to_comment_id": reply_to_comment_id,
        }
    except Exception as e:
        return {
            "status": "error",
            "message": str(e),
            "post_url": post_url,
        }


@async_db_operation
@track_sync_calls
def post_bulk_comments(
    username: str,
    password: str,
    comments: list[dict[str, str]],
    delay_seconds: int = 30,
):
    """Post multiple comments with delays"""
    from instagram_manager.instagram_api.brightdata import BrightDataClient
    from instagram_manager.services import BatchCommentService

    # Create BrightData client instance
    client = BrightDataClient()
    service = BatchCommentService(client)

    # Prepare comment data
    comment_data = []
    for comment in comments:
        comment_data.append(
            {
                "post_url": comment["post_url"],
                "comment_text": comment["comment_text"],
                "username": username,
                "password": password,
            }
        )

    # Execute bulk posting
    results = service.post_comments_batch(comment_data, delay_seconds=delay_seconds)

    return {
        "status": "completed",
        "total_comments": len(comments),
        "successful": len([r for r in results if r.get("status") == "success"]),
        "failed": len([r for r in results if r.get("status") == "error"]),
        "results": results,
    }


@async_db_operation
@track_sync_calls
def search_comments(
    query: str,
    profile_username: str | None = None,
    post_id: int | None = None,
    limit: int = 50,
):
    """Search comments by text content"""
    from django.db.models import Q

    from instagram_manager.services import CommentService
    
    # Basic validation
    if not query or len(query) < 2:
        raise ValueError("Search query must be at least 2 characters")

    service = CommentService()
    queryset = service.repository.all()

    # Apply search filter
    queryset = queryset.filter(Q(text__icontains=query) | Q(author_username__icontains=query))

    # Filter by profile if provided
    if profile_username:
        queryset = queryset.filter(post__profile__username=profile_username)

    # Filter by post if provided
    if post_id:
        queryset = queryset.filter(post_id=post_id)

    # Get results
    total = queryset.count()
    comments = list(queryset[:limit])

    return {
        "query": query,
        "results": [
            {
                "id": comment.id,
                "comment_id": comment.comment_id,
                "username": comment.author_username,
                "text": comment.text,
                "post_id": comment.post_id,
                "post_code": comment.post.code if comment.post else None,
                "like_count": comment.like_count,
                "commented_at": comment.commented_at.isoformat() if comment.commented_at else None,
            }
            for comment in comments
        ],
        "total": total,
    }


@async_db_operation
@track_sync_calls
def get_comment_analytics(post_id: int):
    """Get analytics for comments on a post"""
    from django.db.models import Avg, Count, Max, Min, Q

    from instagram_manager.services import CommentService

    service = CommentService()
    comments = service.repository.get_for_post(post_id)

    # Calculate statistics
    stats = comments.aggregate(
        total_comments=Count("id"),
        total_replies=Count("id", filter=Q(reply_to__isnull=False)),
        avg_likes=Avg("like_count"),
        max_likes=Max("like_count"),
        min_likes=Min("like_count"),
        unique_commenters=Count("author_username", distinct=True),
    )

    # Get top commenters
    from django.db.models import Count as DCount

    top_commenters = (
        comments.values("author_username").annotate(comment_count=DCount("id")).order_by("-comment_count")[:10]
    )

    # Get most liked comments
    most_liked = list(comments.order_by("-like_count")[:5])

    return {
        "post_id": post_id,
        "statistics": {
            "total_comments": stats["total_comments"],
            "total_replies": stats["total_replies"],
            "unique_commenters": stats["unique_commenters"],
            "average_likes": round(stats["avg_likes"] or 0, 2),
            "max_likes": stats["max_likes"] or 0,
            "min_likes": stats["min_likes"] or 0,
        },
        "top_commenters": list(top_commenters),
        "most_liked_comments": [
            {
                "id": comment.id,
                "username": comment.author_username,
                "text": comment.text[:100] + "..." if len(comment.text) > 100 else comment.text,
                "like_count": comment.like_count,
            }
            for comment in most_liked
        ],
    }


# MCP Tools
@mcp.tool()
@handle_tool_errors
async def instagram_get_comments(
    post_id: int,
    limit: int = 50,
    offset: int = 0,
    include_replies: bool = True,
    order_by: str = "-commented_at",
) -> dict[str, Any]:
    """
    Get comments for a specific Instagram post

    Args:
        post_id: Instagram post ID
        limit: Number of comments (1-100)
        offset: Skip comments
        include_replies: Include comment replies
        order_by: Sort order (-commented_at, like_count)

    Returns:
        List of comments with details
    """
    # Validate parameters
    params = CommentFilterParams(
        post_id=post_id,
        limit=limit,
        offset=offset,
        include_replies=include_replies,
        order_by=order_by,
    )

    result: dict[str, Any] = await get_comments_for_post(
        post_id=params.post_id,
        limit=params.limit,
        offset=params.offset,
        include_replies=params.include_replies,
        order_by=params.order_by,
    )
    return result


@mcp.tool()
@handle_tool_errors
async def instagram_post_comment(
    username: str,
    password: str,
    post_url: str,
    comment_text: str,
    reply_to_comment_id: int | None = None,
) -> dict[str, Any]:
    """
    Post a comment to an Instagram post

    Args:
        username: Instagram account username
        password: Instagram account password
        post_url: URL of the post
        comment_text: Comment text (max 2200 chars)
        reply_to_comment_id: ID to reply to

    Returns:
        Posted comment details
    """
    # Validate parameters
    params = CommentParams(
        username=username,
        password=password,
        post_url=post_url,
        comment_text=comment_text,
        reply_to_comment_id=reply_to_comment_id,
    )

    result: dict[str, Any] = await post_single_comment(
        username=params.username,
        password=params.password,
        post_url=params.post_url,
        comment_text=params.comment_text,
        reply_to_comment_id=params.reply_to_comment_id,
    )
    return result


@mcp.tool()
@handle_tool_errors
async def instagram_bulk_post_comments(
    username: str,
    password: str,
    comments: list[dict[str, str]],
    delay_seconds: int = 30,
) -> dict[str, Any]:
    """
    Post multiple comments with delays

    Args:
        username: Instagram account username
        password: Instagram account password
        comments: List of {post_url, comment_text}
        delay_seconds: Delay between comments

    Returns:
        Bulk posting results
    """
    # Validate parameters
    params = BulkCommentParams(
        username=username,
        password=password,
        comments=comments,
        delay_seconds=delay_seconds,
    )

    result: dict[str, Any] = await post_bulk_comments(
        username=params.username,
        password=params.password,
        comments=params.comments,
        delay_seconds=params.delay_seconds,
    )
    return result


@mcp.tool()
@handle_tool_errors
async def instagram_search_comments(
    query: str,
    profile_username: str | None = None,
    post_id: int | None = None,
    limit: int = 50,
) -> dict[str, Any]:
    """
    Search comments by text content

    Args:
        query: Search query
        profile_username: Filter by profile
        post_id: Filter by post
        limit: Number of results (max 100)

    Returns:
        Search results
    """
    # Basic validation
    if not query or len(query) < 2:
        raise ValueError("Search query must be at least 2 characters")

    limit = min(max(1, limit), 100)

    result: dict[str, Any] = await search_comments(
        query=query,
        profile_username=profile_username,
        post_id=post_id,
        limit=limit,
    )
    return result


@mcp.tool()
@handle_tool_errors
async def instagram_get_comment_stats(
    post_id: int,
) -> dict[str, Any]:
    """
    Get analytics for comments on a post

    Args:
        post_id: Instagram post ID

    Returns:
        Comment statistics and analytics
    """
    # Validate parameters
    params = CommentStatsParams(post_id=post_id)

    result: dict[str, Any] = await get_comment_analytics(post_id=params.post_id)
    return result