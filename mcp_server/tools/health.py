"""
Health check tools for MCP server
"""

import logging
from datetime import datetime
from typing import Any

from asgiref.sync import sync_to_async
from django.core.cache import cache
from django.db import connection

from mcp_server.decorators.error_handler import handle_tool_errors
from mcp_server.main import mcp

logger = logging.getLogger(__name__)


@mcp.tool()
@handle_tool_errors
async def health_check(detailed: bool = False) -> dict[str, Any]:
    """
    Check health status of the MCP server
    
    Args:
        detailed: Include detailed status for each component
        
    Returns:
        Health status information
    """
    checks = {}
    overall_status = "healthy"
    
    # Check database
    try:
        # Wrap synchronous database operation in sync_to_async
        @sync_to_async
        def check_database():
            with connection.cursor() as cursor:
                cursor.execute("SELECT 1")
                return True
        
        await check_database()
        checks["database"] = {
            "status": "healthy",
            "message": "Database connection successful"
        }
    except Exception as e:
        checks["database"] = {
            "status": "unhealthy",
            "message": f"Database error: {e!s}"
        }
        overall_status = "degraded"
    
    # Check Redis/cache
    try:
        # Wrap synchronous cache operations in sync_to_async
        @sync_to_async
        def check_redis():
            cache.set("health_check", "ok", 1)
            return cache.get("health_check") == "ok"
        
        redis_ok = await check_redis()
        if redis_ok:
            checks["redis"] = {
                "status": "healthy",
                "message": "Redis connection successful"
            }
        else:
            checks["redis"] = {
                "status": "unhealthy",
                "message": "Redis write/read failed"
            }
            overall_status = "degraded"
    except Exception as e:
        checks["redis"] = {
            "status": "unhealthy",
            "message": f"Redis error: {e!s}"
        }
        overall_status = "degraded"
    
    # Check Celery
    try:
        from celery import current_app
        
        # Wrap synchronous Celery operation in sync_to_async
        @sync_to_async
        def check_celery():
            # Basic check - see if we can inspect Celery
            stats = current_app.control.inspect().stats()
            return stats is not None
        
        celery_ok = await check_celery()
        if celery_ok:
            checks["celery"] = {
                "status": "healthy",
                "message": "Celery connection successful"
            }
        else:
            checks["celery"] = {
                "status": "unhealthy",
                "message": "Celery inspection returned None"
            }
            overall_status = "degraded"
    except Exception as e:
        checks["celery"] = {
            "status": "unhealthy",
            "message": f"Celery error: {e!s}"
        }
        overall_status = "degraded"
    
    # Check task processing
    if detailed:
        try:
            from datetime import timedelta

            from django.utils import timezone

            from core.models import TaskResult
            
            # Wrap synchronous Django ORM operations in sync_to_async
            @sync_to_async
            def get_task_stats():
                # Get recent task stats
                recent_time = timezone.now() - timedelta(hours=1)
                recent_tasks = TaskResult.objects.filter(
                    created_at__gte=recent_time
                )
                
                return {
                    "recent_tasks": recent_tasks.count(),
                    "pending": recent_tasks.filter(status="pending").count(),
                    "running": recent_tasks.filter(status="running").count(),
                    "completed": recent_tasks.filter(status="completed").count(),
                    "failed": recent_tasks.filter(status="failed").count()
                }
            
            stats = await get_task_stats()
            checks["task_processing"] = {
                "status": "healthy",
                "stats": stats
            }
        except Exception as e:
            checks["task_processing"] = {
                "status": "unhealthy",
                "message": f"Task processing error: {e!s}"
            }
    
    response = {
        "status": overall_status,
        "timestamp": datetime.now().isoformat(),
        "checks": checks
    }
    
    if detailed:
        response["version"] = "1.0.0"
        response["uptime"] = "N/A"  # Would need to track server start time
    
    return response