"""
Task results aggregation and analytics
"""

from collections import defaultdict
from datetime import datetime, timedelta
from typing import Any

from mcp_server.decorators.database import async_db_operation, track_sync_calls
from mcp_server.decorators.error_handler import handle_tool_errors
from mcp_server.main import mcp


@mcp.tool()
@handle_tool_errors
async def task_aggregate_results(
    task_type: str,
    aggregation: str = "daily",
    date_from: str | None = None,
    date_to: str | None = None
) -> dict[str, Any]:
    """
    Aggregate task results by time period
    
    Args:
        task_type: Type of tasks to aggregate
        aggregation: Aggregation period (hourly, daily, weekly)
        date_from: Start date
        date_to: End date
        
    Returns:
        Aggregated results
    """
    # Get aggregated data
    data = await _aggregate_task_results(
        task_type=task_type,
        aggregation=aggregation,
        date_from=date_from,
        date_to=date_to
    )
    
    return {
        "task_type": task_type,
        "aggregation": aggregation,
        "period": {
            "from": date_from,
            "to": date_to
        },
        "data": data
    }

@async_db_operation
@track_sync_calls
def _aggregate_task_results(
    task_type: str,
    aggregation: str,
    date_from: str | None,
    date_to: str | None
) -> list[dict[str, Any]]:
    """Aggregate results from database"""

    from core.models import TaskResult
    
    # Build date range
    queryset = TaskResult.objects.filter(task_type=task_type)
    
    if date_from:
        queryset = queryset.filter(created_at__gte=datetime.fromisoformat(date_from))
    if date_to:
        queryset = queryset.filter(created_at__lte=datetime.fromisoformat(date_to))
    
    # Get all tasks in range
    tasks = list(queryset.order_by("created_at"))
    
    # Aggregate by period
    aggregated: dict[str, dict[str, Any]] = defaultdict(lambda: {
        "total": 0,
        "completed": 0,
        "failed": 0,
        "items_processed": 0,
        "avg_duration": 0.0,
        "durations": []
    })
    
    for task in tasks:
        # Determine period key
        if aggregation == "hourly":
            key = task.created_at.strftime("%Y-%m-%d %H:00")
        elif aggregation == "daily":
            key = task.created_at.strftime("%Y-%m-%d")
        elif aggregation == "weekly":
            # Get week start
            week_start = task.created_at - timedelta(days=task.created_at.weekday())
            key = week_start.strftime("%Y-%m-%d")
        
        # Aggregate data
        agg = aggregated[key]
        agg["total"] += 1
        
        if task.status == "completed":
            agg["completed"] += 1
            
            # Extract items processed
            if task.result and isinstance(task.result, dict):
                if task_type == "instagram.posts":
                    agg["items_processed"] += task.result.get("total_posts", 0)
                elif task_type == "telegram.messages":
                    agg["items_processed"] += task.result.get("messages_imported", 0)
            
            # Calculate duration
            if task.started_at and task.completed_at:
                duration = (task.completed_at - task.started_at).total_seconds()
                agg["durations"].append(duration)
        
        elif task.status == "failed":
            agg["failed"] += 1
    
    # Calculate averages
    result = []
    for period, data in sorted(aggregated.items()):
        if data["durations"]:
            data["avg_duration"] = sum(data["durations"]) / len(data["durations"])
        
        # Remove raw durations
        del data["durations"]
        
        # Calculate success rate
        data["success_rate"] = (
            (data["completed"] / data["total"] * 100)
            if data["total"] > 0 else 0
        )
        
        result.append({
            "period": period,
            **data
        })
    
    return result