"""
Instagram profile management tools for MCP server
"""

import logging
from typing import Any

from mcp_server.decorators.database import async_db_operation, track_sync_calls
from mcp_server.decorators.error_handler import handle_tool_errors
from mcp_server.errors import ErrorTypes, create_error_response
from mcp_server.main import mcp
from mcp_server.validators.base import PaginationParams

logger = logging.getLogger(__name__)

# Database helper functions

@async_db_operation
@track_sync_calls
def get_profile_by_username(username: str):
    """Get Instagram profile by username"""
    from instagram_manager.repositories import ProfileRepository
    repo = ProfileRepository()
    return repo.get_by_username(username)

@async_db_operation
@track_sync_calls
def get_profile_by_id(profile_id: int):
    """Get Instagram profile by ID"""
    from instagram_manager.repositories import ProfileRepository
    repo = ProfileRepository()
    return repo.get(id=profile_id)

@async_db_operation
@track_sync_calls
def filter_profiles(**kwargs):
    """Filter Instagram profiles with parameters"""
    from instagram_manager.repositories import ProfileRepository
    repo = ProfileRepository()
    
    # Extract pagination params
    limit = kwargs.pop("limit", 20)
    offset = kwargs.pop("offset", 0)
    
    # Extract ordering param
    order_by = kwargs.pop("order_by", "-follower_count")
    
    # Get queryset
    queryset = repo.filter(**kwargs)
    
    # Apply ordering
    if order_by:
        queryset = queryset.order_by(order_by)
    
    # Get total count before pagination
    total = queryset.count()
    
    # Apply pagination
    profiles = list(queryset[offset:offset + limit])
    
    return profiles, total

@async_db_operation
@track_sync_calls
def search_profiles(query: str, limit: int = 20):
    """Search profiles by username or full name"""
    from django.db.models import Q

    from instagram_manager.models import InstagramProfile
    
    queryset = InstagramProfile.objects.filter(
        Q(username__icontains=query) | 
        Q(full_name__icontains=query)
    ).order_by("-follower_count")[:limit]
    
    return list(queryset)

@async_db_operation
@track_sync_calls
def get_profile_statistics(profile_id: int):
    """Get detailed statistics for a profile"""
    from django.db.models import Avg, Count, Sum

    from instagram_manager.models import InstagramPost
    
    stats = InstagramPost.objects.filter(
        profile_id=profile_id
    ).aggregate(
        total_posts=Count("id"),
        total_likes=Sum("like_count"),
        total_comments=Sum("comment_count"),
        avg_likes=Avg("like_count"),
        avg_comments=Avg("comment_count")
    )
    
    return stats

# MCP Tool implementations

@mcp.tool()
@handle_tool_errors
async def instagram_get_profile(username: str) -> dict[str, Any]:
    """
    Get Instagram profile information by username
    
    Args:
        username: Instagram username (with or without @)
        
    Returns:
        Profile information or error
    """
    # Normalize username
    username = username.strip().lower().lstrip("@")
    
    logger.info(f"Fetching Instagram profile: {username}")
    
    # Get profile from database
    profile = await get_profile_by_username(username)
    
    if not profile:
        return create_error_response(
            error_type=ErrorTypes.NOT_FOUND,
            message=f"Profile '{username}' not found"
        )
    
    # Get additional statistics
    stats = await get_profile_statistics(profile.id)
    
    return {
        "id": profile.id,
        "username": profile.username,
        "full_name": profile.full_name,
        "bio": profile.bio,
        "external_id": profile.external_id,
        "follower_count": profile.follower_count,
        "following_count": profile.following_count,
        "post_count": profile.post_count,
        "is_verified": profile.is_verified,
        "is_private": profile.is_private,
        "is_business": profile.is_business,
        "profile_pic_url": profile.profile_pic_url,
        "external_url": profile.external_url,
        "created_at": profile.created_at.isoformat(),
        "updated_at": profile.updated_at.isoformat(),
        "last_scraped_at": profile.last_scraped_at.isoformat() if profile.last_scraped_at else None,
        "statistics": {
            "total_posts": stats.get("total_posts", 0),
            "total_likes": stats.get("total_likes", 0),
            "total_comments": stats.get("total_comments", 0),
            "average_likes": round(stats.get("avg_likes", 0) or 0, 2),
            "average_comments": round(stats.get("avg_comments", 0) or 0, 2)
        }
    }

@mcp.tool()
@handle_tool_errors
async def instagram_list_profiles(
    limit: int = 20,
    offset: int = 0,
    is_active: bool | None = None,
    is_verified: bool | None = None,
    is_private: bool | None = None,
    is_business: bool | None = None,
    min_followers: int | None = None,
    max_followers: int | None = None,
    order_by: str = "-follower_count"
) -> dict[str, Any]:
    """
    List Instagram profiles with filtering and pagination
    
    Args:
        limit: Number of profiles to return (1-100)
        offset: Number of profiles to skip
        is_active: Filter by active status
        is_verified: Filter by verification status
        is_private: Filter by private status
        is_business: Filter by business account status
        min_followers: Minimum follower count
        max_followers: Maximum follower count
        order_by: Sort order (follower_count, -follower_count, username, -updated_at)
        
    Returns:
        List of profiles with pagination info
    """
    # Validate pagination
    pagination = PaginationParams(limit=limit, offset=offset)
    
    # Build filters
    filters: dict[str, Any] = {}
    if is_active is not None:
        filters["is_active"] = is_active
    if is_verified is not None:
        filters["is_verified"] = is_verified
    if is_private is not None:
        filters["is_private"] = is_private
    if is_business is not None:
        filters["is_business"] = is_business
    if min_followers is not None:
        filters["follower_count__gte"] = min_followers
    if max_followers is not None:
        filters["follower_count__lte"] = max_followers
    
    # Validate order_by
    valid_orders = [
        "follower_count", "-follower_count",
        "username", "-username",
        "updated_at", "-updated_at",
        "post_count", "-post_count"
    ]
    if order_by not in valid_orders:
        order_by = "-follower_count"
    
    filters["order_by"] = order_by
    
    logger.info(f"Listing Instagram profiles with filters: {filters}")
    
    # Get profiles
    profiles, total = await filter_profiles(
        limit=pagination.limit,
        offset=pagination.offset,
        **filters
    )
    
    return {
        "total": total,
        "offset": pagination.offset,
        "limit": pagination.limit,
        "has_more": (pagination.offset + len(profiles)) < total,
        "profiles": [
            {
                "id": p.id,
                "username": p.username,
                "full_name": p.full_name,
                "follower_count": p.follower_count,
                "following_count": p.following_count,
                "post_count": p.post_count,
                "is_verified": p.is_verified,
                "is_private": p.is_private,
                "is_business": p.is_business,
                "is_active": p.is_active,
                "profile_pic_url": p.profile_pic_url,
                "last_scraped_at": p.last_scraped_at.isoformat() if p.last_scraped_at else None
            }
            for p in profiles
        ]
    }

@mcp.tool()
@handle_tool_errors
async def instagram_search_profiles(
    query: str,
    limit: int = 20
) -> dict[str, Any]:
    """
    Search Instagram profiles by username or full name
    
    Args:
        query: Search query (min 2 characters)
        limit: Maximum results to return (1-50)
        
    Returns:
        List of matching profiles
    """
    # Validate query
    if len(query) < 2:
        return create_error_response(
            error_type=ErrorTypes.VALIDATION_ERROR,
            message="Search query must be at least 2 characters"
        )
    
    # Validate limit
    limit = max(1, min(limit, 50))
    
    logger.info(f"Searching Instagram profiles: '{query}'")
    
    # Search profiles
    profiles = await search_profiles(query, limit)
    
    return {
        "query": query,
        "count": len(profiles),
        "profiles": [
            {
                "id": p.id,
                "username": p.username,
                "full_name": p.full_name,
                "follower_count": p.follower_count,
                "is_verified": p.is_verified,
                "is_private": p.is_private,
                "profile_pic_url": p.profile_pic_url
            }
            for p in profiles
        ]
    }

@mcp.tool()
@handle_tool_errors
async def instagram_get_profile_stats(
    username: str,
    period: str = "all"
) -> dict[str, Any]:
    """
    Get detailed statistics for an Instagram profile
    
    Args:
        username: Instagram username
        period: Time period (all, today, week, month)
        
    Returns:
        Profile statistics and metrics
    """
    # Get profile
    profile = await get_profile_by_username(username)
    
    if not profile:
        return create_error_response(
            error_type=ErrorTypes.NOT_FOUND,
            message=f"Profile '{username}' not found"
        )
    
    # Get base statistics
    stats = await get_profile_statistics(profile.id)
    
    # Get growth metrics if available
    growth_metrics = await _calculate_growth_metrics(profile.id, period)
    
    # Get engagement rate
    engagement_rate = 0.0
    if profile.follower_count > 0 and stats["total_posts"] > 0:
        total_engagement = (stats.get("total_likes", 0) + stats.get("total_comments", 0))
        engagement_rate = (total_engagement / (stats["total_posts"] * profile.follower_count)) * 100
    
    return {
        "profile": {
            "username": profile.username,
            "follower_count": profile.follower_count,
            "following_count": profile.following_count,
            "post_count": profile.post_count
        },
        "statistics": {
            "total_posts": stats.get("total_posts", 0),
            "total_likes": stats.get("total_likes", 0),
            "total_comments": stats.get("total_comments", 0),
            "average_likes": round(stats.get("avg_likes", 0) or 0, 2),
            "average_comments": round(stats.get("avg_comments", 0) or 0, 2),
            "engagement_rate": round(engagement_rate, 2)
        },
        "growth": growth_metrics,
        "period": period
    }

@async_db_operation
@track_sync_calls
def _calculate_growth_metrics(profile_id: int, period: str) -> dict[str, Any]:
    """Calculate growth metrics for a profile"""
    from datetime import timedelta

    from django.db.models import Count, Sum
    from django.utils import timezone

    from instagram_manager.models import InstagramPost
    
    # Determine date range
    now = timezone.now()
    if period == "today":
        start_date = now.replace(hour=0, minute=0, second=0, microsecond=0)
    elif period == "week":
        start_date = now - timedelta(days=7)
    elif period == "month":
        start_date = now - timedelta(days=30)
    else:
        # Return empty metrics for "all"
        return {
            "new_posts": 0,
            "likes_gained": 0,
            "comments_gained": 0
        }
    
    # Get metrics for period
    posts = InstagramPost.objects.filter(
        profile_id=profile_id,
        posted_at__gte=start_date
    ).aggregate(
        new_posts=Count("id"),
        likes_gained=Sum("like_count"),
        comments_gained=Sum("comment_count")
    )
    
    return {
        "new_posts": posts.get("new_posts", 0),
        "likes_gained": posts.get("likes_gained", 0),
        "comments_gained": posts.get("comments_gained", 0)
    }

@mcp.tool()
@handle_tool_errors
async def instagram_bulk_check_profiles(
    usernames: list[str]
) -> dict[str, Any]:
    """
    Check multiple Instagram profiles existence
    
    Args:
        usernames: List of usernames to check (max 50)
        
    Returns:
        Status of each username
    """
    # Validate input
    if not usernames:
        return create_error_response(
            error_type=ErrorTypes.VALIDATION_ERROR,
            message="At least one username is required"
        )
    
    if len(usernames) > 50:
        return create_error_response(
            error_type=ErrorTypes.VALIDATION_ERROR,
            message="Maximum 50 usernames allowed"
        )
    
    # Normalize usernames
    normalized = [u.strip().lower().lstrip("@") for u in usernames]
    
    # Check each profile
    results = {}
    for username in normalized:
        profile = await get_profile_by_username(username)
        results[username] = {
            "exists": profile is not None,
            "id": profile.id if profile else None,
            "is_active": profile.is_active if profile else None,
            "last_updated": profile.updated_at.isoformat() if profile else None
        }
    
    return {
        "checked": len(results),
        "found": sum(1 for r in results.values() if r["exists"]),
        "results": results
    }