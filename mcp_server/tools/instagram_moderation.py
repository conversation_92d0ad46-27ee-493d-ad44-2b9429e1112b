"""
Instagram comment moderation tools for MCP server
"""

from datetime import datetime
from typing import Any

from mcp_server.decorators.database import async_db_operation, track_sync_calls
from mcp_server.decorators.error_handler import handle_tool_errors
from mcp_server.main import mcp

# Spam detection patterns
SPAM_PATTERNS = [
    r"check\s+my\s+profile",
    r"link\s+in\s+bio",
    r"free\s+followers",
    r"earn\s+money",
    r"click\s+here",
    r"dm\s+for",
    r"whatsapp",
    r"telegram\s+@",
    r"🔥{3,}",  # Excessive emojis
    r"💰{3,}",
    r"follow\s+for\s+follow",
    r"f4f",
    r"l4l",  # Like for like
]


# Database helper functions
@async_db_operation
@track_sync_calls
def detect_spam_comments(post_id: int, threshold: float = 0.7):
    """Detect potential spam comments on a post"""
    import re

    from instagram_manager.services import CommentService
    
    # Validate threshold
    if not 0.0 <= threshold <= 1.0:
        raise ValueError("Threshold must be between 0.0 and 1.0")

    service = CommentService()
    comments = service.repository.get_for_post(post_id)

    spam_comments = []
    clean_comments = []

    for comment in comments:
        spam_score = 0.0
        reasons = []

        # Check spam patterns
        text_lower = comment.text.lower()
        for pattern in SPAM_PATTERNS:
            if re.search(pattern, text_lower, re.IGNORECASE):
                spam_score += 0.3
                reasons.append(f"Matches pattern: {pattern}")

        # Check for excessive URLs
        url_count = len(re.findall(r"https?://", text_lower))
        if url_count > 1:
            spam_score += 0.4
            reasons.append(f"Multiple URLs: {url_count}")

        # Check for excessive mentions
        mention_count = len(re.findall(r"@\w+", comment.text))
        if mention_count > 3:
            spam_score += 0.3
            reasons.append(f"Excessive mentions: {mention_count}")

        # Check for repetitive characters
        if re.search(r"(.)\1{5,}", comment.text):
            spam_score += 0.2
            reasons.append("Repetitive characters")

        # Normalize score
        spam_score = min(spam_score, 1.0)

        comment_data = {
            "id": comment.id,
            "comment_id": comment.comment_id,
            "username": comment.author_username,
            "text": comment.text[:100] + "..." if len(comment.text) > 100 else comment.text,
            "spam_score": round(spam_score, 2),
            "reasons": reasons,
            "commented_at": comment.commented_at.isoformat() if comment.commented_at else None,
        }

        if spam_score >= threshold:
            spam_comments.append(comment_data)
        else:
            clean_comments.append(comment_data)

    return {
        "post_id": post_id,
        "total_comments": comments.count(),
        "spam_comments": spam_comments,
        "clean_comments": len(clean_comments),
        "spam_percentage": round(len(spam_comments) / comments.count() * 100, 2) if comments.count() > 0 else 0,
    }


@async_db_operation
@track_sync_calls
def get_top_commenters(profile_username: str, limit: int = 20):
    """Get top commenters for a profile"""
    from django.db.models import Count

    from instagram_manager.models import InstagramComment
    
    # Validate username
    if not profile_username:
        raise ValueError("Profile username is required")

    # Get all comments on posts by this profile
    comments = InstagramComment.objects.filter(post__profile__username=profile_username)

    # Exclude the profile owner's own comments
    comments = comments.exclude(author_username=profile_username)

    # Group by username and count
    top_commenters = (
        comments.values("author_username")
        .annotate(
            comment_count=Count("id"),
            unique_posts=Count("post_id", distinct=True),
        )
        .order_by("-comment_count")[:limit]
    )

    # Calculate engagement metrics
    results = []
    for commenter in top_commenters:
        # Get sample comments
        sample_comments = list(
            comments.filter(author_username=commenter["author_username"]).order_by("-like_count")[:3].values("text", "like_count")
        )

        results.append(
            {
                "username": commenter["author_username"],
                "comment_count": commenter["comment_count"],
                "unique_posts_commented": commenter["unique_posts"],
                "engagement_rate": round(commenter["unique_posts"] / commenter["comment_count"] * 100, 2),
                "top_comments": sample_comments,
            }
        )

    return {
        "profile": profile_username,
        "top_commenters": results,
        "total_unique_commenters": comments.values("author_username").distinct().count(),
    }


@async_db_operation
@track_sync_calls
def bulk_delete_comments(comment_ids: list[int], reason: str = "Spam"):
    """Bulk delete comments (mark as hidden)"""
    from instagram_manager.services import CommentService
    
    # Validate input
    if not comment_ids:
        raise ValueError("At least one comment ID is required")
    
    if len(comment_ids) > 100:
        raise ValueError("Cannot delete more than 100 comments at once")

    service = CommentService()
    deleted_count = 0
    errors = []

    for comment_id in comment_ids:
        try:
            comment = service.repository.get_by_id(comment_id)
            if comment:
                # Mark as hidden instead of deleted
                comment.is_hidden = True
                # Store deletion reason in raw_data
                if not comment.raw_data:
                    comment.raw_data = {}
                comment.raw_data["deletion_reason"] = reason
                comment.raw_data["deleted_at"] = datetime.now().isoformat()
                comment.save()
                deleted_count += 1
            else:
                errors.append({"comment_id": comment_id, "error": "Comment not found"})
        except Exception as e:
            errors.append({"comment_id": comment_id, "error": str(e)})

    return {
        "total_requested": len(comment_ids),
        "deleted": deleted_count,
        "errors": errors,
        "reason": reason,
    }


@async_db_operation
@track_sync_calls
def analyze_comment_sentiment(post_id: int):
    """Analyze sentiment of comments (basic implementation)"""
    from instagram_manager.services import CommentService

    service = CommentService()
    comments = service.repository.get_for_post(post_id)

    # Basic sentiment analysis based on keywords and emojis
    positive_keywords = ["love", "amazing", "beautiful", "great", "awesome", "perfect", "best", "wonderful", "excellent"]
    negative_keywords = ["hate", "bad", "ugly", "worst", "terrible", "awful", "horrible", "disgusting", "pathetic"]

    positive_emojis = ["❤️", "😍", "🥰", "😊", "💕", "👍", "🔥", "✨", "💯", "🙌"]
    negative_emojis = ["😠", "😡", "👎", "💔", "😤", "😒", "🙄", "😑"]

    sentiment_scores = {"positive": 0, "negative": 0, "neutral": 0}
    comment_sentiments = []

    for comment in comments:
        text_lower = comment.text.lower()
        score = 0.0

        # Check keywords
        for word in positive_keywords:
            if word in text_lower:
                score += 1
        for word in negative_keywords:
            if word in text_lower:
                score -= 1

        # Check emojis
        for emoji in positive_emojis:
            if emoji in comment.text:
                score += 0.5
        for emoji in negative_emojis:
            if emoji in comment.text:
                score -= 0.5

        # Classify sentiment
        if score > 0:
            sentiment = "positive"
            sentiment_scores["positive"] += 1
        elif score < 0:
            sentiment = "negative"
            sentiment_scores["negative"] += 1
        else:
            sentiment = "neutral"
            sentiment_scores["neutral"] += 1

        comment_sentiments.append(
            {
                "id": comment.id,
                "username": comment.author_username,
                "text": comment.text[:100] + "..." if len(comment.text) > 100 else comment.text,
                "sentiment": sentiment,
                "score": score,
            }
        )

    total = sum(sentiment_scores.values())
    sentiment_percentages = {k: round(v / total * 100, 2) if total > 0 else 0 for k, v in sentiment_scores.items()}

    return {
        "post_id": post_id,
        "total_comments": total,
        "sentiment_distribution": sentiment_scores,
        "sentiment_percentages": sentiment_percentages,
        "sample_comments": comment_sentiments[:10],  # Return sample
    }


# MCP Tools
@mcp.tool()
@handle_tool_errors
async def instagram_detect_spam_comments(
    post_id: int,
    threshold: float = 0.7,
) -> dict[str, Any]:
    """
    Detect potential spam comments on a post

    Args:
        post_id: Instagram post ID
        threshold: Spam score threshold (0.0-1.0)

    Returns:
        Spam detection results
    """
    # Validate threshold
    if not 0.0 <= threshold <= 1.0:
        raise ValueError("Threshold must be between 0.0 and 1.0")

    result = await detect_spam_comments(post_id=post_id, threshold=threshold)
    return result  # type: ignore[no-any-return]


@mcp.tool()
@handle_tool_errors
async def instagram_get_top_commenters(
    profile_username: str,
    limit: int = 20,
) -> dict[str, Any]:
    """
    Get top commenters for a profile

    Args:
        profile_username: Instagram profile username
        limit: Number of top commenters to return

    Returns:
        Top commenters with engagement metrics
    """
    # Basic validation
    if not profile_username:
        raise ValueError("Profile username is required")

    limit = min(max(1, limit), 100)

    result = await get_top_commenters(profile_username=profile_username, limit=limit)
    return result  # type: ignore[no-any-return]


@mcp.tool()
@handle_tool_errors
async def instagram_bulk_delete_comments(
    comment_ids: list[int],
    reason: str = "Spam",
) -> dict[str, Any]:
    """
    Bulk delete comments (soft delete)

    Args:
        comment_ids: List of comment IDs to delete
        reason: Deletion reason

    Returns:
        Deletion results
    """
    # Validate input
    if not comment_ids:
        raise ValueError("At least one comment ID is required")

    if len(comment_ids) > 100:
        raise ValueError("Cannot delete more than 100 comments at once")

    result = await bulk_delete_comments(comment_ids=comment_ids, reason=reason)
    return result  # type: ignore[no-any-return]


@mcp.tool()
@handle_tool_errors
async def instagram_analyze_sentiment(
    post_id: int,
) -> dict[str, Any]:
    """
    Analyze sentiment of comments on a post

    Args:
        post_id: Instagram post ID

    Returns:
        Sentiment analysis results
    """
    result = await analyze_comment_sentiment(post_id=post_id)
    return result  # type: ignore[no-any-return]