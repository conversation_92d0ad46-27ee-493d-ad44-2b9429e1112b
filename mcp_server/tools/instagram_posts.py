"""
Instagram post management tools for MCP server
"""

import logging
from datetime import datetime
from typing import Any

from django.db.models import Q
from django.utils import timezone

from mcp_server.decorators.database import async_db_operation, track_sync_calls
from mcp_server.decorators.error_handler import handle_tool_errors
from mcp_server.errors import ErrorTypes, create_error_response
from mcp_server.main import mcp
from mcp_server.validators.base import PaginationParams

logger = logging.getLogger(__name__)

# Database helper functions

@async_db_operation
@track_sync_calls
def get_posts_by_username(username: str, filters: dict, limit: int, offset: int):
    """Get posts for Instagram profile"""
    from instagram_manager.models import InstagramPost, InstagramProfile
    
    # First get the profile
    try:
        profile = InstagramProfile.objects.get(username=username.lower())
    except InstagramProfile.DoesNotExist:
        return [], 0
    
    # Build queryset
    queryset = InstagramPost.objects.filter(profile=profile)
    
    # Apply filters
    for key, value in filters.items():
        if key == "order_by":
            queryset = queryset.order_by(value)
        else:
            queryset = queryset.filter(**{key: value})
    
    # Get total count before pagination
    total = queryset.count()
    
    # Apply pagination and prefetch
    posts = list(
        queryset
        .select_related("profile")
        .prefetch_related("media", "hashtags")
        [offset:offset + limit]
    )
    
    return posts, total

@async_db_operation
@track_sync_calls
def get_post_by_id(post_id: int):
    """Get single post with all relations"""
    from instagram_manager.models import InstagramPost
    
    return InstagramPost.objects.select_related(
        "profile"
    ).prefetch_related(
        "media",
        "comments",
        "hashtags"
    ).filter(id=post_id).first()

@async_db_operation
@track_sync_calls
def get_post_media(post_id: int):
    """Get media for a post"""
    from instagram_manager.models import InstagramMedia
    
    return list(InstagramMedia.objects.filter(
        post_id=post_id
    ).order_by("position"))

@async_db_operation
@track_sync_calls
def get_trending_posts_db(filters: dict, limit: int):
    """Get trending posts from database"""
    from django.db.models import ExpressionWrapper, F, FloatField

    from instagram_manager.models import InstagramPost
    
    # Calculate engagement rate
    engagement_rate = ExpressionWrapper(
        (F("like_count") + F("comment_count")) * 100.0 / F("profile__follower_count"),
        output_field=FloatField()
    )
    
    queryset = InstagramPost.objects.annotate(
        engagement_rate=engagement_rate
    ).filter(
        profile__follower_count__gt=0  # Avoid division by zero
    )
    
    # Apply filters
    for key, value in filters.items():
        queryset = queryset.filter(**{key: value})
    
    # Order by engagement rate
    posts = list(
        queryset
        .select_related("profile")
        .prefetch_related("media", "hashtags")
        .order_by("-engagement_rate")
        [:limit]
    )
    
    return posts

# MCP Tool implementations

@mcp.tool()
@handle_tool_errors
async def instagram_get_posts(
    username: str,
    limit: int = 20,
    offset: int = 0,
    post_type: str | None = None,
    start_date: str | None = None,
    end_date: str | None = None,
    min_likes: int | None = None,
    has_media: bool | None = None,
    order_by: str = "-posted_at"
) -> dict[str, Any]:
    """
    Get posts for a specific Instagram profile
    
    Args:
        username: Instagram username
        limit: Number of posts (1-100)
        offset: Skip posts
        post_type: Filter by type (photo, video, album, reel)
        start_date: Posts after this date (ISO format)
        end_date: Posts before this date (ISO format)
        min_likes: Minimum like count
        has_media: Filter posts with/without media
        order_by: Sort order (posted_at, -posted_at, like_count, -like_count)
        
    Returns:
        List of posts with details
    """
    # Normalize username
    username = username.strip().lower().lstrip("@")
    
    # Validate pagination
    pagination = PaginationParams(limit=limit, offset=offset)
    
    # Build filters
    filters: dict[str, Any] = {"order_by": order_by}
    
    if post_type:
        if post_type not in ["photo", "video", "album", "reel"]:
            return create_error_response(
                error_type=ErrorTypes.VALIDATION_ERROR,
                message=f"Invalid post type: {post_type}"
            )
        filters["post_type"] = post_type
    
    if start_date:
        try:
            filters["posted_at__gte"] = datetime.fromisoformat(start_date)
        except ValueError:
            return create_error_response(
                error_type=ErrorTypes.VALIDATION_ERROR,
                message="Invalid start_date format. Use ISO format (YYYY-MM-DD)"
            )
    
    if end_date:
        try:
            filters["posted_at__lte"] = datetime.fromisoformat(end_date)
        except ValueError:
            return create_error_response(
                error_type=ErrorTypes.VALIDATION_ERROR,
                message="Invalid end_date format. Use ISO format (YYYY-MM-DD)"
            )
    
    if min_likes is not None:
        filters["like_count__gte"] = min_likes
    
    if has_media is not None:
        filters["media__isnull"] = not has_media
    
    logger.info(f"Fetching posts for {username} with filters: {filters}")
    
    # Get posts
    posts, total = await get_posts_by_username(
        username=username,
        filters=filters,
        limit=pagination.limit,
        offset=pagination.offset
    )
    
    if total == 0 and offset == 0:
        return create_error_response(
            error_type=ErrorTypes.NOT_FOUND,
            message=f"No posts found for profile '{username}'"
        )
    
    # Format response
    return {
        "username": username,
        "total": total,
        "offset": pagination.offset,
        "limit": pagination.limit,
        "has_more": (pagination.offset + len(posts)) < total,
        "posts": [
            {
                "id": p.id,
                "external_id": p.external_id,
                "content": p.content[:200] + "..." if p.content and len(p.content) > 200 else p.content,
                "post_type": p.post_type,
                "post_url": p.post_url,
                "like_count": p.like_count,
                "comment_count": p.comment_count,
                "share_count": p.share_count,
                "view_count": p.view_count,
                "posted_at": p.posted_at.isoformat() if p.posted_at else None,
                "media_count": p.media.count(),
                "hashtags": [h.name for h in p.hashtags.all()[:10]]
            }
            for p in posts
        ]
    }

@mcp.tool()
@handle_tool_errors
async def instagram_get_post_details(post_id: int) -> dict[str, Any]:
    """
    Get detailed information about a specific post
    
    Args:
        post_id: Instagram post ID
        
    Returns:
        Detailed post information including media
    """
    logger.info(f"Fetching post details for ID: {post_id}")
    
    post = await get_post_by_id(post_id)
    
    if not post:
        return create_error_response(
            error_type=ErrorTypes.NOT_FOUND,
            message=f"Post {post_id} not found"
        )
    
    # Format media details
    media_items = []
    for media in post.media.all():
        media_items.append({
            "id": media.id,
            "media_type": media.media_type,
            "media_url": media.media_url,
            "thumbnail_url": media.thumbnail_url,
            "width": media.width,
            "height": media.height,
            "duration": media.duration,
            "is_downloaded": media.is_downloaded,
            "local_path": media.local_file.url if media.local_file else None,
            "gcs_url": media.gcs_url if hasattr(media, "gcs_url") else None,
            "position": media.position
        })
    
    # Format hashtags
    hashtags = [{"id": h.id, "name": h.name} for h in post.hashtags.all()]
    
    return {
        "id": post.id,
        "external_id": post.external_id,
        "profile": {
            "id": post.profile.id,
            "username": post.profile.username,
            "full_name": post.profile.full_name,
            "profile_pic_url": post.profile.profile_pic_url
        },
        "content": post.content,
        "post_type": post.post_type,
        "post_url": post.post_url,
        "like_count": post.like_count,
        "comment_count": post.comment_count,
        "share_count": post.share_count,
        "view_count": post.view_count,
        "posted_at": post.posted_at.isoformat() if post.posted_at else None,
        "location": post.location,
        "is_sponsored": post.is_sponsored,
        "media": media_items,
        "hashtags": hashtags,
        "engagement_rate": _calculate_engagement_rate(post),
        "created_at": post.created_at.isoformat(),
        "updated_at": post.updated_at.isoformat(),
        "last_scraped_at": post.last_scraped_at.isoformat() if post.last_scraped_at else None
    }

@mcp.tool()
@handle_tool_errors
async def instagram_get_trending_posts(
    limit: int = 20,
    time_period: str = "week",
    min_engagement_rate: float | None = None,
    min_followers: int = 1000
) -> dict[str, Any]:
    """
    Get trending posts based on engagement
    
    Args:
        limit: Number of posts (1-50)
        time_period: Period to analyze (today, week, month, all)
        min_engagement_rate: Minimum engagement rate percentage
        min_followers: Minimum follower count for profiles
        
    Returns:
        List of trending posts
    """
    # Validate limit
    limit = max(1, min(limit, 50))
    
    # Build date filter based on time period
    filters: dict[str, Any] = {"profile__follower_count__gte": min_followers}
    
    if time_period != "all":
        now = timezone.now()
        if time_period == "today":
            start_date = now.replace(hour=0, minute=0, second=0, microsecond=0)
        elif time_period == "week":
            start_date = now - timezone.timedelta(days=7)
        elif time_period == "month":
            start_date = now - timezone.timedelta(days=30)
        else:
            return create_error_response(
                error_type=ErrorTypes.VALIDATION_ERROR,
                message=f"Invalid time period: {time_period}"
            )
        filters["posted_at__gte"] = start_date
    
    logger.info(f"Fetching trending posts for period: {time_period}")
    
    # Get trending posts
    posts = await get_trending_posts_db(filters, limit)
    
    # Filter by minimum engagement rate if specified
    if min_engagement_rate is not None:
        posts = [p for p in posts if p.engagement_rate >= min_engagement_rate]
    
    return {
        "time_period": time_period,
        "count": len(posts),
        "filters": {
            "min_followers": min_followers,
            "min_engagement_rate": min_engagement_rate
        },
        "posts": [
            {
                "id": p.id,
                "profile": {
                    "username": p.profile.username,
                    "follower_count": p.profile.follower_count
                },
                "content": p.content[:100] + "..." if p.content and len(p.content) > 100 else p.content,
                "post_type": p.post_type,
                "post_url": p.post_url,
                "like_count": p.like_count,
                "comment_count": p.comment_count,
                "engagement_rate": round(p.engagement_rate, 2),
                "posted_at": p.posted_at.isoformat() if p.posted_at else None,
                "media_count": p.media.count(),
                "hashtags": [h.name for h in p.hashtags.all()[:5]]
            }
            for p in posts
        ]
    }

@mcp.tool()
@handle_tool_errors
async def instagram_search_posts(
    query: str,
    limit: int = 20,
    search_in: str = "all"
) -> dict[str, Any]:
    """
    Search posts by content or hashtags
    
    Args:
        query: Search query (min 2 characters)
        limit: Maximum results (1-50)
        search_in: Where to search (all, content, hashtags)
        
    Returns:
        List of matching posts
    """
    # Validate query
    if len(query) < 2:
        return create_error_response(
            error_type=ErrorTypes.VALIDATION_ERROR,
            message="Search query must be at least 2 characters"
        )
    
    # Validate limit
    limit = max(1, min(limit, 50))
    
    # Validate search_in
    if search_in not in ["all", "content", "hashtags"]:
        return create_error_response(
            error_type=ErrorTypes.VALIDATION_ERROR,
            message=f"Invalid search_in value: {search_in}"
        )
    
    logger.info(f"Searching posts for: '{query}' in {search_in}")
    
    # Search posts
    posts = await _search_posts_db(query, limit, search_in)
    
    return {
        "query": query,
        "search_in": search_in,
        "count": len(posts),
        "posts": [
            {
                "id": p.id,
                "profile": {
                    "username": p.profile.username,
                    "full_name": p.profile.full_name
                },
                "content": _highlight_query(p.content, query) if p.content else None,
                "post_type": p.post_type,
                "post_url": p.post_url,
                "like_count": p.like_count,
                "comment_count": p.comment_count,
                "posted_at": p.posted_at.isoformat() if p.posted_at else None,
                "hashtags": [h.name for h in p.hashtags.all() if query.lower() in h.name.lower()]
            }
            for p in posts
        ]
    }

# Helper functions

def _calculate_engagement_rate(post: Any) -> float:
    """Calculate engagement rate for a post"""
    if post.profile.follower_count > 0:
        total_engagement = post.like_count + post.comment_count
        rate = (total_engagement / post.profile.follower_count) * 100
        result = round(rate, 2)
        return result  # type: ignore[no-any-return]
    return 0.0

def _highlight_query(content: str, query: str, context_length: int = 50) -> str:
    """Highlight query in content with context"""
    if not content:
        return ""
    
    lower_content = content.lower()
    lower_query = query.lower()
    
    pos = lower_content.find(lower_query)
    if pos == -1:
        return content[:100] + "..." if len(content) > 100 else content
    
    # Get context around the match
    start = max(0, pos - context_length)
    end = min(len(content), pos + len(query) + context_length)
    
    snippet = content[start:end]
    if start > 0:
        snippet = "..." + snippet
    if end < len(content):
        snippet = snippet + "..."
    
    return snippet

@async_db_operation
@track_sync_calls
def _search_posts_db(query: str, limit: int, search_in: str):
    """Search posts in database"""
    from instagram_manager.models import InstagramPost
    
    queryset = InstagramPost.objects.select_related("profile").prefetch_related("hashtags")
    
    if search_in == "content":
        queryset = queryset.filter(content__icontains=query)
    elif search_in == "hashtags":
        queryset = queryset.filter(hashtags__name__icontains=query).distinct()
    else:  # all
        queryset = queryset.filter(
            Q(content__icontains=query) |
            Q(hashtags__name__icontains=query)
        ).distinct()
    
    return list(queryset.order_by("-posted_at")[:limit])