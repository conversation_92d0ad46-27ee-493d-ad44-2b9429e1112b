"""
Task creation and management tools for MCP server
"""

import logging
import uuid
from datetime import datetime
from typing import Any

from django.db.models import Avg, Count, F, Q
from django.utils import timezone

from mcp_server.decorators.database import async_db_operation, track_sync_calls
from mcp_server.decorators.error_handler import handle_tool_errors
from mcp_server.errors import ErrorTypes, create_error_response
from mcp_server.main import mcp
from mcp_server.validators.tasks import TaskCreateParams, TaskListParams, validate_task_parameters

logger = logging.getLogger(__name__)

# Import task registry from main
from mcp_server.main import TASK_REGISTRY  # noqa: E402

# Add missing task type if needed
if "instagram.followers" not in TASK_REGISTRY:
    from instagram_manager.tasks.import_tasks import ImportInstagramFollowersTask
    TASK_REGISTRY["instagram.followers"] = ImportInstagramFollowersTask


@async_db_operation
@track_sync_calls
def get_task_class(task_type: str):
    """Get task class from registry"""
    if task_type not in TASK_REGISTRY:
        raise ValueError(f"Unknown task type: {task_type}")
    
    # Return task class directly from registry
    return TASK_REGISTRY[task_type]


@async_db_operation
@track_sync_calls
def submit_task(task_class, task_id: str, parameters: dict):
    """Submit task for execution"""
    # Pass task_id to constructor instead of setting it later
    task_instance = task_class(task_id=task_id)
    
    # Submit async execution
    result = task_instance.run_async(**parameters)
    
    return result


async def _create_import_task_impl(
    task_type: str,
    parameters: dict[str, Any],
    priority: str = "normal"
) -> dict[str, Any]:
    """
    Create a new import task
    
    Args:
        task_type: Type of import task (instagram.profile, telegram.messages, etc.)
        parameters: Task-specific parameters
        priority: Task priority (low, normal, high)
        
    Returns:
        Task creation result with task_id
    """
    # Validate task type and priority
    try:
        TaskCreateParams(
            task_type=task_type,
            parameters=parameters,
            priority=priority
        )
    except Exception as e:
        return create_error_response(
            error_type=ErrorTypes.VALIDATION_ERROR,
            message=str(e)
        )
    
    # Validate task-specific parameters
    try:
        validated_task_params = validate_task_parameters(task_type, parameters)
    except Exception as e:
        return create_error_response(
            error_type=ErrorTypes.VALIDATION_ERROR,
            message=f"Invalid parameters for {task_type}: {e!s}"
        )
    
    # Generate task ID
    task_id = str(uuid.uuid4())
    
    logger.info(f"Creating {task_type} task with ID: {task_id}")
    
    try:
        # Get task class
        task_class = await get_task_class(task_type)
        
        # Add priority to parameters for TaskResult creation
        task_params = validated_task_params.copy()
        task_params["_priority"] = priority
        
        # Submit task for execution
        # Task will be created in run_async method of BaseTask
        celery_result = await submit_task(
            task_class=task_class,
            task_id=task_id,
            parameters=task_params
        )
        
        # Get the created TaskResult to retrieve created_at
        from core.models import TaskResult
        task_result = await TaskResult.objects.aget(task_id=task_id)
        
        return {
            "task_id": task_id,
            "celery_task_id": celery_result.id,
            "task_type": task_type,
            "status": "created",
            "priority": priority,
            "parameters": validated_task_params,
            "created_at": task_result.created_at.isoformat(),
            "estimated_duration": _estimate_task_duration(task_type, validated_task_params)
        }
        
    except Exception as e:
        logger.error(f"Failed to create task: {e!s}", exc_info=True)
        return create_error_response(
            error_type=ErrorTypes.INTERNAL_ERROR,
            message=f"Failed to create task: {e!s}"
        )


@mcp.tool()
@handle_tool_errors
async def task_create_import(
    task_type: str,
    parameters: dict[str, Any],
    priority: str = "normal"
) -> dict[str, Any]:
    """
    Create a new import task
    
    Args:
        task_type: Type of import task (instagram.profile, telegram.messages, etc.)
        parameters: Task-specific parameters
        priority: Task priority (low, normal, high)
        
    Returns:
        Task creation result with task_id
    """
    return await _create_import_task_impl(task_type, parameters, priority)


def _estimate_task_duration(task_type: str, parameters: dict) -> str:
    """Estimate task duration based on type and parameters"""
    
    estimates = {
        "instagram.profile": "30-60 seconds",
        "instagram.posts": f"{parameters.get('limit', 50) * 2} seconds",
        "instagram.batch_posts": f"{len(parameters.get('usernames', [])) * 60} seconds",
        "instagram.comments": "2-5 minutes",
        "instagram.followers": f"{parameters.get('limit', 1000) // 100} minutes",
        "telegram.chats": "1-2 minutes",
        "telegram.messages": f"{parameters.get('limit', 100) // 10} minutes",
        "telegram.users": "1-3 minutes"
    }
    
    return estimates.get(task_type, "Unknown")


@async_db_operation
@track_sync_calls
def get_task_by_id(task_id: str):
    """Get task by ID"""
    from core.models import TaskResult
    
    return TaskResult.objects.filter(task_id=task_id).first()


@mcp.tool()
@handle_tool_errors
async def task_get_status(task_id: str) -> dict[str, Any]:
    """
    Get status of a specific task
    
    Args:
        task_id: Task UUID
        
    Returns:
        Detailed task status and progress
    """
    task = await get_task_by_id(task_id)
    
    if not task:
        return create_error_response(
            error_type=ErrorTypes.NOT_FOUND,
            message=f"Task {task_id} not found"
        )
    
    # Sync with Celery if available
    if task.celery_task_id:
        from asgiref.sync import sync_to_async
        await sync_to_async(task.sync_with_celery)()
    
    # Calculate duration
    duration = None
    if task.started_at and task.completed_at:
        duration = (task.completed_at - task.started_at).total_seconds()
    elif task.started_at:
        duration = (timezone.now() - task.started_at).total_seconds()
    
    return {
        "task_id": task.task_id,
        "task_type": task.task_type,
        "status": task.status,
        "priority": task.priority,
        "progress": task.progress_percentage,
        "progress_message": task.progress_message,
        "parameters": task.parameters,
        "result": task.result,
        "error": task.error_message,
        "created_at": task.created_at.isoformat(),
        "updated_at": task.updated_at.isoformat(),
        "started_at": task.started_at.isoformat() if task.started_at else None,
        "completed_at": task.completed_at.isoformat() if task.completed_at else None,
        "duration": duration,
        "retry_count": task.retry_count,
        "max_retries": task.max_retries,
        "celery_task_id": task.celery_task_id,
        "celery_status": task.celery_status,
        "total_items": task.total_items,
        "processed_items": task.processed_items,
        "failed_items": task.failed_items
    }


@async_db_operation
@track_sync_calls
def list_tasks(filters: dict, limit: int, offset: int):
    """List tasks with filters"""
    from core.models import TaskResult
    
    queryset = TaskResult.objects.all()
    
    # Apply filters
    if filters.get("status"):
        queryset = queryset.filter(status=filters["status"])
    if filters.get("task_type"):
        queryset = queryset.filter(task_type=filters["task_type"])
    if filters.get("created_after"):
        queryset = queryset.filter(created_at__gte=filters["created_after"])
    if filters.get("created_before"):
        queryset = queryset.filter(created_at__lte=filters["created_before"])
    
    # Order by priority and creation time
    queryset = queryset.order_by("-priority", "-created_at")
    
    # Get total count
    total = queryset.count()
    
    # Apply pagination
    tasks = list(queryset[offset:offset + limit])
    
    return tasks, total


@mcp.tool()
@handle_tool_errors
async def task_list(
    status: str | None = None,
    task_type: str | None = None,
    limit: int = 20,
    offset: int = 0,
    created_after: str | None = None,
    created_before: str | None = None
) -> dict[str, Any]:
    """
    List tasks with filtering
    
    Args:
        status: Filter by status (pending, running, completed, failed, cancelled, retry)
        task_type: Filter by task type
        limit: Number of tasks (1-100)
        offset: Skip tasks
        created_after: Tasks created after this date (ISO format)
        created_before: Tasks created before this date (ISO format)
        
    Returns:
        List of tasks with details
    """
    # Validate parameters
    try:
        params = TaskListParams(
            status=status,
            task_type=task_type,
            limit=limit,
            offset=offset,
            created_after=datetime.fromisoformat(created_after) if created_after else None,
            created_before=datetime.fromisoformat(created_before) if created_before else None
        )
    except Exception as e:
        return create_error_response(
            error_type=ErrorTypes.VALIDATION_ERROR,
            message=str(e)
        )
    
    # Build filters
    filters: dict[str, Any] = {}
    if params.status:
        filters["status"] = params.status
    if params.task_type:
        filters["task_type"] = params.task_type
    if params.created_after:
        filters["created_after"] = params.created_after
    if params.created_before:
        filters["created_before"] = params.created_before
    
    # Get tasks
    tasks, total = await list_tasks(
        filters=filters,
        limit=params.limit,
        offset=params.offset
    )
    
    # Calculate duration for each task
    def calc_duration(task):
        if task.started_at and task.completed_at:
            return (task.completed_at - task.started_at).total_seconds()
        elif task.started_at:
            return (timezone.now() - task.started_at).total_seconds()
        return None
    
    return {
        "total": total,
        "offset": offset,
        "limit": limit,
        "has_more": (offset + len(tasks)) < total,
        "tasks": [
            {
                "task_id": t.task_id,
                "task_type": t.task_type,
                "status": t.status,
                "priority": t.priority,
                "progress": t.progress_percentage,
                "progress_message": t.progress_message,
                "created_at": t.created_at.isoformat(),
                "updated_at": t.updated_at.isoformat(),
                "duration": calc_duration(t),
                "retry_count": t.retry_count,
                "total_items": t.total_items,
                "processed_items": t.processed_items
            }
            for t in tasks
        ]
    }


@async_db_operation
@track_sync_calls
def update_task_status(task_id: str, status: str):
    """Update task status"""
    from core.models import TaskResult
    
    TaskResult.objects.filter(task_id=task_id).update(
        status=status,
        updated_at=timezone.now()
    )


@mcp.tool()
@handle_tool_errors
async def task_cancel(task_id: str) -> dict[str, Any]:
    """
    Cancel a running task
    
    Args:
        task_id: Task UUID to cancel
        
    Returns:
        Cancellation result
    """
    task = await get_task_by_id(task_id)
    
    if not task:
        return create_error_response(
            error_type=ErrorTypes.NOT_FOUND,
            message=f"Task {task_id} not found"
        )
    
    if task.status not in ["pending", "running", "retry"]:
        return create_error_response(
            error_type=ErrorTypes.INVALID_STATE,
            message=f"Cannot cancel task in {task.status} state"
        )
    
    # Try to revoke Celery task
    revoked = False
    if task.celery_task_id:
        from asgiref.sync import sync_to_async
        revoked = await sync_to_async(task.revoke_celery_task)(terminate=True)
    
    # Update task status
    await update_task_status(task_id, "cancelled")
    
    return {
        "task_id": task_id,
        "status": "cancelled",
        "revoked": revoked,
        "message": "Task cancellation requested"
    }


@mcp.tool()
@handle_tool_errors
async def task_retry(task_id: str) -> dict[str, Any]:
    """
    Retry a failed task
    
    Args:
        task_id: Task UUID to retry
        
    Returns:
        New task creation result
    """
    task = await get_task_by_id(task_id)
    
    if not task:
        return create_error_response(
            error_type=ErrorTypes.NOT_FOUND,
            message=f"Task {task_id} not found"
        )
    
    if task.status not in ["failed", "cancelled"]:
        return create_error_response(
            error_type=ErrorTypes.INVALID_STATE,
            message=f"Can only retry failed or cancelled tasks, current status: {task.status}"
        )
    
    # Check retry limit
    if not task.can_retry():
        return create_error_response(
            error_type=ErrorTypes.RETRY_LIMIT_EXCEEDED,
            message=f"Task has exceeded maximum retries ({task.max_retries})"
        )
    
    # Create new task with same parameters
    result = await _create_import_task_impl(
        task_type=task.task_type,
        parameters=task.parameters,
        priority="normal"
    )
    return result  # type: ignore[no-any-return]


@async_db_operation
@track_sync_calls
def get_queue_statistics():
    """Get comprehensive queue statistics"""
    from django.db.models import DurationField, ExpressionWrapper
    from django.utils import timezone

    from core.models import TaskResult
    
    now = timezone.now()
    today_start = now.replace(hour=0, minute=0, second=0, microsecond=0)
    
    # Get counts by status
    status_counts = TaskResult.objects.values("status").annotate(
        count=Count("id")
    ).order_by("status")
    
    # Convert to dict
    status_dict = {item["status"]: item["count"] for item in status_counts}
    
    # Get today's completed and failed
    today_completed = TaskResult.objects.filter(
        status="completed",
        completed_at__gte=today_start
    ).count()
    
    today_failed = TaskResult.objects.filter(
        status="failed",
        updated_at__gte=today_start
    ).count()
    
    # Get average duration for completed tasks
    duration_expr = ExpressionWrapper(
        F("completed_at") - F("started_at"),
        output_field=DurationField()
    )
    
    avg_duration_result = TaskResult.objects.filter(
        status="completed",
        started_at__isnull=False,
        completed_at__isnull=False
    ).annotate(
        duration=duration_expr
    ).aggregate(
        avg_duration=Avg("duration")
    )
    
    avg_duration = avg_duration_result["avg_duration"]
    avg_duration_seconds = avg_duration.total_seconds() if avg_duration else 0
    
    # Get counts by task type
    type_counts = TaskResult.objects.values("task_type").annotate(
        total=Count("id"),
        pending=Count("id", filter=Q(status="pending")),
        running=Count("id", filter=Q(status="running")),
        completed=Count("id", filter=Q(status="completed")),
        failed=Count("id", filter=Q(status="failed"))
    ).order_by("task_type")
    
    return {
        "queues": {
            "default": {
                "pending": status_dict.get("pending", 0),
                "running": status_dict.get("running", 0)
            }
        },
        "total_pending": status_dict.get("pending", 0),
        "total_running": status_dict.get("running", 0),
        "total_completed_today": today_completed,
        "total_failed_today": today_failed,
        "average_duration": avg_duration_seconds,
        "task_types": list(type_counts)
    }


@mcp.tool()
@handle_tool_errors
async def task_get_queue_stats() -> dict[str, Any]:
    """
    Get task queue statistics
    
    Returns:
        Queue statistics and task counts
    """
    stats = await get_queue_statistics()
    
    return {
        "queues": stats["queues"],
        "total_pending": stats["total_pending"],
        "total_running": stats["total_running"],
        "total_completed_today": stats["total_completed_today"],
        "total_failed_today": stats["total_failed_today"],
        "average_duration_seconds": stats["average_duration"],
        "average_duration_formatted": f"{stats['average_duration'] // 60:.0f}m {stats['average_duration'] % 60:.0f}s" if stats["average_duration"] else "N/A",
        "task_types": stats["task_types"],
        "timestamp": timezone.now().isoformat()
    }