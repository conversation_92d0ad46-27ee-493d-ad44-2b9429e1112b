"""
Telegram chat management tools
"""

import logging
from datetime import <PERSON><PERSON><PERSON>
from typing import Any

from django.db.models import Avg, Count, Q

from mcp_server.decorators.database import async_db_operation, track_sync_calls
from mcp_server.decorators.error_handler import handle_tool_errors
from mcp_server.errors import ErrorTypes, create_error_response
from mcp_server.main import mcp
from mcp_server.validators.telegram import ChatTypeValidator

logger = logging.getLogger(__name__)

# Database helper functions with proper decorators

@async_db_operation
@track_sync_calls
def get_telegram_chat_by_id(chat_id: int):
    """Get Telegram chat by ID"""
    from telegram_manager.models import TelegramChat
    return TelegramChat.objects.filter(chat_id=chat_id).first()

@async_db_operation
@track_sync_calls
def list_telegram_chats_db(
    limit: int = 20,
    offset: int = 0,
    **filters
) -> tuple[list, int]:
    """List Telegram chats with pagination and filters"""
    from telegram_manager.models import TelegramChat
    
    queryset = TelegramChat.objects.all()
    
    # Apply filters
    if filters.get("chat_type"):
        chat_type = filters["chat_type"]
        if chat_type == "private":
            queryset = queryset.filter(broadcast=False, megagroup=False)
        elif chat_type == "group":
            queryset = queryset.filter(broadcast=False, megagroup=False)
        elif chat_type == "supergroup":
            queryset = queryset.filter(megagroup=True)
        elif chat_type == "channel":
            queryset = queryset.filter(broadcast=True)
    
    if filters.get("is_active") is not None:
        queryset = queryset.filter(left=not filters["is_active"])
    
    if filters.get("min_members") is not None:
        queryset = queryset.filter(participants_count__gte=filters["min_members"])
    
    # Get total count
    total = queryset.count()
    
    # Order by participants count
    queryset = queryset.order_by("-participants_count", "-created_at")
    
    # Get paginated results
    chats = list(queryset[offset:offset + limit])
    
    return chats, total

@async_db_operation
@track_sync_calls
def get_chat_statistics_db(chat_id: int, period: str = "week") -> dict:
    """Get statistics for a chat"""
    from django.utils import timezone

    from telegram_manager.models import TelegramMessage
    
    # Calculate date range based on period
    end_date = timezone.now()
    if period == "today":
        start_date = end_date.replace(hour=0, minute=0, second=0, microsecond=0)
    elif period == "week":
        start_date = end_date - timedelta(days=7)
    elif period == "month":
        start_date = end_date - timedelta(days=30)
    else:  # all
        start_date = None
    
    # Base queryset
    message_qs = TelegramMessage.objects.filter(chat__chat_id=chat_id)
    if start_date:
        message_qs = message_qs.filter(date__gte=start_date)
    
    # Calculate statistics
    stats = message_qs.aggregate(
        total_messages=Count("id"),
        unique_users=Count("from_user", distinct=True),
        avg_message_length=Avg("message_length"),
        total_replies=Count("reply_to_msg_id"),
    )
    
    # Get active users (top 10 by message count)
    active_users = (
        message_qs.values("from_user__username", "from_user__first_name", "from_user__last_name", "from_user__user_id")
        .annotate(message_count=Count("id"))
        .order_by("-message_count")[:10]
    )
    
    # Message distribution by hour (for the period)
    hourly_distribution = []
    if start_date:
        hourly_data = (
            message_qs.extra(select={"hour": "EXTRACT(hour FROM date)"})
            .values("hour")
            .annotate(count=Count("id"))
            .order_by("hour")
        )
        hourly_distribution = list(hourly_data)
    
    return {
        "period": period,
        "start_date": start_date.isoformat() if start_date else None,
        "end_date": end_date.isoformat(),
        "total_messages": stats.get("total_messages", 0),
        "unique_users": stats.get("unique_users", 0),
        "avg_message_length": round(stats.get("avg_message_length", 0) or 0, 2),
        "total_replies": stats.get("total_replies", 0),
        "active_users": [
            {
                "user_id": user["from_user__user_id"],
                "username": user["from_user__username"],
                "first_name": user["from_user__first_name"],
                "last_name": user["from_user__last_name"],
                "message_count": user["message_count"]
            }
            for user in active_users
        ],
        "hourly_distribution": hourly_distribution
    }

@async_db_operation
@track_sync_calls
def search_telegram_chats_db(query: str, limit: int = 20) -> list:
    """Search Telegram chats by title or username"""
    from telegram_manager.models import TelegramChat
    
    queryset = TelegramChat.objects.filter(
        Q(title__icontains=query) | 
        Q(username__icontains=query)
    ).order_by("-participants_count")[:limit]
    
    return list(queryset)

# MCP Tools

@mcp.tool()
@handle_tool_errors
async def telegram_list_chats(
    limit: int = 20,
    offset: int = 0,
    chat_type: str | None = None,
    is_active: bool | None = None,
    min_members: int | None = None
) -> dict[str, Any]:
    """
    List Telegram chats with filtering
    
    Args:
        limit: Number of chats (1-100)
        offset: Skip chats
        chat_type: Filter by type (private, group, supergroup, channel)
        is_active: Filter active chats
        min_members: Minimum member count
        
    Returns:
        List of chats with details
    """
    # Validate parameters
    if limit < 1 or limit > 100:
        return create_error_response(
            error_type=ErrorTypes.VALIDATION_ERROR,
            message="Limit must be between 1 and 100"
        )
    
    if offset < 0:
        return create_error_response(
            error_type=ErrorTypes.VALIDATION_ERROR,
            message="Offset must be non-negative"
        )
    
    # Validate chat type if provided
    if chat_type:
        validator = ChatTypeValidator()
        if not validator.validate_value(chat_type):
            return create_error_response(
                error_type=ErrorTypes.VALIDATION_ERROR,
                message=f"Invalid chat type. Must be one of: {', '.join(validator.allowed_types)}"
            )
    
    logger.info(f"Listing Telegram chats: limit={limit}, offset={offset}, filters={chat_type}, {is_active}, {min_members}")
    
    # Build filters
    filters: dict[str, Any] = {}
    if chat_type:
        filters["chat_type"] = chat_type
    if is_active is not None:
        filters["is_active"] = is_active
    if min_members is not None:
        filters["min_members"] = min_members
    
    # Get chats from database
    chats, total = await list_telegram_chats_db(limit, offset, **filters)
    
    # Format response
    return {
        "chats": [
            {
                "chat_id": chat.chat_id,
                "title": chat.title,
                "username": chat.username,
                "type": _get_chat_type(chat),
                "is_active": not chat.left,
                "is_verified": chat.verified,
                "is_creator": chat.creator,
                "member_count": chat.participants_count,
                "created_at": chat.created_at.isoformat() if chat.created_at else None,
                "updated_at": chat.updated_at.isoformat() if chat.updated_at else None,
            }
            for chat in chats
        ],
        "pagination": {
            "total": total,
            "limit": limit,
            "offset": offset,
            "has_more": offset + limit < total
        }
    }

@mcp.tool()
@handle_tool_errors
async def telegram_get_chat_details(
    chat_id: int
) -> dict[str, Any]:
    """
    Get detailed information about a Telegram chat
    
    Args:
        chat_id: Telegram chat ID
        
    Returns:
        Detailed chat information
    """
    logger.info(f"Fetching Telegram chat details: {chat_id}")
    
    # Get chat from database
    chat = await get_telegram_chat_by_id(chat_id)
    
    if not chat:
        return create_error_response(
            error_type=ErrorTypes.NOT_FOUND,
            message=f"Chat with ID {chat_id} not found"
        )
    
    # Get additional statistics
    stats = await get_chat_statistics_db(chat_id, "all")
    
    return {
        "chat_id": chat.chat_id,
        "title": chat.title,
        "username": chat.username,
        "type": _get_chat_type(chat),
        "is_active": not chat.left,
        "is_verified": chat.verified,
        "is_creator": chat.creator,
        "is_scam": chat.scam,
        "is_restricted": chat.restricted,
        "has_geo": chat.has_geo,
        "has_link": chat.has_link,
        "member_count": chat.participants_count,
        "photo_id": chat.photo_id,
        "created_at": chat.created_at.isoformat() if chat.created_at else None,
        "updated_at": chat.updated_at.isoformat() if chat.updated_at else None,
        "statistics": {
            "total_messages": stats.get("total_messages", 0),
            "unique_users": stats.get("unique_users", 0),
            "avg_message_length": stats.get("avg_message_length", 0),
            "total_replies": stats.get("total_replies", 0)
        }
    }

@mcp.tool()
@handle_tool_errors
async def telegram_get_chat_statistics(
    chat_id: int,
    period: str = "week"
) -> dict[str, Any]:
    """
    Get statistics for a Telegram chat
    
    Args:
        chat_id: Telegram chat ID
        period: Time period (today, week, month, all)
        
    Returns:
        Chat statistics and activity metrics
    """
    # Validate period
    valid_periods = ["today", "week", "month", "all"]
    if period not in valid_periods:
        return create_error_response(
            error_type=ErrorTypes.VALIDATION_ERROR,
            message=f"Invalid period. Must be one of: {', '.join(valid_periods)}"
        )
    
    logger.info(f"Getting statistics for chat {chat_id}, period: {period}")
    
    # Check if chat exists
    chat = await get_telegram_chat_by_id(chat_id)
    if not chat:
        return create_error_response(
            error_type=ErrorTypes.NOT_FOUND,
            message=f"Chat with ID {chat_id} not found"
        )
    
    # Get statistics
    stats = await get_chat_statistics_db(chat_id, period)
    
    return {
        "chat_id": chat_id,
        "chat_title": chat.title,
        "period": stats["period"],
        "date_range": {
            "start": stats["start_date"],
            "end": stats["end_date"]
        },
        "message_statistics": {
            "total_messages": stats["total_messages"],
            "unique_users": stats["unique_users"],
            "avg_message_length": stats["avg_message_length"],
            "total_replies": stats["total_replies"],
            "reply_rate": round(stats["total_replies"] / stats["total_messages"] * 100, 2) if stats["total_messages"] > 0 else 0
        },
        "active_users": stats["active_users"],
        "hourly_distribution": stats["hourly_distribution"],
        "engagement_metrics": {
            "messages_per_user": round(stats["total_messages"] / stats["unique_users"], 2) if stats["unique_users"] > 0 else 0,
            "active_user_percentage": round(stats["unique_users"] / chat.participants_count * 100, 2) if chat.participants_count and chat.participants_count > 0 else 0
        }
    }

@mcp.tool()
@handle_tool_errors
async def telegram_search_chats(
    query: str,
    limit: int = 20
) -> dict[str, Any]:
    """
    Search Telegram chats by title or username
    
    Args:
        query: Search query
        limit: Maximum results
        
    Returns:
        Matching chats
    """
    # Validate parameters
    if not query or len(query.strip()) < 2:
        return create_error_response(
            error_type=ErrorTypes.VALIDATION_ERROR,
            message="Search query must be at least 2 characters long"
        )
    
    if limit < 1 or limit > 100:
        return create_error_response(
            error_type=ErrorTypes.VALIDATION_ERROR,
            message="Limit must be between 1 and 100"
        )
    
    logger.info(f"Searching Telegram chats: query='{query}', limit={limit}")
    
    # Search chats
    chats = await search_telegram_chats_db(query.strip(), limit)
    
    return {
        "query": query,
        "results": [
            {
                "chat_id": chat.chat_id,
                "title": chat.title,
                "username": chat.username,
                "type": _get_chat_type(chat),
                "is_active": not chat.left,
                "is_verified": chat.verified,
                "member_count": chat.participants_count,
                "relevance_score": _calculate_relevance_score(chat, query)
            }
            for chat in chats
        ],
        "total_results": len(chats)
    }

# Helper functions

def _get_chat_type(chat) -> str:
    """Determine chat type from model fields"""
    if chat.broadcast:
        return "channel"
    elif chat.megagroup:
        return "supergroup"
    else:
        return "group"

def _calculate_relevance_score(chat, query: str) -> float:
    """Calculate relevance score for search results"""
    query_lower = query.lower()
    score = 0.0
    
    # Exact match in username
    if chat.username and chat.username.lower() == query_lower:
        score += 1.0
    # Partial match in username
    elif chat.username and query_lower in chat.username.lower():
        score += 0.7
    
    # Exact match in title
    if chat.title.lower() == query_lower:
        score += 0.8
    # Partial match in title
    elif query_lower in chat.title.lower():
        score += 0.5
    
    # Boost for verified chats
    if chat.verified:
        score += 0.2
    
    # Boost based on member count (normalized)
    if chat.participants_count:
        score += min(chat.participants_count / 10000, 0.3)
    
    return round(score, 2)