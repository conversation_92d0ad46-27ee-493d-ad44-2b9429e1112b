"""
Telegram message management tools for MCP server.

This module provides tools for retrieving, searching, and analyzing Telegram messages.
"""

from datetime import datetime, timedelta
from typing import Any

from django.db.models import Avg, Count
from django.utils import timezone

from mcp_server.decorators.database import async_db_operation, track_sync_calls
from mcp_server.decorators.error_handler import handle_tool_errors
from mcp_server.main import mcp
from telegram_manager.models import TelegramMessage

# Database operations

@async_db_operation
@track_sync_calls
def get_messages_from_chat_db(
    chat_id: int,
    limit: int = 50,
    offset: int = 0,
    start_date: str | None = None,
    end_date: str | None = None,
    from_user_id: int | None = None,
    message_type: str | None = None
) -> tuple[list[TelegramMessage], int]:
    """Get messages from a specific chat with filters."""
    queryset = TelegramMessage.objects.filter(chat_id=chat_id)
    
    # Apply date filters
    if start_date:
        start_dt = _parse_date_filter(start_date)
        if start_dt:
            queryset = queryset.filter(date__gte=start_dt)
    
    if end_date:
        end_dt = _parse_date_filter(end_date)
        if end_dt:
            queryset = queryset.filter(date__lte=end_dt)
    
    # Apply user filter
    if from_user_id:
        queryset = queryset.filter(from_user_id=from_user_id)
    
    # Apply message type filter
    if message_type:
        if message_type == "text":
            queryset = queryset.filter(media_type="")
        elif message_type in ["photo", "video", "document", "audio", "voice"]:
            queryset = queryset.filter(media_type=message_type)
    
    # Get total count before pagination
    total = queryset.count()
    
    # Order and paginate
    queryset = queryset.select_related("from_user", "reply_to").order_by("-date")
    messages = list(queryset[offset:offset + limit])
    
    return messages, total


@async_db_operation
@track_sync_calls
def search_messages_db(
    query: str,
    chat_ids: list[int] | None = None,
    limit: int = 50
) -> list[TelegramMessage]:
    """Search messages across chats."""
    queryset = TelegramMessage.objects.filter(text__icontains=query)
    
    # Filter by specific chats if provided
    if chat_ids:
        queryset = queryset.filter(chat_id__in=chat_ids)
    
    # Select related data and order by relevance (most recent first)
    queryset = queryset.select_related("from_user", "chat").order_by("-date")
    
    return list(queryset[:limit])


@async_db_operation
@track_sync_calls
def get_message_statistics_db(
    chat_id: int,
    period: str = "week",
    group_by: str = "day"
) -> dict[str, Any]:
    """Get message statistics for a chat."""
    # Calculate time period
    start_date, end_date = _calculate_statistics_period(period)
    
    # Get messages in period
    messages = TelegramMessage.objects.filter(
        chat_id=chat_id,
        date__gte=start_date,
        date__lte=end_date
    )
    
    # Basic statistics
    stats = messages.aggregate(
        total_count=Count("id"),
        avg_length=Avg("text")
    )
    
    # Get distribution based on grouping
    if group_by == "hour":
        distribution = messages.extra(
            select={"hour": "EXTRACT(hour FROM date)"}
        ).values("hour").annotate(count=Count("id")).order_by("hour")
    else:  # day
        distribution = messages.extra(
            select={"day": "DATE(date)"}
        ).values("day").annotate(count=Count("id")).order_by("day")
    
    # Media type breakdown
    media_stats = messages.values("media_type").annotate(
        count=Count("id")
    ).order_by("-count")
    
    # Top users
    top_users = messages.values(
        "from_user__user_id",
        "from_user__username",
        "from_user__first_name",
        "from_user__last_name"
    ).annotate(
        message_count=Count("id")
    ).order_by("-message_count")[:10]
    
    # Format results
    result = {
        "total_messages": stats["total_count"] or 0,
        "avg_message_length": stats["avg_length"] or 0,
        "period": {
            "start": start_date.isoformat(),
            "end": end_date.isoformat()
        },
        "hourly_distribution" if group_by == "hour" else "daily_distribution": list(distribution),
        "media_breakdown": {
            item["media_type"] or "text": item["count"]
            for item in media_stats
        },
        "top_users": list(top_users)
    }
    
    return result


# Helper functions

def _parse_date_filter(date_str: str | None) -> datetime | None:
    """Parse date string to datetime object."""
    if not date_str:
        return None
    
    try:
        # Try parsing ISO format
        if "T" in date_str:
            return datetime.fromisoformat(date_str.replace("Z", "+00:00"))
        else:
            # Parse date only
            return datetime.strptime(date_str, "%Y-%m-%d")
    except (ValueError, TypeError):
        return None


def _format_message_for_response(message: TelegramMessage) -> dict[str, Any]:
    """Format message object for API response."""
    data = {
        "message_id": message.message_id,
        "chat_id": message.chat_id,
        "text": message.text,
        "date": message.date.isoformat() if message.date else None,
        "from_user": None,
        "media_type": message.media_type,
        "is_reply": message.is_reply,
        "is_forward": message.is_forward,
        "is_edited": message.is_edited,
        "views": message.views,
        "forwards": message.forwards
    }
    
    # Add user info if available
    if message.from_user:
        data["from_user"] = {
            "user_id": message.from_user.user_id,
            "username": message.from_user.username,
            "first_name": message.from_user.first_name,
            "last_name": message.from_user.last_name
        }
    elif message.from_user_id:
        data["from_user_id"] = message.from_user_id
    
    return data


def _calculate_statistics_period(period: str) -> tuple[datetime, datetime]:
    """Calculate start and end dates for statistics period."""
    end_date = timezone.now()
    
    if period == "day":
        start_date = end_date - timedelta(days=1)
    elif period == "month":
        start_date = end_date - timedelta(days=30)
    elif period == "year":
        start_date = end_date - timedelta(days=365)
    else:  # Default to week
        start_date = end_date - timedelta(days=7)
    
    return start_date, end_date


# MCP Tools

@mcp.tool()
@handle_tool_errors
async def telegram_get_messages(
    chat_id: int,
    limit: int = 50,
    offset: int = 0,
    start_date: str | None = None,
    end_date: str | None = None,
    from_user_id: int | None = None,
    message_type: str | None = None
) -> dict[str, Any]:
    """
    Get messages from a Telegram chat
    
    Args:
        chat_id: Telegram chat ID
        limit: Number of messages (max 100)
        offset: Skip messages for pagination
        start_date: Filter from date (ISO format)
        end_date: Filter to date (ISO format)
        from_user_id: Filter by sender user ID
        message_type: Filter by type (text, photo, video, document, audio, voice)
        
    Returns:
        List of messages with details
    """
    # Validate inputs
    limit = min(limit, 100)  # Cap at 100
    offset = max(offset, 0)
    
    # Get messages from database
    messages, total = await get_messages_from_chat_db(
        chat_id=chat_id,
        limit=limit,
        offset=offset,
        start_date=start_date,
        end_date=end_date,
        from_user_id=from_user_id,
        message_type=message_type
    )
    
    # Format messages
    formatted_messages = [
        _format_message_for_response(msg) for msg in messages
    ]
    
    return {
        "messages": formatted_messages,
        "pagination": {
            "total": total,
            "limit": limit,
            "offset": offset,
            "has_more": offset + limit < total
        },
        "message": f"Retrieved {len(messages)} messages from chat {chat_id}"
    }


@mcp.tool()
@handle_tool_errors
async def telegram_search_messages(
    query: str,
    chat_ids: list[int] | None = None,
    limit: int = 50
) -> dict[str, Any]:
    """
    Search messages across Telegram chats
    
    Args:
        query: Search text
        chat_ids: Limit to specific chat IDs (optional)
        limit: Maximum results (max 100)
        
    Returns:
        Matching messages with chat info
    """
    # Validate inputs
    if not query or len(query) < 2:
        raise ValueError("Search query must be at least 2 characters long")
    
    limit = min(limit, 100)
    
    # Search messages
    messages = await search_messages_db(
        query=query,
        chat_ids=chat_ids,
        limit=limit
    )
    
    # Format results with chat info
    results = []
    for msg in messages:
        result = _format_message_for_response(msg)
        # Add chat info
        if msg.chat:
            result["chat"] = {
                "chat_id": msg.chat.chat_id,
                "title": msg.chat.title,
                "username": msg.chat.username
            }
        results.append(result)
    
    return {
        "results": results,
        "query": query,
        "total_found": len(results),
        "message": f"Found {len(results)} messages matching '{query}'"
    }


@mcp.tool()
@handle_tool_errors
async def telegram_get_message_statistics(
    chat_id: int,
    period: str = "week",
    group_by: str = "day"
) -> dict[str, Any]:
    """
    Get message statistics for a Telegram chat
    
    Args:
        chat_id: Telegram chat ID
        period: Time period (day, week, month, year)
        group_by: Grouping (hour, day)
        
    Returns:
        Message statistics and analytics
    """
    # Validate inputs
    valid_periods = ["day", "week", "month", "year"]
    valid_groupings = ["hour", "day"]
    
    if period not in valid_periods:
        period = "week"
    
    if group_by not in valid_groupings:
        group_by = "day"
    
    # Get statistics
    stats = await get_message_statistics_db(
        chat_id=chat_id,
        period=period,
        group_by=group_by
    )
    
    return {
        **stats,
        "message": f"Generated {period} statistics for chat {chat_id}"
    }