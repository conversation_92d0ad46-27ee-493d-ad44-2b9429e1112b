"""
Instagram media management tools for MCP server
"""

import logging
from typing import Any

from mcp_server.decorators.database import async_db_operation, track_sync_calls
from mcp_server.decorators.error_handler import handle_tool_errors
from mcp_server.errors import ErrorTypes, create_error_response
from mcp_server.main import mcp

logger = logging.getLogger(__name__)

# Database helper functions

@async_db_operation
@track_sync_calls
def get_media_for_posts(post_ids: list[int]):
    """Get media for multiple posts"""
    from instagram_manager.models import InstagramMedia
    
    media_by_post: dict[int, list] = {}
    media_items = InstagramMedia.objects.filter(
        post_id__in=post_ids
    ).select_related("post", "post__profile").order_by("post_id", "position")
    
    for media in media_items:
        if media.post_id not in media_by_post:
            media_by_post[media.post_id] = []
        media_by_post[media.post_id].append(media)
    
    return media_by_post

@async_db_operation
@track_sync_calls
def download_media_files(post_id: int, media_types: list[str] | None, save_to_gcs: bool):
    """Download media files for a post"""
    from instagram_manager.services import MediaService
    
    service = MediaService()
    
    # Get post with media
    from instagram_manager.models import InstagramPost
    post = InstagramPost.objects.prefetch_related("media").get(id=post_id)
    
    results = []
    for media in post.media.all():
        # Filter by media type if specified
        if media_types and media.media_type not in media_types:
            continue
        
        # Skip if already downloaded
        if media.is_downloaded and media.local_file:
            results.append({
                "media_id": media.id,
                "status": "already_downloaded",
                "local_path": media.local_file.url,
                "gcs_url": media.gcs_url if hasattr(media, "gcs_url") else None
            })
            continue
        
        try:
            # Download the media using MediaService
            if save_to_gcs:
                # For GCS, we need to use the MediaHandler directly
                from instagram_manager.instagram_api.data_handlers.media_handler import MediaHandler
                handler = MediaHandler(save_to_gcs=True)
                success = handler.download_media(media)
                
                if success:
                    results.append({
                        "media_id": media.id,
                        "status": "downloaded",
                        "local_path": media.local_file.url if media.local_file else None,
                        "gcs_url": media.gcs_url if hasattr(media, "gcs_url") else None
                    })
                else:
                    raise Exception("Failed to download media")
            else:
                # For local download, use the service
                stats = service.download_media_for_post(post)
                if stats["downloaded"] > 0:
                    media.refresh_from_db()
                    results.append({
                        "media_id": media.id,
                        "status": "downloaded",
                        "local_path": media.local_file.url if media.local_file else None,
                        "gcs_url": None
                    })
                else:
                    raise Exception("Failed to download media")
        except Exception as e:
            logger.exception(f"Failed to download media {media.id}: {e}")
            results.append({
                "media_id": media.id,
                "status": "failed",
                "error": str(e)
            })
    
    return results

@async_db_operation
@track_sync_calls
def get_media_statistics(post_ids: list[int]):
    """Get media statistics for posts"""
    from django.db.models import Count, Q

    from instagram_manager.models import InstagramPost
    
    # Get posts with media counts
    posts = InstagramPost.objects.filter(
        id__in=post_ids
    ).annotate(
        total_media=Count("media"),
        photo_count=Count("media", filter=Q(media__media_type="photo")),
        video_count=Count("media", filter=Q(media__media_type="video")),
        downloaded_count=Count("media", filter=Q(media__is_downloaded=True))
    ).select_related("profile")
    
    stats = []
    for post in posts:
        stats.append({
            "post_id": post.id,
            "profile_username": post.profile.username,
            "total_media": post.total_media,
            "photo_count": post.photo_count,
            "video_count": post.video_count,
            "downloaded_count": post.downloaded_count,
            "download_percentage": round(
                (post.downloaded_count / post.total_media * 100) if post.total_media > 0 else 0,
                2
            )
        })
    
    return stats

# MCP Tool implementations

@mcp.tool()
@handle_tool_errors
async def instagram_download_media(
    post_id: int,
    media_types: list[str] | None = None,
    save_to_gcs: bool = False
) -> dict[str, Any]:
    """
    Download media files for a post
    
    Args:
        post_id: Instagram post ID
        media_types: Types to download (photo, video)
        save_to_gcs: Save to Google Cloud Storage
        
    Returns:
        Download status and file paths
    """
    # Validate media types
    if media_types:
        valid_types = ["photo", "video"]
        for media_type in media_types:
            if media_type not in valid_types:
                return create_error_response(
                    error_type=ErrorTypes.VALIDATION_ERROR,
                    message=f"Invalid media type: {media_type}. Must be one of: {valid_types}"
                )
    
    logger.info(f"Downloading media for post {post_id}")
    
    try:
        results = await download_media_files(post_id, media_types, save_to_gcs)
    except Exception as e:
        if "does not exist" in str(e):
            return create_error_response(
                error_type=ErrorTypes.NOT_FOUND,
                message=f"Post {post_id} not found"
            )
        raise
    
    # Count results
    downloaded = sum(1 for r in results if r["status"] == "downloaded")
    already_downloaded = sum(1 for r in results if r["status"] == "already_downloaded")
    failed = sum(1 for r in results if r["status"] == "failed")
    
    return {
        "post_id": post_id,
        "summary": {
            "total": len(results),
            "downloaded": downloaded,
            "already_downloaded": already_downloaded,
            "failed": failed
        },
        "save_to_gcs": save_to_gcs,
        "media": results
    }

@mcp.tool()
@handle_tool_errors
async def instagram_get_media_analytics(
    post_ids: list[int]
) -> dict[str, Any]:
    """
    Get analytics for post media
    
    Args:
        post_ids: List of post IDs (max 50)
        
    Returns:
        Media analytics data
    """
    # Validate input
    if not post_ids:
        return create_error_response(
            error_type=ErrorTypes.VALIDATION_ERROR,
            message="At least one post ID is required"
        )
    
    if len(post_ids) > 50:
        return create_error_response(
            error_type=ErrorTypes.VALIDATION_ERROR,
            message="Maximum 50 post IDs allowed"
        )
    
    logger.info(f"Getting media analytics for {len(post_ids)} posts")
    
    # Get statistics
    stats = await get_media_statistics(post_ids)
    
    # Calculate totals
    total_media = sum(s["total_media"] for s in stats)
    total_photos = sum(s["photo_count"] for s in stats)
    total_videos = sum(s["video_count"] for s in stats)
    total_downloaded = sum(s["downloaded_count"] for s in stats)
    
    return {
        "post_count": len(stats),
        "totals": {
            "media": total_media,
            "photos": total_photos,
            "videos": total_videos,
            "downloaded": total_downloaded,
            "download_percentage": round(
                (total_downloaded / total_media * 100) if total_media > 0 else 0,
                2
            )
        },
        "posts": stats
    }

@mcp.tool()
@handle_tool_errors
async def instagram_get_post_media(
    post_id: int
) -> dict[str, Any]:
    """
    Get all media items for a specific post
    
    Args:
        post_id: Instagram post ID
        
    Returns:
        List of media items with details
    """
    logger.info(f"Getting media for post {post_id}")
    
    # Get media for single post
    media_by_post = await get_media_for_posts([post_id])
    
    if post_id not in media_by_post:
        return create_error_response(
            error_type=ErrorTypes.NOT_FOUND,
            message=f"No media found for post {post_id}"
        )
    
    media_items = media_by_post[post_id]
    
    return {
        "post_id": post_id,
        "media_count": len(media_items),
        "media": [
            {
                "id": media.id,
                "media_type": media.media_type,
                "media_url": media.media_url,
                "thumbnail_url": media.thumbnail_url,
                "width": media.width,
                "height": media.height,
                "duration": media.duration,
                "position": media.position,
                "is_downloaded": media.is_downloaded,
                "local_path": media.local_file.url if media.local_file else None,
                "gcs_url": media.gcs_url if hasattr(media, "gcs_url") else None,
                "file_size": media.file_size if hasattr(media, "file_size") else None,
                "created_at": media.created_at.isoformat()
            }
            for media in media_items
        ]
    }

@mcp.tool()
@handle_tool_errors
async def instagram_bulk_download_media(
    post_ids: list[int],
    media_types: list[str] | None = None,
    save_to_gcs: bool = False,
    skip_downloaded: bool = True
) -> dict[str, Any]:
    """
    Download media for multiple posts
    
    Args:
        post_ids: List of post IDs (max 20)
        media_types: Types to download (photo, video)
        save_to_gcs: Save to Google Cloud Storage
        skip_downloaded: Skip already downloaded media
        
    Returns:
        Bulk download results
    """
    # Validate input
    if not post_ids:
        return create_error_response(
            error_type=ErrorTypes.VALIDATION_ERROR,
            message="At least one post ID is required"
        )
    
    if len(post_ids) > 20:
        return create_error_response(
            error_type=ErrorTypes.VALIDATION_ERROR,
            message="Maximum 20 post IDs allowed for bulk download"
        )
    
    # Validate media types
    if media_types:
        valid_types = ["photo", "video"]
        for media_type in media_types:
            if media_type not in valid_types:
                return create_error_response(
                    error_type=ErrorTypes.VALIDATION_ERROR,
                    message=f"Invalid media type: {media_type}"
                )
    
    logger.info(f"Bulk downloading media for {len(post_ids)} posts")
    
    # Process each post
    all_results: dict[str, dict[str, Any]] = {}
    total_downloaded = 0
    total_failed = 0
    total_skipped = 0
    
    for post_id in post_ids:
        try:
            results = await download_media_files(post_id, media_types, save_to_gcs)
            
            # Count results
            downloaded = sum(1 for r in results if r["status"] == "downloaded")
            failed = sum(1 for r in results if r["status"] == "failed")
            skipped = sum(1 for r in results if r["status"] == "already_downloaded" and skip_downloaded)
            
            total_downloaded += downloaded
            total_failed += failed
            total_skipped += skipped
            
            all_results[str(post_id)] = {
                "success": True,
                "media_count": len(results),
                "downloaded": downloaded,
                "failed": failed,
                "skipped": skipped
            }
        except Exception as e:
            logger.exception(f"Failed to process post {post_id}: {e}")
            all_results[str(post_id)] = {
                "success": False,
                "error": str(e)
            }
            total_failed += 1
    
    return {
        "summary": {
            "posts_processed": len(post_ids),
            "total_downloaded": total_downloaded,
            "total_failed": total_failed,
            "total_skipped": total_skipped
        },
        "save_to_gcs": save_to_gcs,
        "skip_downloaded": skip_downloaded,
        "results": all_results
    }