"""
Instagram roast tools for MCP server
"""

import logging
import uuid
from typing import Any

from mcp_server.decorators.async_sync import async_db_operation
from mcp_server.decorators.error_handler import handle_tool_errors
from mcp_server.main import mcp
from mcp_server.validators.roast import RoastProfileTaskParams

from core.models import TaskResult

logger = logging.getLogger(__name__)


@async_db_operation
def create_roast_task(task_class, task_id: str, parameters: dict):
    """Submit roast task for execution"""
    task_instance = task_class(task_id=task_id)
    result = task_instance.run_async(**parameters)
    return result


@async_db_operation
def get_task_result_by_id(task_id: str):
    """Get task result by ID"""
    return TaskResult.objects.get(task_id=task_id)


@mcp.tool()
@handle_tool_errors
async def roast_instagram_profile(
    profile_url: str,
    post_count: int = 10,
    priority: str = "normal"
) -> dict[str, Any]:
    """
    Create a task to analyze Instagram profile and upload photos to GCS
    
    Args:
        profile_url: Instagram profile URL (e.g., https://www.instagram.com/username/)
        post_count: Number of posts to analyze (1-100, default: 10)
        priority: Task priority (low, normal, high)
        
    Returns:
        Dictionary with task_id and task information
        
    Raises:
        ValueError: If parameters are invalid
    """
    try:
        # Validate parameters
        params = RoastProfileTaskParams(
            profile_url=profile_url,
            post_count=post_count
        )
        
        # Extract username from URL
        username = params.extract_username()
        
        # Get task class from registry
        from mcp_server.main import TASK_REGISTRY
        task_class = TASK_REGISTRY.get("instagram.roast_profile")
        if not task_class:
            raise ValueError("RoastProfileTask not found in task registry")
        
        # Generate task ID
        task_id = str(uuid.uuid4())
        
        # Run task asynchronously using wrapper
        await create_roast_task(
            task_class=task_class,
            task_id=task_id,
            parameters={
                "username": username,
                "post_count": post_count,
                "profile_url": profile_url,
                "_priority": priority
            }
        )
        
        logger.info(
            f"Created roast task for {username}: task_id={task_id}",
            extra={
                "task_id": task_id,
                "username": username,
                "post_count": post_count
            }
        )
        
        return {
            "task_id": task_id,
            "status": "created",
            "message": "Task created successfully",
            "profile_url": profile_url,
            "username": username,
            "post_count": post_count
        }
        
    except Exception as e:
        logger.error(f"Failed to create roast task: {e!s}")
        raise ValueError(f"Invalid parameters: {e!s}")


@mcp.tool()
@handle_tool_errors
async def roast_get_result(
    task_id: str
) -> dict[str, Any]:
    """
    Get the result of a roast analysis task
    
    Args:
        task_id: Task ID returned by roast_instagram_profile
        
    Returns:
        Dictionary with task status and result (if completed)
        
    Raises:
        ValueError: If task not found
    """
    try:
        # Get task result using async wrapper
        task_result = await get_task_result_by_id(task_id)
        
        # Check status
        if task_result.status == "completed":
            # Task completed successfully
            return {
                "task_id": task_id,
                "status": "completed",
                "result": task_result.result
            }
        elif task_result.status == "running":
            # Task still running
            progress_pct = 0
            if task_result.total_items > 0:
                progress_pct = int((task_result.processed_items / task_result.total_items) * 100)
                
            return {
                "task_id": task_id,
                "status": "running",
                "progress": {
                    "processed": task_result.processed_items,
                    "total": task_result.total_items,
                    "percentage": progress_pct,
                    "message": task_result.progress_message or "Processing..."
                }
            }
        elif task_result.status == "failed":
            # Task failed
            return {
                "task_id": task_id,
                "status": "failed",
                "error": task_result.error_message or "Task failed"
            }
        else:
            # Other statuses (pending, cancelled, retry)
            return {
                "task_id": task_id,
                "status": task_result.status,
                "message": f"Task is {task_result.status}"
            }
            
    except TaskResult.DoesNotExist:
        logger.error(f"Task not found: {task_id}")
        raise ValueError(f"Task not found: {task_id}")