"""
Instagram profile export tools
"""

import csv
import io
import json
from typing import Any

from mcp_server.decorators.database import async_db_operation, track_sync_calls
from mcp_server.decorators.error_handler import handle_tool_errors
from mcp_server.main import mcp


@async_db_operation
@track_sync_calls
def export_profiles_data(profile_ids: list[int], format: str) -> str:
    """Export profiles data in specified format"""
    from instagram_manager.models import InstagramProfile
    
    profiles = InstagramProfile.objects.filter(
        id__in=profile_ids
    ).order_by("username")
    
    if format == "csv":
        output = io.StringIO()
        writer = csv.DictWriter(output, fieldnames=[
            "username", "full_name", "follower_count", 
            "following_count", "post_count", "is_verified",
            "is_private", "is_business", "bio", "external_url"
        ])
        writer.writeheader()
        
        for profile in profiles:
            writer.writerow({
                "username": profile.username,
                "full_name": profile.full_name,
                "follower_count": profile.follower_count,
                "following_count": profile.following_count,
                "post_count": profile.post_count,
                "is_verified": profile.is_verified,
                "is_private": profile.is_private,
                "is_business": profile.is_business,
                "bio": profile.bio,
                "external_url": profile.external_url
            })
        
        return output.getvalue()
    
    elif format == "json":
        data = []
        for profile in profiles:
            data.append({
                "username": profile.username,
                "full_name": profile.full_name,
                "follower_count": profile.follower_count,
                "following_count": profile.following_count,
                "post_count": profile.post_count,
                "is_verified": profile.is_verified,
                "is_private": profile.is_private,
                "is_business": profile.is_business,
                "bio": profile.bio,
                "external_url": profile.external_url,
                "created_at": profile.created_at.isoformat(),
                "updated_at": profile.updated_at.isoformat()
            })
        
        return json.dumps(data, indent=2)
    
    else:
        raise ValueError(f"Unsupported format: {format}")

@mcp.tool()
@handle_tool_errors
async def instagram_export_profiles(
    profile_ids: list[int],
    format: str = "json"
) -> dict[str, Any]:
    """
    Export Instagram profiles data
    
    Args:
        profile_ids: List of profile IDs to export
        format: Export format (json, csv)
        
    Returns:
        Exported data as string
    """
    if format not in ["json", "csv"]:
        return {
            "error_type": "validation_error",
            "message": "Format must be 'json' or 'csv'"
        }
    
    data = await export_profiles_data(profile_ids, format)
    
    return {
        "format": format,
        "count": len(profile_ids),
        "data": data
    }