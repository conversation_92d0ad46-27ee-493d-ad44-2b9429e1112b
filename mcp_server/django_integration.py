"""
Django integration utilities for MCP server
Ensures proper async/sync boundary handling
"""

import logging
import os

import django
from django.conf import settings

logger = logging.getLogger(__name__)

def setup_django():
    """
    Setup Django for MCP server
    Must be called before any Django imports
    """
    # Check if Django is already configured
    if settings.configured:
        logger.debug("Django already configured")
        return
    
    # Set default settings module
    os.environ.setdefault(
        "DJANGO_SETTINGS_MODULE", 
        "SocialManager.settings.production"
    )
    
    # Setup Django
    try:
        django.setup()
        logger.info("✅ Django setup successful")
        
        # Log database configuration (without sensitive data)
        db_config = settings.DATABASES.get("default", {})
        logger.info(f"📊 Database engine: {db_config.get('ENGINE', 'Unknown')}")
        logger.info(f"📊 Database name: {db_config.get('NAME', 'Unknown')}")
        
    except Exception as e:
        logger.error(f"❌ Django setup failed: {e}")
        raise

def configure_database_pool():
    """
    Configure database connection pooling for async operations
    """
    if not hasattr(settings, "DATABASES"):
        logger.warning("No database configuration found")
        return
    
    # Add connection pool settings
    pool_config = {
        "connect_timeout": 10,
        "keepalives": 1,
        "keepalives_idle": 30,
        "keepalives_interval": 10,
        "keepalives_count": 5,
    }
    
    # Apply to default database
    if "default" in settings.DATABASES:
        if "OPTIONS" not in settings.DATABASES["default"]:
            settings.DATABASES["default"]["OPTIONS"] = {}
        
        settings.DATABASES["default"]["OPTIONS"].update(pool_config)
        logger.info("✅ Database connection pooling configured")
    
    # Log pool configuration
    logger.debug(f"Pool config: {pool_config}")

def check_django_apps():
    """
    Verify required Django apps are installed
    """
    required_apps = [
        "core",
        "instagram_manager",
        "telegram_manager",
    ]
    
    missing_apps = []
    for app in required_apps:
        if app not in settings.INSTALLED_APPS:
            missing_apps.append(app)
    
    if missing_apps:
        logger.error(f"❌ Missing required Django apps: {missing_apps}")
        raise RuntimeError(f"Missing required apps: {missing_apps}")
    
    logger.info("✅ All required Django apps found")

def check_celery_configuration():
    """
    Verify Celery is properly configured
    """
    celery_settings = [
        ("CELERY_BROKER_URL", "Celery broker"),
        ("CELERY_RESULT_BACKEND", "Celery result backend"),
    ]
    
    missing_settings = []
    for setting, name in celery_settings:
        if not hasattr(settings, setting) or not getattr(settings, setting):
            missing_settings.append(name)
    
    if missing_settings:
        logger.warning(f"⚠️  Missing Celery settings: {missing_settings}")
        logger.warning("Task execution may not work properly")
    else:
        logger.info("✅ Celery configuration found")