[pytest]
DJANGO_SETTINGS_MODULE = tests.settings
# Match all Django test discovery patterns
python_files = test_*.py *_test.py *_tests.py tests.py
# Include all directories with tests, plus . to catch any new apps
testpaths = 
    tests
    core
    telegram_manager
    instagram_manager
    monitoring
    .
timeout = 600
timeout_method = thread

# Ultra-clean output configuration
addopts = 
    --tb=short
    --disable-warnings
    -p no:warnings
    --no-header
    -q
    --color=yes

# Logging configuration
log_cli = false
log_cli_level = CRITICAL
log_file_level = CRITICAL
log_capture = false

# Filter all warnings
filterwarnings =
    ignore

# Markers
markers =
    django_db: mark a test as needing the database
    asyncio: mark a test as using asyncio
    slow: mark a test as slow running
    integration: mark a test as integration test
    unit: mark a test as unit test