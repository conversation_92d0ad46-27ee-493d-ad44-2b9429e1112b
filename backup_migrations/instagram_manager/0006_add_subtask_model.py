# Generated by Django 5.2.3 on 2025-06-27 23:49

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("instagram_manager", "0005_instagramscrapingtask_parent_task"),
    ]

    operations = [
        migrations.AddField(
            model_name="instagramscrapingtask",
            name="subtasks_completed",
            field=models.IntegerField(
                default=0, help_text="Number of completed subtasks"
            ),
        ),
        migrations.AddField(
            model_name="instagramscrapingtask",
            name="subtasks_count",
            field=models.IntegerField(default=0, help_text="Total number of subtasks"),
        ),
        migrations.AddField(
            model_name="instagramscrapingtask",
            name="subtasks_failed",
            field=models.IntegerField(default=0, help_text="Number of failed subtasks"),
        ),
        migrations.AlterField(
            model_name="instagramscrapingtask",
            name="parent_task",
            field=models.ForeignKey(
                blank=True,
                help_text="Parent task for hierarchical task structure (deprecated, use InstagramSubTask instead)",
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="subtasks",
                to="instagram_manager.instagramscrapingtask",
            ),
        ),
        migrations.AlterField(
            model_name="instagramscrapingtask",
            name="task_type",
            field=models.CharField(
                choices=[
                    ("batch_import", "Batch Import"),
                    ("profile", "Profile"),
                    ("posts", "Posts"),
                    ("followers", "Followers"),
                    ("comments", "Comments"),
                    ("hashtag", "Hashtag"),
                ],
                max_length=20,
            ),
        ),
        migrations.CreateModel(
            name="InstagramSubTask",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "subtask_type",
                    models.CharField(
                        choices=[
                            ("import_profile", "Import Profile Data"),
                            ("import_posts", "Import Posts"),
                            ("import_comments", "Import Comments"),
                            ("import_followers", "Import Followers"),
                            ("download_media", "Download Media Files"),
                        ],
                        help_text="Type of import operation",
                        max_length=50,
                    ),
                ),
                (
                    "target_identifier",
                    models.CharField(
                        help_text="Username, post_id, or other identifier for this subtask",
                        max_length=255,
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("pending", "Pending"),
                            ("running", "Running"),
                            ("completed", "Completed"),
                            ("failed", "Failed"),
                            ("cancelled", "Cancelled"),
                        ],
                        default="pending",
                        max_length=20,
                    ),
                ),
                ("progress", models.IntegerField(default=0)),
                ("total_items", models.IntegerField(default=0)),
                ("processed_items", models.IntegerField(default=0)),
                ("failed_items", models.IntegerField(default=0)),
                (
                    "result_data",
                    models.JSONField(
                        blank=True,
                        default=dict,
                        help_text="Detailed results of subtask execution",
                    ),
                ),
                ("error_message", models.TextField(blank=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("started_at", models.DateTimeField(blank=True, null=True)),
                ("completed_at", models.DateTimeField(blank=True, null=True)),
                (
                    "execution_params",
                    models.JSONField(
                        default=dict,
                        help_text="Parameters for subtask execution (start_date, limit, etc.)",
                    ),
                ),
                (
                    "parent_task",
                    models.ForeignKey(
                        help_text="Parent task that owns this subtask",
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="subtasks_new",
                        to="instagram_manager.instagramscrapingtask",
                    ),
                ),
            ],
            options={
                "db_table": "instagram_subtasks",
                "ordering": ["created_at"],
                "indexes": [
                    models.Index(
                        fields=["parent_task", "status"],
                        name="instagram_s_parent__b7f25e_idx",
                    ),
                    models.Index(
                        fields=["subtask_type", "created_at"],
                        name="instagram_s_subtask_e0d085_idx",
                    ),
                    models.Index(
                        fields=["parent_task", "subtask_type"],
                        name="instagram_s_parent__63d5a1_idx",
                    ),
                ],
            },
        ),
    ]
