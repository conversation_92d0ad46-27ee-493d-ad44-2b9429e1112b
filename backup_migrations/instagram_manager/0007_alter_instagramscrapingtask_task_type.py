# Generated by Django 5.2.3 on 2025-07-07 21:17

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("instagram_manager", "0006_add_subtask_model"),
    ]

    operations = [
        migrations.AlterField(
            model_name="instagramscrapingtask",
            name="task_type",
            field=models.CharField(
                choices=[
                    ("batch_import", "Batch Import"),
                    ("profile", "Profile"),
                    ("posts", "Posts"),
                    ("followers", "Followers"),
                    ("comments", "Comments"),
                    ("hashtag", "Hashtag"),
                ],
                max_length=255,
            ),
        ),
    ]
