# Generated by Django 5.2.1 on 2025-06-27 21:04

from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("instagram_manager", "0002_add_celery_task_id"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AddField(
            model_name="instagramscrapingtask",
            name="celery_beat_name",
            field=models.CharField(
                blank=True,
                help_text="Unique name for Celery Beat scheduler",
                max_length=255,
                null=True,
                unique=True,
            ),
        ),
        migrations.AddField(
            model_name="instagramscrapingtask",
            name="initial_start_date",
            field=models.DateField(
                blank=True,
                help_text="Initial start date for periodic imports",
                null=True,
            ),
        ),
        migrations.AddField(
            model_name="instagramscrapingtask",
            name="interval_hours",
            field=models.IntegerField(
                blank=True,
                help_text="Interval between runs in hours (for periodic tasks)",
                null=True,
            ),
        ),
        migrations.AddField(
            model_name="instagramscrapingtask",
            name="is_periodic",
            field=models.<PERSON><PERSON>an<PERSON>ield(
                default=False, help_text="Whether this is a periodic task"
            ),
        ),
        migrations.AddField(
            model_name="instagramscrapingtask",
            name="last_periodic_run",
            field=models.DateTimeField(
                blank=True, help_text="Last time periodic task was executed", null=True
            ),
        ),
        migrations.AddField(
            model_name="instagramscrapingtask",
            name="next_periodic_run",
            field=models.DateTimeField(
                blank=True, help_text="Next scheduled run time", null=True
            ),
        ),
        migrations.AddField(
            model_name="instagramscrapingtask",
            name="periodic_fail_count",
            field=models.IntegerField(
                default=0, help_text="Number of failed periodic runs"
            ),
        ),
        migrations.AddField(
            model_name="instagramscrapingtask",
            name="periodic_parameters",
            field=models.JSONField(
                blank=True,
                help_text="Additional parameters for periodic execution",
                null=True,
            ),
        ),
        migrations.AddField(
            model_name="instagramscrapingtask",
            name="periodic_runs_count",
            field=models.IntegerField(
                default=0, help_text="Total number of periodic runs"
            ),
        ),
        migrations.AddField(
            model_name="instagramscrapingtask",
            name="periodic_success_count",
            field=models.IntegerField(
                default=0, help_text="Number of successful periodic runs"
            ),
        ),
        migrations.AddField(
            model_name="instagramscrapingtask",
            name="total_periodic_items",
            field=models.IntegerField(
                default=0, help_text="Total items imported across all periodic runs"
            ),
        ),
        migrations.AddIndex(
            model_name="instagramscrapingtask",
            index=models.Index(
                fields=["is_periodic", "next_periodic_run"],
                name="instagram_s_is_peri_14fe20_idx",
            ),
        ),
    ]
