# Generated by Django 5.2.3 on 2025-07-07 21:56

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("instagram_manager", "0007_alter_instagramscrapingtask_task_type"),
    ]

    operations = [
        migrations.Alter<PERSON>ield(
            model_name="instagramscrapingtask",
            name="task_type",
            field=models.CharField(
                choices=[
                    ("batch_import", "Batch Import"),
                    ("batch_import_with_comments", "Batch Import with Comments"),
                    ("profile", "Profile"),
                    ("posts", "Posts"),
                    ("followers", "Followers"),
                    ("comments", "Comments"),
                    ("hashtag", "Hashtag"),
                ],
                max_length=255,
            ),
        ),
    ]
