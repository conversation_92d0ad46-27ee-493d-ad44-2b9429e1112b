# Generated by Django 5.2.1 on 2025-06-25 18:48

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="InstagramAccount",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True, db_index=True)),
                ("updated_at", models.DateTimeField(auto_now=True, db_index=True)),
                (
                    "username",
                    models.CharField(db_index=True, max_length=30, unique=True),
                ),
                (
                    "_encrypted_password",
                    models.TextField(blank=True, db_column="password", default=""),
                ),
                ("is_active", models.BooleanField(default=True)),
                ("is_logged_in", models.<PERSON>oleanField(default=False)),
                ("session_data", models.JSONField(blank=True, null=True)),
                ("cookies", models.J<PERSON>NField(blank=True, null=True)),
                ("daily_comment_count", models.IntegerField(default=0)),
                ("last_comment_at", models.DateTimeField(blank=True, null=True)),
                ("last_used_at", models.DateTimeField(blank=True, null=True)),
            ],
            options={
                "db_table": "instagram_accounts",
                "indexes": [
                    models.Index(
                        fields=["username"], name="instagram_a_usernam_536a8c_idx"
                    ),
                    models.Index(
                        fields=["is_active", "is_logged_in"],
                        name="instagram_a_is_acti_7f99f9_idx",
                    ),
                ],
            },
        ),
        migrations.CreateModel(
            name="InstagramPost",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True, db_index=True)),
                ("updated_at", models.DateTimeField(auto_now=True, db_index=True)),
                ("is_deleted", models.BooleanField(db_index=True, default=False)),
                (
                    "deleted_at",
                    models.DateTimeField(blank=True, db_index=True, null=True),
                ),
                (
                    "external_id",
                    models.CharField(
                        blank=True, db_index=True, default="", max_length=255
                    ),
                ),
                ("like_count", models.IntegerField(default=0)),
                ("comment_count", models.IntegerField(default=0)),
                ("share_count", models.IntegerField(default=0)),
                ("view_count", models.IntegerField(blank=True, null=True)),
                ("posted_at", models.DateTimeField(db_index=True)),
                ("edited_at", models.DateTimeField(blank=True, null=True)),
                ("post_url", models.URLField(blank=True, max_length=500)),
                ("raw_data", models.JSONField(blank=True, default=dict)),
                (
                    "post_id",
                    models.CharField(db_index=True, default="", max_length=255),
                ),
                (
                    "shortcode",
                    models.CharField(db_index=True, max_length=50, unique=True),
                ),
                ("caption", models.TextField(blank=True)),
                ("video_play_count", models.IntegerField(blank=True, null=True)),
                ("location", models.CharField(blank=True, max_length=255, null=True)),
                (
                    "location_id",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                ("is_sponsored", models.BooleanField(default=False)),
                ("is_comments_disabled", models.BooleanField(default=False)),
                ("accessibility_caption", models.TextField(blank=True)),
                (
                    "post_type",
                    models.CharField(
                        choices=[
                            ("photo", "Photo"),
                            ("video", "Video"),
                            ("carousel", "Carousel"),
                            ("reel", "Reel"),
                        ],
                        max_length=20,
                    ),
                ),
                (
                    "deleted_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_deleted_items",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "db_table": "instagram_posts",
                "ordering": ["-posted_at"],
            },
        ),
        migrations.CreateModel(
            name="InstagramHashtag",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True, db_index=True)),
                ("updated_at", models.DateTimeField(auto_now=True, db_index=True)),
                ("name", models.CharField(db_index=True, max_length=255, unique=True)),
                (
                    "normalized_name",
                    models.CharField(db_index=True, default="", max_length=255),
                ),
                ("post_count", models.IntegerField(default=0)),
                ("last_scraped_at", models.DateTimeField(blank=True, null=True)),
                (
                    "posts",
                    models.ManyToManyField(
                        related_name="hashtags", to="instagram_manager.instagrampost"
                    ),
                ),
            ],
            options={
                "db_table": "instagram_hashtags",
            },
        ),
        migrations.CreateModel(
            name="InstagramProfile",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True, db_index=True)),
                ("updated_at", models.DateTimeField(auto_now=True, db_index=True)),
                ("is_deleted", models.BooleanField(db_index=True, default=False)),
                (
                    "deleted_at",
                    models.DateTimeField(blank=True, db_index=True, null=True),
                ),
                (
                    "external_id",
                    models.CharField(db_index=True, default="", max_length=255),
                ),
                ("profile_pic_url", models.URLField(blank=True, max_length=500)),
                ("is_verified", models.BooleanField(default=False)),
                ("is_private", models.BooleanField(default=False)),
                ("is_active", models.BooleanField(default=True)),
                ("follower_count", models.IntegerField(default=0)),
                ("following_count", models.IntegerField(default=0)),
                ("post_count", models.IntegerField(default=0)),
                ("raw_data", models.JSONField(blank=True, default=dict)),
                (
                    "profile_id",
                    models.CharField(
                        db_index=True,
                        default="",
                        help_text="Instagram-specific profile ID",
                        max_length=255,
                        unique=True,
                    ),
                ),
                ("is_business", models.BooleanField(default=False)),
                ("profile_pic_hd_url", models.URLField(blank=True, max_length=500)),
                (
                    "external_url",
                    models.URLField(blank=True, max_length=500, null=True),
                ),
                ("category_name", models.CharField(blank=True, max_length=255)),
                ("last_scraped_at", models.DateTimeField(blank=True, null=True)),
                (
                    "username",
                    models.CharField(db_index=True, max_length=30, unique=True),
                ),
                ("full_name", models.CharField(blank=True, max_length=150)),
                ("bio", models.TextField(blank=True, max_length=150)),
                (
                    "deleted_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_deleted_items",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "db_table": "instagram_profiles",
            },
        ),
        migrations.AddField(
            model_name="instagrampost",
            name="profile",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="posts",
                to="instagram_manager.instagramprofile",
            ),
        ),
        migrations.CreateModel(
            name="InstagramFollower",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("follower_username", models.CharField(db_index=True, max_length=150)),
                ("follower_id", models.CharField(max_length=255)),
                (
                    "follower_full_name",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                ("is_verified", models.BooleanField(default=False)),
                ("profile_pic_url", models.URLField(blank=True, max_length=500)),
                ("followed_at", models.DateTimeField(blank=True, null=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "profile",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="followers",
                        to="instagram_manager.instagramprofile",
                    ),
                ),
            ],
            options={
                "db_table": "instagram_followers",
            },
        ),
        migrations.CreateModel(
            name="InstagramScrapingTask",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True, db_index=True)),
                ("updated_at", models.DateTimeField(auto_now=True, db_index=True)),
                (
                    "task_id",
                    models.CharField(
                        db_index=True, default="", max_length=255, unique=True
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("pending", "Pending"),
                            ("running", "Running"),
                            ("completed", "Completed"),
                            ("failed", "Failed"),
                            ("cancelled", "Cancelled"),
                        ],
                        default="pending",
                        max_length=20,
                    ),
                ),
                ("parameters", models.JSONField(default=dict)),
                ("total_items", models.IntegerField(default=0)),
                ("processed_items", models.IntegerField(default=0)),
                ("failed_items", models.IntegerField(default=0)),
                ("started_at", models.DateTimeField(blank=True, null=True)),
                ("completed_at", models.DateTimeField(blank=True, null=True)),
                ("result", models.JSONField(blank=True, default=dict)),
                ("error_message", models.TextField(blank=True)),
                (
                    "task_type",
                    models.CharField(
                        choices=[
                            ("profile", "Profile"),
                            ("posts", "Posts"),
                            ("followers", "Followers"),
                            ("comments", "Comments"),
                            ("hashtag", "Hashtag"),
                        ],
                        max_length=20,
                    ),
                ),
                ("target_identifier", models.CharField(max_length=255)),
                (
                    "brightdata_snapshot_id",
                    models.CharField(blank=True, max_length=255),
                ),
                ("brightdata_dataset_id", models.CharField(max_length=255)),
                ("items_scraped", models.IntegerField(default=0)),
                (
                    "total_items_received",
                    models.IntegerField(
                        default=0, help_text="Total items received from BrightData"
                    ),
                ),
                (
                    "items_filtered",
                    models.IntegerField(
                        default=0, help_text="Items filtered out during import"
                    ),
                ),
                (
                    "additional_users",
                    models.JSONField(
                        blank=True,
                        help_text="List of additional users found in response but not imported",
                        null=True,
                    ),
                ),
                (
                    "skip_media_download",
                    models.BooleanField(
                        default=False,
                        help_text="Skip downloading media files during import",
                    ),
                ),
                (
                    "batch_identifiers",
                    models.JSONField(
                        blank=True,
                        help_text="List of identifiers for batch operations (e.g., multiple usernames)",
                        null=True,
                    ),
                ),
                (
                    "batch_results",
                    models.JSONField(
                        blank=True,
                        help_text="Results for each identifier in batch operation",
                        null=True,
                    ),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_tasks",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "db_table": "instagram_scraping_tasks",
            },
        ),
        migrations.CreateModel(
            name="MajilaServiceAccount",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True, db_index=True)),
                ("updated_at", models.DateTimeField(auto_now=True, db_index=True)),
                ("username", models.CharField(max_length=150)),
                (
                    "_encrypted_password",
                    models.TextField(blank=True, db_column="password", default=""),
                ),
                ("access_token", models.TextField(blank=True)),
                ("user_uuid", models.UUIDField(blank=True, null=True)),
                ("is_active", models.BooleanField(default=True)),
                ("last_auth_at", models.DateTimeField(blank=True, null=True)),
                ("auth_error", models.TextField(blank=True)),
                (
                    "instagram_profile",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="majila_accounts",
                        to="instagram_manager.instagramprofile",
                    ),
                ),
            ],
            options={
                "db_table": "instagram_majila_service_accounts",
            },
        ),
        migrations.CreateModel(
            name="MajilaExportTask",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True, db_index=True)),
                ("updated_at", models.DateTimeField(auto_now=True, db_index=True)),
                (
                    "task_id",
                    models.CharField(
                        db_index=True, default="", max_length=255, unique=True
                    ),
                ),
                ("parameters", models.JSONField(default=dict)),
                ("total_items", models.IntegerField(default=0)),
                ("processed_items", models.IntegerField(default=0)),
                ("failed_items", models.IntegerField(default=0)),
                ("started_at", models.DateTimeField(blank=True, null=True)),
                ("completed_at", models.DateTimeField(blank=True, null=True)),
                ("result", models.JSONField(blank=True, default=dict)),
                ("error_message", models.TextField(blank=True)),
                ("task_type", models.CharField(default="majila_export", max_length=50)),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("pending", "Pending"),
                            ("in_progress", "In Progress"),
                            ("completed", "Completed"),
                            ("failed", "Failed"),
                            ("partial", "Partially Completed"),
                        ],
                        default="pending",
                        max_length=20,
                    ),
                ),
                ("total_posts", models.IntegerField(default=0)),
                ("exported_count", models.IntegerField(default=0)),
                ("failed_count", models.IntegerField(default=0)),
                (
                    "export_results",
                    models.JSONField(
                        blank=True,
                        help_text="Detailed results for each post",
                        null=True,
                    ),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_tasks",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "posts",
                    models.ManyToManyField(
                        related_name="majila_export_tasks",
                        to="instagram_manager.instagrampost",
                    ),
                ),
                (
                    "account",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="export_tasks",
                        to="instagram_manager.majilaserviceaccount",
                    ),
                ),
            ],
            options={
                "db_table": "instagram_majila_export_tasks",
            },
        ),
        migrations.CreateModel(
            name="PostedComment",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True, db_index=True)),
                ("updated_at", models.DateTimeField(auto_now=True, db_index=True)),
                ("text", models.TextField()),
                ("post_url", models.URLField(max_length=500)),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("pending", "Pending"),
                            ("posted", "Posted"),
                            ("failed", "Failed"),
                        ],
                        default="pending",
                        max_length=20,
                    ),
                ),
                ("posted_at", models.DateTimeField(blank=True, null=True)),
                ("error_message", models.TextField(blank=True)),
                ("attempts", models.IntegerField(default=0)),
                (
                    "instagram_comment_id",
                    models.CharField(blank=True, db_index=True, max_length=255),
                ),
                (
                    "account",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="posted_comments",
                        to="instagram_manager.instagramaccount",
                    ),
                ),
                (
                    "post",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="posted_comments",
                        to="instagram_manager.instagrampost",
                    ),
                ),
            ],
            options={
                "db_table": "instagram_posted_comments",
            },
        ),
        migrations.CreateModel(
            name="InstagramMedia",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True, db_index=True)),
                ("updated_at", models.DateTimeField(auto_now=True, db_index=True)),
                (
                    "external_id",
                    models.CharField(blank=True, db_index=True, max_length=255),
                ),
                (
                    "media_type",
                    models.CharField(
                        choices=[
                            ("photo", "Photo"),
                            ("video", "Video"),
                            ("audio", "Audio"),
                            ("document", "Document"),
                            ("gif", "GIF"),
                            ("sticker", "Sticker"),
                            ("other", "Other"),
                        ],
                        max_length=20,
                    ),
                ),
                ("media_url", models.URLField(max_length=1000)),
                ("thumbnail_url", models.URLField(blank=True, max_length=1000)),
                (
                    "local_thumbnail",
                    models.ImageField(
                        blank=True,
                        null=True,
                        upload_to="social_media/thumbnails/%Y/%m/%d/",
                    ),
                ),
                ("width", models.IntegerField(blank=True, null=True)),
                ("height", models.IntegerField(blank=True, null=True)),
                (
                    "duration",
                    models.IntegerField(
                        blank=True, help_text="Duration in seconds", null=True
                    ),
                ),
                (
                    "file_size",
                    models.BigIntegerField(
                        blank=True, help_text="File size in bytes", null=True
                    ),
                ),
                ("mime_type", models.CharField(blank=True, max_length=100)),
                ("is_downloaded", models.BooleanField(default=False)),
                ("download_error", models.TextField(blank=True)),
                ("metadata", models.JSONField(blank=True, default=dict)),
                (
                    "media_id",
                    models.CharField(db_index=True, default="", max_length=255),
                ),
                ("audio_url", models.URLField(blank=True, max_length=1000, null=True)),
                ("order_index", models.IntegerField(default=0)),
                (
                    "local_path",
                    models.FileField(
                        blank=True, null=True, upload_to="instagram/media/"
                    ),
                ),
                (
                    "local_thumbnail_path",
                    models.FileField(
                        blank=True,
                        help_text="Local path to downloaded thumbnail",
                        null=True,
                        upload_to="instagram/media/thumbnails/",
                    ),
                ),
                (
                    "post",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="media",
                        to="instagram_manager.instagrampost",
                    ),
                ),
            ],
            options={
                "db_table": "instagram_media",
                "ordering": ["post", "order_index"],
                "indexes": [
                    models.Index(
                        fields=["post", "order_index"],
                        name="instagram_m_post_id_1791c4_idx",
                    )
                ],
            },
        ),
        migrations.CreateModel(
            name="InstagramComment",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True, db_index=True)),
                ("updated_at", models.DateTimeField(auto_now=True, db_index=True)),
                (
                    "external_id",
                    models.CharField(
                        blank=True, db_index=True, default="", max_length=255
                    ),
                ),
                ("like_count", models.IntegerField(default=0)),
                ("reply_count", models.IntegerField(default=0)),
                ("commented_at", models.DateTimeField(db_index=True)),
                ("edited_at", models.DateTimeField(blank=True, null=True)),
                ("raw_data", models.JSONField(blank=True, default=dict)),
                (
                    "comment_id",
                    models.CharField(db_index=True, default="", max_length=255),
                ),
                ("author_username", models.CharField(db_index=True, max_length=30)),
                ("author_external_id", models.CharField(blank=True, max_length=255)),
                (
                    "author_profile_pic",
                    models.URLField(blank=True, max_length=500, null=True),
                ),
                ("author_is_verified", models.BooleanField(default=False)),
                ("text", models.TextField(max_length=220)),
                (
                    "is_pinned",
                    models.BooleanField(
                        default=False, help_text="Закрепленный комментарий"
                    ),
                ),
                (
                    "is_hidden",
                    models.BooleanField(
                        default=False, help_text="Скрытый/удаленный комментарий"
                    ),
                ),
                (
                    "author",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="%(class)s_authored_comments",
                        to="instagram_manager.instagramcomment",
                    ),
                ),
                (
                    "reply_to",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="replies",
                        to="instagram_manager.instagramcomment",
                    ),
                ),
                (
                    "post",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="comments",
                        to="instagram_manager.instagrampost",
                    ),
                ),
            ],
            options={
                "db_table": "instagram_comments",
                "ordering": ["-commented_at"],
                "indexes": [
                    models.Index(
                        fields=["author_username"],
                        name="instagram_c_author__43c670_idx",
                    ),
                    models.Index(
                        fields=["is_hidden", "is_pinned"],
                        name="instagram_c_is_hidd_f23692_idx",
                    ),
                ],
            },
        ),
        migrations.AddIndex(
            model_name="instagramprofile",
            index=models.Index(
                fields=["profile_id"], name="instagram_p_profile_947a0a_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="instagramprofile",
            index=models.Index(
                fields=["last_scraped_at"], name="instagram_p_last_sc_4dceb9_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="instagrampost",
            index=models.Index(
                fields=["shortcode"], name="instagram_p_shortco_689a05_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="instagramfollower",
            index=models.Index(
                fields=["profile", "follower_username"],
                name="instagram_f_profile_05ee35_idx",
            ),
        ),
        migrations.AddIndex(
            model_name="instagramfollower",
            index=models.Index(
                fields=["follower_username"], name="instagram_f_followe_37683f_idx"
            ),
        ),
        migrations.AlterUniqueTogether(
            name="instagramfollower",
            unique_together={("profile", "follower_id")},
        ),
        migrations.AddIndex(
            model_name="instagramscrapingtask",
            index=models.Index(
                fields=["target_identifier"], name="instagram_s_target__a10b63_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="majilaserviceaccount",
            index=models.Index(
                fields=["instagram_profile", "is_active"],
                name="instagram_m_instagr_ea3d80_idx",
            ),
        ),
        migrations.AddIndex(
            model_name="majilaserviceaccount",
            index=models.Index(
                fields=["username"], name="instagram_m_usernam_12bd5e_idx"
            ),
        ),
        migrations.AlterUniqueTogether(
            name="majilaserviceaccount",
            unique_together={("instagram_profile", "username")},
        ),
        migrations.AddIndex(
            model_name="postedcomment",
            index=models.Index(
                fields=["status", "created_at"], name="instagram_p_status_c623f7_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="postedcomment",
            index=models.Index(
                fields=["account", "posted_at"], name="instagram_p_account_bd729e_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="postedcomment",
            index=models.Index(
                fields=["post", "account"], name="instagram_p_post_id_578344_idx"
            ),
        ),
    ]
