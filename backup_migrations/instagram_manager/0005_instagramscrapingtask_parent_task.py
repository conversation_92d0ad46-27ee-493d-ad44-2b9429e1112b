# Generated by Django 5.2.3 on 2025-06-27 22:37

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("instagram_manager", "0004_rename_interval_hours_to_seconds"),
    ]

    operations = [
        migrations.AddField(
            model_name="instagramscrapingtask",
            name="parent_task",
            field=models.ForeignKey(
                blank=True,
                help_text="Parent task for hierarchical task structure",
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="subtasks",
                to="instagram_manager.instagramscrapingtask",
            ),
        ),
    ]
