# Generated by Django 5.2.1 on 2025-06-27

from django.db import migrations, models


def convert_hours_to_seconds(apps, schema_editor):
    """Convert existing interval_hours values to seconds"""
    InstagramScrapingTask = apps.get_model("instagram_manager", "InstagramScrapingTask")
    for task in InstagramScrapingTask.objects.filter(is_periodic=True, interval_hours__isnull=False):
        task.interval_seconds = task.interval_hours * 3600
        task.save(update_fields=["interval_seconds"])


def convert_seconds_to_hours(apps, schema_editor):
    """Revert seconds back to hours for rollback"""
    InstagramScrapingTask = apps.get_model("instagram_manager", "InstagramScrapingTask")
    for task in InstagramScrapingTask.objects.filter(is_periodic=True, interval_seconds__isnull=False):
        task.interval_hours = task.interval_seconds // 3600
        task.save(update_fields=["interval_hours"])


class Migration(migrations.Migration):

    dependencies = [
        ("instagram_manager", "0003_add_periodic_task_fields"),
    ]

    operations = [
        # First, add the new field
        migrations.AddField(
            model_name="instagramscrapingtask",
            name="interval_seconds",
            field=models.IntegerField(
                blank=True,
                help_text="Interval between runs in seconds (for periodic tasks)",
                null=True,
            ),
        ),
        # Convert data
        migrations.RunPython(convert_hours_to_seconds, convert_seconds_to_hours),
        # Remove old field
        migrations.RemoveField(
            model_name="instagramscrapingtask",
            name="interval_hours",
        ),
    ]