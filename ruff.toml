# Ruff configuration for SocialManager project

# Base configuration
line-length = 120
target-version = "py313"

# Exclude paths
exclude = [
    ".venv",
    "__pycache__",
    "migrations",
    "manage.py",
    "activate",
    "tests/test_*.py",
    "*/test_*.py",
]

[lint]
# Enable specific rule sets
select = [
    "E",      # pycodestyle errors
    "F",      # pyflakes
    "I",      # isort
    "N",      # pep8-naming
    "UP",     # pyupgrade
    "C90",    # mccabe complexity
    "B",      # flake8-bugbear
    "Q",      # flake8-quotes
    "RUF",    # Ruff-specific rules
    "PT",     # flake8-pytest-style
    "TRY",    # tryceratops
    "PLC",    # pylint convention
    "ARG",    # flake8-unused-arguments
]

# Ignore specific rules
ignore = [
    "E501",    # line-too-long (handled by formatter)
    "B008",    # function calls in argument defaults
    "B904",    # raise from None
    "TRY003",  # Avoid generic exception messages
    "RUF012",  # Mutable class defaults
    "ARG002",  # Unused method argument
    "N805",    # First argument of a method should be named 'self'
]

# Configure specific rules
[lint.mccabe]
max-complexity = 10

[lint.isort]
known-first-party = ["core", "instagram_manager", "telegram_manager", "monitoring"]

[lint.pydocstyle]
convention = "google"

[lint.pylint]
max-args = 10
max-branches = 12
max-returns = 6
max-statements = 50

[lint.per-file-ignores]
# Ignore import violations in scripts
"scripts/*.py" = ["E402"]
# Ignore complexity in admin files
"*/admin.py" = ["C901"]
# Ignore unused arguments in tests
"tests/**/*.py" = ["ARG"]

[format]
# Use double quotes for strings
quote-style = "double"

# Indent with 4 spaces
indent-style = "space"

# Unix-style line endings
line-ending = "lf"

# Format docstrings
docstring-code-format = true