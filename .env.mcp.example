# MCP Server Configuration
MCP_HOST=0.0.0.0
MCP_PORT=8000
MCP_LOG_LEVEL=INFO
MCP_LOG_FILE=/var/log/mcp/server.log
ALLOWED_HOSTS=*

# Django Configuration (inherited from main project)
DJANGO_SETTINGS_MODULE=SocialManager.settings.production

# Debug Mode
DEBUG=False

# Database (if using separate pool)
# MCP_DATABASE_URL=postgresql://user:pass@localhost/db

# Optional: Separate Redis for MCP
# MCP_REDIS_URL=redis://localhost:6379/2