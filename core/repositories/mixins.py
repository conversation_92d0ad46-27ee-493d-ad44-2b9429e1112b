"""
Миксины для расширения функциональности репозиториев.
Предоставляют переиспользуемые методы для общих операций.
"""

import logging
from datetime import datetime, timedelta
from typing import Any, Protocol, TypeVar, cast

from django.core.exceptions import ValidationError
from django.db.models import Model, Q, QuerySet
from django.utils import timezone

T = TypeVar("T", bound=Model)


class ModelProtocol(Protocol):
    """Protocol for classes that have a model attribute."""

    model: type[Model]


logger = logging.getLogger(__name__)


class BulkOperationsMixin:
    """
    Миксин для массовых операций с моделями.
    Предоставляет методы для массового создания и обновления объектов.
    """

    def bulk_create_or_update(
        self,
        items_data: list[dict[str, Any]],
        lookup_field: str = "external_id",
        update_fields: list[str] | None = None,
    ) -> tuple[int, int]:
        """
        Массовое создание или обновление объектов.

        Args:
            items_data: Список данных для создания/обновления
            lookup_field: Поле для поиска существующих объектов
            update_fields: Список полей для обновления (если None, обновляются все)

        Returns:
            Кортеж (количество созданных, количество обновленных)

        Raises:
            DatabaseError: При ошибке базы данных
            ValidationError: При ошибке валидации данных
        """
        if not hasattr(self, "model"):
            raise AttributeError("Mixin requires 'model' attribute")

        created_count = 0
        updated_count = 0
        errors = []

        for idx, item_data in enumerate(items_data):
            try:
                # Проверяем наличие поля для поиска
                lookup_value = item_data.get(lookup_field)
                if not lookup_value:
                    errors.append(f"Item {idx}: Missing lookup field '{lookup_field}'")
                    continue

                # Пытаемся найти или создать объект
                obj, created = self.model.objects.update_or_create(
                    defaults=item_data, **{lookup_field: lookup_value}
                )

                if created:
                    created_count += 1
                else:
                    updated_count += 1

            except ValidationError as e:
                errors.append(f"Item {idx}: Validation error - {e!s}")
            except Exception as e:
                errors.append(f"Item {idx}: {e!s}")

        # Логируем результаты
        logger.info(
            f"Bulk operation completed: {created_count} created, "
            f"{updated_count} updated, {len(errors)} errors"
        )

        if errors:
            logger.warning(f"Bulk operation errors: {'; '.join(errors[:5])}")
            if len(errors) > 5:
                logger.warning(f"... and {len(errors) - 5} more errors")

        return created_count, updated_count

    def bulk_update_fields(
        self, ids: list[int | str], update_data: dict[str, Any]
    ) -> int:
        """
        Массовое обновление полей для списка объектов.

        Args:
            ids: Список ID объектов для обновления
            update_data: Данные для обновления

        Returns:
            Количество обновленных объектов
        """
        if not hasattr(self, "model"):
            raise AttributeError("Mixin requires 'model' attribute")

        # Добавляем updated_at если модель имеет это поле
        if hasattr(self.model, "updated_at"):
            update_data["updated_at"] = timezone.now()

        updated = self.model.objects.filter(pk__in=ids).update(**update_data)

        logger.info(
            f"Bulk updated {updated} objects with data: {list(update_data.keys())}"
        )
        return cast(int, updated)


class SearchMixin:
    """
    Миксин для поисковых операций.
    Предоставляет унифицированные методы поиска по различным полям.
    """

    def search(
        self, query: str, search_fields: list[str] | None = None
    ) -> QuerySet[Any, Any]:
        """
        Поиск объектов по текстовым полям.

        Args:
            query: Поисковый запрос
            search_fields: Поля для поиска (если None, используются поля по умолчанию)

        Returns:
            QuerySet с результатами поиска
        """
        if not hasattr(self, "model"):
            raise AttributeError("Mixin requires 'model' attribute")

        # Определяем поля для поиска
        if search_fields is None:
            # Используем стандартные текстовые поля
            search_fields = []
            for field in self.model._meta.fields:
                if field.get_internal_type() in ["CharField", "TextField"]:
                    search_fields.append(field.name)

        if not search_fields:
            logger.warning(f"No searchable fields found for {self.model.__name__}")
            return cast(QuerySet[Any, Any], self.model.objects.none())

        # Строим Q объект для поиска
        q_objects = Q()
        for field in search_fields:
            q_objects |= Q(**{f"{field}__icontains": query})

        queryset = self.model.objects.filter(q_objects)

        # Применяем оптимизации если они определены
        if hasattr(self, "_apply_optimizations"):
            queryset = self._apply_optimizations(queryset)

        return cast(QuerySet[Any, Any], queryset)

    def search_by_multiple_fields(self, **field_queries) -> QuerySet[Any, Any]:
        """
        Поиск по нескольким полям с разными условиями.

        Args:
            **field_queries: Пары поле=значение для поиска

        Returns:
            QuerySet с результатами поиска

        Example:
            search_by_multiple_fields(
                username__icontains="test",
                email__endswith="@example.com",
                is_active=True
            )
        """
        if not hasattr(self, "model"):
            raise AttributeError("Mixin requires 'model' attribute")

        queryset = self.model.objects.filter(**field_queries)

        if hasattr(self, "_apply_optimizations"):
            queryset = self._apply_optimizations(queryset)

        return cast(QuerySet[Any, Any], queryset)


class DateFilterMixin:
    """
    Миксин для фильтрации по датам.
    Предоставляет удобные методы для временных запросов.
    """

    def filter_by_date_range(
        self,
        start_date: datetime | None = None,
        end_date: datetime | None = None,
        date_field: str = "created_at",
    ) -> QuerySet[Any, Any]:
        """
        Фильтрация объектов по диапазону дат.

        Args:
            start_date: Начальная дата
            end_date: Конечная дата
            date_field: Поле даты для фильтрации

        Returns:
            QuerySet отфильтрованный по датам
        """
        if not hasattr(self, "model"):
            raise AttributeError("Mixin requires 'model' attribute")

        queryset = self.model.objects.all()

        if start_date:
            queryset = queryset.filter(**{f"{date_field}__gte": start_date})
        if end_date:
            queryset = queryset.filter(**{f"{date_field}__lte": end_date})

        if hasattr(self, "_apply_optimizations"):
            queryset = self._apply_optimizations(queryset)

        return cast(QuerySet[Any, Any], queryset)

    def get_recent(self, days: int = 7, date_field: str = "created_at") -> QuerySet[Any, Any]:
        """
        Получить объекты за последние N дней.

        Args:
            days: Количество дней
            date_field: Поле даты для фильтрации

        Returns:
            QuerySet с недавними объектами
        """
        cutoff_date = timezone.now() - timedelta(days=days)
        return cast(QuerySet[Any, Any], self.filter_by_date_range(start_date=cutoff_date, date_field=date_field))

    def get_by_period(
        self,
        year: int,
        month: int | None = None,
        day: int | None = None,
        date_field: str = "created_at",
    ) -> QuerySet[Any, Any]:
        """
        Получить объекты за определенный период.

        Args:
            year: Год
            month: Месяц (опционально)
            day: День (опционально)
            date_field: Поле даты для фильтрации

        Returns:
            QuerySet за указанный период
        """
        if not hasattr(self, "model"):
            raise AttributeError("Mixin requires 'model' attribute")

        filters = {f"{date_field}__year": year}

        if month:
            filters[f"{date_field}__month"] = month
        if day:
            filters[f"{date_field}__day"] = day

        queryset = self.model.objects.filter(**filters)

        if hasattr(self, "_apply_optimizations"):
            queryset = self._apply_optimizations(queryset)

        return cast(QuerySet[Any, Any], queryset)

    def get_stale_objects(
        self, field: str = "last_updated", hours: int = 24
    ) -> QuerySet[Any, Any]:
        """
        Получить объекты, которые не обновлялись дольше указанного времени.

        Args:
            field: Поле для проверки
            hours: Количество часов

        Returns:
            QuerySet с устаревшими объектами
        """
        if not hasattr(self, "model"):
            raise AttributeError("Mixin requires 'model' attribute")

        cutoff_time = timezone.now() - timedelta(hours=hours)

        queryset = self.model.objects.filter(
            Q(**{f"{field}__lt": cutoff_time}) | Q(**{f"{field}__isnull": True})
        )

        if hasattr(self, "_apply_optimizations"):
            queryset = self._apply_optimizations(queryset)

        return cast(QuerySet[Any, Any], queryset)


class SoftDeleteMixin:
    """
    Миксин для мягкого удаления объектов.
    Работает с моделями, имеющими поле is_deleted.
    """

    def get_active(self) -> QuerySet[Any, Any]:
        """Получить только активные (не удаленные) объекты."""
        if not hasattr(self, "model"):
            raise AttributeError("Mixin requires 'model' attribute")

        queryset = self.model.objects.filter(is_deleted=False)

        if hasattr(self, "_apply_optimizations"):
            queryset = self._apply_optimizations(queryset)

        return cast(QuerySet[Any, Any], queryset)

    def get_deleted(self) -> QuerySet[Any, Any]:
        """Получить только удаленные объекты."""
        if not hasattr(self, "model"):
            raise AttributeError("Mixin requires 'model' attribute")

        queryset = self.model.objects.filter(is_deleted=True)

        if hasattr(self, "_apply_optimizations"):
            queryset = self._apply_optimizations(queryset)

        return cast(QuerySet[Any, Any], queryset)

    def soft_delete(self, id: int | str, user=None) -> bool:
        """
        Мягкое удаление объекта.

        Args:
            id: ID объекта
            user: Пользователь, выполняющий удаление

        Returns:
            True если удаление успешно
        """
        if not hasattr(self, "model"):
            raise AttributeError("Mixin requires 'model' attribute")

        try:
            obj = self.model.objects.get(pk=id)

            # Используем метод модели если он есть
            if hasattr(obj, "soft_delete"):
                obj.soft_delete(user=user)
            else:
                # Иначе просто устанавливаем флаг
                obj.is_deleted = True
                if hasattr(obj, "deleted_at"):
                    obj.deleted_at = timezone.now()
                if hasattr(obj, "deleted_by") and user:
                    obj.deleted_by = user
                obj.save()

            logger.info(f"Soft deleted {self.model.__name__} with id {id}")
            return True

        except self.model.DoesNotExist:
            logger.warning(f"{self.model.__name__} with id {id} not found for deletion")
            return False

    def restore(self, id: int | str) -> bool:
        """
        Восстановление мягко удаленного объекта.

        Args:
            id: ID объекта

        Returns:
            True если восстановление успешно
        """
        if not hasattr(self, "model"):
            raise AttributeError("Mixin requires 'model' attribute")

        try:
            obj = self.model.objects.get(pk=id, is_deleted=True)

            # Используем метод модели если он есть
            if hasattr(obj, "restore"):
                obj.restore()
            else:
                # Иначе просто сбрасываем флаг
                obj.is_deleted = False
                if hasattr(obj, "deleted_at"):
                    obj.deleted_at = None
                if hasattr(obj, "deleted_by"):
                    obj.deleted_by = None
                obj.save()

            logger.info(f"Restored {self.model.__name__} with id {id}")
            return True

        except self.model.DoesNotExist:
            logger.warning(
                f"{self.model.__name__} with id {id} not found for restoration"
            )
            return False


class StatisticsMixin:
    """
    Миксин для получения статистики по объектам.
    """

    def get_count_by_field(self, field: str) -> dict[Any, int]:
        """
        Подсчет количества объектов по значениям поля.

        Args:
            field: Поле для группировки

        Returns:
            Словарь {значение_поля: количество}
        """
        if not hasattr(self, "model"):
            raise AttributeError("Mixin requires 'model' attribute")

        from django.db.models import Count

        result = (
            self.model.objects.values(field)
            .annotate(count=Count("id"))
            .order_by("-count")
        )

        return {item[field]: item["count"] for item in result}

    def get_statistics(self) -> dict[str, Any]:
        """
        Получить общую статистику по модели.

        Returns:
            Словарь со статистикой
        """
        if not hasattr(self, "model"):
            raise AttributeError("Mixin requires 'model' attribute")

        from django.db.models import Max, Min

        stats = {
            "total_count": self.model.objects.count(),
        }

        # Добавляем статистику по is_deleted если есть
        if hasattr(self.model, "is_deleted"):
            stats["active_count"] = self.model.objects.filter(is_deleted=False).count()
            stats["deleted_count"] = self.model.objects.filter(is_deleted=True).count()

        # Добавляем временную статистику если есть created_at
        if hasattr(self.model, "created_at"):
            time_stats = self.model.objects.aggregate(
                first_created=Min("created_at"), last_created=Max("created_at")
            )
            stats.update(time_stats)

        return stats
