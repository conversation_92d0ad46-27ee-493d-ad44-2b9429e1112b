from abc import ABC, abstractmethod
from datetime import datetime
from typing import Any, Gene<PERSON>, TypeVar, cast

from django.core.exceptions import ObjectDoesNotExist
from django.core.paginator import Paginator
from django.db import models, transaction
from django.db.models import Q, QuerySet

from core.exceptions import DatabaseError, NotFoundError
from core.logging import ContextLogger

# Type variable for Django models
T = TypeVar("T", bound=models.Model)


class BaseRepository(Generic[T], ABC):
    """
    Базовый репозиторий для работы с моделями Django.
    Инкапсулирует логику доступа к данным.
    
    Поддерживает оптимизацию запросов через select_related и prefetch_related.
    """
    
    # Поля для оптимизации запросов - переопределите в наследниках
    select_related_fields: list[str] = []
    prefetch_related_fields: list[str] = []
    
    # Размер батча по умолчанию для bulk операций
    default_batch_size: int = 5000
    
    def __init__(self):
        self.logger = ContextLogger(__name__)
    
    @property
    @abstractmethod
    def model(self) -> type[T]:
        """Возвращает класс модели, с которой работает репозиторий."""
        raise NotImplementedError
    
    def _apply_optimizations(self, queryset: QuerySet[T]) -> QuerySet[T]:
        """
        Применяет оптимизации select_related и prefetch_related к queryset.
        
        Args:
            queryset: Исходный queryset
            
        Returns:
            Оптимизированный queryset
        """
        if self.select_related_fields:
            queryset = queryset.select_related(*self.select_related_fields)
        if self.prefetch_related_fields:
            queryset = queryset.prefetch_related(*self.prefetch_related_fields)
        return queryset
    
    def _apply_field_limits(self, queryset: QuerySet[T], only_fields: list[str] | None = None,
                           defer_fields: list[str] | None = None) -> QuerySet[T]:
        """
        Применяет only() или defer() к queryset для ограничения загружаемых полей.
        
        Args:
            queryset: Исходный queryset
            only_fields: Список полей для загрузки (only)
            defer_fields: Список полей для исключения (defer)
            
        Returns:
            QuerySet с ограниченными полями
        """
        if only_fields:
            queryset = queryset.only(*only_fields)
        elif defer_fields:
            queryset = queryset.defer(*defer_fields)
        return queryset
    
    def get_optimized_queryset(self) -> QuerySet[T]:
        """
        Возвращает оптимизированный queryset со всеми настройками.
        
        Returns:
            Оптимизированный QuerySet
        """
        return self._apply_optimizations(self.model.objects.all())
    
    def get_light_queryset(self, only_fields: list[str] | None = None,
                          defer_fields: list[str] | None = None) -> QuerySet[T]:
        """
        Возвращает queryset с ограниченным набором полей для легких запросов.
        
        Args:
            only_fields: Список полей для загрузки (only)
            defer_fields: Список полей для исключения (defer)
            
        Returns:
            QuerySet с ограниченными полями
        """
        queryset = self.model.objects.all()
        return self._apply_field_limits(queryset, only_fields, defer_fields)
    
    def get_by_id(self, id: int | str, optimized: bool = True) -> T:
        """
        Получает объект по ID.
        
        Args:
            id: ID объекта
            optimized: Применять ли оптимизации
            
        Returns:
            Найденный объект
            
        Raises:
            NotFoundError: Если объект не найден
        """
        try:
            queryset = self.model.objects.all()
            if optimized:
                queryset = self._apply_optimizations(queryset)
            return cast(T, queryset.get(pk=id))
        except ObjectDoesNotExist:
            raise NotFoundError(
                entity=self.model.__name__,
                identifier=id
            )
    
    def get_by_ids(self, ids: list[int | str]) -> list[T]:
        """
        Получает объекты по списку ID.
        
        Args:
            ids: Список ID
            
        Returns:
            Список найденных объектов
        """
        return list(self.model.objects.filter(pk__in=ids))
    
    def get_or_none(self, **kwargs) -> T | None:
        """
        Получает объект по критериям или None если не найден.
        
        Args:
            **kwargs: Критерии поиска
            
        Returns:
            Найденный объект или None
        """
        try:
            return cast(T, self.model.objects.get(**kwargs))
        except ObjectDoesNotExist:
            return None
        except models.Model.MultipleObjectsReturned:
            # Если найдено несколько объектов, возвращаем первый
            return cast(T | None, self.model.objects.filter(**kwargs).first())
    
    def filter(self, optimized: bool = False, only_fields: list[str] | None = None,
              defer_fields: list[str] | None = None, **kwargs) -> QuerySet[T]:
        """
        Фильтрует объекты по критериям.
        
        Args:
            optimized: Применять ли оптимизации
            only_fields: Список полей для загрузки (only)
            defer_fields: Список полей для исключения (defer)
            **kwargs: Критерии фильтрации
            
        Returns:
            QuerySet с отфильтрованными объектами
        """
        queryset = cast(QuerySet[T], self.model.objects.filter(**kwargs))
        if optimized:
            queryset = self._apply_optimizations(queryset)
        if only_fields or defer_fields:
            queryset = self._apply_field_limits(queryset, only_fields, defer_fields)
        return queryset
    
    def all(self, optimized: bool = False, only_fields: list[str] | None = None,
            defer_fields: list[str] | None = None) -> QuerySet[T]:
        """
        Возвращает все объекты.
        
        Args:
            optimized: Применять ли оптимизации
            only_fields: Список полей для загрузки (only)
            defer_fields: Список полей для исключения (defer)
            
        Returns:
            QuerySet со всеми объектами
        """
        queryset = cast(QuerySet[T], self.model.objects.all())
        if optimized:
            queryset = self._apply_optimizations(queryset)
        if only_fields or defer_fields:
            queryset = self._apply_field_limits(queryset, only_fields, defer_fields)
        return queryset
    
    def exists(self, **kwargs) -> bool:
        """
        Проверяет существование объекта по критериям.
        
        Args:
            **kwargs: Критерии поиска
            
        Returns:
            True если объект существует
        """
        return bool(self.model.objects.filter(**kwargs).exists())
    
    def count(self, **kwargs) -> int:
        """
        Подсчитывает количество объектов по критериям.
        
        Args:
            **kwargs: Критерии фильтрации
            
        Returns:
            Количество объектов
        """
        return int(self.model.objects.filter(**kwargs).count())
    
    @transaction.atomic
    def create(self, **data) -> T:
        """
        Создает новый объект.
        
        Args:
            **data: Данные для создания объекта
            
        Returns:
            Созданный объект
            
        Raises:
            DatabaseError: При ошибке создания
        """
        try:
            return cast(T, self.model.objects.create(**data))
        except Exception as e:
            raise DatabaseError(f"Failed to create {self.model.__name__}: {e!s}") from e
    
    @transaction.atomic
    def update(self, id: int | str, **data) -> T:
        """
        Обновляет объект по ID.
        
        Args:
            id: ID объекта
            **data: Данные для обновления
            
        Returns:
            Обновленный объект
            
        Raises:
            NotFoundError: Если объект не найден
            DatabaseError: При ошибке обновления
        """
        try:
            obj = self.get_by_id(id)
            for key, value in data.items():
                setattr(obj, key, value)
            obj.save()
            return obj
        except NotFoundError:
            raise
        except Exception as e:
            raise DatabaseError(f"Failed to update {self.model.__name__}: {e!s}") from e
    
    @transaction.atomic
    def delete(self, id: int | str) -> bool:
        """
        Удаляет объект по ID.
        
        Args:
            id: ID объекта
            
        Returns:
            True если объект удален
            
        Raises:
            NotFoundError: Если объект не найден
            DatabaseError: При ошибке удаления
        """
        try:
            obj = self.get_by_id(id)
            obj.delete()
            return True
        except NotFoundError:
            raise
        except Exception as e:
            raise DatabaseError(f"Failed to delete {self.model.__name__}: {e!s}") from e
    
    @transaction.atomic
    def bulk_create(self, objects: list[T], batch_size: int | None = None) -> list[T]:
        """
        Массовое создание объектов.
        
        Args:
            objects: Список объектов для создания
            batch_size: Размер батча (если None, используется default_batch_size)
            
        Returns:
            Список созданных объектов
            
        Raises:
            DatabaseError: При ошибке создания
        """
        if batch_size is None:
            batch_size = self.default_batch_size
            
        try:
            created = self.model.objects.bulk_create(objects, batch_size=batch_size)
            return cast(list[T], created)
        except Exception as e:
            raise DatabaseError(f"Failed to bulk create {self.model.__name__}: {e!s}") from e
    
    @transaction.atomic
    def bulk_update(self, objects: list[T], fields: list[str], batch_size: int | None = None) -> int:
        """
        Массовое обновление объектов.
        
        Args:
            objects: Список объектов для обновления
            fields: Список полей для обновления
            batch_size: Размер батча (если None, используется default_batch_size)
            
        Returns:
            Количество обновленных объектов
            
        Raises:
            DatabaseError: При ошибке обновления
        """
        if batch_size is None:
            batch_size = self.default_batch_size
            
        try:
            return int(self.model.objects.bulk_update(objects, fields, batch_size=batch_size))
        except Exception as e:
            raise DatabaseError(f"Failed to bulk update {self.model.__name__}: {e!s}") from e
    
    def get_or_create(self, defaults: dict[str, Any] | None = None, **kwargs) -> tuple[T, bool]:
        """
        Получает объект или создает новый если не найден.
        
        Args:
            defaults: Значения по умолчанию для создания
            **kwargs: Критерии поиска
            
        Returns:
            Кортеж (объект, создан_ли_новый)
        """
        obj, created = self.model.objects.get_or_create(defaults=defaults, **kwargs)
        return cast(T, obj), created
    
    def update_or_create(self, defaults: dict[str, Any] | None = None, **kwargs) -> tuple[T, bool]:
        """
        Обновляет объект или создает новый если не найден.
        
        Args:
            defaults: Значения для обновления/создания
            **kwargs: Критерии поиска
            
        Returns:
            Кортеж (объект, создан_ли_новый)
        """
        obj, created = self.model.objects.update_or_create(defaults=defaults, **kwargs)
        return cast(T, obj), created
    
    def paginate(self, queryset: QuerySet[T] | None = None, page: int = 1, 
                 per_page: int = 20, optimized: bool = True) -> dict[str, Any]:
        """
        Пагинация результатов.
        
        Args:
            queryset: QuerySet для пагинации (если None, используется all())
            page: Номер страницы
            per_page: Количество элементов на странице
            optimized: Применять ли оптимизации
            
        Returns:
            Словарь с результатами пагинации
        """
        if queryset is None:
            queryset = self.all(optimized=optimized)
        elif optimized:
            queryset = self._apply_optimizations(queryset)
        
        # Добавляем сортировку для стабильной пагинации
        if not queryset.ordered:
            queryset = queryset.order_by("id")
        
        paginator = Paginator(queryset, per_page)
        page_obj = paginator.get_page(page)
        
        return {
            "items": list(page_obj),
            "total": paginator.count,
            "page": page_obj.number,
            "per_page": per_page,
            "total_pages": paginator.num_pages,
            "has_next": page_obj.has_next(),
            "has_previous": page_obj.has_previous(),
        }
    
    def search(self, query: str, search_fields: list[str], optimized: bool = True) -> QuerySet[T]:
        """
        Поиск по текстовым полям.
        
        Args:
            query: Поисковый запрос
            search_fields: Список полей для поиска
            optimized: Применять ли оптимизации
            
        Returns:
            QuerySet с результатами поиска
        """
        q_objects = Q()
        for field in search_fields:
            q_objects |= Q(**{f"{field}__icontains": query})
        
        queryset = cast(QuerySet[T], self.model.objects.filter(q_objects))
        if optimized:
            queryset = self._apply_optimizations(queryset)
        return queryset
    
    def filter_by_date_range(self, date_field: str, date_from: datetime, 
                            date_to: datetime) -> QuerySet[T]:
        """
        Фильтрация по диапазону дат.
        
        Args:
            date_field: Название поля с датой
            date_from: Начальная дата
            date_to: Конечная дата
            
        Returns:
            QuerySet с отфильтрованными объектами
        """
        return cast(QuerySet[T], self.model.objects.filter(**{
            f"{date_field}__gte": date_from,
            f"{date_field}__lte": date_to
        }))


    
