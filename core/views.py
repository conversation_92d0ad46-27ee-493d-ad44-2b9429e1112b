
"""
Views for task monitoring and progress tracking.
"""

from datetime import <PERSON><PERSON><PERSON>

from asgiref.sync import sync_to_async
from django.contrib.auth.mixins import LoginRequiredMixin
from django.db.models import Avg, Count, Q
from django.http import HttpResponseNotFound, JsonResponse
from django.shortcuts import get_object_or_404
from django.template import loader
from django.utils import timezone
from django.views import View
from django.views.generic import DetailView, ListView, TemplateView

from core.models import TaskResult
from core.tasks.base import TaskStatus


class TaskListView(LoginRequiredMixin, ListView):
    """List view for all tasks."""
    model = TaskResult
    template_name = "core/tasks/task_list.html"
    context_object_name = "tasks"
    paginate_by = 25
    
    def get_queryset(self):
        """Get filtered and sorted task list."""
        queryset = super().get_queryset()
        
        # Filter by status
        status = self.request.GET.get("status")
        if status:
            queryset = queryset.filter(status=status)
        
        # Filter by task type
        task_type = self.request.GET.get("task_type")
        if task_type:
            queryset = queryset.filter(task_type=task_type)
        
        # Filter by date range
        date_from = self.request.GET.get("date_from")
        if date_from:
            queryset = queryset.filter(created_at__gte=date_from)
        
        date_to = self.request.GET.get("date_to")
        if date_to:
            queryset = queryset.filter(created_at__lte=date_to)
        
        # Sorting
        sort_by = self.request.GET.get("sort", "-created_at")
        queryset = queryset.order_by(sort_by)
        
        return queryset.select_related("created_by", "parent_task")
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        # Add filter values
        context["current_status"] = self.request.GET.get("status")
        context["current_task_type"] = self.request.GET.get("task_type")
        context["date_from"] = self.request.GET.get("date_from")
        context["date_to"] = self.request.GET.get("date_to")
        
        # Add statistics
        context["status_choices"] = TaskStatus.choices
        context["task_types"] = TaskResult.objects.values_list(
            "task_type", flat=True
        ).distinct()
        
        # Task statistics
        context["stats"] = self._get_task_statistics()
        
        return context
    
    def _get_task_statistics(self):
        """Get task statistics for the dashboard."""
        now = timezone.now()
        last_24h = now - timedelta(hours=24)
        
        return {
            "total_tasks": TaskResult.objects.count(),
            "pending_tasks": TaskResult.objects.filter(status=TaskStatus.PENDING).count(),
            "running_tasks": TaskResult.objects.filter(status=TaskStatus.RUNNING).count(),
            "completed_today": TaskResult.objects.filter(
                status=TaskStatus.SUCCESS,
                completed_at__gte=last_24h
            ).count(),
            "failed_today": TaskResult.objects.filter(
                status=TaskStatus.FAILED,
                completed_at__gte=last_24h
            ).count(),
            "avg_execution_time": TaskResult.objects.filter(
                status=TaskStatus.SUCCESS,
                started_at__isnull=False,
                completed_at__isnull=False
            ).aggregate(
                avg_time=Avg("completed_at") - Avg("started_at")
            ).get("avg_time"),
        }


class TaskDetailView(LoginRequiredMixin, DetailView):
    """Detail view for a specific task."""
    model = TaskResult
    template_name = "core/tasks/task_detail.html"
    context_object_name = "task"
    pk_url_kwarg = "task_id"
    
    def get_object(self, queryset=None):
        """Get task by task_id instead of pk."""
        task_id = self.kwargs.get(self.pk_url_kwarg)
        return get_object_or_404(TaskResult, task_id=task_id)
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        # Add subtasks if any
        context["subtasks"] = self.object.subtasks.all()
        
        # Calculate execution time
        if self.object.started_at and self.object.completed_at:
            context["execution_time"] = (
                self.object.completed_at - self.object.started_at
            ).total_seconds()
        
        return context


class TaskProgressView(LoginRequiredMixin, View):
    """API endpoint for task progress updates."""
    
    def get(self, request, task_id):
        """Get current task progress."""
        task = get_object_or_404(TaskResult, task_id=task_id)
        
        # Sync with Celery if task has celery_task_id
        if task.celery_task_id:
            task.sync_with_celery()
        
        # Calculate progress percentage
        if task.total_items > 0:
            progress_percentage = (task.processed_items / task.total_items) * 100
        else:
            progress_percentage = 0
        
        # Calculate execution time
        execution_time = None
        if task.started_at:
            if task.completed_at:
                execution_time = (task.completed_at - task.started_at).total_seconds()
            else:
                execution_time = (timezone.now() - task.started_at).total_seconds()
        
        return JsonResponse({
            "task_id": task.task_id,
            "status": task.status,
            "progress": progress_percentage,
            "processed_items": task.processed_items,
            "total_items": task.total_items,
            "progress_message": task.progress_message,
            "error_message": task.error_message,
            "execution_time": execution_time,
            "result": task.result,
            "created_at": task.created_at.isoformat(),
            "started_at": task.started_at.isoformat() if task.started_at else None,
            "completed_at": task.completed_at.isoformat() if task.completed_at else None,
        })


class TaskStatsAPIView(LoginRequiredMixin, View):
    """API endpoint for task statistics."""
    
    def get(self, request):
        """Get task statistics for charts."""
        # Get time range
        hours = int(request.GET.get("hours", 24))
        since = timezone.now() - timedelta(hours=hours)
        
        # Task completion over time
        tasks_by_hour = TaskResult.objects.filter(
            created_at__gte=since
        ).extra(
            select={"hour": "date_trunc('hour', created_at)"}
        ).values("hour").annotate(
            total=Count("id"),
            success=Count("id", filter=Q(status=TaskStatus.SUCCESS)),
            failed=Count("id", filter=Q(status=TaskStatus.FAILED)),
        ).order_by("hour")
        
        # Task types distribution
        task_types = TaskResult.objects.filter(
            created_at__gte=since
        ).values("task_type").annotate(
            count=Count("id")
        ).order_by("-count")[:10]
        
        # Average execution time by task type
        avg_execution = TaskResult.objects.filter(
            status=TaskStatus.SUCCESS,
            created_at__gte=since,
            started_at__isnull=False,
            completed_at__isnull=False
        ).values("task_type").annotate(
            avg_seconds=Avg("completed_at") - Avg("started_at")
        ).order_by("-avg_seconds")[:10]
        
        return JsonResponse({
            "tasks_by_hour": list(tasks_by_hour),
            "task_types": list(task_types),
            "avg_execution": list(avg_execution),
        })


class TaskQueueView(LoginRequiredMixin, TemplateView):
    """View for monitoring task queue."""
    template_name = "core/tasks/task_queue.html"
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        # Get queue status
        context["pending_tasks"] = TaskResult.objects.filter(
            status=TaskStatus.PENDING
        ).order_by("-priority", "created_at")[:20]
        
        context["running_tasks"] = TaskResult.objects.filter(
            status=TaskStatus.RUNNING
        ).order_by("started_at")
        
        # Get recent completed tasks
        context["recent_completed"] = TaskResult.objects.filter(
            status__in=[TaskStatus.SUCCESS, TaskStatus.FAILED]
        ).order_by("-completed_at")[:10]
        
        # Queue statistics
        context["queue_stats"] = {
            "pending_count": TaskResult.objects.filter(status=TaskStatus.PENDING).count(),
            "running_count": TaskResult.objects.filter(status=TaskStatus.RUNNING).count(),
            "retry_count": TaskResult.objects.filter(status=TaskStatus.RETRY).count(),
        }
        
        return context


class TaskActionView(LoginRequiredMixin, View):
    """API endpoint for task actions (cancel, retry, etc)."""
    
    def post(self, request, task_id):
        """Perform action on task."""
        task = get_object_or_404(TaskResult, task_id=task_id)
        action = request.POST.get("action")
        
        if action == "cancel":
            if task.status in [TaskStatus.PENDING, TaskStatus.RUNNING]:
                # Try to revoke via Celery if possible
                if task.celery_task_id:
                    task.revoke_celery_task(terminate=True)
                else:
                    task.status = TaskStatus.CANCELLED
                    task.completed_at = timezone.now()
                    task.error_message = "Task cancelled by user"
                    task.save()
                return JsonResponse({"status": "success", "message": "Task cancelled"})
            else:
                return JsonResponse(
                    {"status": "error", "message": "Cannot cancel completed task"},
                    status=400
                )
        
        elif action == "retry":
            if task.status in [TaskStatus.FAILED, TaskStatus.CANCELLED]:
                # Use Celery to retry the task
                from core.tasks.celery_integration import execute_base_task
                
                # Extract the task class path from task_type
                # Format: "module.path.ClassName"
                task_class_path = task.task_type
                
                # Create new task result
                new_task = TaskResult.objects.create(
                    task_type=task.task_type,
                    status=TaskStatus.PENDING,
                    parameters=task.parameters,
                    parent_task=task.parent_task,
                    created_by=request.user,
                    priority=task.priority,
                    max_retries=task.max_retries,
                    total_items=task.total_items
                )
                
                # Execute via Celery
                celery_result = execute_base_task.delay(
                    task_class_path,
                    task_id=new_task.task_id,
                    **task.parameters
                )
                
                # Update with Celery ID
                new_task.celery_task_id = celery_result.id
                new_task.celery_status = celery_result.status
                new_task.save()
                
                return JsonResponse({
                    "status": "success",
                    "message": "Task requeued via Celery",
                    "new_task_id": new_task.task_id,
                    "celery_task_id": celery_result.id
                })
            else:
                return JsonResponse(
                    {"status": "error", "message": "Can only retry failed tasks"},
                    status=400
                )
        
        return JsonResponse(
            {"status": "error", "message": "Unknown action"},
            status=400
        )


# Async Views for ASGI
class AsyncTaskProgressView(View):
    """Async API endpoint for task progress updates."""
    
    async def get(self, request, task_id):
        """Get current task progress asynchronously."""
        # Get task using sync_to_async
        task = await sync_to_async(
            lambda: get_object_or_404(TaskResult, task_id=task_id)
        )()
        
        # Sync with Celery if task has celery_task_id
        if task.celery_task_id:
            await sync_to_async(task.sync_with_celery)()
        
        # Calculate progress percentage
        if task.total_items > 0:
            progress_percentage = (task.processed_items / task.total_items) * 100
        else:
            progress_percentage = 0
        
        # Calculate execution time
        execution_time = None
        started_at = await sync_to_async(lambda: task.started_at)()
        completed_at = await sync_to_async(lambda: task.completed_at)()
        
        if started_at:
            if completed_at:
                execution_time = (completed_at - started_at).total_seconds()
            else:
                execution_time = (timezone.now() - started_at).total_seconds()
        
        # Get all task attributes using sync_to_async
        task_data = await sync_to_async(lambda: {
            "task_id": task.task_id,
            "status": task.status,
            "progress": progress_percentage,
            "processed_items": task.processed_items,
            "total_items": task.total_items,
            "progress_message": task.progress_message,
            "error_message": task.error_message,
            "execution_time": execution_time,
            "result": task.result,
            "created_at": task.created_at.isoformat(),
            "started_at": task.started_at.isoformat() if task.started_at else None,
            "completed_at": task.completed_at.isoformat() if task.completed_at else None,
        })()
        
        return JsonResponse(task_data)


async def health_check_async(request):
    """Async health check endpoint."""
    checks = {
        "database": False,
        "redis": False,
        "asgi": True,
    }
    
    # Check database
    try:
        await sync_to_async(TaskResult.objects.count)()
        checks["database"] = True
    except Exception:
        pass
    
    # Redis check removed - cache is no longer used
    
    status_code = 200 if all(checks.values()) else 503
    return JsonResponse({
        "status": "ok" if status_code == 200 else "degraded",
        "checks": checks,
        "server": "asgi",
        "timestamp": timezone.now().isoformat()
    }, status=status_code)


def custom_page_not_found(request, exception=None):
    """
    Custom 404 error handler.
    
    Args:
        request: HttpRequest object
        exception: Http404 exception (optional)
        
    Returns:
        HttpResponseNotFound with custom 404 template
    """
    template = loader.get_template("404.html")
    context = {
        "request": request,
        "exception": str(exception) if exception else None,
    }
    return HttpResponseNotFound(template.render(context, request))
