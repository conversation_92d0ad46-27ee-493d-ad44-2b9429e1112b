// core/static/js/forms/media-options.js

document.addEventListener('DOMContentLoaded', function() {
    'use strict';
    
    // Handle field dependencies
    function setupFieldDependencies() {
        const dependentFields = document.querySelectorAll('[data-depends-on]');
        
        dependentFields.forEach(field => {
            const dependsOn = field.getAttribute('data-depends-on');
            const dependsOnValue = field.getAttribute('data-depends-on-value');
            const parentField = document.getElementById(dependsOn);
            
            if (!parentField) return;
            
            // Function to update visibility
            function updateVisibility() {
                const parentValue = parentField.type === 'checkbox' 
                    ? parentField.checked.toString() 
                    : parentField.value;
                
                const shouldShow = parentValue === dependsOnValue;
                const fieldRow = field.closest('.form-row, .form-group, .field-save_media_to_gcs, .checkbox-row');
                
                if (fieldRow) {
                    fieldRow.style.display = shouldShow ? '' : 'none';
                    
                    // Also disable field when hidden
                    field.disabled = !shouldShow;
                    
                    // Uncheck if hidden
                    if (!shouldShow && field.type === 'checkbox') {
                        field.checked = false;
                    }
                }
            }
            
            // Initial state
            updateVisibility();
            
            // Listen for changes
            parentField.addEventListener('change', updateVisibility);
        });
    }
    
    // Handle auto-check functionality with bidirectional dependency
    function setupAutoCheck() {
        const autoCheckFields = document.querySelectorAll('[data-auto-check]');
        
        autoCheckFields.forEach(field => {
            const targetId = field.getAttribute('data-auto-check');
            const targetField = document.getElementById(targetId);
            
            if (!targetField) return;
            
            // When GCS is checked, auto-check download_media
            field.addEventListener('change', function() {
                if (field.checked && !targetField.checked) {
                    targetField.checked = true;
                    // Trigger change event on target field
                    targetField.dispatchEvent(new Event('change', { bubbles: true }));
                }
            });
            
            // When download_media is unchecked, also uncheck GCS
            targetField.addEventListener('change', function() {
                if (!targetField.checked && field.checked) {
                    // Silently uncheck GCS when download_media is unchecked
                    field.checked = false;
                    // Add visual feedback
                    const fieldRow = field.closest('.checkbox-row');
                    if (fieldRow) {
                        fieldRow.style.transition = 'opacity 0.3s ease';
                        fieldRow.style.opacity = '0.7';
                        setTimeout(() => {
                            fieldRow.style.opacity = '1';
                        }, 300);
                    }
                }
            });
        });
    }
    
    // Initialize
    setupFieldDependencies();
    setupAutoCheck();
    
    // Also handle dynamic form additions (if using formsets)
    document.addEventListener('formset:added', function() {
        setupFieldDependencies();
        setupAutoCheck();
    });
});