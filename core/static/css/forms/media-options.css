/* core/static/css/forms/media-options.css */

/* Smooth transitions for showing/hiding fields */
.form-row, .form-group, .checkbox-row {
    transition: all 0.3s ease;
}

/* Checkbox group styling */
.checkbox-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

/* Base checkbox row styling */
.checkbox-row {
    display: flex;
    align-items: flex-start;
    gap: 10px;
    padding: 12px 16px;
    background: #f8f9fa;
    border-radius: 8px;
    transition: all 0.2s ease;
    position: relative;
}

.checkbox-row:hover {
    background: #e9ecef;
    transform: translateX(2px);
}

/* Checkbox input styling */
.checkbox-row input[type="checkbox"] {
    width: 20px;
    height: 20px;
    cursor: pointer;
    flex-shrink: 0;
    margin-top: 2px;
}

/* Label styling */
.checkbox-row label {
    margin: 0;
    cursor: pointer;
    flex-grow: 1;
    display: flex;
    flex-direction: column;
    gap: 4px;
}

/* Help text styling */
.checkbox-row .helptext {
    color: #6c757d;
    font-size: 0.875rem;
    line-height: 1.4;
    display: block;
}

/* Style for GCS checkbox - show dependency */
.field-save_media_to_gcs {
    margin-left: 32px;
    position: relative;
    background: #f0f4f8;
    border: 1px solid #e2e8f0;
}

/* Visual connector line */
.field-save_media_to_gcs::before {
    content: '';
    position: absolute;
    left: -20px;
    top: 50%;
    width: 16px;
    height: 2px;
    background: #cbd5e0;
    transform: translateY(-50%);
}

/* Vertical connector */
.field-save_media_to_gcs::after {
    content: '';
    position: absolute;
    left: -20px;
    top: -8px;
    width: 2px;
    height: calc(50% + 8px);
    background: #cbd5e0;
}

/* Highlight GCS option when available */
.field-save_media_to_gcs label {
    color: #3b82f6;
    font-weight: 500;
}

/* Disabled state */
.field-save_media_to_gcs.disabled {
    opacity: 0.6;
    pointer-events: none;
}

/* Bucket info styling */
.field-save_media_to_gcs .bucket-info,
.bucket-info {
    color: #059669;
    font-size: 0.85em;
    margin-top: 4px;
    font-weight: 600;
    padding: 4px 8px;
    background: #d1fae5;
    border-radius: 4px;
    display: inline-block;
    margin-left: 30px;
}

/* Error state */
.checkbox-row.errors {
    border: 1px solid #dc3545;
    background: #fff5f5;
}

/* Animation for auto-unchecking */
@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.6; }
    100% { opacity: 1; }
}

.checkbox-row.auto-updated {
    animation: pulse 0.6s ease;
}

/* Responsive design */
@media (max-width: 768px) {
    .field-save_media_to_gcs {
        margin-left: 16px;
    }
    
    .field-save_media_to_gcs::before {
        left: -12px;
        width: 10px;
    }
    
    .field-save_media_to_gcs::after {
        left: -12px;
    }
}