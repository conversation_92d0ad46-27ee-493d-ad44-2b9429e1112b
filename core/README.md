# Core Module

Базовый модуль с общей функциональностью для всего проекта SocialManager.

## Структура модуля

```
core/
├── models/           # Базовые модели
├── repositories/     # Базовые репозитории
├── services/         # Базовые сервисы
├── exceptions/       # Обработка исключений
├── utils/           # Утилиты
├── decorators/      # Декораторы
├── forms/           # Базовые формы и валидаторы
├── storage/         # Сервисы хранения (включая GCS)
└── tasks/           # Базовые классы для Celery задач
```

## Storage Module

### Google Cloud Storage (GCS) Service

Модуль `core.storage.gcs_service` предоставляет интеграцию с Google Cloud Storage для хранения медиафайлов.

#### Основные возможности

- Загрузка файлов в GCS с автоматической организацией по папкам
- Валидация типов и размеров файлов
- Удаление файлов
- Проверка доступности сервиса

#### Структура папок в GCS

```
bucket/
├── images/      # Изображения (фотографии)
├── videos/      # Видеофайлы
└── previews/    # Превью/миниатюры видео
```

#### Использование

```python
from core.storage.gcs_service import GCSService

# Инициализация
gcs = GCSService()

# Загрузка файла
url = gcs.upload_file(
    file_content=b"...",
    original_filename="photo.jpg",
    content_type="image/jpeg",
    folder="images"  # Опционально, по умолчанию "images"
)

# Определение папки по типу медиа
folder = GCSService.get_folder_by_media_type("video")  # Вернет "videos"
folder = GCSService.get_folder_by_media_type("image", is_thumbnail=True)  # Вернет "previews"

# Удаление файла
success = gcs.delete_file("images/20250110_123456_abc123_photo.jpg")

# Проверка доступности
if gcs.is_available():
    print("GCS configured and available")
```

#### Конфигурация

Необходимые переменные окружения:

```bash
GCS_BUCKET_NAME=your-bucket-name
GOOGLE_APPLICATION_CREDENTIALS=/path/to/service-account-key.json
```

#### Валидация

- **Максимальный размер файла**: 100MB
- **Разрешенные типы контента**: 
  - Изображения: `image/jpeg`, `image/png`, `image/gif`, `image/webp`
  - Видео: `video/mp4`, `video/quicktime`
- **Разрешенные расширения**: `.jpg`, `.jpeg`, `.png`, `.gif`, `.webp`, `.mp4`, `.mov`

## Models

Базовые абстрактные модели для наследования:

- `TimestampedModel` - добавляет поля `created_at` и `updated_at`
- `SoftDeleteModel` - добавляет soft delete функциональность
- `BaseModel` - комбинирует timestamped и soft delete

## Repositories

Базовый репозиторий с CRUD операциями:

```python
from core.repositories.base import BaseRepository

class MyRepository(BaseRepository):
    model = MyModel
```

## Services

Базовые сервисы для бизнес-логики:

```python
from core.services.base import BaseService

class MyService(BaseService):
    repository_class = MyRepository
```

## Tasks

Базовые классы для Celery задач с поддержкой прогресса:

```python
from core.tasks.base import BaseTask

class MyTask(BaseTask):
    name = "my_task"
    
    def run(self, *args, **kwargs):
        self.update_progress(0.5, "Processing...")
        # Task logic here
```

## Forms

Валидаторы и миксины для Django форм:

- `InstagramFormMixin` - валидация Instagram usernames
- `DateRangeFormMixin` - валидация диапазонов дат
- Pydantic-based валидаторы

## Decorators

Полезные декораторы:

- `@validate_input` - валидация входных данных с Pydantic
- `@require_permission` - проверка прав доступа
- `@deprecated` - пометка устаревших методов
- `@async_to_sync_django` - конвертация async функций для Django

## Exceptions

Базовые классы исключений:

- `BaseServiceException` - базовое исключение для сервисов
- `ValidationException` - ошибки валидации
- `RepositoryException` - ошибки репозиториев