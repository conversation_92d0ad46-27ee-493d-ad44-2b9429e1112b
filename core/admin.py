
"""
Admin configuration for core models.
"""

from django.contrib import admin
from django.urls import reverse
from django.utils.html import format_html
from django.utils.safestring import mark_safe

from core.models import TaskResult


@admin.register(TaskResult)
class TaskResultAdmin(admin.ModelAdmin):
    """Admin configuration for TaskResult model with query optimizations."""
    
    list_display = [
        "task_id_link",
        "task_type",
        "status_colored",
        "progress_bar",
        "created_at",
        "started_at",
        "completed_at",
        "created_by",
    ]
    
    # Query optimization - добавляем select_related для created_by
    list_select_related = ["created_by"]
    
    list_filter = [
        "status",
        "task_type",
        "created_at",
        "priority",
    ]
    
    search_fields = [
        "task_id",
        "task_type",
        "progress_message",
        "error_message",
    ]
    
    readonly_fields = [
        "task_id",
        "created_at",
        "updated_at",
        "started_at",
        "completed_at",
        "execution_time",
        "formatted_parameters",
        "formatted_result",
    ]
    
    date_hierarchy = "created_at"
    
    ordering = ["-created_at"]
    
    def task_id_link(self, obj):
        """Display task ID as a link to detail view."""
        url = reverse("core:task-detail", kwargs={"task_id": obj.task_id})
        return format_html('<a href="{}" target="_blank">{}</a>', url, obj.task_id[:12] + "...")
    task_id_link.short_description = "Task ID"
    
    def status_colored(self, obj):
        """Display status with color coding."""
        colors = {
            "pending": "#ffc107",
            "running": "#17a2b8",
            "success": "#28a745",
            "failed": "#dc3545",
            "retry": "#fd7e14",
            "cancelled": "#6c757d",
        }
        color = colors.get(obj.status, "#333")
        return format_html(
            '<span style="background-color: {}; color: white; padding: 3px 8px; '
            'border-radius: 3px; font-weight: bold;">{}</span>',
            color,
            obj.get_status_display()
        )
    status_colored.short_description = "Status"
    
    def progress_bar(self, obj):
        """Display progress as a bar."""
        if obj.total_items > 0:
            percentage = (obj.processed_items / obj.total_items) * 100
            return format_html(
                '<div style="width: 150px; height: 20px; background: #e9ecef; '
                'border-radius: 10px; overflow: hidden;">'
                '<div style="width: {}%; height: 100%; background: #17a2b8;"></div>'
                '</div>'
                '<small>{}/{} items</small>',
                percentage,
                obj.processed_items,
                obj.total_items
            )
        return "-"
    progress_bar.short_description = "Progress"
    
    def execution_time(self, obj):
        """Calculate and display execution time."""
        if obj.started_at and obj.completed_at:
            duration = obj.completed_at - obj.started_at
            return f"{duration.total_seconds():.2f} seconds"
        return "-"
    execution_time.short_description = "Execution Time"
    
    def formatted_parameters(self, obj):
        """Display parameters as formatted JSON."""
        import json
        if obj.parameters:
            return format_html(
                '<pre style="background: #f8f9fa; padding: 10px; '
                'border-radius: 5px; overflow: auto;">{}</pre>',
                json.dumps(obj.parameters, indent=2)
            )
        return "-"
    formatted_parameters.short_description = "Parameters"
    
    def formatted_result(self, obj):
        """Display result as formatted JSON."""
        import json
        if obj.result:
            return format_html(
                '<pre style="background: #f8f9fa; padding: 10px; '
                'border-radius: 5px; overflow: auto;">{}</pre>',
                json.dumps(obj.result, indent=2)
            )
        return "-"
    formatted_result.short_description = "Result"
    
    def has_add_permission(self, request):
        """Disable manual task creation in admin."""
        return False
    
    def changelist_view(self, request, extra_context=None):
        """Add custom links to the changelist view."""
        extra_context = extra_context or {}
        extra_context["extra_buttons"] = mark_safe(
            f'<a href="{reverse("core:task-list")}" class="button" '
            'target="_blank">Open Task Monitor</a> '
            f'<a href="{reverse("core:task-queue")}" class="button" '
            'target="_blank">View Task Queue</a>'
        )
        return super().changelist_view(request, extra_context=extra_context)


# Customize admin site header
admin.site.site_header = "Social Manager Administration"
admin.site.site_title = "Social Manager"
admin.site.index_title = "Welcome to Social Manager"
