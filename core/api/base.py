import asyncio
from abc import ABC, abstractmethod
from datetime import datetime
from typing import Any, cast

from django.http import HttpResponse

from core.exceptions import NetworkError
from core.utils.error_handler import ErrorHandler


class BaseSocialMediaAPI(ABC):
    """
    Абстрактный базовый класс для всех API клиентов социальных медиа.
    """

    def __init__(self, api_name: str):
        self.api_name = api_name
        self._session = None
        self._is_authenticated = False

    @abstractmethod
    async def authenticate(self, credentials: dict[str, Any]) -> bool:
        """
        Аутентифицирует клиента в API.
        Args:
            credentials: Учетные данные для аутентификации
        Returns:
            True если аутентификация успешна
        """
        raise NotImplementedError

    @abstractmethod
    async def get_user(self, user_id: str | int) -> dict[str, Any]:
        """
        Получает информацию о пользователе.

        Args:
            user_id: ID пользователя

        Returns:
            Словарь с данными пользователя
        """
        raise NotImplementedError

    @abstractmethod
    async def get_posts(
        self,
        user_id: str | int,
        limit: int = 50,
        date_from: datetime | None = None,
        date_to: datetime | None = None,
    ) -> list[dict[str, Any]]:
        """
        Получает посты пользователя.

        Args:
            user_id: ID пользователя
            limit: Максимальное количество постов
            date_from: Начальная дата
            date_to: Конечная дата

        Returns:
            Список постов
        """
        raise NotImplementedError

    @abstractmethod
    async def get_post_comments(
        self, post_id: str | int, limit: int = 100
    ) -> list[dict[str, Any]]:
        """
        Получает комментарии к посту.

        Args:
            post_id: ID поста
            limit: Максимальное количество комментариев

        Returns:
            Список комментариев
        """
        raise NotImplementedError

    async def close(self):
        """
        Закрывает соединение с API.
        """
        if self._session:
            await self._session.close()
            self._session = None
        self._is_authenticated = False

    def check_authentication(self):
        """
        Проверяет, аутентифицирован ли клиент.

        Raises:
            APIError: Если клиент не аутентифицирован
        """
        if not self._is_authenticated:
            from core.exceptions import AuthenticationError

            raise AuthenticationError(f"{self.api_name} client is not authenticated")

    @ErrorHandler.retry_on_error(max_attempts=3, delay=1.0, exceptions=(NetworkError,))
    async def _make_request(self, method: str, url: str, **kwargs) -> Any:
        """
        Базовый метод для выполнения HTTP запросов с обработкой ошибок.

        Args:
            method: HTTP метод
            url: URL для запроса
            **kwargs: Дополнительные параметры запроса

        Returns:
            Ответ от API
        """
        # Этот метод должен быть переопределен в подклассах
        # в зависимости от используемой библиотеки (aiohttp, httpx и т.д.)
        raise NotImplementedError("Subclasses must implement _make_request")


class SyncSocialMediaAPI(ABC):
    """
    Абстрактный базовый класс для синхронных API клиентов.
    Используется для API, которые не поддерживают асинхронные операции.
    """

    def __init__(self, api_name: str):
        self.api_name = api_name
        self._session = None
        self._is_authenticated = False

    @abstractmethod
    def authenticate(self, credentials: dict[str, Any]) -> bool:
        """
        Аутентифицирует клиента в API.

        Args:
            credentials: Учетные данные для аутентификации

        Returns:
            True если аутентификация успешна
        """
        raise NotImplementedError

    @abstractmethod
    def get_user(self, user_id: str | int) -> dict[str, Any]:
        """
        Получает информацию о пользователе.

        Args:
            user_id: ID пользователя

        Returns:
            Словарь с данными пользователя
        """
        raise NotImplementedError

    @abstractmethod
    def get_posts(
        self,
        user_id: str | int,
        limit: int = 50,
        date_from: datetime | None = None,
        date_to: datetime | None = None,
    ) -> list[dict[str, Any]]:
        """
        Получает посты пользователя.

        Args:
            user_id: ID пользователя
            limit: Максимальное количество постов
            date_from: Начальная дата
            date_to: Конечная дата

        Returns:
            Список постов
        """
        raise NotImplementedError

    def close(self):
        """
        Закрывает соединение с API.
        """
        if self._session:
            self._session.close()
            self._session = None
        self._is_authenticated = False

    def check_authentication(self):
        """
        Проверяет, аутентифицирован ли клиент.

        Raises:
            APIError: Если клиент не аутентифицирован
        """
        if not self._is_authenticated:
            from core.exceptions import AuthenticationError

            raise AuthenticationError(f"{self.api_name} client is not authenticated")


class APIAdapter:
    """
    Адаптер для преобразования синхронного API в асинхронный интерфейс.
    """

    def __init__(self, sync_api: SyncSocialMediaAPI):
        self.sync_api = sync_api
        self._loop: asyncio.AbstractEventLoop | None = None

    def _get_loop(self):
        """Получает или создает event loop."""
        if self._loop is None or self._loop.is_closed():
            try:
                self._loop = asyncio.get_event_loop()
            except RuntimeError:
                self._loop = asyncio.new_event_loop()
                asyncio.set_event_loop(self._loop)
        return self._loop

    async def authenticate(self, credentials: dict[str, Any]) -> bool:
        """Асинхронная обертка для authenticate."""
        loop = self._get_loop()
        result = await loop.run_in_executor(
            None, self.sync_api.authenticate, credentials
        )
        return cast(bool, result)

    async def get_user(self, user_id: str | int) -> dict[str, Any]:
        """Асинхронная обертка для get_user."""
        loop = self._get_loop()
        result = await loop.run_in_executor(None, self.sync_api.get_user, user_id)
        return cast(dict[str, Any], result)

    async def get_posts(
        self,
        user_id: str | int,
        limit: int = 50,
        date_from: datetime | None = None,
        date_to: datetime | None = None,
    ) -> list[dict[str, Any]]:
        """Асинхронная обертка для get_posts."""
        loop = self._get_loop()
        result = await loop.run_in_executor(
            None, self.sync_api.get_posts, user_id, limit, date_from, date_to
        )
        return cast(list[dict[str, Any]], result)

    async def close(self):
        """Асинхронная обертка для close."""
        loop = self._get_loop()
        await loop.run_in_executor(None, self.sync_api.close)


def ping(request):
    return HttpResponse()
