# Generated by Django 5.2.1 on 2025-06-25 22:45

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="TaskResult",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True, db_index=True)),
                ("updated_at", models.DateTimeField(auto_now=True, db_index=True)),
                (
                    "task_id",
                    models.CharField(
                        db_index=True, default="", max_length=255, unique=True
                    ),
                ),
                ("task_type", models.CharField(max_length=50)),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("pending", "Pending"),
                            ("running", "Running"),
                            ("completed", "Completed"),
                            ("failed", "Failed"),
                            ("cancelled", "Cancelled"),
                        ],
                        default="pending",
                        max_length=20,
                    ),
                ),
                ("parameters", models.JSO<PERSON>ield(default=dict)),
                ("total_items", models.IntegerField(default=0)),
                ("processed_items", models.IntegerField(default=0)),
                ("failed_items", models.IntegerField(default=0)),
                ("started_at", models.DateTimeField(blank=True, null=True)),
                ("completed_at", models.DateTimeField(blank=True, null=True)),
                ("result", models.JSONField(blank=True, default=dict)),
                ("error_message", models.TextField(blank=True)),
                ("progress_message", models.CharField(blank=True, max_length=500)),
                ("retry_count", models.IntegerField(default=0)),
                ("max_retries", models.IntegerField(default=3)),
                ("channel_name", models.CharField(blank=True, max_length=255)),
                (
                    "priority",
                    models.IntegerField(
                        default=0, help_text="Higher number = higher priority"
                    ),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_tasks",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "parent_task",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="subtasks",
                        to="core.taskresult",
                    ),
                ),
            ],
            options={
                "db_table": "core_taskresult",
                "ordering": ["-priority", "-created_at"],
                "indexes": [
                    models.Index(
                        fields=["status", "priority", "created_at"],
                        name="core_taskre_status_f6c89f_idx",
                    ),
                    models.Index(
                        fields=["channel_name", "status"],
                        name="core_taskre_channel_89550b_idx",
                    ),
                    models.Index(
                        fields=["parent_task", "status"],
                        name="core_taskre_parent__3ffb09_idx",
                    ),
                ],
            },
        ),
    ]
