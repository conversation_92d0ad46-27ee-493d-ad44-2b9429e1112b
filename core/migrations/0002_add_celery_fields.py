# Generated by Django 5.2.1 on 2025-06-26 21:45

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("core", "0001_add_task_result_model"),
    ]

    operations = [
        migrations.AddField(
            model_name="taskresult",
            name="celery_result",
            field=models.JSO<PERSON>ield(
                blank=True, default=dict, help_text="Raw result from Celery task"
            ),
        ),
        migrations.AddField(
            model_name="taskresult",
            name="celery_status",
            field=models.CharField(
                blank=True,
                help_text="Celery task status (PENDING, STARTED, SUCCESS, FAILURE, RETRY, REVOKED)",
                max_length=50,
                null=True,
            ),
        ),
        migrations.AddField(
            model_name="taskresult",
            name="celery_task_id",
            field=models.CharField(
                blank=True,
                db_index=True,
                help_text="Celery AsyncResult ID",
                max_length=255,
                null=True,
                unique=True,
            ),
        ),
    ]
