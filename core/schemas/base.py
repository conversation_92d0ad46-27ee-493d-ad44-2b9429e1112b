"""Базовые Pydantic схемы для всего проекта."""

from datetime import datetime

from pydantic import BaseModel, ConfigDict, Field, field_validator


class BaseSchema(BaseModel):
    """Базовая схема с общей конфигурацией для всех схем проекта."""
    
    model_config = ConfigDict(
        from_attributes=True,  # Поддержка ORM моделей
        validate_assignment=True,  # Валидация при присваивании
        populate_by_name=True,  # Поддержка алиасов
        str_strip_whitespace=True,  # Удаление пробелов
        arbitrary_types_allowed=False,  # Запрет произвольных типов
        validate_default=True,  # Валидация значений по умолчанию
        extra="forbid",  # Запрет дополнительных полей
        json_schema_extra={
            "example": {}  # Для документации API
        }
    )


class TimestampedSchema(BaseSchema):
    """Схема для моделей с временными метками."""
    
    created_at: datetime | None = Field(None, description="Дата создания записи")
    updated_at: datetime | None = Field(None, description="Дата последнего обновления")
    
    @field_validator("created_at", "updated_at", mode="before")
    @classmethod
    def parse_timestamp(cls, v):
        """Универсальный парсер временных меток."""
        from core.schemas.validators import CommonValidators
        return CommonValidators.parse_timestamp(v)


class SocialMediaSchema(BaseSchema):
    """Базовая схема для всех социальных медиа платформ."""
    
    external_id: str = Field(..., description="Уникальный ID во внешней системе")
    username: str | None = Field(None, description="Имя пользователя")
    full_name: str | None = Field(None, description="Полное имя пользователя")
    
    # Временные метки (опциональные)
    created_at: datetime | None = Field(None, description="Дата создания записи")
    updated_at: datetime | None = Field(None, description="Дата последнего обновления")
    
    # Автоматически извлекаемые поля
    hashtags: list[str] = Field(default_factory=list, description="Извлеченные хештеги")
    mentions: list[str] = Field(default_factory=list, description="Извлеченные упоминания")
    urls: list[str] = Field(default_factory=list, description="Извлеченные URL")
    
    @field_validator("username")
    @classmethod
    def validate_username(cls, v):
        """Валидация и очистка имени пользователя."""
        if v:
            from core.schemas.validators import CommonValidators
            return CommonValidators.clean_username(v)
        return v
    
    @field_validator("full_name")
    @classmethod
    def validate_full_name(cls, v):
        """Валидация и очистка полного имени."""
        if v:
            from core.schemas.validators import CommonValidators
            return CommonValidators.clean_text(v, max_length=150)
        return v


class MediaSchema(BaseSchema):
    """Базовая схема для медиа файлов (фото, видео)."""
    
    media_id: str = Field(..., description="Уникальный ID медиа")
    media_type: str = Field(..., description="Тип медиа (photo, video, carousel)")
    url: str = Field(..., description="URL медиа файла")
    thumbnail_url: str | None = Field(None, description="URL миниатюры")
    
    # Метаданные
    width: int | None = Field(None, ge=0, description="Ширина в пикселях")
    height: int | None = Field(None, ge=0, description="Высота в пикселях")
    duration: int | None = Field(None, ge=0, description="Длительность в секундах")
    file_size: int | None = Field(None, ge=0, description="Размер файла в байтах")
    
    @field_validator("media_type")
    @classmethod
    def normalize_media_type(cls, v):
        """Нормализация типа медиа."""
        from core.schemas.validators import CommonValidators
        return CommonValidators.normalize_media_type(v)
    
    @field_validator("url", "thumbnail_url")
    @classmethod
    def validate_url(cls, v):
        """Валидация URL."""
        if v:
            from core.schemas.validators import CommonValidators
            if not CommonValidators.validate_url(v):
                raise ValueError(f"Invalid URL: {v}")
        return v


class ContentSchema(SocialMediaSchema):
    """Базовая схема для контента (посты, сообщения)."""
    
    content_id: str = Field(..., description="Уникальный ID контента")
    text: str | None = Field(None, description="Текстовое содержимое")
    date_posted: datetime | None = Field(None, description="Дата публикации")
    
    # Метрики взаимодействия
    like_count: int = Field(default=0, ge=0, description="Количество лайков")
    comment_count: int = Field(default=0, ge=0, description="Количество комментариев")
    share_count: int = Field(default=0, ge=0, description="Количество репостов")
    view_count: int | None = Field(None, ge=0, description="Количество просмотров")
    
    # Связанные медиа
    media: list[MediaSchema] = Field(default_factory=list, description="Прикрепленные медиа")
    
    @field_validator("date_posted", mode="before")
    @classmethod
    def parse_date_posted(cls, v):
        """Парсинг даты публикации."""
        from core.schemas.validators import CommonValidators
        return CommonValidators.parse_timestamp(v)
    
    @field_validator("text")
    @classmethod
    def clean_and_extract_text(cls, v):
        """Очистка текста и автоматическое извлечение entities."""
        if v:
            from core.schemas.validators import CommonValidators
            return CommonValidators.clean_text(v)
        return v


class CommentSchema(BaseSchema):
    """Базовая схема для комментариев."""
    
    comment_id: str = Field(..., description="Уникальный ID комментария")
    text: str = Field(..., description="Текст комментария")
    author_username: str = Field(..., description="Автор комментария")
    date_created: datetime | None = Field(None, description="Дата создания")
    
    # Иерархия комментариев
    parent_comment_id: str | None = Field(None, description="ID родительского комментария")
    replies_count: int = Field(default=0, ge=0, description="Количество ответов")
    
    # Метрики
    like_count: int = Field(default=0, ge=0, description="Количество лайков")
    
    # Флаги
    is_pinned: bool = Field(default=False, description="Закреплен ли комментарий")
    is_hidden: bool = Field(default=False, description="Скрыт ли комментарий")
    is_verified_author: bool = Field(default=False, description="Верифицирован ли автор")
    
    @field_validator("date_created", mode="before")
    @classmethod
    def parse_date_created(cls, v):
        """Парсинг даты создания."""
        from core.schemas.validators import CommonValidators
        return CommonValidators.parse_timestamp(v)
    
    @field_validator("text")
    @classmethod
    def clean_comment_text(cls, v):
        """Очистка текста комментария."""
        from core.schemas.validators import CommonValidators
        return CommonValidators.clean_text(v, max_length=2200)  # Instagram limit