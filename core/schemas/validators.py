"""Централизованные валидаторы для всех схем проекта."""

import html
import re
import unicodedata
from datetime import datetime
from typing import Any
from urllib.parse import urlparse


class CommonValidators:
    """Общие валидаторы для всех типов данных."""
    
    @staticmethod
    def parse_timestamp(v: Any) -> datetime | None:
        """Универсальный парсер для всех форматов временных меток."""
        if v is None:
            return None
            
        # Уже datetime объект
        if isinstance(v, datetime):
            return v
            
        # Unix timestamp (секунды или миллисекунды)
        if isinstance(v, (int, float)):
            # Проверяем, не миллисекунды ли это
            if v > 10000000000:  # Больше чем 2001-09-09
                return datetime.fromtimestamp(v / 1000)
            return datetime.fromtimestamp(v)
            
        # Строковые форматы
        if isinstance(v, str):
            # ISO формат с Z суффиксом
            if v.endswith("Z"):
                v = v[:-1] + "+00:00"
            
            # Стандартные форматы
            formats = [
                "%Y-%m-%dT%H:%M:%S.%fZ",
                "%Y-%m-%dT%H:%M:%SZ", 
                "%Y-%m-%dT%H:%M:%S.%f",
                "%Y-%m-%dT%H:%M:%S",
                "%Y-%m-%d %H:%M:%S",
                "%Y-%m-%d",
            ]
            
            for fmt in formats:
                try:
                    return datetime.strptime(v, fmt)
                except ValueError:
                    continue
            
            # Сложные форматы (BrightData, Telegram)
            if "GMT" in v:
                # "Sat Jun 03 2023 01:30:06 GMT+0000 (Coordinated Universal Time)"
                try:
                    from dateutil import parser
                    if "(" in v:
                        v = v[:v.index("(")].strip()
                    return parser.parse(v)
                except Exception:
                    pass
            
            # Fallback на dateutil
            try:
                from dateutil import parser
                return parser.parse(v)
            except Exception:
                pass
        
        return None
    
    @staticmethod
    def is_valid_date_format(date_str: str, format: str = "%Y-%m-%d") -> bool:
        """Проверка формата даты."""
        try:
            datetime.strptime(date_str, format)
            return True
        except ValueError:
            return False
    
    @staticmethod
    def clean_text(text: str, max_length: int | None = None, preserve_newlines: bool = False) -> str:
        """Универсальная очистка текста от лишних символов."""
        if not text:
            return ""
        
        # Декодируем HTML entities
        text = html.unescape(text)
        
        # Удаляем null bytes и управляющие символы, сохраняя переводы строк
        text = text.replace("\x00", "")
        text = "".join(char for char in text if ord(char) >= 32 or char in "\n\r\t")
        
        # Нормализация Unicode
        text = unicodedata.normalize("NFKC", text)
        
        if not preserve_newlines:
            # Заменяем множественные пробелы одним
            text = re.sub(r"\s+", " ", text)
            text = text.strip()
        else:
            # Сохраняем переводы строк, но убираем лишние пробелы в строках
            lines = text.split("\n")
            cleaned_lines = [" ".join(line.split()) for line in lines]
            text = "\n".join(cleaned_lines).strip()
        
        # Обрезка по длине
        if max_length and len(text) > max_length:
            text = text[:max_length-3] + "..."
        
        return text
    
    @staticmethod
    def extract_hashtags(text: str) -> list[str]:
        """Извлечение хештегов из текста."""
        if not text:
            return []
        
        # Паттерн для хештегов (поддержка Unicode)
        pattern = r"#([A-Za-z0-9А-Яа-я_]+)"
        hashtags = re.findall(pattern, text, re.UNICODE)
        
        # Удаляем дубликаты, сохраняя порядок, приводим к нижнему регистру
        return list(dict.fromkeys(tag.lower() for tag in hashtags))
    
    @staticmethod
    def extract_mentions(text: str) -> list[str]:
        """Извлечение упоминаний из текста."""
        if not text:
            return []
        
        # Паттерн для упоминаний
        pattern = r"@([A-Za-z0-9А-Яа-я_.]+)"
        mentions = re.findall(pattern, text, re.UNICODE)
        
        # Удаляем дубликаты, сохраняя порядок, приводим к нижнему регистру
        return list(dict.fromkeys(mention.lower() for mention in mentions))
    
    @staticmethod
    def extract_urls(text: str) -> list[str]:
        """Извлечение URL из текста."""
        if not text:
            return []
        
        # Паттерн для URL
        url_pattern = r"http[s]?://(?:[a-zA-Z]|[0-9]|[$-_@.&+]|[!*\(\),]|(?:%[0-9a-fA-F][0-9a-fA-F]))+"
        urls = re.findall(url_pattern, text)
        
        # Удаляем дубликаты
        return list(dict.fromkeys(urls))
    
    @staticmethod
    def validate_url(url: str) -> bool:
        """Валидация URL."""
        try:
            result = urlparse(url)
            return all([result.scheme, result.netloc])
        except Exception:
            return False
    
    @staticmethod
    def clean_username(username: str) -> str:
        """Очистка имени пользователя."""
        if not username:
            return ""
        
        # Удаляем @ если есть
        username = username.lstrip("@")
        
        # Очищаем и нормализуем
        username = username.strip().lower()
        
        return username
    
    @staticmethod
    def normalize_media_type(media_type: str | None) -> str:
        """Нормализация типа медиа к стандартным значениям."""
        if not media_type:
            return "photo"
        
        media_type = media_type.lower().strip()
        
        # Маппинг различных названий к стандартным типам
        type_map = {
            "image": "photo",
            "img": "photo", 
            "picture": "photo",
            "pic": "photo",
            "jpeg": "photo",
            "jpg": "photo",
            "png": "photo",
            "gif": "photo",
            "video": "video",
            "vid": "video",
            "mp4": "video",
            "mov": "video",
            "avi": "video",
            "reel": "video",
            "igtv": "video",
            "carousel": "carousel",
            "album": "carousel",
            "slideshow": "carousel",
        }
        
        return type_map.get(media_type, media_type)
    
    @staticmethod
    def safe_int_conversion(value: Any, default: int = 0) -> int:
        """Безопасное преобразование в integer."""
        if value is None:
            return default
        
        if isinstance(value, int):
            return value
        
        if isinstance(value, float):
            return int(value)
        
        if isinstance(value, str):
            # Удаляем форматирование (запятые)
            value = value.replace(",", "").strip()
            
            # Обрабатываем суффиксы K/M
            multiplier = 1
            if value.lower().endswith("k"):
                value = value[:-1]
                multiplier = 1000
            elif value.lower().endswith("m"):
                value = value[:-1] 
                multiplier = 1000000
            
            try:
                return int(float(value) * multiplier)
            except (ValueError, TypeError):
                pass
        
        return default
    
    @staticmethod
    def parse_location(location_data: Any) -> str | None:
        """Парсинг местоположения из различных форматов."""
        if not location_data:
            return None
        
        # Строковое местоположение
        if isinstance(location_data, str):
            return location_data.strip()
        
        # Объект местоположения
        if isinstance(location_data, dict):
            # Пытаемся различные поля
            for field in ["name", "title", "address", "city", "location"]:
                if location_data.get(field):
                    return str(location_data[field]).strip()
            
            # Собираем из компонентов
            parts = []
            for field in ["city", "region", "country"]:
                if location_data.get(field):
                    parts.append(str(location_data[field]).strip())
            
            if parts:
                return ", ".join(parts)
        
        # Список частей местоположения
        if isinstance(location_data, list):
            parts = [str(item).strip() for item in location_data if item]
            return ", ".join(parts) if parts else None
        
        # Fallback к строковому представлению
        return str(location_data).strip()
    
    @staticmethod
    def is_valid_instagram_username(username: str) -> bool:
        """Проверка валидности Instagram имени пользователя."""
        if not username:
            return False
        
        # Удаляем @ если есть
        username = username.lstrip("@")
        
        # Instagram правила для имен пользователей
        if not re.match(r"^[a-zA-Z0-9._]+$", username):
            return False
        
        if len(username) > 30:
            return False
        
        return True


class InstagramValidators:
    """Валидаторы специфичные для Instagram."""
    
    @staticmethod
    def validate_username(username: str) -> str:
        """Валидация Instagram имени пользователя."""
        if not username:
            raise ValueError("Username cannot be empty")
        
        # Удаляем @ если есть
        username = username.lstrip("@")
        
        # Instagram правила для имен пользователей
        if not re.match(r"^[a-zA-Z0-9._]+$", username):
            raise ValueError(
                "Username can only contain letters, numbers, periods, and underscores"
            )
        
        if len(username) > 30:
            raise ValueError("Username cannot be longer than 30 characters")
        
        return username.lower()
    
    @staticmethod
    def validate_shortcode(shortcode: str) -> str:
        """Валидация Instagram shortcode."""
        if not shortcode:
            raise ValueError("Shortcode cannot be empty")
        
        # Instagram shortcode обычно 11 символов, base64-подобная кодировка
        if not re.match(r"^[A-Za-z0-9_-]+$", shortcode):
            raise ValueError("Invalid shortcode format")
        
        return shortcode
    
    @staticmethod
    def normalize_content_type(content_type: str, is_video: bool = False, media_count: int = 1) -> str:
        """Нормализация типа Instagram контента."""
        if is_video:
            return "Video"
        elif media_count > 1:
            return "Carousel"
        else:
            return "Image"


class TelegramValidators:
    """Валидаторы специфичные для Telegram."""
    
    @staticmethod
    def validate_chat_id(chat_id: int | str) -> int:
        """Валидация Telegram chat ID."""
        if isinstance(chat_id, str):
            try:
                chat_id = int(chat_id)
            except ValueError:
                raise ValueError(f"Invalid chat ID: {chat_id}")
        
        if not isinstance(chat_id, int):
            raise ValueError("Chat ID must be an integer")
        
        return chat_id
    
    @staticmethod
    def validate_user_id(user_id: int | str) -> int:
        """Валидация Telegram user ID."""
        if isinstance(user_id, str):
            try:
                user_id = int(user_id)
            except ValueError:
                raise ValueError(f"Invalid user ID: {user_id}")
        
        if not isinstance(user_id, int):
            raise ValueError("User ID must be an integer")
        
        # Telegram user IDs обычно положительные
        if user_id <= 0:
            raise ValueError("User ID must be positive")
        
        return user_id
    
    @staticmethod
    def normalize_phone(phone: str | None) -> str | None:
        """Нормализация номера телефона."""
        if not phone:
            return None
        
        # Удаляем все нецифровые символы
        phone = re.sub(r"\D", "", str(phone))
        
        # Добавляем + если номер достаточно длинный
        if len(phone) >= 10 and not phone.startswith("+"):
            phone = "+" + phone
        
        return phone if phone else None
    
    @staticmethod
    def determine_chat_type(chat: Any) -> str:
        """Определение типа чата из Telethon entity."""
        if hasattr(chat, "__class__"):
            class_name = chat.__class__.__name__.lower()
            
            if "user" in class_name:
                return "private"
            elif "channel" in class_name:
                if getattr(chat, "broadcast", False):
                    return "channel"
                elif getattr(chat, "megagroup", False) or getattr(chat, "gigagroup", False):
                    return "supergroup"
                else:
                    return "channel"
            elif "chat" in class_name:
                return "chat"
        
        return "chat"