"""Миксины для автоматической обработки данных в схемах."""

from typing import Any

from pydantic import field_validator, model_validator


class TextCleanMixin:
    """Миксин для автоматической очистки текстовых полей."""
    
    # Поля, которые нужно автоматически очищать
    TEXT_FIELDS = ["bio", "biography", "caption", "text", "description", "title", "full_name"]
    
    @model_validator(mode="before")
    @classmethod
    def clean_text_fields(cls, values: dict[str, Any]) -> dict[str, Any]:
        """Автоматическая очистка всех текстовых полей."""
        if not isinstance(values, dict):
            return values
        
        from core.schemas.validators import CommonValidators
        
        for field in cls.TEXT_FIELDS:
            if values.get(field):
                # Определяем максимальную длину для разных полей
                max_length = None
                if field in ["bio", "biography"]:
                    max_length = 150  # Instagram bio limit
                elif field in ["caption", "text"]:
                    max_length = 2200  # Instagram caption limit
                elif field in ["title"]:
                    max_length = 100
                elif field in ["full_name"]:
                    max_length = 150
                
                values[field] = CommonValidators.clean_text(
                    values[field], 
                    max_length=max_length,
                    preserve_newlines=field in ["caption", "text", "description"]
                )
        
        return values


class EntityExtractionMixin:
    """Миксин для автоматического извлечения entities (хештеги, упоминания, URL)."""
    
    # Поля, из которых нужно извлекать entities
    EXTRACTABLE_FIELDS = ["bio", "biography", "caption", "text", "description"]
    
    @model_validator(mode="after")
    def extract_entities(self):
        """Автоматическое извлечение entities из текстовых полей."""
        from core.schemas.validators import CommonValidators
        
        # Инициализируем списки если их нет
        if not hasattr(self, "hashtags"):
            self.hashtags: list[str] = []
        if not hasattr(self, "mentions"):
            self.mentions: list[str] = []
        if not hasattr(self, "urls"):
            self.urls: list[str] = []
        
        # Извлекаем entities из всех подходящих полей
        all_text = ""
        for field in self.EXTRACTABLE_FIELDS:
            if hasattr(self, field):
                field_value = getattr(self, field)
                if field_value:
                    all_text += " " + str(field_value)
        
        if all_text:
            # Извлекаем и объединяем с существующими
            new_hashtags = CommonValidators.extract_hashtags(all_text)
            new_mentions = CommonValidators.extract_mentions(all_text)
            new_urls = CommonValidators.extract_urls(all_text)
            
            # Объединяем, удаляя дубликаты
            self.hashtags = list(dict.fromkeys(self.hashtags + new_hashtags))
            self.mentions = list(dict.fromkeys(self.mentions + new_mentions))
            self.urls = list(dict.fromkeys(self.urls + new_urls))
        
        return self


class TimestampMixin:
    """Миксин для автоматической обработки временных меток."""
    
    # Поля с временными метками
    TIMESTAMP_FIELDS = [
        "created_at", "updated_at", "date_posted", "date_created", 
        "timestamp", "date", "edit_date", "comment_date"
    ]
    
    @model_validator(mode="before")
    @classmethod
    def parse_timestamps(cls, values: dict[str, Any]) -> dict[str, Any]:
        """Автоматический парсинг всех временных меток."""
        if not isinstance(values, dict):
            return values
        
        from core.schemas.validators import CommonValidators
        
        for field in cls.TIMESTAMP_FIELDS:
            if field in values and values[field] is not None:
                parsed_timestamp = CommonValidators.parse_timestamp(values[field])
                if parsed_timestamp:
                    values[field] = parsed_timestamp
        
        return values


class CountValidationMixin:
    """Миксин для автоматической валидации счетчиков."""
    
    # Поля со счетчиками
    COUNT_FIELDS = [
        "like_count", "likes", "comment_count", "num_comments",
        "share_count", "view_count", "views", "follower_count", 
        "following_count", "post_count", "media_count",
        "replies_count", "reply_count"
    ]
    
    @model_validator(mode="before")
    @classmethod
    def validate_counts(cls, values: dict[str, Any]) -> dict[str, Any]:
        """Автоматическая валидация и нормализация счетчиков."""
        if not isinstance(values, dict):
            return values
        
        from core.schemas.validators import CommonValidators
        
        for field in cls.COUNT_FIELDS:
            if field in values:
                values[field] = CommonValidators.safe_int_conversion(values[field], default=0)
                # Убеждаемся, что счетчики не отрицательные
                if values[field] < 0:
                    values[field] = 0
        
        return values


class MediaNormalizationMixin:
    """Миксин для автоматической нормализации медиа данных."""
    
    @model_validator(mode="before")
    @classmethod
    def normalize_media_fields(cls, values: dict[str, Any]) -> dict[str, Any]:
        """Автоматическая нормализация медиа полей."""
        if not isinstance(values, dict):
            return values
        
        from core.schemas.validators import CommonValidators
        
        # Нормализация типа медиа
        if "type" in values:
            values["type"] = CommonValidators.normalize_media_type(values["type"])
        if "media_type" in values:
            values["media_type"] = CommonValidators.normalize_media_type(values["media_type"])
        if "content_type" in values:
            values["content_type"] = CommonValidators.normalize_media_type(values["content_type"])
        
        # Нормализация размеров
        for field in ["width", "height", "duration", "file_size"]:
            if field in values:
                values[field] = CommonValidators.safe_int_conversion(values[field])
        
        return values


class LocationMixin:
    """Миксин для автоматической обработки местоположения."""
    
    @field_validator("location", mode="before")
    @classmethod
    def parse_location(cls, v):
        """Автоматический парсинг местоположения."""
        from core.schemas.validators import CommonValidators
        return CommonValidators.parse_location(v)


class UsernameNormalizationMixin:
    """Миксин для автоматической нормализации имен пользователей."""
    
    @field_validator("username", "user_posted", "owner_username", "author_username", mode="before")
    @classmethod
    def normalize_username(cls, v):
        """Автоматическая нормализация имени пользователя."""
        if v:
            from core.schemas.validators import CommonValidators
            return CommonValidators.clean_username(v)
        return v


class InstagramMixin(
    TextCleanMixin, 
    EntityExtractionMixin, 
    TimestampMixin, 
    CountValidationMixin,
    MediaNormalizationMixin,
    LocationMixin,
    UsernameNormalizationMixin
):
    """Комбинированный миксин для Instagram схем."""
    
    @field_validator("username", mode="before")
    @classmethod
    def validate_instagram_username(cls, v):
        """Валидация Instagram имени пользователя."""
        if v:
            from core.schemas.validators import InstagramValidators
            return InstagramValidators.validate_username(v)
        return v
    
    @field_validator("shortcode", mode="before")
    @classmethod
    def validate_instagram_shortcode(cls, v):
        """Валидация Instagram shortcode."""
        if v:
            from core.schemas.validators import InstagramValidators
            return InstagramValidators.validate_shortcode(v)
        return v


class TelegramMixin(
    TextCleanMixin,
    EntityExtractionMixin, 
    TimestampMixin,
    CountValidationMixin,
    UsernameNormalizationMixin
):
    """Комбинированный миксин для Telegram схем."""
    
    @field_validator("user_id", "from_id", mode="before")
    @classmethod
    def validate_telegram_user_id(cls, v):
        """Валидация Telegram user ID."""
        if v is not None:
            from core.schemas.validators import TelegramValidators
            return TelegramValidators.validate_user_id(v)
        return v
    
    @field_validator("chat_id", mode="before")  
    @classmethod
    def validate_telegram_chat_id(cls, v):
        """Валидация Telegram chat ID."""
        if v is not None:
            from core.schemas.validators import TelegramValidators
            return TelegramValidators.validate_chat_id(v)
        return v
    
    @field_validator("phone", mode="before")
    @classmethod
    def normalize_telegram_phone(cls, v):
        """Нормализация номера телефона."""
        if v:
            from core.schemas.validators import TelegramValidators
            return TelegramValidators.normalize_phone(v)
        return v


class ValidationHelperMixin:
    """Миксин с полезными методами для валидации."""
    
    @classmethod
    def validate_list(cls, data: list[dict[str, Any]], skip_errors: bool = True) -> list["ValidationHelperMixin"]:
        """Валидация списка объектов, с возможностью пропуска ошибок."""
        from pydantic import ValidationError
        
        validated = []
        errors = []
        
        for idx, item in enumerate(data):
            try:
                validated.append(cls(**item))
            except ValidationError as e:
                if not skip_errors:
                    raise
                errors.append({
                    "index": idx,
                    "data": item,
                    "error": str(e)
                })
        
        if errors:
            # Логируем ошибки
            import logging
            logger = logging.getLogger(__name__)
            logger.warning(
                f"Validation errors for {len(errors)} out of {len(data)} items in {cls.__name__}"
            )
        
        return validated
    
    def to_dict(self, exclude_none: bool = True) -> dict[str, Any]:
        """Преобразование в словарь с опциональным исключением None значений."""
        data: dict[str, Any] = self.model_dump()
        if exclude_none:
            return {k: v for k, v in data.items() if v is not None}
        return data