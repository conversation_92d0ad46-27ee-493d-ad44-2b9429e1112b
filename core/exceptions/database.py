"""
Исключения для работы с базой данных
"""


from .base import BaseServiceException


class DatabaseError(BaseServiceException):
    """Базовая ошибка работы с базой данных."""
    
    def __init__(self, message: str, **kwargs):
        """
        Инициализация ошибки БД.
        
        Args:
            message: Сообщение об ошибке
            **kwargs: Дополнительные параметры для BaseServiceException
        """
        super().__init__(message, code="DATABASE_ERROR", **kwargs)


class IntegrityError(DatabaseError):
    """Нарушение целостности данных."""
    
    def __init__(
        self, 
        constraint: str,
        model: str | None = None,
        **kwargs
    ):
        """
        Инициализация ошибки целостности.
        
        Args:
            constraint: Название ограничения
            model: Название модели
            **kwargs: Дополнительные параметры для DatabaseError
        """
        details = kwargs.get("details", {})
        details.update({
            "constraint": constraint,
            "model": model,
        })
        kwargs["details"] = details
        
        if model:
            message = f"Integrity constraint violated on {model}: {constraint}"
        else:
            message = f"Integrity constraint violated: {constraint}"
            
        super().__init__(message, **kwargs)
        self.code = "INTEGRITY_ERROR"


class TransactionError(DatabaseError):
    """Ошибка транзакции."""
    
    def __init__(self, operation: str, **kwargs):
        """
        Инициализация ошибки транзакции.
        
        Args:
            operation: Операция, которая вызвала ошибку
            **kwargs: Дополнительные параметры для DatabaseError
        """
        details = kwargs.get("details", {})
        details["operation"] = operation
        kwargs["details"] = details
        
        message = f"Transaction failed during {operation}"
        super().__init__(message, **kwargs)
        self.code = "TRANSACTION_ERROR"


class ConnectionError(DatabaseError):
    """Ошибка соединения с БД."""
    
    def __init__(self, database: str | None = None, **kwargs):
        """
        Инициализация ошибки соединения.
        
        Args:
            database: Название базы данных
            **kwargs: Дополнительные параметры для DatabaseError
        """
        if database:
            details = kwargs.get("details", {})
            details["database"] = database
            kwargs["details"] = details
            message = f"Failed to connect to database: {database}"
        else:
            message = "Failed to connect to database"
            
        super().__init__(message, **kwargs)
        self.code = "CONNECTION_ERROR"


class QueryError(DatabaseError):
    """Ошибка выполнения запроса."""
    
    def __init__(
        self, 
        query_type: str,
        model: str | None = None,
        **kwargs
    ):
        """
        Инициализация ошибки запроса.
        
        Args:
            query_type: Тип запроса (SELECT, INSERT, UPDATE, DELETE)
            model: Модель, к которой выполнялся запрос
            **kwargs: Дополнительные параметры для DatabaseError
        """
        details = kwargs.get("details", {})
        details.update({
            "query_type": query_type,
            "model": model,
        })
        kwargs["details"] = details
        
        if model:
            message = f"Query error during {query_type} on {model}"
        else:
            message = f"Query error during {query_type}"
            
        super().__init__(message, **kwargs)
        self.code = "QUERY_ERROR"