"""
Исключения для работы с внешними API
"""


from .base import BaseServiceException


class ExternalAPIError(BaseServiceException):
    """Базовая ошибка внешнего API."""
    
    def __init__(
        self, 
        api_name: str, 
        status_code: int | None = None, 
        response: str | None = None,
        **kwargs
    ):
        """
        Инициализация ошибки API.
        
        Args:
            api_name: Название API
            status_code: HTTP статус код
            response: Текст ответа
            **kwargs: Дополнительные параметры для BaseServiceException
        """
        details = kwargs.get("details", {})
        details.update({
            "api": api_name,
            "status_code": status_code,
            "response": response,
        })
        kwargs["details"] = details
        
        if status_code:
            message = f"{api_name} API error (status {status_code})"
        else:
            message = f"{api_name} API error"
            
        super().__init__(message, code="EXTERNAL_API_ERROR", **kwargs)


class RateLimitError(ExternalAPIError):
    """Превышен лимит запросов к API."""
    
    def __init__(
        self, 
        api_name: str, 
        retry_after: int | None = None,
        **kwargs
    ):
        """
        Инициализация ошибки лимита запросов.
        
        Args:
            api_name: Название API
            retry_after: Через сколько секунд можно повторить
            **kwargs: Дополнительные параметры для ExternalAPIError
        """
        self.retry_after = retry_after
        
        if retry_after:
            details = kwargs.get("details", {})
            details["retry_after"] = retry_after
            kwargs["details"] = details
        
        super().__init__(
            api_name=api_name,
            status_code=429,
            **kwargs
        )
        
        # Переопределяем код для специфичности
        self.code = "RATE_LIMIT_ERROR"


class APIAuthenticationError(ExternalAPIError):
    """Ошибка аутентификации в API."""
    
    def __init__(self, api_name: str, **kwargs):
        """
        Инициализация ошибки аутентификации.
        
        Args:
            api_name: Название API
            **kwargs: Дополнительные параметры для ExternalAPIError
        """
        super().__init__(
            api_name=api_name,
            status_code=401,
            **kwargs
        )
        self.code = "API_AUTHENTICATION_ERROR"


class APITimeoutError(ExternalAPIError):
    """Таймаут при обращении к API."""
    
    def __init__(
        self, 
        api_name: str, 
        timeout_seconds: float | None = None,
        **kwargs
    ):
        """
        Инициализация ошибки таймаута.
        
        Args:
            api_name: Название API
            timeout_seconds: Таймаут в секундах
            **kwargs: Дополнительные параметры для ExternalAPIError
        """
        if timeout_seconds:
            details = kwargs.get("details", {})
            details["timeout_seconds"] = timeout_seconds
            kwargs["details"] = details
        
        super().__init__(
            api_name=api_name,
            **kwargs
        )
        self.code = "API_TIMEOUT_ERROR"


class NetworkError(ExternalAPIError):
    """Сетевая ошибка при обращении к API."""
    
    def __init__(
        self, 
        api_name: str,
        error_message: str | None = None,
        **kwargs
    ):
        """
        Инициализация сетевой ошибки.
        
        Args:
            api_name: Название API
            error_message: Сообщение об ошибке
            **kwargs: Дополнительные параметры для ExternalAPIError
        """
        if error_message:
            details = kwargs.get("details", {})
            details["error_message"] = error_message
            kwargs["details"] = details
        
        super().__init__(
            api_name=api_name,
            response=error_message,
            **kwargs
        )
        self.code = "NETWORK_ERROR"


class APIResponseError(ExternalAPIError):
    """Некорректный ответ от API."""
    
    def __init__(
        self, 
        api_name: str,
        reason: str,
        **kwargs
    ):
        """
        Инициализация ошибки ответа API.
        
        Args:
            api_name: Название API
            reason: Причина ошибки
            **kwargs: Дополнительные параметры для ExternalAPIError
        """
        details = kwargs.get("details", {})
        details["reason"] = reason
        kwargs["details"] = details
        
        super().__init__(
            api_name=api_name,
            **kwargs
        )
        self.code = "API_RESPONSE_ERROR"