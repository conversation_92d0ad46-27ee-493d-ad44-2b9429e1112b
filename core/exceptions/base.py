"""
Базовые исключения с автоматическим логированием
"""

from typing import Any

from core.logging import ContextLogger


class BaseServiceException(Exception):
    """
    Базовое исключение для всех сервисов.
    
    Автоматически логирует ошибку при создании.
    """
    
    def __init__(
        self, 
        message: str, 
        code: str | None = None, 
        details: dict[str, Any] | None = None
    ):
        """
        Инициализация исключения.
        
        Args:
            message: Сообщение об ошибке
            code: Код ошибки для API
            details: Дополнительные детали ошибки
        """
        super().__init__(message)
        self.message = message
        self.code = code or self.__class__.__name__.upper().replace("ERROR", "")
        self.details = details or {}
        
        # Автоматическое логирование
        logger = ContextLogger(__name__)
        logger.error(
            f"{self.__class__.__name__}: {message}",
            error_code=self.code,
            error_details=self.details,
        )
    
    def to_dict(self) -> dict[str, Any]:
        """
        Преобразование исключения в словарь для API ответов.
        
        Returns:
            Словарь с информацией об ошибке
        """
        return {
            "error": {
                "message": self.message,
                "code": self.code,
                "details": self.details,
            }
        }


class ValidationError(BaseServiceException):
    """Ошибка валидации данных."""
    
    def __init__(
        self, 
        field: str, 
        value: Any, 
        reason: str,
        **kwargs
    ):
        """
        Инициализация ошибки валидации.
        
        Args:
            field: Поле с ошибкой
            value: Некорректное значение
            reason: Причина ошибки
            **kwargs: Дополнительные параметры для BaseServiceException
        """
        details = kwargs.get("details", {})
        details.update({
            "field": field,
            "value": value,
            "reason": reason,
        })
        kwargs["details"] = details
        
        message = f"Validation failed for {field}: {reason}"
        super().__init__(message, code="VALIDATION_ERROR", **kwargs)


class NotFoundError(BaseServiceException):
    """Ресурс не найден."""
    
    def __init__(
        self, 
        entity: str, 
        identifier: Any,
        **kwargs
    ):
        """
        Инициализация ошибки "не найдено".
        
        Args:
            entity: Тип сущности
            identifier: Идентификатор сущности
            **kwargs: Дополнительные параметры для BaseServiceException
        """
        details = kwargs.get("details", {})
        details.update({
            "entity": entity,
            "identifier": identifier,
        })
        kwargs["details"] = details
        
        message = f"{entity} not found: {identifier}"
        super().__init__(message, code="NOT_FOUND", **kwargs)


class AlreadyExistsError(BaseServiceException):
    """Ресурс уже существует."""
    
    def __init__(
        self, 
        entity: str, 
        identifier: Any,
        **kwargs
    ):
        """
        Инициализация ошибки "уже существует".
        
        Args:
            entity: Тип сущности
            identifier: Идентификатор сущности
            **kwargs: Дополнительные параметры для BaseServiceException
        """
        details = kwargs.get("details", {})
        details.update({
            "entity": entity,
            "identifier": identifier,
        })
        kwargs["details"] = details
        
        message = f"{entity} already exists: {identifier}"
        super().__init__(message, code="ALREADY_EXISTS", **kwargs)


class PermissionDeniedError(BaseServiceException):
    """Недостаточно прав для выполнения операции."""
    
    def __init__(
        self, 
        action: str,
        resource: str | None = None,
        **kwargs
    ):
        """
        Инициализация ошибки доступа.
        
        Args:
            action: Действие, которое пытались выполнить
            resource: Ресурс, к которому нет доступа
            **kwargs: Дополнительные параметры для BaseServiceException
        """
        details = kwargs.get("details", {})
        details.update({
            "action": action,
            "resource": resource,
        })
        kwargs["details"] = details
        
        if resource:
            message = f"Permission denied for {action} on {resource}"
        else:
            message = f"Permission denied for {action}"
        
        super().__init__(message, code="PERMISSION_DENIED", **kwargs)


class BusinessLogicError(BaseServiceException):
    """Ошибка бизнес-логики."""
    
    def __init__(self, message: str, **kwargs):
        """
        Инициализация ошибки бизнес-логики.
        
        Args:
            message: Описание ошибки
            **kwargs: Дополнительные параметры для BaseServiceException
        """
        super().__init__(message, code="BUSINESS_LOGIC_ERROR", **kwargs)