"""
Тесты для декораторов
"""

import warnings
from unittest import TestCase

from django.contrib.auth import get_user_model
from django.test import TestCase as DjangoTestCase
from pydantic import BaseModel

from core.exceptions import ValidationError as ServiceValidationError
from core.utils.decorators import deprecated, require_permission, validate_input

User = get_user_model()


class SampleSchema(BaseModel):
    name: str
    age: int
    email: str = None


class DeprecatedTests(TestCase):
    """Тесты для декоратора deprecated"""
    
    def test_warns_on_call(self):
        """Тест вывода предупреждения при вызове"""
        
        @deprecated(
            reason="Используйте new_function",
            version="2.0",
            alternative="new_function"
        )
        def old_function():
            return "old"
        
        with warnings.catch_warnings(record=True) as w:
            warnings.simplefilter("always")
            result = old_function()
            
            self.assertEqual(result, "old")
            # Filter out non-deprecation warnings
            deprecation_warnings = [warning for warning in w if issubclass(warning.category, DeprecationWarning)]
            self.assertEqual(len(deprecation_warnings), 1)
            self.assertIn("is deprecated", str(deprecation_warnings[0].message))
            self.assertIn("new_function", str(deprecation_warnings[0].message))
            
    def test_preserves_function_metadata(self):
        """Тест сохранения метаданных функции"""
        
        @deprecated(reason="Test reason")
        def test_function():
            """Test docstring"""
            return 42
        
        self.assertEqual(test_function.__name__, "test_function")
        # The decorator prepends deprecation message to docstring
        self.assertIn("Test docstring", test_function.__doc__)
        self.assertIn("**DEPRECATED**", test_function.__doc__)
        
    def test_works_with_methods(self):
        """Тест работы с методами класса"""
        
        class TestClass:
            @deprecated(reason="Use new_method")
            def old_method(self):
                return "old"
        
        obj = TestClass()
        
        with warnings.catch_warnings(record=True) as w:
            warnings.simplefilter("always")
            result = obj.old_method()
            
            self.assertEqual(result, "old")
            deprecation_warnings = [warning for warning in w if issubclass(warning.category, DeprecationWarning)]
            self.assertEqual(len(deprecation_warnings), 1)


class ValidateInputTests(TestCase):
    """Тесты для декоратора validate_input"""
    
    def test_validates_input_successfully(self):
        """Тест успешной валидации входных данных"""
        
        @validate_input(SampleSchema)
        def process_data(**kwargs):
            return f"Processing {kwargs['name']}"
        
        result = process_data(name="John", age=30)
        self.assertEqual(result, "Processing John")
        
    def test_raises_validation_error(self):
        """Тест ошибки валидации"""
        
        @validate_input(SampleSchema)
        def process_data(**kwargs):
            return f"Processing {kwargs['name']}"
        
        with self.assertRaises(ServiceValidationError) as cm:
            process_data(name="John")  # age is missing
        
        self.assertIn("age", str(cm.exception))
        
    def test_works_with_first_arg(self):
        """Тест работы с первым аргументом"""
        
        @validate_input(SampleSchema, source="first_arg")
        def process_data(data):
            # data is now a validated Pydantic model
            return f"Processing {data.name}"
        
        result = process_data({"name": "Jane", "age": 25})
        self.assertEqual(result, "Processing Jane")


class RequirePermissionTests(DjangoTestCase):
    """Тесты для декоратора require_permission"""
    
    def setUp(self):
        self.user = User.objects.create_user(
            username="testuser",
            password="testpass"
        )
        
    def test_allows_with_permission(self):
        """Тест доступа с разрешением"""
        from django.contrib.auth.models import Permission
        from django.contrib.contenttypes.models import ContentType
        
        # Создаем и добавляем разрешение
        content_type = ContentType.objects.get_for_model(User)
        permission = Permission.objects.create(
            codename="test_permission",
            name="Test Permission",
            content_type=content_type
        )
        self.user.user_permissions.add(permission)
        
        @require_permission("auth.test_permission")
        def protected_function(user):
            return "allowed"
        
        result = protected_function(self.user)
        self.assertEqual(result, "allowed")
        
    def test_denies_without_permission(self):
        """Тест отказа в доступе без разрешения"""
        
        @require_permission("auth.nonexistent_permission")
        def protected_function(user):
            return "allowed"
        
        from django.core.exceptions import PermissionDenied
        
        with self.assertRaises(PermissionDenied):
            protected_function(self.user)