from unittest.mock import Mock, patch

import pytest
from django.db import models
from django.test import TestCase

from core.exceptions import DataValidationError
from core.models import TimestampedModel
from core.repositories.base import BaseRepository
from core.services.base import BaseService


# Тестовая модель
class SampleServiceModel(TimestampedModel):
    name = models.CharField(max_length=100)
    value = models.IntegerField(default=0)
    is_active = models.BooleanField(default=True)

    class Meta:
        app_label = "core"


# Тестовый репозиторий
class SampleServiceRepository(BaseRepository[SampleServiceModel]):
    @property
    def model(self):
        return SampleServiceModel


# Тестовый сервис
class SampleService(BaseService[SampleServiceModel, SampleServiceRepository]):
    @property
    def entity_name(self):
        return "TestEntity"

    def validate_create_data(self, data):
        if "name" not in data:
            raise DataValidationError("Name is required")
        if len(data["name"]) < 3:
            raise DataValidationError("Name must be at least 3 characters long")

    def validate_update_data(self, entity, data):
        if "value" in data and data["value"] < 0:
            raise DataValidationError("Value cannot be negative")

    def process_create_data(self, data):
        # Добавляем префикс к имени
        data["name"] = f"CREATED_{data['name']}"
        return data


class TestBaseService(TestCase):
    """Тесты для BaseService."""

    def setUp(self):
        self.repository = Mock(spec=SampleServiceRepository)
        self.service = SampleService(self.repository)

    def test_create_success(self):
        """Тест успешного создания сущности."""
        # Подготовка
        test_data = {"name": "Test", "value": 42}
        created_entity = Mock(pk=1, name="CREATED_Test", value=42)
        self.repository.create.return_value = created_entity

        # Выполнение
        result = self.service.create(**test_data)

        # Проверка
        assert result == created_entity
        self.repository.create.assert_called_once_with(name="CREATED_Test", value=42)

    def test_create_validation_error(self):
        """Тест ошибки валидации при создании."""
        # Данные с коротким именем
        test_data = {"name": "AB", "value": 42}

        # Проверка исключения
        with pytest.raises(DataValidationError) as exc_info:
            self.service.create(**test_data)

        assert "Name must be at least 3 characters long" in str(exc_info.value)
        self.repository.create.assert_not_called()

    def test_update_success(self):
        """Тест успешного обновления сущности."""
        # Подготовка
        entity_id = 1
        update_data = {"value": 100}
        existing_entity = Mock(pk=entity_id, name="Test", value=42)
        updated_entity = Mock(pk=entity_id, name="Test", value=100)

        self.repository.get_by_id.return_value = existing_entity
        self.repository.update.return_value = updated_entity

        # Выполнение
        result = self.service.update(entity_id, **update_data)

        # Проверка
        assert result == updated_entity
        self.repository.get_by_id.assert_called_once_with(entity_id)
        self.repository.update.assert_called_once_with(entity_id, value=100)

    def test_update_validation_error(self):
        """Тест ошибки валидации при обновлении."""
        # Подготовка
        entity_id = 1
        update_data = {"value": -10}  # Отрицательное значение
        existing_entity = Mock(pk=entity_id, name="Test", value=42)

        self.repository.get_by_id.return_value = existing_entity

        # Проверка исключения
        with pytest.raises(DataValidationError) as exc_info:
            self.service.update(entity_id, **update_data)

        assert "Value cannot be negative" in str(exc_info.value)
        self.repository.update.assert_not_called()

    def test_get_by_id(self):
        """Тест получения сущности по ID."""
        # Подготовка
        entity_id = 1
        entity = Mock(pk=entity_id, name="Test")
        self.repository.get_by_id.return_value = entity

        # Выполнение
        result = self.service.get_by_id(entity_id)

        # Проверка
        assert result == entity
        self.repository.get_by_id.assert_called_once_with(entity_id)

    def test_get_list(self):
        """Тест получения списка с пагинацией."""
        # Подготовка
        filters = {"is_active": True}
        page_result = {"items": [Mock(), Mock()], "total": 2, "page": 1, "per_page": 20}

        queryset = Mock()
        self.repository.filter.return_value = queryset
        self.repository.paginate.return_value = page_result

        # Выполнение
        result = self.service.get_list(filters=filters, page=1, per_page=20)

        # Проверка
        assert result == page_result
        self.repository.filter.assert_called_once_with(is_active=True)
        self.repository.paginate.assert_called_once_with(queryset, 1, 20)

    def test_delete_success(self):
        """Тест успешного удаления сущности."""
        # Подготовка
        entity_id = 1
        entity = Mock(pk=entity_id, name="ToDelete")

        self.repository.get_by_id.return_value = entity
        self.repository.delete.return_value = True

        # Выполнение
        result = self.service.delete(entity_id)

        # Проверка
        assert result is True
        self.repository.get_by_id.assert_called_once_with(entity_id)
        self.repository.delete.assert_called_once_with(entity_id)

    def test_bulk_create(self):
        """Тест массового создания сущностей."""
        # Подготовка
        items = [{"name": "Item1", "value": 10}, {"name": "Item2", "value": 20}]

        created_entities = [Mock(), Mock()]
        self.repository.model = SampleServiceModel
        self.repository.bulk_create.return_value = created_entities

        # Выполнение
        with patch.object(SampleServiceModel, "__init__", return_value=None):
            result = self.service.bulk_create(items)

        # Проверка
        assert result == created_entities
        self.repository.bulk_create.assert_called_once()

    def test_search(self):
        """Тест поиска сущностей."""
        # Подготовка
        query = "test"
        search_fields = ["name", "description"]
        search_results = [Mock(), Mock()]

        self.repository.search.return_value = search_results

        # Выполнение
        result = self.service.search(query, search_fields)

        # Проверка
        assert result == search_results
        self.repository.search.assert_called_once_with(query, search_fields)


