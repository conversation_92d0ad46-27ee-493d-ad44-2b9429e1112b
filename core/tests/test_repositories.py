
import pytest
from django.contrib.auth import get_user_model
from django.db import connection, models
from django.test import TransactionTestCase
from django.test.utils import override_settings

from core.exceptions import NotFoundError
from core.models import TimestampedModel
from core.repositories.base import BaseRepository

User = get_user_model()


# Тестовая модель для репозитория
class SampleModel(TimestampedModel):
    name = models.CharField(max_length=100)
    value = models.IntegerField(default=0)
    
    class Meta:
        app_label = "core"
        db_table = "core_samplemodel"


# Тестовый репозиторий
class SampleRepository(BaseRepository[SampleModel]):
    @property
    def model(self):
        return SampleModel


@override_settings(MIGRATION_MODULES={"core": None})
class TestBaseRepository(TransactionTestCase):
    """Тесты для BaseRepository."""
    
    def setUp(self):
        # Создаём таблицу для тестовой модели
        with connection.schema_editor() as schema_editor:
            try:
                schema_editor.delete_model(SampleModel)
            except Exception:
                pass
            schema_editor.create_model(SampleModel)
        
        self.repository = SampleRepository()
    
    def tearDown(self):
        # Удаляем таблицу после тестов
        with connection.schema_editor() as schema_editor:
            try:
                schema_editor.delete_model(SampleModel)
            except Exception:
                pass
    
    def test_get_by_id_success(self):
        """Тест успешного получения объекта по ID."""
        # Создаем тестовый объект
        obj = SampleModel.objects.create(name="Test", value=42)
        
        # Получаем через репозиторий
        result = self.repository.get_by_id(obj.id)
        
        assert result.id == obj.id
        assert result.name == "Test"
        assert result.value == 42
    
    def test_get_by_id_not_found(self):
        """Тест получения несуществующего объекта."""
        with pytest.raises(NotFoundError) as exc_info:
            self.repository.get_by_id(999)
        
        assert "SampleModel not found: 999" in str(exc_info.value)
        assert exc_info.value.details["entity"] == "SampleModel"
        assert exc_info.value.details["identifier"] == 999
    
    def test_get_or_none(self):
        """Тест получения объекта или None."""
        # Создаем объект
        obj = SampleModel.objects.create(name="Test")
        
        # Существующий объект
        result = self.repository.get_or_none(name="Test")
        assert result is not None
        assert result.id == obj.id
        
        # Несуществующий объект
        result = self.repository.get_or_none(name="NonExistent")
        assert result is None
    
    def test_filter(self):
        """Тест фильтрации объектов."""
        # Создаем несколько объектов
        SampleModel.objects.create(name="Test1", value=10)
        SampleModel.objects.create(name="Test2", value=20)
        SampleModel.objects.create(name="Test3", value=10)
        
        # Фильтруем
        results = self.repository.filter(value=10)
        
        assert results.count() == 2
        assert all(obj.value == 10 for obj in results)
    
    def test_create(self):
        """Тест создания объекта."""
        obj = self.repository.create(name="New", value=100)
        
        assert obj.id is not None
        assert obj.name == "New"
        assert obj.value == 100
        assert SampleModel.objects.filter(id=obj.id).exists()
    
    def test_update(self):
        """Тест обновления объекта."""
        # Создаем объект
        obj = SampleModel.objects.create(name="Old", value=50)
        
        # Обновляем
        updated = self.repository.update(obj.id, name="New", value=100)
        
        assert updated.id == obj.id
        assert updated.name == "New"
        assert updated.value == 100
    
    def test_delete(self):
        """Тест удаления объекта."""
        # Создаем объект
        obj = SampleModel.objects.create(name="ToDelete")
        obj_id = obj.id
        
        # Удаляем
        result = self.repository.delete(obj_id)
        
        assert result is True
        assert not SampleModel.objects.filter(id=obj_id).exists()
    
    def test_bulk_create(self):
        """Тест массового создания объектов."""
        objects = [
            SampleModel(name=f"Bulk{i}", value=i*10)
            for i in range(5)
        ]
        
        created = self.repository.bulk_create(objects)
        
        assert len(created) == 5
        assert SampleModel.objects.count() == 5
    
    def test_get_or_create(self):
        """Тест получения или создания объекта."""
        # Первый вызов - создание
        obj1, created1 = self.repository.get_or_create(
            name="Unique",
            defaults={"value": 42}
        )
        
        assert created1 is True
        assert obj1.name == "Unique"
        assert obj1.value == 42
        
        # Второй вызов - получение существующего
        obj2, created2 = self.repository.get_or_create(
            name="Unique",
            defaults={"value": 100}
        )
        
        assert created2 is False
        assert obj2.id == obj1.id
        assert obj2.value == 42  # Значение не изменилось
    
    def test_paginate(self):
        """Тест пагинации."""
        # Создаем 25 объектов
        for i in range(25):
            SampleModel.objects.create(name=f"Page{i}", value=i)
        
        # Первая страница
        page1 = self.repository.paginate(page=1, per_page=10)
        
        assert len(page1["items"]) == 10
        assert page1["total"] == 25
        assert page1["page"] == 1
        assert page1["total_pages"] == 3
        assert page1["has_next"] is True
        assert page1["has_previous"] is False
        
        # Последняя страница
        page3 = self.repository.paginate(page=3, per_page=10)
        
        assert len(page3["items"]) == 5
        assert page3["has_next"] is False
        assert page3["has_previous"] is True
    
    def test_search(self):
        """Тест поиска по текстовым полям."""
        # Создаем объекты
        SampleModel.objects.create(name="Python Developer")
        SampleModel.objects.create(name="Java Developer")
        SampleModel.objects.create(name="Python Engineer")
        
        # Поиск
        results = self.repository.search("Python", ["name"])
        
        assert results.count() == 2
        assert all("Python" in obj.name for obj in results)


