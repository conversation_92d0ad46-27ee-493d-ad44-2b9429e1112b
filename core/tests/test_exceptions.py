from core.exceptions import (
    AuthenticationError,
    DataValidationError,
    NotFoundError,
    RateLimitError,
    RetryableError,
    SocialMediaError,
)


class TestExceptions:
    """Тесты для кастомных исключений."""
    
    def test_social_media_error(self):
        """Тест базового исключения SocialMediaError."""
        error = SocialMediaError("Test error", code="TEST_CODE", details={"key": "value"})
        
        assert str(error) == "Test error"  # BaseServiceException doesn't override __str__
        assert error.message == "Test error"
        assert error.code == "TEST_CODE"
        assert error.details == {"key": "value"}
    
    def test_social_media_error_without_code(self):
        """Тест SocialMediaError без кода."""
        error = SocialMediaError("Test error")
        
        assert str(error) == "Test error"
        assert error.code == "SOCIALMEDIA"  # Auto-generated from class name
        assert error.details == {}
    
    def test_authentication_error(self):
        """Тест AuthenticationError."""
        error = AuthenticationError()
        
        assert error.message == "Authentication failed"
        assert error.code == "API_AUTHENTICATION_ERROR"  # Code set by APIAuthenticationError
    
    def test_rate_limit_error(self):
        """Тест RateLimitError с retry_after."""
        error = RateLimitError(api_name="TestAPI", retry_after=60)
        
        assert "TestAPI API error" in str(error)
        assert error.code == "RATE_LIMIT_ERROR"  # Code set by RateLimitError
        assert error.details["retry_after"] == 60
    
    def test_data_validation_error(self):
        """Тест DataValidationError с полем."""
        error = DataValidationError("Invalid email", field="email")
        
        assert error.message == "Validation failed for email: Invalid email"
        assert error.code == "VALIDATION_ERROR"
        assert error.details["field"] == "email"
    
    def test_not_found_error(self):
        """Тест NotFoundError с деталями ресурса."""
        error = NotFoundError(
            entity="User",
            identifier="123"
        )
        
        assert error.message == "User not found: 123"
        assert error.code == "NOT_FOUND"
        assert error.details["entity"] == "User"
        assert error.details["identifier"] == "123"
    
    def test_retryable_error(self):
        """Тест RetryableError с счетчиком попыток."""
        error = RetryableError("Temporary failure", retry_count=2, max_retries=5)
        
        assert error.details["retry_count"] == 2
        assert error.details["max_retries"] == 5
        assert error.details["can_retry"] is True
    
    def test_retryable_error_max_retries_reached(self):
        """Тест RetryableError когда достигнут лимит попыток."""
        error = RetryableError("Temporary failure", retry_count=3, max_retries=3)
        
        assert error.details["can_retry"] is False