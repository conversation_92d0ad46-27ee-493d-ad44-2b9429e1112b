"""
URL patterns for core app task monitoring.
"""

from django.urls import path

from core.views import (
    AsyncTaskProgressView,
    TaskActionView,
    TaskDetailView,
    TaskListView,
    TaskProgressView,
    TaskQueueView,
    TaskStatsAPIView,
    health_check_async,
)

app_name = "core"

urlpatterns = [
    # Task monitoring views
    path("tasks/", TaskListView.as_view(), name="task-list"),
    path("tasks/queue/", TaskQueueView.as_view(), name="task-queue"),
    path("tasks/<str:task_id>/", TaskDetailView.as_view(), name="task-detail"),
    
    # API endpoints
    path("api/tasks/<str:task_id>/progress/", TaskProgressView.as_view(), name="task-progress"),
    path("api/tasks/<str:task_id>/action/", TaskActionView.as_view(), name="task-action"),
    path("api/tasks/stats/", TaskStatsAPIView.as_view(), name="task-stats"),
    
    # Async API endpoints (для ASGI)
    path("api/async/tasks/<str:task_id>/progress/", AsyncTaskProgressView.as_view(), name="async-task-progress"),
    path("api/async/health/", health_check_async, name="async-health-check"),
]