"""
Google Cloud Storage service for uploading media files.
MVP implementation with basic validation and error handling.
"""
import logging
import os
import uuid
from datetime import datetime

from django.conf import settings
from google.cloud import storage
from google.cloud.exceptions import GoogleCloudError

logger = logging.getLogger(__name__)


class GCSService:
    """
    Service for uploading files to Google Cloud Storage.
    MVP implementation with basic validation and error handling.
    """
    
    # MVP: Simple validation constants
    ALLOWED_CONTENT_TYPES = {
        "image/jpeg",
        "image/png", 
        "image/gif",
        "image/webp",
        "video/mp4",
        "video/quicktime",  # .mov files
    }
    
    ALLOWED_EXTENSIONS = {
        ".jpg", ".jpeg", ".png", ".gif", ".webp",
        ".mp4", ".mov"
    }
    
    MAX_FILE_SIZE = 100 * 1024 * 1024  # 100MB
    
    @staticmethod
    def get_folder_by_media_type(media_type: str | None, is_thumbnail: bool = False) -> str:
        """
        Determine GCS folder based on media type.
        
        Args:
            media_type: Media type ('image' or 'video')
            is_thumbnail: Whether the file is a thumbnail/preview
            
        Returns:
            Folder name for storage
        """
        if is_thumbnail:
            return "previews"
        
        # Handle None or empty media type
        if not media_type:
            return "images"
        
        # Case insensitive comparison
        media_type_lower = media_type.lower()
        
        if media_type_lower == "video":
            return "videos"
        else:
            # Default to images for any other type
            return "images"
    
    def __init__(self, bucket_name: str | None = None):
        """
        Initialize GCS service.
        
        Args:
            bucket_name: Override default bucket from settings
        """
        self.bucket_name = bucket_name or settings.GCS_BUCKET_NAME
        
        if not self.bucket_name:
            raise ValueError("GCS_BUCKET_NAME not configured")
        
        # Set credentials if configured
        if hasattr(settings, "GOOGLE_APPLICATION_CREDENTIALS") and settings.GOOGLE_APPLICATION_CREDENTIALS:
            os.environ["GOOGLE_APPLICATION_CREDENTIALS"] = settings.GOOGLE_APPLICATION_CREDENTIALS
        
        try:
            self.client = storage.Client()
            self.bucket = self.client.bucket(self.bucket_name)
        except Exception:
            logger.exception("Failed to initialize GCS client")
            raise
    
    def upload_file(
        self, 
        file_content: bytes, 
        original_filename: str,
        content_type: str,
        folder: str = "images"
    ) -> str:
        """
        Upload file to GCS and return public URL.
        
        Args:
            file_content: File content as bytes
            original_filename: Original filename (for extension)
            content_type: MIME type of the file
            folder: Folder in bucket (default: images)
            
        Returns:
            Public URL of uploaded file
            
        Raises:
            ValueError: If file validation fails
            GoogleCloudError: If upload fails
        """
        # Validate file
        self._validate_file(file_content, original_filename, content_type)
        
        # Generate unique filename
        blob_name = self._generate_blob_name(original_filename, folder)
        
        try:
            # Create blob
            blob = self.bucket.blob(blob_name)
            
            # Upload file
            blob.upload_from_string(
                file_content,
                content_type=content_type
            )
            
            # Note: The bucket uses uniform bucket-level access and is already public.
            # We don't need to call make_public() on individual objects.
            
            logger.info(
                f"Successfully uploaded file to GCS: {blob_name} "
                f"({len(file_content)} bytes)"
            )
            
            return str(blob.public_url)
            
        except GoogleCloudError:
            logger.exception("GCS upload failed")
            raise
        except Exception:
            logger.exception("Unexpected error during GCS upload")
            raise
    
    def _validate_file(
        self, 
        file_content: bytes, 
        filename: str, 
        content_type: str
    ) -> None:
        """Validate file before upload."""
        # Check size
        file_size = len(file_content)
        if file_size > self.MAX_FILE_SIZE:
            raise ValueError(
                f"File too large: {file_size} bytes "
                f"(max: {self.MAX_FILE_SIZE} bytes)"
            )
        
        # Check content type
        if content_type not in self.ALLOWED_CONTENT_TYPES:
            raise ValueError(
                f"Content type not allowed: {content_type}. "
                f"Allowed types: {', '.join(self.ALLOWED_CONTENT_TYPES)}"
            )
        
        # Check extension
        ext = os.path.splitext(filename)[1].lower()
        if ext not in self.ALLOWED_EXTENSIONS:
            raise ValueError(
                f"File extension not allowed: {ext}. "
                f"Allowed extensions: {', '.join(self.ALLOWED_EXTENSIONS)}"
            )
    
    def _generate_blob_name(self, original_filename: str, folder: str) -> str:
        """Generate unique blob name with folder structure."""
        
        # Generate unique name with timestamp
        timestamp = datetime.utcnow().strftime("%Y%m%d_%H%M%S")
        unique_id = str(uuid.uuid4())[:8]
        
        # Format: folder/YYYYMMDD_HHMMSS_UUID_original.ext
        safe_filename = original_filename.replace(" ", "_")
        safe_filename = "".join(
            c for c in safe_filename 
            if c.isalnum() or c in ("_", "-", ".")
        )
        
        blob_name = f"{folder}/{timestamp}_{unique_id}_{safe_filename}"
        
        return blob_name
    
    def is_available(self) -> bool:
        """Check if GCS service is available and configured."""
        try:
            # Try to get bucket metadata
            self.bucket.reload()
            return True
        except Exception as e:
            logger.warning(f"GCS service not available: {e}")
            return False
    
    def delete_file(self, blob_name: str) -> bool:
        """
        Delete file from GCS (for cleanup/tests).
        
        Args:
            blob_name: Full blob name including folder
            
        Returns:
            True if deleted, False if not found
        """
        try:
            blob = self.bucket.blob(blob_name)
            blob.delete()
            logger.info(f"Deleted blob from GCS: {blob_name}")
            return True
        except Exception:
            logger.exception("Failed to delete blob")
            return False