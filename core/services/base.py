import logging
from abc import ABC, abstractmethod
from typing import Any, <PERSON><PERSON>, TypeVar, cast

from django.db import transaction

from core.repositories.base import BaseRepository
from core.utils.error_handler import log_errors

T = TypeVar("T")
R = TypeVar("R", bound=BaseRepository)


class BaseService(Generic[T, R], ABC):
    """
    Базовый сервис для бизнес-логики.
    Работает с репозиториями для доступа к данным.
    """
    
    def __init__(self, repository: R):
        self.repository = repository
        self.logger = logging.getLogger(self.__class__.__name__)
    
    @property
    @abstractmethod
    def entity_name(self) -> str:
        """Возвращает название сущности для логирования."""
        raise NotImplementedError
    
    @transaction.atomic
    @log_errors()
    def create(self, **data) -> T:
        """
        Создает новую сущность.
        
        Args:
            **data: Данные для создания
            
        Returns:
            Созданная сущность
            
        Raises:
            DataValidationError: При ошибке валидации
        """
        # Валидация данных
        self.validate_create_data(data)
        
        # Преобразование данных перед созданием
        processed_data = self.process_create_data(data)
        
        # Создание через репозиторий
        entity = self.repository.create(**processed_data)
        
        # Действия после создания
        self.after_create(entity)
        
        self.logger.info(f"Created {self.entity_name} with id {entity.pk}")
        return cast(T, entity)
    
    @transaction.atomic
    @log_errors()
    def update(self, id: int | str, **data) -> T:
        """
        Обновляет существующую сущность.
        
        Args:
            id: ID сущности
            **data: Данные для обновления
            
        Returns:
            Обновленная сущность
            
        Raises:
            NotFoundError: Если сущность не найдена
            DataValidationError: При ошибке валидации
        """
        # Получаем существующую сущность
        entity = self.get_by_id(id)
        
        # Валидация данных
        self.validate_update_data(entity, data)
        
        # Преобразование данных перед обновлением
        processed_data = self.process_update_data(entity, data)
        
        # Обновление через репозиторий
        updated_entity = self.repository.update(id, **processed_data)
        
        # Действия после обновления
        self.after_update(updated_entity, data)
        
        self.logger.info(f"Updated {self.entity_name} with id {id}")
        return cast(T, updated_entity)
    
    @log_errors()
    def get_by_id(self, id: int | str) -> T:
        """
        Получает сущность по ID.
        
        Args:
            id: ID сущности
            
        Returns:
            Найденная сущность
            
        Raises:
            NotFoundError: Если сущность не найдена
        """
        return cast(T, self.repository.get_by_id(id))
    
    @log_errors()
    def get_list(self, filters: dict[str, Any] | None = None, 
                 page: int = 1, per_page: int = 20) -> dict[str, Any]:
        """
        Получает список сущностей с пагинацией.
        
        Args:
            filters: Фильтры для выборки
            page: Номер страницы
            per_page: Количество элементов на странице
            
        Returns:
            Словарь с результатами пагинации
        """
        filters = filters or {}
        queryset = self.repository.filter(**filters)
        
        # Применяем дополнительную фильтрацию
        queryset = self.apply_list_filters(queryset, filters)
        
        return self.repository.paginate(queryset, page, per_page)
    
    @transaction.atomic
    @log_errors()
    def delete(self, id: int | str) -> bool:
        """
        Удаляет сущность.
        
        Args:
            id: ID сущности
            
        Returns:
            True если удаление успешно
            
        Raises:
            NotFoundError: Если сущность не найдена
        """
        # Проверяем существование
        entity = self.get_by_id(id)
        
        # Проверяем возможность удаления
        self.validate_delete(entity)
        
        # Действия перед удалением
        self.before_delete(entity)
        
        # Удаление через репозиторий
        result = self.repository.delete(id)
        
        # Действия после удаления
        self.after_delete(entity)
        
        self.logger.info(f"Deleted {self.entity_name} with id {id}")
        return result
    
    @transaction.atomic
    @log_errors()
    def bulk_create(self, items: list[dict[str, Any]]) -> list[T]:
        """
        Массовое создание сущностей.
        
        Args:
            items: Список данных для создания
            
        Returns:
            Список созданных сущностей
        """
        # Валидация всех элементов
        for item in items:
            self.validate_create_data(item)
        
        # Преобразование данных
        processed_items = [self.process_create_data(item) for item in items]
        
        # Создание объектов модели
        model_class = self.repository.model
        objects = [model_class(**data) for data in processed_items]
        
        # Массовое создание
        created = self.repository.bulk_create(objects)
        
        self.logger.info(f"Bulk created {len(created)} {self.entity_name} entities")
        return created
    
    def search(self, query: str, search_fields: list[str]) -> list[T]:
        """
        Поиск сущностей по текстовым полям.
        
        Args:
            query: Поисковый запрос
            search_fields: Поля для поиска
            
        Returns:
            Список найденных сущностей
        """
        return list(self.repository.search(query, search_fields))
    
    # Методы для переопределения в подклассах
    
    def validate_create_data(self, data: dict[str, Any]):
        """
        Валидирует данные перед созданием.
        Переопределите для добавления специфичной валидации.
        
        Args:
            data: Данные для валидации
            
        Raises:
            DataValidationError: При ошибке валидации
        """
        pass
    
    def validate_update_data(self, entity: T, data: dict[str, Any]):
        """
        Валидирует данные перед обновлением.
        Переопределите для добавления специфичной валидации.
        
        Args:
            entity: Существующая сущность
            data: Данные для валидации
            
        Raises:
            DataValidationError: При ошибке валидации
        """
        pass
    
    def validate_delete(self, entity: T):
        """
        Проверяет возможность удаления сущности.
        Переопределите для добавления специфичных проверок.
        
        Args:
            entity: Сущность для удаления
            
        Raises:
            DataValidationError: Если удаление невозможно
        """
        pass
    
    def process_create_data(self, data: dict[str, Any]) -> dict[str, Any]:
        """
        Обрабатывает данные перед созданием.
        Переопределите для добавления специфичной обработки.
        
        Args:
            data: Исходные данные
            
        Returns:
            Обработанные данные
        """
        return data
    
    def process_update_data(self, entity: T, data: dict[str, Any]) -> dict[str, Any]:
        """
        Обрабатывает данные перед обновлением.
        Переопределите для добавления специфичной обработки.
        
        Args:
            entity: Существующая сущность
            data: Исходные данные
            
        Returns:
            Обработанные данные
        """
        return data
    
    def after_create(self, entity: T):
        """
        Действия после создания сущности.
        Переопределите для добавления специфичных действий.
        
        Args:
            entity: Созданная сущность
        """
        pass
    
    def after_update(self, entity: T, original_data: dict[str, Any]):
        """
        Действия после обновления сущности.
        Переопределите для добавления специфичных действий.
        
        Args:
            entity: Обновленная сущность
            original_data: Исходные данные обновления
        """
        pass
    
    def before_delete(self, entity: T):
        """
        Действия перед удалением сущности.
        Переопределите для добавления специфичных действий.
        
        Args:
            entity: Сущность для удаления
        """
        pass
    
    def after_delete(self, entity: T):
        """
        Действия после удаления сущности.
        Переопределите для добавления специфичных действий.
        
        Args:
            entity: Удаленная сущность
        """
        pass
    
    def apply_list_filters(self, queryset, filters: dict[str, Any]):
        """
        Применяет дополнительные фильтры к queryset.
        Переопределите для добавления специфичной фильтрации.
        
        Args:
            queryset: Исходный queryset
            filters: Фильтры
            
        Returns:
            Отфильтрованный queryset
        """
        return queryset


class ImportService(ABC):
    """
    Базовый сервис для импорта данных из внешних источников.
    """
    
    def __init__(self):
        self.logger = logging.getLogger(self.__class__.__name__)
    
    @property
    @abstractmethod
    def source_name(self) -> str:
        """Возвращает название источника данных."""
        raise NotImplementedError
    
    @transaction.atomic
    @log_errors()
    def import_data(self, **kwargs) -> dict[str, Any]:
        """
        Импортирует данные из внешнего источника.
        
        Args:
            **kwargs: Параметры импорта
            
        Returns:
            Результаты импорта
        """
        # Валидация параметров
        self.validate_import_params(kwargs)
        
        # Подготовка к импорту
        self.before_import(kwargs)
        
        # Выполнение импорта
        result = self.perform_import(kwargs)
        
        # Действия после импорта
        self.after_import(result, kwargs)
        
        self.logger.info(
            f"Imported data from {self.source_name}: {result.get('imported_count', 0)} items"
        )
        return result
    
    @abstractmethod
    def perform_import(self, params: dict[str, Any]) -> dict[str, Any]:
        """
        Выполняет импорт данных.
        Переопределите в подклассах.
        
        Args:
            params: Параметры импорта
            
        Returns:
            Результаты импорта
        """
        raise NotImplementedError
    
    def validate_import_params(self, params: dict[str, Any]):
        """
        Валидирует параметры импорта.
        Переопределите для добавления специфичной валидации.
        
        Args:
            params: Параметры для валидации
        """
        pass
    
    def before_import(self, params: dict[str, Any]):
        """
        Действия перед импортом.
        Переопределите для добавления специфичных действий.
        
        Args:
            params: Параметры импорта
        """
        pass
    
    def after_import(self, result: dict[str, Any], params: dict[str, Any]):
        """
        Действия после импорта.
        Переопределите для добавления специфичных действий.
        
        Args:
            result: Результаты импорта
            params: Параметры импорта
        """
        pass


class MediaService:
    """
    Сервис для работы с медиафайлами.
    """
    
    def __init__(self, storage_path: str = "media/social_media"):
        self.storage_path = storage_path
        self.logger = logging.getLogger(self.__class__.__name__)
    
    @log_errors()
    def process_media(self, media_url: str, media_type: str) -> dict[str, Any]:
        """
        Обрабатывает медиафайл.
        
        Args:
            media_url: URL медиафайла
            media_type: Тип медиа (photo, video, etc.)
            
        Returns:
            Словарь с информацией о медиафайле
        """
        # Базовая реализация - просто возвращаем URL
        return {
            "media_url": media_url,
            "media_type": media_type,
            "is_downloaded": False
        }
    
    async def download_media(self, media_url: str, filename: str) -> str:
        """
        Загружает медиафайл.
        
        Args:
            media_url: URL для загрузки
            filename: Имя файла для сохранения
            
        Returns:
            Путь к сохраненному файлу
        """
        # Реализация будет добавлена при рефакторинге конкретных приложений
        raise NotImplementedError
    
    def get_media_metadata(self, file_path: str) -> dict[str, Any]:
        """
        Получает метаданные медиафайла.
        
        Args:
            file_path: Путь к файлу
            
        Returns:
            Словарь с метаданными
        """
        # Реализация будет добавлена при рефакторинге конкретных приложений
        raise NotImplementedError