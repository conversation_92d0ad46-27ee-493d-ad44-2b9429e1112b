"""
Core application package with shared functionality
"""

# Модули импортируются отдельно для избежания циклических зависимостей при старте Django
# Используйте: 
# from core.models import TimestampedModel, SoftDeleteModel
# from core.services.base import BaseService, CachedService, MediaService
# from core.repositories.base import BaseRepository
# from core.utils import TextUtils, FileUtils, URLUtils
# from core.exceptions import NotFoundError, DatabaseError, etc.