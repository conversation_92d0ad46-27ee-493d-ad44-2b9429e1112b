"""
Decorators for task management system.
"""

import functools
import inspect
import time
from collections.abc import Callable
from typing import Any

from core.logging import ContextLogger
from core.utils.decorators.async_utils import async_to_sync_django

# We'll implement exponential backoff inline instead of importing
from .base import BaseTask
from .exceptions import TaskExecutionError

logger = ContextLogger(__name__)

# Global registry for dynamic task classes
_DYNAMIC_TASK_REGISTRY = {}


def exponential_backoff(attempt: int, initial_delay: float = 1.0, backoff_factor: float = 2.0) -> float:
    """Calculate exponential backoff delay."""
    return initial_delay * (backoff_factor ** (attempt - 1))


def make_async(
    task_type: str | None = None,
    description: str = "",
    max_retries: int = 3,
    retry_delay: float = 1.0,
    retry_backoff: float = 2.0
):
    """
    Decorator to convert a synchronous function into an asynchronous task.
    
    Args:
        task_type: Unique task type identifier (defaults to function name)
        description: Task description
        max_retries: Maximum number of retry attempts
        retry_delay: Initial retry delay in seconds
        retry_backoff: Exponential backoff multiplier
        
    Example:
        @make_async(task_type="import_profile", max_retries=5)
        def import_instagram_profile(username: str, deep_scan: bool = False):
            # Function logic here
            return {"profile_id": profile.id}
            
        # Can be called synchronously
        result = import_instagram_profile("johndoe")
        
        # Or enqueued as async task
        task_id = import_instagram_profile.enqueue_async("johndoe", deep_scan=True)
    """
    def decorator(func: Callable) -> Callable:
        # Generate task type from function name if not provided
        nonlocal task_type
        if not task_type:
            task_type = f"{func.__module__}.{func.__name__}"
        
        # Create dynamic task class name
        dynamic_class_name = f"DynamicTask_{func.__name__}_{id(func)}"
        
        # Create dynamic task class
        class DynamicTask(BaseTask):
            def validate_params(self, *args, **kwargs) -> dict[str, Any]:
                """Convert args to kwargs based on function signature."""
                sig = inspect.signature(func)
                bound = sig.bind(*args, **kwargs)
                bound.apply_defaults()
                return dict(bound.arguments)
            
            def execute_task(self, **kwargs) -> dict[str, Any]:
                """Execute the wrapped function with retry logic."""
                attempt = 0
                last_error = None
                
                while attempt <= self.max_retries:
                    try:
                        # Log execution
                        logger.info(
                            f"Executing task {self.task_id} (attempt {attempt + 1}/{self.max_retries + 1})",
                            extra={
                                "task_id": self.task_id,
                                "task_type": self.task_type,
                                "attempt": attempt + 1,
                                "parameters": kwargs
                            }
                        )
                        
                        # Execute function
                        result = func(**kwargs)
                        
                        # Ensure result is serializable
                        if not isinstance(result, dict):
                            result = {"result": result}
                        
                        return result
                        
                    except Exception as e:
                        last_error = e
                        attempt += 1
                        
                        if attempt <= self.max_retries:
                            # Calculate retry delay with exponential backoff
                            delay = exponential_backoff(
                                attempt,
                                initial_delay=retry_delay,
                                backoff_factor=retry_backoff
                            )
                            
                            logger.warning(
                                f"Task {self.task_id} failed, retrying in {delay:.1f}s",
                                extra={
                                    "task_id": self.task_id,
                                    "attempt": attempt,
                                    "delay": delay,
                                    "error": str(e)
                                }
                            )
                            
                            # Update progress
                            self.update_progress(
                                0,
                                f"Retry attempt {attempt}/{self.max_retries} after error: {e!s}"
                            )
                            
                            time.sleep(delay)
                        else:
                            # Max retries exceeded
                            raise TaskExecutionError(
                                self.task_id,
                                f"Max retries exceeded: {last_error!s}",
                                last_error
                            )
                
                # This should never be reached, but satisfies mypy
                raise TaskExecutionError(
                    self.task_id,
                    "Unexpected execution path",
                    RuntimeError("Task execution completed without result or error")
                )
        
        # Set class attributes
        DynamicTask.__name__ = dynamic_class_name
        DynamicTask.__module__ = "core.tasks.decorators"  # Always use this module
        DynamicTask.task_type = task_type
        DynamicTask.description = description or f"Task for {func.__name__}"
        DynamicTask.max_retries = max_retries
        
        # Register in global registry and module namespace
        _DYNAMIC_TASK_REGISTRY[dynamic_class_name] = DynamicTask
        globals()[dynamic_class_name] = DynamicTask
        
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            """Synchronous execution wrapper."""
            task = DynamicTask()
            # Convert args to kwargs using validate_params
            params = task.validate_params(*args, **kwargs)
            return task.execute(**params)
        
        # Add async enqueueing method
        def enqueue_async(*args, **kwargs) -> str:
            """Enqueue task for asynchronous execution via Celery."""
            task = DynamicTask()
            params = task.validate_params(*args, **kwargs)
            task.run_async(**params)
            return task.task_id
        
        # Attach methods to wrapper
        wrapper.enqueue_async = enqueue_async
        wrapper.task_class = DynamicTask
        wrapper.task_type = task_type
        
        return wrapper
    
    return decorator


def task(
    task_class: type[BaseTask] | None = None,
    *,
    monitor: bool = True,
    cache_result: bool = False,
    cache_ttl: int = 300
):
    """
    Decorator to register a task class and add monitoring/caching.
    
    Args:
        task_class: Task class to decorate (for direct decoration)
        monitor: Whether to monitor task execution
        cache_result: Whether to cache task results
        cache_ttl: Cache TTL in seconds
        
    Example:
        @task(monitor=True, cache_result=True)
        class ImportProfileTask(BaseTask):
            task_type = "import_profile"
            
            def execute_task(self, **kwargs):
                # Task logic
                pass
    """
    def decorator(cls: type[BaseTask]) -> type[BaseTask]:
        # Wrap execute method with monitoring
        if monitor:
            original_execute = cls.execute
            
            def monitored_execute(self, **kwargs):
                # Use log_execution_time manually to avoid decorator issues
                import time
                start_time = time.time()
                try:
                    result = original_execute(self, **kwargs)
                    execution_time = time.time() - start_time
                    logger.info(
                        f"Task {self.task_id} executed in {execution_time:.2f}s",
                        extra={
                            "task_id": self.task_id,
                            "task_type": self.task_type,
                            "execution_time": execution_time
                        }
                    )
                    return result
                except Exception as e:
                    execution_time = time.time() - start_time
                    logger.error(
                        f"Task {self.task_id} failed after {execution_time:.2f}s",
                        extra={
                            "task_id": self.task_id,
                            "task_type": self.task_type,
                            "execution_time": execution_time,
                            "error": str(e)
                        }
                    )
                    raise
            
            setattr(cls, 'execute', monitored_execute)
        
        # Add caching if requested
        if cache_result:
            # Cache functionality has been removed
            # The decorator now does nothing when cache_result=True
            pass
        
        return cls
    
    # Handle both @task and @task() syntax
    if task_class is not None:
        return decorator(task_class)
    return decorator


def async_task(func: Callable) -> Callable:
    """
    Decorator to create an async task from an async function.
    
    Combines make_async with async_to_sync_django for async functions.
    
    Example:
        @async_task
        async def fetch_external_api(url: str):
            async with aiohttp.ClientSession() as session:
                async with session.get(url) as response:
                    return await response.json()
                    
        # Can be called synchronously
        data = fetch_external_api("https://api.example.com")
        
        # Or enqueued
        task_id = fetch_external_api.enqueue_async("https://api.example.com")
    """
    # First convert async to sync
    sync_func = async_to_sync_django()(func)
    
    # Then make it a task
    task_func = make_async()(sync_func)
    
    # Preserve original async function reference
    task_func.__async_original__ = func
    
    return task_func  # type: ignore[no-any-return]


def periodic_task(interval_seconds: float):
    """
    Decorator for periodic tasks (placeholder for future Celery integration).
    
    Args:
        interval_seconds: Execution interval in seconds
        
    Example:
        @periodic_task(interval_seconds=3600)  # Run every hour
        def cleanup_old_tasks():
            # Cleanup logic
            pass
    """
    def decorator(func: Callable) -> Callable:
        # For now, just mark the function with metadata
        func._periodic_interval = interval_seconds
        func._is_periodic_task = True
        
        logger.info(
            f"Registered periodic task: {func.__name__} (interval: {interval_seconds}s)",
            extra={
                "task_name": func.__name__,
                "interval": interval_seconds
            }
        )
        
        return func
    
    return decorator