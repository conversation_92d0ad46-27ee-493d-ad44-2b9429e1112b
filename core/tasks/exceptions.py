"""
Exceptions for task management system.
"""

from core.exceptions.base import BaseServiceException


class TaskException(BaseServiceException):
    """Base exception for all task-related errors."""

    pass


class TaskExecutionError(TaskException):
    """Raised when task execution fails."""

    def __init__(self, task_id: str, message: str, original_error: Exception | None = None):
        self.task_id = task_id
        self.original_error = original_error
        super().__init__(f"Task {task_id} failed: {message}")


class TaskRetryError(TaskException):
    """Raised when task retry limit is exceeded."""

    def __init__(self, task_id: str, retry_count: int, max_retries: int):
        self.task_id = task_id
        self.retry_count = retry_count
        self.max_retries = max_retries
        super().__init__(
            f"Task {task_id} exceeded retry limit: {retry_count}/{max_retries}"
        )


class TaskValidationError(TaskException):
    """Raised when task parameters validation fails."""

    def __init__(self, task_type: str, errors: dict):
        self.task_type = task_type
        self.errors = errors
        super().__init__(f"Task {task_type} validation failed: {errors}")


class TaskNotFoundError(TaskException):
    """Raised when task is not found."""

    def __init__(self, task_id: str):
        self.task_id = task_id
        super().__init__(f"Task {task_id} not found")


class TaskAlreadyRunningError(TaskException):
    """Raised when trying to start an already running task."""

    def __init__(self, task_id: str):
        self.task_id = task_id
        super().__init__(f"Task {task_id} is already running")


class TaskQueueError(TaskException):
    """Raised when there's an error with the task queue."""

    pass