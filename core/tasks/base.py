"""
Base classes for task management system.
"""

import uuid
from abc import ABC, abstractmethod
from typing import Any, ClassVar

from django.db import models

from core.logging import ContextLogger
from core.models import TaskResult
from core.repositories.base import BaseRepository

from .exceptions import TaskExecutionError, TaskRetryError, TaskValidationError

logger = ContextLogger.get_logger(__name__)


class TaskStatus(models.TextChoices):
    """Task execution status choices."""

    PENDING = "pending", "Ожидает"
    RUNNING = "running", "Выполняется"
    SUCCESS = "success", "Успешно"
    FAILED = "failed", "Ошибка"
    RETRY = "retry", "Повтор"
    CANCELLED = "cancelled", "Отменено"


class TaskRepository(BaseRepository[TaskResult]):
    """Repository for TaskResult model."""

    model = TaskResult

    def get_pending_tasks(self, limit: int = 10) -> list[TaskResult]:
        """Get pending tasks ordered by priority."""
        return list(
            self.model.objects.filter(status="pending")
            .order_by("-priority", "created_at")[:limit]
        )

    def get_running_tasks(self) -> list[TaskResult]:
        """Get all running tasks."""
        return list(self.model.objects.filter(status="running"))

    def get_tasks_by_channel(self, channel_name: str) -> list[TaskResult]:
        """Get tasks by WebSocket channel."""
        return list(
            self.model.objects.filter(channel_name=channel_name)
            .order_by("-created_at")
        )


class BaseTask(ABC):
    """
    Abstract base class for all asynchronous tasks.
    
    Provides common functionality for task execution, progress tracking,
    and error handling.
    """

    # Task metadata
    task_type: ClassVar[str] = "base_task"
    description: ClassVar[str] = "Base task"
    max_retries: ClassVar[int] = 3
    
    def __init__(self, task_id: str | None = None):
        """Initialize task with unique ID."""
        self.task_id = task_id or str(uuid.uuid4())
        self.task_result: TaskResult | None = None
        self._repository = TaskRepository()
        
    @abstractmethod
    def validate_params(self, **kwargs) -> dict[str, Any]:
        """
        Validate task parameters.
        
        Should raise TaskValidationError if validation fails.
        Returns cleaned parameters.
        """
        pass
        
    @abstractmethod
    def execute_task(self, **kwargs) -> dict[str, Any]:
        """
        Execute the main task logic.
        
        This method should:
        - Implement the core business logic
        - Call update_progress() periodically
        - Return result dictionary
        - Raise TaskExecutionError on failure
        """
        pass
        
    def run_async(self, **kwargs):
        """Запустить задачу асинхронно через Celery"""
        from django.db import IntegrityError

        from core.tasks.celery_integration import execute_base_task
        
        # Extract priority from parameters
        priority_map = {"low": -1, "normal": 0, "high": 1}
        priority = kwargs.pop("_priority", "normal")
        priority_value = priority_map.get(priority, 0)
        
        # Try to create or get existing task result
        try:
            task_result, created = TaskResult.objects.get_or_create(
                task_id=self.task_id,
                defaults={
                    "task_type": self.task_type,
                    "status": "pending",
                    "parameters": kwargs,
                    "max_retries": self.max_retries,
                    "total_items": kwargs.get("total_items", 0),
                    "priority": priority_value,
                    "progress_message": "Task created"
                }
            )
            
            # If task already exists, update its status and parameters
            if not created:
                task_result.status = "pending"
                task_result.parameters = kwargs
                task_result.total_items = kwargs.get("total_items", 0)
                task_result.save()
                
        except IntegrityError:
            # Fallback: generate new task_id if there's still a conflict
            self.task_id = str(uuid.uuid4())
            task_result = TaskResult.objects.create(
                task_id=self.task_id,
                task_type=self.task_type,
                status="pending",
                parameters=kwargs,
                max_retries=self.max_retries,
                total_items=kwargs.get("total_items", 0),
                priority=priority_value,
                progress_message="Task created"
            )
        
        # Execute via Celery
        task_path = f"{self.__class__.__module__}.{self.__class__.__name__}"
        celery_result = execute_base_task.delay(task_path, task_id=self.task_id, **kwargs)
        
        # Update task result with Celery ID
        task_result.celery_task_id = celery_result.id
        task_result.celery_status = celery_result.status
        task_result.save(update_fields=["celery_task_id", "celery_status"])
        
        logger.info(
            f"Task {self.task_id} started via Celery",
            extra={
                "task_id": self.task_id,
                "celery_task_id": celery_result.id,
                "task_type": self.task_type
            }
        )
        
        return celery_result
        
    def execute(self, **kwargs) -> dict[str, Any]:
        """
        Main execution method with error handling and progress tracking.
        """
        logger.info(f"[BASE TASK] execute() called for {self.task_type} with task_id={self.task_id}")
        try:
            # Create or get task result
            self._ensure_task_result(**kwargs)
            
            # Mark as running
            self.task_result.mark_as_running()
            logger.info(f"[BASE TASK] Task {self.task_id} marked as running")
            
            # Validate parameters
            try:
                logger.info(f"[BASE TASK] Validating parameters for {self.task_type}")
                cleaned_params = self.validate_params(**kwargs)
                logger.info("[BASE TASK] Parameters validated successfully")
            except Exception as e:
                logger.exception("[BASE TASK] Validation failed")
                raise TaskValidationError(self.task_type, {"error": str(e)})
            
            # Execute task
            logger.info(f"[BASE TASK] Calling execute_task for {self.task_type}")
            result = self.execute_task(**cleaned_params)
            
            # Mark as completed
            self.task_result.mark_as_completed(result)
            
            logger.info(
                f"Task {self.task_id} completed successfully",
                extra={
                    "task_id": self.task_id,
                    "task_type": self.task_type,
                    "result": result
                }
            )
            
        except TaskValidationError as e:
            self.task_result.mark_as_failed(f"Validation error: {e!s}")
            raise
        except Exception as e:
            error_msg = f"Task execution failed: {e!s}"
            logger.exception(
                f"Task {self.task_id} failed",
                extra={
                    "task_id": self.task_id,
                    "task_type": self.task_type,
                    "error": str(e)
                }
            )
            
            # Mark as failed
            self.task_result.mark_as_failed(error_msg)
            
            # Check if can retry
            if self.task_result.can_retry():
                self.task_result.increment_retry()
                raise TaskRetryError(
                    self.task_id,
                    self.task_result.retry_count,
                    self.task_result.max_retries
                )
            else:
                raise TaskExecutionError(self.task_id, error_msg, e)
        else:
            return result
    
    def set_total_items(self, total: int) -> None:
        """Set total items for the task."""
        if self.task_result:
            self.task_result.total_items = total
            self.task_result.save(update_fields=["total_items"])
    
    def increment_processed_items(self, count: int = 1) -> None:
        """Increment processed items count."""
        if self.task_result:
            self.task_result.processed_items += count
            self.task_result.save(update_fields=["processed_items"])
    
    def log_error(self, error_message: str) -> None:
        """Log an error to the task result."""
        logger.error(
            f"Task {self.task_id} error: {error_message}",
            extra={
                "task_id": self.task_id,
                "task_type": self.task_type,
                "error": error_message
            }
        )
        if self.task_result:
            # Append error to error_message field
            current_error = self.task_result.error_message or ""
            if current_error:
                current_error += "\n"
            self.task_result.error_message = current_error + error_message
            self.task_result.save(update_fields=["error_message"])
    
    def update_progress(self, progress: int, message: str = ""):
        """
        Update task progress.
        
        Args:
            progress: Progress percentage (0-100)
            message: Progress message
        """
        if self.task_result:
            # Map percentage to processed items
            if self.task_result.total_items > 0:
                processed = int((progress / 100) * self.task_result.total_items)
                self.task_result.processed_items = processed
            
            self.task_result.update_progress(progress, message)
    
    def _ensure_task_result(self, **kwargs):
        """Ensure TaskResult exists for this task."""
        if not self.task_result:
            # Try to get existing task
            try:
                self.task_result = TaskResult.objects.get(task_id=self.task_id)
            except TaskResult.DoesNotExist:
                # Create new task result
                self.task_result = TaskResult.objects.create(
                    task_id=self.task_id,
                    task_type=self.task_type,
                    status="pending",
                    parameters=kwargs,
                    max_retries=self.max_retries,
                    total_items=kwargs.get("total_items", 0)
                )
    
    @classmethod
    def create_and_run_async(cls, **kwargs) -> str:
        """
        Create task instance and run asynchronously via Celery.
        
        Returns task_id for tracking.
        """
        task = cls()
        task.run_async(**kwargs)
        
        return task.task_id