"""
Test task for demonstrating task system.
"""

import time
from typing import Any, ClassVar

from core.tasks.base import BaseTask
from core.tasks.decorators import task


@task(monitor=True, cache_result=True, cache_ttl=60)
class EchoTask(BaseTask):
    """Simple task that echoes a message."""
    
    task_type: ClassVar[str] = "core.test_echo"
    description: ClassVar[str] = "Test task that echoes a message"
    max_retries: ClassVar[int] = 3
    
    def validate_params(self, message: str, delay: float = 0.0, **kwargs) -> dict[str, Any]:
        """Validate parameters."""
        if not message:
            raise ValueError("Message cannot be empty")
        if delay < 0:
            raise ValueError("Delay must be non-negative")
        return {
            "message": message,
            "delay": delay
        }
    
    def execute_task(self, message: str, delay: float = 0.0, **kwargs) -> dict[str, Any]:
        """Execute task - echo the message after optional delay."""
        self.update_progress(10, "Starting echo task")
        
        if delay > 0:
            self.update_progress(30, f"Waiting {delay}s...")
            time.sleep(delay)
        
        self.update_progress(70, "Processing message")
        
        # Simulate some processing
        result = {
            "original_message": message,
            "echo": message.upper(),
            "length": len(message),
            "word_count": len(message.split())
        }
        
        self.update_progress(100, "Echo complete!")
        
        return result