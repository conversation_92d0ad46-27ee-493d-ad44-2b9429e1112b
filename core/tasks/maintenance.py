"""
Задачи обслуживания системы.
"""
from datetime import timedelta

from celery import shared_task
from django.utils import timezone

from core.models import TaskResult


@shared_task(name="core.tasks.maintenance.clean_old_tasks")
def clean_old_tasks(days=30):
    """Удаляет старые задачи из базы данных"""
    cutoff_date = timezone.now() - timedelta(days=days)
    
    old_tasks = TaskResult.objects.filter(
        created_at__lt=cutoff_date
    )
    
    count = old_tasks.count()
    old_tasks.delete()
    
    return f"Deleted {count} old tasks"