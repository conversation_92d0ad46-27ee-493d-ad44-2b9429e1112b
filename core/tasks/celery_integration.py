"""
Интеграция существующих BaseTask с Celery.
"""
import logging
from importlib import import_module

from celery import shared_task

logger = logging.getLogger(__name__)


@shared_task(bind=True, name="core.execute_base_task")
def execute_base_task(self, task_class_path: str, task_id: str = None, **kwargs):
    """
    Универсальная Celery задача для выполнения любого BaseTask.
    
    Args:
        task_class_path: Путь к классу задачи (например, 'instagram_manager.tasks.import_tasks.ImportInstagramProfileTask')
        task_id: ID задачи (если не указан, используется Celery request.id)
        **kwargs: Параметры для задачи
    """
    logger.info(f"[CELERY] execute_base_task called with task_class_path={task_class_path}, task_id={task_id}")
    logger.info(f"[CELERY] kwargs keys: {list(kwargs.keys())}")
    
    # Импортируем класс задачи
    module_name, class_name = task_class_path.rsplit(".", 1)
    logger.info(f"[CELERY] Importing {class_name} from {module_name}")
    module = import_module(module_name)
    task_class = getattr(module, class_name)
    
    # Создаем экземпляр с правильным task_id
    logger.info(f"[CELERY] Creating task instance with task_id={task_id or self.request.id}")
    task = task_class(task_id=task_id or self.request.id)
    
    # Выполняем задачу
    logger.info("[CELERY] Executing task.execute() with kwargs")
    return task.execute(**kwargs)