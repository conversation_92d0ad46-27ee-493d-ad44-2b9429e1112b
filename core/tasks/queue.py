"""
Task queue implementation for managing asynchronous tasks.
"""
import logging
from collections import deque
from typing import Any, cast

from .base import BaseTask, TaskStatus

logger = logging.getLogger(__name__)


class TaskQueue:
    """Simple in-memory task queue for managing tasks."""
    
    def __init__(self):
        self._queue: deque = deque()
        self._running_tasks: dict[str, BaseTask] = {}
        self._completed_tasks: list[BaseTask] = []
        self._failed_tasks: list[BaseTask] = []
        self._registered_tasks: dict[str, type[BaseTask]] = {}
        # Add backend attribute that points to self for compatibility
        self.backend = self
    
    def add_task(self, task: BaseTask) -> str:
        """Add a task to the queue."""
        self._queue.append(task)
        logger.info(f"Task {task.task_id} added to queue")
        return task.task_id
    
    def get_next_task(self) -> BaseTask | None:
        """Get the next task from the queue."""
        if self._queue:
            return cast(BaseTask, self._queue.popleft())
        return None
    
    def mark_running(self, task: BaseTask) -> None:
        """Mark a task as running."""
        self._running_tasks[task.task_id] = task
        task.status = TaskStatus.RUNNING
    
    def mark_completed(self, task: BaseTask) -> None:
        """Mark a task as completed."""
        if task.task_id in self._running_tasks:
            del self._running_tasks[task.task_id]
        self._completed_tasks.append(task)
        task.status = TaskStatus.COMPLETED
    
    def mark_failed(self, task: BaseTask, error: Exception) -> None:
        """Mark a task as failed."""
        if task.task_id in self._running_tasks:
            del self._running_tasks[task.task_id]
        self._failed_tasks.append(task)
        task.status = TaskStatus.FAILED
        task.error = str(error)
    
    def get_status(self) -> dict[str, Any]:
        """Get the current status of the queue."""
        return {
            "queued": len(self._queue),
            "running": len(self._running_tasks),
            "completed": len(self._completed_tasks),
            "failed": len(self._failed_tasks),
        }
    
    def get_task_status(self, task_id: str) -> dict[str, Any]:
        """Get the status of a specific task."""
        # Check running tasks
        if task_id in self._running_tasks:
            task = self._running_tasks[task_id]
            return {
                "status": "running",
                "task_id": task_id,
                "task": task
            }
        
        # Check completed tasks
        for task in self._completed_tasks:
            if task.task_id == task_id:
                return {
                    "status": "completed",
                    "task_id": task_id,
                    "task": task
                }
        
        # Check failed tasks
        for task in self._failed_tasks:
            if task.task_id == task_id:
                return {
                    "status": "failed",
                    "task_id": task_id,
                    "task": task,
                    "error": getattr(task, "error", None)
                }
        
        # Check queue
        for task in self._queue:
            if task.task_id == task_id:
                return {
                    "status": "queued",
                    "task_id": task_id,
                    "task": task
                }
        
        return {
            "status": "not_found",
            "task_id": task_id
        }
    
    def clear(self) -> None:
        """Clear all tasks from the queue."""
        self._queue.clear()
        self._running_tasks.clear()
        self._completed_tasks.clear()
        self._failed_tasks.clear()
    
    def clear_queue(self) -> None:
        """Alias for clear() method for backward compatibility."""
        self.clear()
    
    def register_task(self, task_class: type[BaseTask]) -> None:
        """Register a task class."""
        self._registered_tasks[task_class.__name__] = task_class
        logger.info(f"Registered task: {task_class.__name__}")
    
    def process_all(self) -> int:
        """Process all tasks in the queue."""
        processed = 0
        while self._queue:
            task = self.get_next_task()
            if task:
                self.mark_running(task)
                try:
                    # Simulate task execution
                    task.status = TaskStatus.COMPLETED
                    self.mark_completed(task)
                    processed += 1
                except Exception as e:
                    self.mark_failed(task, e)
        return processed
    
    def process_next(self) -> bool:
        """Process the next task in the queue."""
        task = self.get_next_task()
        if task:
            self.mark_running(task)
            try:
                # Simulate task execution
                task.status = TaskStatus.COMPLETED
                self.mark_completed(task)
                return True
            except Exception as e:
                self.mark_failed(task, e)
                return False
        return False


# Global task queue instance
task_queue = TaskQueue()