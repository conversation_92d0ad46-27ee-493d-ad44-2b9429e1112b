"""
Template tags for accessing logging context in templates.

These tags provide access to:
- Current request ID
- Current user context
- Full logging context dictionary
"""

from typing import Any

from django import template
from django.http import HttpRequest

from core.logging import get_context

register = template.Library()


@register.simple_tag(takes_context=True)
def get_request_id(context: dict[str, Any]) -> str | None:
    """
    Get current request ID from context.
    
    Usage:
        {% load logging_tags %}
        {% get_request_id as request_id %}
        <div data-request-id="{{ request_id }}">...</div>
    
    Args:
        context: Template context
        
    Returns:
        Request ID string or None
    """
    # Try to get from request object first
    request = context.get("request")
    if isinstance(request, HttpRequest) and hasattr(request, "request_id"):
        return str(request.request_id)
    
    # Fall back to logging context
    logging_context = get_context()
    request_id = logging_context.get("request_id")
    return str(request_id) if request_id is not None else None


@register.simple_tag(takes_context=True)
def get_user_context(context: dict[str, Any]) -> dict[str, Any]:
    """
    Get current user context for logging.
    
    Usage:
        {% load logging_tags %}
        {% get_user_context as user_ctx %}
        {% if user_ctx %}
            User ID: {{ user_ctx.user_id }}
            Username: {{ user_ctx.username }}
        {% endif %}
    
    Args:
        context: Template context
        
    Returns:
        Dictionary with user context
    """
    user_context = {}
    
    # Try to get from request object first
    request = context.get("request")
    if isinstance(request, HttpRequest):
        user = getattr(request, "user", None)
        if user and user.is_authenticated:
            user_context["user_id"] = user.id
            user_context["username"] = user.username
            user_context["email"] = getattr(user, "email", None)
    
    # Merge with logging context
    logging_context = get_context()
    if "user_id" in logging_context:
        user_context.setdefault("user_id", logging_context["user_id"])
    if "username" in logging_context:
        user_context.setdefault("username", logging_context["username"])
    
    return user_context


@register.simple_tag
def get_logging_context() -> dict[str, Any]:
    """
    Get full logging context.
    
    Usage:
        {% load logging_tags %}
        {% get_logging_context as log_ctx %}
        {% for key, value in log_ctx.items %}
            {{ key }}: {{ value }}
        {% endfor %}
    
    Returns:
        Full logging context dictionary
    """
    return get_context()


@register.inclusion_tag("core/logging_debug.html", takes_context=True)
def render_logging_debug(context: dict[str, Any], show_in_production: bool = False) -> dict[str, Any]:
    """
    Render logging debug panel.
    
    Shows current logging context in a debug panel (only in DEBUG mode by default).
    
    Usage:
        {% load logging_tags %}
        {% render_logging_debug %}
        
        Or to show in production:
        {% render_logging_debug show_in_production=True %}
    
    Args:
        context: Template context
        show_in_production: Whether to show in production
        
    Returns:
        Context for debug template
    """
    from django.conf import settings
    
    # Check if we should show the debug panel
    show_panel = settings.DEBUG or show_in_production
    
    if not show_panel:
        return {"show_panel": False}
    
    # Get all context information
    request = context.get("request")
    logging_ctx = get_context()
    
    # Prepare debug data
    debug_data = {
        "show_panel": True,
        "logging_context": logging_ctx,
        "request_id": None,
        "user_context": {},
        "request_info": {},
    }
    
    # Extract request information
    if isinstance(request, HttpRequest):
        debug_data["request_id"] = getattr(request, "request_id", None)
        debug_data["request_info"] = {
            "method": request.method,
            "path": request.path,
            "is_ajax": request.headers.get("X-Requested-With") == "XMLHttpRequest",
            "content_type": request.content_type,
        }
        
        # User information
        user = getattr(request, "user", None)
        if user and user.is_authenticated:
            debug_data["user_context"] = {
                "id": user.id,
                "username": user.username,
                "email": getattr(user, "email", None),
                "is_staff": user.is_staff,
                "is_superuser": user.is_superuser,
            }
    
    return debug_data


@register.filter
def dict_get(dictionary: dict[str, Any], key: str) -> Any:
    """
    Get value from dictionary by key.
    
    Usage:
        {{ my_dict|dict_get:"key_name" }}
    
    Args:
        dictionary: Dictionary to get value from
        key: Key to look up
        
    Returns:
        Value or None
    """
    if not isinstance(dictionary, dict):
        return None
    return dictionary.get(key)


@register.filter
def format_request_id(request_id: str | None) -> str:
    """
    Format request ID for display.
    
    Usage:
        {{ request_id|format_request_id }}
    
    Args:
        request_id: Request ID to format
        
    Returns:
        Formatted request ID
    """
    if not request_id:
        return "N/A"
    
    # If it's a UUID, format it nicely
    if len(request_id) == 36 and request_id.count("-") == 4:
        # Standard UUID format, return first 8 chars
        return request_id[:8]
    
    # For other formats, return as is
    return request_id


@register.simple_tag
def logging_js_context() -> str:
    """
    Generate JavaScript code to expose logging context.
    
    Usage:
        {% load logging_tags %}
        <script>
            {% logging_js_context %}
            // Now you can use window.LOGGING_CONTEXT in JavaScript
        </script>
    
    Returns:
        JavaScript code
    """
    import json
    
    context = get_context()
    
    # Filter sensitive data
    safe_context = {
        "request_id": context.get("request_id"),
        "user_id": context.get("user_id"),
        "task_id": context.get("task_id"),
    }
    
    # Remove None values
    safe_context = {k: v for k, v in safe_context.items() if v is not None}
    
    return f"window.LOGGING_CONTEXT = {json.dumps(safe_context)};"


# Utility tags for conditional rendering

@register.simple_tag(takes_context=True)
def if_debug_mode(context: dict[str, Any]) -> bool:
    """
    Check if in debug mode.
    
    Usage:
        {% load logging_tags %}
        {% if_debug_mode as debug %}
        {% if debug %}
            <!-- Debug content -->
        {% endif %}
    
    Args:
        context: Template context
        
    Returns:
        True if in debug mode
    """
    from django.conf import settings
    return settings.DEBUG


@register.simple_tag(takes_context=True)
def if_logging_enabled(context: dict[str, Any]) -> bool:
    """
    Check if logging context is enabled.
    
    Usage:
        {% load logging_tags %}
        {% if_logging_enabled as logging_enabled %}
        {% if logging_enabled %}
            Request ID: {% get_request_id %}
        {% endif %}
    
    Args:
        context: Template context
        
    Returns:
        True if logging context is available
    """
    # Check if middleware is active by looking for request_id
    request = context.get("request")
    if isinstance(request, HttpRequest) and hasattr(request, "request_id"):
        return True
    
    # Check if any context is available
    logging_context = get_context()
    return bool(logging_context)