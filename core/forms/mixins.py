"""Base mixins for Django forms with Pydantic validation integration."""

from typing import Any

from django import forms
from django.conf import settings
from django.core.exceptions import ValidationError
from pydantic import BaseModel
from pydantic import ValidationError as PydanticValidationError

from core.schemas.validators import CommonValidators

from .widgets import SafeRadioSelect


class BaseValidatedForm(forms.Form):
    """Base form with common validation utilities."""
    
    def add_field_error(self, field: str, message: str, code: str = "invalid") -> None:
        """Add error to specific field."""
        self.add_error(field, ValidationError(message, code=code))
        
    def get_cleaned_value(self, field: str, default: Any = None) -> Any:
        """Safely get cleaned value with default."""
        return self.cleaned_data.get(field, default)


class CleanUsernameMixin:
    """Mixin for cleaning username fields."""
    
    def clean_username(self) -> str:
        """Clean and validate username field."""
        username = self.cleaned_data.get("username", "")  # type: ignore[attr-defined]
        if not username:
            return ""
            
        # Clean the username
        return CommonValidators.clean_username(username)


class CleanHashtagMixin:
    """Mixin for cleaning hashtag fields."""
    
    def clean_hashtag(self) -> str:
        """Clean hashtag field."""
        hashtag = self.cleaned_data.get("hashtag", "")  # type: ignore[attr-defined]
        if not hashtag:
            return ""
            
        # Remove # prefix
        return str(hashtag).lstrip("#").strip()


class InstagramURLMixin:
    """Mixin for Instagram URL validation."""
    
    def clean_post_url(self) -> str:
        """Clean and validate Instagram post URL."""
        url = self.cleaned_data.get("post_url", "")  # type: ignore[attr-defined]
        if not url:
            return ""
            
        # Validate URL
        try:
            CommonValidators.validate_url(url)
        except ValueError as e:
            raise ValidationError(str(e))
            
        # Check Instagram specific patterns
        if "instagram.com" not in url:
            raise ValidationError("URL must be from instagram.com")
            
        if "/p/" not in url and "/reel/" not in url and "/tv/" not in url:
            raise ValidationError("Invalid Instagram post URL")
            
        return str(url)
    
    def extract_shortcode_from_url(self, url: str) -> str:
        """Extract shortcode from Instagram URL."""
        # Remove query parameters
        url = url.split("?")[0].rstrip("/")
        
        # Extract shortcode
        for pattern in ["/p/", "/reel/", "/tv/"]:
            if pattern in url:
                parts = url.split(pattern)
                if len(parts) > 1:
                    return parts[1].split("/")[0]
                    
        raise ValidationError("Could not extract shortcode from URL")


class DateRangeMixin:
    """Mixin for date range validation."""
    
    def clean(self) -> dict[str, Any]:
        """Validate date range."""
        cleaned_data = super().clean()
        
        # Check for date range fields
        date_pairs = [
            ("date_from", "date_to"),
            ("start_date", "end_date"),
            ("from_date", "to_date"),
        ]
        
        for start_field, end_field in date_pairs:
            start_date = cleaned_data.get(start_field)
            end_date = cleaned_data.get(end_field)
            
            if start_date and end_date and start_date > end_date:
                self.add_error(
                    end_field,
                    ValidationError(
                        "End date must be after start date.",
                        code="invalid_date_range"
                    )
                )
                break
                
        return cleaned_data  # type: ignore[no-any-return]


class ExecutionModeMixin:
    """Mixin for forms with execution mode selection."""
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        
        # Add execution mode field with safe widget
        widget = SafeRadioSelect()
        widget.attrs = {"class": "form-check-input"}
        
        self.fields["execution_mode"] = forms.ChoiceField(
            choices=[
                ("async", "Asynchronous (Background Task)"),
                ("sync", "Synchronous (Immediate)"),
            ],
            initial="async",
            required=False,
            label="Execution Mode",
            help_text="Choose how to execute this task",
            widget=widget
        )


class MediaDownloadOptionsMixin:
    """Mixin for forms with media download options."""
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        
        # Add download media field with proper widget initialization
        download_widget = forms.CheckboxInput()
        download_widget.attrs = {
            "class": "form-check-input",
            "id": "id_download_media"
        }
        self.fields["download_media"] = forms.BooleanField(
            required=False,
            initial=False,  # Unchecked by default
            label="Download media files",
            help_text="Check this to download photos and videos during import (requires more disk space)",
            widget=download_widget
        )
        
        # GCS option with proper widget initialization
        gcs_widget = forms.CheckboxInput()
        gcs_widget.attrs = {}
        self.fields["save_media_to_gcs"] = forms.BooleanField(
            required=False,
            initial=False,
            label="Save media to Google Cloud Storage",
            help_text="Store media files in GCS instead of local storage (requires GCS configuration)",
            widget=gcs_widget
        )
        
        # Keep skip_media_download for backward compatibility but hidden
        hidden_widget = forms.HiddenInput()
        hidden_widget.attrs = {}
        self.fields["skip_media_download"] = forms.BooleanField(
            required=False,
            initial=True,  # True since download_media is False by default
            widget=hidden_widget
        )
        
        # Hide GCS checkbox if not configured
        if not getattr(settings, "GCS_BUCKET_NAME", ""):
            # Hide with HiddenInput (preferred for consistency)
            hidden_gcs_widget = forms.HiddenInput()
            hidden_gcs_widget.attrs = {}
            self.fields["save_media_to_gcs"].widget = hidden_gcs_widget
            self.fields["save_media_to_gcs"].initial = False
        else:
            # Update GCS widget attributes for dependency
            self.fields["save_media_to_gcs"].widget.attrs.update({
                "class": "form-check-input",
                "data-depends-on": "id_download_media",
                "data-depends-on-value": "true"
            })
    
    def clean(self) -> dict[str, Any]:
        """Validate form data."""
        cleaned_data = super().clean()
        
        # Invert the logic: if download_media is True, skip_media_download is False
        download_media = cleaned_data.get("download_media", False)
        cleaned_data["skip_media_download"] = not download_media
        
        # Server-side validation: Cannot save to GCS without downloading media
        save_media_to_gcs = cleaned_data.get("save_media_to_gcs", False)
        if save_media_to_gcs and not download_media:
            # Silently disable GCS when download_media is False
            cleaned_data["save_media_to_gcs"] = False
        
        # Remove auto_download_media if it exists (for backward compatibility)
        cleaned_data.pop("auto_download_media", None)
        
        return cleaned_data  # type: ignore[no-any-return]


class PydanticFormMixin:
    """Mixin to integrate Pydantic schema validation with Django forms."""
    
    pydantic_schema: type[BaseModel] | None = None
    
    def clean(self) -> dict[str, Any]:
        """Validate form data using Pydantic schema if provided."""
        cleaned_data = super().clean()
        
        if self.pydantic_schema:
            try:
                # Validate data with Pydantic schema
                validated_data = self.pydantic_schema(**cleaned_data)
                # Update cleaned data with validated values
                cleaned_data.update(validated_data.model_dump())
            except PydanticValidationError as e:
                # Convert Pydantic errors to Django form errors
                for error in e.errors():
                    field = error["loc"][0] if error["loc"] else "__all__"
                    message = error["msg"]
                    
                    if field in self.fields:  # type: ignore[attr-defined]
                        self.add_error(field, message)  # type: ignore[attr-defined]
                    else:
                        self.add_error(None, f"{field}: {message}")  # type: ignore[attr-defined]
                        
        return cleaned_data  # type: ignore[no-any-return]  # type: ignore[no-any-return]