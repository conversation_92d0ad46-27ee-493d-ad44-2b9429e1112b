"""Django validators that leverage Pydantic validation logic."""


from django.core.exceptions import ValidationError
from django.core.validators import RegexValidator

from core.schemas.validators import CommonValidators, InstagramValidators


class InstagramUsernameValidator:
    """Django validator for Instagram usernames using Pydantic logic."""
    
    message = "Enter a valid Instagram username (letters, numbers, dots, and underscores only)."
    code = "invalid_instagram_username"
    
    def __call__(self, value: str) -> None:
        """Validate Instagram username."""
        # Clean the username first
        cleaned = CommonValidators.clean_username(value)
        
        # Validate using Pydantic validator
        try:
            InstagramValidators.validate_username(cleaned)
        except ValueError as e:
            raise ValidationError(str(e), code=self.code)


def hashtag_validator(value: str) -> str:
    """Validate and clean hashtag."""
    if not value:
        return value
        
    # Remove # prefix
    cleaned = value.lstrip("#").strip()
    
    # Basic validation
    if not cleaned:
        raise ValidationError("Hashtag cannot be empty")
        
    # Check for valid characters
    if not cleaned.replace("_", "").isalnum():
        raise ValidationError("Hashtag can only contain letters, numbers, and underscores")
        
    return cleaned


# Create regex validators that match Pydantic patterns
instagram_username_regex = RegexValidator(
    regex=r"^[a-zA-Z0-9._]+$",
    message="Instagram username can only contain letters, numbers, dots, and underscores.",
    code="invalid_instagram_username"
)

telegram_username_regex = RegexValidator(
    regex=r"^[a-zA-Z][a-zA-Z0-9_]{4,31}$",
    message="Telegram username must start with a letter and be 5-32 characters long.",
    code="invalid_telegram_username"
)


# For backwards compatibility
validate_instagram_username = InstagramUsernameValidator()
validate_hashtag = hashtag_validator