"""Patch for Django 5.2 BoundField to ensure attrs is never None in templates."""

from django.forms.boundfield import Bound<PERSON>ield

# Store original method
_original_label_tag = Bound<PERSON>ield.label_tag


def patched_label_tag(self, contents=None, attrs=None, label_suffix=None, tag=None):
    """
    Patched version of label_tag that ensures attrs is always a dict.
    
    Django 5.2 templates expect attrs to be a dict, but the original
    method can pass None, causing "Failed lookup for key [items] in None" errors.
    """
    # Ensure attrs is always a dict
    if attrs is None:
        attrs = {}
    
    # Call original method with guaranteed dict attrs
    return _original_label_tag(self, contents, attrs, label_suffix, tag)


# Apply patch using setattr to avoid mypy error
setattr(Bound<PERSON><PERSON>, 'label_tag', patched_label_tag)


print("Applied Django 5.2 BoundField.label_tag patch")