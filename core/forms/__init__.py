"""Core forms module with reusable components."""

# Apply Django 5.2 patch before importing anything else
from . import boundfield_patch  # noqa: F401
from .fields import (
    BulkURLsField,
    BulkUsernamesField,
    HashtagField,
    InstagramURLField,
    PostTypesField,
    SocialUsernameField,
)
from .mixins import (
    BaseValidatedForm,
    CleanHashtagMixin,
    CleanUsernameMixin,
    DateRangeMixin,
    ExecutionModeMixin,
    InstagramURLMixin,
    MediaDownloadOptionsMixin,
    PydanticFormMixin,
)
from .validators import (
    InstagramUsernameValidator,
    hashtag_validator,
    instagram_username_regex,
    telegram_username_regex,
    validate_hashtag,
    validate_instagram_username,
)
from .widgets import SafeCheckboxSelectMultiple, SafeRadioSelect

__all__ = [
    # Mixins
    "BaseValidatedForm",
    # Fields
    "BulkURLsField",
    "BulkUsernamesField",
    "CleanHashtagMixin",
    "CleanUsernameMixin",
    "DateRangeMixin",
    "ExecutionModeMixin",
    "HashtagField",
    "InstagramURLField",
    "InstagramURLMixin",
    # Validators
    "InstagramUsernameValidator",
    "MediaDownloadOptionsMixin",
    "PostTypesField",
    "PydanticFormMixin",
    "SocialUsernameField",
    "hashtag_validator",
    "instagram_username_regex",
    "telegram_username_regex",
    "validate_hashtag",
    "validate_instagram_username",
    # Widgets
    "SafeCheckboxSelectMultiple",
    "SafeRadioSelect",
]