"""Safe widget implementations that ensure attrs is never None."""

from django import forms


class SafeRadioSelect(forms.RadioSelect):
    """RadioSelect widget that ensures all subwidgets have attrs as dict."""
    
    def subwidgets(self, name, value, attrs=None):
        """Override to ensure all subwidgets have attrs as dict."""
        # Ensure attrs is a dict
        if attrs is None:
            attrs = {}
        
        # Get subwidgets from parent
        for subwidget in super().subwidgets(name, value, attrs):
            # Ensure subwidget has attrs
            if isinstance(subwidget, dict):
                if "attrs" not in subwidget or subwidget["attrs"] is None:
                    subwidget["attrs"] = {}
            yield subwidget
    
    def optgroups(self, name, value, attrs=None):
        """Override to ensure all options have attrs as dict."""
        # Ensure attrs is a dict
        if attrs is None:
            attrs = {}
            
        # Get optgroups from parent
        groups = super().optgroups(name, value, attrs)
        
        # Process each group
        for group_name, group_choices, group_index in groups:
            # Process each option in the group
            for option in group_choices:
                # Ensure option has attrs
                if "attrs" not in option or option["attrs"] is None:
                    option["attrs"] = {}
                    
            yield group_name, group_choices, group_index
    
    def create_option(self, name, value, label, selected, index, 
                     subindex=None, attrs=None):
        """Override to ensure option has attrs as dict."""
        # Ensure attrs is a dict
        if attrs is None:
            attrs = {}
        
        # Get the option from parent
        option = super().create_option(
            name, value, label, selected, index, subindex, attrs
        )
        
        # Ensure attrs in option dict
        if "attrs" not in option or option["attrs"] is None:
            option["attrs"] = {}
            
        return option
    
    def get_context(self, name, value, attrs):
        """Ensure context always has attrs as dict."""
        if attrs is None:
            attrs = {}
        
        # Ensure attrs always has 'class' key for Django 5.2 compatibility
        if "class" not in attrs:
            attrs["class"] = ""
            
        context = super().get_context(name, value, attrs)
        
        # Ensure widget attrs in context
        if "widget" in context:
            if "attrs" not in context["widget"] or context["widget"]["attrs"] is None:
                context["widget"]["attrs"] = {}
            
            # Ensure 'class' key exists in widget attrs
            if "class" not in context["widget"]["attrs"]:
                context["widget"]["attrs"]["class"] = ""
                
            # Process optgroups in context
            if "optgroups" in context["widget"]:
                optgroups = []
                for group_name, group_choices, group_index in context["widget"]["optgroups"]:
                    # Process each option
                    for option in group_choices:
                        if "attrs" not in option or option["attrs"] is None:
                            option["attrs"] = {}
                        # Ensure 'class' key exists in option attrs
                        if "class" not in option["attrs"]:
                            option["attrs"]["class"] = ""
                    optgroups.append((group_name, group_choices, group_index))
                context["widget"]["optgroups"] = optgroups
                
        return context


class SafeCheckboxSelectMultiple(forms.CheckboxSelectMultiple):
    """CheckboxSelectMultiple widget that ensures all subwidgets have attrs as dict."""
    
    def __init__(self, attrs=None):
        """Initialize widget with attrs as dict."""
        if attrs is None:
            attrs = {}
        # Ensure 'class' key exists for Django 5.2 compatibility
        if "class" not in attrs:
            attrs["class"] = ""
        super().__init__(attrs)
    
    def subwidgets(self, name, value, attrs=None):
        """Override to ensure all subwidgets have attrs as dict."""
        # Ensure attrs is a dict with 'class' key
        if attrs is None:
            attrs = {}
        if "class" not in attrs:
            attrs["class"] = ""
        
        # Get subwidgets from parent
        for subwidget in super().subwidgets(name, value, attrs):
            # Ensure subwidget has attrs
            if isinstance(subwidget, dict):
                if "attrs" not in subwidget or subwidget["attrs"] is None:
                    subwidget["attrs"] = {}
                if "class" not in subwidget["attrs"]:
                    subwidget["attrs"]["class"] = ""
            yield subwidget
    
    def optgroups(self, name, value, attrs=None):
        """Override to ensure all options have attrs as dict."""
        # Ensure attrs is a dict with 'class' key
        if attrs is None:
            attrs = {}
        if "class" not in attrs:
            attrs["class"] = ""
            
        # Get optgroups from parent
        groups = super().optgroups(name, value, attrs)
        
        # Process each group
        for group_name, group_choices, group_index in groups:
            # Process each option in the group
            for option in group_choices:
                # Ensure option has attrs with 'class' key
                if "attrs" not in option or option["attrs"] is None:
                    option["attrs"] = {}
                if "class" not in option["attrs"]:
                    option["attrs"]["class"] = ""
                    
            yield group_name, group_choices, group_index
    
    def create_option(self, name, value, label, selected, index, 
                     subindex=None, attrs=None):
        """Override to ensure option has attrs as dict."""
        # Ensure attrs is a dict with 'class' key
        if attrs is None:
            attrs = {}
        if "class" not in attrs:
            attrs["class"] = ""
        
        # Get the option from parent
        option = super().create_option(
            name, value, label, selected, index, subindex, attrs
        )
        
        # Ensure attrs in option dict with 'class' key
        if "attrs" not in option or option["attrs"] is None:
            option["attrs"] = {}
        if "class" not in option["attrs"]:
            option["attrs"]["class"] = ""
            
        return option
    
    def get_context(self, name, value, attrs):
        """Ensure context always has attrs as dict."""
        if attrs is None:
            attrs = {}
        
        # Ensure attrs always has 'class' key for Django 5.2 compatibility
        if "class" not in attrs:
            attrs["class"] = ""
            
        context = super().get_context(name, value, attrs)
        
        # Ensure widget attrs in context
        if "widget" in context:
            if "attrs" not in context["widget"] or context["widget"]["attrs"] is None:
                context["widget"]["attrs"] = {}
            
            # Ensure 'class' key exists in widget attrs
            if "class" not in context["widget"]["attrs"]:
                context["widget"]["attrs"]["class"] = ""
                
            # Process optgroups in context
            if "optgroups" in context["widget"]:
                optgroups = []
                for group_name, group_choices, group_index in context["widget"]["optgroups"]:
                    # Process each option
                    for option in group_choices:
                        if "attrs" not in option or option["attrs"] is None:
                            option["attrs"] = {}
                        # Ensure 'class' key exists in option attrs
                        if "class" not in option["attrs"]:
                            option["attrs"]["class"] = ""
                    optgroups.append((group_name, group_choices, group_index))
                context["widget"]["optgroups"] = optgroups
                
        return context
    
    def render(self, name, value, attrs=None, renderer=None):
        """Override render to ensure attrs is always a dict with 'class' key."""
        if attrs is None:
            attrs = {}
        if "class" not in attrs:
            attrs["class"] = ""
        return super().render(name, value, attrs, renderer)