"""Reusable Django form fields with integrated validation."""

from typing import Any

from django import forms
from django.core.exceptions import ValidationError

from core.schemas.validators import CommonValidators, InstagramValidators, TelegramValidators

from .widgets import SafeCheckboxSelectMultiple


class SocialUsernameField(forms.CharField):
    """Form field for social media usernames with automatic cleaning and validation."""
    
    def __init__(self, platform: str = "instagram", *args, **kwargs):
        self.platform = platform
        
        # Set defaults based on platform
        if platform == "instagram":
            kwargs.setdefault("max_length", 30)
            kwargs.setdefault("help_text", "Enter Instagram username without @")
            pattern = "[a-zA-Z0-9._]+"
        elif platform == "telegram":
            kwargs.setdefault("max_length", 32)
            kwargs.setdefault("min_length", 5)
            kwargs.setdefault("help_text", "Enter Telegram username (5-32 characters)")
            pattern = "[a-zA-Z][a-zA-Z0-9_]{4,31}"
        else:
            kwargs.setdefault("max_length", 150)
            pattern = None
            
        # Set widget
        widget_attrs = {
            "placeholder": "username",
            "autocomplete": "off",
            "class": "form-control"
        }
        if pattern:
            widget_attrs["pattern"] = pattern
            
        kwargs.setdefault("widget", forms.TextInput(attrs=widget_attrs))
        super().__init__(*args, **kwargs)
        
    def to_python(self, value: Any) -> str:
        """Clean and convert the value."""
        value = super().to_python(value)
        if value:
            # Clean username
            value = CommonValidators.clean_username(value)
        return value
        
    def validate(self, value):
        """Validate the username based on platform."""
        super().validate(value)
        
        if value and self.platform == "instagram":
            try:
                InstagramValidators.validate_username(value)
            except ValueError as e:
                raise ValidationError(str(e))
        elif value and self.platform == "telegram":
            try:
                TelegramValidators.validate_username(value)
            except ValueError as e:
                raise ValidationError(str(e))


class InstagramURLField(forms.URLField):
    """Form field for Instagram URLs with validation."""
    
    def __init__(self, *args, **kwargs):
        kwargs.setdefault("help_text", "Enter Instagram post, reel, or profile URL")
        kwargs.setdefault("widget", forms.URLInput(attrs={
            "placeholder": "https://www.instagram.com/p/...",
            "autocomplete": "off",
            "class": "form-control"
        }))
        super().__init__(*args, **kwargs)
        
    def validate(self, value):
        """Validate Instagram URL."""
        super().validate(value)
        
        if value:
            if "instagram.com" not in value:
                raise ValidationError("URL must be from instagram.com")
                
            # Check for valid patterns
            valid_patterns = ["/p/", "/reel/", "/tv/", "/@", "/stories/"]
            if not any(pattern in value for pattern in valid_patterns):
                # Check if it's a profile URL
                if not value.rstrip("/").endswith("instagram.com") and not value.count("/") >= 3:
                    raise ValidationError("Invalid Instagram URL format")


class HashtagField(forms.CharField):
    """Form field for hashtags with automatic cleaning."""
    
    def __init__(self, *args, **kwargs):
        kwargs.setdefault("max_length", 100)
        kwargs.setdefault("widget", forms.TextInput(attrs={
            "placeholder": "#hashtag or hashtag",
            "autocomplete": "off",
            "class": "form-control"
        }))
        super().__init__(*args, **kwargs)
        
    def to_python(self, value: Any) -> str:
        """Clean hashtag - remove # if present."""
        value = super().to_python(value)
        if value:
            value = value.lstrip("#").strip()
        return value
    
    def validate(self, value: str) -> None:
        """Validate hashtag."""
        super().validate(value)
        if value and " " in value:
            raise ValidationError("Hashtag cannot contain spaces")


class BulkUsernamesField(forms.CharField):
    """Form field for bulk input of usernames (comma or newline separated)."""
    
    def __init__(self, platform: str = "instagram", max_count: int | None = None, *args, **kwargs):
        self.platform = platform
        self.max_count = max_count
        kwargs.setdefault("widget", forms.Textarea(attrs={
            "rows": 5,
            "placeholder": "Enter usernames separated by commas or new lines\nusername1\nusername2, username3",
            "autocomplete": "off",
            "class": "form-control"
        }))
        kwargs.setdefault("help_text", f"Enter {platform} usernames (one per line or comma-separated)")
        super().__init__(*args, **kwargs)
        
    def to_python(self, value: Any) -> list[str]:
        """Parse and validate bulk usernames."""
        value = super().to_python(value)
        if not value:
            return []
            
        # Split by newlines and commas
        usernames = []
        for line in value.strip().split("\n"):
            for username in line.split(","):
                username = username.strip()
                if username:
                    # Clean username
                    username = CommonValidators.clean_username(username)
                    usernames.append(username)
                    
        return usernames
        
    def validate(self, value):
        """Validate all usernames."""
        super().validate(value)
        
        if value:
            # Check max count
            if self.max_count and len(value) > self.max_count:
                raise ValidationError(
                    f"Maximum {self.max_count} usernames allowed, got {len(value)}"
                )
            
            # Platform-specific validation
            if self.platform == "instagram":
                errors = []
                for username in value:
                    try:
                        InstagramValidators.validate_username(username)
                    except ValueError as e:
                        errors.append(f"{username}: {e!s}")
                        
                if errors:
                    raise ValidationError(errors)


class BulkURLsField(forms.CharField):
    """Form field for bulk input of URLs."""
    
    def __init__(self, url_type: str = "instagram_post", max_count: int | None = None, *args, **kwargs):
        self.url_type = url_type
        self.max_count = max_count
        kwargs.setdefault("widget", forms.Textarea(attrs={
            "rows": 5,
            "placeholder": "Enter URLs separated by new lines\nhttps://www.instagram.com/p/...\nhttps://www.instagram.com/reel/...",
            "autocomplete": "off",
            "class": "form-control"
        }))
        kwargs.setdefault("help_text", "Enter URLs (one per line)")
        super().__init__(*args, **kwargs)
        
    def to_python(self, value: Any) -> list[str]:
        """Parse URLs."""
        value = super().to_python(value)
        if not value:
            return []
            
        # Split by newlines
        urls = []
        for line in value.strip().split("\n"):
            url = line.strip()
            if url:
                urls.append(url)
                
        return urls
    
    def validate(self, value: list[str]) -> None:
        """Validate the URLs."""
        super().validate(value)
        
        if value and self.max_count and len(value) > self.max_count:
            raise ValidationError(
                f"Maximum {self.max_count} URLs allowed, got {len(value)}"
            )


class PostTypesField(forms.MultipleChoiceField):
    """Field for selecting post types."""
    
    POST_TYPE_CHOICES = [
        ("photo", "Photos"),
        ("video", "Videos"),
        ("carousel", "Carousels"),
        ("reel", "Reels"),
    ]
    
    def __init__(self, *args, **kwargs):
        kwargs.setdefault("choices", self.POST_TYPE_CHOICES)
        kwargs.setdefault("initial", ["photo", "video", "carousel", "reel"])
        kwargs.setdefault("required", False)
        kwargs.setdefault("label", "Post types to import")
        # Use safe widget with attrs
        widget = SafeCheckboxSelectMultiple()
        widget.attrs = {"class": "form-check-input"}
        kwargs.setdefault("widget", widget)
        kwargs.setdefault("help_text", "Select which types of posts to import")
        super().__init__(*args, **kwargs)
    
    def validate(self, value: list[str]) -> None:
        """Validate post types."""
        if not value:
            return
        
        valid_types = [choice[0] for choice in self.POST_TYPE_CHOICES]
        invalid_types = [v for v in value if v not in valid_types]
        
        if invalid_types:
            raise ValidationError(f"Invalid post types: {', '.join(invalid_types)}")