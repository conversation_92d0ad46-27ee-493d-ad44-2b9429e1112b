import logging
import traceback
from collections.abc import Callable
from functools import wraps
from time import sleep
from typing import Any

from django.db import DatabaseError as DjangoDatabaseError
from django.db import transaction

from core.exceptions import (
    DatabaseError,
    NetworkError,
    RateLimitError,
    SocialMediaError,
    TemporaryError,
)

logger = logging.getLogger(__name__)


class ErrorHandler:
    """
    Утилита для унифицированной обработки ошибок.
    """

    @staticmethod
    def handle_exception(
        exception: Exception, context: str = "", raise_after: bool = True
    ) -> str | None:
        """
        Обрабатывает исключение с логированием.

        Args:
            exception: Исключение для обработки
            context: Контекст, в котором произошла ошибка
            raise_after: Перебросить исключение после обработки

        Returns:
            Сообщение об ошибке или None
        """
        error_message = (
            f"Error in {context}: {exception!s}" if context else str(exception)
        )

        if isinstance(exception, SocialMediaError):
            # Наши кастомные исключения
            logger.error(
                f"{error_message} [Code: {exception.code}]",
                extra={
                    "error_code": exception.code,
                    "error_details": exception.details,
                    "traceback": traceback.format_exc(),
                },
            )
        else:
            # Стандартные исключения
            logger.error(error_message, exc_info=True)

        if raise_after:
            raise

        return error_message

    @staticmethod
    def retry_on_error(
        max_attempts: int = 3,
        delay: float = 1.0,
        backoff: float = 2.0,
        exceptions: tuple[type[Exception], ...] = (TemporaryError, NetworkError),
    ) -> Callable:
        """
        Декоратор для автоматического повтора операции при определенных ошибках.

        Args:
            max_attempts: Максимальное количество попыток
            delay: Начальная задержка между попытками (в секундах)
            backoff: Множитель для увеличения задержки
            exceptions: Типы исключений, при которых нужно повторять

        Returns:
            Декорированная функция
        """

        def decorator(func: Callable) -> Callable:
            @wraps(func)
            def wrapper(*args, **kwargs) -> Any:
                last_exception = None
                current_delay = delay

                for attempt in range(max_attempts):
                    try:
                        return func(*args, **kwargs)
                    except exceptions as e:
                        last_exception = e

                        if attempt < max_attempts - 1:
                            # Логируем попытку
                            logger.warning(
                                f"Attempt {attempt + 1}/{max_attempts} failed for {func.__name__}: {e!s}. "
                                f"Retrying in {current_delay} seconds..."
                            )

                            # Специальная обработка для RateLimitError
                            if isinstance(e, RateLimitError) and e.details.get(
                                "retry_after"
                            ):
                                sleep(e.details["retry_after"])
                            else:
                                sleep(current_delay)
                                current_delay *= backoff
                        else:
                            # Последняя попытка не удалась
                            logger.exception(
                                f"All {max_attempts} attempts failed for {func.__name__}: {e!s}"
                            )

                # Если все попытки исчерпаны, перебрасываем последнее исключение
                if last_exception:
                    raise last_exception

            return wrapper

        return decorator

    @staticmethod
    def safe_transaction(func: Callable) -> Callable:
        """
        Декоратор для безопасного выполнения операций в транзакции.

        Args:
            func: Функция для выполнения в транзакции

        Returns:
            Декорированная функция
        """

        @wraps(func)
        def wrapper(*args, **kwargs) -> Any:
            operation_name = f"{func.__module__}.{func.__name__}"
            
            try:
                with transaction.atomic():
                    logger.debug(
                        f"[TRANSACTION START] Starting atomic transaction in safe_transaction: {operation_name}"
                    )
                    
                    result = func(*args, **kwargs)
                    
                    logger.debug(
                        f"[TRANSACTION END] Successfully completed transaction in safe_transaction: {operation_name}"
                    )
                    
                    return result
                    
            except DjangoDatabaseError as e:
                logger.error(
                    f"[TRANSACTION ERROR] Database operation failed in transaction: {operation_name} - {e}"
                )
                # Преобразуем Django DatabaseError в наш DatabaseError
                raise DatabaseError(f"Database operation failed: {e!s}") from e

        return wrapper

    @staticmethod
    def convert_api_error(api_name: str) -> Callable:
        """
        Декоратор для преобразования ошибок API в наши исключения.

        Args:
            api_name: Название API для контекста

        Returns:
            Декорированная функция
        """

        def decorator(func: Callable) -> Callable:
            @wraps(func)
            def wrapper(*args, **kwargs) -> Any:
                try:
                    return func(*args, **kwargs)
                except Exception as e:
                    # Здесь можно добавить логику преобразования специфичных
                    # исключений API в наши унифицированные исключения
                    error_message = f"{api_name} API error: {e!s}"

                    # Пример преобразования общих типов ошибок
                    if "rate limit" in str(e).lower():
                        raise RateLimitError(error_message) from e
                    elif "network" in str(e).lower() or "connection" in str(e).lower():
                        raise NetworkError(error_message) from e
                    elif "auth" in str(e).lower() or "unauthorized" in str(e).lower():
                        from core.exceptions import AuthenticationError

                        raise AuthenticationError(error_message) from e
                    else:
                        # Для неизвестных ошибок используем общий APIError
                        from core.exceptions import APIError

                        raise APIError(error_message) from e

            return wrapper

        return decorator


def log_errors(logger_name: str | None = None) -> Callable:
    """
    Декоратор для логирования ошибок функции.

    Args:
        logger_name: Имя логгера (если None, используется имя модуля функции)

    Returns:
        Декорированная функция
    """

    def decorator(func: Callable) -> Callable:
        func_logger = logging.getLogger(logger_name or func.__module__)

        @wraps(func)
        def wrapper(*args, **kwargs) -> Any:
            try:
                return func(*args, **kwargs)
            except Exception as e:
                # Skip logging during tests to avoid noise from expected validation errors
                import sys
                if "test" not in sys.argv and "pytest" not in sys.modules:
                    func_logger.error(
                        f"Error in {func.__name__}: {e!s}",
                        exc_info=True,
                        extra={
                            "function_name": func.__name__,
                            "function_module": func.__module__,
                            "function_args": str(args),
                            "function_kwargs": str(kwargs),
                        },
                    )
                raise

        return wrapper

    return decorator
