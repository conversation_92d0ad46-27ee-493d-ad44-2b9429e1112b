"""Перечисления (Enums) для проекта."""

from enum import Enum


class TaskStatus(Enum):
    """Статусы задач"""
    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"
    PAUSED = "paused"
    

class MediaType(Enum):
    """Типы медиа файлов"""
    IMAGE = "image"
    VIDEO = "video"
    AUDIO = "audio"
    DOCUMENT = "document"
    UNKNOWN = "unknown"
    

class PostType(Enum):
    """Типы постов"""
    PHOTO = "photo"
    VIDEO = "video"
    CAROUSEL = "carousel"
    REEL = "reel"
    STORY = "story"
    IGTV = "igtv"
    

class ChatType(Enum):
    """Типы чатов в Telegram"""
    PRIVATE = "private"
    GROUP = "group"
    SUPERGROUP = "supergroup"
    CHANNEL = "channel"
    

class CommentStatus(Enum):
    """Статусы комментариев"""
    PENDING = "pending"
    POSTED = "posted"
    FAILED = "failed"
    DELETED = "deleted"
    

class Platform(Enum):
    """Платформы социальных сетей"""
    INSTAGRAM = "instagram"
    TELEGRAM = "telegram"
    TWITTER = "twitter"
    FACEBOOK = "facebook"
    TIKTOK = "tiktok"
    

class ErrorCodes(Enum):
    """Коды ошибок"""
    RATE_LIMIT_EXCEEDED = "rate_limit_exceeded"
    AUTHENTICATION_FAILED = "authentication_failed"
    RESOURCE_NOT_FOUND = "resource_not_found"
    VALIDATION_ERROR = "validation_error"
    NETWORK_ERROR = "network_error"
    TIMEOUT_ERROR = "timeout_error"
    PERMISSION_DENIED = "permission_denied"
    INTERNAL_ERROR = "internal_error"