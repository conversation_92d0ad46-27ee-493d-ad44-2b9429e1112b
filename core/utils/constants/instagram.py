"""Константы для Instagram."""


class InstagramLimits:
    """Константы ограничений Instagram."""
    
    # Ограничения для профиля
    USERNAME_MAX_LENGTH = 30
    BIO_MAX_LENGTH = 150
    FULL_NAME_MAX_LENGTH = 150
    
    # Ограничения для постов
    CAPTION_MAX_LENGTH = 2200
    HASHTAG_MAX_COUNT = 30
    MENTION_MAX_COUNT = 20
    
    # Ограничения для комментариев
    COMMENT_MAX_LENGTH = 220
    
    # Ограничения для медиа
    MAX_IMAGES_PER_POST = 10
    MAX_VIDEO_DURATION_SECONDS = 3600  # 60 минут
    
    # Ограничения API
    MAX_POSTS_PER_REQUEST = 100
    MAX_COMMENTS_PER_REQUEST = 100
    

class InstagramURLs:
    """Константы URL Instagram."""
    
    BASE_URL = "https://www.instagram.com"
    PROFILE_URL_TEMPLATE = "{base_url}/{username}/"
    POST_URL_TEMPLATE = "{base_url}/p/{post_code}/"
    REEL_URL_TEMPLATE = "{base_url}/reel/{reel_code}/"
    STORIES_URL_TEMPLATE = "{base_url}/stories/{username}/"
    HASHTAG_URL_TEMPLATE = "{base_url}/explore/tags/{hashtag}/"