"""
Environment configuration module.

This module defines environment constants and utilities for managing
different deployment environments (development, testing, production).
"""

from __future__ import annotations

from enum import Enum
from typing import TYPE_CHECKING

if TYPE_CHECKING:
    pass


class Environment(Enum):
    """
    Enumeration of available deployment environments.
    
    Attributes:
        DEVELOPMENT: Local development environment
        TESTING: Automated testing environment
        PRODUCTION: Production deployment environment
    """
    
    DEVELOPMENT = "development"
    TESTING = "testing"
    PRODUCTION = "production"
    
    @classmethod
    def from_string(cls, value: str) -> Environment:
        """
        Create Environment from string value.
        
        Args:
            value: String representation of environment
            
        Returns:
            Environment enum value
            
        Raises:
            ValueError: If value doesn't match any environment
        """
        value = value.lower().strip()
        
        # Handle common aliases
        aliases = {
            "dev": cls.DEVELOPMENT,
            "local": cls.DEVELOPMENT,
            "test": cls.TESTING,
            "tests": cls.TESTING,
            "prod": cls.PRODUCTION,
            "production": cls.PRODUCTION,
            "development": cls.DEVELOPMENT,
            "testing": cls.TESTING,
        }
        
        if value in aliases:
            return aliases[value]
            
        raise ValueError(
            f"Invalid environment: '{value}'. "
            f"Valid options: {', '.join(aliases.keys())}"
        )
    
    @property
    def is_debug(self) -> bool:
        """Check if debug mode should be enabled."""
        return self == Environment.DEVELOPMENT
    
    @property
    def is_production(self) -> bool:
        """Check if this is production environment."""
        return self == Environment.PRODUCTION
    
    @property
    def is_testing(self) -> bool:
        """Check if this is testing environment."""
        return self == Environment.TESTING
    
    @property
    def required_env_vars(self) -> set[str]:
        """
        Get required environment variables for this environment.
        
        Returns:
            Set of required environment variable names
        """
        # Base required variables for all environments
        base_vars = {
            "SECRET_KEY",
            "DJANGO_ENV",
        }
        
        if self == Environment.PRODUCTION:
            # Production requires all API keys and database
            return base_vars | {
                "DATABASE_URL",
                "ALLOWED_HOSTS",
                "BRIGHTDATA_API_TOKEN",
                "API_ID",
                "API_HASH",
                "FIELD_ENCRYPTION_KEY",
                "REDIS_URL",
                "SENTRY_DSN",
            }
        elif self == Environment.TESTING:
            # Testing only needs minimal configuration
            return base_vars | {
                "FIELD_ENCRYPTION_KEY",
            }
        else:
            # Development needs API keys but can use defaults for infrastructure
            return base_vars | {
                "BRIGHTDATA_API_TOKEN",
                "API_ID", 
                "API_HASH",
                "FIELD_ENCRYPTION_KEY",
            }
    
    def __str__(self) -> str:
        """String representation of environment."""
        return self.value