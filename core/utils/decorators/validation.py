"""
Декораторы для валидации входных данных и проверки прав доступа
"""

import functools
from collections.abc import Callable

from pydantic import BaseModel, ValidationError

from core.exceptions import ValidationError as ServiceValidationError
from core.logging import ContextLogger

logger = ContextLogger(__name__)


def validate_input(
    schema: type[BaseModel], 
    source: str = "kwargs",
    exclude_unset: bool = False,
    partial: bool = False
):
    """
    Декоратор для валидации входных данных через Pydantic схему.
    
    Args:
        schema: Pydantic схема для валидации
        source: Источник данных для валидации:
            - 'kwargs': валидировать kwargs (по умолчанию)
            - 'first_arg': валидировать первый позиционный аргумент
            - 'all_args': собрать все аргументы в dict и валидировать
        exclude_unset: Исключить неустановленные поля при передаче в функцию
        partial: Разрешить частичную валидацию (не все обязательные поля)
    
    Example:
        @validate_input(ProfileCreateSchema)
        def create_profile(self, **kwargs):
            # kwargs уже провалидированы
            return self.repository.create(**kwargs)
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            try:
                validated_data = None
                
                if source == "kwargs":
                    # Валидируем kwargs
                    if partial:
                        # Создаем схему с необязательными полями
                        validated_data = schema(**kwargs)
                    else:
                        validated_data = schema(**kwargs)
                    
                    # Преобразуем в dict с нужными настройками
                    data_dict = validated_data.model_dump(exclude_unset=exclude_unset)
                    return func(*args, **data_dict)
                
                elif source == "first_arg":
                    # Валидируем первый аргумент
                    if not args:
                        # Нет аргументов для валидации
                        return func(*args, **kwargs)
                    
                    # Определяем индекс первого аргумента (пропуская self если это метод)
                    first_arg_index = 0
                    if args and hasattr(args[0], "__class__") and hasattr(func, "__self__"):
                        first_arg_index = 1
                        
                    if len(args) <= first_arg_index:
                        # Нет аргументов для валидации после self
                        return func(*args, **kwargs)
                    
                    first_arg = args[first_arg_index]
                    
                    if isinstance(first_arg, dict):
                        validated_data = schema(**first_arg)
                    else:
                        # Пытаемся преобразовать в dict
                        validated_data = schema.model_validate(first_arg)
                    
                    # Заменяем первый аргумент валидированными данными
                    new_args = list(args)
                    new_args[first_arg_index] = validated_data
                    
                    return func(*new_args, **kwargs)
                
                elif source == "all_args":
                    # Собираем все аргументы в словарь для валидации
                    # Получаем имена параметров функции
                    import inspect
                    sig = inspect.signature(func)
                    bound_args = sig.bind(*args, **kwargs)
                    bound_args.apply_defaults()
                    
                    # Исключаем self если это метод
                    all_params = dict(bound_args.arguments)
                    all_params.pop("self", None)
                    
                    # Валидируем все параметры
                    validated_data = schema(**all_params)
                    data_dict = validated_data.model_dump(exclude_unset=exclude_unset)
                    
                    # Восстанавливаем позиционные аргументы из валидированных данных
                    # Получаем список параметров функции без self
                    func_params = list(sig.parameters.keys())
                    if "self" in func_params:
                        func_params.remove("self")
                    
                    # Разделяем на позиционные и именованные аргументы
                    new_args = []
                    new_kwargs = {}
                    
                    # Добавляем self обратно если это метод
                    if hasattr(args[0], "__class__") and "self" in sig.parameters:
                        new_args.append(args[0])
                    
                    # Восстанавливаем позиционные аргументы в правильном порядке
                    # Сначала добавляем аргументы, которые были переданы, но не в схеме
                    param_index = 1 if hasattr(args[0], "__class__") and "self" in sig.parameters else 0
                    for i, param_name in enumerate(func_params):
                        param = sig.parameters[param_name]
                        if param_name in data_dict:
                            # Параметр есть в валидированных данных
                            if param.kind in (param.POSITIONAL_ONLY, param.POSITIONAL_OR_KEYWORD):
                                new_kwargs[param_name] = data_dict[param_name]
                            else:
                                new_kwargs[param_name] = data_dict[param_name]
                        elif param_index < len(args):
                            # Параметр не в схеме, но был передан как позиционный аргумент
                            if param.kind in (param.POSITIONAL_ONLY, param.POSITIONAL_OR_KEYWORD):
                                new_args.append(args[param_index])
                                param_index += 1
                    
                    # Вызываем функцию с правильными аргументами
                    return func(*new_args, **new_kwargs)
                
                else:
                    # Нет данных для валидации, вызываем как есть
                    return func(*args, **kwargs)
                    
            except ValidationError as e:
                # Преобразуем Pydantic ошибку в нашу
                errors = e.errors()
                if errors:
                    first_error = errors[0]
                    field_path = ".".join(str(loc) for loc in first_error["loc"])
                    
                    logger.exception(
                        f"Validation failed for {func.__name__}",
                        extra={
                            "function": f"{func.__module__}.{func.__name__}",
                            "field": field_path,
                            "error": first_error["msg"],
                            "input": first_error.get("input"),
                            "all_errors": errors
                        }
                    )
                    
                    raise ServiceValidationError(
                        field=field_path,
                        value=first_error.get("input"),
                        reason=first_error["msg"]
                    )
                raise
        
        return wrapper
    return decorator


def require_permission(
    permissions: str | list[str], 
    check_method: Callable | None = None,
    raise_exception: bool = True
):
    """
    Декоратор для проверки прав доступа.
    
    Args:
        permissions: Требуемые разрешения (строка или список)
        check_method: Метод для проверки прав (по умолчанию проверяет user.has_perm)
        raise_exception: Бросать исключение при отсутствии прав
    
    Example:
        @require_permission('instagram_manager.change_profile')
        def update_profile(self, request, profile_id):
            # Только для пользователей с правами
            pass
            
        @require_permission(['view_profile', 'change_profile'], check_method=custom_check)
        def manage_profile(self, user, profile):
            # Кастомная проверка прав
            pass
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            # Определяем пользователя
            user = None
            request = None
            
            # Ищем request или user в аргументах
            for arg in args:
                if hasattr(arg, "user"):
                    request = arg
                    user = arg.user
                    break
                elif hasattr(arg, "has_perm"):
                    user = arg
                    break
            
            # Проверяем в kwargs
            if not user:
                request = kwargs.get("request")
                if request and hasattr(request, "user"):
                    user = request.user
                else:
                    user = kwargs.get("user")
            
            if not user:
                logger.warning(
                    f"No user found for permission check in {func.__name__}",
                    extra={"function": f"{func.__module__}.{func.__name__}"}
                )
                if raise_exception:
                    raise PermissionError("User not found for permission check")
                return None
            
            # Преобразуем permissions в список
            perms_list = [permissions] if isinstance(permissions, str) else permissions
            
            # Проверяем права
            has_permission = False
            
            if check_method:
                # Используем кастомный метод проверки
                has_permission = check_method(user, perms_list)
            else:
                # Стандартная проверка Django
                if hasattr(user, "has_perms"):
                    has_permission = user.has_perms(perms_list)
                elif hasattr(user, "has_perm"):
                    has_permission = all(user.has_perm(perm) for perm in perms_list)
            
            if not has_permission:
                logger.warning(
                    f"Permission denied for {func.__name__}",
                    extra={
                        "function": f"{func.__module__}.{func.__name__}",
                        "user": str(user),
                        "required_permissions": perms_list,
                        "user_permissions": getattr(user, "get_all_permissions", lambda: [])()
                    }
                )
                
                if raise_exception:
                    from django.core.exceptions import PermissionDenied
                    raise PermissionDenied(f"User {user} lacks required permissions: {perms_list}")
                return None
            
            # Права есть, выполняем функцию
            logger.debug(
                f"Permission granted for {func.__name__}",
                extra={
                    "function": f"{func.__module__}.{func.__name__}",
                    "user": str(user),
                    "permissions": perms_list
                }
            )
            
            return func(*args, **kwargs)
        
        return wrapper
    return decorator