"""
Sentry integration decorators.

This module provides decorators for enhanced Sentry integration,
including transaction tracking, error capturing, and performance monitoring.
"""

import functools
import time
from collections.abc import Callable
from typing import Any, TypeVar, cast

import structlog

logger = structlog.get_logger(__name__)

# Type definitions
F = TypeVar("F", bound=Callable[..., Any])


def capture_errors(
    capture_args: bool = True,
    capture_result: bool = False,
    level: str = "error",
    fingerprint: list | None = None,
) -> Callable[[F], F]:
    """
    Decorator to automatically capture exceptions to Sentry.
    
    Args:
        capture_args: Whether to capture function arguments
        capture_result: Whether to capture function result
        level: Error level for Sentry
        fingerprint: Custom fingerprint for grouping
        
    Returns:
        Decorated function
        
    Example:
        @capture_errors(capture_args=True)
        def process_data(data: dict) -> dict:
            # Process data that might raise exceptions
            return processed_data
    """
    def decorator(func: F) -> F:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            try:
                import sentry_sdk
            except ImportError:
                # Sentry not available, run function normally
                return func(*args, **kwargs)
            
            try:
                result = func(*args, **kwargs)
                
                # Capture result if requested
                if capture_result and result:
                    with sentry_sdk.push_scope() as scope:
                        scope.set_context("function_result", {
                            "function": func.__name__,
                            "result_type": type(result).__name__,
                            "result_preview": str(result)[:200],
                        })
                
                return result
                
            except Exception as e:
                # Enhance error context
                with sentry_sdk.push_scope() as scope:
                    # Add function context
                    scope.set_context("function", {
                        "name": func.__name__,
                        "module": func.__module__,
                        "qualname": func.__qualname__,
                    })
                    
                    # Add arguments if requested
                    if capture_args:
                        scope.set_context("function_args", {
                            "args": [str(arg)[:100] for arg in args],
                            "kwargs": {k: str(v)[:100] for k, v in kwargs.items()},
                        })
                    
                    # Set custom fingerprint if provided
                    if fingerprint:
                        scope.fingerprint = fingerprint
                    
                    # Set error level
                    scope.level = level
                    
                    # Capture exception
                    sentry_sdk.capture_exception(e)
                
                # Re-raise the exception
                raise
        
        return cast(F, wrapper)
    
    return decorator


def track_transaction(
    operation: str = "function",
    capture_args: bool = False,
    track_performance: bool = True,
) -> Callable[[F], F]:
    """
    Decorator to track function execution as Sentry transaction.
    
    Args:
        operation: Operation type for transaction
        capture_args: Whether to capture function arguments
        track_performance: Whether to track performance metrics
        
    Returns:
        Decorated function
        
    Example:
        @track_transaction(operation="data_import")
        def import_instagram_profile(username: str) -> dict:
            # Import logic
            return profile_data
    """
    def decorator(func: F) -> F:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            try:
                import sentry_sdk
            except ImportError:
                # Sentry not available, run function normally
                return func(*args, **kwargs)
            
            # Start transaction
            transaction_name = f"{func.__module__}.{func.__name__}"
            with sentry_sdk.start_transaction(
                op=operation,
                name=transaction_name,
            ) as transaction:
                # Add function metadata
                transaction.set_data("function.module", func.__module__)
                transaction.set_data("function.name", func.__name__)
                
                # Add arguments if requested
                if capture_args:
                    transaction.set_data("function.args", {
                        "args_count": len(args),
                        "kwargs_keys": list(kwargs.keys()),
                    })
                
                # Track performance if requested
                start_time = time.time() if track_performance else None
                
                try:
                    result = func(*args, **kwargs)
                    transaction.set_status("ok")
                    return result
                    
                except Exception as e:
                    transaction.set_status("internal_error")
                    transaction.set_data("error.type", type(e).__name__)
                    transaction.set_data("error.message", str(e))
                    raise
                    
                finally:
                    if track_performance and start_time:
                        duration = time.time() - start_time
                        transaction.set_data("duration", duration)
                        
                        # Add performance classification
                        if duration < 0.1:
                            transaction.set_tag("performance", "fast")
                        elif duration < 1.0:
                            transaction.set_tag("performance", "normal")
                        elif duration < 5.0:
                            transaction.set_tag("performance", "slow")
                        else:
                            transaction.set_tag("performance", "very_slow")
        
        return cast(F, wrapper)
    
    return decorator


def monitor_performance(
    alert_threshold: float = 5.0,
    capture_slow_only: bool = False,
) -> Callable[[F], F]:
    """
    Decorator to monitor function performance and alert on slow execution.
    
    Args:
        alert_threshold: Seconds after which to create alert
        capture_slow_only: Only capture to Sentry if slow
        
    Returns:
        Decorated function
        
    Example:
        @monitor_performance(alert_threshold=2.0)
        def process_large_dataset(data: list) -> dict:
            # Time-consuming processing
            return results
    """
    def decorator(func: F) -> F:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            start_time = time.time()
            
            try:
                result = func(*args, **kwargs)
                return result
                
            finally:
                duration = time.time() - start_time
                
                # Log performance
                logger.info(
                    "Function executed",
                    function=func.__name__,
                    duration=duration,
                    slow=duration > alert_threshold,
                )
                
                # Send to Sentry if slow or not capture_slow_only
                if duration > alert_threshold or not capture_slow_only:
                    try:
                        import sentry_sdk
                        
                        # Create performance alert
                        with sentry_sdk.push_scope() as scope:
                            scope.set_level("warning" if duration > alert_threshold else "info")
                            scope.set_context("performance", {
                                "function": func.__name__,
                                "module": func.__module__,
                                "duration": duration,
                                "threshold": alert_threshold,
                                "slow": duration > alert_threshold,
                            })
                            
                            if duration > alert_threshold:
                                sentry_sdk.capture_message(
                                    f"Slow function execution: {func.__name__} took {duration:.2f}s",
                                    level="warning",
                                )
                                
                    except ImportError:
                        pass
        
        return cast(F, wrapper)
    
    return decorator


def add_breadcrumb(
    message: str,
    category: str = "function",
    level: str = "info",
    include_args: bool = False,
) -> Callable[[F], F]:
    """
    Decorator to add Sentry breadcrumb when function is called.
    
    Args:
        message: Breadcrumb message
        category: Breadcrumb category
        level: Breadcrumb level
        include_args: Whether to include function arguments
        
    Returns:
        Decorated function
        
    Example:
        @add_breadcrumb("User authentication attempted", category="auth")
        def authenticate_user(username: str, password: str) -> bool:
            # Authentication logic
            return is_authenticated
    """
    def decorator(func: F) -> F:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            try:
                import sentry_sdk
                
                # Prepare breadcrumb data
                breadcrumb_data: dict[str, Any] = {
                    "function": func.__name__,
                    "module": func.__module__,
                }
                
                if include_args:
                    breadcrumb_data["args_count"] = len(args)
                    breadcrumb_data["kwargs_keys"] = list(kwargs.keys())
                
                # Add breadcrumb
                sentry_sdk.add_breadcrumb(
                    category=category,
                    message=message,
                    level=level,
                    data=breadcrumb_data,
                )
                
            except ImportError:
                pass
            
            return func(*args, **kwargs)
        
        return cast(F, wrapper)
    
    return decorator


def set_context(
    context_key: str,
    context_builder: Callable[..., dict[str, Any]] | None = None,
) -> Callable[[F], F]:
    """
    Decorator to set Sentry context before function execution.
    
    Args:
        context_key: Context key for Sentry
        context_builder: Optional function to build context from args/kwargs
        
    Returns:
        Decorated function
        
    Example:
        @set_context("instagram_import", lambda username: {"username": username})
        def import_profile(username: str) -> dict:
            # Import logic
            return profile_data
    """
    def decorator(func: F) -> F:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            try:
                import sentry_sdk
                
                # Build context
                if context_builder:
                    context_data = context_builder(*args, **kwargs)
                else:
                    context_data = {
                        "function": func.__name__,
                        "module": func.__module__,
                    }
                
                # Set context
                sentry_sdk.set_context(context_key, context_data)
                
            except ImportError:
                pass
            except Exception as e:
                logger.warning(
                    "Failed to set Sentry context",
                    error=str(e),
                    context_key=context_key,
                )
            
            return func(*args, **kwargs)
        
        return cast(F, wrapper)
    
    return decorator