"""
Декораторы для маркировки устаревшего функционала
"""

import functools
import warnings
from collections.abc import Callable
from datetime import UTC, datetime

from core.logging import ContextLogger

logger = ContextLogger(__name__)


def deprecated(
    reason: str,
    version: str | None = None,
    removal_version: str | None = None,
    alternative: str | None = None,
    category: type = DeprecationWarning
):
    """
    Декоратор для маркировки устаревших методов и функций.
    
    Args:
        reason: Причина устаревания
        version: Версия, в которой метод стал устаревшим
        removal_version: Версия, в которой метод будет удален
        alternative: Альтернативный метод/функция для использования
        category: Категория предупреждения (по умолчанию DeprecationWarning)
    
    Example:
        @deprecated(
            reason="Метод перемещен в ProfileService",
            version="2.0",
            removal_version="3.0",
            alternative="ProfileService.get_by_username"
        )
        def get_profile(username: str):
            return Profile.objects.get(username=username)
    """
    def decorator(func: Callable) -> Callable:
        # Формируем сообщение об устаревании
        message_parts = [f"{func.__module__}.{func.__name__} is deprecated"]
        
        if version:
            message_parts.append(f"since version {version}")
        
        message_parts.append(f"({reason})")
        
        if alternative:
            message_parts.append(f"Use {alternative} instead")
        
        if removal_version:
            message_parts.append(f"Will be removed in version {removal_version}")
        
        deprecation_message = ". ".join(message_parts) + "."
        
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            # Выводим предупреждение
            warnings.warn(
                deprecation_message,
                category=category,
                stacklevel=2
            )
            
            # Логируем использование deprecated метода
            logger.warning(
                f"Deprecated function called: {func.__name__}",
                extra={
                    "function": f"{func.__module__}.{func.__name__}",
                    "reason": reason,
                    "version": version,
                    "removal_version": removal_version,
                    "alternative": alternative,
                    "timestamp": datetime.now(UTC).isoformat(),
                    # Добавляем информацию о вызывающем коде
                    "caller_info": _get_caller_info()
                }
            )
            
            # Вызываем оригинальную функцию
            return func(*args, **kwargs)
        
        # Добавляем атрибуты для интроспекции
        wrapper.__deprecated__ = True
        wrapper.__deprecated_info__ = {
            "reason": reason,
            "version": version,
            "removal_version": removal_version,
            "alternative": alternative,
            "message": deprecation_message
        }
        
        # Обновляем docstring
        if wrapper.__doc__:
            wrapper.__doc__ = f"**DEPRECATED**: {deprecation_message}\n\n{wrapper.__doc__}"
        else:
            wrapper.__doc__ = f"**DEPRECATED**: {deprecation_message}"
        
        return wrapper
    
    return decorator


def _get_caller_info() -> dict:
    """Получение информации о вызывающем коде"""
    import inspect
    
    try:
        # Получаем стек вызовов
        stack = inspect.stack()
        
        # Пропускаем фреймы декоратора и ищем реальный вызов
        for frame_info in stack[3:]:  # Начинаем с 3-го фрейма
            filename = frame_info.filename
            
            # Пропускаем системные модули
            if not any(skip in filename for skip in ["<", "importlib", "warnings"]):
                return {
                    "filename": filename,
                    "line_number": frame_info.lineno,
                    "function": frame_info.function,
                    "code_context": frame_info.code_context[0].strip() if frame_info.code_context else None
                }
        
        return {}
    except Exception:
        return {}


def pending_deprecation(
    reason: str,
    planned_version: str | None = None,
    alternative: str | None = None
):
    """
    Декоратор для маркировки функционала, который будет устаревшим в будущем.
    
    Args:
        reason: Причина будущего устаревания
        planned_version: Версия, в которой функционал станет устаревшим
        alternative: Рекомендуемая альтернатива
    
    Example:
        @pending_deprecation(
            reason="Будет заменен на async версию",
            planned_version="3.0",
            alternative="get_profile_async"
        )
        def get_profile_sync(username: str):
            return Profile.objects.get(username=username)
    """
    def decorator(func: Callable) -> Callable:
        # Формируем сообщение
        message_parts = [f"{func.__module__}.{func.__name__} will be deprecated"]
        
        if planned_version:
            message_parts.append(f"in version {planned_version}")
        
        message_parts.append(f"({reason})")
        
        if alternative:
            message_parts.append(f"Consider using {alternative}")
        
        pending_message = ". ".join(message_parts) + "."
        
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            # Используем PendingDeprecationWarning
            warnings.warn(
                pending_message,
                category=PendingDeprecationWarning,
                stacklevel=2
            )
            
            # Логируем только в debug режиме
            logger.debug(
                f"Pending deprecation function called: {func.__name__}",
                extra={
                    "function": f"{func.__module__}.{func.__name__}",
                    "reason": reason,
                    "planned_version": planned_version,
                    "alternative": alternative
                }
            )
            
            return func(*args, **kwargs)
        
        # Добавляем атрибуты
        wrapper.__pending_deprecation__ = True
        wrapper.__pending_deprecation_info__ = {
            "reason": reason,
            "planned_version": planned_version,
            "alternative": alternative,
            "message": pending_message
        }
        
        # Обновляем docstring
        if wrapper.__doc__:
            wrapper.__doc__ = f"**PENDING DEPRECATION**: {pending_message}\n\n{wrapper.__doc__}"
        else:
            wrapper.__doc__ = f"**PENDING DEPRECATION**: {pending_message}"
        
        return wrapper
    
    return decorator