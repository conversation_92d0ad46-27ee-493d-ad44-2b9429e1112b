# Декораторы SocialManager

Набор переиспользуемых декораторов для улучшения кода и мониторинга.

## Установленные декораторы

### 1. `@log_execution_time` - Мониторинг производительности

Логирует время выполнения функций и предупреждает о медленных операциях.

```python
from core.decorators import log_execution_time

@log_execution_time(threshold=2.0, log_args=True)
def slow_operation(username: str):
    # Операция, которая может быть медленной
    return fetch_data_from_api(username)
```

**Параметры:**
- `threshold` (float): Порог времени в секундах для логирования (по умолчанию: 1.0)
- `log_args` (bool): Логировать ли аргументы функции (по умолчанию: False)
- `log_result` (bool): Логировать ли результат функции (по умолчанию: False)

### 2. `@cache_result` - Кеширование результатов

Кеширует результаты функций с автоматической генерацией ключей.

```python
from core.decorators import cache_result

@cache_result(ttl=3600, key_prefix="profile")
def get_profile_by_username(username: str):
    return Profile.objects.get(username=username)

# Инвалидация кеша
get_profile_by_username.invalidate_cache("john_doe")

# Очистка всего кеша функции
get_profile_by_username.clear_all_cache()
```

**Параметры:**
- `ttl` (int): Время жизни кеша в секундах (None = бессрочно)
- `key_prefix` (str): Префикс для ключа кеша
- `cache_none` (bool): Кешировать ли None результаты (по умолчанию: False)
- `version` (int): Версия кеша для инвалидации

### 3. `@validate_input` - Валидация входных данных

Валидирует входные данные через Pydantic схемы.

```python
from core.decorators import validate_input
from myapp.schemas import ProfileCreateSchema

@validate_input(ProfileCreateSchema)
def create_profile(**kwargs):
    # kwargs уже провалидированы
    return Profile.objects.create(**kwargs)
```

**Параметры:**
- `schema`: Pydantic схема для валидации
- `source`: Источник данных ('kwargs', 'first_arg', 'all_args')
- `exclude_unset` (bool): Исключить неустановленные поля
- `partial` (bool): Разрешить частичную валидацию

### 4. `@deprecated` - Маркировка устаревших методов

Помечает устаревшие функции и методы.

```python
from core.decorators import deprecated

@deprecated(
    reason="Функция перенесена в ProfileService",
    version="2.0",
    removal_version="3.0",
    alternative="ProfileService.get_by_username"
)
def get_profile(username: str):
    return ProfileService().get_by_username(username)
```

**Параметры:**
- `reason` (str): Причина устаревания
- `version` (str): Версия, в которой метод стал устаревшим
- `removal_version` (str): Версия, в которой метод будет удален
- `alternative` (str): Альтернативный метод для использования

### 5. `@require_permission` - Проверка прав доступа

Проверяет права доступа перед выполнением функции.

```python
from core.decorators import require_permission

@require_permission('instagram_manager.change_profile')
def update_profile(request, profile_id):
    # Только для пользователей с правами
    pass

@require_permission(['view_profile', 'change_profile'])
def manage_profile(request, profile_id):
    # Требуются оба разрешения
    pass
```

**Параметры:**
- `permissions`: Требуемые разрешения (строка или список)
- `check_method`: Кастомный метод проверки прав
- `raise_exception` (bool): Бросать исключение при отсутствии прав

### 6. `@profile_method` - Профилирование с отслеживанием памяти

Детальное профилирование с опциональным отслеживанием памяти.

```python
from core.decorators import profile_method

@profile_method(trace_memory=True, log_top_stats=5)
def memory_intensive_operation():
    # Операция с большим использованием памяти
    data = process_large_dataset()
    return data
```

**Параметры:**
- `trace_memory` (bool): Отслеживать использование памяти
- `log_top_stats` (int): Количество топ операций для логирования

## Примеры использования

### В API клиентах

```python
from core.decorators import log_execution_time, cache_result
from core.utils.retry import RetryStrategy

class BrightDataClient:
    @log_execution_time(threshold=2.0, log_args=True)
    @RetryStrategy.exponential_backoff(
        max_retries=3,
        exceptions=(RequestException, RateLimitError)
    )
    def get_profile(self, username: str):
        return self._make_request('GET', f'/profiles/{username}')
```

### В сервисах

```python
from core.decorators import validate_input, log_execution_time

class ProfileService:
    @log_execution_time(threshold=1.0)
    @validate_input(ProfileCreateSchema)
    def create_profile(self, **kwargs):
        # Данные уже провалидированы
        return self.repository.create(**kwargs)
```

### В репозиториях

```python
from core.decorators import cache_result, log_execution_time

class ProfileRepository:
    @cache_result(ttl=3600, key_prefix="profile")
    @log_execution_time(threshold=0.5)
    def get_by_username(self, username: str):
        return self.model.objects.get(username=username)
    
    def update(self, pk, **kwargs):
        # Обновляем профиль
        profile = super().update(pk, **kwargs)
        
        # Инвалидируем кеш
        if profile and 'username' in kwargs:
            self.get_by_username.invalidate_cache(kwargs['username'])
        
        return profile
```

### Deprecated функции

```python
from core.decorators import deprecated

@deprecated(
    reason="Используйте CommonValidators.clean_text",
    version="2.0",
    alternative="CommonValidators.clean_text"
)
def clean_text(text: str) -> str:
    return CommonValidators.clean_text(text)
```

## Async декораторы

```python
from core.decorators import async_to_sync_django, sync_to_async_django

# Преобразование async в sync
@async_to_sync_django()
async def fetch_external_data(url: str):
    async with aiohttp.ClientSession() as session:
        async with session.get(url) as response:
            return await response.json()

# Преобразование sync в async
@sync_to_async_django()
def get_user_from_db(user_id: int):
    return User.objects.get(id=user_id)

# Использование
data = fetch_external_data('https://api.example.com')  # Синхронный вызов
user = await get_user_from_db(123)  # Асинхронный вызов
```

## Best Practices

1. **Комбинируйте декораторы** для максимальной эффективности:
   ```python
   @log_execution_time(threshold=1.0)
   @cache_result(ttl=600)
   @validate_input(MySchema)
   def complex_operation(**kwargs):
       pass
   ```

2. **Используйте правильный порядок** декораторов:
   - Сначала мониторинг (`@log_execution_time`)
   - Затем кеширование (`@cache_result`)
   - Потом валидация (`@validate_input`)

3. **Инвалидируйте кеш** при изменении данных:
   ```python
   def update_profile(self, username, **data):
       result = self._update(username, **data)
       self.get_by_username.invalidate_cache(username)
       return result
   ```

4. **Логируйте аргументы** только для отладки:
   ```python
   @log_execution_time(threshold=2.0, log_args=DEBUG)
   ```

5. **Версионируйте кеш** при изменении логики:
   ```python
   @cache_result(ttl=3600, version=2)  # Увеличьте версию
   ```