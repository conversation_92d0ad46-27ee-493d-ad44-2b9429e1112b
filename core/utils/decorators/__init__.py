"""
Декораторы для общей функциональности проекта
"""

from .async_utils import async_to_sync_django, sync_to_async_django
from .deprecation import deprecated
from .permissions import require_authenticated, require_group, require_permission, require_superuser
from .sentry_decorators import (
    add_breadcrumb,
    capture_errors,
    monitor_performance,
    set_context,
    track_transaction,
)
from .validation import validate_input

__all__ = [
    "add_breadcrumb",
    "async_to_sync_django",
    "capture_errors",
    "deprecated",
    "monitor_performance",
    "require_authenticated",
    "require_group",
    "require_permission",
    "require_superuser",
    "set_context",
    "sync_to_async_django",
    "track_transaction",
    "validate_input",
]