"""
Permission checking decorators.
"""

import functools
import logging
from collections.abc import Callable

from django.contrib.auth.decorators import user_passes_test
from django.core.exceptions import PermissionDenied

logger = logging.getLogger(__name__)


def require_permission(
    permission: str | list[str], 
    raise_exception: bool = True,
    redirect_field_name: str = "next",
    login_url: str | None = None,
    check_method: Callable | None = None
):
    """
    Decorator to check if user has required permission(s).
    
    Args:
        permission: Single permission string or list of permissions
        raise_exception: If True, raise PermissionDenied, else redirect
        redirect_field_name: Field name for redirect URL
        login_url: URL to redirect if user lacks permission
        check_method: Optional custom permission check method
        
    Usage:
        @require_permission('instagram_manager.change_instagramprofile')
        def update_profile(self, profile_id: int, **kwargs):
            ...
            
        @require_permission(['app.add_model', 'app.change_model'])
        def manage_model(self, **kwargs):
            ...
            
        @require_permission('admin_only', check_method=lambda u, p: u.username == 'admin')
        def admin_action(self):
            ...
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            # Extract user from request or self
            user = None
            request = None
            
            # Check if this is a view method (has self with request)
            if args and hasattr(args[0], "request"):
                request = args[0].request
                user = request.user
            # Check if first arg is request
            elif args and hasattr(args[0], "user"):
                request = args[0]
                user = request.user
            # Check kwargs for request
            elif "request" in kwargs:
                request = kwargs["request"]
                user = request.user if request else None
            # Check if first arg is a user object (for custom tests)
            elif args and hasattr(args[0], "username"):
                user = args[0]
            # Check kwargs for user
            elif "user" in kwargs:
                user = kwargs["user"]
            
            if not user:
                logger.warning(
                    f"No user found for permission check in {func.__module__}.{func.__name__}"
                )
                if raise_exception:
                    raise PermissionDenied("No user available for permission check")
                return None
            
            # Normalize permissions to list
            permissions = permission if isinstance(permission, list) else [permission]
            
            # Check permissions
            if check_method:
                # Use custom check method
                has_permission = check_method(user, permissions)
            else:
                # Use Django's built-in permission check
                has_permission = all(user.has_perm(perm) for perm in permissions)
            
            if not has_permission:
                logger.warning(
                    f"User {user} lacks permission(s) {permissions} for "
                    f"{func.__module__}.{func.__name__}"
                )
                
                if raise_exception:
                    raise PermissionDenied(
                        f"User lacks required permission(s): {', '.join(permissions)}"
                    )
                    
                # Use Django's user_passes_test for redirect behavior
                if request:
                    def test_func(u):
                        return all(u.has_perm(p) for p in permissions)
                    
                    decorator = user_passes_test(
                        test_func,
                        login_url=login_url,
                        redirect_field_name=redirect_field_name
                    )
                    return decorator(func)(*args, **kwargs)
                
                return None
            
            # Log successful permission check
            logger.debug(
                f"Permission check passed for user {user} on "
                f"{func.__module__}.{func.__name__}"
            )
            
            return func(*args, **kwargs)
            
        return wrapper
    return decorator


def require_superuser(raise_exception: bool = True):
    """
    Decorator to check if user is superuser.
    
    Args:
        raise_exception: If True, raise PermissionDenied, else return None
        
    Usage:
        @require_superuser()
        def admin_only_action(self):
            ...
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            # Extract user (same logic as require_permission)
            user = None
            
            if args and hasattr(args[0], "request"):
                user = args[0].request.user
            elif args and hasattr(args[0], "user"):
                user = args[0].user
            elif kwargs.get("request"):
                user = kwargs["request"].user
            
            if not user or not user.is_superuser:
                logger.warning(
                    f"Superuser check failed for {func.__module__}.{func.__name__}"
                )
                
                if raise_exception:
                    raise PermissionDenied("Superuser access required")
                return None
            
            return func(*args, **kwargs)
            
        return wrapper
    return decorator


def require_group(group_name: str, raise_exception: bool = True):
    """
    Decorator to check if user belongs to a specific group.
    
    Args:
        group_name: Name of the required group
        raise_exception: If True, raise PermissionDenied, else return None
        
    Usage:
        @require_group('editors')
        def edit_content(self, **kwargs):
            ...
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            # Extract user
            user = None
            
            if args and hasattr(args[0], "request"):
                user = args[0].request.user
            elif args and hasattr(args[0], "user"):
                user = args[0].user
            elif kwargs.get("request"):
                user = kwargs["request"].user
            
            if not user:
                if raise_exception:
                    raise PermissionDenied("No user available for group check")
                return None
            
            # Check group membership
            if not user.groups.filter(name=group_name).exists():
                logger.warning(
                    f"User {user} not in group '{group_name}' for "
                    f"{func.__module__}.{func.__name__}"
                )
                
                if raise_exception:
                    raise PermissionDenied(f"User must be in group '{group_name}'")
                return None
            
            return func(*args, **kwargs)
            
        return wrapper
    return decorator


def require_authenticated(raise_exception: bool = True):
    """
    Decorator to check if user is authenticated.
    
    Args:
        raise_exception: If True, raise PermissionDenied, else return None
        
    Usage:
        @require_authenticated()
        def user_profile(self):
            ...
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            # Extract user
            user = None
            
            if args and hasattr(args[0], "request"):
                user = args[0].request.user
            elif args and hasattr(args[0], "user"):
                user = args[0].user
            elif kwargs.get("request"):
                user = kwargs["request"].user
            
            if not user or not user.is_authenticated:
                logger.warning(
                    f"Authentication check failed for {func.__module__}.{func.__name__}"
                )
                
                if raise_exception:
                    raise PermissionDenied("Authentication required")
                return None
            
            return func(*args, **kwargs)
            
        return wrapper
    return decorator