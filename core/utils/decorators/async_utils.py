"""
Декораторы для работы с асинхронным кодом
"""

import asyncio
import functools
from collections.abc import Callable
from concurrent.futures import ThreadPoolExecutor
from typing import Any

from asgiref.sync import async_to_sync, sync_to_async
from django.db import close_old_connections

from core.logging import ContextLogger

logger = ContextLogger(__name__)


# Глобальный пул потоков для async операций
_thread_pool = ThreadPoolExecutor(max_workers=10, thread_name_prefix="async_decorator")


def async_to_sync_django(force_new_loop: bool = False):
    """
    Декоратор для преобразования async функций в sync с поддержкой Django ORM.
    
    Args:
        force_new_loop: Принудительно создавать новый event loop
    
    Example:
        @async_to_sync_django()
        async def fetch_external_data(url: str):
            async with aiohttp.ClientSession() as session:
                async with session.get(url) as response:
                    return await response.json()
                    
        # Можно вызывать синхронно
        data = fetch_external_data('https://api.example.com/data')
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            function_name = f"{func.__module__}.{func.__name__}"
            
            try:
                # Закрываем старые соединения Django перед async операцией
                close_old_connections()
                
                if force_new_loop:
                    # Создаем новый event loop в отдельном потоке
                    def run_in_new_loop():
                        loop = asyncio.new_event_loop()
                        asyncio.set_event_loop(loop)
                        try:
                            return loop.run_until_complete(func(*args, **kwargs))
                        finally:
                            loop.close()
                    
                    import concurrent.futures
                    with concurrent.futures.ThreadPoolExecutor(max_workers=1) as executor:
                        future = executor.submit(run_in_new_loop)
                        result = future.result()
                else:
                    # Используем стандартный async_to_sync от Django
                    sync_func = async_to_sync(func)
                    result = sync_func(*args, **kwargs)
                
                logger.debug(
                    f"Async function {function_name} executed synchronously",
                    extra={"function": function_name}
                )
                
                return result
                
            except Exception as e:
                logger.exception(
                    f"Error executing async function {function_name} synchronously",
                    extra={
                        "function": function_name,
                        "error": str(e),
                        "error_type": type(e).__name__
                    }
                )
                raise
            finally:
                # Закрываем соединения после async операции
                close_old_connections()
        
        # Сохраняем ссылку на оригинальную async функцию
        wrapper.__async_original__ = func
        wrapper.__is_async_to_sync__ = True
        
        return wrapper
    
    return decorator


def sync_to_async_django(thread_sensitive: bool = True, executor: ThreadPoolExecutor | None = None):
    """
    Декоратор для преобразования sync функций в async с поддержкой Django ORM.
    
    Args:
        thread_sensitive: Выполнять в том же потоке (для Django ORM)
        executor: Кастомный executor для выполнения
    
    Example:
        @sync_to_async_django()
        def get_user_from_db(user_id: int):
            return User.objects.get(id=user_id)
            
        # Можно вызывать асинхронно
        user = await get_user_from_db(123)
    """
    def decorator(func: Callable) -> Callable:
        # Используем переданный executor или глобальный
        exec_pool = executor or _thread_pool
        
        @functools.wraps(func)
        async def wrapper(*args, **kwargs):
            function_name = f"{func.__module__}.{func.__name__}"
            
            try:
                if thread_sensitive:
                    # Используем Django sync_to_async для thread-sensitive операций
                    async_func = sync_to_async(func, thread_sensitive=True)
                    result = await async_func(*args, **kwargs)
                else:
                    # Выполняем в отдельном потоке
                    loop = asyncio.get_event_loop()
                    result = await loop.run_in_executor(
                        exec_pool,
                        functools.partial(func, *args, **kwargs)
                    )
                
                logger.debug(
                    f"Sync function {function_name} executed asynchronously",
                    extra={"function": function_name, "thread_sensitive": thread_sensitive}
                )
                
                return result
                
            except Exception as e:
                logger.exception(
                    f"Error executing sync function {function_name} asynchronously",
                    extra={
                        "function": function_name,
                        "error": str(e),
                        "error_type": type(e).__name__
                    }
                )
                raise
        
        # Сохраняем ссылку на оригинальную sync функцию
        wrapper.__sync_original__ = func
        wrapper.__is_sync_to_async__ = True
        
        return wrapper
    
    return decorator


def ensure_async_context(create_new_loop: bool = False):
    """
    Декоратор, гарантирующий выполнение функции в async контексте.
    
    Args:
        create_new_loop: Создавать новый event loop если его нет
    
    Example:
        @ensure_async_context()
        async def process_data():
            # Эта функция всегда будет выполняться в async контексте
            await asyncio.sleep(1)
            return "processed"
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            try:
                # Проверяем, есть ли активный event loop
                loop = asyncio.get_running_loop()
                # Если есть, просто вызываем функцию
                return func(*args, **kwargs)
            except RuntimeError:
                # Нет активного event loop
                if create_new_loop:
                    # Создаем новый loop и выполняем функцию
                    loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(loop)
                    try:
                        if asyncio.iscoroutinefunction(func):
                            return loop.run_until_complete(func(*args, **kwargs))
                        else:
                            return func(*args, **kwargs)
                    finally:
                        loop.close()
                else:
                    # Преобразуем в sync версию
                    if asyncio.iscoroutinefunction(func):
                        sync_func = async_to_sync(func)
                        return sync_func(*args, **kwargs)
                    else:
                        return func(*args, **kwargs)
        
        return wrapper
    
    return decorator


def run_async_in_thread(daemon: bool = True):
    """
    Декоратор для запуска async функции в отдельном потоке.
    Полезно для Django admin и других sync контекстов.
    
    Args:
        daemon: Делать поток демоном
    
    Example:
        @run_async_in_thread()
        async def import_telegram_data():
            client = TelegramClient(...)
            await client.connect()
            # ... async операции
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            import threading
            
            result_container: dict[str, Any] = {"result": None, "exception": None}
            
            def run_async():
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                try:
                    result = loop.run_until_complete(func(*args, **kwargs))
                    result_container["result"] = result
                except Exception as e:
                    result_container["exception"] = e
                finally:
                    loop.close()
            
            thread = threading.Thread(target=run_async, daemon=daemon)
            thread.start()
            thread.join()
            
            if result_container["exception"]:
                raise result_container["exception"]
            
            return result_container["result"]
        
        return wrapper
    
    return decorator