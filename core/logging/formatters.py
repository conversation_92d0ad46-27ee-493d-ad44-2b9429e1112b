"""
Custom logging formatters for structured logging.

This module provides JSON formatter for production logging
with structured output suitable for log aggregation systems.
"""

from __future__ import annotations

import json
import logging
import traceback
from datetime import UTC, datetime
from typing import TYPE_CHECKING, Any

if TYPE_CHECKING:
    from logging import LogRecord


class JSONFormatter(logging.Formatter):
    """
    JSON formatter for structured logging.
    
    Outputs log records as JSON with consistent structure,
    making them suitable for log aggregation and analysis tools.
    """
    
    def __init__(
        self,
        *,
        include_traceback: bool = True,
        include_extra: bool = True,
        timestamp_format: str = "iso",
    ) -> None:
        """
        Initialize JSON formatter.
        
        Args:
            include_traceback: Whether to include exception tracebacks
            include_extra: Whether to include extra fields from log record
            timestamp_format: Format for timestamps ('iso' or 'unix')
        """
        super().__init__()
        self.include_traceback = include_traceback
        self.include_extra = include_extra
        self.timestamp_format = timestamp_format
        
        # Standard fields to exclude from extra
        self._reserved_fields = {
            "name", "msg", "args", "created", "filename", "funcName",
            "levelname", "levelno", "lineno", "module", "msecs",
            "pathname", "process", "processName", "relativeCreated",
            "thread", "threadName", "exc_info", "exc_text", "stack_info",
            "getMessage", "message"
        }
    
    def format(self, record: LogRecord) -> str:
        """
        Format log record as JSON.
        
        Args:
            record: Log record to format
            
        Returns:
            JSON-formatted log entry
        """
        # Build base log structure
        log_obj: dict[str, Any] = {
            "timestamp": self._format_timestamp(record),
            "level": record.levelname,
            "logger": record.name,
            "message": record.getMessage(),
            "module": record.module,
            "function": record.funcName,
            "line": record.lineno,
            "thread": record.threadName,
            "process": record.processName,
        }
        
        # Add exception info if present
        if record.exc_info and self.include_traceback:
            log_obj["exception"] = {
                "type": record.exc_info[0].__name__,
                "message": str(record.exc_info[1]),
                "traceback": self._format_exception(record.exc_info)
            }
        
        # Add extra fields if enabled
        if self.include_extra:
            extra_fields = {
                k: v for k, v in record.__dict__.items()
                if k not in self._reserved_fields
            }
            if extra_fields:
                log_obj["extra"] = extra_fields
        
        # Ensure JSON is serializable
        return json.dumps(log_obj, default=self._json_default, ensure_ascii=False)
    
    def _format_timestamp(self, record: LogRecord) -> str | float:
        """
        Format timestamp based on configured format.
        
        Args:
            record: Log record containing timestamp
            
        Returns:
            Formatted timestamp
        """
        if self.timestamp_format == "unix":
            return record.created
        else:  # ISO format
            dt = datetime.fromtimestamp(record.created, tz=UTC)
            return dt.isoformat()
    
    def _format_exception(self, exc_info: tuple) -> list[str]:
        """
        Format exception traceback.
        
        Args:
            exc_info: Exception info tuple
            
        Returns:
            List of traceback lines
        """
        return traceback.format_exception(*exc_info)
    
    def _json_default(self, obj: Any) -> Any:
        """
        JSON serialization fallback for non-serializable objects.
        
        Args:
            obj: Object to serialize
            
        Returns:
            Serializable representation
        """
        # Handle datetime objects
        if isinstance(obj, datetime):
            return obj.isoformat()
        
        # Handle Django model instances
        if hasattr(obj, "pk"):
            return f"{obj.__class__.__name__}(pk={obj.pk})"
        
        # Handle sets
        if isinstance(obj, set):
            return list(obj)
        
        # Fallback to string representation
        try:
            return str(obj)
        except Exception:
            return f"<{obj.__class__.__name__}: unserializable>"


class ColoredFormatter(logging.Formatter):
    """
    Colored formatter for development console output.
    
    Adds ANSI color codes to log output for better readability
    in terminal environments.
    """
    
    # Color codes for different log levels
    COLORS = {
        "DEBUG": "\033[36m",     # Cyan
        "INFO": "\033[32m",      # Green
        "WARNING": "\033[33m",   # Yellow
        "ERROR": "\033[31m",     # Red
        "CRITICAL": "\033[35m",  # Magenta
    }
    RESET = "\033[0m"
    
    def __init__(self, fmt: str | None = None, datefmt: str | None = None):
        """
        Initialize colored formatter.
        
        Args:
            fmt: Log message format
            datefmt: Date format
        """
        if fmt is None:
            fmt = "%(levelname)s %(asctime)s %(name)s %(message)s"
        super().__init__(fmt, datefmt)
    
    def format(self, record: LogRecord) -> str:
        """
        Format log record with color codes.
        
        Args:
            record: Log record to format
            
        Returns:
            Colored log message
        """
        # Save original levelname
        levelname = record.levelname
        
        # Add color to levelname
        if levelname in self.COLORS:
            record.levelname = f"{self.COLORS[levelname]}{levelname}{self.RESET}"
        
        # Format the record
        result = super().format(record)
        
        # Restore original levelname
        record.levelname = levelname
        
        return result