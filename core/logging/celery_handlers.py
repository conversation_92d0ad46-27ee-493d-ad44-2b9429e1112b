"""
Celery signal handlers for automatic logging context injection.

This module sets up signal handlers that automatically inject
task context into logs for all Celery tasks.
"""

from typing import Any

from celery import signals
from celery.app.task import Task

from core.logging.logger import (
    ContextLogger,
    add_context,
    clear_context,
    set_task_id,
)

logger = ContextLogger.get_logger(__name__)


@signals.task_prerun.connect
def setup_task_logging_context(
    sender: Task | None = None,
    task_id: str | None = None,
    task: Task | None = None,
    args: tuple | None = None,
    kwargs: dict[str, Any] | None = None,
    **signal_kwargs
) -> None:
    """
    Set up logging context before task execution.
    
    This handler:
    - Sets task_id in context
    - Adds task metadata
    - Logs task start
    
    Args:
        sender: The task class being executed
        task_id: Unique id of the task instance
        task: The task instance
        args: Positional arguments passed to the task
        kwargs: Keyword arguments passed to the task
        **signal_kwargs: Additional signal arguments
    """
    # Clear any existing context
    clear_context()
    
    # Set task_id
    if task_id:
        set_task_id(task_id)
    
    # Add task metadata
    task_name = task.name if task else (sender.name if sender else "unknown")
    
    add_context(
        task_name=task_name,
        task_args_count=len(args) if args else 0,
        task_kwargs_keys=list(kwargs.keys()) if kwargs else [],
    )
    
    # Log task start
    logger.info(
        "Task started",
        task_name=task_name,
        task_id=task_id,
    )


@signals.task_postrun.connect
def cleanup_task_logging_context(
    sender: Task | None = None,
    task_id: str | None = None,
    task: Task | None = None,
    args: tuple | None = None,
    kwargs: dict[str, Any] | None = None,
    retval: Any | None = None,
    state: str | None = None,
    **signal_kwargs
) -> None:
    """
    Clean up logging context after task execution.
    
    This handler:
    - Logs task completion
    - Clears task context
    
    Args:
        sender: The task class that was executed
        task_id: Unique id of the task instance
        task: The task instance
        args: Positional arguments passed to the task
        kwargs: Keyword arguments passed to the task
        retval: Task return value
        state: Final state of the task (SUCCESS, FAILURE, etc.)
        **signal_kwargs: Additional signal arguments
    """
    task_name = task.name if task else (sender.name if sender else "unknown")
    
    # Log task completion
    logger.info(
        "Task completed",
        task_name=task_name,
        task_id=task_id,
        state=state,
    )
    
    # Clear context
    clear_context()


@signals.task_failure.connect
def log_task_failure(
    sender: Task | None = None,
    task_id: str | None = None,
    exception: Exception | None = None,
    args: tuple | None = None,
    kwargs: dict[str, Any] | None = None,
    traceback: Any | None = None,
    einfo: Any | None = None,
    **signal_kwargs
) -> None:
    """
    Log task failure with exception details.
    
    Args:
        sender: The task class that failed
        task_id: Unique id of the task instance
        exception: The exception raised
        args: Positional arguments passed to the task
        kwargs: Keyword arguments passed to the task
        traceback: Exception traceback
        einfo: Exception info
        **signal_kwargs: Additional signal arguments
    """
    task_name = sender.name if sender else "unknown"
    
    # Add exception context
    add_context(
        exception_type=type(exception).__name__ if exception else None,
        exception_message=str(exception) if exception else None,
    )
    
    # Log failure
    logger.error(
        "Task failed",
        task_name=task_name,
        task_id=task_id,
        exception_type=type(exception).__name__ if exception else None,
        exception_message=str(exception) if exception else None,
        exc_info=einfo,
    )


@signals.task_retry.connect
def log_task_retry(
    sender: Task | None = None,
    task_id: str | None = None,
    reason: Exception | None = None,
    **signal_kwargs
) -> None:
    """
    Log task retry attempt.
    
    Args:
        sender: The task class being retried
        task_id: Unique id of the task instance
        reason: The exception that caused the retry
        **signal_kwargs: Additional signal arguments
    """
    task_name = sender.name if sender else "unknown"
    
    # Log retry
    logger.warning(
        "Task retry",
        task_name=task_name,
        task_id=task_id,
        retry_reason=str(reason) if reason else None,
    )


@signals.task_revoked.connect
def log_task_revoked(
    sender: Task | None = None,
    request: Any | None = None,
    terminated: bool = False,
    signum: int | None = None,
    expired: bool = False,
    **signal_kwargs
) -> None:
    """
    Log task revocation.
    
    Args:
        sender: The task class that was revoked
        request: Task request object
        terminated: Whether task was terminated
        signum: Signal number used to terminate
        expired: Whether task expired
        **signal_kwargs: Additional signal arguments
    """
    task_id = request.id if request else None
    task_name = request.task if request else (sender.name if sender else "unknown")
    
    # Log revocation
    logger.warning(
        "Task revoked",
        task_name=task_name,
        task_id=task_id,
        terminated=terminated,
        expired=expired,
    )


def setup_celery_logging() -> None:
    """
    Setup Celery logging configuration.
    
    This function should be called during Celery app initialization
    to ensure all signal handlers are connected.
    """
    # Signal handlers are already connected when module is imported
    pass