"""
Compatibility layer for ContextLogger migration.

This module provides backward compatibility for code that still uses logging.getLogger()
by monkey-patching the logging module to return ContextLogger instances instead.

Usage:
    # Enable compatibility mode in your Django settings or app initialization
    from core.logging.compat import enable_compatibility_mode
    enable_compatibility_mode()

Warning:
    This is a transitional solution. All code should be migrated to use
    get_logger() directly for better type hints and explicit dependencies.
"""

import logging
import warnings
from collections.abc import Callable
from functools import wraps
from typing import Any

from core.logging import ContextLogger, get_logger

# Store the original getLogger function
_original_getLogger = logging.getLogger
_compatibility_enabled = False
_deprecation_warnings_enabled = True


class CompatibilityWarning(UserWarning):
    """Warning issued when using compatibility mode."""
    pass


def _wrapped_getLogger(name: str | None = None) -> Any:
    """
    Wrapped version of logging.getLogger that returns ContextLogger.
    
    This function is used to replace logging.getLogger when compatibility
    mode is enabled.
    """
    if name is None:
        name = "root"
    
    # Issue deprecation warning if enabled
    if _deprecation_warnings_enabled:
        warnings.warn(
            f"logging.getLogger('{name}') is deprecated. "
            f"Use 'from core.logging import get_logger; logger = get_logger(__name__)' instead.",
            CompatibilityWarning,
            stacklevel=2
        )
    
    # Return ContextLogger instance
    return get_logger(name)


def enable_compatibility_mode(show_warnings: bool = True) -> None:
    """
    Enable compatibility mode by monkey-patching logging.getLogger.
    
    Args:
        show_warnings: Whether to show deprecation warnings (default: True)
    
    This replaces logging.getLogger with a wrapper that returns
    ContextLogger instances instead of standard Logger instances.
    
    Warning:
        This modifies the global logging module and affects all code
        in the process. Use with caution.
    """
    global _compatibility_enabled, _deprecation_warnings_enabled
    
    if _compatibility_enabled:
        return
    
    _deprecation_warnings_enabled = show_warnings
    
    # Monkey-patch logging.getLogger
    logging.getLogger = _wrapped_getLogger
    _compatibility_enabled = True
    
    # Log that compatibility mode is enabled
    logger = get_logger(__name__)
    logger.info(
        "ContextLogger compatibility mode enabled",
        show_warnings=show_warnings
    )


def disable_compatibility_mode() -> None:
    """
    Disable compatibility mode and restore original logging.getLogger.
    
    This restores the original logging.getLogger function.
    """
    global _compatibility_enabled
    
    if not _compatibility_enabled:
        return
    
    # Restore original function
    logging.getLogger = _original_getLogger
    _compatibility_enabled = False
    
    # Log that compatibility mode is disabled
    logger = get_logger(__name__)
    logger.info("ContextLogger compatibility mode disabled")


def is_compatibility_enabled() -> bool:
    """Check if compatibility mode is currently enabled."""
    return _compatibility_enabled


def suppress_deprecation_warnings() -> None:
    """Suppress deprecation warnings in compatibility mode."""
    global _deprecation_warnings_enabled
    _deprecation_warnings_enabled = False


def enable_deprecation_warnings() -> None:
    """Enable deprecation warnings in compatibility mode."""
    global _deprecation_warnings_enabled
    _deprecation_warnings_enabled = True


class compatibility_context:
    """
    Context manager for temporary compatibility mode.
    
    Example:
        with compatibility_context():
            # Code here can use logging.getLogger() and get ContextLogger
            import some_third_party_library
            some_third_party_library.do_something()
    """
    
    def __init__(self, show_warnings: bool = False):
        self.show_warnings = show_warnings
        self.was_enabled = False
        self.previous_warning_state = True
    
    def __enter__(self):
        self.was_enabled = is_compatibility_enabled()
        self.previous_warning_state = _deprecation_warnings_enabled
        
        if not self.was_enabled:
            enable_compatibility_mode(show_warnings=self.show_warnings)
        elif not self.show_warnings:
            suppress_deprecation_warnings()
        
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        if not self.was_enabled:
            disable_compatibility_mode()
        elif self.previous_warning_state and not self.show_warnings:
            enable_deprecation_warnings()


def with_compatibility(show_warnings: bool = False):
    """
    Decorator to enable compatibility mode for a specific function.
    
    Args:
        show_warnings: Whether to show deprecation warnings
    
    Example:
        @with_compatibility()
        def legacy_function():
            # This function can use logging.getLogger()
            logger = logging.getLogger(__name__)
            logger.info("This works with ContextLogger")
    """
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(*args, **kwargs):
            with compatibility_context(show_warnings=show_warnings):
                return func(*args, **kwargs)
        return wrapper
    return decorator


# Utility functions for migration assistance

def check_logger_type(logger_instance) -> str:
    """
    Check the type of a logger instance.
    
    Returns:
        "context" if it's a ContextLogger
        "standard" if it's a standard Logger
        "unknown" otherwise
    """
    if isinstance(logger_instance, ContextLogger):
        return "context"
    elif isinstance(logger_instance, logging.Logger):
        return "standard"
    else:
        return "unknown"


def migrate_logger_config(config: dict) -> dict:
    """
    Migrate a logging configuration dict to use ContextLogger.
    
    This modifies logger class references in the configuration.
    
    Args:
        config: Logging configuration dictionary
        
    Returns:
        Modified configuration dictionary
    """
    # Deep copy to avoid modifying original
    import copy
    new_config = copy.deepcopy(config)
    
    # Update logger classes
    if "loggers" in new_config:
        for logger_name, logger_config in new_config["loggers"].items():
            if "class" not in logger_config:
                # Add ContextLogger class
                logger_config["class"] = "core.logging.ContextLogger"
    
    return new_config


# Auto-enable compatibility if environment variable is set
import os  # noqa: E402

if os.environ.get("CONTEXTLOGGER_COMPATIBILITY", "").lower() in ("1", "true", "yes", "on"):
    enable_compatibility_mode(
        show_warnings=os.environ.get("CONTEXTLOGGER_SHOW_WARNINGS", "").lower() in ("1", "true", "yes", "on")
    )