"""
Testing utilities for the logging system.

This module provides:
- Log capture fixtures
- Asser<PERSON> helpers
- Mock handlers for testing
"""

import logging


class LogCapture:
    """Utility for capturing logs in tests."""
    
    def __init__(self):
        self.records: list[logging.LogRecord] = []
        self.messages: list[str] = []
        
    def capture(self, record: logging.LogRecord) -> None:
        """Capture a log record."""
        self.records.append(record)
        self.messages.append(record.getMessage())
        
    def reset(self) -> None:
        """Clear captured logs."""
        self.records.clear()
        self.messages.clear()
        
    def assert_logged(self, message: str, level: int | None = None) -> None:
        """Assert that a message was logged."""
        if message not in self.messages:
            raise AssertionError(f"Message '{message}' not found in logs")
            
        if level is not None:
            for record in self.records:
                if record.getMessage() == message and record.levelno != level:
                    raise AssertionError(
                        f"Message '{message}' logged with level {record.levelname}, "
                        f"expected {logging.getLevelName(level)}"
                    )