"""
Decorators for automatic logging of function calls and performance.

These decorators provide automatic logging for:
- API endpoint calls with request/response details
- Celery task execution with timing and results
- Slow method detection with performance warnings
"""

import functools
import time
from collections.abc import Callable
from typing import Any, TypeVar, cast

from django.http import HttpRequest, HttpResponse

from core.logging.logger import ContextLogger, add_context, context_logging

# Type variable for generic function decoration
F = TypeVar("F", bound=Callable[..., Any])


def log_api_call(
    logger: ContextLogger | None = None,
    log_request_body: bool = True,
    log_response_body: bool = True,
    max_body_length: int = 1000
) -> Callable[[F], F]:
    """
    Decorator for logging API endpoint calls.
    
    Automatically logs:
    - Request method, path, headers
    - Request body (for POST/PUT/PATCH)
    - Response status code and timing
    - Any exceptions that occur
    
    Args:
        logger: Logger instance (if None, creates one from function module)
        log_request_body: Whether to log request body
        log_response_body: Whether to log response body
        max_body_length: Maximum length of body to log
        
    Example:
        @log_api_call()
        def my_api_view(request):
            return JsonResponse({'status': 'ok'})
    """
    def decorator(func: F) -> F:
        # Get logger if not provided
        nonlocal logger
        if logger is None:
            logger = ContextLogger.get_logger(func.__module__)
        
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            # Find request in args
            request = None
            for arg in args:
                if isinstance(arg, HttpRequest):
                    request = arg
                    break
            
            # Extract request details
            request_data: dict[str, Any] = {}
            if request:
                request_data = {
                    "method": request.method,
                    "path": request.path,
                    "query_params": dict(request.GET),
                    "content_type": request.content_type,
                }
                
                # Log request body for POST/PUT/PATCH
                if log_request_body and request.method in ["POST", "PUT", "PATCH"]:
                    try:
                        body = request.body.decode("utf-8")
                        if len(body) > max_body_length:
                            body = body[:max_body_length] + "...(truncated)"
                        request_data["body"] = body
                    except Exception:
                        request_data["body"] = "<binary or unparseable>"
            
            # Log API call start
            logger.info(
                f"API call started: {func.__name__}",
                **request_data
            )
            
            start_time = time.time()
            
            try:
                # Call the actual function
                result = func(*args, **kwargs)
                
                # Log response details
                duration_ms = int((time.time() - start_time) * 1000)
                response_data: dict[str, Any] = {
                    "duration_ms": duration_ms,
                    "success": True,
                }
                
                if isinstance(result, HttpResponse):
                    response_data["status_code"] = result.status_code
                    
                    # Optionally log response body
                    if log_response_body and hasattr(result, "content"):
                        try:
                            content = result.content.decode("utf-8")
                            if len(content) > max_body_length:
                                content = content[:max_body_length] + "...(truncated)"
                            response_data["response_body"] = content
                        except Exception:
                            response_data["response_body"] = "<binary or unparseable>"
                
                logger.info(
                    f"API call completed: {func.__name__}",
                    **response_data
                )
                
                return result
                
            except Exception as e:
                duration_ms = int((time.time() - start_time) * 1000)
                
                logger.exception(
                    f"API call failed: {func.__name__}",
                    duration_ms=duration_ms,
                    exception_type=type(e).__name__,
                    exception_message=str(e),
                    **request_data
                )
                
                raise
                
        return cast(F, wrapper)
    return decorator


def log_task_execution(
    logger: ContextLogger | None = None,
    log_arguments: bool = True,
    log_result: bool = True,
    track_progress: bool = True
) -> Callable[[F], F]:
    """
    Decorator for logging Celery task execution.
    
    Automatically logs:
    - Task start with arguments
    - Task completion with result and timing
    - Task failures with exceptions
    - Progress updates (if task uses update_progress)
    
    Args:
        logger: Logger instance (if None, creates task logger)
        log_arguments: Whether to log task arguments
        log_result: Whether to log task result
        track_progress: Whether to track progress updates
        
    Example:
        @log_task_execution()
        @shared_task
        def process_data(data_id):
            return process(data_id)
    """
    def decorator(func: F) -> F:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            # Get task context
            task = None
            task_id = None
            task_name = func.__name__
            
            # Check if this is a Celery task
            if hasattr(func, "request"):
                task = func.request
                task_id = task.id
                task_name = task.task
            
            # Get appropriate logger
            nonlocal logger
            if logger is None:
                if task_id:
                    # get_task_logger returns BoundContextLogger, so we need to handle this differently
                    # We'll use the base logger for now
                    logger = ContextLogger.get_logger(f"task.{task_name}")
                else:
                    logger = ContextLogger.get_logger(func.__module__)
            
            # Prepare task data
            task_data = {
                "task_name": task_name,
                "task_id": task_id,
            }
            
            if log_arguments:
                task_data["args"] = args
                task_data["kwargs"] = kwargs
            
            # Use context for this task
            with context_logging(task_id=task_id, task_name=task_name):
                # Log task start
                logger.info(
                    f"Task started: {task_name}",
                    **task_data
                )
                
                start_time = time.time()
                
                try:
                    # Execute task
                    result = func(*args, **kwargs)
                    
                    # Log task completion
                    duration_seconds = time.time() - start_time
                    completion_data: dict[str, Any] = {
                        "duration_seconds": round(duration_seconds, 2),
                        "success": True,
                    }
                    
                    if log_result:
                        # Be careful with result size
                        result_str = str(result)
                        if len(result_str) > 1000:
                            result_str = result_str[:1000] + "...(truncated)"
                        completion_data["result"] = result_str
                    
                    logger.info(
                        f"Task completed: {task_name}",
                        **completion_data
                    )
                    
                    return result
                    
                except Exception as e:
                    # Log task failure
                    duration_seconds = time.time() - start_time
                    
                    logger.exception(
                        f"Task failed: {task_name}",
                        duration_seconds=round(duration_seconds, 2),
                        exception_type=type(e).__name__,
                        exception_message=str(e),
                        **task_data
                    )
                    
                    raise
                    
        return cast(F, wrapper)
    return decorator


def log_slow_method(
    threshold_seconds: float = 1.0,
    logger: ContextLogger | None = None,
    include_args: bool = False,
    sample_rate: float = 1.0
) -> Callable[[F], F]:
    """
    Decorator for logging slow method execution.
    
    Logs a warning when method execution exceeds the threshold.
    
    Args:
        threshold_seconds: Time threshold in seconds
        logger: Logger instance (if None, creates one from function module)
        include_args: Whether to include method arguments in log
        sample_rate: Fraction of calls to monitor (0.0-1.0)
        
    Example:
        @log_slow_method(threshold_seconds=0.5)
        def process_data(self, data):
            # Long running method
            time.sleep(1)
    """
    import random
    
    def decorator(func: F) -> F:
        # Get logger if not provided
        nonlocal logger
        if logger is None:
            logger = ContextLogger.get_logger(func.__module__)
        
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            # Sample rate check
            if sample_rate < 1.0 and random.random() > sample_rate:
                # Skip monitoring this call
                return func(*args, **kwargs)
            
            start_time = time.time()
            
            try:
                result = func(*args, **kwargs)
                
                # Check execution time
                duration = time.time() - start_time
                
                if duration > threshold_seconds:
                    log_data = {
                        "method": func.__name__,
                        "module": func.__module__,
                        "duration_seconds": round(duration, 3),
                        "threshold_seconds": threshold_seconds,
                        "exceeded_by": round(duration - threshold_seconds, 3),
                    }
                    
                    if include_args:
                        # Be careful with args size
                        args_str = str(args)
                        kwargs_str = str(kwargs)
                        if len(args_str) > 500:
                            args_str = args_str[:500] + "...(truncated)"
                        if len(kwargs_str) > 500:
                            kwargs_str = kwargs_str[:500] + "...(truncated)"
                        
                        log_data["args"] = args_str
                        log_data["kwargs"] = kwargs_str
                    
                    logger.warning(
                        f"Slow method detected: {func.__name__}",
                        **log_data
                    )
                
                return result
                
            except Exception:
                # Log exception time as well
                duration = time.time() - start_time
                
                if duration > threshold_seconds:
                    logger.warning(
                        f"Slow method failed after {duration:.3f}s: {func.__name__}",
                        duration_seconds=round(duration, 3),
                        threshold_seconds=threshold_seconds,
                    )
                
                raise
                
        return cast(F, wrapper)
    return decorator


# Additional utility decorators

def with_request_context(func: F) -> F:
    """
    Decorator that ensures request context is available in logs.
    
    Extracts request_id and user_id from the first HttpRequest argument.
    
    Example:
        @with_request_context
        def process_request(request, data):
            logger.info("Processing")  # Will have request context
    """
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        # Find request in args
        request = None
        for arg in args:
            if isinstance(arg, HttpRequest):
                request = arg
                break
        
        if request:
            # Extract context from request
            context = {}
            
            if hasattr(request, "request_id"):
                context["request_id"] = request.request_id
            
            if hasattr(request, "user") and request.user.is_authenticated:
                context["user_id"] = request.user.id
                context["username"] = request.user.username
            
            # Execute with context
            with context_logging(**context):
                return func(*args, **kwargs)
        else:
            # No request found, execute normally
            return func(*args, **kwargs)
    
    return cast(F, wrapper)


def with_method_timing(method_name: str | None = None) -> Callable[[F], F]:
    """
    Decorator that adds method execution time to context.
    
    Args:
        method_name: Optional custom method name (defaults to function name)
        
    Example:
        @with_method_timing()
        def calculate_metrics(self):
            # Method execution time will be added to all logs
            logger.info("Calculating")
    """
    def decorator(func: F) -> F:
        name = method_name or func.__name__
        
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            start_time = time.time()
            
            # Add method name to context
            with context_logging(method=name):
                try:
                    result = func(*args, **kwargs)
                    
                    # Add timing to context
                    duration_ms = int((time.time() - start_time) * 1000)
                    add_context(duration_ms=duration_ms)
                    
                    return result
                except Exception:
                    # Add timing even on exception
                    duration_ms = int((time.time() - start_time) * 1000)
                    add_context(duration_ms=duration_ms, failed=True)
                    raise
        
        return cast(F, wrapper)
    return decorator


__all__ = [
    "log_api_call",
    "log_slow_method",
    "log_task_execution",
    "with_method_timing",
    "with_request_context",
]