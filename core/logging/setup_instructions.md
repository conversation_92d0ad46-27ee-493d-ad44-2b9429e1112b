# Инструкция по настройке улучшенного логирования

## Интеграция в Django Settings

### 1. В файле `SocialManager/settings/base.py` добавить:

```python
# В начале файла после импортов
from core.logging.django_config import setup_django_logging, configure_structlog

# Перед секцией LOGGING
# Настройка structlog
configure_structlog()

# Заменить существующую секцию LOGGING на:
from core.logging.django_config import get_logging_config

LOGGING = get_logging_config(
    log_level="INFO",
    logs_dir=LOGS_DIR,
    include_structlog=True,
    include_sentry=True,  # Если Sentry настроен
)

# Добавить middleware (если еще не добавлен)
MIDDLEWARE = [
    'django.middleware.security.SecurityMiddleware',
    'core.logging.middleware.LoggingContextMiddleware',  # <-- Добавить здесь
    # ... остальные middleware
]
```

### 2. В файле `SocialManager/celery.py` добавить:

```python
# В начале файла после импортов Celery
from core.logging.celery_handlers import setup_celery_logging

# После создания app
app = Celery('SocialManager')

# Добавить настройку логирования
setup_celery_logging()
```

### 3. Для локальной разработки в `SocialManager/settings/local.py`:

```python
# Переопределить формат логов для разработки
LOGGING['formatters']['verbose']['processor'] = structlog.dev.ConsoleRenderer(colors=True)
```

### 4. Для production в `SocialManager/settings/production.py`:

```python
# Убедиться что используется JSON формат
LOGGING['formatters']['verbose']['processor'] = structlog.processors.JSONRenderer()

# Добавить Sentry процессор если используется
if SENTRY_DSN:
    from core.logging.django_config import get_structlog_processors
    processors = get_structlog_processors(include_sentry=True)
```

## Использование в коде

### В views:

```python
from core.logging import ContextLogger

logger = ContextLogger.get_logger(__name__)

def my_view(request):
    # Контекст автоматически добавляется middleware
    logger.info("Processing request")
    
    # Можно добавить дополнительный контекст
    logger.bind(user_email=request.user.email).info("User action")
```

### В Celery tasks:

```python
from celery import shared_task
from core.logging import ContextLogger

@shared_task
def my_task(data):
    # Контекст task_id автоматически добавляется signal handlers
    logger = ContextLogger.get_task_logger("my_task")
    logger.info("Task started", data_size=len(data))
```

### В management commands:

```python
from django.core.management.base import BaseCommand
from core.logging import ContextLogger, context_logging

class Command(BaseCommand):
    def handle(self, *args, **options):
        logger = ContextLogger.get_logger(__name__)
        
        with context_logging(command="import_data"):
            logger.info("Starting import")
            # ... код импорта
```

## Проверка работы

1. Запустить Django сервер и проверить логи в консоли
2. Проверить файлы логов в директории logs/
3. Выполнить Celery задачу и проверить наличие task_id в логах
4. Сделать HTTP запрос и проверить наличие request_id в логах

## Дополнительные возможности

### Временный контекст:

```python
from core.logging import context_logging

with context_logging(operation="bulk_import", source="csv"):
    logger.info("Starting import")
    # Все логи внутри блока будут иметь operation и source
```

### Декоратор для функций:

```python
from core.logging import with_logging_context

@with_logging_context(operation="user_registration")
def register_user(email):
    logger.info("Registering user", email=email)
```

### Фильтрация чувствительных данных:

Процессор `filter_sensitive_keys` автоматически фильтрует:
- password, token, api_key, secret
- Вложенные словари с такими ключами
- Регистронезависимые варианты