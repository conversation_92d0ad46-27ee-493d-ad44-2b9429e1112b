"""
Context-aware logger with full structlog integration.

This module provides enhanced ContextLogger with:
- Automatic context enrichment (request, user, task)
- Bound loggers for component-specific context
- Context managers for temporary context
- Factory methods for different logger types
- Full structlog processor chain integration
"""

import logging
import uuid
from collections.abc import Callable
from contextvars import ContextVar
from functools import wraps
from typing import Any

import structlog

# Store original getLogger to avoid recursion when compatibility mode is enabled
_original_getLogger = logging.getLogger

# Context variables for request-scoped data
request_id_var: ContextVar[str | None] = ContextVar("request_id", default=None)
user_id_var: ContextVar[int | None] = ContextVar("user_id", default=None)
task_id_var: ContextVar[str | None] = ContextVar("task_id", default=None)

# General context variable for additional context
_context_var: ContextVar[dict[str, Any]] = ContextVar("logging_context", default={})


# Context management functions
def set_request_id(request_id: str | None = None) -> str | None:
    """Set request ID in context. Generate one if not provided."""
    if request_id is None and request_id_var.get() is None:
        request_id = str(uuid.uuid4())
    if request_id:
        request_id_var.set(request_id)
    return request_id


def set_user_id(user_id: int | None) -> None:
    """Set user ID in context."""
    if user_id is not None:
        user_id_var.set(user_id)


def set_task_id(task_id: str | None) -> None:
    """Set task ID in context."""
    if task_id is not None:
        task_id_var.set(task_id)


def clear_context() -> None:
    """Clear all context variables."""
    request_id_var.set(None)
    user_id_var.set(None)
    task_id_var.set(None)
    _context_var.set({})


def get_context() -> dict[str, Any]:
    """
    Get current logging context.
    
    Returns:
        Dictionary with current context
    """
    # Optimized version - cache context var values
    request_id = request_id_var.get()
    user_id = user_id_var.get()
    task_id = task_id_var.get()
    additional_context = _context_var.get()
    
    # Early return if no context
    if not any([request_id, user_id, task_id, additional_context]):
        return {}
    
    # Build context directly without intermediate dict
    context: dict[str, Any] = {}
    if request_id is not None:
        context["request_id"] = request_id
    if user_id is not None:
        context["user_id"] = user_id
    if task_id is not None:
        context["task_id"] = task_id
    
    # Add additional context if exists
    if additional_context:
        context.update(additional_context)
    
    return context


def set_context(**kwargs):
    """
    Set logging context for current async context.
    
    Args:
        **kwargs: Context to set (replaces existing)
    """
    # Handle special context vars
    if "request_id" in kwargs:
        set_request_id(kwargs.pop("request_id"))
    if "user_id" in kwargs:
        set_user_id(kwargs.pop("user_id"))
    if "task_id" in kwargs:
        set_task_id(kwargs.pop("task_id"))
    
    # Set remaining context
    _context_var.set(kwargs)


def add_context(**kwargs):
    """
    Add to existing logging context.
    
    Args:
        **kwargs: Context to add (merges with existing)
    """
    # Handle special context vars
    if "request_id" in kwargs:
        set_request_id(kwargs.pop("request_id"))
    if "user_id" in kwargs:
        set_user_id(kwargs.pop("user_id"))
    if "task_id" in kwargs:
        set_task_id(kwargs.pop("task_id"))
    
    # Merge with existing context
    current = _context_var.get()
    updated = {**current, **kwargs}
    _context_var.set(updated)


def update_context(**kwargs):
    """Alias for add_context for backward compatibility."""
    add_context(**kwargs)


class BoundContextLogger:
    """
    Logger bound with specific context.
    
    Created by ContextLogger.bind() method.
    """
    
    def __init__(self, name: str, bound_logger, parent: "ContextLogger"):
        self.name = name
        self._logger = bound_logger
        self._parent = parent
    
    def bind(self, **kwargs) -> "BoundContextLogger":
        """Add more context to bound logger."""
        bound_logger = self._logger.bind(**kwargs)
        return BoundContextLogger(self.name, bound_logger, self._parent)
    
    def unbind(self, *keys) -> "BoundContextLogger":
        """Remove context from bound logger."""
        bound_logger = self._logger.unbind(*keys)
        return BoundContextLogger(self.name, bound_logger, self._parent)
    
    def new(self, **kwargs) -> "BoundContextLogger":
        """Create a new logger with only the specified context."""
        bound_logger = self._logger.new(**kwargs)
        return BoundContextLogger(self.name, bound_logger, self._parent)
    
    # Proxy all logging methods
    def debug(self, msg: str, *args, **kwargs):
        self._logger.debug(msg, *args, **kwargs)
    
    def info(self, msg: str, *args, **kwargs):
        self._logger.info(msg, *args, **kwargs)
    
    def warning(self, msg: str, *args, **kwargs):
        self._logger.warning(msg, *args, **kwargs)
    
    def error(self, msg: str, *args, **kwargs):
        self._logger.error(msg, *args, **kwargs)
    
    def critical(self, msg: str, *args, **kwargs):
        self._logger.critical(msg, *args, **kwargs)
    
    def exception(self, msg: str, *args, **kwargs):
        kwargs["exc_info"] = kwargs.get("exc_info", True)
        self._logger.error(msg, *args, **kwargs)
    
    def log(self, level: int | str, msg: str, *args, **kwargs):
        """Log message at specified level."""
        if isinstance(level, int):
            level = logging.getLevelName(level).lower()
        getattr(self._logger, level)(msg, *args, **kwargs)


class ContextLogger:
    """
    Enhanced logger with automatic context inclusion and structlog integration.
    
    Features:
    - Automatic context enrichment
    - Bound loggers for component-specific context
    - Factory methods for different use cases
    - Full structlog processor chain
    """
    
    # Default processors for structlog
    DEFAULT_PROCESSORS: list[Any] = [
        structlog.stdlib.add_logger_name,
        structlog.stdlib.add_log_level,
        structlog.stdlib.PositionalArgumentsFormatter(),
        structlog.processors.StackInfoRenderer(),
        structlog.processors.format_exc_info,
        structlog.processors.UnicodeDecoder(),
    ]
    
    # Cache for loggers
    _loggers_cache: dict[str, "ContextLogger"] = {}
    
    def __init__(self, name: str, processors: list[Callable] | None = None):
        """
        Initialize ContextLogger.
        
        Args:
            name: Logger name (usually __name__)
            processors: Custom processors list (if None, use defaults)
        """
        self.name = name
        self._stdlib_logger = _original_getLogger(name)
        
        # Import and add our processor
        from core.logging.processors import add_app_context, filter_sensitive_keys
        
        # Use custom processors or defaults
        if processors is None:
            processors = [
                # Standard structlog processors
                structlog.stdlib.add_logger_name,
                structlog.stdlib.add_log_level,
                structlog.processors.StackInfoRenderer(),
                structlog.processors.format_exc_info,
                # Our custom processors
                add_app_context,
                filter_sensitive_keys,
                # Renderer must be last
                structlog.processors.JSONRenderer(),
            ]
        
        # Create structlog wrapper
        self._logger = structlog.wrap_logger(
            self._stdlib_logger,
            processors=processors,
            context_class=dict,
            logger_factory=structlog.stdlib.LoggerFactory(),
            cache_logger_on_first_use=True,
        )
        
        # For backward compatibility
        self.logger = self._logger
    
    @classmethod
    def get_logger(cls, name: str) -> "ContextLogger":
        """
        Factory method to get or create a logger.
        
        Args:
            name: Logger name
            
        Returns:
            ContextLogger instance
        """
        if name not in cls._loggers_cache:
            cls._loggers_cache[name] = cls(name)
        return cls._loggers_cache[name]
    
    @classmethod
    def get_task_logger(cls, task_name: str, task_id: str | None = None) -> "BoundContextLogger":
        """
        Get logger for Celery task with task context.
        
        Args:
            task_name: Name of the task
            task_id: Task ID (optional)
            
        Returns:
            ContextLogger with task context
        """
        logger = cls.get_logger(f"task.{task_name}")
        context: dict[str, Any] = {"task_name": task_name}
        if task_id:
            context["task_id"] = task_id
            set_task_id(task_id)
        return logger.bind(**context)
    
    @classmethod
    def get_request_logger(cls, request_id: str | None = None, user_id: int | None = None) -> "BoundContextLogger":
        """
        Get logger for request with request context.
        
        Args:
            request_id: Request ID
            user_id: User ID
            
        Returns:
            ContextLogger with request context
        """
        logger = cls.get_logger("request")
        context: dict[str, Any] = {}
        if request_id:
            context["request_id"] = request_id
            set_request_id(request_id)
        if user_id:
            context["user_id"] = user_id
            set_user_id(user_id)
        return logger.bind(**context)
    
    def bind(self, **kwargs) -> BoundContextLogger:
        """
        Create a new logger with additional context.
        
        Args:
            **kwargs: Context to bind
            
        Returns:
            BoundContextLogger with additional context
        """
        bound_logger = self._logger.bind(**kwargs)
        return BoundContextLogger(self.name, bound_logger, self)
    
    def unbind(self, *keys) -> BoundContextLogger:
        """
        Create a new logger with keys removed from context.
        
        Args:
            *keys: Keys to remove
            
        Returns:
            BoundContextLogger with keys removed
        """
        bound_logger = self._logger.unbind(*keys)
        return BoundContextLogger(self.name, bound_logger, self)
    
    def new(self, **kwargs) -> BoundContextLogger:
        """
        Create a new logger with only the specified context.
        
        Args:
            **kwargs: New context (replaces all existing)
            
        Returns:
            BoundContextLogger with new context
        """
        bound_logger = self._logger.new(**kwargs)
        return BoundContextLogger(self.name, bound_logger, self)
    
    # Logging methods
    def debug(self, msg: str, *args, **kwargs):
        """Log debug message with context."""
        self._logger.debug(msg, *args, **kwargs)
    
    def info(self, msg: str, *args, **kwargs):
        """Log info message with context."""
        self._logger.info(msg, *args, **kwargs)
    
    def warning(self, msg: str, *args, **kwargs):
        """Log warning message with context."""
        self._logger.warning(msg, *args, **kwargs)
    
    def error(self, msg: str, *args, **kwargs):
        """Log error message with context."""
        self._logger.error(msg, *args, **kwargs)
    
    def critical(self, msg: str, *args, **kwargs):
        """Log critical message with context."""
        self._logger.critical(msg, *args, **kwargs)
    
    def exception(self, msg: str, *args, **kwargs):
        """Log exception with traceback."""
        kwargs["exc_info"] = kwargs.get("exc_info", True)
        self._logger.error(msg, *args, **kwargs)
    
    def log(self, level: int | str, msg: str, *args, **kwargs):
        """Log message at specified level."""
        if isinstance(level, int):
            level = logging.getLevelName(level).lower()
        getattr(self._logger, level)(msg, *args, **kwargs)
    
    # Additional methods for convenience
    def add_context(self, **kwargs):
        """
        Add context that will be included in all subsequent logs.
        
        This modifies the global context for the current async context.
        """
        add_context(**kwargs)
    
    def remove_context(self, *keys):
        """
        Remove keys from the global context.
        
        Args:
            *keys: Keys to remove
        """
        current = _context_var.get()
        for key in keys:
            current.pop(key, None)
        _context_var.set(current)
    
    @property
    def level(self) -> int:
        """Get current logging level."""
        return self._stdlib_logger.level
    
    @level.setter
    def level(self, value: int | str):
        """Set logging level."""
        if isinstance(value, str):
            value = getattr(logging, value.upper())
        self._stdlib_logger.setLevel(value)


# Context manager for temporary logging context
class context_logging:
    """
    Context manager for temporary logging context.
    
    Example:
        with context_logging(operation="user_import", user_id=123):
            logger.info("Starting import")  # Will have operation and user_id
    """
    
    def __init__(self, **kwargs):
        self.context = kwargs
        self.token = None
        self.previous_context = None
    
    def __enter__(self):
        self.previous_context = get_context()
        updated = {**self.previous_context, **self.context}
        
        # Set special context vars
        if "request_id" in updated:
            set_request_id(updated.pop("request_id"))
        if "user_id" in updated:
            set_user_id(updated.pop("user_id"))
        if "task_id" in updated:
            set_task_id(updated.pop("task_id"))
        
        # Set remaining context
        self.token = _context_var.set(updated)
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        if self.token:
            _context_var.reset(self.token)
        # Restore special context vars
        if "request_id" in self.previous_context:
            set_request_id(self.previous_context["request_id"])
        if "user_id" in self.previous_context:
            set_user_id(self.previous_context["user_id"])
        if "task_id" in self.previous_context:
            set_task_id(self.previous_context["task_id"])


# Decorator for adding logging context to functions
def with_logging_context(**context):
    """
    Decorator to add logging context to a function.
    
    Args:
        **context: Static context to add
        
    Example:
        @with_logging_context(operation="data_processing")
        def process_data(data):
            logger.info("Processing")  # Will have operation in context
    """
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            # Can add dynamic context from arguments
            dynamic_context = {}
            
            # Example: if first argument is request, add request_id
            if args and hasattr(args[0], "META"):
                request = args[0]
                if hasattr(request, "request_id"):
                    dynamic_context["request_id"] = request.request_id
            
            # Combine static and dynamic context
            full_context = {**context, **dynamic_context}
            
            with context_logging(**full_context):
                return func(*args, **kwargs)
        
        return wrapper
    return decorator


# Configure structlog on module import
def configure_structlog(
    processors: list[Callable] | None = None,
    logger_factory: Callable | None = None,
    cache_logger_on_first_use: bool = True
):
    """
    Configure structlog globally.
    
    Args:
        processors: List of processors
        logger_factory: Logger factory
        cache_logger_on_first_use: Whether to cache loggers
    """
    from core.logging.processors import add_app_context
    
    if processors is None:
        processors = [add_app_context] + ContextLogger.DEFAULT_PROCESSORS
    elif add_app_context not in processors:
        processors = [add_app_context] + list(processors)
    
    structlog.configure(
        processors=processors + [
            structlog.stdlib.ProcessorFormatter.wrap_for_formatter,
        ],
        context_class=dict,
        logger_factory=logger_factory or structlog.stdlib.LoggerFactory(),
        cache_logger_on_first_use=cache_logger_on_first_use,
    )


# For backward compatibility
def get_logger(name: str) -> ContextLogger:
    """
    Get a ContextLogger instance.
    
    Args:
        name: Logger name
        
    Returns:
        ContextLogger instance
    """
    return ContextLogger.get_logger(name)


# Export for use
__all__ = [
    "BoundContextLogger",
    "ContextLogger",
    "add_context",
    "clear_context",
    "configure_structlog",
    "context_logging",
    "get_context",
    "get_logger",
    "request_id_var",
    "set_context",
    "set_request_id",
    "set_task_id",
    "set_user_id",
    "task_id_var",
    "update_context",
    "user_id_var",
    "with_logging_context",
]