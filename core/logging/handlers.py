"""
Custom logging handlers for Sentry integration.

This module contains:
- Sen<PERSON><PERSON><PERSON><PERSON>: Handler that sends logs to Sentry with context
- BufferedSentryHandler: Buffered version for better performance
- ConditionalSentryHandler: Handler with filtering capabilities
"""

import logging
from collections.abc import Callable
from datetime import datetime
from typing import Any, Literal, cast

try:
    import sentry_sdk
    from sentry_sdk import Scope
except ImportError:
    sentry_sdk = None  # type: ignore
    Scope = None  # type: ignore

# Import context function
# Will be fully implemented in task 005, using temporary stub for now
try:
    from .logger import request_id_var, task_id_var, user_id_var
    
    def get_context() -> dict[str, Any]:
        """Get current logging context."""
        return {
            "request_id": request_id_var.get(),
            "user_id": user_id_var.get(),
            "task_id": task_id_var.get(),
        }
except ImportError:
    # Fallback if logger not available yet
    def get_context() -> dict[str, Any]:
        return {}


class SentryHandler(logging.Handler):
    """
    Custom logging handler that sends logs to Sentry with enriched context.
    
    This handler:
    - Adds request context (request_id, user_id, etc.)
    - Maps Python logging levels to Sentry levels
    - <PERSON>les exceptions with full traceback
    - Adds custom tags for better filtering
    - Supports breadcrumbs for context
    """
    
    # Mapping of Python logging levels to Sentry levels
    LEVEL_MAPPING = {
        logging.DEBUG: "debug",
        logging.INFO: "info",
        logging.WARNING: "warning",
        logging.ERROR: "error",
        logging.CRITICAL: "critical",  # Sentry expects 'critical', not 'fatal'
    }
    
    def __init__(self, level: int = logging.ERROR, 
                 capture_message_level: int = logging.ERROR,
                 add_breadcrumbs: bool = True):
        """
        Initialize SentryHandler.
        
        Args:
            level: Minimum level for handling records
            capture_message_level: Minimum level for sending as Sentry events
            add_breadcrumbs: Whether to add lower level logs as breadcrumbs
        """
        super().__init__(level)
        self.capture_message_level = capture_message_level
        self.add_breadcrumbs = add_breadcrumbs
        
    def emit(self, record: logging.LogRecord) -> None:
        """
        Send log record to Sentry with context enrichment.
        
        Args:
            record: The log record to process
        """
        try:
            # Check if Sentry SDK is available and initialized
            if not sentry_sdk or not sentry_sdk.Hub.current.client:
                return
                
            # Check level threshold
            if record.levelno < self.level:
                return
                
            with sentry_sdk.push_scope() as scope:
                # Add context to scope
                self._add_context_to_scope(scope, record)
                
                # Add tags
                self._add_tags_to_scope(scope, record)
                
                # Add extra data
                self._add_extra_to_scope(scope, record)
                
                # Process the record
                if record.exc_info and record.exc_info[0] is not None:
                    # If there's an exception, capture it
                    self._capture_exception(record)
                elif record.levelno >= self.capture_message_level:
                    # Send as message
                    self._capture_message(record)
                elif self.add_breadcrumbs and record.levelno >= logging.INFO:
                    # Add as breadcrumb
                    self._add_breadcrumb(record)
                    
        except Exception:
            # Don't let handler errors break the application
            self.handleError(record)
    
    def _add_context_to_scope(self, scope: "Scope", 
                             record: logging.LogRecord) -> None:
        """Add ContextLogger context to Sentry scope."""
        context = get_context()
        
        # Also check for context attributes directly on record
        if hasattr(record, "request_id") and record.request_id:
            if not context.get("request_id"):
                context["request_id"] = record.request_id
        if hasattr(record, "user_id") and record.user_id:
            if not context.get("user_id"):
                context["user_id"] = record.user_id
        if hasattr(record, "task_id") and record.task_id:
            if not context.get("task_id"):
                context["task_id"] = record.task_id
        
        if context:
            # Request context
            request_context = {
                "request_id": context.get("request_id"),
                "correlation_id": context.get("correlation_id"),
                "session_id": context.get("session_id"),
            }
            # Remove None values
            request_context = {k: v for k, v in request_context.items() if v is not None}
            if request_context:
                scope.set_context("request", request_context)
            
            # User context
            user_id = context.get("user_id")
            if user_id:
                scope.set_user({
                    "id": str(user_id),
                    "username": context.get("username"),
                    "email": context.get("user_email"),
                })
            
            # Task context (for Celery)
            task_context = {
                "task_id": context.get("task_id"),
                "task_name": context.get("task_name"),
                "task_args": context.get("task_args"),
                "task_kwargs": context.get("task_kwargs"),
            }
            task_context = {k: v for k, v in task_context.items() if v is not None}
            if task_context:
                scope.set_context("task", task_context)
            
            # App context
            app_context = {
                "environment": context.get("environment"),
                "service": context.get("service"),
                "version": context.get("version"),
            }
            app_context = {k: v for k, v in app_context.items() if v is not None}
            if app_context:
                scope.set_context("app", app_context)
    
    def _add_tags_to_scope(self, scope: "Scope", 
                          record: logging.LogRecord) -> None:
        """Add tags for filtering in Sentry UI."""
        # Basic tags
        scope.set_tag("logger", record.name)
        scope.set_tag("level", record.levelname)
        
        # Tags from context
        context = get_context()
        if context:
            # Important tags for filtering
            if "environment" in context:
                scope.set_tag("environment", context["environment"])
            if "service" in context:
                scope.set_tag("service", context["service"]) 
            if "user_id" in context:
                scope.set_tag("has_user", "true")
            if "task_name" in context:
                scope.set_tag("task_name", context["task_name"])
                
        # Tags from record attributes
        if hasattr(record, "tags") and isinstance(record.tags, dict):
            for key, value in record.tags.items():
                scope.set_tag(key, str(value))
    
    def _add_extra_to_scope(self, scope: "Scope", 
                           record: logging.LogRecord) -> None:
        """Add extra data to scope."""
        # Data from record
        extra_data = {
            "logger": record.name,
            "module": record.module,
            "function": record.funcName,
            "line_number": record.lineno,
            "thread": record.thread,
            "thread_name": record.threadName,
            "process": record.process,
        }
        
        # Additional attributes from record
        for key, value in record.__dict__.items():
            if key not in [
                "name", "msg", "args", "created", "filename", "funcName",
                "levelname", "levelno", "lineno", "module", "msecs",
                "pathname", "process", "processName", "relativeCreated",
                "thread", "threadName", "exc_info", "exc_text", "stack_info",
                "getMessage", "tags"  # Skip method and already processed attrs
            ]:
                extra_data[key] = value
                
        # Add to scope
        for key, value in extra_data.items():
            if value is not None:
                scope.set_extra(key, value)
    
    def _capture_exception(self, record: logging.LogRecord) -> None:
        """Capture exception with traceback."""
        # exc_info is a tuple (type, value, traceback)
        if record.exc_info and record.exc_info[0] is not None:
            sentry_sdk.capture_exception(record.exc_info[1])
    
    def _capture_message(self, record: logging.LogRecord) -> None:
        """Capture log message as Sentry event."""
        # Use getMessage() if format() fails (no formatter set)
        try:
            message = self.format(record)
        except Exception:
            message = record.getMessage()
            
        level = self.LEVEL_MAPPING.get(record.levelno, "error")
        
        # Create event dict for capture_event
        event: dict[str, Any] = {
            "message": message,
            "level": level,
            "logger": record.name,
        }
        
        # Add extra data from record
        extra = {}
        for key, value in record.__dict__.items():
            if key not in [
                "name", "msg", "args", "created", "filename", "funcName",
                "levelname", "levelno", "lineno", "module", "msecs",
                "pathname", "process", "processName", "relativeCreated",
                "thread", "threadName", "exc_info", "exc_text", "stack_info",
                "getMessage", "tags"
            ]:
                extra[key] = value
        
        if extra:
            event["extra"] = extra
        
        # Sentry SDK expects specific event structure
        # We use capture_message for log messages
        # Convert level to Sentry format
        sentry_level = self.LEVEL_MAPPING.get(record.levelno, "error")
        # Cast to Literal type that Sentry expects
        sentry_level_typed = cast(Literal["fatal", "critical", "error", "warning", "info", "debug"], sentry_level)
        sentry_sdk.capture_message(message, level=sentry_level_typed)
    
    def _add_breadcrumb(self, record: logging.LogRecord) -> None:
        """Add log record as breadcrumb for context."""
        sentry_sdk.add_breadcrumb(
            message=record.getMessage(),
            level=self.LEVEL_MAPPING.get(record.levelno, "info"),
            category=record.name,
            timestamp=datetime.fromtimestamp(record.created),
            data={
                "logger": record.name,
                "module": record.module,
                "function": record.funcName,
                "line": record.lineno,
            }
        )


class BufferedSentryHandler(SentryHandler):
    """
    Buffered version of SentryHandler for better performance.
    
    Collects multiple log records and sends them in batches.
    """
    
    def __init__(self, level: int = logging.ERROR,
                 buffer_size: int = 10,
                 flush_interval: float = 5.0,
                 **kwargs):
        """
        Initialize BufferedSentryHandler.
        
        Args:
            level: Minimum level for handling records
            buffer_size: Maximum number of records to buffer
            flush_interval: Maximum seconds between flushes
            **kwargs: Additional arguments for SentryHandler
        """
        super().__init__(level=level, **kwargs)
        self.buffer_size = buffer_size
        self.flush_interval = flush_interval
        self.buffer: list[logging.LogRecord] = []
        self.last_flush = datetime.now()
        
    def emit(self, record: logging.LogRecord) -> None:
        """Buffer the record and flush if needed."""
        # Check level threshold first
        if record.levelno < self.level:
            return
            
        self.buffer.append(record)
        
        # Check flush conditions
        should_flush = (
            len(self.buffer) >= self.buffer_size or
            (datetime.now() - self.last_flush).total_seconds() > self.flush_interval or
            record.levelno >= logging.ERROR  # Immediately send errors
        )
        
        if should_flush:
            self.flush()
    
    def flush(self) -> None:
        """Flush all buffered records to Sentry."""
        if not self.buffer:
            return
            
        # Send each record
        for record in self.buffer:
            try:
                super().emit(record)
            except Exception:
                self.handleError(record)
                
        # Clear buffer
        self.buffer.clear()
        self.last_flush = datetime.now()
    
    def close(self) -> None:
        """Flush and close the handler."""
        self.flush()
        super().close()


class ConditionalSentryHandler(SentryHandler):
    """
    Conditional handler that only sends to Sentry based on conditions.
    
    Useful for filtering out certain types of errors or loggers.
    """
    
    def __init__(self, level: int = logging.ERROR,
                 exclude_loggers: list[str] | None = None,
                 include_loggers: list[str] | None = None,
                 condition_func: Callable[[logging.LogRecord], bool] | None = None,
                 **kwargs):
        """
        Initialize ConditionalSentryHandler.
        
        Args:
            level: Minimum level for handling records
            exclude_loggers: List of logger names to exclude
            include_loggers: List of logger names to include (if set, only these are included)
            condition_func: Custom function to determine if record should be sent
            **kwargs: Additional arguments for SentryHandler
        """
        super().__init__(level=level, **kwargs)
        self.exclude_loggers = set(exclude_loggers or [])
        self.include_loggers = set(include_loggers or [])
        self.condition_func = condition_func
        
    def should_emit(self, record: logging.LogRecord) -> bool:
        """Determine if record should be sent to Sentry."""
        # Check exclude list
        if record.name in self.exclude_loggers:
            return False
            
        # Check include list
        if self.include_loggers and record.name not in self.include_loggers:
            return False
            
        # Check custom condition
        if self.condition_func and not self.condition_func(record):
            return False
            
        return True
    
    def emit(self, record: logging.LogRecord) -> None:
        """Conditionally emit the record."""
        if self.should_emit(record):
            super().emit(record)


def create_sentry_handler(level: int | str = logging.ERROR,
                         buffered: bool = False,
                         conditional: bool = False,
                         **kwargs) -> SentryHandler:
    """
    Factory function to create appropriate Sentry handler.
    
    Args:
        level: Logging level (int or string)
        buffered: Whether to use buffered handler
        conditional: Whether to use conditional handler
        **kwargs: Additional arguments for handler
        
    Returns:
        Configured SentryHandler instance
        
    Examples:
        Basic usage:
        >>> handler = create_sentry_handler(level=logging.ERROR)
        
        Buffered handler for performance:
        >>> handler = create_sentry_handler(
        ...     level='WARNING',
        ...     buffered=True,
        ...     buffer_size=20,
        ...     flush_interval=10.0
        ... )
        
        Conditional handler to exclude noisy loggers:
        >>> handler = create_sentry_handler(
        ...     level=logging.ERROR,
        ...     conditional=True,
        ...     exclude_loggers=['urllib3', 'requests']
        ... )
    """
    # Convert string level to int
    if isinstance(level, str):
        level = getattr(logging, level.upper())
    
    # Choose handler class
    handler_class: type[SentryHandler] | type[BufferedSentryHandler] | type[ConditionalSentryHandler]
    if buffered and conditional:
        raise ValueError("Cannot use both buffered and conditional handlers")
    elif buffered:
        handler_class = BufferedSentryHandler
    elif conditional:
        handler_class = ConditionalSentryHandler
    else:
        handler_class = SentryHandler
    
    # Create handler
    # Convert string level to int if needed
    level_int: int
    if isinstance(level, str):
        level_int = getattr(logging, level.upper())
    else:
        level_int = level
    return handler_class(level=level_int, **kwargs)