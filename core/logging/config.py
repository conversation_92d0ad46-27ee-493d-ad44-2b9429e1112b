"""
Centralized Django logging configuration.

This module provides a comprehensive logging configuration that:
- Integrates with the existing ContextLogger and structlog
- Provides environment-specific configurations
- Supports Sentry integration
- Includes filters for sensitive data and rate limiting
- Configures handlers for different log types
"""

from pathlib import Path
from typing import Any

from django.conf import settings

# Environment-specific log levels
LOGGING_LEVELS = {
    "development": {
        "django": "DEBUG",
        "django.db.backends": "DEBUG",
        "core": "DEBUG",
        "instagram_manager": "DEBUG",
        "telegram_manager": "DEBUG",
        "root": "INFO",
    },
    "staging": {
        "django": "INFO",
        "django.db.backends": "INFO",
        "core": "INFO",
        "instagram_manager": "INFO",
        "telegram_manager": "INFO",
        "root": "INFO",
    },
    "production": {
        "django": "WARNING",
        "django.db.backends": "WARNING",
        "core": "INFO",
        "instagram_manager": "INFO",
        "telegram_manager": "INFO",
        "root": "WARNING",
    }
}


def get_django_logging_config(
    environment: str = "development",
    log_dir: Path | None = None,
    enable_sentry: bool = True,
    sentry_dsn: str | None = None,
) -> dict[str, Any]:
    """
    Get Django LOGGING configuration for the specified environment.
    
    Args:
        environment: Target environment (development/staging/production)
        log_dir: Directory for log files (defaults to settings.LOG_DIR)
        enable_sentry: Whether to enable Sentry integration
        sentry_dsn: Sentry DSN (if not provided, will check settings)
        
    Returns:
        Django LOGGING configuration dictionary
    """
    # Determine log directory
    if log_dir is None:
        log_dir = getattr(settings, "LOG_DIR", settings.BASE_DIR / "logs")
    
    # Ensure log directory exists
    log_dir = Path(log_dir)
    log_dir.mkdir(exist_ok=True)
    
    # Create subdirectories
    for subdir in ["app", "errors", "performance", "security", "celery"]:
        (log_dir / subdir).mkdir(exist_ok=True)
    
    # Determine actual Sentry DSN
    if sentry_dsn is None:
        sentry_dsn = getattr(settings, "SENTRY_DSN", None)
    
    # Get environment-specific log levels
    levels = LOGGING_LEVELS.get(environment, LOGGING_LEVELS["development"])
    
    # Base configuration
    config = {
        "version": 1,
        "disable_existing_loggers": False,
        "filters": _get_filters(environment),
        "formatters": _get_formatters(environment),
        "handlers": _get_handlers(environment, log_dir, sentry_dsn),
        "loggers": _get_loggers(environment, levels, sentry_dsn),
        "root": _get_root_logger(environment, levels),
    }
    
    return config


def _get_filters(environment: str) -> dict[str, Any]:
    """Get logging filters configuration."""
    return {
        "require_debug_false": {
            "()": "django.utils.log.RequireDebugFalse",
        },
        "require_debug_true": {
            "()": "django.utils.log.RequireDebugTrue",
        },
        "context": {
            "()": "core.logging.context.LoggingContextFilter",
        },
        "sensitive_data": {
            "()": "core.logging.filters.create_sensitive_data_filter",
            "enabled": True,
            "preset": "strict" if environment == "production" else "balanced",
        },
        "rate_limit": {
            "()": "core.logging.filters.RateLimitFilter",
            "rate": 10,  # 10 одинаковых сообщений
            "per": 60,   # в минуту
            "burst": 20,  # максимум 20 в burst
        },
        "rate_limit_errors": {
            "()": "core.logging.filters.RateLimitFilter",
            "rate": 5,   # Max 5 identical errors
            "per": 300,  # Per 5 minutes
            "burst": 2,  # Allow 2 burst errors
        },
    }


def _get_formatters(environment: str) -> dict[str, Any]:
    """Get log formatters configuration."""
    import importlib.util
    
    # Check for optional formatters using importlib
    has_json_formatter = importlib.util.find_spec("pythonjsonlogger") is not None
    has_colored_formatter = importlib.util.find_spec("colorlog") is not None
    
    formatters = {
        "verbose": {
            "format": (
                "%(asctime)s [%(levelname)s] %(name)s "
                "%(funcName)s:%(lineno)d | %(message)s | "
                "request_id=%(request_id)s user_id=%(user_id)s"
            ),
            "datefmt": "%Y-%m-%d %H:%M:%S",
            "defaults": {
                "request_id": "N/A",
                "user_id": "N/A",
            },
        },
        "simple": {
            "format": "%(asctime)s [%(levelname)s] %(name)s: %(message)s",
            "datefmt": "%H:%M:%S",
        },
    }
    
    # Add JSON formatter if available
    if has_json_formatter:
        formatters["json"] = {
            "()": "pythonjsonlogger.jsonlogger.JsonFormatter",
            "format": (
                "%(asctime)s %(name)s %(levelname)s %(funcName)s "
                "%(lineno)d %(message)s %(request_id)s %(user_id)s"
            ),
            "defaults": {
                "request_id": None,
                "user_id": None,
            },
        }
    else:
        # Fallback to our custom JSON formatter
        formatters["json"] = {
            "()": "core.logging.formatters.JSONFormatter",
            "include_traceback": True,
            "include_extra": True,
            "timestamp_format": "iso",
        }
    
    # Add colored formatter if available
    if has_colored_formatter:
        formatters["colored"] = {
            "()": "colorlog.ColoredFormatter",
            "format": (
                "%(log_color)s%(asctime)s [%(levelname)s]%(reset)s "
                "%(blue)s%(name)s%(reset)s %(message)s"
            ),
            "datefmt": "%H:%M:%S",
            "log_colors": {
                "DEBUG": "cyan",
                "INFO": "green",
                "WARNING": "yellow",
                "ERROR": "red",
                "CRITICAL": "red,bg_white",
            },
        }
    else:
        # Fallback to our custom colored formatter
        formatters["colored"] = {
            "()": "core.logging.formatters.ColoredFormatter",
            "fmt": (
                "%(asctime)s %(levelname)s %(name)s "
                "[%(request_id)s] %(message)s"
            ),
            "datefmt": "%Y-%m-%d %H:%M:%S",
        }
    
    return formatters


def _get_handlers(
    environment: str, 
    log_dir: Path,
    sentry_dsn: str | None
) -> dict[str, Any]:
    """Get log handlers configuration."""
    handlers = {
        "null": {
            "class": "logging.NullHandler",
        },
        "console": {
            "level": "DEBUG",
            "class": "logging.StreamHandler",
            "formatter": "colored" if environment == "development" else "verbose",
            "filters": ["context", "sensitive_data"],
        },
        "console_errors": {
            "class": "logging.StreamHandler",
            "level": "ERROR",
            "formatter": "verbose",
            "filters": ["context", "sensitive_data", "rate_limit_errors"],
        },
    }
    
    # Add console_json handler for production
    if environment == "production":
        handlers["console_json"] = {
            "level": "INFO",
            "class": "logging.StreamHandler",
            "formatter": "json",
            "filters": ["context", "sensitive_data"],
        }
    
    # File handlers - not created for production
    if environment not in ["test", "production"]:
        handlers.update({
            "file_debug": {
                "level": "DEBUG",
                "class": "logging.handlers.RotatingFileHandler",
                "filename": str(log_dir / "debug.log"),
                "maxBytes": 10 * 1024 * 1024,  # 10MB
                "backupCount": 5,
                "formatter": "verbose",
                "filters": ["context", "sensitive_data", "require_debug_true"],
            },
            "file_error": {
                "level": "ERROR",
                "class": "logging.handlers.RotatingFileHandler",
                "filename": str(log_dir / "error.log"),
                "maxBytes": 10 * 1024 * 1024,  # 10MB
                "backupCount": 10,
                "formatter": "verbose",
                "filters": ["context", "sensitive_data"],
            },
            "file_json": {
                "level": "INFO",
                "class": "logging.handlers.RotatingFileHandler",
                "filename": str(log_dir / "app.json"),
                "maxBytes": 50 * 1024 * 1024,  # 50MB
                "backupCount": 7,
                "formatter": "json",
                "filters": ["context", "sensitive_data"],
            },
            "security_file": {
                "class": "logging.handlers.RotatingFileHandler",
                "filename": str(log_dir / "security" / "security.log"),
                "maxBytes": 5242880,  # 5MB
                "backupCount": 20,
                "formatter": "json",
                "encoding": "utf-8",
                "filters": ["context", "sensitive_data"],
            },
            "performance_file": {
                "class": "logging.handlers.RotatingFileHandler",
                "filename": str(log_dir / "performance" / "performance.log"),
                "maxBytes": 10485760,  # 10MB
                "backupCount": 3,
                "formatter": "json",
                "encoding": "utf-8",
                "filters": ["context"],
            },
            "celery_file": {
                "class": "logging.handlers.RotatingFileHandler",
                "filename": str(log_dir / "celery" / "celery.log"),
                "maxBytes": 50 * 1024 * 1024,  # 50MB
                "backupCount": 10,
                "formatter": "json" if environment in ["production", "staging"] else "verbose",
                "encoding": "utf-8",
                "filters": ["context", "sensitive_data"],
            },
        })
    
    # Add Sentry handler if enabled
    if sentry_dsn:
        handlers["sentry"] = {
            "level": "WARNING" if environment == "production" else "ERROR",
            "class": "core.logging.handlers.BufferedSentryHandler",
            "filters": ["context", "sensitive_data", "rate_limit"],
            "buffer_size": 100,
        }
    
    # Environment-specific handler adjustments
    if environment == "development":
        # In development, add SQL handler
        if environment != "test":
            handlers["sql_file"] = {
                "level": "DEBUG",
                "class": "logging.handlers.RotatingFileHandler",
                "filename": str(log_dir / "sql.log"),
                "maxBytes": 10 * 1024 * 1024,
                "backupCount": 3,
                "formatter": "simple",
                "filters": ["context"],
            }
    elif environment == "test":
        # In tests, minimize logging
        handlers = {
            "null": handlers["null"],
            "console": {
                "class": "logging.StreamHandler",
                "level": "ERROR",
                "formatter": "simple",
            },
        }
    
    return handlers


def _get_loggers(environment: str, levels: dict[str, str], sentry_dsn: str | None) -> dict[str, Any]:
    """Get logger configuration for different modules."""
    # Default handler list based on environment
    if environment == "test":
        default_handlers = ["null"]
        error_handlers = ["console"]
    elif environment == "development":
        default_handlers = ["console", "file_json"]
        error_handlers = ["console", "file_error"]
    elif environment == "production":
        default_handlers = ["console_json"]
        error_handlers = ["console_errors"]
    else:
        default_handlers = ["file_json"]
        error_handlers = ["file_error"]
    
    # Add sentry handler if available
    if environment != "test" and sentry_dsn:
        for handler_list in [default_handlers, error_handlers]:
            if "sentry" not in handler_list:
                handler_list.append("sentry")
    
    loggers = {
        # Django loggers
        "django": {
            "handlers": default_handlers,
            "level": levels["django"],
            "propagate": False,
        },
        "django.request": {
            "handlers": error_handlers,
            "level": "ERROR",
            "propagate": False,
        },
        # "django.security": {
        #     "handlers": ["security_file"] + error_handlers if environment != "test" else error_handlers,
        #     "level": "ERROR",
        #     "propagate": False,
        # },
        "django.db.backends": {
            "handlers": ["console"] if environment == "development" else ["null"],
            "level": levels["django.db.backends"],
            "propagate": False,
        },
        
        # App loggers
        "instagram_manager": {
            "handlers": default_handlers,
            "level": levels["instagram_manager"],
            "propagate": False,
        },
        "telegram_manager": {
            "handlers": default_handlers,
            "level": levels["telegram_manager"],
            "propagate": False,
        },
        "core": {
            "handlers": default_handlers,
            "level": levels["core"],
            "propagate": False,
        },
        
        # Task and performance loggers
        # "celery": {
        #     "handlers": ["celery_file"] if environment != "test" else ["null"],
        #     "level": "INFO",
        #     "propagate": False,
        # },
        # "monitoring": {
        #     "handlers": ["performance_file"] if environment != "test" else ["null"],
        #     "level": "INFO",
        #     "propagate": False,
        # },
        
        # External library loggers
        # "urllib3": {
        #     "handlers": ["null"],
        #     "level": "INFO",
        #     "propagate": False,
        # },
        # "requests": {
        #     "handlers": ["null"],
        #     "level": "INFO",
        #     "propagate": False,
        # },
        # "PIL": {
        #     "handlers": ["null"],
        #     "level": "INFO",
        #     "propagate": False,
        # },
    }
    
    # Special configuration for development
    if environment == "development":
        # SQL queries in console
        loggers["django.db.backends"]["handlers"] = ["console", "sql_file"] if environment != "test" else ["console"]
        
        # Add debug file handlers
        loggers["django"]["handlers"].append("file_debug")
        loggers["core"]["handlers"].append("file_debug")
    
    return loggers


def _get_root_logger(environment: str, levels: dict[str, str]) -> dict[str, Any]:
    """Get root logger configuration."""
    if environment == "test":
        return {
            "handlers": ["null"],
            "level": "WARNING",
        }
    elif environment == "development":
        return {
            "handlers": ["console", "file_error"],
            "level": levels["root"],
        }
    elif environment == "production":
        return {
            "handlers": ["console_json", "console_errors"],
            "level": levels["root"],
        }
    else:
        return {
            "handlers": ["file_json", "file_error"],
            "level": levels["root"],
        }


def configure_logging(environment: str | None = None):
    """
    Configure Django logging for the application.
    
    This should be called from settings after LOGGING is set.
    
    Args:
        environment: Override environment detection
    """
    import logging.config
    
    # Detect environment if not provided
    if environment is None:
        if hasattr(settings, "ENVIRONMENT"):
            environment = settings.ENVIRONMENT
        elif hasattr(settings, "DEBUG"):
            environment = "development" if settings.DEBUG else "production"
        else:
            environment = "development"
    
    # Get logging configuration
    logging_config = get_django_logging_config(
        environment=environment,
        enable_sentry=bool(getattr(settings, "SENTRY_DSN", None)),
        sentry_dsn=getattr(settings, "SENTRY_DSN", None),
    )
    
    # Apply configuration
    logging.config.dictConfig(logging_config)
    
    # Log startup message
    import logging
    logger = logging.getLogger(__name__)
    logger.info(
        f"Logging configured for environment: {environment}",
        extra={"environment": environment}
    )


def get_celery_logging_config(
    environment: str = "development",
    log_dir: Path | None = None,
) -> dict[str, Any]:
    """
    Get Celery-specific logging configuration.
    
    This configuration is designed to work with Celery's logging system
    and integrates with the main Django logging configuration.
    
    Args:
        environment: Target environment (development/staging/production)
        log_dir: Directory for log files
        
    Returns:
        Celery logging configuration dictionary
    """
    if log_dir is None:
        log_dir = getattr(settings, "LOG_DIR", settings.BASE_DIR / "logs")
    
    log_dir = Path(log_dir)
    celery_log_dir = log_dir / "celery"
    celery_log_dir.mkdir(exist_ok=True, parents=True)
    
    # Get environment-specific log levels
    levels = LOGGING_LEVELS.get(environment, LOGGING_LEVELS["development"])
    
    config: dict[str, Any] = {
        "version": 1,
        "disable_existing_loggers": False,
        "formatters": {
            "celery": {
                "format": (
                    "[%(asctime)s: %(levelname)s/%(processName)s] "
                    "%(name)s: %(message)s | "
                    "task_id=%(task_id)s task_name=%(task_name)s"
                ),
                "datefmt": "%Y-%m-%d %H:%M:%S",
                "defaults": {
                    "task_id": "N/A",
                    "task_name": "N/A",
                },
            },
            "celery_json": {
                "()": "pythonjsonlogger.jsonlogger.JsonFormatter",
                "format": (
                    "%(asctime)s %(name)s %(levelname)s %(processName)s "
                    "%(message)s %(task_id)s %(task_name)s"
                ),
                "defaults": {
                    "task_id": None,
                    "task_name": None,
                },
            },
        },
        "filters": {
            "context": {
                "()": "core.logging.context.LoggingContextFilter",
            },
            "sensitive_data": {
                "()": "core.logging.filters.create_sensitive_data_filter",
                "enabled": True,
                "preset": "strict" if environment == "production" else "balanced",
            },
        },
        "handlers": {
            "celery_console": {
                "class": "logging.StreamHandler",
                "formatter": "celery",
                "filters": ["context", "sensitive_data"],
            },
            "celery_file": {
                "class": "logging.handlers.RotatingFileHandler",
                "filename": str(celery_log_dir / "celery.log"),
                "maxBytes": 50 * 1024 * 1024,  # 50MB
                "backupCount": 10,
                "formatter": "celery_json" if environment != "development" else "celery",
                "filters": ["context", "sensitive_data"],
            },
            "celery_error_file": {
                "class": "logging.handlers.RotatingFileHandler",
                "filename": str(celery_log_dir / "celery_errors.log"),
                "maxBytes": 10 * 1024 * 1024,  # 10MB
                "backupCount": 20,
                "level": "ERROR",
                "formatter": "celery_json",
                "filters": ["context", "sensitive_data"],
            },
        },
        "loggers": {
            "celery": {
                "handlers": ["celery_console", "celery_file"],
                "level": "INFO",
                "propagate": False,
            },
            "celery.task": {
                "handlers": ["celery_console", "celery_file"],
                "level": "INFO",
                "propagate": False,
            },
            "celery.worker": {
                "handlers": ["celery_console", "celery_file"],
                "level": "INFO",
                "propagate": False,
            },
            "celery.beat": {
                "handlers": ["celery_console", "celery_file"],
                "level": "INFO",
                "propagate": False,
            },
            "celery.error": {
                "handlers": ["celery_console", "celery_error_file"],
                "level": "ERROR",
                "propagate": False,
            },
        },
        "root": {
            "handlers": ["celery_console"],
            "level": levels.get("root", "INFO"),
        },
    }
    
    # Environment-specific adjustments
    if environment == "development":
        # More verbose logging in development
        config["loggers"]["celery"]["level"] = "DEBUG"
        config["loggers"]["celery.task"]["level"] = "DEBUG"
    elif environment == "production":
        # Less console output in production
        config["handlers"]["celery_console"]["level"] = "WARNING"
        
        # Add Sentry handler if available
        if getattr(settings, "SENTRY_DSN", None):
            config["handlers"]["sentry"] = {
                "level": "ERROR",
                "class": "core.logging.handlers.BufferedSentryHandler",
                "filters": ["context", "sensitive_data"],
            }
            # Add sentry to all error loggers
            config["loggers"]["celery.error"]["handlers"].append("sentry")
    
    return config


def configure_celery_logging(app, environment: str | None = None):
    """
    Configure Celery logging after the Celery app is created.
    
    This should be called in your celery.py file after creating the app.
    
    Args:
        app: Celery application instance
        environment: Override environment detection
        
    Example:
        from celery import Celery
        from core.logging.config import configure_celery_logging
        
        app = Celery('myapp')
        configure_celery_logging(app)
    """
    import logging.config

    from celery.signals import setup_logging
    
    # Detect environment if not provided
    if environment is None:
        environment = getattr(settings, "ENVIRONMENT", "development")
    
    @setup_logging.connect
    def setup_celery_logging(loglevel=None, logfile=None, format=None, colorize=None, **kwargs):
        """Configure Celery logging using our custom configuration."""
        config = get_celery_logging_config(environment=environment)
        logging.config.dictConfig(config)
        
        # Set task context
        from core.logging.context import set_task_context
        if hasattr(app, "current_task") and app.current_task:
            set_task_context(
                task_id=app.current_task.request.id,
                task_name=app.current_task.name
            )
    
    # Configure task logging
    from celery.signals import task_failure, task_postrun, task_prerun
    
    @task_prerun.connect
    def setup_task_logging(task_id=None, task=None, args=None, kwargs=None, **kw):
        """Set up logging context before task execution."""
        from core.logging.context import set_task_context
        set_task_context(task_id=task_id, task_name=task.name)
        
        logger = logging.getLogger("celery.task")
        logger.info(
            f"Starting task {task.name}",
            extra={
                "task_id": task_id,
                "task_name": task.name,
                "args": args,
                "kwargs": kwargs,
            }
        )
    
    @task_postrun.connect
    def cleanup_task_logging(task_id=None, task=None, retval=None, state=None, **kw):
        """Clean up logging context after task execution."""
        from core.logging.context import clear_task_context
        
        logger = logging.getLogger("celery.task")
        logger.info(
            f"Task {task.name} completed with state: {state}",
            extra={
                "task_id": task_id,
                "task_name": task.name,
                "state": state,
            }
        )
        
        clear_task_context()
    
    @task_failure.connect
    def log_task_failure(task_id=None, exception=None, args=None, kwargs=None, 
                        traceback=None, einfo=None, **kw):
        """Log task failures with full context."""
        logger = logging.getLogger("celery.error")
        logger.error(
            f"Task failed: {exception}",
            exc_info=True,
            extra={
                "task_id": task_id,
                "exception": str(exception),
                "args": args,
                "kwargs": kwargs,
            }
        )
