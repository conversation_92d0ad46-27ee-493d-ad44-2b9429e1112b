"""
Sentry-integrated logging system for SocialManager.

This package provides:
- Context-aware logging with automatic request/task/user context
- Sensitive data filtering
- Rate limiting for duplicate messages
- Sentry integration with proper error handling
"""

from .config import configure_logging, get_django_logging_config
from .formatters import ColoredFormatter, JSONFormatter
from .logger import (
    ContextLogger,
    add_context,
    clear_context,
    get_context,
    get_logger,
    request_id_var,
    set_context,
    set_request_id,
    set_task_id,
    set_user_id,
    task_id_var,
    user_id_var,
)
from .setup import setup_logging

__all__ = [
    # Main components
    "ContextLogger",
    "get_logger",
    "get_context",
    "set_context",
    "add_context",
    "clear_context",
    # Formatters
    "ColoredFormatter",
    "JSONFormatter",
    # Context variables and setters
    "request_id_var",
    "set_request_id",
    "set_task_id",
    "set_user_id",
    "task_id_var",
    "user_id_var",
    # Setup
    "setup_logging",
    # Config
    "get_django_logging_config",
    "configure_logging",
]
