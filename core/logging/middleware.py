"""
Django middleware for automatic logging context injection.

This middleware automatically sets request context for all Django requests,
including request_id, user_id, and other request metadata.
"""

import time
import uuid
from collections.abc import Callable

from django.http import HttpRequest, HttpResponse
from django.utils.deprecation import MiddlewareMixin

from core.logging.logger import (
    ContextLogger,
    add_context,
    clear_context,
    set_context,
)


class LoggingContextMiddleware(MiddlewareMixin):
    """
    Middleware to add request context to all logs during request processing.
    
    This middleware:
    - Generates unique request_id for each request
    - Adds user information to logging context
    - Tracks request timing
    - Ensures context cleanup after request
    - Logs request start/end with timing
    """
    
    def __init__(self, get_response: Callable | None = None):
        """
        Initialize middleware.
        
        Args:
            get_response: The next middleware or view
        """
        self.get_response = get_response
        super().__init__(get_response)
        
        # Конфигурация
        self.log_request_start = True
        self.log_request_end = True
        self.log_slow_requests = True
        self.slow_request_threshold = 1.0  # секунды
        
        # Логгер для самого middleware
        self.logger = ContextLogger.get_logger(__name__)
        
    def process_request(self, request: HttpRequest) -> HttpResponse | None:
        """
        Process incoming request and set up logging context.
        
        Args:
            request: The HTTP request
            
        Returns:
            None to continue processing
        """
        # Генерируем уникальный request_id или используем существующий из заголовка
        request_id = request.META.get("HTTP_X_REQUEST_ID") or self._generate_request_id()
        request.request_id = request_id
        
        # Сохраняем время начала
        request._logging_start_time = time.time()
        
        # Устанавливаем базовый контекст
        context = {
            "request_id": request_id,
            "method": request.method,
            "path": request.path,
            "remote_addr": self._get_client_ip(request),
            "user_agent": request.META.get("HTTP_USER_AGENT", ""),
        }
        
        # Добавляем информацию о пользователе
        if hasattr(request, "user") and request.user.is_authenticated:
            context.update({
                "user_id": request.user.id,
                "username": request.user.username,
                "user_email": getattr(request.user, "email", None),
            })
        
        # Добавляем дополнительные headers если нужно
        self._add_custom_headers(request, context)
        
        # Устанавливаем контекст
        set_context(**context)
        
        # Логируем начало запроса
        if self.log_request_start:
            self.logger.info(
                "Request started",
                extra_headers=self._get_relevant_headers(request)
            )
        
        return None
    
    def process_response(self, request: HttpRequest, response: HttpResponse) -> HttpResponse:
        """
        Process response and log request completion.
        
        Args:
            request: The HTTP request
            response: The HTTP response
            
        Returns:
            The response object
        """
        # Вычисляем время выполнения
        duration = None
        if hasattr(request, "_logging_start_time"):
            duration = time.time() - request._logging_start_time
            
        # Добавляем информацию о response
        add_context(
            status_code=response.status_code,
            response_size=len(response.content) if hasattr(response, "content") else None,
            duration_ms=int(duration * 1000) if duration else None
        )
        
        # Логируем завершение запроса
        if self.log_request_end:
            log_data = {
                "status_code": response.status_code,
            }
            
            if duration is not None:
                log_data["duration_seconds"] = round(duration, 3)
                
                # Предупреждение о медленных запросах
                if self.log_slow_requests and duration > self.slow_request_threshold:
                    self.logger.warning(
                        f"Slow request detected: {duration:.2f}s",
                        **log_data
                    )
                else:
                    self.logger.info("Request completed", **log_data)
            else:
                self.logger.info("Request completed", **log_data)
        
        # Добавляем request_id в заголовки ответа
        if hasattr(request, "request_id"):
            response["X-Request-ID"] = request.request_id
        
        # Очищаем контекст
        clear_context()
        
        return response
    
    def process_exception(self, request: HttpRequest, exception: Exception) -> HttpResponse | None:
        """
        Process exceptions and log them with context.
        
        Args:
            request: The HTTP request
            exception: The exception that occurred
            
        Returns:
            None to continue with default exception handling
        """
        # Логируем исключение с полным контекстом
        self.logger.exception(
            f"Unhandled exception in view: {exception.__class__.__name__}",
            exception_type=exception.__class__.__name__,
            exception_message=str(exception),
            view_name=self._get_view_name(request)
        )
        
        # Django продолжит обработку исключения
        return None
    
    def _generate_request_id(self) -> str:
        """
        Generate unique request ID.
        
        Returns:
            Unique request identifier
        """
        # Можно использовать разные форматы
        # return uuid.uuid4().hex  # 32 символа без дефисов
        return str(uuid.uuid4())  # С дефисами для читаемости
    
    def _get_client_ip(self, request: HttpRequest) -> str:
        """
        Get client IP address from request.
        
        Handles X-Forwarded-For and X-Real-IP headers.
        
        Args:
            request: The HTTP request
            
        Returns:
            Client IP address
        """
        # Проверяем заголовки прокси
        x_forwarded_for = request.META.get("HTTP_X_FORWARDED_FOR")
        if x_forwarded_for:
            # Берем первый IP из списка
            ip = x_forwarded_for.split(",")[0].strip()
        else:
            # Проверяем X-Real-IP
            ip = request.META.get("HTTP_X_REAL_IP")
            if not ip:
                # Используем REMOTE_ADDR
                ip = request.META.get("REMOTE_ADDR", "")
        
        return ip or ""
    
    def _get_relevant_headers(self, request: HttpRequest) -> dict:
        """
        Extract relevant headers for logging.
        
        Args:
            request: The HTTP request
            
        Returns:
            Dictionary of relevant headers
        """
        relevant_headers = {
            "Accept": request.META.get("HTTP_ACCEPT", ""),
            "Accept-Language": request.META.get("HTTP_ACCEPT_LANGUAGE", ""),
            "Content-Type": request.META.get("CONTENT_TYPE", ""),
            "Referer": request.META.get("HTTP_REFERER", ""),
        }
        
        # Убираем пустые значения
        return {k: v for k, v in relevant_headers.items() if v}
    
    def _add_custom_headers(self, request: HttpRequest, context: dict) -> None:
        """
        Add custom headers to context if present.
        
        Args:
            request: The HTTP request
            context: Context dictionary to update
        """
        # Correlation ID для распределенного трейсинга
        correlation_id = request.META.get("HTTP_X_CORRELATION_ID")
        if correlation_id:
            context["correlation_id"] = correlation_id
            
        # Session ID если есть
        if hasattr(request, "session") and request.session.session_key:
            context["session_id"] = request.session.session_key
            
        # API версия
        api_version = request.META.get("HTTP_X_API_VERSION")
        if api_version:
            context["api_version"] = api_version
    
    def _get_view_name(self, request: HttpRequest) -> str | None:
        """
        Get view name from request.
        
        Args:
            request: The HTTP request
            
        Returns:
            View name if available
        """
        if hasattr(request, "resolver_match") and request.resolver_match:
            if request.resolver_match.func:
                return f"{request.resolver_match.func.__module__}.{request.resolver_match.func.__name__}"
            elif request.resolver_match.view_name:
                return request.resolver_match.view_name
        return None


class RequestIDMiddleware(LoggingContextMiddleware):
    """
    Simplified middleware that only adds request_id.
    
    Use this if you already have other middleware for logging
    but just need request_id functionality.
    """
    
    def __init__(self, get_response: Callable | None = None):
        super().__init__(get_response)
        self.log_request_start = False
        self.log_request_end = False
    
    def process_request(self, request: HttpRequest) -> HttpResponse | None:
        """Only set request_id without full logging."""
        request_id = self._generate_request_id()
        request.request_id = request_id
        set_context(request_id=request_id)
        return None
    
    def process_response(self, request: HttpRequest, response: HttpResponse) -> HttpResponse:
        """Clear context without logging."""
        clear_context()
        return response


class APILoggingMiddleware(LoggingContextMiddleware):
    """
    Extended middleware for API endpoints with additional logging.
    
    Adds:
    - Request/response body logging (with size limits)
    - API key/token information
    - Response time SLA tracking
    """
    
    def __init__(self, get_response: Callable | None = None):
        super().__init__(get_response)
        self.log_request_body = True
        self.log_response_body = True
        self.max_body_length = 1000  # Максимальная длина тела для логирования
        self.api_sla_ms = 200  # SLA для API в миллисекундах
    
    def process_request(self, request: HttpRequest) -> HttpResponse | None:
        """Process API request with body logging."""
        # Вызываем родительский метод
        result = super().process_request(request)
        
        # Добавляем API-специфичный контекст
        if request.path.startswith("/api/"):
            # API ключ или токен
            api_key = request.META.get("HTTP_X_API_KEY")
            if api_key:
                # Маскируем ключ для безопасности
                masked_key = f"{api_key[:4]}...{api_key[-4:]}" if len(api_key) > 8 else "***"
                add_context(api_key=masked_key)
            
            # Логируем тело запроса для POST/PUT/PATCH
            if self.log_request_body and request.method in ["POST", "PUT", "PATCH"]:
                try:
                    body = request.body.decode("utf-8")
                    if len(body) > self.max_body_length:
                        body = body[:self.max_body_length] + "...(truncated)"
                    self.logger.debug("API request body", body=body)
                except Exception:
                    self.logger.debug("API request body", body="<binary or unparseable>")
        
        return result
    
    def process_response(self, request: HttpRequest, response: HttpResponse) -> HttpResponse:
        """Process API response with SLA checking."""
        # Проверяем SLA для API
        if request.path.startswith("/api/") and hasattr(request, "_logging_start_time"):
            duration_ms = int((time.time() - request._logging_start_time) * 1000)
            
            if duration_ms > self.api_sla_ms:
                self.logger.warning(
                    f"API SLA exceeded: {duration_ms}ms > {self.api_sla_ms}ms",
                    duration_ms=duration_ms,
                    sla_ms=self.api_sla_ms
                )
        
        # Вызываем родительский метод
        return super().process_response(request, response)