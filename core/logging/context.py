"""
Logging context management for request and task tracking.

This module provides context variables for tracking:
- Request ID across HTTP requests
- User ID for authenticated users
- Task ID for Celery tasks
- Custom context for specific operations
"""

import uuid
from contextvars import ContextVar
from typing import Any

# Context variables for tracking across async operations
request_id_var: ContextVar[str | None] = ContextVar("request_id", default=None)
user_id_var: ContextVar[int | None] = ContextVar("user_id", default=None)
task_id_var: ContextVar[str | None] = ContextVar("task_id", default=None)
task_name_var: ContextVar[str | None] = ContextVar("task_name", default=None)

# Additional context for custom data
extra_context_var: ContextVar[dict[str, Any]] = ContextVar("extra_context", default={})


def set_request_context(request_id: str | None = None, user_id: int | None = None) -> str:
    """
    Set request context for logging.
    
    Args:
        request_id: Unique request identifier (generated if not provided)
        user_id: ID of authenticated user
        
    Returns:
        The request ID that was set
    """
    if request_id is None:
        request_id = str(uuid.uuid4())
    
    request_id_var.set(request_id)
    if user_id is not None:
        user_id_var.set(user_id)
    
    return request_id


def set_task_context(task_id: str, task_name: str | None = None) -> None:
    """
    Set task context for Celery task logging.
    
    Args:
        task_id: Celery task ID
        task_name: Name of the task
    """
    task_id_var.set(task_id)
    if task_name:
        task_name_var.set(task_name)


def clear_request_context() -> None:
    """Clear request-related context variables."""
    request_id_var.set(None)
    user_id_var.set(None)


def clear_task_context() -> None:
    """Clear task-related context variables."""
    task_id_var.set(None)
    task_name_var.set(None)


def add_context(**kwargs) -> None:
    """
    Add custom context data for logging.
    
    Args:
        **kwargs: Key-value pairs to add to context
    """
    current = extra_context_var.get()
    updated = {**current, **kwargs}
    extra_context_var.set(updated)


def clear_extra_context() -> None:
    """Clear all extra context data."""
    extra_context_var.set({})


def get_logging_context() -> dict[str, Any]:
    """
    Get all current logging context.
    
    Returns:
        Dictionary with all context variables
    """
    context: dict[str, Any] = {}
    
    request_id = request_id_var.get()
    if request_id:
        context["request_id"] = request_id
    
    user_id = user_id_var.get()
    if user_id:
        context["user_id"] = user_id
    
    task_id = task_id_var.get()
    if task_id:
        context["task_id"] = task_id
    
    task_name = task_name_var.get()
    if task_name:
        context["task_name"] = task_name
    
    # Add any extra context
    extra = extra_context_var.get()
    if extra:
        context.update(extra)
    
    return context


class LoggingContextFilter:
    """
    Logging filter that adds context variables to log records.
    
    This filter automatically adds request_id, user_id, task_id, etc.
    to all log records for inclusion in formatted output.
    """
    
    def filter(self, record):
        """Add context variables to log record."""
        # Get all context
        context = get_logging_context()
        
        # Add each context variable to the record
        for key, value in context.items():
            if not hasattr(record, key):
                setattr(record, key, value)
        
        # Ensure all expected fields exist (with None if not set)
        for field in ["request_id", "user_id", "task_id", "task_name"]:
            if not hasattr(record, field):
                setattr(record, field, None)
        
        return True