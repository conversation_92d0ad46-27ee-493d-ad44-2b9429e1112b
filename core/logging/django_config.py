"""
Django configuration for structlog integration.

This module provides configuration functions that should be called
in Django settings to set up structlog with proper processors.
"""

from typing import Any

import structlog

from core.logging.processors import add_app_context, filter_sensitive_keys


def get_structlog_processors(include_sentry: bool = True) -> list[Any]:
    """
    Get list of structlog processors for Django.
    
    Args:
        include_sentry: Whether to include Sentry integration
        
    Returns:
        List of configured processors
    """
    processors = [
        # Add standard structlog processors
        structlog.stdlib.filter_by_level,
        structlog.stdlib.add_logger_name,
        structlog.stdlib.add_log_level,
        structlog.processors.TimeStamper(fmt="iso"),
        structlog.stdlib.PositionalArgumentsFormatter(),
        structlog.processors.StackInfoRenderer(),
        structlog.processors.format_exc_info,
        structlog.processors.UnicodeDecoder(),
        
        # Add our custom processors
        add_app_context,
        filter_sensitive_keys,
    ]
    
    # Add Django request processor if available
    try:
        from core.logging.processors import django_request_processor
        processors.append(django_request_processor)
    except ImportError:
        pass
    
    # Add Celery task processor if available
    try:
        from core.logging.processors import celery_task_processor
        processors.append(celery_task_processor)
    except ImportError:
        pass
    
    # Add Sentry processor if requested and available
    if include_sentry:
        try:
            from sentry_sdk.integrations.logging import EventFromLogRecord
            processors.append(EventFromLogRecord())
        except ImportError:
            pass
    
    # Final processor for formatting
    processors.append(
        structlog.stdlib.ProcessorFormatter.wrap_for_formatter
    )
    
    return processors


def configure_structlog(
    processors: list[Any] | None = None,
    cache_logger_on_first_use: bool = True,
    wrapper_class: Any | None = None,
    context_class: Any | None = None,
) -> None:
    """
    Configure structlog for Django.
    
    Args:
        processors: List of processors (if None, uses default)
        cache_logger_on_first_use: Whether to cache loggers
        wrapper_class: Wrapper class for loggers
        context_class: Context class (default: dict)
    """
    if processors is None:
        processors = get_structlog_processors()
    
    structlog.configure(
        processors=processors[:-1],  # Remove the final formatter
        context_class=context_class or dict,
        logger_factory=structlog.stdlib.LoggerFactory(),
        wrapper_class=wrapper_class or structlog.stdlib.BoundLogger,
        cache_logger_on_first_use=cache_logger_on_first_use,
    )


def get_logging_config(
    log_level: str = "INFO",
    logs_dir: str = "logs",
    include_structlog: bool = True,
    include_sentry: bool = True,
) -> dict[str, Any]:
    """
    Get Django LOGGING configuration with structlog integration.
    
    Args:
        log_level: Default log level
        logs_dir: Directory for log files
        include_structlog: Whether to include structlog processors
        include_sentry: Whether to include Sentry integration
        
    Returns:
        Django LOGGING configuration dict
    """
    import os
    
    # Ensure logs directory exists
    os.makedirs(logs_dir, exist_ok=True)
    
    # Build formatters based on structlog availability
    formatters: dict[str, Any] = {
        "simple": {
            "format": "[{levelname}] {asctime} {message}",
            "style": "{",
        },
    }
    
    if include_structlog:
        formatters["verbose"] = {
            "()": structlog.stdlib.ProcessorFormatter,
            "processor": structlog.dev.ConsoleRenderer()
            if os.environ.get("DJANGO_ENV") == "local"
            else structlog.processors.JSONRenderer(),
            "foreign_pre_chain": get_structlog_processors(include_sentry)[:-1],
        }
        formatters["json"] = {
            "()": structlog.stdlib.ProcessorFormatter,
            "processor": structlog.processors.JSONRenderer(),
            "foreign_pre_chain": get_structlog_processors(include_sentry)[:-1],
        }
    else:
        formatters["verbose"] = {
            "format": "[{levelname}] {asctime} {module} {process:d} {thread:d} {message}",
            "style": "{",
        }
        formatters["json"] = {
            "format": '{"time": "%(asctime)s", "level": "%(levelname)s", "message": "%(message)s"}',
        }
    
    config = {
        "version": 1,
        "disable_existing_loggers": False,
        "formatters": formatters,
        "handlers": {
            "console": {
                "class": "logging.StreamHandler",
                "formatter": "verbose" if include_structlog else "simple",
            },
            "file": {
                "class": "logging.handlers.RotatingFileHandler",
                "filename": os.path.join(logs_dir, "django.log"),
                "maxBytes": 10485760,  # 10MB
                "backupCount": 5,
                "formatter": "json" if include_structlog else "verbose",
            },
            "error_file": {
                "class": "logging.handlers.RotatingFileHandler",
                "filename": os.path.join(logs_dir, "errors.log"),
                "maxBytes": 10485760,  # 10MB
                "backupCount": 5,
                "formatter": "json" if include_structlog else "verbose",
                "level": "ERROR",
            },
        },
        "root": {
            "handlers": ["console", "file"],
            "level": log_level,
        },
        "loggers": {
            "django": {
                "handlers": ["console", "file"],
                "level": log_level,
                "propagate": False,
            },
            "django.request": {
                "handlers": ["console", "error_file"],
                "level": "ERROR",
                "propagate": False,
            },
            "core": {
                "handlers": ["console", "file"],
                "level": "DEBUG",
                "propagate": False,
            },
            "instagram_manager": {
                "handlers": ["console", "file"],
                "level": "DEBUG",
                "propagate": False,
            },
            "telegram_manager": {
                "handlers": ["console", "file"],
                "level": "DEBUG",
                "propagate": False,
            },
            "celery": {
                "handlers": ["console", "file"],
                "level": log_level,
                "propagate": False,
            },
        },
    }
    
    return config


def setup_django_logging(
    settings_module: Any,
    include_structlog: bool = True,
    include_sentry: bool = True,
) -> None:
    """
    Setup Django logging with structlog integration.
    
    This function should be called in Django settings:
    
    from core.logging.django_config import setup_django_logging
    setup_django_logging(globals())
    
    Args:
        settings_module: Django settings module (use globals())
        include_structlog: Whether to include structlog
        include_sentry: Whether to include Sentry integration
    """
    # Get logs directory from settings
    logs_dir = getattr(settings_module.get("LOGS_DIR"), "logs", "logs")
    log_level = getattr(settings_module.get("LOG_LEVEL"), "INFO", "INFO")
    
    # Configure structlog
    if include_structlog:
        configure_structlog()
    
    # Update LOGGING configuration
    settings_module["LOGGING"] = get_logging_config(
        log_level=log_level,
        logs_dir=logs_dir,
        include_structlog=include_structlog,
        include_sentry=include_sentry,
    )
    
    # Add middleware if not already present
    middleware = settings_module.get("MIDDLEWARE", [])
    logging_middleware = "core.logging.middleware.LoggingContextMiddleware"
    
    if logging_middleware not in middleware:
        # Add after SecurityMiddleware
        security_idx = next(
            (i for i, m in enumerate(middleware) if "SecurityMiddleware" in m),
            0
        )
        middleware.insert(security_idx + 1, logging_middleware)
        settings_module["MIDDLEWARE"] = middleware