# Logging System Documentation

## Overview

The SocialManager logging system provides a comprehensive, context-aware logging infrastructure with:

- **Context-aware logging** with automatic request/task/user context injection
- **Structured logging** with `structlog` integration  
- **Sensitive data filtering** to prevent leaking passwords, API keys, etc.
- **Rate limiting** to prevent log spam
- **Sentry integration** for error tracking
- **Environment-specific configurations** for local/staging/production

## Architecture

### Core Components

1. **ContextLogger** (`logger.py`) - Main logging interface with context management
2. **LoggingContextMiddleware** (`middleware.py`) - Automatic request context injection
3. **Formatters** (`formatters.py`) - JSON and colored console output
4. **Filters** (`filters.py`) - Sensitive data filtering and rate limiting
5. **Configuration** (`config.py`) - Centralized Django LOGGING configuration

### Context Variables

The system uses Python's `contextvars` to maintain context across async operations:

- `request_id` - Unique ID for each HTTP request
- `user_id` - Authenticated user ID
- `task_id` - Celery task ID (when in task context)

## Usage

### Basic Logging

```python
from core.logging import get_logger

logger = get_logger(__name__)

# Simple logging
logger.info("User logged in", user_id=123)

# With extra context
logger.error("Payment failed", 
    order_id=456,
    amount=99.99,
    error_code="INSUFFICIENT_FUNDS"
)
```

### Request Context

The `LoggingContextMiddleware` automatically adds:
- `request_id` - Unique request identifier
- `user_id` - If user is authenticated
- `path` - Request path
- `method` - HTTP method
- `status_code` - Response status
- `duration` - Request processing time

### Task Context

For Celery tasks:

```python
from core.logging import get_logger

logger = get_logger(__name__)

@app.task
def process_data(data_id):
    logger.info("Processing data", data_id=data_id)
    # task_id is automatically included
```

### Manual Context

```python
from core.logging import add_context, get_logger

logger = get_logger(__name__)

# Add context for a block
add_context(operation="bulk_import", source="instagram")
logger.info("Starting import")

# Context is automatically included in all logs within this scope
```

## Configuration

### Environment-Specific Settings

The system automatically configures itself based on the environment:

#### Local Development
- Colored console output for readability
- DEBUG level logging for app code
- SQL query logging (optional)
- File rotation at 10MB

#### Production
- JSON formatted logs for parsing
- INFO level logging
- Separate error and performance logs
- Sentry integration for errors
- Log rotation with compression

### Log Files

Logs are organized in subdirectories:

```
logs/
├── app/          # General application logs
├── errors/       # Error logs (ERROR level and above)
├── performance/  # Performance monitoring logs
├── security/     # Security-related logs
└── celery/       # Celery task logs
```

### Customization

To customize logging for your environment:

```python
# In settings/local.py or settings/production.py
from core.logging.config import get_django_logging_config

LOGGING = get_django_logging_config(
    environment="production",
    log_dir=Path("/custom/log/path"),
    enable_sentry=True,
)

# Add custom loggers
LOGGING["loggers"]["myapp"] = {
    "handlers": ["file", "error_file"],
    "level": "DEBUG",
    "propagate": False,
}
```

## Sensitive Data Protection

The `SensitiveDataFilter` automatically redacts:

- Passwords and password-like fields
- API keys and tokens
- Credit card numbers
- Social Security Numbers
- Email addresses (in certain contexts)
- Authorization headers
- Cookie values

Example:
```python
# This:
logger.info("User login", password="secret123", api_key="sk_live_xyz")

# Becomes:
# User login password=[REDACTED] api_key=[API_KEY]
```

## Rate Limiting

Duplicate messages are rate-limited to prevent log spam:

- General logs: Max 10 identical messages per 60 seconds
- Error logs: Max 5 identical errors per 5 minutes
- Burst allowance for legitimate repeated operations

## Sentry Integration

When `SENTRY_DSN` is configured:

1. Errors are automatically sent to Sentry
2. Request context is included (user, request_id, etc.)
3. Sensitive data is filtered before sending
4. Performance monitoring tracks slow requests
5. Custom tags and context are added

### Sentry Configuration

```python
# .env file
SENTRY_DSN=https://<EMAIL>/yyy
DJANGO_ENV=production  # or staging, development
```

Environment-specific settings:
- **Development**: 100% transaction sampling, full debug
- **Staging**: 50% transaction sampling
- **Production**: 10% transaction sampling, optimized for performance

## Best Practices

1. **Use structured logging**:
   ```python
   # Good
   logger.info("Order processed", order_id=123, amount=99.99)
   
   # Avoid
   logger.info(f"Order {order_id} processed for ${amount}")
   ```

2. **Include relevant context**:
   ```python
   logger.error("API call failed",
       endpoint=url,
       status_code=response.status_code,
       retry_count=retry_count
   )
   ```

3. **Use appropriate log levels**:
   - `DEBUG`: Detailed information for diagnosing problems
   - `INFO`: General informational messages
   - `WARNING`: Something unexpected but handled
   - `ERROR`: Error that prevented an operation
   - `CRITICAL`: System-wide critical issues

4. **Let middleware handle request context** - Don't manually log request IDs

5. **Use logger names based on module**:
   ```python
   logger = get_logger(__name__)  # e.g., "instagram_manager.services"
   ```

## Troubleshooting

### Logs not appearing

1. Check environment - test environments suppress most logs
2. Verify log level - DEBUG logs won't show in production
3. Check file permissions for log directory
4. Ensure middleware is properly configured

### Performance impact

1. Use rate limiting for high-frequency operations
2. Avoid logging large objects - log IDs instead
3. Use sampling for performance logs in production
4. Consider async logging for high-throughput scenarios

### Sentry not receiving errors

1. Verify `SENTRY_DSN` is set correctly
2. Check network connectivity
3. Ensure error is not in ignored list
4. Verify Sentry initialization in settings