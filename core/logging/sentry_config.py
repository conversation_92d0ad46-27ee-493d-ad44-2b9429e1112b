"""
Sentry SDK configuration module.

This module provides centralized configuration for Sentry SDK
with environment-specific settings and advanced features.
"""

import logging
from collections.abc import Callable
from typing import TYPE_CHECKING, Any

import structlog
from django.conf import settings

if TYPE_CHECKING:
    from types import ModuleType

logger = structlog.get_logger(__name__)


class SentryConfig:
    """
    Centralized Sentry configuration with environment-specific settings.
    
    This class provides methods to configure Sentry SDK with:
    - Environment-specific settings
    - Custom integrations
    - Advanced sampling rules
    - Error filtering and processing
    - Performance monitoring
    """
    
    def __init__(self, environment: str = "development"):
        """
        Initialize Sentry configuration.
        
        Args:
            environment: Current environment (development/staging/production)
        """
        self.environment = environment
        self.sentry_sdk: ModuleType | None = None
        self._initialized = False
        
    def initialize(
        self,
        dsn: str,
        traces_sample_rate: float = 0.1,
        profiles_sample_rate: float = 0.1,
        send_default_pii: bool = False,
        debug: bool = False,
        integrations: list[Any] | None = None,
        before_send: Callable | None = None,
        before_send_transaction: Callable | None = None,
        **kwargs
    ) -> None:
        """
        Initialize Sentry SDK with configuration.
        
        Args:
            dsn: Sentry DSN
            traces_sample_rate: Transaction sampling rate
            profiles_sample_rate: Profile sampling rate
            send_default_pii: Whether to send personally identifiable information
            debug: Enable debug mode
            integrations: List of integrations to enable
            before_send: Custom error processor
            before_send_transaction: Custom transaction processor
            **kwargs: Additional Sentry configuration options
        """
        if self._initialized:
            logger.warning("Sentry already initialized, skipping")
            return
            
        try:
            import sentry_sdk
            self.sentry_sdk = sentry_sdk
            
            # Configure integrations
            if integrations is None:
                integrations = self._get_default_integrations()
            
            # Configure Sentry
            # Set defaults that can be overridden by kwargs
            config: dict[str, Any] = {
                "dsn": dsn,
                "environment": self.environment,
                "traces_sample_rate": traces_sample_rate,
                "profiles_sample_rate": profiles_sample_rate,
                "send_default_pii": send_default_pii,
                "debug": debug,
                "integrations": integrations,
                "before_send": before_send or self.before_send,
                "before_send_transaction": before_send_transaction or self.before_send_transaction,
                "traces_sampler": self.traces_sampler,
                "release": self._get_release(),
                "attach_stacktrace": True,
                "max_breadcrumbs": 50,
            }
            
            # Update with any additional kwargs (which may override defaults)
            config.update(kwargs)
            
            sentry_sdk.init(**config)
            
            self._initialized = True
            logger.info(
                "Sentry initialized",
                environment=self.environment,
                traces_sample_rate=traces_sample_rate,
                profiles_sample_rate=profiles_sample_rate,
            )
            
        except ImportError:
            print("ERROR: sentry-sdk not installed, Sentry monitoring disabled")
        except Exception as e:
            print(f"ERROR: Failed to initialize Sentry: {e!s}")
    
    def _get_default_integrations(self) -> list[Any]:
        """
        Get default integrations based on environment.
        
        Returns:
            List of Sentry integrations
        """
        integrations: list[Any] = []
        
        try:
            from sentry_sdk.integrations.django import DjangoIntegration
            integrations.append(
                DjangoIntegration(
                    transaction_style="function_name",
                    middleware_spans=True,
                    signals_spans=True,
                )
            )
        except ImportError:
            pass
            
        try:
            from sentry_sdk.integrations.celery import CeleryIntegration
            integrations.append(
                CeleryIntegration(
                    monitor_beat_tasks=True,
                    propagate_traces=True,
                )
            )
        except ImportError:
            pass
            
        try:
            from sentry_sdk.integrations.redis import RedisIntegration
            integrations.append(RedisIntegration())
        except ImportError:
            pass
            
        try:
            from sentry_sdk.integrations.logging import LoggingIntegration
            integrations.append(
                LoggingIntegration(
                    level=logging.INFO,
                    event_level=logging.ERROR,
                )
            )
        except ImportError:
            pass
            
        try:
            from sentry_sdk.integrations.excepthook import ExcepthookIntegration
            integrations.append(
                ExcepthookIntegration(
                    always_run=True,
                )
            )
        except ImportError:
            pass
            
        return integrations
    
    def before_send(self, event: dict[str, Any], hint: dict[str, Any]) -> dict[str, Any] | None:
        """
        Process events before sending to Sentry.
        
        This method:
        - Filters sensitive data
        - Adds custom context
        - Can drop events by returning None
        
        Args:
            event: Event data
            hint: Additional event information
            
        Returns:
            Modified event or None to drop
        """
        # Skip health check errors
        if "exc_info" in hint:
            exc_type, exc_value, tb = hint["exc_info"]
            if exc_type.__name__ == "Http404":
                request = event.get("request", {})
                url = request.get("url", "")
                if any(pattern in url for pattern in ["/health/", "/favicon.ico", "/robots.txt"]):
                    return None
        
        # Apply sensitive data filter
        try:
            from core.logging.filters import SensitiveDataFilter
            filter_instance = SensitiveDataFilter()
            
            # Filter request data
            if "request" in event and "data" in event["request"]:
                event["request"]["data"] = filter_instance._scrub_dict(
                    event["request"]["data"]
                )
                
            # Filter extra context
            if "extra" in event:
                event["extra"] = filter_instance._scrub_dict(event["extra"])
                
            # Filter user context
            if "user" in event:
                event["user"] = filter_instance._scrub_dict(event["user"])
                
        except ImportError:
            logger.warning("SensitiveDataFilter not available")
        
        # Add custom tags
        event["tags"] = event.get("tags", {})
        event["tags"]["environment"] = self.environment
        
        # Add request ID if available
        if "request" in event:
            request = event["request"]
            if hasattr(request, "id"):
                event["tags"]["request_id"] = str(request.id)
        
        return event
    
    def before_send_transaction(self, event: dict[str, Any], hint: dict[str, Any]) -> dict[str, Any] | None:
        """
        Process transactions before sending to Sentry.
        
        Args:
            event: Transaction event data
            hint: Additional information
            
        Returns:
            Modified event or None to drop
        """
        # Skip static file transactions
        transaction_name = event.get("transaction", "")
        if any(pattern in transaction_name for pattern in ["/static/", "/media/", "/__debug__/"]):
            return None
            
        # Skip health check transactions
        if "/health/" in transaction_name:
            return None
            
        # Add custom measurements
        if "measurements" not in event:
            event["measurements"] = {}
            
        return event
    
    def traces_sampler(self, sampling_context: dict[str, Any]) -> float:
        """
        Dynamic sampling for transactions.
        
        Args:
            sampling_context: Context for sampling decision
            
        Returns:
            Sample rate (0.0 to 1.0)
        """
        # Get transaction name
        transaction_name = sampling_context.get("transaction_context", {}).get("name", "")
        
        # High sampling for critical endpoints
        critical_endpoints = [
            "/api/instagram/import/",
            "/api/telegram/import/",
            "/api/comments/post/",
        ]
        if any(endpoint in transaction_name for endpoint in critical_endpoints):
            return 0.5 if self.environment == "production" else 1.0
            
        # Low sampling for static/media files
        if any(pattern in transaction_name for pattern in ["/static/", "/media/"]):
            return 0.01
            
        # Low sampling for health checks
        if "/health/" in transaction_name:
            return 0.01
            
        # Medium sampling for admin
        if "/admin/" in transaction_name:
            return 0.2 if self.environment == "production" else 0.5
            
        # Default sampling based on environment
        default_rates = {
            "development": 1.0,
            "staging": 0.5,
            "production": 0.1,
        }
        return default_rates.get(self.environment, 0.1)
    
    def _get_release(self) -> str | None:
        """
        Get release version for Sentry.
        
        Returns:
            Release version string or None
        """
        # Try to get from environment variable
        import os
        release = os.environ.get("SENTRY_RELEASE")
        if release:
            return release
            
        # Try to get from git
        try:
            import subprocess
            result = subprocess.run(
                ["git", "rev-parse", "HEAD"],
                capture_output=True,
                text=True,
                check=True,
            )
            return result.stdout.strip()[:7]  # First 7 chars of commit hash
        except Exception:
            pass
            
        # Try to get from Django settings
        version = getattr(settings, "VERSION", None)
        if isinstance(version, str):
            return version
        return None
    
    def capture_exception(self, exception: Exception, **kwargs) -> str | None:
        """
        Capture exception to Sentry.
        
        Args:
            exception: Exception to capture
            **kwargs: Additional context
            
        Returns:
            Event ID or None
        """
        if not self._initialized or not self.sentry_sdk:
            return None
            
        result = self.sentry_sdk.capture_exception(exception, **kwargs)
        return result if isinstance(result, str) else None
    
    def capture_message(self, message: str, level: str = "info", **kwargs) -> str | None:
        """
        Capture message to Sentry.
        
        Args:
            message: Message to capture
            level: Log level
            **kwargs: Additional context
            
        Returns:
            Event ID or None
        """
        if not self._initialized or not self.sentry_sdk:
            return None
            
        result = self.sentry_sdk.capture_message(message, level=level, **kwargs)
        return result if isinstance(result, str) else None
    
    def set_context(self, key: str, value: dict[str, Any]) -> None:
        """
        Set custom context for Sentry.
        
        Args:
            key: Context key
            value: Context data
        """
        if not self._initialized or not self.sentry_sdk:
            return
            
        self.sentry_sdk.set_context(key, value)
    
    def set_tag(self, key: str, value: str) -> None:
        """
        Set tag for Sentry.
        
        Args:
            key: Tag key
            value: Tag value
        """
        if not self._initialized or not self.sentry_sdk:
            return
            
        self.sentry_sdk.set_tag(key, value)
    
    def set_user(self, user_info: dict[str, Any]) -> None:
        """
        Set user information for Sentry.
        
        Args:
            user_info: User data
        """
        if not self._initialized or not self.sentry_sdk:
            return
            
        self.sentry_sdk.set_user(user_info)


# Global instance
_sentry_config: SentryConfig | None = None


def get_sentry_config(environment: str = "development") -> SentryConfig:
    """
    Get or create global Sentry configuration instance.
    
    Args:
        environment: Current environment
        
    Returns:
        SentryConfig instance
    """
    global _sentry_config
    if _sentry_config is None:
        _sentry_config = SentryConfig(environment)
    return _sentry_config