"""
Custom logging filters for Sentry integration.

This module contains:
- SensitiveDataFilter: Filter that removes sensitive data from logs
- RateLimitFilter: Filter that prevents duplicate messages within time window
"""

import hashlib
import logging
import re
import time
from collections import deque
from dataclasses import dataclass
from functools import lru_cache
from re import <PERSON><PERSON>
from threading import Lock
from typing import Any, NamedTuple


@dataclass
class SensitivePattern:
    """
    Pattern for detecting sensitive data in logs.
    
    Attributes:
        name: Descriptive name of the pattern
        pattern: Compiled regex pattern
        replacement: Text to replace sensitive data with
        groups: Which regex groups contain sensitive data (default: all)
    """
    name: str
    pattern: Pattern[str]
    replacement: str = "[REDACTED]"
    groups: list[int] | None = None
    
    def replace(self, text: str) -> str:
        """Replace sensitive data in text using this pattern."""
        if self.groups:
            # Replace only specified groups
            def replacer(match):
                result = match.group(0)
                for group_num in self.groups:
                    if group_num <= len(match.groups()) and match.group(group_num):
                        # Get the start and end of the group relative to the full match
                        group_start = match.start(group_num) - match.start(0)
                        group_end = match.end(group_num) - match.start(0)
                        # Replace the group within the match
                        result = result[:group_start] + self.replacement + result[group_end:]
                return result
            return self.pattern.sub(replacer, text)
        else:
            # Simple replacement of entire match
            return self.pattern.sub(self.replacement, text)


class SensitiveDataFilter(logging.Filter):
    """
    Filter to remove sensitive data from log records.
    
    This filter:
    - Detects and masks passwords, API keys, tokens, etc.
    - Recursively cleans dict and list structures
    - Handles both message text and extra fields
    - Configurable patterns and field names
    """
    
    # Default patterns for detecting sensitive data
    DEFAULT_PATTERNS = [
        # API keys and tokens
        SensitivePattern(
            name="api_key",
            pattern=re.compile(
                r'\b(api[-_]?key|apikey|api[-_]?token|access[-_]?key)'
                r'[\s"\':=]+([a-zA-Z0-9\-_]{8,})\b',
                re.IGNORECASE
            ),
            replacement="[API_KEY]",
            groups=[2]  # Replace only the value, not the key
        ),
        
        # Passwords
        SensitivePattern(
            name="password",
            pattern=re.compile(
                r'\b(password|passwd|pwd|pass|пароль)'
                r'[\s"\':=]+([^\s"\'<>&,;]+)',
                re.IGNORECASE
            ),
            replacement="***",
            groups=[2]
        ),
        
        # Bearer tokens
        SensitivePattern(
            name="bearer_token",
            pattern=re.compile(
                r"\b(bearer|authorization)[\s:]+([a-zA-Z0-9\-_.]+)\b",
                re.IGNORECASE
            ),
            replacement="[TOKEN]",
            groups=[2]
        ),
        
        # Generic tokens
        SensitivePattern(
            name="token",
            pattern=re.compile(
                r'\b(token)[\s"\':=]+([a-zA-Z0-9\-_.]+)\b',
                re.IGNORECASE
            ),
            replacement="[TOKEN]",
            groups=[2]
        ),
        
        # JWT tokens
        SensitivePattern(
            name="jwt",
            pattern=re.compile(
                r"\b(eyJ[a-zA-Z0-9\-_]+\.eyJ[a-zA-Z0-9\-_]+\.[a-zA-Z0-9\-_]+)\b"
            ),
            replacement="[JWT_TOKEN]"
        ),
        
        # Secret keys
        SensitivePattern(
            name="secret",
            pattern=re.compile(
                r'\b(secret[-_]?key|private[-_]?key|secret)'
                r'[\s"\':=]+([a-zA-Z0-9\-_/+=]{16,})\b',
                re.IGNORECASE
            ),
            replacement="[SECRET]",
            groups=[2]
        ),
        
        # Sentry DSN
        SensitivePattern(
            name="sentry_dsn",
            pattern=re.compile(
                r"https?://[a-f0-9]+@[^/]+\.sentry\.io/\d+",
                re.IGNORECASE
            ),
            replacement="[SENTRY_DSN]"
        ),
        
        # Database URLs
        SensitivePattern(
            name="database_url",
            pattern=re.compile(
                r"(postgres|postgresql|mysql|mongodb|redis)://"
                r"([^:]+):([^@]+)@([^/]+)/([^\s]+)",
                re.IGNORECASE
            ),
            replacement=r"\1://[USER]:[PASSWORD]@\4/\5"
        ),
        
        # Email addresses
        SensitivePattern(
            name="email",
            pattern=re.compile(
                r"\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b"
            ),
            replacement="[EMAIL]"
        ),
        
        # Credit cards
        SensitivePattern(
            name="credit_card",
            pattern=re.compile(
                r"\b(?:\d[ -]*?){13,19}\b"
            ),
            replacement="[CREDIT_CARD]"
        ),
        
        # SSH keys
        SensitivePattern(
            name="ssh_key",
            pattern=re.compile(
                r"-----BEGIN (RSA |DSA |EC |OPENSSH )?PRIVATE KEY-----"
                r".*?"
                r"-----END (RSA |DSA |EC |OPENSSH )?PRIVATE KEY-----",
                re.DOTALL
            ),
            replacement="[PRIVATE_KEY]"
        ),
        
        # AWS keys
        SensitivePattern(
            name="aws_key",
            pattern=re.compile(
                r'\b(AKIA[0-9A-Z]{16}|aws_access_key_id|aws_secret_access_key)'
                r'[\s"\':=]+([a-zA-Z0-9/+=]{20,})\b',
                re.IGNORECASE
            ),
            replacement="[AWS_KEY]",
            groups=[2]
        ),
        
        # BrightData specific
        SensitivePattern(
            name="brightdata_token",
            pattern=re.compile(
                r'\b(BRIGHTDATA_API_TOKEN|BRIGHTDATA_API_KEY)'
                r'[\s"\':=]+([^\s"\']+)',
                re.IGNORECASE
            ),
            replacement="[BRIGHTDATA_KEY]",
            groups=[2]
        ),
        
        # Telegram API
        SensitivePattern(
            name="telegram_api",
            pattern=re.compile(
                r'\b(API_ID|API_HASH)'
                r'[\s"\':=]+([^\s"\']+)',
                re.IGNORECASE
            ),
            replacement="[TELEGRAM_API]",
            groups=[2]
        ),
        
        # Encryption keys
        SensitivePattern(
            name="encryption_key",
            pattern=re.compile(
                r'\b(FIELD_ENCRYPTION_KEY|ENCRYPTION_KEY)'
                r'[\s"\':=]+([^\s"\']+)',
                re.IGNORECASE
            ),
            replacement="[ENCRYPTION_KEY]",
            groups=[2]
        ),
    ]
    
    # Default names of fields that contain sensitive data
    DEFAULT_SENSITIVE_FIELDS = {
        # Authentication
        "password", "passwd", "pwd", "pass", "secret", "token",
        "api_key", "apikey", "api_token", "access_token", "auth_token",
        "refresh_token", "private_key", "secret_key", "session_key",
        
        # HTTP headers
        "authorization", "x-api-key", "x-auth-token", "x-access-token",
        "cookie", "set-cookie", "x-csrf-token", "x-xsrf-token",
        
        # Project-specific
        "brightdata_api_token", "field_encryption_key", "sentry_dsn",
        "database_url", "redis_url", "api_hash", "api_id",
        "celery_broker_url", "aws_secret_access_key", "aws_access_key_id",
        "majila_service_key", "google_application_credentials",
        
        # Payment data
        "credit_card", "card_number", "cvv", "cvc", "card_code",
        "account_number", "routing_number", "iban", "swift",
        
        # Personal data
        "ssn", "social_security", "passport", "driver_license",
        "tax_id", "national_id",
    }
    
    def __init__(self, 
                 patterns: list[SensitivePattern] | None = None,
                 sensitive_fields: set[str] | None = None,
                 additional_patterns: list[SensitivePattern] | None = None,
                 additional_fields: set[str] | None = None,
                 max_value_length: int = 10000,
                 enabled: bool = True):
        """
        Initialize SensitiveDataFilter.
        
        Args:
            patterns: Replace default patterns (if None, use defaults)
            sensitive_fields: Replace default sensitive fields
            additional_patterns: Add to default patterns
            additional_fields: Add to default sensitive fields
            max_value_length: Max length of values to process (performance)
            enabled: Whether filter is enabled
        """
        super().__init__()
        
        # Setup patterns
        if patterns is not None:
            self.patterns = patterns
        else:
            self.patterns = self.DEFAULT_PATTERNS.copy()
            
        if additional_patterns:
            self.patterns.extend(additional_patterns)
            
        # Setup fields
        if sensitive_fields is not None:
            self.sensitive_fields = sensitive_fields
        else:
            self.sensitive_fields = self.DEFAULT_SENSITIVE_FIELDS.copy()
            
        if additional_fields:
            self.sensitive_fields.update(additional_fields)
            
        # Normalize field names (lowercase)
        self.sensitive_fields = {f.lower() for f in self.sensitive_fields}
        
        self.max_value_length = max_value_length
        self.enabled = enabled
        
        # Cache for performance
        self._field_check_cache: dict[str, bool] = {}
    
    def filter(self, record: logging.LogRecord) -> bool:
        """
        Filter sensitive data from log record.
        
        Args:
            record: Log record to filter
            
        Returns:
            True (always pass the record through)
        """
        if not self.enabled:
            return True
            
        try:
            # Clean main message
            if hasattr(record, "msg") and record.msg:
                record.msg = self._scrub_value(record.msg)
                
            # Clean arguments
            if hasattr(record, "args") and record.args:
                record.args = self._scrub_value(record.args)
                
            # Clean extra fields
            self._scrub_record_dict(record)
            
        except Exception:
            # Don't break logging due to filter errors
            pass
            
        return True
    
    def _scrub_value(self, value: Any) -> Any:
        """
        Recursively scrub sensitive data from value.
        
        Args:
            value: Value to scrub
            
        Returns:
            Scrubbed value
        """
        if value is None:
            return None
            
        # Strings
        if isinstance(value, str):
            return self._scrub_string(value)
            
        # Dictionaries
        elif isinstance(value, dict):
            return self._scrub_dict(value)
            
        # Lists and tuples
        elif isinstance(value, (list, tuple)):
            scrubbed = [self._scrub_value(item) for item in value]
            return type(value)(scrubbed)
            
        # Numbers and booleans - don't touch
        elif isinstance(value, (int, float, bool)):
            return value
            
        # Objects with __dict__
        elif hasattr(value, "__dict__"):
            # Try to clean object attributes
            for attr_name in dir(value):
                if not attr_name.startswith("_"):
                    try:
                        attr_value = getattr(value, attr_name)
                        if isinstance(attr_value, (str, dict, list)):
                            setattr(value, attr_name, self._scrub_value(attr_value))
                    except (AttributeError, TypeError):
                        pass
            return value
            
        # Everything else convert to string and clean
        else:
            try:
                str_value = str(value)
                if len(str_value) <= self.max_value_length:
                    return self._scrub_string(str_value)
            except Exception:
                pass
                
        return value
    
    @lru_cache(maxsize=1024)
    def _scrub_string(self, text: str) -> str:
        """
        Scrub sensitive data from string.
        
        Args:
            text: String to scrub
            
        Returns:
            Scrubbed string
        """
        if not text or len(text) > self.max_value_length:
            return text
            
        # Apply all patterns
        result = text
        for pattern in self.patterns:
            try:
                result = pattern.replace(result)
            except Exception:
                # Ignore errors in individual patterns
                continue
                
        return result
    
    def _scrub_dict(self, data: dict[str, Any]) -> dict[str, Any]:
        """
        Scrub sensitive data from dictionary.
        
        Args:
            data: Dictionary to scrub
            
        Returns:
            Scrubbed dictionary
        """
        result = {}
        
        for key, value in data.items():
            # Check if key is sensitive
            if self._is_sensitive_field(key):
                result[key] = "[REDACTED]"
            else:
                # Recursively clean value
                result[key] = self._scrub_value(value)
                
        return result
    
    def _is_sensitive_field(self, field_name: str) -> bool:
        """
        Check if field name indicates sensitive data.
        
        Args:
            field_name: Field name to check
            
        Returns:
            True if field is sensitive
        """
        if not field_name:
            return False
            
        # Use cache for performance
        cache_key = field_name.lower()
        if cache_key in self._field_check_cache:
            return self._field_check_cache[cache_key]
            
        # Check exact match
        is_sensitive = cache_key in self.sensitive_fields
        
        # Check partial match
        if not is_sensitive:
            for sensitive_field in self.sensitive_fields:
                if sensitive_field in cache_key or cache_key in sensitive_field:
                    is_sensitive = True
                    break
                    
        # Cache result
        self._field_check_cache[cache_key] = is_sensitive
        
        return is_sensitive
    
    def _scrub_record_dict(self, record: logging.LogRecord) -> None:
        """
        Scrub sensitive data from record's __dict__.
        
        Args:
            record: Log record to scrub
        """
        # List of standard LogRecord attributes to skip
        standard_attrs = {
            "name", "msg", "args", "created", "filename", "funcName",
            "levelname", "levelno", "lineno", "module", "msecs",
            "pathname", "process", "processName", "relativeCreated",
            "thread", "threadName", "exc_info", "exc_text", "stack_info",
            "getMessage", "message"
        }
        
        # Process additional attributes
        for attr_name in list(record.__dict__.keys()):
            if attr_name not in standard_attrs:
                attr_value = getattr(record, attr_name, None)
                if attr_value is not None:
                    if self._is_sensitive_field(attr_name):
                        setattr(record, attr_name, "[REDACTED]")
                    else:
                        setattr(record, attr_name, self._scrub_value(attr_value))
    
    def add_pattern(self, pattern: SensitivePattern) -> None:
        """Add a new pattern to the filter."""
        self.patterns.append(pattern)
        
    def add_sensitive_field(self, field_name: str) -> None:
        """Add a new sensitive field name."""
        self.sensitive_fields.add(field_name.lower())
        
    def remove_pattern(self, pattern_name: str) -> None:
        """Remove a pattern by name."""
        self.patterns = [p for p in self.patterns if p.name != pattern_name]
        
    def list_patterns(self) -> list[str]:
        """Get list of pattern names."""
        return [p.name for p in self.patterns]
        
    def test_string(self, text: str) -> str:
        """Test the filter on a string (for debugging)."""
        return self._scrub_string(text)


def create_sensitive_data_filter(**kwargs) -> SensitiveDataFilter:
    """
    Factory function to create SensitiveDataFilter.
    
    Common configurations:
    - strict: Maximum security, may over-redact
    - balanced: Good security with less false positives
    - minimal: Only obvious sensitive data
    
    Args:
        **kwargs: Arguments for SensitiveDataFilter
        
    Returns:
        Configured filter instance
    """
    # Preset configurations
    presets = {
        "strict": {
            "additional_patterns": [
                SensitivePattern(
                    name="number_sequence",
                    pattern=re.compile(r"\b\d{6,}\b"),
                    replacement="[NUMBER]"
                ),
                SensitivePattern(
                    name="hex_string",
                    pattern=re.compile(r"\b[a-fA-F0-9]{16,}\b"),
                    replacement="[HEX]"
                ),
            ],
            "additional_fields": {
                "id", "uuid", "guid", "key", "hash", "signature"
            }
        },
        "balanced": {
            # Use default settings
        },
        "minimal": {
            "patterns": [
                p for p in SensitiveDataFilter.DEFAULT_PATTERNS
                if p.name in ["password", "api_key", "credit_card"]
            ],
            "sensitive_fields": {
                "password", "api_key", "token", "secret"
            }
        }
    }
    
    # Apply preset if specified
    preset = kwargs.pop("preset", None)
    if preset and preset in presets:
        preset_config = presets[preset]
        # Merge preset with passed parameters
        for key, value in preset_config.items():
            if key not in kwargs:
                kwargs[key] = value
                
    return SensitiveDataFilter(**kwargs)


class DjangoSensitiveDataFilter(SensitiveDataFilter):
    """
    Sensitive data filter with Django-specific patterns.
    """
    
    def __init__(self, **kwargs):
        # Add Django-specific patterns
        django_patterns = [
            SensitivePattern(
                name="django_secret_key",
                pattern=re.compile(
                    r"SECRET_KEY\s*=\s*['\"]([^'\"]+)['\"]"
                ),
                replacement='SECRET_KEY = "[DJANGO_SECRET]"'
            ),
            SensitivePattern(
                name="csrf_token",
                pattern=re.compile(
                    r'csrfmiddlewaretoken["\']?\s*[:=]\s*["\']?([a-zA-Z0-9]+)'
                ),
                replacement="csrfmiddlewaretoken=[CSRF]"
            ),
            SensitivePattern(
                name="session_id",
                pattern=re.compile(
                    r'sessionid["\']?\s*[:=]\s*["\']?([a-zA-Z0-9]+)'
                ),
                replacement="sessionid=[SESSION]"
            ),
        ]
        
        # Add Django-specific fields
        django_fields = {
            "csrfmiddlewaretoken", "sessionid", "django_secret",
            "_auth_user_id", "_auth_user_backend", "_auth_user_hash",
        }
        
        # Update kwargs
        additional_patterns = kwargs.get("additional_patterns", [])
        additional_patterns.extend(django_patterns)
        kwargs["additional_patterns"] = additional_patterns
        
        additional_fields = kwargs.get("additional_fields", set())
        additional_fields.update(django_fields)
        kwargs["additional_fields"] = additional_fields
        
        super().__init__(**kwargs)


class LogMessage(NamedTuple):
    """Represents a unique log message for rate limiting."""
    logger_name: str
    level: int
    message: str
    
    @classmethod
    def from_record(cls, record: logging.LogRecord) -> "LogMessage":
        """Create LogMessage from LogRecord."""
        # Получаем сообщение без форматирования времени
        try:
            message = record.getMessage()
        except Exception:
            message = str(record.msg)
            
        return cls(
            logger_name=record.name,
            level=record.levelno,
            message=message
        )
    
    def get_hash(self) -> str:
        """Get hash of the message for deduplication."""
        # Используем hash для быстрого сравнения
        content = f"{self.logger_name}:{self.level}:{self.message}"
        return hashlib.md5(content.encode("utf-8")).hexdigest()


class MessageStats:
    """Statistics for a rate-limited message."""
    
    def __init__(self):
        self.count = 0
        self.first_seen = time.time()
        self.last_seen = time.time()
        self.suppressed_count = 0
        
    def update(self, suppressed: bool = False):
        """Update statistics for this message."""
        self.last_seen = time.time()
        if suppressed:
            self.suppressed_count += 1
        else:
            self.count += 1
            
    def get_summary(self) -> str:
        """Get summary of suppressed messages."""
        if self.suppressed_count > 0:
            return (f" [Suppressed {self.suppressed_count} similar messages "
                   f"in the last {int(self.last_seen - self.first_seen)}s]")
        return ""


class RateLimitFilter(logging.Filter):
    """
    Filter that prevents duplicate messages within a time window.
    
    This filter:
    - Limits frequency of identical log messages
    - Tracks suppressed message counts
    - Provides summary of suppressed messages
    - Cleans up old entries to prevent memory leaks
    - Thread-safe for concurrent logging
    """
    
    def __init__(self, 
                 rate: int = 1,
                 per: float = 60.0,
                 burst: int = 1,
                 max_cache_size: int = 10000,
                 cleanup_interval: int = 300,
                 rate_limit_seconds: float | None = None):
        """
        Initialize RateLimitFilter.
        
        Args:
            rate: Number of allowed messages
            per: Time period in seconds  
            burst: Initial burst capacity
            max_cache_size: Maximum number of unique messages to track
            cleanup_interval: Seconds between cache cleanups
            rate_limit_seconds: Backward compatibility parameter (same as per)
        """
        super().__init__()
        
        # Handle backward compatibility
        if rate_limit_seconds is not None:
            # If old parameter provided, use it
            self.per = rate_limit_seconds
            self.rate = 1  # Default rate for backward compatibility
        else:
            # Use new parameters
            self.per = per
            self.rate = rate
            
        self.burst = burst
        self.max_cache_size = max_cache_size
        self.cleanup_interval = cleanup_interval
        
        # Store for backward compatibility access
        self.rate_limit_seconds = self.per
        
        # Thread-safe message tracking
        self._lock = Lock()
        self._messages: dict[str, deque[float]] = {}
        self._stats: dict[str, MessageStats] = {}
        self._last_cleanup = time.time()
        
        # LRU tracking for cache eviction
        self._access_order: deque[str] = deque()
        
    def filter(self, record: logging.LogRecord) -> bool:
        """
        Check if message should be logged based on rate limit.
        
        Args:
            record: Log record to check
            
        Returns:
            True if message should be logged, False if rate limited
        """
        # Create unique key for this message
        message_key = self._get_message_key(record)
        
        with self._lock:
            # Check if we should allow this message
            allowed = self._should_allow(message_key)
            
            # Record the message (for stats)
            self._record_message(message_key, allowed)
            
            # Add suppression info if message was blocked
            if not allowed and message_key in self._stats:
                # You could log a summary later, but for now just suppress
                pass
                    
            # Periodic cleanup
            if time.time() - self._last_cleanup > self.cleanup_interval:
                self._cleanup()
                
            return allowed
    
    def _should_allow(self, message_key: str) -> bool:
        """
        Check if message should be allowed based on rate limit.
        
        Args:
            message_key: Unique key for the message
            
        Returns:
            True if message should be allowed
        """
        current_time = time.time()
        
        # Initialize message queue if needed
        if message_key not in self._messages:
            self._messages[message_key] = deque()
            
        # Get message timestamps
        timestamps = self._messages[message_key]
        
        # Remove old timestamps outside the time window
        cutoff_time = current_time - self.per
        while timestamps and timestamps[0] < cutoff_time:
            timestamps.popleft()
            
        # Check if we're within rate limit
        if len(timestamps) < self.rate + self.burst - 1:
            # Allow the message
            timestamps.append(current_time)
            
            # Update access order for LRU
            if message_key in self._access_order:
                self._access_order.remove(message_key)
            self._access_order.append(message_key)
            
            # Enforce cache size limit
            if len(self._messages) > self.max_cache_size:
                self._evict_lru()
                
            return True
        else:
            # Rate limit exceeded
            return False
    
    def _record_message(self, message_key: str, allowed: bool) -> None:
        """
        Record message statistics.
        
        Args:
            message_key: Unique key for the message
            allowed: Whether message was allowed through
        """
        if message_key not in self._stats:
            self._stats[message_key] = MessageStats()
            
        self._stats[message_key].update(suppressed=not allowed)
    
    def _get_message_key(self, record: logging.LogRecord) -> str:
        """
        Generate unique key for a log message.
        
        Args:
            record: Log record
            
        Returns:
            Unique key for deduplication
        """
        # Create LogMessage and get its hash
        log_message = LogMessage.from_record(record)
        return log_message.get_hash()
    
    def _cleanup(self) -> None:
        """Clean up old entries to prevent memory leaks."""
        current_time = time.time()
        cutoff_time = current_time - self.per
        
        # Find keys to remove
        keys_to_remove = []
        for key, timestamps in list(self._messages.items()):
            # Remove old timestamps
            while timestamps and timestamps[0] < cutoff_time:
                timestamps.popleft()
                
            # If no recent timestamps, mark for removal
            if not timestamps:
                keys_to_remove.append(key)
                
        # Remove empty entries
        for key in keys_to_remove:
            del self._messages[key]
            if key in self._stats:
                # Keep stats for a bit longer for summary
                if self._stats[key].last_seen < cutoff_time - self.per:
                    del self._stats[key]
            if key in self._access_order:
                self._access_order.remove(key)
                
        self._last_cleanup = current_time
    
    def _evict_lru(self) -> None:
        """Evict least recently used entries when cache is full."""
        # Remove 10% of oldest entries
        evict_count = max(1, self.max_cache_size // 10)
        
        for _ in range(evict_count):
            if self._access_order:
                key = self._access_order.popleft()
                if key in self._messages:
                    del self._messages[key]
                if key in self._stats:
                    del self._stats[key]
    
    def get_summary(self) -> dict[str, Any]:
        """
        Get summary of rate limiting activity.
        
        Returns:
            Dictionary with rate limiting statistics
        """
        with self._lock:
            total_messages = sum(s.count for s in self._stats.values())
            total_suppressed = sum(s.suppressed_count for s in self._stats.values())
            
            return {
                "total_messages": total_messages,
                "total_suppressed": total_suppressed,
                "unique_messages": len(self._messages),
                "suppression_rate": total_suppressed / max(1, total_messages + total_suppressed),
                "cache_size": len(self._messages),
                "max_cache_size": self.max_cache_size,
            }


class BurstRateLimitFilter(RateLimitFilter):
    """
    Rate limit filter with token bucket algorithm for burst handling.
    
    Allows bursts of messages while maintaining long-term rate limit.
    Tokens are added at a constant rate and consumed by messages.
    """
    
    def __init__(self,
                 rate: int = 10,
                 per: float = 60.0,
                 burst: int = 20,
                 **kwargs):
        """
        Initialize BurstRateLimitFilter.
        
        Args:
            rate: Token generation rate (tokens per period)
            per: Time period in seconds
            burst: Maximum burst size (bucket capacity)
            **kwargs: Additional arguments for RateLimitFilter
        """
        super().__init__(rate=rate, per=per, burst=burst, **kwargs)
        
        # Token bucket state
        self._tokens: dict[str, float] = {}
        self._last_update: dict[str, float] = {}
        
    def _should_allow(self, message_key: str) -> bool:
        """
        Check if message should be allowed using token bucket algorithm.
        
        Args:
            message_key: Unique key for the message
            
        Returns:
            True if message should be allowed
        """
        current_time = time.time()
        
        # Initialize bucket for new messages
        if message_key not in self._tokens:
            self._tokens[message_key] = float(self.burst)
            self._last_update[message_key] = current_time
            
        # Calculate tokens to add based on time elapsed
        time_elapsed = current_time - self._last_update[message_key]
        tokens_to_add = (self.rate / self.per) * time_elapsed
        
        # Update token count (cap at burst size)
        self._tokens[message_key] = min(
            self.burst,
            self._tokens[message_key] + tokens_to_add
        )
        self._last_update[message_key] = current_time
        
        # Check if we have tokens available
        if self._tokens[message_key] >= 1.0:
            # Consume a token
            self._tokens[message_key] -= 1.0
            
            # Update access order for LRU
            if message_key in self._access_order:
                self._access_order.remove(message_key)
            self._access_order.append(message_key)
            
            # Enforce cache size limit
            if len(self._tokens) > self.max_cache_size:
                self._evict_lru()
                
            return True
        else:
            # No tokens available
            return False
    
    def _cleanup(self) -> None:
        """Clean up old entries to prevent memory leaks."""
        super()._cleanup()
        
        # Also clean up token bucket state
        current_time = time.time()
        cutoff_time = current_time - self.cleanup_interval
        
        keys_to_remove = []
        for key, last_update in list(self._last_update.items()):
            if last_update < cutoff_time:
                keys_to_remove.append(key)
                
        for key in keys_to_remove:
            self._tokens.pop(key, None)
            self._last_update.pop(key, None)
    
    def _evict_lru(self) -> None:
        """Evict least recently used entries when cache is full."""
        super()._evict_lru()
        
        # Also remove from token bucket state
        if self._access_order:
            # The parent already removed from _access_order
            # We need to clean up our own state for any missing keys
            for key in list(self._tokens.keys()):
                if key not in self._messages:
                    self._tokens.pop(key, None)
                    self._last_update.pop(key, None)


class SamplingRateLimitFilter(RateLimitFilter):
    """
    Rate limit filter that samples messages when limit is exceeded.
    
    Instead of blocking all messages after limit, it samples them
    at a specified rate (e.g., log 1 in every 10 messages).
    """
    
    def __init__(self,
                 rate: int = 10,
                 per: float = 60.0,
                 sample_rate: float = 0.1,
                 **kwargs):
        """
        Initialize SamplingRateLimitFilter.
        
        Args:
            rate: Number of allowed messages before sampling
            per: Time period in seconds
            sample_rate: Fraction of messages to allow when rate exceeded (0.0-1.0)
            **kwargs: Additional arguments for RateLimitFilter
        """
        super().__init__(rate=rate, per=per, **kwargs)
        self.sample_rate = max(0.0, min(1.0, sample_rate))
        
        # Sampling state
        self._sample_counters: dict[str, int] = {}
        
    def filter(self, record: logging.LogRecord) -> bool:
        """
        Check if message should be logged based on rate limit with sampling.
        
        Args:
            record: Log record to check
            
        Returns:
            True if message should be logged, False if filtered
        """
        message_key = self._get_message_key(record)
        
        with self._lock:
            # First check normal rate limit
            if self._should_allow(message_key):
                # Reset sample counter when under limit
                self._sample_counters[message_key] = 0
                self._record_message(message_key, True)
                return True
            else:
                # Rate exceeded - apply sampling
                if message_key not in self._sample_counters:
                    self._sample_counters[message_key] = 0
                    
                self._sample_counters[message_key] += 1
                
                # Sample based on rate
                sample_threshold = int(1.0 / self.sample_rate) if self.sample_rate > 0 else 0
                
                if sample_threshold > 0 and self._sample_counters[message_key] % sample_threshold == 0:
                    # This message is sampled - allow it
                    self._record_message(message_key, True)
                    
                    # Add sampling info to message
                    record.msg = f"{record.msg} [SAMPLED: 1/{sample_threshold}]"
                    return True
                else:
                    # Not sampled - suppress
                    self._record_message(message_key, False)
                    return False
                    
            # Periodic cleanup
            if time.time() - self._last_cleanup > self.cleanup_interval:
                self._cleanup()
    
    def _cleanup(self) -> None:
        """Clean up old entries to prevent memory leaks."""
        super()._cleanup()
        
        # Clean up sampling counters for removed messages
        for key in list(self._sample_counters.keys()):
            if key not in self._messages:
                del self._sample_counters[key]


class LoggerSpecificRateLimitFilter(RateLimitFilter):
    """
    Rate limit filter with per-logger configuration.
    
    Allows different rate limits for different loggers.
    Useful for applying stricter limits to noisy loggers.
    """
    
    def __init__(self,
                 default_rate: int = 10,
                 default_per: float = 60.0,
                 logger_configs: dict[str, dict[str, Any]] | None = None,
                 **kwargs):
        """
        Initialize LoggerSpecificRateLimitFilter.
        
        Args:
            default_rate: Default rate for loggers without specific config
            default_per: Default time period in seconds
            logger_configs: Per-logger configurations
                Format: {
                    'logger.name': {'rate': 5, 'per': 30.0, 'burst': 10},
                    'noisy.logger': {'rate': 1, 'per': 300.0},
                }
            **kwargs: Additional arguments for RateLimitFilter
        """
        super().__init__(rate=default_rate, per=default_per, **kwargs)
        
        self.default_rate = default_rate
        self.default_per = default_per
        self.logger_configs = logger_configs or {}
        
        # Per-logger message tracking
        self._logger_messages: dict[str, dict[str, deque[float]]] = {}
        self._logger_stats: dict[str, dict[str, MessageStats]] = {}
        
    def _get_logger_config(self, logger_name: str) -> dict[str, Any]:
        """
        Get configuration for a specific logger.
        
        Args:
            logger_name: Name of the logger
            
        Returns:
            Configuration dict for the logger
        """
        # Check for exact match
        if logger_name in self.logger_configs:
            return self.logger_configs[logger_name]
            
        # Check for parent logger match
        parts = logger_name.split(".")
        for i in range(len(parts), 0, -1):
            parent_name = ".".join(parts[:i])
            if parent_name in self.logger_configs:
                return self.logger_configs[parent_name]
                
        # Return default config
        return {
            "rate": self.default_rate,
            "per": self.default_per,
            "burst": self.burst
        }
    
    def _should_allow(self, message_key: str) -> bool:
        """
        Check if message should be allowed based on logger-specific rate limit.
        
        Args:
            message_key: Unique key for the message
            
        Returns:
            True if message should be allowed
        """
        # This is called within the lock from parent filter()
        # We need the logger name from the current record being processed
        # Since we don't have it here, we'll override filter() instead
        raise NotImplementedError("Use filter() method instead")
    
    def filter(self, record: logging.LogRecord) -> bool:
        """
        Check if message should be logged based on logger-specific rate limit.
        
        Args:
            record: Log record to check
            
        Returns:
            True if message should be logged, False if rate limited
        """
        message_key = self._get_message_key(record)
        logger_name = record.name
        
        with self._lock:
            # Get logger-specific config
            config = self._get_logger_config(logger_name)
            rate = config.get("rate", self.default_rate)
            per = config.get("per", self.default_per)
            burst = config.get("burst", self.burst)
            
            # Initialize logger-specific tracking if needed
            if logger_name not in self._logger_messages:
                self._logger_messages[logger_name] = {}
                self._logger_stats[logger_name] = {}
                
            # Check rate limit for this logger
            allowed = self._check_logger_rate_limit(
                logger_name, message_key, rate, per, burst
            )
            
            # Record statistics
            self._record_logger_message(logger_name, message_key, allowed)
            
            # Add suppression info if blocked
            if not allowed and message_key in self._logger_stats[logger_name]:
                stats = self._logger_stats[logger_name][message_key]
                if stats.suppressed_count == 1:
                    record.msg = f"{record.msg} [Rate limit reached for {logger_name}]"
                    return True
                    
            # Periodic cleanup
            if time.time() - self._last_cleanup > self.cleanup_interval:
                self._cleanup()
                
            return allowed
    
    def _check_logger_rate_limit(self, logger_name: str, message_key: str,
                                 rate: int, per: float, burst: int) -> bool:
        """
        Check rate limit for a specific logger and message.
        
        Args:
            logger_name: Logger name
            message_key: Message key
            rate: Rate limit
            per: Time period
            burst: Burst capacity
            
        Returns:
            True if allowed, False if rate limited
        """
        current_time = time.time()
        
        # Get or create message queue
        if message_key not in self._logger_messages[logger_name]:
            self._logger_messages[logger_name][message_key] = deque()
            
        timestamps = self._logger_messages[logger_name][message_key]
        
        # Remove old timestamps
        cutoff_time = current_time - per
        while timestamps and timestamps[0] < cutoff_time:
            timestamps.popleft()
            
        # Check rate limit
        if len(timestamps) < rate + burst - 1:
            timestamps.append(current_time)
            return True
        else:
            return False
    
    def _record_logger_message(self, logger_name: str, message_key: str,
                              allowed: bool) -> None:
        """Record message statistics for a logger."""
        if message_key not in self._logger_stats[logger_name]:
            self._logger_stats[logger_name][message_key] = MessageStats()
            
        self._logger_stats[logger_name][message_key].update(suppressed=not allowed)
    
    def _cleanup(self) -> None:
        """Clean up old entries to prevent memory leaks."""
        current_time = time.time()
        
        # Clean up each logger's data
        for logger_name in list(self._logger_messages.keys()):
            # Get logger config for cleanup interval
            config = self._get_logger_config(logger_name)
            per = config.get("per", self.default_per)
            cutoff_time = current_time - per
            
            # Clean messages
            messages = self._logger_messages[logger_name]
            stats = self._logger_stats[logger_name]
            
            keys_to_remove = []
            for key, timestamps in list(messages.items()):
                while timestamps and timestamps[0] < cutoff_time:
                    timestamps.popleft()
                    
                if not timestamps:
                    keys_to_remove.append(key)
                    
            for key in keys_to_remove:
                del messages[key]
                if key in stats and stats[key].last_seen < cutoff_time - per:
                    del stats[key]
                    
            # Remove empty logger entries
            if not messages:
                del self._logger_messages[logger_name]
            if not stats:
                del self._logger_stats[logger_name]
                
        self._last_cleanup = current_time


def create_rate_limit_filter(filter_type: str = "standard",
                           preset: str | None = None,
                           **kwargs) -> RateLimitFilter:
    """
    Factory function to create appropriate rate limit filter.
    
    Args:
        filter_type: Type of filter to create
            - 'standard': Basic rate limiting (default)
            - 'burst': Token bucket algorithm for burst handling
            - 'sampling': Sample messages when rate exceeded
            - 'logger_specific': Per-logger rate limits
        preset: Predefined configuration
            - 'strict': Low rate limits for production
            - 'lenient': Higher limits for development
            - 'performance': Optimized for high-throughput
        **kwargs: Additional arguments for filter
        
    Returns:
        Configured RateLimitFilter instance
        
    Examples:
        Basic rate limiting:
        >>> filter_obj = create_rate_limit_filter()
        
        Burst handling for APIs:
        >>> filter_obj = create_rate_limit_filter(
        ...     filter_type='burst',
        ...     rate=100,
        ...     burst=200
        ... )
        
        Sampling for noisy loggers:
        >>> filter_obj = create_rate_limit_filter(
        ...     filter_type='sampling',
        ...     sample_rate=0.01  # Log 1% when rate exceeded
        ... )
        
        Per-logger configuration:
        >>> filter_obj = create_rate_limit_filter(
        ...     filter_type='logger_specific',
        ...     logger_configs={
        ...         'requests': {'rate': 5, 'per': 60},
        ...         'urllib3': {'rate': 1, 'per': 300},
        ...     }
        ... )
    """
    # Preset configurations
    presets = {
        "strict": {
            "rate": 1,
            "per": 60.0,
            "burst": 3,
            "max_cache_size": 1000,
        },
        "lenient": {
            "rate": 10,
            "per": 60.0,
            "burst": 20,
            "max_cache_size": 10000,
        },
        "performance": {
            "rate": 100,
            "per": 60.0,
            "burst": 200,
            "max_cache_size": 50000,
            "cleanup_interval": 600,
        },
    }
    
    # Apply preset if specified
    if preset and preset in presets:
        preset_config = presets[preset].copy()
        # Merge with provided kwargs (kwargs take precedence)
        preset_config.update(kwargs)
        kwargs = preset_config
    
    # Create appropriate filter type
    if filter_type == "burst":
        return BurstRateLimitFilter(**kwargs)
    elif filter_type == "sampling":
        # Set default sample rate if not provided
        if "sample_rate" not in kwargs:
            kwargs["sample_rate"] = 0.1  # 10% sampling by default
        return SamplingRateLimitFilter(**kwargs)
    elif filter_type == "logger_specific":
        return LoggerSpecificRateLimitFilter(**kwargs)
    else:
        # Standard rate limiting
        return RateLimitFilter(**kwargs)


# Convenience functions for common use cases
def create_strict_rate_limiter(**kwargs) -> RateLimitFilter:
    """Create a strict rate limiter for production use."""
    return create_rate_limit_filter(preset="strict", **kwargs)


def create_lenient_rate_limiter(**kwargs) -> RateLimitFilter:
    """Create a lenient rate limiter for development."""
    return create_rate_limit_filter(preset="lenient", **kwargs)


def create_burst_limiter(rate: int = 100, burst: int = 200, **kwargs) -> BurstRateLimitFilter:
    """Create a burst rate limiter for handling traffic spikes."""
    filter_obj = create_rate_limit_filter(
        filter_type="burst",
        rate=rate,
        burst=burst,
        **kwargs
    )
    assert isinstance(filter_obj, BurstRateLimitFilter)
    return filter_obj


def create_sampling_limiter(rate: int = 10, sample_rate: float = 0.1, **kwargs) -> SamplingRateLimitFilter:
    """Create a sampling rate limiter for noisy logs."""
    filter_obj = create_rate_limit_filter(
        filter_type="sampling",
        rate=rate,
        sample_rate=sample_rate,
        **kwargs
    )
    assert isinstance(filter_obj, SamplingRateLimitFilter)
    return filter_obj