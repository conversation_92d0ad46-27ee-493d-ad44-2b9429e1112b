"""
Structlog processors for log enrichment and filtering.

This module will contain:
- add_app_context: Processor that adds application context to logs
- filter_sensitive_keys: Processor that filters sensitive data from logs
"""

import logging
from datetime import UTC, datetime
from typing import Any

from django.conf import settings

# Import context function
import core.logging


def add_app_context(logger: logging.Logger, method_name: str, event_dict: dict[str, Any]) -> dict[str, Any]:
    """
    Structlog processor that adds application context to log events.
    
    Adds:
    - app_name
    - environment
    - request context (if available)
    - task context (if in Celery task)
    - timestamp (if not present)
    """
    # Add app_name if not already present
    if "app_name" not in event_dict:
        # Get app name from settings or default to "socialmanager"
        app_name = getattr(settings, "APP_NAME", "socialmanager")
        event_dict["app_name"] = app_name
    
    # Add environment if not already present
    if "environment" not in event_dict:
        # Determine environment based on DEBUG setting or ENVIRONMENT variable
        if hasattr(settings, "ENVIRONMENT"):
            environment = settings.ENVIRONMENT
        elif hasattr(settings, "DEBUG"):
            environment = "local" if settings.DEBUG else "production"
        else:
            environment = "unknown"
        event_dict["environment"] = environment
    
    # Add request/task context if available
    try:
        context = core.logging.get_context()
        # Only add context fields that are not None and not already in event_dict
        for key, value in context.items():
            if value is not None and key not in event_dict:
                event_dict[key] = value
                
        # If we have a request_id, check for additional request info
        if context.get("request_id") and "path" not in event_dict:
            # Try to get path from context (might be added by middleware)
            if "path" in context:
                event_dict["path"] = context["path"]
                
    except Exception:
        # Silently ignore any errors getting context
        pass
    
    # Check if we're in a Celery task
    try:
        from celery import current_task
        if current_task and current_task.request:
            # Add task context if not already present
            if "task_id" not in event_dict and hasattr(current_task.request, "id"):
                event_dict["task_id"] = current_task.request.id
            if "task_name" not in event_dict and hasattr(current_task, "name"):
                event_dict["task_name"] = current_task.name
    except (ImportError, AttributeError):
        # Celery not installed or not in task context
        pass
    
    # Add timestamp if not present
    if "timestamp" not in event_dict:
        event_dict["timestamp"] = datetime.now(UTC).isoformat()
    
    return event_dict


def filter_sensitive_keys(logger: logging.Logger, method_name: str, event_dict: dict[str, Any]) -> dict[str, Any]:
    """
    Structlog processor that filters sensitive data from log events.
    
    Filters:
    - password fields
    - token fields
    - API key fields
    - Other sensitive patterns
    """
    # Sensitive keywords to filter (case-insensitive)
    SENSITIVE_PATTERNS = [
        "password", "passwd", "pwd",
        "token", "auth_token", "access_token", "refresh_token",
        "api_key", "apikey", "key",
        "secret", "private", "credential"
    ]
    
    def is_sensitive_key(key: str) -> bool:
        """Check if a key contains sensitive patterns."""
        key_lower = key.lower()
        return any(pattern in key_lower for pattern in SENSITIVE_PATTERNS)
    
    def filter_value(value: Any) -> Any:
        """Recursively filter sensitive values."""
        if isinstance(value, dict):
            return {
                k: "[FILTERED]" if is_sensitive_key(k) else filter_value(v)
                for k, v in value.items()
            }
        elif isinstance(value, list):
            return [filter_value(item) for item in value]
        else:
            return value
    
    # Create a copy and filter sensitive data
    filtered_dict = {}
    for key, value in event_dict.items():
        if is_sensitive_key(key):
            filtered_dict[key] = "[FILTERED]"
        else:
            filtered_dict[key] = filter_value(value)
    
    return filtered_dict