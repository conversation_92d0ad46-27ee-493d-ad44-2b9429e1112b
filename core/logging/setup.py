"""
Настройка структурированного логирования
"""

import logging
import logging.config
from typing import Any, cast

import structlog
from django.conf import settings
from structlog.typing import FilteringBoundLogger


def setup_logging(log_level: str = None):
    """
    Настройка structlog для структурированного логирования.
    
    Args:
        log_level: Уровень логирования (по умолчанию из settings)
    """
    
    if log_level is None:
        log_level = getattr(settings, "LOG_LEVEL", "INFO")
    
    # Настройка стандартного логирования Python
    logging_config = {
        "version": 1,
        "disable_existing_loggers": False,
        "formatters": {
            "json": {
                "format": "%(message)s",
            },
            "console": {
                "format": "%(asctime)s [%(levelname)s] %(name)s: %(message)s",
            },
        },
        "handlers": {
            "console": {
                "class": "logging.StreamHandler",
                "formatter": "console" if settings.DEBUG else "json",
                "level": log_level,
            },
            "file": {
                "class": "logging.handlers.RotatingFileHandler",
                "filename": getattr(settings, "LOG_FILE", "socialmanager.log"),
                "maxBytes": 10 * 1024 * 1024,  # 10 MB
                "backupCount": 5,
                "formatter": "json",
                "level": log_level,
            },
        },
        "root": {
            "handlers": ["console", "file"],
            "level": log_level,
        },
        "loggers": {
            "django": {
                "handlers": ["console", "file"],
                "level": "INFO",
                "propagate": False,
            },
            "instagram_manager": {
                "handlers": ["console", "file"],
                "level": log_level,
                "propagate": False,
            },
            "telegram_manager": {
                "handlers": ["console", "file"],
                "level": log_level,
                "propagate": False,
            },
            "core": {
                "handlers": ["console", "file"],
                "level": log_level,
                "propagate": False,
            },
        },
    }
    
    logging.config.dictConfig(logging_config)
    
    # Настройка процессоров structlog
    timestamper = structlog.processors.TimeStamper(fmt="iso")
    
    shared_processors: list[Any] = [
        structlog.stdlib.add_logger_name,
        structlog.stdlib.add_log_level,
        timestamper,
        structlog.processors.StackInfoRenderer(),
        structlog.processors.format_exc_info,
        structlog.processors.UnicodeDecoder(),
        structlog.contextvars.merge_contextvars,  # Автоматически добавляет контекст
        structlog.processors.CallsiteParameterAdder(
            parameters=[
                structlog.processors.CallsiteParameter.PATHNAME,
                structlog.processors.CallsiteParameter.LINENO,
                structlog.processors.CallsiteParameter.FUNC_NAME,
            ]
        ),
        add_app_context,  # Наш кастомный процессор
    ]
    
    renderer: Any  # Type for renderer that can be either ConsoleRenderer or JSONRenderer
    if settings.DEBUG:
        # Красивый вывод для разработки
        renderer = structlog.dev.ConsoleRenderer(
            colors=True,
            exception_formatter=structlog.dev.plain_traceback,
        )
    else:
        # JSON для продакшена
        renderer = structlog.processors.JSONRenderer()
    
    structlog.configure(
        processors=shared_processors + [
            structlog.stdlib.ProcessorFormatter.wrap_for_formatter,
        ],
        context_class=dict,
        logger_factory=structlog.stdlib.LoggerFactory(),
        cache_logger_on_first_use=True,
    )
    
    # Настройка форматтера для интеграции со стандартным logging
    formatter = structlog.stdlib.ProcessorFormatter(
        foreign_pre_chain=shared_processors,
        processors=[
            structlog.stdlib.ProcessorFormatter.remove_processors_meta,
            renderer,
        ],
    )
    
    # Применяем форматтер ко всем хендлерам
    for handler in logging.root.handlers:
        handler.setFormatter(formatter)


def add_app_context(logger, method_name, event_dict: dict[str, Any]) -> dict[str, Any]:
    """
    Кастомный процессор для добавления контекста приложения.
    
    Args:
        logger: Логгер
        method_name: Имя метода логирования
        event_dict: Словарь события
        
    Returns:
        Обновленный словарь события
    """
    # Добавляем информацию о приложении
    event_dict.setdefault("app", "socialmanager")
    event_dict.setdefault("environment", getattr(settings, "ENVIRONMENT", "development"))
    
    # Добавляем версию приложения если есть
    if hasattr(settings, "VERSION"):
        event_dict["version"] = settings.VERSION
    
    return event_dict


# Функция для получения настроенного логгера
def get_logger(name: str) -> FilteringBoundLogger:
    """
    Получить настроенный structlog логгер.
    
    Args:
        name: Имя логгера
        
    Returns:
        Настроенный логгер
    """
    return cast(FilteringBoundLogger, structlog.get_logger(name))