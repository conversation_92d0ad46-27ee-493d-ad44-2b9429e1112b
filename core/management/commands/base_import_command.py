"""
Базовый класс для команд импорта данных из социальных медиа.
"""

import time
from abc import abstractmethod
from typing import Any

from django.db import transaction
from tqdm import tqdm

from core.exceptions import RateLimitError

from .base import BaseSocialMediaCommand


class BaseImportCommand(BaseSocialMediaCommand):
    """
    Базовый класс для всех команд импорта данных.
    
    Предоставляет общую функциональность:
    - Управление лимитами и пакетной обработкой
    - Прогресс-бары
    - Обработка rate limits
    - Статистика импорта
    - Транзакционная безопасность
    """
    
    def add_custom_arguments(self, parser):
        """Добавляет аргументы специфичные для импорта"""
        super().add_custom_arguments(parser)
        
        parser.add_argument(
            "--limit",
            type=int,
            default=None,
            help="Максимальное количество элементов для импорта"
        )
        
        parser.add_argument(
            "--batch-size",
            type=int,
            default=100,
            help="Размер пакета для обработки (по умолчанию: 100)"
        )
        
        parser.add_argument(
            "--skip-existing",
            action="store_true",
            help="Пропустить существующие записи"
        )
        
        parser.add_argument(
            "--update-existing",
            action="store_true",
            help="Обновить существующие записи"
        )
        
        parser.add_argument(
            "--retry-on-error",
            action="store_true",
            help="Повторить попытку при ошибке"
        )
        
        parser.add_argument(
            "--retry-delay",
            type=int,
            default=5,
            help="Задержка между повторными попытками в секундах (по умолчанию: 5)"
        )
        
        parser.add_argument(
            "--max-retries",
            type=int,
            default=3,
            help="Максимальное количество повторных попыток (по умолчанию: 3)"
        )
        
        parser.add_argument(
            "--progress",
            action="store_true",
            default=True,
            help="Показывать прогресс-бар"
        )
        
        parser.add_argument(
            "--no-progress",
            action="store_false",
            dest="progress",
            help="Не показывать прогресс-бар"
        )
    
    def execute_command(
        self,
        credentials: dict[str, Any],
        date_range: tuple | None,
        **options
    ) -> dict[str, Any]:
        """
        Выполняет команду импорта.
        
        Returns:
            Словарь со статистикой импорта
        """
        # Инициализация статистики
        stats = {
            "total": 0,
            "created": 0,
            "updated": 0,
            "skipped": 0,
            "errors": 0,
            "start_time": time.time()
        }
        
        # Получаем опции
        limit = options.get("limit")
        batch_size = options.get("batch_size", 100)
        skip_existing = options.get("skip_existing", False)
        update_existing = options.get("update_existing", False)
        show_progress = options.get("progress", True)
        
        try:
            # Получаем данные для импорта
            self.log_progress("Fetching data for import...", "info")
            items = self.fetch_data(credentials, date_range, limit, **options)
            
            if not items:
                self.log_progress("No data to import", "warning")
                return stats
            
            stats["total"] = len(items)
            self.log_progress(f"Found {stats['total']} items to import", "info")
            
            # Обрабатываем элементы пакетами
            progress_bar = None
            if show_progress and self.verbosity >= 1:
                progress_bar = tqdm(
                    total=stats["total"],
                    desc="Importing",
                    unit="items"
                )
            
            # Обработка пакетами
            for i in range(0, int(stats["total"]), batch_size):
                batch = items[i:i + batch_size]
                
                if self.dry_run:
                    # В dry run режиме только считаем
                    stats["skipped"] += len(batch)
                else:
                    # Обрабатываем пакет в транзакции
                    batch_stats = self._process_batch(
                        batch,
                        skip_existing=skip_existing,
                        update_existing=update_existing,
                        **options
                    )
                    
                    # Обновляем статистику
                    stats["created"] += batch_stats["created"]
                    stats["updated"] += batch_stats["updated"]
                    stats["skipped"] += batch_stats["skipped"]
                    stats["errors"] += batch_stats["errors"]
                
                # Обновляем прогресс
                if progress_bar:
                    progress_bar.update(len(batch))
            
            if progress_bar:
                progress_bar.close()
            
            # Финальная статистика
            stats["duration"] = time.time() - stats["start_time"]
            
        except RateLimitError as e:
            self.handle_rate_limit(e)
            stats["errors"] += 1
        except Exception as e:
            self.handle_exception("import", e)
            stats["errors"] += 1
        
        return stats
    
    def _process_batch(
        self,
        batch: list,
        skip_existing: bool = False,
        update_existing: bool = False,
        **options
    ) -> dict[str, int]:
        """
        Обрабатывает пакет элементов.
        
        Returns:
            Статистика обработки пакета
        """
        batch_stats = {
            "created": 0,
            "updated": 0,
            "skipped": 0,
            "errors": 0
        }
        
        with transaction.atomic():
            self.log_progress(
                f"[TRANSACTION START] Starting atomic transaction for batch processing "
                f"{len(batch)} items",
                "debug"
            )
            
            for item in batch:
                try:
                    result = self.process_item(
                        item,
                        skip_existing=skip_existing,
                        update_existing=update_existing,
                        **options
                    )
                    
                    if result == "created":
                        batch_stats["created"] += 1
                    elif result == "updated":
                        batch_stats["updated"] += 1
                    elif result == "skipped":
                        batch_stats["skipped"] += 1
                    
                except Exception as e:
                    batch_stats["errors"] += 1
                    if self.verbosity >= 2:
                        self.log_progress(
                            f"Error processing item: {e}",
                            "error"
                        )
                    
                    # Решаем, продолжать ли после ошибки
                    if not options.get("continue_on_error", True):
                        self.log_progress(
                            f"[TRANSACTION ERROR] Error in transaction, aborting batch: {e}",
                            "error"
                        )
                        raise
            
            self.log_progress(
                f"[TRANSACTION END] Successfully processed batch in transaction. "
                f"Created: {batch_stats['created']}, Updated: {batch_stats['updated']}, "
                f"Skipped: {batch_stats['skipped']}, Errors: {batch_stats['errors']}",
                "debug"
            )
        
        return batch_stats
    
    @abstractmethod
    def fetch_data(
        self,
        credentials: dict[str, Any],
        date_range: tuple | None,
        limit: int | None,
        **options
    ) -> list:
        """
        Получает данные для импорта.
        
        Должен быть реализован в подклассах.
        
        Args:
            credentials: Учетные данные API
            date_range: Диапазон дат для фильтрации
            limit: Максимальное количество элементов
            **options: Дополнительные опции
            
        Returns:
            Список элементов для импорта
        """
        raise NotImplementedError
    
    @abstractmethod
    def process_item(
        self,
        item: Any,
        skip_existing: bool = False,
        update_existing: bool = False,
        **options
    ) -> str:
        """
        Обрабатывает один элемент.
        
        Должен быть реализован в подклассах.
        
        Args:
            item: Элемент для обработки
            skip_existing: Пропускать существующие
            update_existing: Обновлять существующие
            **options: Дополнительные опции
            
        Returns:
            Результат обработки: "created", "updated", "skipped"
        """
        raise NotImplementedError
    
    def handle_result(self, result: Any):
        """Обрабатывает результат импорта"""
        if isinstance(result, dict):
            # Показываем статистику импорта
            self.log_progress("\nImport Statistics:", "success")
            self.log_progress(f"  Total items: {result.get('total', 0)}", "info")
            self.log_progress(f"  Created: {result.get('created', 0)}", "info")
            self.log_progress(f"  Updated: {result.get('updated', 0)}", "info")
            self.log_progress(f"  Skipped: {result.get('skipped', 0)}", "info")
            self.log_progress(f"  Errors: {result.get('errors', 0)}", "info")
            
            duration = result.get("duration", 0)
            if duration > 0:
                self.log_progress(
                    f"  Duration: {duration:.2f} seconds",
                    "info"
                )
                
                # Вычисляем скорость
                total = result.get("total", 0)
                if total > 0:
                    rate = total / duration
                    self.log_progress(
                        f"  Rate: {rate:.2f} items/second",
                        "info"
                    )
        else:
            super().handle_result(result)
    
    def handle_rate_limit(self, error: RateLimitError):
        """
        Обрабатывает превышение rate limit.
        
        Args:
            error: Исключение rate limit
        """
        retry_after = getattr(error, "retry_after", 60)
        self.log_progress(
            f"Rate limit exceeded. Waiting {retry_after} seconds...",
            "warning"
        )
        
        if self.verbosity >= 1:
            # Показываем обратный отсчет
            for remaining in range(retry_after, 0, -1):
                print(f"\rWaiting: {remaining} seconds remaining...", end="", flush=True)
                time.sleep(1)
            print("\r" + " " * 50 + "\r", end="", flush=True)  # Очищаем строку
        else:
            time.sleep(retry_after)
    
    def retry_with_backoff(
        self,
        func,
        *args,
        max_retries: int = 3,
        initial_delay: int = 1,
        **kwargs
    ):
        """
        Выполняет функцию с экспоненциальной задержкой при ошибках.
        
        Args:
            func: Функция для выполнения
            max_retries: Максимальное количество попыток
            initial_delay: Начальная задержка в секундах
            
        Returns:
            Результат функции
            
        Raises:
            Последнее исключение если все попытки неудачны
        """
        delay = initial_delay
        last_exception = None
        
        for attempt in range(max_retries):
            try:
                return func(*args, **kwargs)
            except RateLimitError:
                # Rate limit обрабатываем отдельно
                raise
            except Exception as e:
                last_exception = e
                
                if attempt < max_retries - 1:
                    self.log_progress(
                        f"Attempt {attempt + 1} failed: {e}. "
                        f"Retrying in {delay} seconds...",
                        "warning"
                    )
                    time.sleep(delay)
                    delay *= 2  # Экспоненциальная задержка
                else:
                    self.log_progress(
                        f"All {max_retries} attempts failed",
                        "error"
                    )
        
        if last_exception:
            raise last_exception