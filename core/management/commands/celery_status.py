"""
Command to monitor Celery task status and queue.
"""

from celery import current_app
from celery.result import As<PERSON><PERSON><PERSON><PERSON>
from django.core.management.base import BaseCommand, CommandParser

from core.logging import ContextLogger
from core.models import TaskResult

logger = ContextLogger(__name__)


class Command(BaseCommand):
    """Monitor Celery task status and queue information."""
    
    help = "Monitor Celery task status, queue information and worker status"
    
    def add_arguments(self, parser: CommandParser) -> None:
        parser.add_argument(
            "task_id",
            nargs="?",
            help="Specific task ID to monitor"
        )
        parser.add_argument(
            "--follow",
            "-f",
            action="store_true",
            help="Follow task progress until completion"
        )
        parser.add_argument(
            "--queue-stats",
            action="store_true",
            help="Show queue statistics"
        )
        parser.add_argument(
            "--active-tasks",
            action="store_true",
            help="Show all active tasks"
        )
        parser.add_argument(
            "--workers",
            action="store_true",
            help="Show worker status"
        )
        parser.add_argument(
            "--cancel",
            action="store_true",
            help="Cancel the specified task"
        )
    
    def handle(self, *args, **options) -> None:
        if options["queue_stats"]:
            self._show_queue_stats()
        elif options["active_tasks"]:
            self._show_active_tasks()
        elif options["workers"]:
            self._show_workers()
        elif options["task_id"]:
            if options["cancel"]:
                self._cancel_task(options["task_id"])
            else:
                self._monitor_task(options["task_id"], follow=options["follow"])
        else:
            # Show general status
            self._show_general_status()
    
    def _monitor_task(self, task_id: str, follow: bool = False) -> None:
        """Monitor specific task status."""
        # First check if it's in our TaskResult
        try:
            task_result = TaskResult.objects.get(task_id=task_id)
            self._display_task_result(task_result)
            
            if follow and task_result.status in ["pending", "running"]:
                self._follow_task(task_result)
        except TaskResult.DoesNotExist:
            # Check if it's a Celery task ID
            try:
                task_result = TaskResult.objects.get(celery_task_id=task_id)
                self._display_task_result(task_result)
                
                if follow and task_result.status in ["pending", "running"]:
                    self._follow_task(task_result)
            except TaskResult.DoesNotExist:
                # Try to get directly from Celery
                result: AsyncResult = AsyncResult(task_id)
                self._display_celery_result(result)
                
                if follow and not result.ready():
                    self._follow_celery_task(result)
    
    def _display_task_result(self, task_result: TaskResult) -> None:
        """Display TaskResult information."""
        # Sync with Celery first
        task_result.sync_with_celery()
        
        self.stdout.write(f"\n{'=' * 60}")
        self.stdout.write(f"Task ID: {task_result.task_id}")
        self.stdout.write(f"Celery ID: {task_result.celery_task_id or 'N/A'}")
        self.stdout.write(f"Type: {task_result.task_type}")
        self.stdout.write(f"Status: {task_result.get_status_display()}")
        
        if task_result.celery_status:
            self.stdout.write(f"Celery Status: {task_result.celery_status}")
        
        self.stdout.write(f"\nProgress: {task_result.progress_percentage:.1f}%")
        if task_result.progress_message:
            self.stdout.write(f"Message: {task_result.progress_message}")
        
        self.stdout.write(f"\nCreated: {task_result.created_at}")
        if task_result.started_at:
            self.stdout.write(f"Started: {task_result.started_at}")
        if task_result.completed_at:
            self.stdout.write(f"Completed: {task_result.completed_at}")
            if task_result.started_at:
                duration = task_result.completed_at - task_result.started_at
                self.stdout.write(f"Duration: {duration}")
        
        if task_result.parameters:
            self.stdout.write("\nParameters:")
            for key, value in task_result.parameters.items():
                self.stdout.write(f"  {key}: {value}")
        
        if task_result.result and task_result.status == "completed":
            self.stdout.write("\nResult:")
            for key, value in task_result.result.items():
                self.stdout.write(f"  {key}: {value}")
        
        if task_result.error_message:
            self.stdout.write(self.style.ERROR(f"\nError: {task_result.error_message}"))
        
        self.stdout.write(f"{'=' * 60}\n")
    
    def _display_celery_result(self, result: AsyncResult) -> None:
        """Display Celery AsyncResult information."""
        self.stdout.write(f"\n{'=' * 60}")
        self.stdout.write(f"Celery Task ID: {result.id}")
        self.stdout.write(f"Status: {result.status}")
        self.stdout.write(f"Ready: {result.ready()}")
        
        if result.ready():
            self.stdout.write(f"Successful: {result.successful()}")
            if result.successful():
                self.stdout.write(f"Result: {result.result}")
            else:
                self.stdout.write(self.style.ERROR(f"Error: {result.info}"))
        
        self.stdout.write(f"{'=' * 60}\n")
    
    def _follow_task(self, task_result: TaskResult) -> None:
        """Follow task progress until completion."""
        import time
        
        self.stdout.write("\nFollowing task progress...")
        
        last_progress = task_result.processed_items
        while task_result.status in ["pending", "running"]:
            time.sleep(1)
            task_result.refresh_from_db()
            task_result.sync_with_celery()
            
            if task_result.processed_items != last_progress:
                last_progress = task_result.processed_items
                self.stdout.write(
                    f"[{task_result.progress_percentage:3.0f}%] {task_result.progress_message}"
                )
        
        # Show final status
        self.stdout.write("\n")
        self._display_task_result(task_result)
    
    def _follow_celery_task(self, result: AsyncResult) -> None:
        """Follow Celery task until completion."""
        import time
        
        self.stdout.write("\nFollowing Celery task...")
        
        while not result.ready():
            time.sleep(1)
            self.stdout.write(f"Status: {result.status}")
        
        self.stdout.write("\n")
        self._display_celery_result(result)
    
    def _show_queue_stats(self) -> None:
        """Show queue statistics."""
        inspect = current_app.control.inspect()
        
        # Get queue lengths
        self.stdout.write("\nQueue Statistics:")
        self.stdout.write("=" * 40)
        
        # Active tasks
        active = inspect.active()
        if active:
            total_active = sum(len(tasks) for tasks in active.values())
            self.stdout.write(f"Active tasks: {total_active}")
        
        # Reserved tasks
        reserved = inspect.reserved()
        if reserved:
            total_reserved = sum(len(tasks) for tasks in reserved.values())
            self.stdout.write(f"Reserved tasks: {total_reserved}")
        
        # Scheduled tasks
        scheduled = inspect.scheduled()
        if scheduled:
            total_scheduled = sum(len(tasks) for tasks in scheduled.values())
            self.stdout.write(f"Scheduled tasks: {total_scheduled}")
        
        # Database stats
        self.stdout.write("\nDatabase Task Statistics:")
        self.stdout.write(f"Pending: {TaskResult.objects.filter(status='pending').count()}")
        self.stdout.write(f"Running: {TaskResult.objects.filter(status='running').count()}")
        self.stdout.write(f"Completed: {TaskResult.objects.filter(status='completed').count()}")
        self.stdout.write(f"Failed: {TaskResult.objects.filter(status='failed').count()}")
        self.stdout.write(f"Cancelled: {TaskResult.objects.filter(status='cancelled').count()}")
    
    def _show_active_tasks(self) -> None:
        """Show all active tasks."""
        active_tasks = TaskResult.objects.filter(
            status__in=["pending", "running"]
        ).order_by("-created_at")
        
        if not active_tasks:
            self.stdout.write("No active tasks")
            return
        
        self.stdout.write(f"\nActive Tasks ({active_tasks.count()}):")
        self.stdout.write("=" * 80)
        self.stdout.write(
            f"{'Task ID':^36} | {'Type':^20} | {'Status':^10} | {'Progress':^8}"
        )
        self.stdout.write("-" * 80)
        
        for task in active_tasks:
            self.stdout.write(
                f"{task.task_id} | {task.task_type:^20} | "
                f"{task.status:^10} | {task.progress_percentage:>6.1f}%"
            )
    
    def _show_workers(self) -> None:
        """Show worker status."""
        inspect = current_app.control.inspect()
        stats = inspect.stats()
        
        if not stats:
            self.stdout.write("No workers available")
            return
        
        self.stdout.write("\nWorker Status:")
        self.stdout.write("=" * 60)
        
        for worker_name, worker_stats in stats.items():
            self.stdout.write(f"\nWorker: {worker_name}")
            self.stdout.write(f"  Total tasks: {worker_stats.get('total', {})}")
            
            # Get active tasks for this worker
            active = inspect.active()
            if active and worker_name in active:
                self.stdout.write(f"  Active tasks: {len(active[worker_name])}")
                for task in active[worker_name][:5]:  # Show first 5
                    self.stdout.write(f"    - {task['name']} ({task['id']})")
    
    def _show_general_status(self) -> None:
        """Show general Celery and task status."""
        self.stdout.write(self.style.SUCCESS("\nCelery Task Management Status"))
        self.stdout.write("=" * 60)
        
        # Check if Celery is responsive
        try:
            inspect = current_app.control.inspect()
            stats = inspect.stats()
            if stats:
                self.stdout.write(self.style.SUCCESS(f"✓ Celery is running with {len(stats)} worker(s)"))
            else:
                self.stdout.write(self.style.WARNING("⚠ No Celery workers found"))
        except Exception as e:
            self.stdout.write(self.style.ERROR(f"✗ Cannot connect to Celery: {e}"))
        
        # Show queue stats
        self._show_queue_stats()
        
        # Show recent tasks
        recent_tasks = TaskResult.objects.order_by("-created_at")[:10]
        if recent_tasks:
            self.stdout.write("\nRecent Tasks:")
            self.stdout.write("-" * 80)
            for task in recent_tasks:
                status_style = self.style.SUCCESS if task.status == "completed" else \
                              self.style.ERROR if task.status == "failed" else \
                              self.style.WARNING
                self.stdout.write(
                    f"{task.created_at.strftime('%Y-%m-%d %H:%M')} | "
                    f"{task.task_type:<25} | "
                    f"{status_style(task.get_status_display())}"
                )
    
    def _cancel_task(self, task_id: str) -> None:
        """Cancel a task."""
        try:
            # Try to find by task_id first
            task_result = TaskResult.objects.get(task_id=task_id)
        except TaskResult.DoesNotExist:
            # Try by celery_task_id
            try:
                task_result = TaskResult.objects.get(celery_task_id=task_id)
            except TaskResult.DoesNotExist:
                # Try to revoke directly
                result: AsyncResult = AsyncResult(task_id)
                result.revoke(terminate=True)
                self.stdout.write(self.style.SUCCESS(f"Celery task {task_id} revoked"))
                return
        
        # Revoke via TaskResult
        if task_result.revoke_celery_task(terminate=True):
            self.stdout.write(
                self.style.SUCCESS(f"Task {task_result.task_id} cancelled successfully")
            )
        else:
            self.stdout.write(
                self.style.ERROR(f"Failed to cancel task {task_result.task_id}")
            )