"""
Command to test the echo task.
"""

from django.core.management.base import BaseCommand

from core.tasks.test_task import EchoTask


class Command(BaseCommand):
    """Test the echo task."""
    
    help = "Test the echo task system"
    
    def add_arguments(self, parser):
        """Add command arguments."""
        parser.add_argument(
            "message",
            type=str,
            help="Message to echo"
        )
        parser.add_argument(
            "--delay",
            type=float,
            default=0.0,
            help="Delay in seconds before echoing"
        )
        parser.add_argument(
            "--async",
            action="store_true",
            help="Run task asynchronously"
        )
    
    def handle(self, *args, **options):
        """Execute command."""
        message = options["message"]
        delay = options["delay"]
        is_async = options["async"]
        
        if is_async:
            # Enqueue for async execution
            task_id = EchoTask.create_and_enqueue(
                message=message,
                delay=delay
            )
            self.stdout.write(
                self.style.SUCCESS(
                    f"Task queued successfully!\n"
                    f"Task ID: {task_id}\n"
                    f"Use 'python manage.py check_task {task_id}' to check status"
                )
            )
        else:
            # Execute synchronously
            task = EchoTask()
            result = task.execute(message=message, delay=delay)
            
            self.stdout.write(
                self.style.SUCCESS("Echo task completed!")
            )
            self.stdout.write(f"Original: {result['original_message']}")
            self.stdout.write(f"Echo: {result['echo']}")
            self.stdout.write(f"Length: {result['length']} chars")
            self.stdout.write(f"Words: {result['word_count']}")