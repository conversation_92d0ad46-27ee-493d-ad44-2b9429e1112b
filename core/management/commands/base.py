from abc import ABC, abstractmethod
from datetime import datetime
from typing import Any

from django.core.management.base import BaseCommand, CommandError
from django.utils import timezone as django_timezone


class BaseSocialMediaCommand(BaseCommand, ABC):
    """
    Базовая команда для всех команд социальных медиа.
    Предоставляет общую функциональность для обработки дат, 
    обработки ошибок и управления учетными данными.
    """
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.verbosity = 1
        self.dry_run = False
    
    def add_arguments(self, parser):
        """Добавляет общие аргументы для всех команд социальных медиа"""
        parser.add_argument(
            "--dry-run",
            action="store_true",
            help="Выполнить команду без сохранения изменений в базе данных"
        )
        parser.add_argument(
            "--date-from",
            type=str,
            help="Начальная дата в формате YYYY-MM-DD"
        )
        parser.add_argument(
            "--date-to",
            type=str,
            help="Конечная дата в формате YYYY-MM-DD"
        )
        # Позволяем подклассам добавлять свои аргументы
        self.add_custom_arguments(parser)
    
    def add_custom_arguments(self, parser):
        """Переопределите этот метод для добавления специфичных аргументов"""
        pass
    
    def handle(self, *args, **options):
        """Основной метод обработки команды"""
        self.verbosity = options.get("verbosity", 1)
        self.dry_run = options.get("dry_run", False)
        
        if self.dry_run:
            self.stdout.write(self.style.WARNING("DRY RUN MODE: No changes will be saved"))
        
        try:
            # Получаем учетные данные
            credentials = self.get_credentials()
            if not self.validate_credentials(credentials):
                raise CommandError("Invalid credentials")
            
            # Обрабатываем диапазон дат если указан
            date_range = self.process_date_range(
                date_from=options.get("date_from"),
                date_to=options.get("date_to")
            )
            
            # Выполняем основную логику команды
            result = self.execute_command(credentials, date_range, **options)
            
            # Обрабатываем результат
            self.handle_result(result)
            
        except Exception as e:
            self.handle_exception("command execution", e)
    
    @abstractmethod
    def get_credentials(self) -> dict[str, Any]:
        """
        Получает учетные данные для API.
        Должен быть реализован в подклассах.
        
        Returns:
            Dict с учетными данными
        """
        raise NotImplementedError
    
    def validate_credentials(self, credentials: dict[str, Any]) -> bool:
        """
        Валидирует учетные данные.
        Может быть переопределен в подклассах для специфичной валидации.
        
        Args:
            credentials: Словарь с учетными данными
            
        Returns:
            True если учетные данные валидны, False в противном случае
        """
        return bool(credentials)
    
    @abstractmethod
    def execute_command(self, credentials: dict[str, Any], date_range: tuple[datetime, datetime] | None, **options) -> Any:
        """
        Выполняет основную логику команды.
        Должен быть реализован в подклассах.
        
        Args:
            credentials: Учетные данные для API
            date_range: Кортеж (date_from, date_to) или None
            **options: Дополнительные опции команды
            
        Returns:
            Результат выполнения команды
        """
        raise NotImplementedError
    
    def handle_result(self, result: Any):
        """
        Обрабатывает результат выполнения команды.
        Может быть переопределен в подклассах.
        
        Args:
            result: Результат выполнения команды
        """
        if self.verbosity >= 1:
            self.stdout.write(self.style.SUCCESS("Command completed successfully"))
    
    def process_date_range(self, date_from: str | None = None, date_to: str | None = None) -> tuple[datetime, datetime] | None:
        """
        Обрабатывает диапазон дат из строковых параметров.
        
        Args:
            date_from: Начальная дата в формате YYYY-MM-DD
            date_to: Конечная дата в формате YYYY-MM-DD
            
        Returns:
            Кортеж (date_from, date_to) с timezone-aware datetime объектами или None
        """
        if not date_from and not date_to:
            return None
        
        try:
            # Преобразуем строки в datetime объекты
            if date_from:
                date_from_dt = datetime.strptime(date_from, "%Y-%m-%d")
                date_from_dt = django_timezone.make_aware(date_from_dt)
            else:
                # Если не указана начальная дата, используем очень раннюю дату
                date_from_dt = django_timezone.make_aware(datetime(1970, 1, 1))
            
            if date_to:
                date_to_dt = datetime.strptime(date_to, "%Y-%m-%d")
                # Устанавливаем конец дня
                date_to_dt = date_to_dt.replace(hour=23, minute=59, second=59, microsecond=999999)
                date_to_dt = django_timezone.make_aware(date_to_dt)
            else:
                # Если не указана конечная дата, используем текущую дату
                date_to_dt = django_timezone.now()
            
            # Проверяем корректность диапазона
            if date_from_dt > date_to_dt:
                raise CommandError("date_from cannot be later than date_to")
            
            return (date_from_dt, date_to_dt)
            
        except ValueError as e:
            raise CommandError(f"Invalid date format. Use YYYY-MM-DD. Error: {e}")
    
    def handle_exception(self, context: str, exception: Exception):
        """
        Унифицированная обработка исключений.
        
        Args:
            context: Контекст, в котором произошла ошибка
            exception: Исключение
        """
        error_message = f"Error during {context}: {exception!s}"
        
        if self.verbosity >= 2:
            # В режиме высокой детализации показываем полный traceback
            import traceback
            self.stderr.write(self.style.ERROR(error_message))
            self.stderr.write(traceback.format_exc())
        else:
            self.stderr.write(self.style.ERROR(error_message))
        
        # Перебрасываем исключение как CommandError
        raise CommandError(error_message) from exception
    
    def log_progress(self, message: str, level: str = "info"):
        """
        Логирует прогресс выполнения команды.
        
        Args:
            message: Сообщение для логирования
            level: Уровень логирования (info, success, warning, error)
        """
        if self.verbosity >= 1:
            style_map = {
                "info": self.style.NOTICE,
                "success": self.style.SUCCESS,
                "warning": self.style.WARNING,
                "error": self.style.ERROR,
            }
            style = style_map.get(level, self.style.NOTICE)
            self.stdout.write(style(message))
    
    def confirm_action(self, message: str) -> bool:
        """
        Запрашивает подтверждение пользователя для действия.
        
        Args:
            message: Сообщение для показа пользователю
            
        Returns:
            True если пользователь подтвердил, False в противном случае
        """
        if self.dry_run:
            return True
            
        response = input(f"{message} (y/N): ").lower().strip()
        return response in ["y", "yes"]