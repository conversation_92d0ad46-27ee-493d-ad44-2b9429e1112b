"""
Management command to test logging configuration.

This command tests various logging scenarios to ensure that:
- All loggers are properly configured
- Log levels work correctly
- Filters (sensitive data, rate limiting) work
- Handlers output to correct locations
- Context variables are captured
- Sentry integration works (if configured)
"""

import logging
import time
from django.core.management.base import BaseCommand
from django.conf import settings

# Import our logging components
from core.logging.logger import ContextLogger
from core.logging.context import set_request_context, set_task_context, add_context


class Command(BaseCommand):
    help = "Test logging configuration by generating various log messages"
    
    def add_arguments(self, parser):
        parser.add_argument(
            '--level',
            type=str,
            default='all',
            choices=['debug', 'info', 'warning', 'error', 'critical', 'all'],
            help='Log level to test (default: all)'
        )
        parser.add_argument(
            '--logger',
            type=str,
            default='all',
            help='Logger name to test (default: all)'
        )
        parser.add_argument(
            '--test-sentry',
            action='store_true',
            help='Test Sentry integration (sends test error)'
        )
        parser.add_argument(
            '--test-rate-limit',
            action='store_true',
            help='Test rate limiting by sending repeated messages'
        )
        parser.add_argument(
            '--test-sensitive',
            action='store_true',
            help='Test sensitive data filtering'
        )
        parser.add_argument(
            '--test-context',
            action='store_true',
            help='Test context variables (request_id, user_id, task_id)'
        )
        parser.add_argument(
            '--test-performance',
            action='store_true',
            help='Test performance logging'
        )
        
    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('Testing logging configuration...'))
        self.stdout.write(f"Environment: {settings.ENVIRONMENT}")
        self.stdout.write(f"Log directory: {getattr(settings, 'LOG_DIR', 'Not configured')}")
        self.stdout.write(f"Sentry enabled: {bool(getattr(settings, 'SENTRY_DSN', ''))}")
        self.stdout.write('-' * 50)
        
        # Determine which loggers to test
        if options['logger'] == 'all':
            loggers_to_test = [
                'django',
                'django.request',
                'django.security',
                'core',
                'instagram_manager',
                'telegram_manager',
                'celery',
                'monitoring',
            ]
        else:
            loggers_to_test = [options['logger']]
        
        # Determine which levels to test
        if options['level'] == 'all':
            levels_to_test = ['debug', 'info', 'warning', 'error', 'critical']
        else:
            levels_to_test = [options['level']]
        
        # Test basic logging for each logger and level
        self._test_basic_logging(loggers_to_test, levels_to_test)
        
        # Test specific features if requested
        if options['test_context']:
            self._test_context_logging()
            
        if options['test_sensitive']:
            self._test_sensitive_data_filtering()
            
        if options['test_rate_limit']:
            self._test_rate_limiting()
            
        if options['test_performance']:
            self._test_performance_logging()
            
        if options['test_sentry']:
            self._test_sentry_integration()
        
        self.stdout.write(self.style.SUCCESS('\nLogging test completed!'))
        self.stdout.write('Check the following locations for output:')
        self.stdout.write('- Console output above')
        if hasattr(settings, 'LOG_DIR'):
            self.stdout.write(f'- Log files in: {settings.LOG_DIR}')
        if getattr(settings, 'SENTRY_DSN', ''):
            self.stdout.write('- Sentry dashboard for error events')
    
    def _test_basic_logging(self, loggers, levels):
        """Test basic logging for specified loggers and levels."""
        self.stdout.write('\n' + self.style.MIGRATE_HEADING('Testing basic logging:'))
        
        for logger_name in loggers:
            logger = logging.getLogger(logger_name)
            self.stdout.write(f'\nLogger: {logger_name}')
            
            for level in levels:
                level_value = getattr(logging, level.upper())
                test_message = f'Test {level} message from {logger_name}'
                
                # Log the message
                logger.log(level_value, test_message)
                
                # Also test with extra data
                logger.log(
                    level_value,
                    f'{test_message} with extra data',
                    extra={
                        'user': 'test_user',
                        'action': 'test_action',
                        'metadata': {'test': True}
                    }
                )
    
    def _test_context_logging(self):
        """Test context variable logging."""
        self.stdout.write('\n' + self.style.MIGRATE_HEADING('Testing context logging:'))
        
        # Test request context
        set_request_context(user_id=123)
        logger = logging.getLogger('core')
        logger.info('Test message with request context')
        
        # Test task context
        set_task_context('test-task-123', 'test_logging_command')
        logger.info('Test message with task context')
        
        # Test custom context
        add_context(environment='test', version='1.0.0')
        logger.info('Test message with custom context')
        
        # Test ContextLogger
        context_logger = ContextLogger('test.context')
        context_logger.info(
            'Test message from ContextLogger',
            user_id=456,
            action='test_context_logger'
        )
    
    def _test_sensitive_data_filtering(self):
        """Test sensitive data filtering."""
        self.stdout.write('\n' + self.style.MIGRATE_HEADING('Testing sensitive data filtering:'))
        
        logger = logging.getLogger('core')
        
        # Test various sensitive data patterns
        sensitive_messages = [
            'User password: secret123!',
            'API key: sk-1234567890abcdef',
            'Bearer token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9',
            'Credit card: ****************',
            'Social Security: ***********',
            'Email: <EMAIL> with password: mypassword',
            'Connection string: postgresql://user:password@localhost/db',
        ]
        
        for msg in sensitive_messages:
            logger.info(f'Testing filter: {msg}')
            
        # Test in structured data
        logger.info('User data', extra={
            'user': {
                'email': '<EMAIL>',
                'password': 'should_be_filtered',
                'api_key': 'sk-secret-key-12345'
            }
        })
    
    def _test_rate_limiting(self):
        """Test rate limit filtering."""
        self.stdout.write('\n' + self.style.MIGRATE_HEADING('Testing rate limiting:'))
        
        logger = logging.getLogger('core')
        
        # Send the same message multiple times quickly
        for i in range(15):
            logger.warning('This is a repeated warning message')
            if i == 5:
                self.stdout.write('...sending 10 more identical messages...')
        
        # Wait a bit and send one more
        time.sleep(2)
        logger.warning('This is a repeated warning message')
        
        # Test with errors
        for i in range(10):
            logger.error('This is a repeated error message')
    
    def _test_performance_logging(self):
        """Test performance logging."""
        self.stdout.write('\n' + self.style.MIGRATE_HEADING('Testing performance logging:'))
        
        perf_logger = logging.getLogger('monitoring')
        
        # Log performance metrics
        perf_logger.info('API request completed', extra={
            'endpoint': '/api/test',
            'method': 'GET',
            'response_time': 125.5,
            'status_code': 200,
            'user_id': 123
        })
        
        perf_logger.warning('Slow query detected', extra={
            'query': 'SELECT * FROM large_table',
            'execution_time': 2500.0,
            'rows_examined': 1000000
        })
        
        # Test with ContextLogger
        context_logger = ContextLogger('monitoring.performance')
        context_logger.log_performance(
            'test_operation',
            duration=0.345,
            metadata={'items_processed': 100}
        )
    
    def _test_sentry_integration(self):
        """Test Sentry integration."""
        self.stdout.write('\n' + self.style.MIGRATE_HEADING('Testing Sentry integration:'))
        
        if not getattr(settings, 'SENTRY_DSN', ''):
            self.stdout.write(self.style.WARNING('Sentry is not configured, skipping test'))
            return
        
        logger = logging.getLogger('core')
        
        # Test warning (should be sent to Sentry in production)
        logger.warning('Test warning for Sentry', extra={
            'test': True,
            'command': 'test_logging'
        })
        
        # Test error with exception
        try:
            raise ValueError('Test exception for Sentry')
        except ValueError:
            logger.exception('Test error with exception for Sentry')
        
        # Test critical
        logger.critical('Test critical message for Sentry', extra={
            'severity': 'critical',
            'immediate_action_required': True
        })
        
        self.stdout.write(self.style.SUCCESS('Test events sent to Sentry'))