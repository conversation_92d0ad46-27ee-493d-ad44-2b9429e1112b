"""
Management command to test Sentry integration.

This command allows testing various Sentry features including
error capturing, performance monitoring, and custom contexts.
"""

import time

from django.conf import settings
from django.core.management.base import BaseCommand, CommandError

import structlog

logger = structlog.get_logger(__name__)


class Command(BaseCommand):
    """Test Sentry integration with various scenarios."""
    
    help = "Test Sentry SDK integration"
    
    def add_arguments(self, parser):
        """Add command arguments."""
        parser.add_argument(
            "--test",
            type=str,
            choices=[
                "error",
                "message",
                "transaction",
                "breadcrumb",
                "context",
                "performance",
                "all",
            ],
            default="error",
            help="Type of test to run",
        )
        
        parser.add_argument(
            "--slow",
            action="store_true",
            help="Include slow operations for performance testing",
        )
        
        parser.add_argument(
            "--user-context",
            action="store_true",
            help="Include user context in tests",
        )
    
    def handle(self, *args, **options):
        """Handle command execution."""
        try:
            import sentry_sdk
        except ImportError:
            raise CommandError("Sentry SDK is not installed")
        
        # Check if Sen<PERSON> is configured
        if not hasattr(settings, "SENTRY_DSN") or not settings.SENTRY_DSN:
            raise CommandError("SENTRY_DSN is not configured")
        
        test_type = options["test"]
        
        self.stdout.write(self.style.WARNING(f"Running Sentry {test_type} test..."))
        
        # Set user context if requested
        if options["user_context"]:
            sentry_sdk.set_user({
                "id": "test-user-123",
                "username": "testuser",
                "email": "<EMAIL>",
            })
        
        # Run specific test
        if test_type == "all":
            self._test_error()
            self._test_message()
            self._test_transaction(options["slow"])
            self._test_breadcrumb()
            self._test_context()
            self._test_performance(options["slow"])
        else:
            test_method = getattr(self, f"_test_{test_type}")
            if test_type in ["transaction", "performance"]:
                test_method(options["slow"])
            else:
                test_method()
        
        self.stdout.write(self.style.SUCCESS("Sentry test completed successfully!"))
    
    def _test_error(self):
        """Test error capturing."""
        self.stdout.write("Testing error capture...")
        
        try:
            # Simulate various error types
            import sentry_sdk
            
            # Add breadcrumb before error
            sentry_sdk.add_breadcrumb(
                category="test",
                message="About to raise test error",
                level="info",
            )
            
            # Raise test exception
            raise ValueError("This is a test error from test_sentry command")
            
        except ValueError as e:
            # Capture with additional context
            sentry_sdk.capture_exception(
                e,
                contexts={
                    "test_info": {
                        "command": "test_sentry",
                        "test_type": "error",
                        "purpose": "Testing Sentry error capture",
                    }
                },
                tags={
                    "test": True,
                    "command": "test_sentry",
                },
            )
            
            self.stdout.write(self.style.SUCCESS("✓ Error captured"))
    
    def _test_message(self):
        """Test message capturing."""
        self.stdout.write("Testing message capture...")
        
        import sentry_sdk
        
        # Send different level messages
        sentry_sdk.capture_message(
            "Test info message from test_sentry command",
            level="info",
            tags={"test": True, "type": "info"},
        )
        
        sentry_sdk.capture_message(
            "Test warning message from test_sentry command",
            level="warning",
            tags={"test": True, "type": "warning"},
        )
        
        self.stdout.write(self.style.SUCCESS("✓ Messages captured"))
    
    def _test_transaction(self, slow: bool = False):
        """Test transaction tracking."""
        self.stdout.write("Testing transaction...")
        
        import sentry_sdk
        
        # Start transaction
        with sentry_sdk.start_transaction(
            op="test",
            name="test_sentry_transaction",
        ) as transaction:
            transaction.set_tag("test", True)
            transaction.set_data("command", "test_sentry")
            
            # Simulate operations with spans
            with transaction.start_child(op="db", description="Simulated DB query"):
                time.sleep(0.1)
            
            with transaction.start_child(op="http", description="Simulated HTTP call"):
                time.sleep(0.2)
            
            if slow:
                with transaction.start_child(op="processing", description="Slow operation"):
                    time.sleep(2.0)
            
            transaction.set_status("ok")
        
        self.stdout.write(self.style.SUCCESS("✓ Transaction tracked"))
    
    def _test_breadcrumb(self):
        """Test breadcrumb functionality."""
        self.stdout.write("Testing breadcrumbs...")
        
        import sentry_sdk
        
        # Add various breadcrumbs
        sentry_sdk.add_breadcrumb(
            category="navigation",
            message="User navigated to test command",
            level="info",
            data={"command": "test_sentry"},
        )
        
        sentry_sdk.add_breadcrumb(
            category="action",
            message="Starting breadcrumb test",
            level="debug",
        )
        
        sentry_sdk.add_breadcrumb(
            category="test",
            message="Test breadcrumb with data",
            level="info",
            data={
                "test_id": 123,
                "test_type": "breadcrumb",
                "metadata": {"key": "value"},
            },
        )
        
        # Capture a message to see breadcrumbs
        sentry_sdk.capture_message(
            "Test message with breadcrumbs",
            level="info",
        )
        
        self.stdout.write(self.style.SUCCESS("✓ Breadcrumbs added"))
    
    def _test_context(self):
        """Test custom context."""
        self.stdout.write("Testing custom context...")
        
        import sentry_sdk
        
        # Set various contexts
        sentry_sdk.set_context("app_info", {
            "version": "1.0.0",
            "environment": settings.DJANGO_ENV,
            "debug": settings.DEBUG,
        })
        
        sentry_sdk.set_context("test_context", {
            "command": "test_sentry",
            "test_type": "context",
            "custom_data": {
                "nested": True,
                "values": [1, 2, 3],
            },
        })
        
        # Set tags
        sentry_sdk.set_tag("test.command", "test_sentry")
        sentry_sdk.set_tag("test.type", "context")
        
        # Capture event with context
        sentry_sdk.capture_message(
            "Test message with custom context",
            level="info",
        )
        
        self.stdout.write(self.style.SUCCESS("✓ Context set"))
    
    def _test_performance(self, slow: bool = False):
        """Test performance monitoring."""
        self.stdout.write("Testing performance monitoring...")
        
        import sentry_sdk
        from core.utils.decorators import monitor_performance
        
        @monitor_performance(alert_threshold=1.0)
        def slow_operation():
            """Simulate slow operation."""
            duration = 2.0 if slow else 0.5
            time.sleep(duration)
            return "completed"
        
        # Test decorated function
        with sentry_sdk.start_transaction(
            op="test.performance",
            name="test_performance_monitoring",
        ):
            slow_operation()
            
            # Add custom measurements
            sentry_sdk.set_measurement("test.duration", 1.5, "second")
            sentry_sdk.set_measurement("test.items", 100, "none")
        
        self.stdout.write(self.style.SUCCESS("✓ Performance monitored"))