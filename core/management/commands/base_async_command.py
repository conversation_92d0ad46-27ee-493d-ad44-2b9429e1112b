"""
Base command class with async task support.

This module provides a base class for Django management commands that can be
executed either synchronously or asynchronously using the task queue system.
"""

from abc import ABC, abstractmethod
from typing import Any

from django.core.management.base import BaseCommand, CommandParser

from core.logging import ContextLogger
from core.tasks.base import BaseTask, TaskStatus

logger = ContextLogger(__name__)


class BaseAsyncCommand(BaseCommand, ABC):
    """
    Base class for management commands with async execution support.
    
    Subclasses should implement:
    - get_task_class(): Return the BaseTask subclass to use
    - prepare_task_params(): Convert command options to task parameters
    
    Example:
        class Command(BaseAsyncCommand):
            def get_task_class(self):
                return ImportInstagramProfileTask
            
            def prepare_task_params(self, **options):
                return {
                    'username': options['username'],
                    'deep_scan': options.get('deep_scan', False)
                }
    """
    
    def add_arguments(self, parser: CommandParser) -> None:
        """Add common arguments including sync/async flags."""
        # Add sync flag (inverted logic - async by default)
        parser.add_argument(
            "--sync",
            action="store_true",
            help="Run command synchronously (default: async via Celery)"
        )
        
        # Legacy --async flag for backward compatibility
        parser.add_argument(
            "--async",
            action="store_true",
            help="(Deprecated) Run asynchronously - this is now the default behavior"
        )
        
        # Add monitoring options
        parser.add_argument(
            "--monitor",
            action="store_true",
            help="Monitor task progress (only in async mode)"
        )
        
        parser.add_argument(
            "--wait",
            action="store_true",
            help="Wait for async task to complete and show result"
        )
        
        # Let subclasses add their own arguments
        self.add_task_arguments(parser)
    
    def add_task_arguments(self, parser: CommandParser) -> None:
        """
        Add task-specific arguments.
        
        Subclasses should override this to add their own arguments.
        """
        pass
    
    @abstractmethod
    def get_task_class(self) -> type[BaseTask]:
        """Return the BaseTask class to use for async execution."""
        pass
    
    @abstractmethod
    def prepare_task_params(self, **options) -> dict[str, Any]:
        """
        Convert command options to task parameters.
        
        This method should extract and validate parameters needed for the task
        from the command line options.
        """
        pass
    
    def handle(self, *args, **options) -> None:
        """Main command handler with async support (async by default)."""
        # Get task class and parameters
        task_class = self.get_task_class()
        task_params = self.prepare_task_params(**options)
        
        # If task_params is None, don't create task
        if task_params is None:
            # prepare_task_params should have already printed error message
            return
        
        # If task_class is None, try to use handle_command if available
        if task_class is None:
            if hasattr(self, "handle_command"):
                self.handle_command(*args, **options)  # type: ignore[attr-defined]
                return
            else:
                self.stdout.write(
                    self.style.ERROR(
                        "No task class defined and no handle_command method available"
                    )
                )
                return
        
        # Determine execution mode
        force_sync = options.get("sync", False)
        
        # Check global setting if not explicitly set
        if not force_sync and not options.get("async"):
            from django.conf import settings
            force_sync = getattr(settings, "INSTAGRAM_IMPORTS_FORCE_SYNC", False)
        
        if force_sync:
            # Run synchronously
            self._handle_sync(task_class, task_params, options)
        else:
            # Run asynchronously (default)
            self._handle_async(task_class, task_params, options)
    
    def _handle_async(self, task_class: type[BaseTask], params: dict[str, Any], options: dict[str, Any]) -> None:
        """Handle asynchronous execution."""
        # Create task and run via Celery
        task = task_class()
        celery_result = task.run_async(**(params or {}))
        task_id = task.task_id
        
        self.stdout.write(
            self.style.SUCCESS("Task started successfully via Celery!")
        )
        self.stdout.write(f"Task ID: {task_id}")
        self.stdout.write(f"Celery Task ID: {celery_result.id}")
        self.stdout.write(f"Task Type: {task_class.task_type}")
        
        # Provide monitoring instructions
        if not options.get("wait"):
            self.stdout.write("\nTo monitor task progress:")
            self.stdout.write(f"  python manage.py celery_status {task_id}")
            self.stdout.write("  Or visit Flower: http://localhost:5555")
            self.stdout.write("  Or visit: /monitoring/tasks/")
        
        # Wait for completion if requested
        if options.get("wait"):
            self._wait_for_task(task_id, monitor=options.get("monitor"), celery_result=celery_result)
    
    def _handle_sync(self, task_class: type[BaseTask], params: dict[str, Any], options: dict[str, Any]) -> None:
        """Handle synchronous execution."""
        # Create task instance
        task = task_class()
        
        # Show progress in terminal
        original_update_progress = task.update_progress
        
        def terminal_progress(progress: int, message: str = ""):
            original_update_progress(progress, message)
            self.stdout.write(f"[{progress:3d}%] {message}")
        
        task.update_progress = terminal_progress  # type: ignore[method-assign]
        
        try:
            # Execute task
            self.stdout.write(
                self.style.WARNING(f"Executing {task_class.description} in SYNC mode (debug)...")
            )
            result = task.execute(**(params or {}))
            
            # Show result
            self._display_result(result)
            
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f"Task failed: {e}")
            )
            raise
    
    def _wait_for_task(self, task_id: str, monitor: bool = False, celery_result = None) -> None:
        """Wait for async task to complete."""
        import time

        from core.models import TaskResult
        
        self.stdout.write("\nWaiting for task to complete...")
        
        last_progress = -1
        while True:
            try:
                task_result = TaskResult.objects.get(task_id=task_id)
                
                # Sync with Celery if we have result
                if celery_result and task_result.celery_task_id:
                    task_result.sync_with_celery()
                
                # Show progress if monitoring
                if monitor and task_result.processed_items != last_progress:
                    last_progress = task_result.processed_items
                    progress_pct = task_result.progress_percentage
                    self.stdout.write(
                        f"[{progress_pct:3.0f}%] {task_result.progress_message}"
                    )
                
                # Check if completed
                if task_result.status in [TaskStatus.SUCCESS, TaskStatus.FAILED, TaskStatus.CANCELLED]:
                    break
                
                # Also check Celery status directly
                if celery_result and celery_result.ready():
                    task_result.sync_with_celery()
                    break
                
            except TaskResult.DoesNotExist:
                self.stdout.write("Waiting for task to start...")
            
            time.sleep(1)
        
        # Show final result
        if task_result.status == TaskStatus.SUCCESS:
            self.stdout.write(
                self.style.SUCCESS("\nTask completed successfully!")
            )
            if task_result.result:
                self._display_result(task_result.result)
        elif task_result.status == TaskStatus.FAILED:
            self.stdout.write(
                self.style.ERROR(f"\nTask failed: {task_result.error_message}")
            )
        else:
            self.stdout.write(
                self.style.WARNING(f"\nTask {task_result.status}")
            )
    
    def _display_result(self, result: dict[str, Any]) -> None:
        """Display task result in a formatted way."""
        self.stdout.write("\nResult:")
        
        # Default display - subclasses can override for custom formatting
        for key, value in result.items():
            if key == "errors" and value:
                self.stdout.write(self.style.ERROR(f"  {key}: {len(value)} errors"))
                for error in value[:5]:  # Show first 5 errors
                    self.stdout.write(f"    - {error}")
                if len(value) > 5:
                    self.stdout.write(f"    ... and {len(value) - 5} more")
            else:
                self.stdout.write(f"  {key}: {value}")


class TaskMonitorCommand(BaseCommand):
    """Command to monitor task status."""
    
    help = "Monitor the status of an async task"
    
    def add_arguments(self, parser: CommandParser) -> None:
        parser.add_argument("task_id", help="Task ID to monitor")
        parser.add_argument(
            "--follow",
            action="store_true",
            help="Follow task progress until completion"
        )
    
    def handle(self, *args, **options) -> None:
        from core.models import TaskResult
        
        task_id = options["task_id"]
        
        try:
            task_result = TaskResult.objects.get(task_id=task_id)
        except TaskResult.DoesNotExist:
            self.stdout.write(
                self.style.ERROR(f"Task {task_id} not found")
            )
            return
        
        # Display task info
        self.stdout.write(f"Task ID: {task_result.task_id}")
        self.stdout.write(f"Type: {task_result.task_type}")
        self.stdout.write(f"Status: {task_result.get_status_display()}")
        self.stdout.write(f"Progress: {task_result.progress_percentage:.0f}%")
        
        if task_result.progress_message:
            self.stdout.write(f"Message: {task_result.progress_message}")
        
        if task_result.started_at:
            self.stdout.write(f"Started: {task_result.started_at}")
        
        if task_result.completed_at:
            self.stdout.write(f"Completed: {task_result.completed_at}")
            duration = task_result.completed_at - task_result.started_at
            self.stdout.write(f"Duration: {duration}")
        
        if task_result.error_message:
            self.stdout.write(
                self.style.ERROR(f"Error: {task_result.error_message}")
            )
        
        if task_result.result:
            self.stdout.write("\nResult:")
            for key, value in task_result.result.items():
                self.stdout.write(f"  {key}: {value}")
        
        # Follow progress if requested
        if options["follow"] and task_result.status in [TaskStatus.PENDING, TaskStatus.RUNNING]:
            self._follow_progress(task_result)
    
    def _follow_progress(self, task_result) -> None:
        """Follow task progress until completion."""
        import time
        
        self.stdout.write("\nFollowing task progress...")
        
        last_progress = task_result.processed_items
        while task_result.status in [TaskStatus.PENDING, TaskStatus.RUNNING]:
            time.sleep(1)
            task_result.refresh_from_db()
            
            if task_result.processed_items != last_progress:
                last_progress = task_result.processed_items
                self.stdout.write(
                    f"[{task_result.progress_percentage:3.0f}%] {task_result.progress_message}"
                )
        
        # Show final status
        if task_result.status == TaskStatus.SUCCESS:
            self.stdout.write(self.style.SUCCESS("\nTask completed successfully!"))
        elif task_result.status == TaskStatus.FAILED:
            self.stdout.write(self.style.ERROR(f"\nTask failed: {task_result.error_message}"))
        else:
            self.stdout.write(f"\nTask {task_result.get_status_display()}")