"""
Management command to verify Sentry integration.

This command intentionally raises a division by zero error
as recommended by Sentry documentation for verifying the setup.
"""

from django.conf import settings
from django.core.management.base import BaseCommand


class Command(BaseCommand):
    """Verify Sentry integration by causing an intentional error."""
    
    help = "Verify Sentry integration by causing a division by zero error"
    
    def handle(self, *args, **options):
        """Handle command execution."""
        # Check if <PERSON><PERSON> is configured
        if not hasattr(settings, "SENTRY_DSN") or not settings.SENTRY_DSN:
            self.stdout.write(
                self.style.ERROR("SENTRY_DSN is not configured in settings")
            )
            return
        
        self.stdout.write(
            self.style.WARNING(
                "Triggering a test error for Sentry...\n"
                "This will intentionally cause a division by zero error."
            )
        )
        
        # Add some context before the error
        self.stdout.write("Performing some operations before the error...")
        
        # This is the intentional error as recommended by Sentry docs
        1 / 0
        
        # This line will never execute
        self.stdout.write("This message should not appear")