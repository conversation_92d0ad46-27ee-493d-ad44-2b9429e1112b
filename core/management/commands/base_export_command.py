"""
Базовый класс для команд экспорта данных из социальных медиа.
"""

import csv
import json
import os
from abc import abstractmethod
from datetime import datetime
from typing import Any

from django.core.serializers.json import DjangoJSONEncoder
from django.db.models import QuerySet
from tqdm import tqdm

from .base import BaseSocialMediaCommand


class BaseExportCommand(BaseSocialMediaCommand):
    """
    Базовый класс для всех команд экспорта данных.
    
    Предоставляет общую функциональность:
    - Поддержка различных форматов (CSV, JSON, Excel)
    - Пагинация больших наборов данных
    - Прогресс отслеживание
    - Фильтрация данных
    - Потоковая запись для экономии памяти
    """
    
    # Поддерживаемые форматы экспорта
    SUPPORTED_FORMATS = ["csv", "json", "jsonl", "excel"]
    
    def add_custom_arguments(self, parser):
        """Добавляет аргументы специфичные для экспорта"""
        super().add_custom_arguments(parser)
        
        parser.add_argument(
            "--format",
            type=str,
            choices=self.SUPPORTED_FORMATS,
            default="csv",
            help="Формат экспорта (по умолчанию: csv)"
        )
        
        parser.add_argument(
            "--output",
            "-o",
            type=str,
            required=True,
            help="Путь к выходному файлу"
        )
        
        parser.add_argument(
            "--limit",
            type=int,
            default=None,
            help="Максимальное количество записей для экспорта"
        )
        
        parser.add_argument(
            "--offset",
            type=int,
            default=0,
            help="Количество записей для пропуска"
        )
        
        parser.add_argument(
            "--batch-size",
            type=int,
            default=1000,
            help="Размер пакета для обработки (по умолчанию: 1000)"
        )
        
        parser.add_argument(
            "--fields",
            type=str,
            nargs="+",
            help="Список полей для экспорта (по умолчанию: все)"
        )
        
        parser.add_argument(
            "--exclude-fields",
            type=str,
            nargs="+",
            help="Список полей для исключения из экспорта"
        )
        
        parser.add_argument(
            "--filter",
            type=str,
            action="append",
            help="Фильтры в формате field=value (можно использовать несколько раз)"
        )
        
        parser.add_argument(
            "--order-by",
            type=str,
            help="Поле для сортировки (используйте '-' для обратной сортировки)"
        )
        
        parser.add_argument(
            "--progress",
            action="store_true",
            default=True,
            help="Показывать прогресс-бар"
        )
        
        parser.add_argument(
            "--no-progress",
            action="store_false",
            dest="progress",
            help="Не показывать прогресс-бар"
        )
        
        parser.add_argument(
            "--append",
            action="store_true",
            help="Добавить к существующему файлу (только для CSV)"
        )
    
    def execute_command(
        self,
        credentials: dict[str, Any],
        date_range: tuple | None,
        **options
    ) -> dict[str, Any]:
        """
        Выполняет команду экспорта.
        
        Returns:
            Словарь со статистикой экспорта
        """
        # Инициализация статистики
        stats: dict[str, Any] = {
            "total": 0,
            "exported": 0,
            "errors": 0,
            "start_time": datetime.now()
        }
        
        # Получаем опции
        output_format = options.get("format", "csv")
        output_path = options.get("output")
        limit = options.get("limit")
        offset = options.get("offset", 0)
        batch_size = options.get("batch_size", 1000)
        show_progress = options.get("progress", True)
        append_mode = options.get("append", False)
        
        # Проверяем выходной путь
        output_dir = os.path.dirname(output_path)
        if output_dir and not os.path.exists(output_dir):
            os.makedirs(output_dir)
        
        try:
            # Получаем queryset для экспорта
            self.log_progress("Preparing data for export...", "info")
            queryset = self.get_queryset(date_range, **options)
            
            # Применяем фильтры
            filters = self._parse_filters(options.get("filter", []))
            if filters:
                queryset = self.apply_filters(queryset, filters)
            
            # Применяем сортировку
            order_by = options.get("order_by")
            if order_by:
                queryset = queryset.order_by(order_by)
            
            # Применяем offset и limit
            if offset:
                queryset = queryset[offset:]
            if limit:
                queryset = queryset[:limit]
            
            # Получаем общее количество
            stats["total"] = queryset.count()
            
            if stats["total"] == 0:
                self.log_progress("No data to export", "warning")
                return stats
            
            self.log_progress(f"Found {stats['total']} records to export", "info")
            
            # Определяем поля для экспорта
            fields = self._determine_fields(queryset, options)
            
            # Экспортируем данные
            if output_format == "csv":
                stats["exported"] = self._export_csv(
                    queryset, output_path, fields, batch_size,
                    show_progress, append_mode
                )
            elif output_format == "json":
                stats["exported"] = self._export_json(
                    queryset, output_path, fields, batch_size,
                    show_progress
                )
            elif output_format == "jsonl":
                stats["exported"] = self._export_jsonl(
                    queryset, output_path, fields, batch_size,
                    show_progress
                )
            elif output_format == "excel":
                stats["exported"] = self._export_excel(
                    queryset, output_path, fields, batch_size,
                    show_progress
                )
            
            # Финальная статистика
            stats["duration"] = (datetime.now() - stats["start_time"]).total_seconds()
            stats["output_file"] = output_path
            
        except Exception as e:
            self.handle_exception("export", e)
            stats["errors"] += 1
        
        return stats
    
    def _export_csv(
        self,
        queryset: QuerySet,
        output_path: str,
        fields: list[str],
        batch_size: int,
        show_progress: bool,
        append_mode: bool
    ) -> int:
        """Экспорт в CSV формат"""
        exported = 0
        mode = "a" if append_mode else "w"
        
        with open(output_path, mode, newline="", encoding="utf-8-sig") as csvfile:
            writer = None
            
            # Создаем прогресс-бар
            progress_bar = None
            if show_progress and self.verbosity >= 1:
                progress_bar = tqdm(
                    total=queryset.count(),
                    desc="Exporting to CSV",
                    unit="records"
                )
            
            # Обрабатываем пакетами
            for batch in self._iterate_queryset(queryset, batch_size):
                for item in batch:
                    row_data = self.serialize_item(item, fields)
                    
                    # Инициализируем writer с заголовками при первой записи
                    if writer is None and not (append_mode and os.path.getsize(output_path) > 0):
                        writer = csv.DictWriter(csvfile, fieldnames=row_data.keys())
                        writer.writeheader()
                    elif writer is None:
                        writer = csv.DictWriter(csvfile, fieldnames=row_data.keys())
                    
                    writer.writerow(row_data)
                    exported += 1
                    
                    if progress_bar:
                        progress_bar.update(1)
            
            if progress_bar:
                progress_bar.close()
        
        return exported
    
    def _export_json(
        self,
        queryset: QuerySet,
        output_path: str,
        fields: list[str],
        batch_size: int,
        show_progress: bool
    ) -> int:
        """Экспорт в JSON формат"""
        exported = 0
        all_data = []
        
        # Создаем прогресс-бар
        progress_bar = None
        if show_progress and self.verbosity >= 1:
            progress_bar = tqdm(
                total=queryset.count(),
                desc="Exporting to JSON",
                unit="records"
            )
        
        # Собираем все данные
        for batch in self._iterate_queryset(queryset, batch_size):
            for item in batch:
                row_data = self.serialize_item(item, fields)
                all_data.append(row_data)
                exported += 1
                
                if progress_bar:
                    progress_bar.update(1)
        
        if progress_bar:
            progress_bar.close()
        
        # Записываем весь JSON
        with open(output_path, "w", encoding="utf-8") as jsonfile:
            json.dump(
                all_data,
                jsonfile,
                cls=DjangoJSONEncoder,
                ensure_ascii=False,
                indent=2
            )
        
        return exported
    
    def _export_jsonl(
        self,
        queryset: QuerySet,
        output_path: str,
        fields: list[str],
        batch_size: int,
        show_progress: bool
    ) -> int:
        """Экспорт в JSON Lines формат"""
        exported = 0
        
        with open(output_path, "w", encoding="utf-8") as jsonlfile:
            # Создаем прогресс-бар
            progress_bar = None
            if show_progress and self.verbosity >= 1:
                progress_bar = tqdm(
                    total=queryset.count(),
                    desc="Exporting to JSONL",
                    unit="records"
                )
            
            # Обрабатываем пакетами
            for batch in self._iterate_queryset(queryset, batch_size):
                for item in batch:
                    row_data = self.serialize_item(item, fields)
                    json_line = json.dumps(
                        row_data,
                        cls=DjangoJSONEncoder,
                        ensure_ascii=False
                    )
                    jsonlfile.write(json_line + "\n")
                    exported += 1
                    
                    if progress_bar:
                        progress_bar.update(1)
            
            if progress_bar:
                progress_bar.close()
        
        return exported
    
    def _export_excel(
        self,
        queryset: QuerySet,
        output_path: str,
        fields: list[str],
        batch_size: int,
        show_progress: bool
    ) -> int:
        """Экспорт в Excel формат"""
        try:
            from openpyxl import Workbook
        except ImportError:
            self.log_progress(
                "openpyxl not installed. Install it with: pip install openpyxl",
                "error"
            )
            return 0
        
        exported = 0
        
        # Создаем новый workbook
        wb = Workbook()
        ws = wb.active
        ws.title = "Export"
        
        # Создаем прогресс-бар
        progress_bar = None
        if show_progress and self.verbosity >= 1:
            progress_bar = tqdm(
                total=queryset.count(),
                desc="Exporting to Excel",
                unit="records"
            )
        
        row_num = 1
        header_written = False
        
        # Обрабатываем пакетами
        for batch in self._iterate_queryset(queryset, batch_size):
            for item in batch:
                row_data = self.serialize_item(item, fields)
                
                # Записываем заголовки при первой записи
                if not header_written:
                    headers = list(row_data.keys())
                    for col_num, header in enumerate(headers, 1):
                        ws.cell(row=row_num, column=col_num, value=header)
                    row_num += 1
                    header_written = True
                
                # Записываем данные
                for col_num, value in enumerate(row_data.values(), 1):
                    # Преобразуем datetime объекты в строки для Excel
                    if hasattr(value, "isoformat"):
                        value = value.isoformat()
                    ws.cell(row=row_num, column=col_num, value=value)
                
                row_num += 1
                exported += 1
                
                if progress_bar:
                    progress_bar.update(1)
        
        if progress_bar:
            progress_bar.close()
        
        # Сохраняем файл
        wb.save(output_path)
        
        return exported
    
    def _iterate_queryset(self, queryset: QuerySet, batch_size: int):
        """Итерирует по queryset пакетами для экономии памяти"""
        total = queryset.count()
        
        for offset in range(0, total, batch_size):
            yield queryset[offset:offset + batch_size]
    
    def _determine_fields(self, queryset: QuerySet, options: dict) -> list[str]:
        """Определяет поля для экспорта"""
        # Если указаны конкретные поля
        if options.get("fields"):
            return list(options["fields"])
        
        # Получаем все поля модели
        model = queryset.model
        all_fields = [field.name for field in model._meta.fields]
        
        # Исключаем указанные поля
        exclude_fields = options.get("exclude_fields", [])
        if exclude_fields:
            all_fields = [f for f in all_fields if f not in exclude_fields]
        
        return all_fields
    
    def _parse_filters(self, filter_list: list[str]) -> dict[str, Any]:
        """Парсит фильтры из командной строки"""
        filters: dict[str, Any] = {}
        
        for filter_str in filter_list:
            if "=" not in filter_str:
                self.log_progress(
                    f"Invalid filter format: {filter_str}. Use field=value",
                    "warning"
                )
                continue
            
            field, value_str = filter_str.split("=", 1)
            
            # Пытаемся преобразовать значение в правильный тип
            value: Any = value_str  # Начинаем со строки
            if value_str.lower() == "true":
                value = True
            elif value_str.lower() == "false":
                value = False
            elif value_str.lower() == "none" or value_str.lower() == "null":
                value = None
            elif value_str.isdigit():
                value = int(value_str)
            
            filters[field] = value
        
        return filters
    
    @abstractmethod
    def get_queryset(
        self,
        date_range: tuple | None,
        **options
    ) -> QuerySet:
        """
        Возвращает queryset для экспорта.
        
        Должен быть реализован в подклассах.
        
        Args:
            date_range: Диапазон дат для фильтрации
            **options: Дополнительные опции
            
        Returns:
            QuerySet для экспорта
        """
        raise NotImplementedError
    
    def apply_filters(self, queryset: QuerySet, filters: dict) -> QuerySet:
        """
        Применяет фильтры к queryset.
        
        Может быть переопределен в подклассах для специфичной логики.
        
        Args:
            queryset: Исходный queryset
            filters: Словарь фильтров
            
        Returns:
            Отфильтрованный queryset
        """
        return queryset.filter(**filters)
    
    @abstractmethod
    def serialize_item(self, item: Any, fields: list[str]) -> dict:
        """
        Сериализует элемент для экспорта.
        
        Должен быть реализован в подклассах.
        
        Args:
            item: Элемент для сериализации
            fields: Список полей для экспорта
            
        Returns:
            Словарь с данными для экспорта
        """
        raise NotImplementedError
    
    def handle_result(self, result: Any):
        """Обрабатывает результат экспорта"""
        if isinstance(result, dict):
            # Показываем статистику экспорта
            self.log_progress("\nExport Statistics:", "success")
            self.log_progress(f"  Total records: {result.get('total', 0)}", "info")
            self.log_progress(f"  Exported: {result.get('exported', 0)}", "info")
            self.log_progress(f"  Errors: {result.get('errors', 0)}", "info")
            
            duration = result.get("duration", 0)
            if duration > 0:
                self.log_progress(
                    f"  Duration: {duration:.2f} seconds",
                    "info"
                )
                
                # Вычисляем скорость
                exported = result.get("exported", 0)
                if exported > 0:
                    rate = exported / duration
                    self.log_progress(
                        f"  Rate: {rate:.2f} records/second",
                        "info"
                    )
            
            output_file = result.get("output_file")
            if output_file and os.path.exists(output_file):
                size = os.path.getsize(output_file) / 1024 / 1024  # MB
                self.log_progress(
                    f"  Output file: {output_file} ({size:.2f} MB)",
                    "info"
                )
        else:
            super().handle_result(result)