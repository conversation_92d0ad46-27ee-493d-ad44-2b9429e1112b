"""
Middleware для унифицированной обработки ошибок API
"""

from typing import Any

from django.http import HttpRequest, HttpResponse, JsonResponse
from django.utils.deprecation import MiddlewareMixin

from core.exceptions import (
    BaseServiceException,
    RateLimitError,
)
from core.logging import ContextLogger


class ErrorHandlingMiddleware(MiddlewareMixin):
    """
    Миддлварь для унифицированной обработки ошибок API.
    
    Преобразует исключения в стандартные JSON ответы с правильными HTTP кодами.
    """
    
    def __init__(self, get_response=None):
        super().__init__(get_response)
        self.logger = ContextLogger(__name__)
        
        # Маппинг кодов ошибок на HTTP статусы
        self.status_map = {
            "NOT_FOUND": 404,
            "VALIDATION_ERROR": 400,
            "ALREADY_EXISTS": 409,
            "PERMISSION_DENIED": 403,
            "API_AUTHENTICATION_ERROR": 401,
            "RATE_LIMIT_ERROR": 429,
            "EXTERNAL_API_ERROR": 502,
            "DATABASE_ERROR": 500,
            "INTERNAL_ERROR": 500,
            "BUSINESS_LOGIC_ERROR": 422,
            "TEMPORARY_ERROR": 503,
            "NETWORK_ERROR": 502,
            "API_TIMEOUT_ERROR": 504,
            "API_RESPONSE_ERROR": 502,
        }
    
    def process_exception(
        self, 
        request: HttpRequest, 
        exception: Exception
    ) -> HttpResponse | None:
        """
        Обработка исключений и преобразование в JSON ответы.
        
        Args:
            request: HTTP запрос
            exception: Возникшее исключение
            
        Returns:
            JSON ответ с информацией об ошибке или None
        """
        if isinstance(exception, BaseServiceException):
            # Наши кастомные исключения
            return self._handle_service_exception(request, exception)
        else:
            # Неожиданные исключения
            return self._handle_unexpected_exception(request, exception)
    
    def _handle_service_exception(
        self, 
        request: HttpRequest, 
        exception: BaseServiceException
    ) -> JsonResponse:
        """
        Обработка кастомных исключений сервисов.
        
        Args:
            request: HTTP запрос
            exception: Исключение сервиса
            
        Returns:
            JSON ответ с деталями ошибки
        """
        # Определяем HTTP статус
        status_code = self._get_status_code(exception)
        
        # Формируем тело ответа
        error_response: dict[str, Any] = {
            "error": {
                "message": str(exception),
                "code": exception.code,
                "details": exception.details,
            }
        }
        
        # Добавляем request_id если есть
        if hasattr(request, "request_id"):
            error_response["error"]["request_id"] = request.request_id
        
        # Специальная обработка для некоторых типов ошибок
        if isinstance(exception, RateLimitError) and exception.retry_after:
            error_response["error"]["retry_after"] = exception.retry_after
        
        # Логируем в зависимости от типа ошибки
        if status_code >= 500:
            self.logger.error(
                f"Server error: {exception}",
                error_type=type(exception).__name__,
                error_code=exception.code,
                status_code=status_code,
                path=request.path,
                method=request.method,
            )
        else:
            self.logger.warning(
                f"Client error: {exception}",
                error_type=type(exception).__name__,
                error_code=exception.code,
                status_code=status_code,
                path=request.path,
                method=request.method,
            )
        
        response = JsonResponse(error_response, status=status_code)
        
        # Добавляем заголовки для некоторых типов ошибок
        if isinstance(exception, RateLimitError) and exception.retry_after:
            response["Retry-After"] = str(exception.retry_after)
        
        return response
    
    def _handle_unexpected_exception(
        self, 
        request: HttpRequest, 
        exception: Exception
    ) -> JsonResponse | None:
        """
        Обработка неожиданных исключений.
        
        Args:
            request: HTTP запрос
            exception: Неожиданное исключение
            
        Returns:
            JSON ответ с общей ошибкой или None для стандартной обработки
        """
        # Проверяем, нужно ли обрабатывать это исключение
        if not self._should_handle_exception(request):
            return None
        
        # Логируем полную информацию об ошибке
        self.logger.error(
            "Unexpected error occurred",
            exc_info=True,
            path=request.path,
            method=request.method,
            error_type=type(exception).__name__,
        )
        
        # Формируем безопасный ответ (не раскрываем детали)
        error_response = {
            "error": {
                "message": "Internal server error",
                "code": "INTERNAL_ERROR",
            }
        }
        
        if hasattr(request, "request_id"):
            error_response["error"]["request_id"] = request.request_id
        
        return JsonResponse(error_response, status=500)
    
    def _get_status_code(self, exception: BaseServiceException) -> int:
        """
        Определение HTTP статуса по типу исключения.
        
        Args:
            exception: Исключение
            
        Returns:
            HTTP статус код
        """
        return self.status_map.get(exception.code, 500)
    
    def _should_handle_exception(self, request: HttpRequest) -> bool:
        """
        Проверка, нужно ли обрабатывать исключение.
        
        Обрабатываем только API endpoints.
        
        Args:
            request: HTTP запрос
            
        Returns:
            True если нужно обработать
        """
        # Обрабатываем только API endpoints
        return (
            request.path.startswith("/api/") or
            request.path.startswith("/instagram/api/") or
            request.path.startswith("/telegram/api/")
        )


class APIErrorHandlingMiddleware:
    """
    Альтернативная реализация для ASGI/новых версий Django.
    """
    
    def __init__(self, get_response):
        self.get_response = get_response
        self.logger = ContextLogger(__name__)
        self.error_handler = ErrorHandlingMiddleware()
    
    async def __call__(self, request):
        try:
            response = await self.get_response(request)
            return response
        except Exception as exception:
            # Используем логику из ErrorHandlingMiddleware
            error_response = self.error_handler.process_exception(request, exception)
            if error_response:
                return error_response
            raise