"""
Sentry integration middleware for Django.

This middleware provides request context enrichment for <PERSON><PERSON>
and handles transaction tracking.
"""

import time
from collections.abc import Callable

import structlog
from django.http import HttpRequest, HttpResponse
from django.utils.deprecation import MiddlewareMixin

logger = structlog.get_logger(__name__)


class SentryContextMiddleware(MiddlewareMixin):
    """
    Middleware to enrich Sentry context with request information.
    
    This middleware:
    - Adds request metadata to Sentry context
    - Tracks transaction performance
    - Enriches error context with user and request data
    - Integrates with existing logging context
    """
    
    def __init__(self, get_response: Callable | None = None):
        """
        Initialize middleware.
        
        Args:
            get_response: The next middleware or view
        """
        self.get_response = get_response
        super().__init__(get_response)
        
        # Try to import Sentry
        try:
            import sentry_sdk
            self.sentry_sdk = sentry_sdk
            self.enabled = True
        except ImportError:
            self.sentry_sdk = None
            self.enabled = False
            logger.warning("Sentry SDK not available, SentryContextMiddleware disabled")
    
    def process_request(self, request: HttpRequest) -> HttpResponse | None:
        """
        Process incoming request.
        
        Args:
            request: Django HttpRequest
            
        Returns:
            None to continue processing
        """
        if not self.enabled:
            return None
            
        # Start transaction
        transaction_name = self._get_transaction_name(request)
        
        # Use modern Sentry SDK API - start transaction with context manager
        transaction = self.sentry_sdk.start_transaction(
            op="http.server",
            name=transaction_name,
            source="route",
        )
        
        # Store transaction on request
        request._sentry_transaction = transaction
        request._sentry_scope = self.sentry_sdk.push_scope()
        
        # Add request context
        with self.sentry_sdk.configure_scope() as scope:
            scope.set_transaction_name(transaction_name)
            
            # Add user context if authenticated
            if hasattr(request, "user") and request.user.is_authenticated:
                scope.set_user({
                    "id": str(request.user.id),
                    "username": request.user.username,
                    "email": getattr(request.user, "email", None),
                })
            
            # Add request context
            scope.set_context("request_meta", {
                "method": request.method,
                "path": request.path,
                "remote_addr": self._get_client_ip(request),
                "user_agent": request.META.get("HTTP_USER_AGENT", ""),
                "host": request.get_host(),
            })
            
            # Add custom tags
            scope.set_tag("http.method", request.method)
            scope.set_tag("http.path", request.path)
            
            # Add request ID if available
            if hasattr(request, "id"):
                scope.set_tag("request_id", str(request.id))
                scope.set_context("request", {"id": str(request.id)})
        
        # Start timing
        request._sentry_start_time = time.time()
        
        return None
    
    def process_response(
        self, request: HttpRequest, response: HttpResponse
    ) -> HttpResponse:
        """
        Process outgoing response.
        
        Args:
            request: Django HttpRequest
            response: Django HttpResponse
            
        Returns:
            HttpResponse
        """
        if not self.enabled:
            return response
            
        # Finish transaction if exists
        if hasattr(request, "_sentry_transaction"):
            transaction = request._sentry_transaction
            
            # Set response data
            transaction.set_status("ok" if response.status_code < 400 else "internal_error")
            transaction.set_http_status(response.status_code)
            
            # Add response size if available
            if hasattr(response, "content"):
                transaction.set_data("response_size", len(response.content))
            
            # Calculate duration
            if hasattr(request, "_sentry_start_time"):
                duration = time.time() - request._sentry_start_time
                transaction.set_data("duration", duration)
            
            # Finish transaction
            transaction.finish()
        
        # Clear scope if it was pushed
        if hasattr(request, "_sentry_scope"):
            try:
                request._sentry_scope.__exit__(None, None, None)
            except Exception:
                pass
        
        return response
    
    def process_exception(
        self, request: HttpRequest, exception: Exception
    ) -> HttpResponse | None:
        """
        Process exception.
        
        Args:
            request: Django HttpRequest
            exception: Exception that occurred
            
        Returns:
            None to continue processing
        """
        if not self.enabled:
            return None
            
        # Capture exception with additional context
        with self.sentry_sdk.push_scope() as scope:
            # Add exception-specific context
            scope.set_context("exception_details", {
                "type": type(exception).__name__,
                "module": type(exception).__module__,
                "request_path": request.path,
                "request_method": request.method,
            })
            
            # Capture the exception
            self.sentry_sdk.capture_exception(exception)
        
        # Mark transaction as failed
        if hasattr(request, "_sentry_transaction"):
            request._sentry_transaction.set_status("internal_error")
        
        return None
    
    def _get_transaction_name(self, request: HttpRequest) -> str:
        """
        Generate transaction name from request.
        
        Args:
            request: Django HttpRequest
            
        Returns:
            Transaction name
        """
        # Try to get from resolver
        if hasattr(request, "resolver_match") and request.resolver_match:
            if request.resolver_match.url_name:
                return f"{request.method} {request.resolver_match.url_name}"
            elif request.resolver_match.view_name:
                return f"{request.method} {request.resolver_match.view_name}"
        
        # Fallback to path
        return f"{request.method} {request.path}"
    
    def _get_client_ip(self, request: HttpRequest) -> str:
        """
        Get client IP address from request.
        
        Args:
            request: Django HttpRequest
            
        Returns:
            Client IP address
        """
        # Check for forwarded IP
        x_forwarded_for = request.META.get("HTTP_X_FORWARDED_FOR")
        if x_forwarded_for:
            return str(x_forwarded_for).split(",")[0].strip()
        
        # Check for real IP
        x_real_ip = request.META.get("HTTP_X_REAL_IP")
        if x_real_ip:
            return str(x_real_ip)
        
        # Fallback to remote addr
        return str(request.META.get("REMOTE_ADDR", ""))