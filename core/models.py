from typing import TYPE_CHECKING

from django.contrib.auth import get_user_model
from django.db import models
from django.utils import timezone

from core.logging import ContextLogger

logger = ContextLogger(__name__)

if TYPE_CHECKING:
    from django.contrib.auth.models import User
else:
    User = get_user_model()


class TimestampedModel(models.Model):
    """
    Абстрактная модель с полями created_at и updated_at.
    """
    created_at = models.DateTimeField(auto_now_add=True, db_index=True)
    updated_at = models.DateTimeField(auto_now=True, db_index=True)
    
    class Meta:
        abstract = True
        ordering = ["-created_at"]


class SoftDeleteModel(models.Model):
    """
    Абстрактная модель с мягким удалением.
    """
    is_deleted = models.BooleanField(default=False, db_index=True)
    deleted_at = models.DateTimeField(null=True, blank=True, db_index=True)
    deleted_by = models.ForeignKey(
        User, 
        on_delete=models.SET_NULL, 
        null=True, 
        blank=True,
        related_name="%(class)s_deleted_items"
    )
    
    class Meta:
        abstract = True
    
    def soft_delete(self, user: User | None = None):
        """Мягкое удаление объекта."""
        self.is_deleted = True
        self.deleted_at = timezone.now()
        self.deleted_by = user
        self.save(update_fields=["is_deleted", "deleted_at", "deleted_by"])
    
    def restore(self):
        """Восстановление удаленного объекта."""
        self.is_deleted = False
        self.deleted_at = None
        self.deleted_by = None
        self.save(update_fields=["is_deleted", "deleted_at", "deleted_by"])


class SocialMediaUser(TimestampedModel):
    """
    Абстрактная модель пользователя социальной сети.
    """
    external_id = models.CharField(max_length=255, db_index=True, default="")
    username = models.CharField(max_length=255, blank=True)
    full_name = models.CharField(max_length=255, blank=True)
    bio = models.TextField(blank=True)
    profile_pic_url = models.URLField(max_length=500, blank=True)
    is_verified = models.BooleanField(default=False)
    is_private = models.BooleanField(default=False)
    is_active = models.BooleanField(default=True)
    follower_count = models.IntegerField(default=0)
    following_count = models.IntegerField(default=0)
    post_count = models.IntegerField(default=0)
    
    # Дополнительные данные из API в JSON формате
    raw_data = models.JSONField(default=dict, blank=True)
    
    class Meta:
        abstract = True
        indexes = [
            models.Index(fields=["external_id"]),
            models.Index(fields=["username"]),
            models.Index(fields=["is_active", "created_at"]),
        ]
    
    def __str__(self):
        return f"{self.username or self.full_name or self.external_id}"


class SocialMediaPost(TimestampedModel, SoftDeleteModel):
    """
    Абстрактная модель поста в социальной сети.
    """
    external_id = models.CharField(max_length=255, db_index=True, blank=True, default="")
    content = models.TextField(blank=True)
    author = models.ForeignKey(
        "self",  # Будет переопределено в наследниках
        on_delete=models.CASCADE,
        related_name="%(class)s_posts",
        null=True,
        blank=True
    )
    
    # Метрики поста
    like_count = models.IntegerField(default=0)
    comment_count = models.IntegerField(default=0)
    share_count = models.IntegerField(default=0)
    view_count = models.IntegerField(null=True, blank=True)
    
    # Даты
    posted_at = models.DateTimeField(db_index=True)
    edited_at = models.DateTimeField(null=True, blank=True)
    
    # Тип поста
    POST_TYPE_CHOICES = [
        ("text", "Text"),
        ("photo", "Photo"),
        ("video", "Video"),
        ("album", "Album"),
        ("story", "Story"),
        ("reel", "Reel"),
        ("live", "Live"),
        ("other", "Other"),
    ]
    post_type = models.CharField(max_length=20, choices=POST_TYPE_CHOICES, default="text")
    
    # URL поста в социальной сети
    post_url = models.URLField(max_length=500, blank=True)
    
    # Дополнительные данные из API
    raw_data = models.JSONField(default=dict, blank=True)
    
    class Meta:
        abstract = True
        ordering = ["-posted_at"]
        indexes = [
            models.Index(fields=["external_id"]),
            models.Index(fields=["author", "posted_at"]),
            models.Index(fields=["post_type", "posted_at"]),
            models.Index(fields=["is_deleted", "posted_at"]),
        ]
    
    def __str__(self):
        return f"{self.post_type} by {self.author} at {self.posted_at}"


class SocialMediaComment(TimestampedModel):
    """
    Абстрактная модель комментария в социальной сети.
    """
    external_id = models.CharField(max_length=255, db_index=True, blank=True, default="")
    post = models.ForeignKey(
        "self",  # Будет переопределено в наследниках
        on_delete=models.CASCADE,
        related_name="%(class)s_comments",
        null=True,
        blank=True
    )
    author = models.ForeignKey(
        "self",  # Будет переопределено в наследниках
        on_delete=models.CASCADE,
        related_name="%(class)s_authored_comments",
        null=True,
        blank=True
    )
    content = models.TextField()
    
    # Родительский комментарий для ответов
    parent_comment = models.ForeignKey(
        "self",
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name="replies"
    )
    
    # Метрики
    like_count = models.IntegerField(default=0)
    reply_count = models.IntegerField(default=0)
    
    # Даты
    commented_at = models.DateTimeField(db_index=True)
    edited_at = models.DateTimeField(null=True, blank=True)
    
    # Дополнительные данные
    raw_data = models.JSONField(default=dict, blank=True)
    
    class Meta:
        abstract = True
        ordering = ["-commented_at"]
        indexes = [
            models.Index(fields=["post", "commented_at"]),
            models.Index(fields=["author", "commented_at"]),
            models.Index(fields=["parent_comment", "commented_at"]),
        ]
    
    def __str__(self):
        return f"Comment by {self.author} on {self.post}"


class SocialMediaMedia(TimestampedModel):
    """
    Абстрактная модель для медиафайлов (фото, видео).
    """
    MEDIA_TYPE_CHOICES = [
        ("photo", "Photo"),
        ("video", "Video"),
        ("audio", "Audio"),
        ("document", "Document"),
        ("gif", "GIF"),
        ("sticker", "Sticker"),
        ("other", "Other"),
    ]
    
    external_id = models.CharField(max_length=255, blank=True, db_index=True)
    media_type = models.CharField(max_length=20, choices=MEDIA_TYPE_CHOICES)
    
    # URL медиафайла
    media_url = models.URLField(max_length=2048)
    thumbnail_url = models.URLField(max_length=2048, blank=True)
    
    # Локальный путь к файлу (если загружен)
    local_file = models.FileField(upload_to="social_media/%Y/%m/%d/", null=True, blank=True)
    local_thumbnail = models.ImageField(upload_to="social_media/thumbnails/%Y/%m/%d/", null=True, blank=True)
    
    # Метаданные
    width = models.IntegerField(null=True, blank=True)
    height = models.IntegerField(null=True, blank=True)
    duration = models.IntegerField(null=True, blank=True, help_text="Duration in seconds")
    file_size = models.BigIntegerField(null=True, blank=True, help_text="File size in bytes")
    mime_type = models.CharField(max_length=100, blank=True)
    
    # Флаги
    is_downloaded = models.BooleanField(default=False)
    download_error = models.TextField(blank=True)
    
    # Дополнительные данные
    metadata = models.JSONField(default=dict, blank=True)
    
    class Meta:
        abstract = True
        indexes = [
            models.Index(fields=["media_type", "created_at"]),
            models.Index(fields=["is_downloaded", "created_at"]),
        ]
    
    def __str__(self):
        return f"{self.media_type}: {self.external_id or self.media_url[:50]}"


class SocialMediaHashtag(TimestampedModel):
    """
    Абстрактная модель для хештегов.
    """
    name = models.CharField(max_length=255, unique=True, db_index=True)
    normalized_name = models.CharField(max_length=255, db_index=True, default="")
    post_count = models.IntegerField(default=0)
    
    class Meta:
        abstract = True
        ordering = ["-post_count"]
    
    def save(self, *args, **kwargs):
        # Нормализуем имя хештега (убираем # и приводим к нижнему регистру)
        self.normalized_name = self.name.lstrip("#").lower()
        super().save(*args, **kwargs)
    
    def __str__(self):
        return f"#{self.name}"


class ImportTask(TimestampedModel):
    """
    Абстрактная модель для отслеживания задач импорта.
    """
    STATUS_CHOICES = [
        ("pending", "Pending"),
        ("running", "Running"),
        ("completed", "Completed"),
        ("failed", "Failed"),
        ("cancelled", "Cancelled"),
    ]
    
    task_id = models.CharField(max_length=255, unique=True, db_index=True, default="")
    task_type = models.CharField(max_length=50)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default="pending")
    
    # Параметры задачи
    parameters = models.JSONField(default=dict)
    
    # Прогресс
    total_items = models.IntegerField(default=0)
    processed_items = models.IntegerField(default=0)
    failed_items = models.IntegerField(default=0)
    
    # Временные метки
    started_at = models.DateTimeField(null=True, blank=True)
    completed_at = models.DateTimeField(null=True, blank=True)
    
    # Результаты и ошибки
    result = models.JSONField(default=dict, blank=True)
    error_message = models.TextField(blank=True)
    
    # Пользователь, запустивший задачу
    created_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="%(class)s_tasks"
    )
    
    class Meta:
        abstract = True
        ordering = ["-created_at"]
        indexes = [
            models.Index(fields=["status", "created_at"]),
            models.Index(fields=["task_type", "status"]),
        ]
    
    @property
    def progress_percentage(self) -> float:
        """Возвращает процент выполнения задачи."""
        if self.total_items == 0:
            return 0.0
        return (self.processed_items / self.total_items) * 100
    
    def save(self, *args, **kwargs):
        """Автоматически генерирует task_id если не задан."""
        if not self.task_id:
            import uuid
            # Генерируем уникальный task_id в формате: task_type_timestamp_uuid
            timestamp = timezone.now().strftime("%Y%m%d_%H%M%S")
            short_uuid = str(uuid.uuid4())[:8]
            self.task_id = f"{self.task_type}_{timestamp}_{short_uuid}"
        super().save(*args, **kwargs)
    
    def __str__(self):
        return f"{self.task_type} - {self.status} ({self.progress_percentage:.1f}%)"


class TaskResult(ImportTask):
    """
    Concrete model for storing task execution results.
    Extends the abstract ImportTask model with additional fields for async task management.
    """
    # Additional fields for real-time progress tracking
    progress_message = models.CharField(max_length=500, blank=True)
    retry_count = models.IntegerField(default=0)
    max_retries = models.IntegerField(default=3)
    
    # WebSocket/SSE channel for real-time updates
    channel_name = models.CharField(max_length=255, blank=True)
    
    # Task priority
    priority = models.IntegerField(default=0, help_text="Higher number = higher priority")
    
    # Parent task for subtasks
    parent_task = models.ForeignKey(
        "self",
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name="subtasks"
    )
    
    # Celery integration fields
    celery_task_id = models.CharField(
        max_length=255, 
        unique=True, 
        null=True, 
        blank=True,
        db_index=True,
        help_text="Celery AsyncResult ID"
    )
    celery_status = models.CharField(
        max_length=50, 
        null=True, 
        blank=True,
        help_text="Celery task status (PENDING, STARTED, SUCCESS, FAILURE, RETRY, REVOKED)"
    )
    celery_result = models.JSONField(
        default=dict, 
        blank=True,
        help_text="Raw result from Celery task"
    )

    class Meta:
        db_table = "core_taskresult"
        ordering = ["-priority", "-created_at"]
        indexes = [
            models.Index(fields=["status", "priority", "created_at"]),
            models.Index(fields=["channel_name", "status"]),
            models.Index(fields=["parent_task", "status"]),
        ]

    def update_progress(self, progress: int, message: str = ""):
        """Update task progress and message."""
        self.processed_items = progress
        self.progress_message = message
        self.save(update_fields=["processed_items", "progress_message", "updated_at"])
        
        # Emit progress signal for real-time updates
        # This can be consumed by WebSocket/SSE handlers
        from django.dispatch import Signal
        task_progress = Signal()
        task_progress.send(
            sender=self.__class__,
            task_id=self.task_id,
            progress=progress,
            message=message
        )
        
        from core.logging import ContextLogger
        logger = ContextLogger(__name__)
        logger.info(
            f"Task {self.task_id} progress: {progress}% - {message}",
            extra={
                "task_id": self.task_id,
                "progress": progress,
                "message": message
            }
        )

    def mark_as_running(self):
        """Mark task as running."""
        self.status = "running"
        self.started_at = timezone.now()
        self.save(update_fields=["status", "started_at", "updated_at"])

    def mark_as_completed(self, result: dict | None = None):
        """Mark task as completed."""
        self.status = "completed"
        self.completed_at = timezone.now()
        if result:
            self.result = result
        self.save(update_fields=["status", "completed_at", "result", "updated_at"])

    def mark_as_failed(self, error_message: str):
        """Mark task as failed."""
        self.status = "failed"
        self.completed_at = timezone.now()
        self.error_message = error_message
        self.save(update_fields=["status", "completed_at", "error_message", "updated_at"])

    def can_retry(self) -> bool:
        """Check if task can be retried."""
        return self.retry_count < self.max_retries

    def increment_retry(self):
        """Increment retry count."""
        self.retry_count += 1
        self.status = "retry"
        self.save(update_fields=["retry_count", "status", "updated_at"])
    
    def sync_with_celery(self):
        """Synchronize status with Celery task."""
        if not self.celery_task_id:
            return
        
        from celery.result import AsyncResult
        from django.conf import settings
        
        # In test mode with CELERY_TASK_ALWAYS_EAGER, we can't sync
        # because DisabledBackend doesn't support status tracking
        if getattr(settings, "CELERY_TASK_ALWAYS_EAGER", False):
            logger.debug(f"Skipping Celery sync for task {self.task_id} in eager mode")
            return
        
        try:
            result: AsyncResult = AsyncResult(self.celery_task_id)
            
            # Update Celery status
            self.celery_status = result.status
            
            # Map Celery status to our status
            status_mapping = {
                "PENDING": "pending",
                "STARTED": "running",
                "SUCCESS": "completed",
                "FAILURE": "failed",
                "RETRY": "retry",
                "REVOKED": "cancelled",
            }
            
            if result.status in status_mapping:
                self.status = status_mapping[result.status]
            
            # Save result if available
            if result.ready():
                try:
                    if result.successful():
                        # Get the actual result
                        task_result = result.result
                        if isinstance(task_result, dict):
                            self.celery_result = task_result
                            self.result = task_result
                    else:
                        # Get error info
                        self.celery_result = {"error": str(result.info)}
                        self.error_message = str(result.info)
                except Exception as e:
                    # Log but don't fail
                    logger.warning(f"Failed to get Celery result: {e}")
            
            # Update timestamps
            if result.status == "STARTED" and not self.started_at:
                self.started_at = timezone.now()
            elif result.status in ["SUCCESS", "FAILURE", "REVOKED"] and not self.completed_at:
                self.completed_at = timezone.now()
            
            self.save()
        except AttributeError as e:
            # Handle DisabledBackend or other backend issues
            logger.warning(f"Failed to sync with Celery for task {self.task_id}: {e}")
            # Don't fail, just skip sync
    
    def get_celery_result(self):
        """Get result from Celery task."""
        if not self.celery_task_id:
            return None
        
        from celery.result import AsyncResult
        return AsyncResult(self.celery_task_id)
    
    def revoke_celery_task(self, terminate=False):
        """Revoke/cancel Celery task."""
        if not self.celery_task_id:
            return False
        
        from celery.result import AsyncResult
        result: AsyncResult = AsyncResult(self.celery_task_id)
        result.revoke(terminate=terminate)
        
        self.status = "cancelled"
        self.celery_status = "REVOKED"
        self.save(update_fields=["status", "celery_status", "updated_at"])
        return True
