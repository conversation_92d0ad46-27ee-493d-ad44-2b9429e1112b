rbac:
  create: true

## Define serviceAccount names for components. Defaults to component's fully qualified name.
##
serviceAccounts:
  app:
    create: false
    name:
  accounts:
    create: false
    name:
  utils:
    create: false
    name:

app:
  ## If false, api will not be installed

  ##
  enabled: true

  ## api container name
  ##
  name: django
  fullnameOverride: django

  ## api container image
  ##
  image:
    repository: REPO_NAME
    tag: REPO_TAG
    pullPolicy: Always

#  ## Command instead entry point
#  command: {}
#
#  ## Args for entry point
#  args: []

  #  probes:
  #    enabled: true

  prefixURL: "/-"

  baseURL: "/"

  ## Additional api container environment variable
  ## For instance to add a http_proxy
  ##
  extraEnv:
    - name: "API_ID"
      value: "********"
    - name: "SESSION_NAME"
      value: "telegram_manager"
    - name: "OUTPUT_DIR"
      value: "examples"
    - name: "INSTAGRAM_RATE_LIMITS_ENABLED"
      value: "False"
    - name: "INSTAGRAM_COMMENTS_PER_HOUR"
      value: "999999"
    - name: "INSTAGRAM_COMMENTS_PER_DAY"
      value: "999999"
    - name: "INSTAGRAM_COMMENT_DELAY"
      value: "0"
    - name: "INSTAGRAM_TEST_USERNAME"
      value: "igorkishik"
    - name: "POSTGRES_DB"
      value: "ai"
    - name: "POSTGRES_HOST"
      value: "*************"
    - name: "POSTGRES_PORT"
      value: "5432"
    - name: "DJANGO_SETTINGS_MODULE"
      value: "SocialManager.settings.docker_dev"
    # - name: "DEBUG"
    #   value: "True"
#    - name: "ALLOWED_HOSTS"
#      value: "localhost,127.0.0.1"
    - name: "CELERY_BROKER_URL"
      value: "redis://*************:6379/0"
    - name: "CELERY_RESULT_BACKEND"
      value: "redis://*************:6379/1"
    - name: "REDIS_URL"
      value: "redis://*************:6379/2"
#    - name: "DJANGO_ENV"
#      value: "development"
    - name: "GCS_BUCKET_NAME"
      values: "754872181769-community-prod"
    - name: "GOOGLE_APPLICATION_CREDENTIALS"
      value: "/app/auth_cred.json"
    - name: "SENTRY_DSN"
      value: "https://<EMAIL>/4509667853926400"
    - name: "ALLOWED_HOSTS"
      value: "socialmanager.ot4.dev,localhost,*********,127.0.0.1,*"
    - name: "DEBUG"
      value: "false"
    - name: "DJANGO_ENV"
      value: "production"
    - name: "LOG_DIR"
      value: "/app/logs"
    - name: "EMAIL_HOST"
      value: "smtp.example.com"
    - name: "EMAIL_HOST_USER"
      value: "foo"
    - name: "EMAIL_HOST_PASSWORD"
      value: "bar"

  ## Container port
  containerPort: "8000"

  ## Port name for probes and service
  containerPortName: api-port

  ingress:
    enabled: true

    annotations:
      kubernetes.io/ingress.class: nginx
      nginx.ingress.kubernetes.io/ssl-redirect: "false"
      nginx.ingress.kubernetes.io/proxy-body-size: "50m"
      nginx.ingress.kubernetes.io/server-snippet: |
        proxy_set_header Host $host;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    #      nginx.ingress.kubernetes.io/configuration-snippet: |
    #        rewrite /api/(.*)$ /$1 break;

    paths:
      - "/"

    # TODO: hosts must be set from terraform
    hosts:
      - "socialmanager.ot4.dev"

    #    hostsWithPathes:
    #      - host: "argo.ot4.dev"
    #        paths:
    #          - "/api/"


    # TODO: secret name and hosts must be set from terraform
    tls: []
    #  - secretName: "argomedia-co"
    #    hosts:
    #      - "argo-api.ot4.dev"

  ## Annotations to be added to api pods
  ##
  podAnnotations:
    prometheus.io/path: /metrics
    prometheus.io/port: "8080"
    prometheus.io/scrape: "true"

  ## api resource requests and limits
  ## Ref: http://kubernetes.io/docs/user-guide/compute-resources/
  ##
  resources:
    limits:
      cpu: 1
      memory: 2Gi
    requests:
      cpu: 1
      memory: 2Gi

  ## Horizontal Pod Autoscaler settings
  #
  #  hpa:
  #    minReplicas: 1
  #    maxReplicas: 10
  #    resourceName: targetCPUUtilizationPercentage
  #    averageUtilization: 70

  service:
    annotations:
      "helm.sh/hook-delete-policy": before-hook-creation
    labels: { }

    ports:
      - name: api-external
        port: "80"
        targetPort: api-port

      - name: ssl-external
        port: "443"
        targetPort: api-port

    servicePort: api-external
    type: NodePort
secondPartyApp:
  enabled: true
  apps:
    - name: mcp-server
      enabled: true
      fullnameOverride: mcp-server
      probes:
        enabled: false

      ## api container image
      ##
      image:
        repository: REPO_NAME
        tag: REPO_TAG-mcp
        pullPolicy: Always


#      ## Command instead entry point
#      command: {}
#
#      ## Args for entry point
#      args: []


      prefixURL: "/-"

      ## External URL which can access api
      ## Maybe same with Ingress host name
      baseURL: "/"

      extraEnv:
        - name: "MCP_HOST"
          value: "0.0.0.0"
        - name: "MCP_PORT"
          value: "8080"
        - name: "MCP_LOG_LEVEL"
          value: "INFO"
        - name: "DJANGO_SETTINGS_MODULE"
          value: "SocialManager.settings.docker_dev"
        - name: "ALLOWED_HOSTS"
          value: "socialmanager.ot4.dev,localhost,*********,127.0.0.1,*"
        - name: "DEBUG"
          value: "True"
        - name: "MCP_REDIS_URL"
          value: "redis://*************:6379/2"
        - name: "SENTRY_DSN"
          value: "https://<EMAIL>/4509667853926400"

      ## Container port
      containerPort: "8001"

      ## Port name for probes and service
      containerPortName: api-port
      ingress:
        enabled: true

        annotations:
          kubernetes.io/ingress.class: nginx
          #          cert-manager.io/cluster-issuer: letsencrypt
          #          nginx.ingress.kubernetes.io/ssl-passthrough: "true"
          nginx.ingress.kubernetes.io/ssl-redirect: "false"
          nginx.ingress.kubernetes.io/proxy-body-size: "50m"
          nginx.ingress.kubernetes.io/server-snippet: |
            proxy_set_header Host $host;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
          # nginx.ingress.kubernetes.io/configuration-snippet: |
          #   rewrite /djad/(.*)$ /$1 break;

        paths:
          - "/mcp/"

        hosts:
          - "socialmanager.ot4.dev"

      ## Annotations to be added to api pods
      ##
      podAnnotations: { }
      #        prometheus.io/path: /-/metrics
      #        prometheus.io/port: "8080"
      #        prometheus.io/scrape: "true"

      ## api resource requests and limits
      ## Ref: http://kubernetes.io/docs/user-guide/compute-resources/
      ##
      resources:
        limits:
          cpu: 1
          memory: 2Gi
        requests:
          cpu: 1
          memory: 2Gi

      service:
        annotations:
          "helm.sh/hook-delete-policy": before-hook-creation
        labels: { }

        ports:
          - name: api-external
            port: "80"
            targetPort: api-port

          - name: ssl-external
            port: "443"
            targetPort: api-port


        servicePort: api-external
        type: NodePort
    - name: celery
      enabled: true
      fullnameOverride: celery
      probes:
        enabled: false

      ## api container image
      ##
      image:
        repository: REPO_NAME
        tag: REPO_TAG-celery
        pullPolicy: Always


      #      ## Command instead entry point
      #      command: {}
      #
      #      ## Args for entry point
      #      args: []


      prefixURL: "/-"

      ## External URL which can access api
      ## Maybe same with Ingress host name
      baseURL: "/"

      extraEnv:
        - name: "DJANGO_SETTINGS_MODULE"
          value: "SocialManager.settings.docker_dev"
        - name: "DEBUG"
          value: "True"
        - name: "POSTGRES_HOST"
          value: "*************"
        - name: "POSTGRES_DB"
          value: "ai"
        - name: "POSTGRES_PORT"
          value: "5432"
        - name: "CELERY_BROKER_URL"
          value: "redis://*************:6379/0"
        - name: "CELERY_RESULT_BACKEND"
          value: "redis://*************:6379/1"
        - name: "SENTRY_DSN"
          value: "https://<EMAIL>/4509667853926400"

      ## Container port
      containerPort: "5555"

      ## Port name for probes and service
      containerPortName: api-port

      ## Annotations to be added to api pods
      ##
      podAnnotations: {}
      #        prometheus.io/path: /-/metrics
      #        prometheus.io/port: "8080"
      #        prometheus.io/scrape: "true"

      ## api resource requests and limits
      ## Ref: http://kubernetes.io/docs/user-guide/compute-resources/
      ##
      resources:
        limits:
          cpu: 1
          memory: 2Gi
        requests:
          cpu: 1
          memory: 2Gi

      service:
        annotations:
          "helm.sh/hook-delete-policy": before-hook-creation
        labels: {}

        ports:
          - name: api-external
            port: "80"
            targetPort: api-port

          - name: ssl-external
            port: "443"
            targetPort: api-port


        servicePort: api-external
        type: NodePort
