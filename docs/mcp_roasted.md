# Архитектурный план: Instagram Profile Roast Tools

## Обзор решения

Добавление новых tools в MCP server для анализа Instagram профилей с загрузкой фотографий в Google Cloud Storage. Решение следует существующей архитектуре проекта с асинхронными задачами через Celery.

## Структура файлов

```
mcp_server/
├── tools/
│   └── instagram_roast.py          # Новый файл с roast tools
├── validators/
│   └── roast.py                    # Новый файл с валидаторами
└── task_registry.py                # Обновить для регистрации новой задачи

instagram_manager/
├── tasks/
│   └── roast_tasks.py              # Новый файл с RoastProfileTask
└── services/
    └── roast_service.py            # Новый файл с бизнес-логикой
```

## Основные компоненты

### 1. MCP Tools (`mcp_server/tools/instagram_roast.py`)

```python
@mcp.tool()
@handle_tool_errors
async def roast_instagram_profile(
    profile_url: str,
    post_count: int = 10,
    priority: str = "normal"
) -> dict[str, Any]:
    """
    Создает задачу для анализа Instagram профиля
    
    Args:
        profile_url: URL профиля Instagram
        post_count: Количество постов для анализа
        priority: Приоритет задачи (low, normal, high)
        
    Returns:
        dict с task_id и информацией о задаче
    """
    # 1. Валидация URL и извлечение username
    # 2. Создание задачи через task_create_import механизм
    # 3. Возврат task_id для отслеживания

@mcp.tool()
@handle_tool_errors
async def roast_get_result(
    task_id: str
) -> dict[str, Any]:
    """
    Получает результат roast анализа
    
    Args:
        task_id: ID задачи
        
    Returns:
        dict с profile_url и массивом GCS URLs
    """
    # 1. Проверка статуса задачи
    # 2. Если completed - возврат результата
    # 3. Если pending/running - информация о прогрессе
```

### 2. Асинхронная задача (`instagram_manager/tasks/roast_tasks.py`)

```python
from core.tasks.base import BaseTask
from core.tasks.decorators import task
from instagram_manager.services.roast_service import RoastService

@task(monitor=True)
class RoastProfileTask(BaseTask):
    """Задача для анализа Instagram профиля и загрузки фото в GCS"""
    
    task_type: ClassVar[str] = "instagram.roast_profile"
    description: ClassVar[str] = "Analyze Instagram profile and upload photos"
    max_retries: ClassVar[int] = 3
    
    def validate_params(self, **kwargs) -> dict[str, Any]:
        """Валидация параметров задачи"""
        # Валидация profile_url и post_count
        # Извлечение username из URL
        
    def execute_task(
        self,
        username: str,
        post_count: int,
        profile_url: str,
        **kwargs
    ) -> dict[str, Any]:
        """
        Основная логика выполнения:
        1. Проверка наличия профиля в БД
        2. Импорт профиля из BrightData если нужно
        3. Проверка наличия достаточного количества постов с фото
        4. Импорт постов из BrightData если нужно
        5. Фильтрация постов (только с фото)
        6. Загрузка медиа файлов в GCS
        7. Формирование и сохранение результата
        """
        service = RoastService()
        
        # Подготовка данных
        self.update_progress(10, "Checking profile and posts...")
        profile, posts = service.prepare_roast_data(username, post_count)
        
        # Загрузка фото в GCS
        self.update_progress(50, "Uploading photos to GCS...")
        gcs_urls = service.upload_photos_to_gcs(posts)
        
        # Формирование результата
        self.update_progress(90, "Preparing results...")
        result = {
            "profile_url": profile_url,
            "username": username,
            "post_count": len(posts),
            "media_urls": gcs_urls,
            "statistics": {
                "total_posts_analyzed": len(posts),
                "photos_found": len(gcs_urls),
                "photos_uploaded": len(gcs_urls)
            }
        }
        
        return result
```

### 3. Сервис (`instagram_manager/services/roast_service.py`)

```python
from instagram_manager.repositories import ProfileRepository, PostRepository
from instagram_manager.services import ImportService
from instagram_manager.instagram_api.data_handlers.media_handler import MediaHandler

class RoastService:
    """Сервис для подготовки данных для roast анализа"""
    
    def __init__(self):
        self.profile_repo = ProfileRepository()
        self.post_repo = PostRepository()
        self.import_service = ImportService()
        self.media_handler = MediaHandler(save_to_gcs=True)
    
    def prepare_roast_data(
        self, 
        username: str, 
        post_count: int
    ) -> tuple[InstagramProfile, list[InstagramPost]]:
        """
        Подготавливает данные для анализа:
        - Проверяет наличие профиля
        - Импортирует профиль если нужно
        - Проверяет наличие постов с фото
        - Импортирует посты если недостаточно
        """
        # 1. Проверка профиля
        profile = self.profile_repo.get_by_username(username)
        if not profile:
            # Импорт профиля через ImportInstagramProfileTask
            profile = self._import_profile(username)
        
        # 2. Получение постов с фото
        posts = self._get_photo_posts(profile, post_count)
        
        # 3. Если недостаточно постов - импортировать
        if len(posts) < post_count:
            posts = self._import_posts(profile, post_count)
        
        return profile, posts[:post_count]
    
    def upload_photos_to_gcs(
        self, 
        posts: list[InstagramPost]
    ) -> list[str]:
        """
        Загружает фотографии из постов в GCS
        Возвращает список GCS URLs
        """
        gcs_urls = []
        
        for post in posts:
            for media in post.media.filter(media_type="photo"):
                if not media.gcs_url:
                    # Загрузка через MediaHandler
                    success = self.media_handler.download_media(media)
                    if success and media.gcs_url:
                        gcs_urls.append(media.gcs_url)
                else:
                    gcs_urls.append(media.gcs_url)
        
        return gcs_urls
    
    def _import_profile(self, username: str) -> InstagramProfile:
        """Импортирует профиль через существующий механизм"""
        # Использует ImportInstagramProfileTask
        
    def _get_photo_posts(
        self, 
        profile: InstagramProfile, 
        limit: int
    ) -> list[InstagramPost]:
        """Получает посты с фотографиями"""
        return list(
            InstagramPost.objects.filter(
                profile=profile,
                media__media_type="photo"
            ).distinct()
            .prefetch_related("media")
            .order_by("-posted_at")[:limit * 2]  # Берем с запасом
        )
    
    def _import_posts(
        self, 
        profile: InstagramProfile, 
        needed_count: int
    ) -> list[InstagramPost]:
        """Импортирует дополнительные посты"""
        # Использует ImportInstagramPostsTask
        # с фильтрацией по типу media
```

### 4. Валидаторы (`mcp_server/validators/roast.py`)

```python
from pydantic import Field, field_validator
from .base import BaseTaskParams

class RoastProfileTaskParams(BaseTaskParams):
    """Параметры для roast профиля"""
    profile_url: str = Field(
        ..., 
        pattern=r"^https://www\.instagram\.com/[a-zA-Z0-9_.]+/?$",
        description="Instagram profile URL"
    )
    post_count: int = Field(
        default=10, 
        ge=1, 
        le=100,
        description="Number of posts to analyze"
    )
    
    @field_validator("profile_url")
    @classmethod
    def validate_and_extract_username(cls, v: str) -> str:
        """Валидирует URL и извлекает username"""
        # Проверка формата URL
        # Извлечение username из URL
        return v
    
    def extract_username(self) -> str:
        """Извлекает username из URL"""
        import re
        match = re.search(r"instagram\.com/([a-zA-Z0-9_.]+)", self.profile_url)
        if match:
            return match.group(1).lower()
        raise ValueError("Invalid Instagram URL")
```

## Интеграция с существующими компонентами

### 1. Обновление `task_registry.py`

```python
from instagram_manager.tasks.roast_tasks import RoastProfileTask

TASK_REGISTRY["instagram.roast_profile"] = RoastProfileTask

TASK_DESCRIPTIONS["instagram.roast_profile"] = "Analyze Instagram profile and upload photos to GCS"
```

### 2. Обновление валидаторов в `validators/tasks.py`

```python
# Добавить в valid_types в TaskCreateParams
valid_types = [
    # ... existing types ...
    "instagram.roast_profile",
]

# Добавить в TASK_PARAM_VALIDATORS
from .roast import RoastProfileTaskParams

TASK_PARAM_VALIDATORS["instagram.roast_profile"] = RoastProfileTaskParams
```

## Workflow диаграмма

```mermaid
graph TD
    A[roast_instagram_profile] --> B[Валидация URL]
    B --> C[Создание задачи]
    C --> D[Возврат task_id]
    
    E[RoastProfileTask.execute] --> F{Профиль в БД?}
    F -->|Нет| G[ImportInstagramProfileTask]
    F -->|Да| H{Достаточно постов?}
    G --> H
    H -->|Нет| I[ImportInstagramPostsTask]
    H -->|Да| J[Фильтрация фото]
    I --> J
    J --> K[Загрузка в GCS]
    K --> L[Сохранение результата]
    
    M[roast_get_result] --> N{Статус задачи}
    N -->|completed| O[Возврат результата]
    N -->|running| P[Возврат прогресса]
    N -->|failed| Q[Возврат ошибки]
```

## Формат результата

```json
{
  "task_id": "uuid-here",
  "status": "completed",
  "result": {
    "profile_url": "https://www.instagram.com/username/",
    "username": "username",
    "post_count": 10,
    "media_urls": [
      "https://storage.googleapis.com/bucket-name/images/post1_photo1.jpg",
      "https://storage.googleapis.com/bucket-name/images/post2_photo1.jpg",
      "https://storage.googleapis.com/bucket-name/images/post3_photo1.jpg"
    ],
    "statistics": {
      "total_posts_analyzed": 10,
      "photos_found": 15,
      "photos_uploaded": 15
    }
  }
}
```

## Обработка ошибок

1. **Валидация**:
   - Некорректный URL профиля
   - Превышение лимита post_count
   - Несуществующий username

2. **Импорт данных**:
   - Недоступность BrightData API
   - Приватный профиль
   - Отсутствие постов

3. **Загрузка в GCS**:
   - Ошибки аутентификации GCS
   - Недоступность медиа URL
   - Fallback на локальное хранилище

4. **Retry механизм**:
   - Автоматический повтор при временных ошибках
   - Экспоненциальная задержка между попытками
   - Максимум 3 попытки

## Преимущества архитектуры

1. **Соответствие существующим паттернам**: Использует те же подходы, что и другие части системы
2. **Переиспользование компонентов**: MediaHandler, ImportService, репозитории
3. **Асинхронность**: Не блокирует MCP server во время выполнения
4. **Масштабируемость**: Через Celery можно обрабатывать множество запросов параллельно
5. **Отслеживание прогресса**: Через TaskResult и update_progress
6. **Надежность**: Retry механизм и обработка ошибок
7. **Гибкость**: Легко расширить для дополнительной функциональности

## Следующие шаги

1. Создать файлы согласно структуре
2. Реализовать компоненты начиная с валидаторов
3. Интегрировать с существующей системой
4. Написать тесты для новой функциональности
5. Протестировать загрузку в GCS
6. Добавить документацию в README