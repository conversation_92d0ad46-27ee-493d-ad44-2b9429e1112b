# MCP Inspector Connection Guide

## Server Status ✅
The MCP server is now properly configured and running:
- Container: **healthy**
- URL: `http://localhost:8001/mcp`
- Health endpoint: `http://localhost:8001/health`

## How to Connect with MCP Inspector

1. **Open MCP Inspector**
   ```bash
   npx @modelcontextprotocol/inspector
   ```
   Or if you have it installed globally:
   ```bash
   mcp-inspector
   ```

2. **Connect to the Server**
   - In the Inspector UI (http://localhost:5173/)
   - Click "Connect to Server"
   - Enter the connection URL: `http://localhost:8001/mcp`
   - Select "Streamable HTTP" as the transport type

3. **Available Tools**
   The server provides 42+ tools for Instagram and Telegram management, including:
   - Instagram profile operations
   - Instagram post and comment management
   - Telegram chat and message operations
   - Task management and monitoring

## Troubleshooting

If you still cannot connect:

1. **Verify the server is running:**
   ```bash
   docker ps | grep mcp-server
   ```
   Should show status as "(healthy)"

2. **Check server logs:**
   ```bash
   docker logs socialmanager-mcp-server-1 --tail 50
   ```

3. **Test direct connection:**
   ```bash
   curl http://localhost:8001/health
   ```
   Should return: `{"status":"healthy","service":"SocialManager MCP"}`

## What Changed

1. Fixed health endpoint to return JSON instead of plain text
2. Added CORS middleware for browser-based Inspector access
3. Changed MCP path from "/" to "/mcp" to avoid conflicts
4. Updated Docker healthcheck to use proper Accept headers

The server is now fully compatible with MCP Inspector!