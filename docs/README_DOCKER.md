# Docker Infrastructure Setup

This guide explains how to run the SocialManager project with Docker infrastructure while keeping Django running locally for easy development and debugging.

## Architecture

- **Django**: Runs locally on your machine (for easy debugging)
- **PostgreSQL**: Runs in Docker (latest version)
- **Redis**: Runs in Docker (latest version, used for cache and Celery broker)
- **Celery Worker**: Runs in Docker (processes background tasks)
- **Celery Beat**: Runs in Docker (schedules periodic tasks)
- **Flower**: Runs in Docker (monitors Celery tasks)

## Prerequisites

- Docker and Docker Compose installed
- Python 3.13 installed locally
- `uv` package manager installed

## Quick Start

### 1. Start Docker Infrastructure

Start all infrastructure services with a single command:

```bash
docker-compose up -d
```

This starts:
- PostgreSQL on port 5432
- Redis on port 6379
- Celery worker
- Celery beat scheduler
- Flower monitoring UI on port 5555

### 2. Set Up Local Django

```bash
# Copy environment variables
cp .env.example .env

# Install dependencies
uv sync

# Apply database migrations
uv run python manage.py migrate

# Create superuser
uv run python manage.py createsuperuser

# Run Django development server
uv run python manage.py runserver
```

## Accessing Services

- **Django**: http://localhost:8000
- **Django Admin**: http://localhost:8000/admin
- **Flower (Celery monitoring)**: http://localhost:5555
- **PostgreSQL**: localhost:5432 (database: socialmanager, user: postgres, password: postgres)
- **Redis**: localhost:6379

## Using Celery Tasks

### Running Tasks Asynchronously

All BaseTask subclasses can be run asynchronously:

```python
# In Django shell
from instagram_manager.tasks import ImportInstagramProfileTask

# Synchronous execution (immediate)
task = ImportInstagramProfileTask()
result = task.execute(username="instagram")

# Asynchronous execution (via Celery)
task = ImportInstagramProfileTask()
async_result = task.run_async(username="instagram")

# Check task status
async_result.status  # 'PENDING', 'STARTED', 'SUCCESS', 'FAILURE'
async_result.result  # Task result when completed
```

### Using Celery Tasks Directly

```python
# Import profile asynchronously
from instagram_manager.tasks import import_profile_async
result = import_profile_async.delay(username="instagram")

# Import posts for a profile
from instagram_manager.tasks import import_posts_async
result = import_posts_async.delay(profile_id=1)

# Download media
from instagram_manager.tasks import download_media_async
result = download_media_async.delay(media_id=1)
```

### Monitoring Tasks

1. **Via Flower UI**: http://localhost:5555
   - Real-time task monitoring
   - Worker status
   - Task history and results

2. **Via Django Admin**: http://localhost:8000/admin/core/taskresult/
   - Task results stored in database
   - Progress tracking
   - Error logs

3. **Via Management Command**:
   ```bash
   uv run python manage.py task_status
   ```

## Periodic Tasks

Celery Beat automatically runs periodic tasks defined in settings:

- **Clean old tasks**: Runs weekly, removes TaskResult entries older than 30 days
- **Update active profiles**: Can be configured to run periodically

## Management Commands

### Infrastructure Management

```bash
# Start all services
docker-compose up -d

# Stop all services
docker-compose down

# View logs
docker-compose logs -f

# View specific service logs
docker-compose logs -f celery
docker-compose logs -f db

# Restart a service
docker-compose restart celery
```

### Database Operations

```bash
# Connect to PostgreSQL
docker-compose exec db psql -U postgres socialmanager

# Backup database
docker-compose exec db pg_dump -U postgres socialmanager > backup.sql

# Restore database
docker-compose exec db psql -U postgres socialmanager < backup.sql
```

### Redis Operations

```bash
# Connect to Redis CLI
docker-compose exec redis redis-cli

# Monitor Redis commands
docker-compose exec redis redis-cli monitor

# Flush all data (CAREFUL!)
docker-compose exec redis redis-cli FLUSHALL
```

## Development Workflow

1. **Start infrastructure**: `docker-compose up -d`
2. **Run Django locally**: `uv run python manage.py runserver`
3. **Make code changes** - Django auto-reloads
4. **Run tests**: `uv run pytest`
5. **Check Celery tasks**: http://localhost:5555

## Troubleshooting

### PostgreSQL Connection Issues

If Django can't connect to PostgreSQL:
```bash
# Check if PostgreSQL is running
docker-compose ps db

# Check PostgreSQL logs
docker-compose logs db

# Ensure .env has correct settings
POSTGRES_HOST=localhost  # Not 'db' since Django runs outside Docker
```

### Celery Not Processing Tasks

```bash
# Check Celery worker logs
docker-compose logs celery

# Restart Celery worker
docker-compose restart celery

# Check Redis connectivity
docker-compose exec redis redis-cli ping
```

### Port Conflicts

If ports are already in use:
```bash
# Check what's using the ports
lsof -i :5432  # PostgreSQL
lsof -i :6379  # Redis
lsof -i :5555  # Flower

# Stop conflicting services or change ports in docker-compose.yml
```

## Production Considerations

For production deployment:

1. Use environment-specific .env files
2. Set strong passwords for PostgreSQL and Redis
3. Use Redis authentication
4. Configure proper resource limits in docker-compose.yml
5. Set up database backups
6. Use a reverse proxy (nginx) for Django
7. Enable SSL/TLS
8. Set DEBUG=False in Django settings

## Useful Resources

- [Docker Compose documentation](https://docs.docker.com/compose/)
- [Celery documentation](https://docs.celeryproject.org/)
- [PostgreSQL documentation](https://www.postgresql.org/docs/)
- [Redis documentation](https://redis.io/documentation)
- [Flower documentation](https://flower.readthedocs.io/)