# Google Cloud Storage Setup Guide

## 📋 Task 001: GCS Infrastructure Setup

Эта инструкция поможет настроить Google Cloud Storage для хранения медиа файлов из Instagram.

### Шаг 1: Создание GCS Bucket

1. **Перейдите в Google Cloud Console:**
   ```
   https://console.cloud.google.com/storage
   ```

2. **Создайте новый bucket:**
   - Нажмите "CREATE BUCKET"
   - **Name:** `socialmanager-media-prod` (или другое уникальное имя)
   - **Location type:** Multi-region
   - **Location:** us (или ближайший регион)
   - **Storage class:** Standard
   - **Access control:** Uniform
   - **Protection tools:** оставить по умолчанию

3. **Сделайте bucket публичным (для MVP):**
   - Перейдите во вкладку Permissions
   - Нажмите "ADD PRINCIPAL"
   - **New principals:** allUsers
   - **Role:** Storage Object Viewer
   - Нажмите "Save"

### Шаг 2: Создание Service Account

1. **Перейдите в IAM & Admin:**
   ```
   https://console.cloud.google.com/iam-admin/serviceaccounts
   ```

2. **Создайте новый service account:**
   - Нажмите "CREATE SERVICE ACCOUNT"
   - **Service account name:** socialmanager-gcs-uploader
   - **Service account ID:** будет сгенерирован автоматически
   - **Description:** Service account for uploading media files to GCS
   - Нажмите "CREATE AND CONTINUE"

3. **Назначьте роль:**
   - В поле "Select a role" найдите и выберите: **Storage Object Creator**
   - Нажмите "CONTINUE"
   - Нажмите "DONE"

4. **Создайте и скачайте ключ:**
   - Найдите созданный service account в списке
   - Нажмите на email service account
   - Перейдите на вкладку "KEYS"
   - Нажмите "ADD KEY" → "Create new key"
   - Выберите "JSON"
   - Нажмите "CREATE"
   - Файл автоматически скачается

### Шаг 3: Настройка локального окружения

1. **Создайте директорию для секретов:**
   ```bash
   mkdir -p ~/socialmanager/secrets/
   ```

2. **Переместите скачанный ключ:**
   ```bash
   # Замените имя файла на реальное
   mv ~/Downloads/socialmanager-*.json ~/socialmanager/secrets/gcs-key.json
   
   # Установите безопасные права
   chmod 600 ~/socialmanager/secrets/gcs-key.json
   ```

3. **Добавьте в .env файл:**
   ```bash
   # Откройте .env файл
   # Добавьте следующие строки:
   
   # Google Cloud Storage Configuration
   GCS_BUCKET_NAME=socialmanager-media-prod
   GOOGLE_APPLICATION_CREDENTIALS=/Users/<USER>/socialmanager/secrets/gcs-key.json
   ```
   
   ⚠️ **Важно:** Замените `YOUR_USERNAME` на ваше реальное имя пользователя!

### Шаг 4: Проверка настройки

1. **Запустите тестовый скрипт:**
   ```bash
   python scripts/test_gcs_access.py
   ```

2. **Если все настроено правильно, вы увидите:**
   ```
   ✓ Bucket name: socialmanager-media-prod
   ✓ Credentials path: /Users/<USER>/socialmanager/secrets/gcs-key.json
   ✓ Successfully connected to bucket: socialmanager-media-prod
   ✓ Successfully uploaded test file
   ✓ Successfully read back test content
   ✓ Successfully deleted test file
   
   ✅ All tests passed! GCS is properly configured.
   🎉 GCS is ready to use!
   ```

### 🚨 Важные моменты безопасности

1. **НИКОГДА не коммитьте JSON ключ в git!**
2. Убедитесь, что `.gitignore` содержит:
   ```
   *.json
   secrets/
   ```

3. Храните ключ в безопасном месте
4. Service account имеет только права на создание объектов (не может удалять)

### ❓ Возможные проблемы

1. **"Bucket name already exists"** - выберите другое уникальное имя
2. **"Permission denied"** - проверьте, что service account имеет роль Storage Object Creator
3. **"File not found"** - проверьте путь к JSON ключу в .env

### ✅ Чек-лист

- [ ] GCS bucket создан
- [ ] Bucket сделан публичным для чтения
- [ ] Service account создан с правами Storage Object Creator
- [ ] JSON ключ скачан и сохранен в безопасном месте
- [ ] Переменные добавлены в .env
- [ ] Тестовый скрипт выполнен успешно

После выполнения всех шагов, Task 001 будет завершена!