# Контекст проекта SocialManager

**Последнее обновление**: 2025-01-16  
**Версия**: 1.6

## Описание проекта

SocialManager - это Django-приложение для управления интеграциями с социальными сетями, в первую очередь Telegram и Instagram. Проект предоставляет инструменты для импорта данных, автоматизации взаимодействий и аналитики социальных медиа.

## Технологический стек

### Backend
- **Python 3.13+**
- **Django 5.2** - основной веб-фреймворк
- **Django REST Framework** - для построения API
- **Celery 5.3** - асинхронная обработка задач
- **Redis** - брокер сообщений для Celery и кеширование
- **PostgreSQL** - основная база данных

### Ключевые библиотеки
- **Pydantic 2.5** - валидация данных и схемы
- **Telethon** - интеграция с Telegram API
- **Pillow** - обработка изображений
- **python-decouple** - управление конфигурацией
- **uv** - управление пакетами и виртуальным окружением
- **google-cloud-storage** - интеграция с GCS для хранения медиафайлов
- **structlog** - структурированное логирование
- **sentry-sdk** - интеграция с Sentry для мониторинга ошибок
- **python-json-logger** - JSON форматирование логов

### Инструменты разработки
- **pytest** - тестирование
- **black** - форматирование кода
- **ruff** - линтер
- **mypy** - статическая типизация

## Архитектура

### Паттерны и принципы
1. **Repository Pattern** - весь доступ к данным через репозитории
2. **Service Layer** - бизнес-логика изолирована в сервисах
3. **Pydantic Validation** - все внешние данные валидируются
4. **Async Tasks** - долгие операции выполняются через Celery
5. **Transaction Management** - критические операции в транзакциях

### Структура проекта
```
SocialManager/
├── telegram_manager/          # Telegram интеграция
│   ├── models.py             # TelegramChat, TelegramUser, TelegramMessage
│   ├── repositories/         # ChatRepository, UserRepository, MessageRepository
│   ├── services/            # TelegramService, MessageImportService
│   ├── tasks/               # Celery задачи для импорта
│   ├── schemas/             # Pydantic схемы для валидации
│   └── telegram_api/        # Telethon интеграция
│
├── instagram_manager/         # Instagram интеграция
│   ├── models.py            # Profile, Post, Media, Comment, Hashtag, etc.
│   ├── repositories/        # Репозитории для каждой модели
│   ├── services/           # ProfileService, PostService, CommentService, etc.
│   ├── tasks/              # ImportProfileTask, ImportPostsTask, etc.
│   ├── schemas/            # Pydantic схемы для BrightData API
│   └── instagram_api/      # BrightDataClient и обработчики
│
├── core/                     # Общие компоненты
│   ├── models/              # Базовые модели (TimestampedModel, SoftDeleteModel)
│   ├── repositories/        # BaseRepository
│   ├── services/           # BaseService
│   ├── tasks/              # BaseTask для Celery
│   ├── decorators/         # @validate_input, @require_permission, etc.
│   ├── forms/              # Валидаторы и миксины для форм
│   ├── storage/            # Сервисы хранения (GCS)
│   ├── logging/            # Система логирования с Sentry интеграцией
│   │   ├── config.py       # Централизованная Django LOGGING конфигурация
│   │   ├── context.py      # Context management с contextvars
│   │   ├── handlers.py     # SentryHandler для отправки логов в Sentry
│   │   ├── filters.py      # SensitiveDataFilter, RateLimitFilter
│   │   ├── processors.py   # Structlog процессоры
│   │   ├── sentry_config.py # Класс SentryConfig для централизованной настройки
│   │   ├── celery_handlers.py # Celery signal handlers для контекста
│   │   └── testing.py      # Утилиты для тестирования логов
│   ├── middleware/         # Django middleware
│   │   └── sentry_middleware.py # SentryContextMiddleware для обогащения контекста
│   ├── utils/              # Утилиты проекта
│   │   └── decorators/     # Декораторы
│   │       └── sentry_decorators.py # @capture_errors, @track_transaction, etc.
│   ├── management/         # Management команды
│   │   └── commands/       # Django команды
│   │       └── test_sentry.py # Команда для тестирования Sentry интеграции
│   └── exceptions.py       # Кастомные исключения
│
└── SocialManager/           # Настройки Django
    ├── settings/          # Конфигурационные файлы
    │   ├── base.py        # Базовые настройки
    │   ├── local.py       # Локальная разработка
    │   ├── production.py  # Продакшн окружение
    │   ├── test.py        # Тестовое окружение
    │   └── sentry.py      # Настройки Sentry по окружениям
    ├── urls.py            # URL маршруты
    └── celery.py          # Конфигурация Celery
```

## Django Apps

### 1. telegram_manager

**Модели**:
- `TelegramChat` - чаты и каналы Telegram
- `TelegramUser` - пользователи Telegram
- `TelegramMessage` - сообщения с связями к чатам и пользователям

**Сервисы**:
- `TelegramService` - основная бизнес-логика
- `MessageImportService` - импорт сообщений
- `ChatService` - управление чатами
- `UserService` - управление пользователями

**Репозитории**:
- `ChatRepository` - CRUD для чатов
- `UserRepository` - CRUD для пользователей  
- `MessageRepository` - CRUD для сообщений

**API интеграция**:
- Telethon для прямого доступа к Telegram API
- Поддержка сессий и аутентификации
- Обработка rate limits

### 2. instagram_manager

**Модели**:
- `InstagramProfile` - профили Instagram
- `InstagramPost` - посты с метриками
- `InstagramMedia` - фото и видео с поддержкой локального хранения и GCS
- `InstagramComment` - комментарии с иерархией ответов
- `InstagramHashtag` - хештеги
- `InstagramFollower` - связи подписчиков
- `InstagramScrapingTask` - отслеживание задач импорта
- `InstagramAccount` - аккаунты для автоматизации
- `PostedComment` - отслеживание опубликованных комментариев

**Сервисы**:
- `ProfileService` - управление профилями
- `PostService` - работа с постами
- `CommentService` - управление комментариями
- `MediaService` - обработка медиафайлов
- `BatchImportService` - массовый импорт данных

**Репозитории**:
Репозиторий для каждой модели с поддержкой:
- Bulk операций
- Оптимизированных запросов (select_related/prefetch_related)
- Транзакций

**API интеграция**:
- BrightData API для сбора данных
- Rate limiting (100 req/min)
- Экспоненциальный backoff при ошибках
- Pydantic валидация всех ответов

### 3. core

**Базовые классы**:
- `TimestampedModel` - created_at, updated_at для всех моделей
- `SoftDeleteModel` - мягкое удаление
- `BaseRepository` - абстрактный репозиторий
- `BaseService` - абстрактный сервис
- `BaseTask` - базовый класс для Celery задач

**Декораторы**:
- `@validate_input` - Pydantic валидация входных данных
- `@require_permission` - проверка Django permissions
- `@deprecated` - пометка устаревших методов
- `@async_to_sync_django` - конвертация async функций

**Формы и валидация**:
- Унифицированная валидация Django форм через Pydantic
- Миксины для Instagram/Telegram форм
- Специализированные поля с встроенной валидацией

**Система логирования**:
- `ContextLogger` - контекстно-зависимое логирование с автоматическим добавлением request_id, user_id, task_id (✅ миграция task 007 завершена)
- `SentryHandler` - отправка ошибок в Sentry с обогащением контекста (✅ реализовано в task 002)
  - Автоматическое добавление контекста из ContextLogger (request_id, user_id, task_id)
  - Маппинг уровней логирования Python на уровни Sentry
  - Поддержка исключений с полным traceback
  - Добавление тегов для фильтрации в Sentry UI
  - Поддержка breadcrumbs для отладки
- `BufferedSentryHandler` - буферизированная версия для оптимизации производительности (✅ реализовано)
  - Батчинг логов с настраиваемым размером буфера
  - Автоматический flush по времени или размеру
  - Немедленная отправка критических ошибок
- `ConditionalSentryHandler` - условная отправка в Sentry (✅ реализовано)
  - Фильтрация по именам логгеров (include/exclude списки)
  - Кастомные условия через функции-предикаты
- `create_sentry_handler()` - фабричная функция для создания обработчиков (✅ реализовано)
- `SensitiveDataFilter` - фильтрация паролей, токенов, API ключей из логов (✅ реализовано в task 003)
  - Поддержка регулярных выражений с группами для точечной замены
  - 20+ встроенных паттернов (пароли, API ключи, JWT, кредитные карты, SSH ключи)
  - Проектно-специфичные паттерны (BrightData, Telegram, Sentry DSN)
  - Рекурсивная очистка сложных структур данных
  - Кеширование для производительности (LRU cache)
  - Django-версия с дополнительными паттернами (CSRF, session)
  - Factory функция с пресетами: strict, balanced, minimal
- `RateLimitFilter` - предотвращение дублирования сообщений в логах (✅ реализовано в task 004)
  - Ограничение частоты одинаковых сообщений (по умолчанию 1 сообщение в 60 секунд)
  - Thread-safe реализация с Lock для параллельного логирования
  - MD5 хеширование для дедупликации сообщений
  - Управление памятью через LRU eviction (до 10K уникальных сообщений)
  - Автоматическая очистка старых записей каждые 5 минут
  - Обратная совместимость с параметром rate_limit_seconds
  - Расширенные версии:
    - BurstRateLimitFilter: алгоритм token bucket для burst трафика
    - SamplingRateLimitFilter: выборочное логирование при превышении лимита
    - LoggerSpecificRateLimitFilter: настройки для конкретных логгеров
  - Factory функция с пресетами: default, strict, burst, sampling, flexible
- **Unit tests для Sentry handlers (task 011 завершена ✅)**:
  - Комплексное покрытие тестами всех Sentry handlers (94.42% coverage)
  - Тесты для SentryHandler: базовая функциональность, обогащение контекста, обработка исключений
  - Тесты для BufferedSentryHandler: буферизация, flush по размеру/времени, thread-safety
  - Тесты для ConditionalSentryHandler: фильтрация по логгерам, кастомные условия
  - Тесты для create_sentry_handler: создание правильных типов handlers
  - Интеграционные тесты с мокированным Sentry SDK
  - Performance тесты и тесты многопоточности
  - Edge case тесты: пустой контекст, отсутствующие атрибуты, ошибки инициализации
- **ContextLogger + structlog интеграция (task 005 завершена ✅)**:
  - Расширенный ContextLogger с полной structlog интеграцией
  - Автоматическое обогащение контекста (request_id, user_id, task_id)
  - Фабричные методы: get_logger, get_task_logger, get_request_logger  
  - BoundContextLogger для компонентного контекста
  - Context managers и декораторы для временного контекста
  - Django middleware для request context injection
  - Celery signal handlers для task context injection  
  - Процессоры: add_app_context (добавляет app_name, version, environment)
  - filter_sensitive_keys процессор для фильтрации паролей и токенов
- **LoggingContextMiddleware (task 006 завершена ✅)**:
  - Полная активация LoggingContextMiddleware в Django
  - Автоматическая генерация request_id для каждого запроса
  - Поддержка HTTP_X_REQUEST_ID заголовка для внешних request_id
  - Автоматическое добавление контекста пользователя в логи
  - Отслеживание времени выполнения запросов (slow request detection)
  - Правильная очистка контекста после обработки запроса
  - RequestIDMiddleware - облегченная версия только для request_id
  - APILoggingMiddleware - расширенная версия для API endpoints с SLA tracking
  - Декораторы для автоматического логирования:
    - @log_api_call - логирование API вызовов
    - @log_task_execution - логирование Celery задач
    - @log_slow_method - предупреждение о медленных методах
    - @with_request_context - обеспечение контекста запроса
    - @with_method_timing - добавление времени выполнения
  - Django templatetags для доступа к контексту в шаблонах:
    - get_request_id - получение текущего request_id
    - get_user_context - контекст пользователя
    - get_logging_context - полный контекст логирования
    - render_logging_debug - debug панель (только в DEBUG режиме)
    - logging_js_context - экспорт контекста в JavaScript
  - Debug шаблон с перетаскиваемой панелью для отображения контекста
  - Комплексные тесты для всех middleware классов и функциональности
- **Sentry SDK Configuration (task 008 завершена ✅)**:
  - Создан core/logging/sentry_config.py с классом SentryConfig для централизованной конфигурации
  - Environment-aware настройки через SocialManager/settings/sentry.py
  - Интеграции: Django, Celery, Redis, Structlog, Logging
  - before_send hook для фильтрации чувствительных данных через SensitiveDataFilter
  - before_send_transaction hook для контроля performance мониторинга
  - Динамический traces_sampler для умного сэмплирования на основе типа транзакции
  - SentryContextMiddleware для автоматического обогащения контекста
  - Декораторы: @capture_errors, @track_transaction, @monitor_performance
  - Management команда test_sentry для проверки интеграции
  - Поддержка profiling для детального анализа производительности
  - Настройки по окружениям: development (100% sampling), staging (25%), production (10%)
  - Автоматическая интеграция с существующей системой логирования
- **Django LOGGING Configuration (task 009 завершена ✅)**:
  - Централизованная конфигурация логирования в core/logging/config.py
  - Environment-specific уровни логирования (development, staging, production, test)
  - Интеграция всех компонентов: SentryHandler, SensitiveDataFilter, RateLimitFilter, LoggingContextFilter
  - Форматтеры: verbose, simple, json (с pythonjsonlogger), colored (с colorlog)
  - Обработчики:
    - Console handlers с цветным выводом для разработки
    - File handlers с ротацией (RotatingFileHandler) для различных уровней
    - JSON file handler для production логов
    - Специализированные handlers для SQL, security, performance, Celery
    - Sentry handler с автоматической активацией при наличии SENTRY_DSN
  - Логгеры для всех компонентов проекта с правильной иерархией
  - Context management через contextvars для async-safe tracking:
    - core/logging/context.py с request_id_var, user_id_var, task_id_var
    - LoggingContextFilter для автоматического добавления контекста в логи
  - Celery-specific конфигурация:
    - get_celery_logging_config() для настройки логирования Celery
    - configure_celery_logging() для интеграции с Celery app
    - Специальные форматтеры и handlers для задач
  - Management команда test_logging для проверки конфигурации
  - Полная интеграция с Django settings:
    - Автоматическое определение окружения через DJANGO_ENV
    - Настройка LOG_DIR для хранения логов
    - Поддержка SENTRY_DSN для активации Sentry
  - Комплексные тесты для всей конфигурации логирования
- **Integration tests (task 012 завершена ✅)**:
  - Полные интеграционные тесты системы логирования с Sentry (32 теста)
  - /tests/core/logging/test_integration.py - основной файл тестов
  - Тестовое приложение test_app с views, tasks, middleware для интеграционного тестирования
  - MockSentryTransport для перехвата событий Sentry без отправки
  - TestDjangoViewIntegration: тесты view логирования, контекста, обработки ошибок
  - TestMiddlewareIntegration: изоляция контекста между запросами, обогащение Sentry
  - TestSensitiveDataFilteringIntegration: фильтрация паролей, токенов, вложенных данных
  - TestCeleryIntegration: контекст в Celery задачах, цепочки задач, retry логика
  - TestPerformanceIntegration: overhead логирования, concurrent logging, memory usage
  - TestEndToEndIntegration: полные сценарии запрос→логирование→Sentry
  - TestConfigurationIntegration: различные конфигурации (minimal, production, custom)
  - test_settings.py и test_urls.py для изоляции тестового окружения
  - conftest.py для автоматической настройки URL в тестах
  - integration_helpers.py с утилитами для интеграционных тестов:
    - create_test_request() - создание тестовых Django запросов
    - capture_logs() - контекстный менеджер для перехвата логов
    - assert_sentry_event() - проверка Sentry событий
    - mock_celery_task() - мок Celery задач
    - временные тестовые настройки и изоляция
  - Исправлена проблема с устаревшим методом set_current_hub в SentryContextMiddleware
  - Все тесты проекта (1161) проходят успешно
- Утилиты для тестирования системы логирования
- **Celery logging handlers (task 013 завершена ✅)**:
  - core/logging/celery_handlers.py - Celery signal handlers для контекста
  - Автоматическая инъекция контекста при старте задачи (task_prerun)
  - Очистка контекста после завершения задачи (task_postrun)
  - Логирование ошибок с контекстом (task_failure)
  - Логирование retry попыток (task_retry)
  - Логирование отмененных задач (task_revoked)
  - Поддержка контекста через contextvars для изоляции между процессами
  - CeleryContextPlugin для автоматической регистрации handlers
  - tests/core/logging/test_celery_logging.py - комплексные тесты:
    - Тесты регистрации signal handlers
    - Тесты propagation контекста в simple, chain, group, chord задачах
    - Тесты изоляции контекста между задачами
    - Тесты производительности с context lookups
    - Тесты error handling и retry логики
    - Тесты очистки контекста после выполнения
  - Исправлены проблемы с eager mode тестированием:
    - Правильная обработка EagerResult в chord задачах
    - Мокирование task логгеров вместо signal handlers
    - Настройка производительных порогов для context lookups

## Внешние интеграции

### BrightData API
- **Назначение**: Сбор данных из Instagram
- **Rate limit**: 100 запросов в минуту
- **Endpoints**: профили, посты, комментарии, медиа
- **Особенности**: требует API ключ, поддерживает batch запросы

### Telegram API (Telethon)
- **Назначение**: Прямая интеграция с Telegram
- **Аутентификация**: API ID, API Hash, сессия
- **Возможности**: чтение чатов, пользователей, сообщений
- **Ограничения**: flood limits, требует номер телефона

### Redis
- **Назначение**: Брокер для Celery, кеширование
- **Использование**: очереди задач, временное хранение
- **Конфигурация**: через REDIS_URL

### PostgreSQL
- **Версия**: 14+
- **Особенности**: JSONB для гибких данных, индексы для производительности
- **Миграции**: Django migrations

### Google Cloud Storage
- **Назначение**: Централизованное хранение медиафайлов
- **Структура папок**: 
  - `images/` - фотографии
  - `videos/` - видеофайлы  
  - `previews/` - превью видео
- **Возможности**: автоматическая организация файлов, валидация, fallback на локальное хранение
- **Конфигурация**: GCS_BUCKET_NAME, GOOGLE_APPLICATION_CREDENTIALS

## Celery задачи

### Инфраструктура
- **Брокер**: Redis
- **Result backend**: Redis
- **Конфигурация**: автоматическое обнаружение задач
- **Мониторинг**: через TaskResult модель

### Instagram задачи
- `ImportBatchPostsTask` - массовый импорт постов
- `ImportInstagramProfileTask` - импорт профиля
- `ImportInstagramPostsTask` - импорт постов профиля
- `ImportInstagramCommentsTask` - импорт комментариев
- `DownloadMediaTask` - загрузка медиафайлов

### Telegram задачи
- `ImportTelegramChatsTask` - импорт чатов
- `ImportTelegramUsersTask` - импорт пользователей
- `ImportTelegramMessagesTask` - импорт сообщений

### Базовый класс BaseTask
- Отслеживание прогресса
- Retry логика
- Сохранение результатов
- WebSocket/SSE поддержка

## Паттерны и соглашения

### Код
1. **Никакой бизнес-логики в моделях** - только в сервисах
2. **Все операции с БД через репозитории**
3. **Pydantic для всех внешних данных**
4. **Транзакции для критических операций**
5. **Логирование всех важных операций**

### Тестирование
- **pytest** для всех тестов
- **Фабрики** для тестовых данных
- **Моки** для внешних API
- **Coverage** > 80%
- **Важно**: Глобальное отключение логирования в tests/conftest.py не влияет на тесты логирования благодаря локальному tests/core/logging/conftest.py

### API Design
- **RESTful** принципы
- **Версионирование** через URL (/api/v1/)
- **Pagination** для списков
- **Фильтрация** через query параметры

### Безопасность
- **Шифрование** паролей и токенов
- **Валидация** всех входных данных
- **Rate limiting** для API endpoints
- **CORS** настройки для фронтенда

## Переменные окружения

### Обязательные
```env
# Django
SECRET_KEY=your-secret-key
DEBUG=False
ALLOWED_HOSTS=localhost,127.0.0.1

# Database
DATABASE_URL=postgres://user:pass@localhost/socialmanager

# Telegram
API_ID=your-telegram-api-id
API_HASH=your-telegram-api-hash
SESSION_NAME=telegram_session

# Instagram/BrightData
BRIGHTDATA_API_KEY=your-api-key
BRIGHTDATA_DATASET_ID=dataset-id

# Security
FIELD_ENCRYPTION_KEY=your-encryption-key

# Sentry (optional)
SENTRY_DSN=your-sentry-dsn
SENTRY_ENVIRONMENT=development
SENTRY_TRACES_SAMPLE_RATE=0.1
```

### Опциональные
```env
# Redis/Celery
REDIS_URL=redis://localhost:6379
CELERY_BROKER_URL=redis://localhost:6379
CELERY_RESULT_BACKEND=redis://localhost:6379

# Google Cloud Storage
GCS_BUCKET_NAME=your-bucket-name
GOOGLE_APPLICATION_CREDENTIALS=/path/to/service-account-key.json

# Timeouts
BRIGHTDATA_TIMEOUT=120

# Paths
MEDIA_ROOT=/path/to/media
OUTPUT_DIR=/path/to/exports
```

## Команды управления

### Django
```bash
python manage.py runserver          # Запуск сервера
python manage.py makemigrations     # Создание миграций
python manage.py migrate            # Применение миграций
python manage.py createsuperuser    # Создание админа
```

### Instagram импорт
```bash
python manage.py instagram_profiles <username>     # Импорт профиля
python manage.py instagram_posts <username>        # Импорт постов
python manage.py instagram_comments <username>     # Импорт комментариев
python manage.py download_instagram_media          # Загрузка медиа
```

### Telegram импорт
```bash
python manage.py telegram_chats              # Импорт чатов
python manage.py telegram_messages <chat_id> # Импорт сообщений
python manage.py fix_telegram_users          # Исправление пользователей
```

### Celery
```bash
celery -A SocialManager worker -l info     # Запуск воркера
celery -A SocialManager beat -l info       # Запуск планировщика
celery -A SocialManager flower             # Веб-интерфейс мониторинга
```

### Тестирование интеграций
```bash
python manage.py test_sentry            # Тестирование Sentry интеграции
python manage.py test_sentry --test-transaction  # Тест performance мониторинга
python manage.py test_sentry --test-breadcrumbs  # Тест breadcrumbs
python manage.py test_logging           # Тестирование конфигурации логирования
python manage.py test_logging --test-context  # Тест контекстного логирования
python manage.py test_logging --test-sensitive  # Тест фильтрации чувствительных данных
python manage.py test_logging --test-rate-limit  # Тест rate limiting
```

## Метрики и мониторинг

### Ключевые метрики
- API response time < 200ms
- Celery task completion rate > 95%
- Database query time < 50ms
- Cache hit rate > 80%

### Логирование
- Структурированные логи (JSON) с использованием structlog
- Контекстно-зависимое логирование с автоматическим добавлением метаданных
- Интеграция с Sentry для мониторинга ошибок в production
- **SensitiveDataFilter** (✅ реализован):
  - Автоматическое удаление паролей, API ключей, токенов из логов
  - Поддержка проектно-специфичных паттернов (BrightData, Telegram, GCS)
  - Рекурсивная очистка dict/list структур
  - Django-специфичная версия с CSRF/session фильтрацией
  - Factory функция с пресетами (strict/balanced/minimal)
- Rate limiting для предотвращения дублирования сообщений
- Уровни: DEBUG, INFO, WARNING, ERROR, CRITICAL
- Ротация логов ежедневно
- Централизованное хранение

### Мониторинг
- Health checks для всех сервисов
- Алерты при превышении thresholds
- Dashboard с ключевыми метриками
- APM для отслеживания производительности

## Известные ограничения

1. **BrightData API**: 100 req/min - требует очередь и батчинг
2. **Telegram flood limits**: адаптивные задержки между запросами
3. **Большие медиафайлы**: асинхронная загрузка через Celery
4. **PostgreSQL connections**: connection pooling обязателен
5. **Memory usage**: большие импорты требуют пагинации

## Roadmap

### Краткосрочные планы
- [x] Интеграция с Google Cloud Storage для медиафайлов
- [x] Инфраструктура для Sentry интеграции (stubs созданы)
- [x] SentryHandler реализация (task 002 завершена ✅)
- [x] Полная реализация Sentry интеграции (tasks 003-009)
  - [x] SensitiveDataFilter (task 003) ✅
  - [x] RateLimitFilter (task 004) ✅ 
  - [x] ContextLogger + structlog integration (task 005) ✅
  - [x] LoggingContextMiddleware activation (task 006) ✅
  - [x] Миграция на ContextLogger (task 007) ✅
  - [x] Sentry SDK Configuration (task 008) ✅
  - [x] Django LOGGING Configuration (task 009) ✅
  - [x] Unit tests для всех handlers (task 011) ✅ - Покрытие 94.42%
  - [x] Integration tests для системы логирования (task 012) ✅
  - [x] Celery logging tests (task 013) ✅ - Контекст propagation и signal handlers
- [ ] Автоматическая модерация комментариев
- [ ] Webhook интеграции

### Долгосрочные планы
- [ ] Поддержка TikTok
- [ ] ML для анализа контента
- [ ] Real-time уведомления
- [ ] Микросервисная архитектура

## План очистки кода (2025-01-18)

### Анализ мертвого кода - Результаты

#### 1. Неиспользуемые импорты (минимальный риск)
- **Найдено**: 2-3 потенциально неиспользуемых импорта
- **Риск**: Минимальный
- **Действие**: Удалить после проверки

#### 2. Закомментированный код (безопасно)
- **Найдено**: Только импорты для предотвращения циклических зависимостей
- **Риск**: Отсутствует
- **Действие**: Оставить как есть с комментариями

#### 3. Неиспользуемые утилиты (средний риск)
- **TextUtils, URLUtils**: Используются только в тестах
- **Retry утилиты**: Не используются вообще
- **Большинство декораторов**: Только в тестах
- **Действие**: Переместить в тесты или удалить

#### 4. Дублирующийся код (высокий риск рефакторинга)
- **Найдено**: 30-40% дублирования между instagram_manager и telegram_manager
- **Риск**: Высокий при рефакторинге
- **Действие**: Оставить для будущего major рефакторинга

#### 5. Пустые файлы (минимальный риск)
- **Найдено**: 20 пустых __init__.py файлов
- **Риск**: Минимальный для большинства
- **Действие**: Безопасно удалить избранные

### Пошаговый план безопасной очистки

#### Этап 1: Безопасные удаления (низкий риск)

1. **Удалить неиспользуемые импорты**:
   - `core/management/commands/base_export_command.py` строка 366
   - Проверить и запустить тесты после изменения

2. **Удалить пустые __init__.py в тестовых директориях**:
   - `/tests/e2e/__init__.py`
   - `/tests/integration/__init__.py`
   - `/tests/performance/__init__.py`
   - `/tests/unit/__init__.py`
   - `/tests/utils/__init__.py`
   - Эти файлы точно не нужны в Python 3.3+

3. **Удалить неиспользуемые retry утилиты**:
   - `/core/utils/retry.py` - полностью не используется
   - Убедиться, что нет импортов из этого файла

#### Этап 2: Перемещение кода (средний риск)

1. **Переместить тестовые утилиты**:
   - `TextUtils` → `/tests/utils/text_utils.py`
   - `URLUtils` → `/tests/utils/url_utils.py`
   - Обновить импорты в тестах
   - Удалить из production кода

2. **Переместить неиспользуемые декораторы**:
   - `@validate_input`, `@deprecated` → тестовые утилиты
   - Оставить только используемые в production

#### Этап 3: Очистка констант и enums

1. **Удалить неиспользуемые enums**:
   - `MediaType`, `PostType`, `ChatType`, `CommentStatus`
   - `Platform`, `ErrorCodes` из `core/utils/constants/enums.py`
   - Проверить отсутствие импортов

2. **Очистить неиспользуемые константы**:
   - `CacheKeys`, `APIDefaults`, `InstagramURLs`
   - Оставить только используемые limits

#### Этап 4: Документирование (безопасно)

1. **Создать документ с планом рефакторинга**:
   - Детальный план устранения дублирования кода
   - Архитектура общих базовых классов
   - Временные рамки и приоритеты

2. **Обновить README**:
   - Документировать текущие ограничения
   - Указать области для улучшения

### Проверки после каждого этапа

1. Запустить полный набор тестов: `uv run pytest`
2. Проверить линтер: `ruff check`
3. Проверить типы: `uv run mypy .`
4. Запустить приложение локально
5. Проверить основные use cases

### Рекомендации для будущего рефакторинга

#### Приоритет 1: Устранение дублирования (отложено)
- Создать общие миксины для репозиториев
- Абстрактные сервисы для социальных сетей
- Унифицированные management команды

#### Приоритет 2: Миграции Instagram
- Объединить 12 миграций в 2-3 после стабилизации

#### Приоритет 3: Архитектурные улучшения
- Внедрить используемые утилиты в основной код
- Расширить использование ErrorHandler
- Улучшить систему декораторов

### Метрики успеха

- ✅ Все тесты проходят
- ✅ Нет регрессий в функциональности
- ✅ Уменьшение объема кода на ~5-10%
- ✅ Улучшение maintainability index
- ✅ Сохранение production стабильности

### Важные замечания

1. **НЕ трогать** дублирующийся код между менеджерами - требует major рефакторинга
2. **НЕ удалять** пустые __init__.py в основных модулях - могут быть нужны для импортов
3. **НЕ менять** публичные API без версионирования
4. **Всегда** делать backup перед массовыми изменениями

Этот план обеспечивает безопасную очистку с минимальным риском для production системы.