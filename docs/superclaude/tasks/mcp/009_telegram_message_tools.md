# Subtask 009: Telegram Message Management Tools

## Objective
Implement MCP tools for Telegram message retrieval, search, and analytics.

## Prerequisites
- Completed subtasks 001-008
- Chat management tools ready

## Implementation Steps

### 1. Create Message Management Tools

Create `mcp_server/tools/telegram_messages.py`:

```python
"""
Telegram message management tools
"""

@mcp.tool()
@handle_tool_errors
async def telegram_get_messages(
    chat_id: int,
    limit: int = 50,
    offset: int = 0,
    start_date: Optional[str] = None,
    end_date: Optional[str] = None,
    from_user_id: Optional[int] = None,
    message_type: Optional[str] = None
) -> Dict[str, Any]:
    """
    Get messages from a Telegram chat
    
    Args:
        chat_id: Telegram chat ID
        limit: Number of messages
        offset: Skip messages
        start_date: Filter from date
        end_date: Filter to date
        from_user_id: Filter by sender
        message_type: Filter by type (text, photo, video)
        
    Returns:
        List of messages with details
    """
    # Implementation
    pass

@mcp.tool()
@handle_tool_errors
async def telegram_search_messages(
    query: str,
    chat_ids: Optional[List[int]] = None,
    limit: int = 50
) -> Dict[str, Any]:
    """
    Search messages across chats
    
    Args:
        query: Search text
        chat_ids: Limit to specific chats
        limit: Maximum results
        
    Returns:
        Matching messages
    """
    # Implementation
    pass

@mcp.tool()
@handle_tool_errors
async def telegram_get_message_statistics(
    chat_id: int,
    period: str = "week",
    group_by: str = "day"
) -> Dict[str, Any]:
    """
    Get message statistics for a chat
    
    Args:
        chat_id: Telegram chat ID
        period: Time period
        group_by: Grouping (hour, day, week)
        
    Returns:
        Message statistics
    """
    # Implementation
    pass
```

### 2. Key Features

1. **Message Retrieval**:
   - Pagination support
   - Date filtering
   - User filtering
   - Type filtering

2. **Search Features**:
   - Full-text search
   - Multi-chat search
   - Regex support
   - Result ranking

3. **Analytics**:
   - Message frequency
   - User activity
   - Media statistics
   - Peak hours

## Next Steps

Proceed to **010_task_creation_system.md** for task management.