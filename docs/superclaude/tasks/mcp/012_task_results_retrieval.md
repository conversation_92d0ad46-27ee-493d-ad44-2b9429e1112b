# Subtask 012: Task Results Retrieval

## Objective
Implement comprehensive task results retrieval system with support for different formats, filtering, pagination, and export capabilities.

## Prerequisites
- Completed subtasks 001-011
- Task creation and management system ready
- WebSocket support implemented

## Implementation Steps

### 1. Create Task Results Retrieval Tools

Create `mcp_server/tools/task_results.py`:

```python
"""
Task results retrieval and export tools
"""

from typing import Dict, Any, Optional, List, Union
from datetime import datetime
import json
import io
import zipfile
import base64

from mcp_server.main import mcp
from mcp_server.decorators.error_handler import handle_tool_errors
from mcp_server.decorators.database import async_db_operation, track_sync_calls
from mcp_server.errors import create_error_response, ErrorTypes
from mcp_server.validators.results import ResultsExportParams

import logging

logger = logging.getLogger(__name__)

@mcp.tool()
@handle_tool_errors
async def task_get_result(
    task_id: str,
    include_raw: bool = False,
    include_metadata: bool = True
) -> Dict[str, Any]:
    """
    Get detailed results for a completed task
    
    Args:
        task_id: Task UUID
        include_raw: Include raw result data
        include_metadata: Include task metadata
        
    Returns:
        Task results with requested details
    """
    result_data = await _get_task_result(task_id)
    
    if "error" in result_data:
        return create_error_response(
            error_type=result_data.get("error_type", ErrorTypes.NOT_FOUND),
            message=result_data["error"]
        )
    
    task = result_data["task"]
    
    # Build response
    response = {
        "task_id": task.task_id,
        "task_type": task.task_type,
        "status": task.status,
        "completed_at": task.completed_at.isoformat() if task.completed_at else None
    }
    
    # Add metadata if requested
    if include_metadata:
        response["metadata"] = {
            "task_name": task.task_name,
            "parameters": task.parameters,
            "created_at": task.created_at.isoformat(),
            "started_at": task.started_at.isoformat() if task.started_at else None,
            "duration": _calculate_duration(task),
            "progress": task.progress,
            "error": task.error
        }
    
    # Process results based on task type
    if task.status == "completed" and task.result:
        response["result"] = await _process_task_result(
            task_type=task.task_type,
            result=task.result,
            include_raw=include_raw
        )
    elif task.status == "failed":
        response["error"] = {
            "message": task.error or "Task failed",
            "type": task.result.get("error_type") if isinstance(task.result, dict) else "unknown",
            "details": task.result.get("error_details") if isinstance(task.result, dict) else None
        }
    
    return response

@async_db_operation
@track_sync_calls
def _get_task_result(task_id: str) -> Dict[str, Any]:
    """Get task result from database"""
    from core.models import TaskResult
    
    task = TaskResult.objects.filter(task_id=task_id).first()
    
    if not task:
        return {
            "error": f"Task {task_id} not found",
            "error_type": ErrorTypes.NOT_FOUND
        }
    
    if task.status not in ["completed", "failed", "cancelled"]:
        return {
            "error": f"Task is still {task.status}",
            "error_type": ErrorTypes.INVALID_STATE
        }
    
    return {"task": task}

def _calculate_duration(task) -> Optional[float]:
    """Calculate task duration in seconds"""
    if task.started_at and task.completed_at:
        return (task.completed_at - task.started_at).total_seconds()
    return None

async def _process_task_result(
    task_type: str,
    result: Dict[str, Any],
    include_raw: bool
) -> Dict[str, Any]:
    """Process task result based on type"""
    
    processed = {}
    
    if task_type == "instagram.profile":
        processed = {
            "profile": {
                "username": result.get("username"),
                "profile_id": result.get("profile_id"),
                "imported": result.get("imported", False),
                "updated": result.get("updated", False)
            },
            "statistics": result.get("statistics", {})
        }
    
    elif task_type == "instagram.posts":
        processed = {
            "summary": {
                "total_posts": result.get("total_posts", 0),
                "new_posts": result.get("new_posts", 0),
                "updated_posts": result.get("updated_posts", 0),
                "failed_posts": result.get("failed_posts", 0)
            },
            "posts": result.get("posts", [])[:10]  # First 10 posts
        }
    
    elif task_type == "instagram.batch_posts":
        processed = {
            "profiles_processed": result.get("profiles_processed", 0),
            "total_posts": result.get("total_posts", 0),
            "profiles": result.get("profiles", [])
        }
    
    elif task_type == "telegram.messages":
        processed = {
            "chat_id": result.get("chat_id"),
            "messages_imported": result.get("messages_imported", 0),
            "users_created": result.get("users_created", 0),
            "date_range": result.get("date_range", {})
        }
    
    # Add raw data if requested
    if include_raw:
        processed["_raw"] = result
    
    return processed

@mcp.tool()
@handle_tool_errors
async def task_get_batch_results(
    task_ids: List[str],
    summary_only: bool = True
) -> Dict[str, Any]:
    """
    Get results for multiple tasks
    
    Args:
        task_ids: List of task UUIDs (max 50)
        summary_only: Return only summary data
        
    Returns:
        Batch task results
    """
    # Validate input
    if not task_ids:
        return create_error_response(
            error_type=ErrorTypes.VALIDATION_ERROR,
            message="At least one task_id required"
        )
    
    if len(task_ids) > 50:
        return create_error_response(
            error_type=ErrorTypes.VALIDATION_ERROR,
            message="Maximum 50 tasks allowed"
        )
    
    # Get results
    results = await _get_batch_results(task_ids, summary_only)
    
    return {
        "requested": len(task_ids),
        "found": len(results),
        "results": results
    }

@async_db_operation
@track_sync_calls
def _get_batch_results(
    task_ids: List[str],
    summary_only: bool
) -> List[Dict[str, Any]]:
    """Get results for multiple tasks"""
    from core.models import TaskResult
    
    tasks = TaskResult.objects.filter(
        task_id__in=task_ids
    ).order_by("-created_at")
    
    results = []
    for task in tasks:
        if summary_only:
            result = {
                "task_id": task.task_id,
                "task_type": task.task_type,
                "status": task.status,
                "created_at": task.created_at.isoformat(),
                "completed_at": task.completed_at.isoformat() if task.completed_at else None,
                "duration": _calculate_duration(task)
            }
            
            # Add summary based on status
            if task.status == "completed" and task.result:
                result["summary"] = _get_result_summary(task.task_type, task.result)
            elif task.status == "failed":
                result["error"] = task.error
        else:
            # Full result
            result = {
                "task_id": task.task_id,
                "task_type": task.task_type,
                "status": task.status,
                "parameters": task.parameters,
                "result": task.result,
                "error": task.error,
                "created_at": task.created_at.isoformat(),
                "started_at": task.started_at.isoformat() if task.started_at else None,
                "completed_at": task.completed_at.isoformat() if task.completed_at else None,
                "duration": _calculate_duration(task)
            }
        
        results.append(result)
    
    return results

def _get_result_summary(task_type: str, result: Dict[str, Any]) -> Dict[str, Any]:
    """Get summary for task result"""
    if task_type == "instagram.profile":
        return {
            "username": result.get("username"),
            "imported": result.get("imported", False)
        }
    elif task_type == "instagram.posts":
        return {
            "total_posts": result.get("total_posts", 0),
            "new_posts": result.get("new_posts", 0)
        }
    elif task_type == "telegram.messages":
        return {
            "messages_imported": result.get("messages_imported", 0)
        }
    
    return {}

@mcp.tool()
@handle_tool_errors
async def task_export_results(
    task_ids: List[str],
    format: str = "json",
    include_metadata: bool = True,
    compress: bool = False
) -> Dict[str, Any]:
    """
    Export task results in various formats
    
    Args:
        task_ids: List of task UUIDs to export
        format: Export format (json)
        include_metadata: Include task metadata
        compress: Compress output (zip)
        
    Returns:
        Exported data as base64 string or download URL
    """
    # Validate format
    valid_formats = ["json"]
    if format not in valid_formats:
        return create_error_response(
            error_type=ErrorTypes.VALIDATION_ERROR,
            message=f"Invalid format: {format}"
        )
    
    # Get task results
    tasks = await _get_tasks_for_export(task_ids)
    
    if not tasks:
        return create_error_response(
            error_type=ErrorTypes.NOT_FOUND,
            message="No tasks found for export"
        )
    
    # Export based on format
    if format == "json":
        export_data = await _export_json(tasks, include_metadata)
    
    # Compress if requested
    if compress:
        export_data = await _compress_data(export_data, format)
    
    # Encode to base64
    encoded = base64.b64encode(export_data).decode('utf-8')
    
    return {
        "format": format,
        "compressed": compress,
        "size_bytes": len(export_data),
        "tasks_count": len(tasks),
        "data": encoded,
        "filename": f"task_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.{format}"
    }

@async_db_operation
@track_sync_calls
def _get_tasks_for_export(task_ids: List[str]) -> List[Dict[str, Any]]:
    """Get tasks for export"""
    from core.models import TaskResult
    
    tasks = TaskResult.objects.filter(
        task_id__in=task_ids,
        status__in=["completed", "failed"]
    ).order_by("-created_at")
    
    return [
        {
            "task_id": t.task_id,
            "task_type": t.task_type,
            "task_name": t.task_name,
            "status": t.status,
            "parameters": t.parameters,
            "result": t.result,
            "error": t.error,
            "progress": t.progress,
            "created_at": t.created_at,
            "started_at": t.started_at,
            "completed_at": t.completed_at
        }
        for t in tasks
    ]

async def _export_json(
    tasks: List[Dict[str, Any]],
    include_metadata: bool
) -> bytes:
    """Export as JSON"""
    export_data = []
    
    for task in tasks:
        item = {
            "task_id": task["task_id"],
            "task_type": task["task_type"],
            "status": task["status"]
        }
        
        if include_metadata:
            item["metadata"] = {
                "task_name": task["task_name"],
                "parameters": task["parameters"],
                "created_at": task["created_at"].isoformat(),
                "started_at": task["started_at"].isoformat() if task["started_at"] else None,
                "completed_at": task["completed_at"].isoformat() if task["completed_at"] else None
            }
        
        if task["status"] == "completed":
            item["result"] = task["result"]
        else:
            item["error"] = task["error"]
        
        export_data.append(item)
    
    return json.dumps(export_data, indent=2).encode('utf-8')


def _format_result_summary(task_type: str, result: Dict[str, Any]) -> str:
    """Format result summary for export"""
    if task_type == "instagram.profile":
        return f"Profile: {result.get('username', 'Unknown')}"
    elif task_type == "instagram.posts":
        return f"Posts: {result.get('total_posts', 0)} total, {result.get('new_posts', 0)} new"
    elif task_type == "telegram.messages":
        return f"Messages: {result.get('messages_imported', 0)}"
    
    return json.dumps(result)[:100]


async def _compress_data(data: bytes, format: str) -> bytes:
    """Compress data to ZIP"""
    output = io.BytesIO()
    
    with zipfile.ZipFile(output, 'w', zipfile.ZIP_DEFLATED) as zf:
        filename = f"task_results.{format}"
        zf.writestr(filename, data)
    
    output.seek(0)
    return output.read()

@mcp.tool()
@handle_tool_errors
async def task_search_results(
    query: str,
    task_type: Optional[str] = None,
    status: Optional[str] = None,
    date_from: Optional[str] = None,
    date_to: Optional[str] = None,
    limit: int = 20
) -> Dict[str, Any]:
    """
    Search task results
    
    Args:
        query: Search query for results
        task_type: Filter by task type
        status: Filter by status
        date_from: Start date (ISO format)
        date_to: End date (ISO format)
        limit: Maximum results
        
    Returns:
        Matching task results
    """
    # Build search filters
    filters = {
        "query": query,
        "task_type": task_type,
        "status": status
    }
    
    if date_from:
        filters["created_at__gte"] = datetime.fromisoformat(date_from)
    if date_to:
        filters["created_at__lte"] = datetime.fromisoformat(date_to)
    
    # Search results
    results = await _search_task_results(filters, limit)
    
    return {
        "query": query,
        "filters": {
            "task_type": task_type,
            "status": status,
            "date_range": {
                "from": date_from,
                "to": date_to
            }
        },
        "count": len(results),
        "results": results
    }

@async_db_operation
@track_sync_calls
def _search_task_results(filters: Dict[str, Any], limit: int) -> List[Dict[str, Any]]:
    """Search task results in database"""
    from core.models import TaskResult
    from django.db.models import Q
    
    query = filters.pop("query")
    queryset = TaskResult.objects.all()
    
    # Apply filters
    if filters.get("task_type"):
        queryset = queryset.filter(task_type=filters["task_type"])
    if filters.get("status"):
        queryset = queryset.filter(status=filters["status"])
    if filters.get("created_at__gte"):
        queryset = queryset.filter(created_at__gte=filters["created_at__gte"])
    if filters.get("created_at__lte"):
        queryset = queryset.filter(created_at__lte=filters["created_at__lte"])
    
    # Search in results and parameters
    if query:
        queryset = queryset.filter(
            Q(result__icontains=query) |
            Q(parameters__icontains=query) |
            Q(task_name__icontains=query)
        )
    
    # Order and limit
    queryset = queryset.order_by("-created_at")[:limit]
    
    # Format results
    results = []
    for task in queryset:
        result = {
            "task_id": task.task_id,
            "task_type": task.task_type,
            "task_name": task.task_name,
            "status": task.status,
            "created_at": task.created_at.isoformat(),
            "relevance": _calculate_relevance(task, query)
        }
        
        # Add matching snippet
        if query:
            result["snippet"] = _extract_snippet(task, query)
        
        results.append(result)
    
    # Sort by relevance
    results.sort(key=lambda x: x["relevance"], reverse=True)
    
    return results

def _calculate_relevance(task, query: str) -> float:
    """Calculate search relevance score"""
    if not query:
        return 0.0
    
    score = 0.0
    query_lower = query.lower()
    
    # Check task name
    if task.task_name and query_lower in task.task_name.lower():
        score += 2.0
    
    # Check parameters
    if task.parameters:
        params_str = json.dumps(task.parameters).lower()
        score += params_str.count(query_lower) * 0.5
    
    # Check results
    if task.result:
        result_str = json.dumps(task.result).lower()
        score += result_str.count(query_lower) * 0.3
    
    return score

def _extract_snippet(task, query: str) -> str:
    """Extract relevant snippet from task data"""
    # Search in parameters first
    if task.parameters:
        params_str = json.dumps(task.parameters)
        snippet = _find_snippet_in_text(params_str, query)
        if snippet:
            return f"Parameters: ...{snippet}..."
    
    # Then in results
    if task.result:
        result_str = json.dumps(task.result)
        snippet = _find_snippet_in_text(result_str, query)
        if snippet:
            return f"Result: ...{snippet}..."
    
    return ""

def _find_snippet_in_text(text: str, query: str, context_chars: int = 50) -> str:
    """Find query in text and return with context"""
    pos = text.lower().find(query.lower())
    if pos == -1:
        return ""
    
    start = max(0, pos - context_chars)
    end = min(len(text), pos + len(query) + context_chars)
    
    snippet = text[start:end]
    
    # Clean up
    if start > 0:
        snippet = "..." + snippet
    if end < len(text):
        snippet = snippet + "..."
    
    return snippet
```

### 2. Create Results Aggregation Tools

Create `mcp_server/tools/task_aggregation.py`:

```python
"""
Task results aggregation and analytics
"""

from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
from collections import defaultdict

from mcp_server.main import mcp
from mcp_server.decorators.error_handler import handle_tool_errors
from mcp_server.decorators.database import async_db_operation, track_sync_calls

@mcp.tool()
@handle_tool_errors
async def task_aggregate_results(
    task_type: str,
    aggregation: str = "daily",
    date_from: Optional[str] = None,
    date_to: Optional[str] = None
) -> Dict[str, Any]:
    """
    Aggregate task results by time period
    
    Args:
        task_type: Type of tasks to aggregate
        aggregation: Aggregation period (hourly, daily, weekly)
        date_from: Start date
        date_to: End date
        
    Returns:
        Aggregated results
    """
    # Get aggregated data
    data = await _aggregate_task_results(
        task_type=task_type,
        aggregation=aggregation,
        date_from=date_from,
        date_to=date_to
    )
    
    return {
        "task_type": task_type,
        "aggregation": aggregation,
        "period": {
            "from": date_from,
            "to": date_to
        },
        "data": data
    }

@async_db_operation
@track_sync_calls
def _aggregate_task_results(
    task_type: str,
    aggregation: str,
    date_from: Optional[str],
    date_to: Optional[str]
) -> List[Dict[str, Any]]:
    """Aggregate results from database"""
    from core.models import TaskResult
    from django.db.models import Count, Sum, Avg, Q
    
    # Build date range
    queryset = TaskResult.objects.filter(task_type=task_type)
    
    if date_from:
        queryset = queryset.filter(created_at__gte=datetime.fromisoformat(date_from))
    if date_to:
        queryset = queryset.filter(created_at__lte=datetime.fromisoformat(date_to))
    
    # Get all tasks in range
    tasks = list(queryset.order_by("created_at"))
    
    # Aggregate by period
    aggregated = defaultdict(lambda: {
        "total": 0,
        "completed": 0,
        "failed": 0,
        "items_processed": 0,
        "avg_duration": 0,
        "durations": []
    })
    
    for task in tasks:
        # Determine period key
        if aggregation == "hourly":
            key = task.created_at.strftime("%Y-%m-%d %H:00")
        elif aggregation == "daily":
            key = task.created_at.strftime("%Y-%m-%d")
        elif aggregation == "weekly":
            # Get week start
            week_start = task.created_at - timedelta(days=task.created_at.weekday())
            key = week_start.strftime("%Y-%m-%d")
        
        # Aggregate data
        agg = aggregated[key]
        agg["total"] += 1
        
        if task.status == "completed":
            agg["completed"] += 1
            
            # Extract items processed
            if task.result and isinstance(task.result, dict):
                if task_type == "instagram.posts":
                    agg["items_processed"] += task.result.get("total_posts", 0)
                elif task_type == "telegram.messages":
                    agg["items_processed"] += task.result.get("messages_imported", 0)
            
            # Calculate duration
            if task.started_at and task.completed_at:
                duration = (task.completed_at - task.started_at).total_seconds()
                agg["durations"].append(duration)
        
        elif task.status == "failed":
            agg["failed"] += 1
    
    # Calculate averages
    result = []
    for period, data in sorted(aggregated.items()):
        if data["durations"]:
            data["avg_duration"] = sum(data["durations"]) / len(data["durations"])
        
        # Remove raw durations
        del data["durations"]
        
        # Calculate success rate
        data["success_rate"] = (
            (data["completed"] / data["total"] * 100)
            if data["total"] > 0 else 0
        )
        
        result.append({
            "period": period,
            **data
        })
    
    return result
```

### 3. Create Results Validation

Create `mcp_server/validators/results.py`:

```python
"""
Results validation models
"""

from pydantic import BaseModel, Field, validator
from typing import Optional, List, Literal
from datetime import datetime

class ResultsExportParams(BaseModel):
    """Export parameters validation"""
    task_ids: List[str] = Field(..., min_items=1, max_items=100)
    format: Literal["json"] = Field(default="json")
    include_metadata: bool = Field(default=True)
    compress: bool = Field(default=False)
    
    @validator('task_ids')
    def validate_task_ids(cls, v):
        # Validate UUID format
        import uuid
        for task_id in v:
            try:
                uuid.UUID(task_id)
            except ValueError:
                raise ValueError(f"Invalid task ID format: {task_id}")
        return v

class ResultsSearchParams(BaseModel):
    """Search parameters validation"""
    query: str = Field(..., min_length=2, max_length=200)
    task_type: Optional[str] = None
    status: Optional[Literal["completed", "failed", "cancelled"]] = None
    date_from: Optional[datetime] = None
    date_to: Optional[datetime] = None
    limit: int = Field(default=20, ge=1, le=100)
```

## Key Features

### 1. Results Retrieval
- Get single task results
- Batch results retrieval
- Metadata inclusion options
- Raw data access

### 2. Export Capabilities
- JSON export
- ZIP compression
- Base64 encoding

### 3. Search Functionality
- Full-text search in results
- Filter by type, status, date
- Relevance scoring
- Snippet extraction

### 4. Aggregation & Analytics
- Time-based aggregation
- Success rate calculation
- Task completion statistics
- Item processing counts

## Usage Examples

### Get Single Result
```python
result = await task_get_result(
    task_id="uuid-here",
    include_raw=True,
    include_metadata=True
)
```

### Export Multiple Results
```python
export = await task_export_results(
    task_ids=["uuid1", "uuid2", "uuid3"],
    format="json",
    compress=True
)

# Save to file
import base64
data = base64.b64decode(export["data"])
with open(export["filename"], "wb") as f:
    f.write(data)
```

### Search Results
```python
results = await task_search_results(
    query="fashion",
    task_type="instagram.posts",
    status="completed",
    limit=50
)
```

### Aggregate Analytics
```python
analytics = await task_aggregate_results(
    task_type="instagram.posts",
    aggregation="daily",
    date_from="2024-01-01",
    date_to="2024-01-31"
)
```

## Testing Strategy

```python
# Test result retrieval
async def test_get_result():
    result = await task_get_result(task_id)
    assert "result" in result
    assert result["status"] == "completed"

# Test export
async def test_export_results():
    export = await task_export_results(
        task_ids=[task_id],
        format="json"
    )
    assert export["format"] == "json"
    assert "data" in export

# Test search
async def test_search_results():
    results = await task_search_results(
        query="test",
        limit=10
    )
    assert len(results["results"]) <= 10
```

## Performance Considerations

1. **Pagination**: Always paginate large result sets
2. **Caching**: Cache frequently accessed results
3. **Compression**: Use compression for large exports
4. **Indexing**: Index result fields for search
5. **Async Processing**: Use async for all I/O operations

## Success Criteria

- [ ] Single result retrieval
- [ ] Batch results retrieval
- [ ] Export in JSON format
- [ ] Compression support
- [ ] Search functionality
- [ ] Aggregation tools
- [ ] Performance optimization
- [ ] All tests passing

## Next Steps

Proceed to **014_docker_configuration.md** for implementing Docker configuration and deployment setup.