# Subtask 006: Instagram Post and Media Management Tools

## Objective
Implement MCP tools for Instagram post and media management, including fetching posts, media handling, and post analytics.

## Prerequisites
- Completed subtasks 001-005
- Instagram profile tools implemented

## Implementation Steps

### 1. Create Post Management Tools

Create `mcp_server/tools/instagram_posts.py`:

```python
"""
Instagram post management tools
"""

from typing import Dict, Any, Optional, List
from datetime import datetime

from mcp_server.main import mcp
from mcp_server.decorators.error_handler import handle_tool_errors
from mcp_server.decorators.database import async_db_operation, track_sync_calls
from mcp_server.errors import create_error_response, ErrorTypes

import logging

logger = logging.getLogger(__name__)

@async_db_operation
@track_sync_calls
def get_posts_by_username(username: str, filters: dict, limit: int, offset: int):
    """Get posts for Instagram profile"""
    from instagram_manager.services import InstagramPostService
    service = InstagramPostService()
    
    # Get posts with optimized queries
    posts = service.get_posts_by_username(
        username=username,
        filters=filters,
        limit=limit,
        offset=offset
    )
    
    # Get total count
    total = service.count_posts_by_username(username, filters=filters)
    
    return posts, total

@async_db_operation
@track_sync_calls
def get_post_by_id(post_id: int):
    """Get single post with all relations"""
    from instagram_manager.models import InstagramPost
    
    return InstagramPost.objects.select_related(
        'profile'
    ).prefetch_related(
        'media',
        'comments',
        'hashtags'
    ).filter(id=post_id).first()

@async_db_operation
@track_sync_calls
def get_post_media(post_id: int):
    """Get media for a post"""
    from instagram_manager.models import InstagramMedia
    
    return list(InstagramMedia.objects.filter(
        post_id=post_id
    ).order_by('position'))

@mcp.tool()
@handle_tool_errors
async def instagram_get_posts(
    username: str,
    limit: int = 20,
    offset: int = 0,
    post_type: Optional[str] = None,
    start_date: Optional[str] = None,
    end_date: Optional[str] = None,
    min_likes: Optional[int] = None,
    has_media: Optional[bool] = None
) -> Dict[str, Any]:
    """
    Get posts for a specific Instagram profile
    
    Args:
        username: Instagram username
        limit: Number of posts (1-100)
        offset: Skip posts
        post_type: Filter by type (photo, video, album, reel)
        start_date: Posts after this date (ISO format)
        end_date: Posts before this date (ISO format)
        min_likes: Minimum like count
        has_media: Filter posts with/without media
        
    Returns:
        List of posts with details
    """
    # Build filters
    filters = {}
    if post_type:
        filters['post_type'] = post_type
    if start_date:
        filters['posted_at__gte'] = datetime.fromisoformat(start_date)
    if end_date:
        filters['posted_at__lte'] = datetime.fromisoformat(end_date)
    if min_likes is not None:
        filters['like_count__gte'] = min_likes
    if has_media is not None:
        filters['has_media'] = has_media
    
    # Get posts
    posts, total = await get_posts_by_username(
        username=username,
        filters=filters,
        limit=limit,
        offset=offset
    )
    
    return {
        "username": username,
        "total": total,
        "offset": offset,
        "limit": limit,
        "posts": [
            {
                "id": p.id,
                "external_id": p.external_id,
                "content": p.content[:200] + "..." if len(p.content) > 200 else p.content,
                "post_type": p.post_type,
                "post_url": p.post_url,
                "like_count": p.like_count,
                "comment_count": p.comment_count,
                "share_count": p.share_count,
                "view_count": p.view_count,
                "posted_at": p.posted_at.isoformat(),
                "media_count": await sync_to_async(p.media.count)(),
                "hashtags": [h.name for h in await sync_to_async(list)(p.hashtags.all()[:10])]
            }
            for p in posts
        ]
    }

@mcp.tool()
@handle_tool_errors
async def instagram_get_post_details(post_id: int) -> Dict[str, Any]:
    """
    Get detailed information about a specific post
    
    Args:
        post_id: Instagram post ID
        
    Returns:
        Detailed post information including media
    """
    post = await get_post_by_id(post_id)
    
    if not post:
        return create_error_response(
            error_type=ErrorTypes.NOT_FOUND,
            message=f"Post {post_id} not found"
        )
    
    # Get media details
    media_items = []
    for media in await sync_to_async(list)(post.media.all()):
        media_items.append({
            "id": media.id,
            "media_type": media.media_type,
            "media_url": media.media_url,
            "thumbnail_url": media.thumbnail_url,
            "width": media.width,
            "height": media.height,
            "duration": media.duration,
            "is_downloaded": media.is_downloaded,
            "local_path": media.local_file.url if media.local_file else None,
            "gcs_url": media.gcs_url if hasattr(media, 'gcs_url') else None
        })
    
    return {
        "id": post.id,
        "external_id": post.external_id,
        "profile": {
            "id": post.profile.id,
            "username": post.profile.username,
            "full_name": post.profile.full_name
        },
        "content": post.content,
        "post_type": post.post_type,
        "post_url": post.post_url,
        "like_count": post.like_count,
        "comment_count": post.comment_count,
        "share_count": post.share_count,
        "view_count": post.view_count,
        "posted_at": post.posted_at.isoformat(),
        "location": post.location,
        "is_sponsored": post.is_sponsored,
        "media": media_items,
        "hashtags": [{"id": h.id, "name": h.name} for h in await sync_to_async(list)(post.hashtags.all())],
        "created_at": post.created_at.isoformat(),
        "updated_at": post.updated_at.isoformat()
    }

@mcp.tool()
@handle_tool_errors
async def instagram_get_trending_posts(
    limit: int = 20,
    time_period: str = "week",
    min_engagement_rate: Optional[float] = None
) -> Dict[str, Any]:
    """
    Get trending posts based on engagement
    
    Args:
        limit: Number of posts (1-50)
        time_period: Period to analyze (today, week, month)
        min_engagement_rate: Minimum engagement rate percentage
        
    Returns:
        List of trending posts
    """
    # Implementation would query posts with high engagement
    # relative to follower count and time period
    pass
```

### 2. Create Media Management Tools

Create `mcp_server/tools/instagram_media.py`:

```python
"""
Instagram media management tools
"""

@mcp.tool()
@handle_tool_errors
async def instagram_download_media(
    post_id: int,
    media_types: Optional[List[str]] = None,
    save_to_gcs: bool = False
) -> Dict[str, Any]:
    """
    Download media files for a post
    
    Args:
        post_id: Instagram post ID
        media_types: Types to download (photo, video)
        save_to_gcs: Save to Google Cloud Storage
        
    Returns:
        Download status and file paths
    """
    # Implementation would trigger media download
    pass

@mcp.tool()
@handle_tool_errors
async def instagram_get_media_analytics(
    post_ids: List[int]
) -> Dict[str, Any]:
    """
    Get analytics for post media
    
    Args:
        post_ids: List of post IDs
        
    Returns:
        Media analytics data
    """
    # Implementation would return media analytics data
    pass
```

### 3. Key Features to Implement

1. **Post Retrieval**:
   - Get posts by profile
   - Filter by type, date, engagement
   - Pagination support

2. **Media Handling**:
   - List media for posts
   - Download media files
   - GCS integration

3. **Analytics**:
   - Engagement rates
   - Trending posts
   - Post analytics

4. **Bulk Operations**:
   - Get multiple posts
   - Batch media download

## Testing

Create comprehensive tests for:
- Post retrieval with filters
- Media listing and download
- Analytics calculations
- Error handling

## Next Steps

Proceed to **007_instagram_comment_tools.md** for comment management implementation.