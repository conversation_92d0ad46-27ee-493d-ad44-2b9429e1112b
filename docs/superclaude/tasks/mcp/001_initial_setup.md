# Subtask 001: Initial Setup and Dependencies

## Objective
Set up the initial project structure, install required dependencies, and prepare the environment for MCP server development.

## Prerequisites
- Python 3.11+
- Django project (SocialManager) already configured
- Celery configured and running
- Redis/RabbitMQ for message broker

## Implementation Steps

### 1. Install Required Dependencies

Add the following packages to `requirements.txt`:

```txt
# MCP Server dependencies
fastmcp>=0.3.0
uvicorn[standard]>=0.27.0
pydantic>=2.5.0
asyncpg>=0.29.0
httpx>=0.26.0
python-multipart>=0.0.9
```

Install dependencies:
```bash
uv pip install -r requirements.txt
```

### 2. Create MCP Directory Structure

```bash
# Create directories
mkdir -p mcp_server
mkdir -p mcp_server/tools
mkdir -p mcp_server/validators
mkdir -p mcp_server/decorators
mkdir -p mcp_server/utils
```

### 3. Create Initial Configuration File

Create `mcp_server/config.py`:

```python
import os
from pathlib import Path

# MCP Server Configuration
MCP_SERVER_NAME = "SocialManager MCP"
MCP_SERVER_VERSION = "1.0.0"
MCP_SERVER_HOST = os.getenv("MCP_HOST", "0.0.0.0")
MCP_SERVER_PORT = int(os.getenv("MCP_PORT", 8000))

# Debug mode
DEBUG = os.getenv("DEBUG", "False").lower() == "true"

# Logging configuration
LOG_LEVEL = os.getenv("MCP_LOG_LEVEL", "INFO" if not DEBUG else "DEBUG")

# Performance settings
SLOW_QUERY_THRESHOLD = 0.1  # 100ms
MAX_CONCURRENT_TASKS = 100

# Connection pool settings
DB_POOL_MIN_SIZE = 10
DB_POOL_MAX_SIZE = 20
DB_COMMAND_TIMEOUT = 60

# Task registry validation
VALIDATE_ON_STARTUP = True
```

### 4. Create Base MCP Server Module

Create `mcp_server/__init__.py`:

```python
"""
MCP Server for SocialManager
Provides remote access to Instagram and Telegram management functionality
"""

__version__ = "1.0.0"
```

### 5. Create Logging Configuration

Create `mcp_server/logging_config.py`:

```python
import logging
import sys
from .config import LOG_LEVEL

def setup_logging():
    """Configure logging for MCP server"""
    
    # Create formatter
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    
    # Configure root logger
    root_logger = logging.getLogger()
    root_logger.setLevel(getattr(logging, LOG_LEVEL))
    
    # Console handler
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setFormatter(formatter)
    root_logger.addHandler(console_handler)
    
    # File handler (optional)
    if os.getenv("MCP_LOG_FILE"):
        file_handler = logging.FileHandler(os.getenv("MCP_LOG_FILE"))
        file_handler.setFormatter(formatter)
        root_logger.addHandler(file_handler)
    
    # Silence noisy libraries
    logging.getLogger("uvicorn.access").setLevel(logging.WARNING)
    logging.getLogger("httpx").setLevel(logging.WARNING)
    
    return root_logger
```

### 6. Environment Variables Documentation

Create `.env.mcp.example`:

```env
# MCP Server Configuration
MCP_HOST=0.0.0.0
MCP_PORT=8000
MCP_LOG_LEVEL=INFO
MCP_LOG_FILE=/var/log/mcp/server.log

# Django Configuration (inherited from main project)
DJANGO_SETTINGS_MODULE=SocialManager.settings.production

# Debug Mode
DEBUG=False

# Database (if using separate pool)
# MCP_DATABASE_URL=postgresql://user:pass@localhost/db

# Optional: Separate Redis for MCP
# MCP_REDIS_URL=redis://localhost:6379/2
```

### 7. Create Development Helper Scripts

Create `scripts/start_mcp_dev.sh`:

```bash
#!/bin/bash
# Development startup script for MCP server

echo "🚀 Starting MCP Server in development mode..."

# Activate virtual environment if exists
if [ -f ".venv/bin/activate" ]; then
    source .venv/bin/activate
fi

# Set development environment
export DJANGO_SETTINGS_MODULE=SocialManager.settings.local
export DEBUG=True
export MCP_LOG_LEVEL=DEBUG

# Run the server with auto-reload
uvicorn mcp_server.main:app --reload --host 0.0.0.0 --port 8000
```

Create `scripts/check_mcp_deps.py`:

```python
#!/usr/bin/env python
"""Check if all MCP dependencies are properly installed"""

import sys

def check_imports():
    """Check required imports"""
    required_modules = [
        ("fastmcp", "FastMCP"),
        ("uvicorn", "Uvicorn"),
        ("pydantic", "Pydantic"),
        ("asyncpg", "AsyncPG"),
        ("httpx", "HTTPX"),
        ("asgiref.sync", "Django Async Support"),
    ]
    
    missing = []
    
    for module, name in required_modules:
        try:
            __import__(module)
            print(f"✅ {name} installed")
        except ImportError:
            print(f"❌ {name} missing")
            missing.append(name)
    
    if missing:
        print(f"\n❌ Missing dependencies: {', '.join(missing)}")
        print("Run: uv pip install -r requirements.txt")
        sys.exit(1)
    else:
        print("\n✅ All dependencies installed!")

if __name__ == "__main__":
    check_imports()
```

Make scripts executable:
```bash
chmod +x scripts/start_mcp_dev.sh
chmod +x scripts/check_mcp_deps.py
```

## Verification Steps

1. Run dependency check:
   ```bash
   python scripts/check_mcp_deps.py
   ```

2. Verify Django can be imported:
   ```bash
   python -c "import os; os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'SocialManager.settings.local'); import django; django.setup(); print('✅ Django setup successful')"
   ```

3. Check directory structure:
   ```bash
   tree mcp_server/
   ```

## Success Criteria

- [ ] All dependencies installed successfully
- [ ] Directory structure created
- [ ] Configuration files in place
- [ ] Environment variables documented
- [ ] Helper scripts working
- [ ] Django integration verified

## Notes

- FastMCP is the chosen framework for its async support and simple API
- We're using uvicorn as the ASGI server for production-ready performance
- Pydantic 2.5+ is required for the latest validation features
- AsyncPG is optional but recommended for better async database performance

## Next Steps

Proceed to **002_server_foundation.md** to implement the basic server structure with Django integration.