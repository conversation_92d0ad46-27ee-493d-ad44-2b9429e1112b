# Subtask 003: Error Handling and Validation Framework

## Objective
Implement a unified error handling system and comprehensive Pydantic validation models for all task parameters to ensure type safety and consistent error responses.

## Prerequisites
- Completed subtasks 001-002
- Understanding of Pydantic v2 validation
- Familiarity with FastMCP error handling

## Implementation Steps

### 1. Create Unified Error Models

Create `mcp_server/errors.py`:

```python
"""
Unified error handling for MCP server
All errors follow consistent format for client predictability
"""

from typing import Optional, Dict, Any, List
from pydantic import BaseModel, Field
import traceback
from datetime import datetime
import logging

logger = logging.getLogger(__name__)

class MCPError(BaseModel):
    """
    Unified error response format
    All MCP errors must use this format
    """
    error_type: str = Field(..., description="Error category")
    message: str = Field(..., description="Human-readable error message")
    details: Optional[Dict[str, Any]] = Field(None, description="Additional error details")
    traceback: Optional[str] = Field(None, description="Stack trace (only in debug mode)")
    timestamp: datetime = Field(default_factory=datetime.now)
    request_id: Optional[str] = Field(None, description="Request correlation ID")
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }

# Standard error types
class ErrorTypes:
    """Standard error type constants"""
    VALIDATION_ERROR = "validation_error"
    NOT_FOUND = "not_found"
    AUTHENTICATION_ERROR = "authentication_error"
    PERMISSION_DENIED = "permission_denied"
    INTERNAL_ERROR = "internal_error"
    TASK_ERROR = "task_error"
    INVALID_STATE = "invalid_state"
    RATE_LIMIT = "rate_limit_exceeded"
    TIMEOUT = "timeout"
    CONNECTION_ERROR = "connection_error"
    CONFIGURATION_ERROR = "configuration_error"
    INVALID_TASK_TYPE = "invalid_task_type"
    RETRY_LIMIT_EXCEEDED = "retry_limit_exceeded"

class ValidationErrorDetail(BaseModel):
    """Detail for validation errors"""
    field: str
    message: str
    type: str
    input: Any

def create_error_response(
    error_type: str,
    message: str,
    details: Optional[Dict[str, Any]] = None,
    include_traceback: bool = False,
    request_id: Optional[str] = None
) -> Dict[str, Any]:
    """
    Create standard error response
    
    Args:
        error_type: One of ErrorTypes constants
        message: Human-readable error message
        details: Additional error details
        include_traceback: Whether to include stack trace
        request_id: Request correlation ID
        
    Returns:
        Error response dict
    """
    error = MCPError(
        error_type=error_type,
        message=message,
        details=details,
        request_id=request_id
    )
    
    if include_traceback:
        error.traceback = traceback.format_exc()
    
    return error.dict()

def handle_validation_error(exc: Exception) -> Dict[str, Any]:
    """
    Convert Pydantic ValidationError to standard format
    """
    from pydantic import ValidationError
    
    if isinstance(exc, ValidationError):
        details = []
        for error in exc.errors():
            details.append({
                "field": ".".join(str(loc) for loc in error["loc"]),
                "message": error["msg"],
                "type": error["type"],
                "input": error.get("input")
            })
        
        return create_error_response(
            error_type=ErrorTypes.VALIDATION_ERROR,
            message="Invalid parameters provided",
            details={"errors": details}
        )
    
    # Not a validation error
    return create_error_response(
        error_type=ErrorTypes.INTERNAL_ERROR,
        message=str(exc)
    )
```

### 2. Create Error Handling Decorator

Create `mcp_server/decorators/error_handler.py`:

```python
"""
Error handling decorator for MCP tools
Ensures all errors follow unified format
"""

from functools import wraps
from typing import Dict, Any, Callable
import logging
from contextvars import ContextVar
from pydantic import ValidationError

from ..errors import (
    create_error_response, 
    handle_validation_error,
    ErrorTypes
)
from ..config import DEBUG

logger = logging.getLogger(__name__)

# Context for tracking tool calls
tool_context: ContextVar[str] = ContextVar('tool', default='unknown')
request_id_context: ContextVar[str] = ContextVar('request_id', default=None)

def handle_tool_errors(func: Callable) -> Callable:
    """
    Decorator for unified error handling across all MCP tools
    
    Features:
    - Catches all exceptions and converts to standard format
    - Logs errors with context
    - Includes traceback only in debug mode
    - Tracks tool execution context
    """
    @wraps(func)
    async def wrapper(*args, **kwargs) -> Dict[str, Any]:
        tool_name = func.__name__
        tool_context.set(tool_name)
        
        try:
            # Log tool invocation
            logger.debug(f"Tool invoked: {tool_name} with args: {kwargs}")
            
            # Execute tool
            result = await func(*args, **kwargs)
            
            # If result is already an error, return as-is
            if isinstance(result, dict) and "error_type" in result:
                return result
            
            return result
            
        except ValidationError as e:
            # Handle validation errors specially
            logger.warning(f"Validation error in {tool_name}: {e}")
            return handle_validation_error(e)
            
        except Exception as e:
            # Log the full exception
            logger.exception(f"Error in tool {tool_name}")
            
            # Determine error type
            error_type = ErrorTypes.INTERNAL_ERROR
            
            if "DoesNotExist" in str(type(e)):
                error_type = ErrorTypes.NOT_FOUND
            elif "PermissionDenied" in str(type(e)):
                error_type = ErrorTypes.PERMISSION_DENIED
            elif "Timeout" in str(type(e)):
                error_type = ErrorTypes.TIMEOUT
            
            # Create error response
            return create_error_response(
                error_type=error_type,
                message=str(e),
                include_traceback=DEBUG,
                request_id=request_id_context.get()
            )
    
    return wrapper

def set_request_id(request_id: str):
    """Set request ID for current context"""
    request_id_context.set(request_id)

def get_current_tool() -> str:
    """Get current tool being executed"""
    return tool_context.get()
```

### 3. Create Base Validation Models

Create `mcp_server/validators/base.py`:

```python
"""
Base validation models and utilities
"""

from pydantic import BaseModel, Field, field_validator
from typing import Optional, List, Dict, Any
from datetime import datetime

class BaseTaskParams(BaseModel):
    """Base class for all task parameter models"""
    
    class Config:
        # Pydantic v2 configuration
        str_strip_whitespace = True
        validate_assignment = True
        use_enum_values = True
        json_schema_extra = {
            "example": {}
        }

class PaginationParams(BaseModel):
    """Common pagination parameters"""
    limit: int = Field(20, ge=1, le=100, description="Number of items to return")
    offset: int = Field(0, ge=0, description="Number of items to skip")

class DateRangeParams(BaseModel):
    """Common date range parameters"""
    start_date: Optional[datetime] = Field(None, description="Start date (inclusive)")
    end_date: Optional[datetime] = Field(None, description="End date (inclusive)")
    
    @field_validator('end_date')
    @classmethod
    def validate_date_range(cls, v: Optional[datetime], info) -> Optional[datetime]:
        """Ensure end_date is after start_date"""
        if v and info.data.get('start_date'):
            if v < info.data['start_date']:
                raise ValueError('end_date must be after start_date')
        return v

def normalize_username(username: str) -> str:
    """Normalize social media username"""
    return username.strip().lower().lstrip('@')

def validate_username_list(usernames: List[str]) -> List[str]:
    """Validate and normalize list of usernames"""
    if not usernames:
        raise ValueError("At least one username is required")
    
    # Normalize all usernames
    normalized = [normalize_username(u) for u in usernames]
    
    # Remove duplicates while preserving order
    seen = set()
    unique = []
    for u in normalized:
        if u not in seen:
            seen.add(u)
            unique.append(u)
    
    return unique
```

### 4. Create Instagram Validation Models

Create `mcp_server/validators/instagram.py`:

```python
"""
Instagram-specific parameter validation models
"""

from typing import Optional, List
from datetime import datetime
from pydantic import BaseModel, Field, field_validator

from .base import BaseTaskParams, normalize_username, validate_username_list

# Instagram username constraints
INSTAGRAM_USERNAME_MIN = 1
INSTAGRAM_USERNAME_MAX = 30
INSTAGRAM_MAX_BATCH_SIZE = 50

class InstagramProfileTaskParams(BaseTaskParams):
    """Parameters for importing a single Instagram profile"""
    username: str = Field(
        ..., 
        min_length=INSTAGRAM_USERNAME_MIN,
        max_length=INSTAGRAM_USERNAME_MAX,
        description="Instagram username to import"
    )
    
    @field_validator('username')
    @classmethod
    def normalize_username(cls, v: str) -> str:
        return normalize_username(v)

class InstagramPostsTaskParams(BaseTaskParams):
    """Parameters for importing posts from a single profile"""
    username: str = Field(
        ...,
        min_length=INSTAGRAM_USERNAME_MIN,
        max_length=INSTAGRAM_USERNAME_MAX,
        description="Instagram username"
    )
    limit: Optional[int] = Field(100, ge=1, le=1000, description="Maximum posts to import")
    start_date: Optional[datetime] = Field(None, description="Import posts from this date")
    end_date: Optional[datetime] = Field(None, description="Import posts until this date")
    post_types: Optional[List[str]] = Field(
        default_factory=list,
        description="Filter by post types (photo, video, album, reel)"
    )
    
    @field_validator('username')
    @classmethod
    def normalize_username(cls, v: str) -> str:
        return normalize_username(v)
    
    @field_validator('post_types')
    @classmethod
    def validate_post_types(cls, v: List[str]) -> List[str]:
        valid_types = {'photo', 'video', 'album', 'reel', 'story'}
        for post_type in v:
            if post_type not in valid_types:
                raise ValueError(f"Invalid post type: {post_type}")
        return v

class InstagramBatchPostsTaskParams(BaseTaskParams):
    """Parameters for batch importing posts from multiple profiles"""
    usernames: List[str] = Field(
        ...,
        min_items=1,
        max_items=INSTAGRAM_MAX_BATCH_SIZE,
        description="List of Instagram usernames"
    )
    batch_size: int = Field(
        10,
        ge=1,
        le=20,
        description="Number of profiles to process in parallel"
    )
    limit: Optional[int] = Field(
        None,
        ge=1,
        le=1000,
        description="Maximum posts per profile"
    )
    start_date: Optional[datetime] = None
    end_date: Optional[datetime] = None
    post_types: Optional[List[str]] = Field(default_factory=list)
    import_comments: bool = Field(False, description="Import comments for posts")
    skip_media_download: bool = Field(False, description="Skip downloading media files")
    save_media_to_gcs: bool = Field(False, description="Save media to Google Cloud Storage")
    import_all_users: bool = Field(False, description="Import all mentioned users")
    
    @field_validator('usernames')
    @classmethod
    def validate_usernames(cls, v: List[str]) -> List[str]:
        return validate_username_list(v)

class InstagramCommentsTaskParams(BaseTaskParams):
    """Parameters for importing comments for posts"""
    post_id: int = Field(..., gt=0, description="Instagram post ID")
    limit: Optional[int] = Field(100, ge=1, le=1000, description="Maximum comments to import")
    include_replies: bool = Field(True, description="Include comment replies")
    
class PostCommentParams(BaseModel):
    """Parameters for posting a comment"""
    username: str = Field(..., min_length=1, description="Instagram account username")
    password: str = Field(..., min_length=1, description="Instagram account password")
    post_url: str = Field(..., description="Instagram post URL")
    comment_text: str = Field(
        ...,
        min_length=1,
        max_length=2200,
        description="Comment text to post"
    )
    
    @field_validator('post_url')
    @classmethod
    def validate_post_url(cls, v: str) -> str:
        if not v.startswith('https://www.instagram.com/'):
            raise ValueError("Invalid Instagram post URL")
        return v
```

### 5. Create Telegram Validation Models

Create `mcp_server/validators/telegram.py`:

```python
"""
Telegram-specific parameter validation models
"""

from typing import Optional
from datetime import datetime
from pydantic import Field, field_validator

from .base import BaseTaskParams

class TelegramChatsTaskParams(BaseTaskParams):
    """Parameters for importing Telegram chats"""
    chat_limit: str = Field(
        "all",
        description="Number of chats to import ('all' or number)"
    )
    custom_limit: Optional[int] = Field(
        None,
        ge=1,
        description="Custom limit when chat_limit is 'custom'"
    )
    include_private: bool = Field(True, description="Include private chats")
    include_groups: bool = Field(True, description="Include group chats")
    include_supergroups: bool = Field(True, description="Include supergroups")
    include_channels: bool = Field(True, description="Include channels")
    
    @field_validator('custom_limit')
    @classmethod
    def validate_custom_limit(cls, v: Optional[int], info) -> Optional[int]:
        """Validate custom limit when required"""
        if info.data.get('chat_limit') == 'custom' and not v:
            raise ValueError("custom_limit required when chat_limit is 'custom'")
        return v

class TelegramMessagesTaskParams(BaseTaskParams):
    """Parameters for importing Telegram messages"""
    category: str = Field(
        "all",
        pattern="^(all|today|week|month|custom)$",
        description="Time range category"
    )
    date_from: Optional[datetime] = Field(None, description="Start date for custom range")
    date_to: Optional[datetime] = Field(None, description="End date for custom range")
    message_limit: int = Field(
        1000,
        ge=1,
        le=100000,
        description="Maximum messages per chat"
    )
    skip_user_fetch: bool = Field(
        False,
        description="Skip fetching user details"
    )
    
    @field_validator('date_from')
    @classmethod
    def validate_dates(cls, v: Optional[datetime], info) -> Optional[datetime]:
        """Validate dates for custom category"""
        if info.data.get('category') == 'custom':
            if not v or not info.data.get('date_to'):
                raise ValueError("Both date_from and date_to required for custom category")
        return v

class TelegramUsersTaskParams(BaseTaskParams):
    """Parameters for importing Telegram users"""
    extract_from_messages: bool = Field(
        False,
        description="Extract users from existing messages"
    )
    from_chat: Optional[int] = Field(
        None,
        description="Import users from specific chat ID"
    )
    update_existing: bool = Field(
        False,
        description="Update existing user records"
    )
    limit: Optional[int] = Field(
        None,
        ge=1,
        description="Maximum users to import"
    )
```

### 6. Create Validator Registry

Create `mcp_server/validators/registry.py`:

```python
"""
Central registry for all parameter validators
Maps task types to their validation models
"""

from typing import Dict, Type
from pydantic import BaseModel

# Import all validators
from .instagram import (
    InstagramProfileTaskParams,
    InstagramPostsTaskParams,
    InstagramBatchPostsTaskParams,
    InstagramCommentsTaskParams
)
from .telegram import (
    TelegramChatsTaskParams,
    TelegramMessagesTaskParams,
    TelegramUsersTaskParams
)

# Parameter validator registry
PARAM_VALIDATORS: Dict[str, Type[BaseModel]] = {
    # Instagram validators
    "instagram.profile": InstagramProfileTaskParams,
    "instagram.posts": InstagramPostsTaskParams,
    "instagram.batch_posts": InstagramBatchPostsTaskParams,
    "instagram.comments": InstagramCommentsTaskParams,
    
    # Telegram validators
    "telegram.chats": TelegramChatsTaskParams,
    "telegram.messages": TelegramMessagesTaskParams,
    "telegram.users": TelegramUsersTaskParams,
}

def get_validator(task_type: str) -> Type[BaseModel]:
    """
    Get validator for task type
    
    Args:
        task_type: Task type identifier
        
    Returns:
        Validator class
        
    Raises:
        KeyError: If no validator found
    """
    if task_type not in PARAM_VALIDATORS:
        raise KeyError(f"No validator found for task type: {task_type}")
    return PARAM_VALIDATORS[task_type]

def validate_task_params(task_type: str, params: Dict) -> Dict:
    """
    Validate parameters for a task
    
    Args:
        task_type: Task type identifier
        params: Raw parameters dict
        
    Returns:
        Validated parameters dict
        
    Raises:
        ValidationError: If validation fails
        KeyError: If no validator found
    """
    validator_class = get_validator(task_type)
    validated = validator_class(**params)
    return validated.dict(exclude_unset=True)
```

### 7. Create Validation Tests

Create `tests/test_validation.py`:

```python
"""Test parameter validation"""

import pytest
from datetime import datetime
from pydantic import ValidationError

from mcp_server.validators.instagram import (
    InstagramProfileTaskParams,
    InstagramBatchPostsTaskParams
)
from mcp_server.validators.telegram import TelegramMessagesTaskParams

def test_instagram_profile_validation():
    """Test Instagram profile parameter validation"""
    # Valid params
    params = InstagramProfileTaskParams(username="@TestUser")
    assert params.username == "testuser"  # Normalized
    
    # Invalid - empty username
    with pytest.raises(ValidationError):
        InstagramProfileTaskParams(username="")
    
    # Invalid - too long username
    with pytest.raises(ValidationError):
        InstagramProfileTaskParams(username="a" * 31)

def test_instagram_batch_validation():
    """Test Instagram batch parameter validation"""
    # Valid params
    params = InstagramBatchPostsTaskParams(
        usernames=["@user1", "USER2", "user1"],  # Duplicate
        batch_size=5
    )
    assert len(params.usernames) == 2  # Deduplicated
    assert params.usernames == ["user1", "user2"]
    
    # Invalid - empty list
    with pytest.raises(ValidationError):
        InstagramBatchPostsTaskParams(usernames=[])
    
    # Invalid - too many usernames
    with pytest.raises(ValidationError):
        InstagramBatchPostsTaskParams(
            usernames=[f"user{i}" for i in range(51)]
        )

def test_telegram_messages_validation():
    """Test Telegram messages parameter validation"""
    # Valid - all messages
    params = TelegramMessagesTaskParams(category="all")
    assert params.category == "all"
    
    # Valid - custom range
    params = TelegramMessagesTaskParams(
        category="custom",
        date_from=datetime(2024, 1, 1),
        date_to=datetime(2024, 1, 31)
    )
    assert params.date_from is not None
    
    # Invalid - custom without dates
    with pytest.raises(ValidationError):
        TelegramMessagesTaskParams(category="custom")
    
    # Invalid - invalid category
    with pytest.raises(ValidationError):
        TelegramMessagesTaskParams(category="invalid")

def test_error_response_format():
    """Test error response formatting"""
    from mcp_server.errors import create_error_response, ErrorTypes
    
    error = create_error_response(
        error_type=ErrorTypes.VALIDATION_ERROR,
        message="Test error",
        details={"field": "username", "issue": "too short"}
    )
    
    assert error["error_type"] == "validation_error"
    assert error["message"] == "Test error"
    assert "timestamp" in error
    assert error["details"]["field"] == "username"
```

## Usage Example

```python
# In MCP tool implementation
from mcp_server.decorators.error_handler import handle_tool_errors
from mcp_server.validators.registry import validate_task_params

@mcp.tool()
@handle_tool_errors
async def task_create_import(
    task_type: str,
    parameters: Dict[str, Any]
) -> Dict[str, Any]:
    """Create an import task with validation"""
    
    # Validate parameters - will raise ValidationError if invalid
    validated_params = validate_task_params(task_type, parameters)
    
    # Create task with validated params
    # ... rest of implementation
```

## Verification Steps

1. Run validation tests:
   ```bash
   pytest tests/test_validation.py -v
   ```

2. Test error handling:
   ```python
   from mcp_server.errors import handle_validation_error
   from pydantic import ValidationError
   
   try:
       # This will fail
       from mcp_server.validators.instagram import InstagramProfileTaskParams
       params = InstagramProfileTaskParams(username="")
   except ValidationError as e:
       error = handle_validation_error(e)
       print(error)
   ```

## Success Criteria

- [ ] Unified error model implemented
- [ ] Error handling decorator working
- [ ] All task parameter validators created
- [ ] Validator registry functional
- [ ] Tests passing for all validators
- [ ] Error responses follow consistent format

## Notes

- All errors must use MCPError format for consistency
- Validation happens before any task execution
- Usernames are always normalized (lowercase, no @)
- Date ranges are validated for logical consistency
- Stack traces only included in debug mode

## Next Steps

Proceed to **004_database_decorators.md** to implement async/sync decorators and database operation handling.