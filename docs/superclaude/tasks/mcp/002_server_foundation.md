# Subtask 002: Server Foundation and Django Integration

## Objective
Create the basic MCP server structure with proper Django integration, including the main server file, Django setup, and static task registry.

## Prerequisites
- Completed subtask 001 (Initial Setup)
- Django project configured
- Understanding of FastMCP framework

## Implementation Steps

### 1. Create Main Server File

Create `mcp_server/main.py`:

```python
import os
import sys
from pathlib import Path

# Add project root to Python path BEFORE any imports
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# Django setup - MUST be before any Django imports
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "SocialManager.settings.production")
import django
django.setup()

# Now we can import Django-dependent modules
from fastmcp import FastMCP
from asgiref.sync import sync_to_async
from contextvars import ContextVar
import logging

# Import configuration
from .config import MCP_SERVER_NAME, MCP_SERVER_VERSION, DEBUG
from .logging_config import setup_logging

# Import task classes - STATIC imports only
from instagram_manager.tasks.import_tasks import (
    ImportInstagramProfileTask,
    ImportInstagramPostsTask,
    ImportBatchPostsTask,
    ImportInstagramCommentsTask
)
from telegram_manager.tasks.import_tasks import (
    ImportTelegramChatsTask,
    ImportTelegramMessagesTask,
    ImportTelegramUsersTask
)
from core.tasks.base import BaseTask

# Setup logging
logger = setup_logging()

# Initialize MCP server
mcp = FastMCP(MCP_SERVER_NAME, version=MCP_SERVER_VERSION)

# Context variable for tracking DB calls
db_call_context: ContextVar[str] = ContextVar('db_call', default='unknown')

# CRITICAL: Static task registry - NO dynamic imports
TASK_REGISTRY = {
    "instagram.profile": ImportInstagramProfileTask,
    "instagram.posts": ImportInstagramPostsTask,
    "instagram.batch_posts": ImportBatchPostsTask,
    "instagram.comments": ImportInstagramCommentsTask,
    "telegram.chats": ImportTelegramChatsTask,
    "telegram.messages": ImportTelegramMessagesTask,
    "telegram.users": ImportTelegramUsersTask,
}

# Log server initialization
logger.info(f"🚀 {MCP_SERVER_NAME} v{MCP_SERVER_VERSION} initialized")
logger.info(f"📋 Registered {len(TASK_REGISTRY)} task types")
logger.info(f"🐛 Debug mode: {DEBUG}")

# Export app for uvicorn
app = mcp.get_asgi_app()
```

### 2. Create Task Registry Module

Create `mcp_server/task_registry.py`:

```python
"""
Static task registry for MCP server
CRITICAL: All tasks must be imported statically - NO dynamic imports
"""

from typing import Dict, Type
from core.tasks.base import BaseTask

# Import all task classes
from instagram_manager.tasks.import_tasks import (
    ImportInstagramProfileTask,
    ImportInstagramPostsTask,
    ImportBatchPostsTask,
    ImportInstagramCommentsTask
)
from telegram_manager.tasks.import_tasks import (
    ImportTelegramChatsTask,
    ImportTelegramMessagesTask,
    ImportTelegramUsersTask
)

# Task registry with full type annotations
TASK_REGISTRY: Dict[str, Type[BaseTask]] = {
    # Instagram tasks
    "instagram.profile": ImportInstagramProfileTask,
    "instagram.posts": ImportInstagramPostsTask,
    "instagram.batch_posts": ImportBatchPostsTask,
    "instagram.comments": ImportInstagramCommentsTask,
    
    # Telegram tasks
    "telegram.chats": ImportTelegramChatsTask,
    "telegram.messages": ImportTelegramMessagesTask,
    "telegram.users": ImportTelegramUsersTask,
}

# Task descriptions for documentation
TASK_DESCRIPTIONS = {
    "instagram.profile": "Import Instagram profile information",
    "instagram.posts": "Import posts from a single Instagram profile",
    "instagram.batch_posts": "Import posts from multiple Instagram profiles in batch",
    "instagram.comments": "Import comments for Instagram posts",
    "telegram.chats": "Import Telegram chats/channels",
    "telegram.messages": "Import messages from Telegram chats",
    "telegram.users": "Import users from Telegram chats",
}

def get_task_class(task_type: str) -> Type[BaseTask]:
    """
    Get task class by type
    
    Args:
        task_type: Task type identifier
        
    Returns:
        Task class
        
    Raises:
        KeyError: If task type not found
    """
    if task_type not in TASK_REGISTRY:
        raise KeyError(f"Unknown task type: {task_type}")
    return TASK_REGISTRY[task_type]

def list_task_types() -> list[str]:
    """Get list of all available task types"""
    return list(TASK_REGISTRY.keys())

def validate_task_type(task_type: str) -> bool:
    """Check if task type is valid"""
    return task_type in TASK_REGISTRY
```

### 3. Create Django Integration Module

Create `mcp_server/django_integration.py`:

```python
"""
Django integration utilities for MCP server
Ensures proper async/sync boundary handling
"""

import os
import django
from django.conf import settings
from pathlib import Path
import logging

logger = logging.getLogger(__name__)

def setup_django():
    """
    Setup Django for MCP server
    Must be called before any Django imports
    """
    # Check if Django is already configured
    if settings.configured:
        logger.debug("Django already configured")
        return
    
    # Set default settings module
    os.environ.setdefault(
        "DJANGO_SETTINGS_MODULE", 
        "SocialManager.settings.production"
    )
    
    # Setup Django
    try:
        django.setup()
        logger.info("✅ Django setup successful")
        
        # Log database configuration (without sensitive data)
        db_config = settings.DATABASES.get('default', {})
        logger.info(f"📊 Database engine: {db_config.get('ENGINE', 'Unknown')}")
        logger.info(f"📊 Database name: {db_config.get('NAME', 'Unknown')}")
        
    except Exception as e:
        logger.error(f"❌ Django setup failed: {e}")
        raise

def configure_database_pool():
    """
    Configure database connection pooling for async operations
    """
    if not hasattr(settings, 'DATABASES'):
        logger.warning("No database configuration found")
        return
    
    # Add connection pool settings
    pool_config = {
        'connect_timeout': 10,
        'keepalives': 1,
        'keepalives_idle': 30,
        'keepalives_interval': 10,
        'keepalives_count': 5,
    }
    
    # Apply to default database
    if 'default' in settings.DATABASES:
        if 'OPTIONS' not in settings.DATABASES['default']:
            settings.DATABASES['default']['OPTIONS'] = {}
        
        settings.DATABASES['default']['OPTIONS'].update(pool_config)
        logger.info("✅ Database connection pooling configured")
    
    # Log pool configuration
    logger.debug(f"Pool config: {pool_config}")

def check_django_apps():
    """
    Verify required Django apps are installed
    """
    required_apps = [
        'core',
        'instagram_manager',
        'telegram_manager',
    ]
    
    missing_apps = []
    for app in required_apps:
        if app not in settings.INSTALLED_APPS:
            missing_apps.append(app)
    
    if missing_apps:
        logger.error(f"❌ Missing required Django apps: {missing_apps}")
        raise RuntimeError(f"Missing required apps: {missing_apps}")
    
    logger.info("✅ All required Django apps found")

def check_celery_configuration():
    """
    Verify Celery is properly configured
    """
    celery_settings = [
        ('CELERY_BROKER_URL', 'Celery broker'),
        ('CELERY_RESULT_BACKEND', 'Celery result backend'),
    ]
    
    missing_settings = []
    for setting, name in celery_settings:
        if not hasattr(settings, setting) or not getattr(settings, setting):
            missing_settings.append(name)
    
    if missing_settings:
        logger.warning(f"⚠️  Missing Celery settings: {missing_settings}")
        logger.warning("Task execution may not work properly")
    else:
        logger.info("✅ Celery configuration found")
```

### 4. Create Server Runner Script

Create `start_mcp.py` in project root:

```python
#!/usr/bin/env python
"""
MCP Server Runner
Validates configuration and starts the server
"""

import sys
import os
from pathlib import Path

# Add project to path
sys.path.insert(0, str(Path(__file__).parent))

# Import after path setup
import uvicorn
from mcp_server.main import app, TASK_REGISTRY
from mcp_server.validators.registry import PARAM_VALIDATORS
from mcp_server.django_integration import (
    setup_django, 
    configure_database_pool,
    check_django_apps,
    check_celery_configuration
)
from mcp_server.config import (
    MCP_SERVER_HOST, 
    MCP_SERVER_PORT,
    VALIDATE_ON_STARTUP
)
from core.tasks.base import BaseTask
import logging

logger = logging.getLogger(__name__)

def validate_task_registry():
    """Validate all tasks at server startup"""
    print("🔍 Validating task registry...")
    
    errors = []
    
    for task_type, task_class in TASK_REGISTRY.items():
        # Check that class inherits from BaseTask
        if not issubclass(task_class, BaseTask):
            errors.append(f"Task {task_type} must inherit from BaseTask")
        
        # Check required methods
        required_methods = ['validate_params', 'execute_task']
        for method in required_methods:
            if not hasattr(task_class, method):
                errors.append(f"Task {task_type} missing {method} method")
        
        # Check parameter validator exists
        if task_type not in PARAM_VALIDATORS:
            errors.append(f"No parameter validator for {task_type}")
    
    if errors:
        print("❌ Task registry validation failed:")
        for error in errors:
            print(f"   - {error}")
        sys.exit(1)
    
    print(f"✅ Validated {len(TASK_REGISTRY)} task types")

def startup_checks():
    """Run all startup checks"""
    print("🚀 Starting MCP Server startup checks...")
    
    try:
        # Django setup
        setup_django()
        
        # Configure database
        configure_database_pool()
        
        # Check required apps
        check_django_apps()
        
        # Check Celery
        check_celery_configuration()
        
        # Validate tasks
        if VALIDATE_ON_STARTUP:
            validate_task_registry()
        
        print("✅ All startup checks passed!")
        
    except Exception as e:
        print(f"❌ Startup check failed: {e}")
        sys.exit(1)

def main():
    """Main entry point"""
    # Run startup checks
    startup_checks()
    
    # Start server
    print(f"\n🚀 Starting MCP server on http://{MCP_SERVER_HOST}:{MCP_SERVER_PORT}")
    print("📝 Press CTRL+C to stop\n")
    
    uvicorn.run(
        app,
        host=MCP_SERVER_HOST,
        port=MCP_SERVER_PORT,
        log_level="info",
        access_log=True,
        use_colors=True
    )

if __name__ == "__main__":
    main()
```

### 5. Create Development Configuration Override

Create `mcp_server/config_dev.py`:

```python
"""
Development configuration overrides
"""

from .config import *

# Override for development
DEBUG = True
LOG_LEVEL = "DEBUG"

# Use local Django settings
import os
os.environ["DJANGO_SETTINGS_MODULE"] = "SocialManager.settings.local"

# Development server settings
MCP_SERVER_HOST = "127.0.0.1"

# Disable some validations for faster startup
VALIDATE_ON_STARTUP = False

# More verbose logging
SLOW_QUERY_THRESHOLD = 0.05  # 50ms for development
```

## Testing

Create `test_server_foundation.py`:

```python
"""Test server foundation setup"""

import pytest
import sys
from pathlib import Path

# Add project to path
sys.path.insert(0, str(Path(__file__).parent.parent))

def test_imports():
    """Test all required imports work"""
    try:
        from mcp_server.main import mcp, TASK_REGISTRY
        from mcp_server.task_registry import get_task_class
        from mcp_server.django_integration import setup_django
        assert True
    except ImportError as e:
        pytest.fail(f"Import failed: {e}")

def test_task_registry():
    """Test task registry is properly configured"""
    from mcp_server.task_registry import TASK_REGISTRY, list_task_types
    
    # Check registry not empty
    assert len(TASK_REGISTRY) > 0
    
    # Check all tasks are classes
    for task_type, task_class in TASK_REGISTRY.items():
        assert isinstance(task_class, type)
    
    # Check list function
    task_types = list_task_types()
    assert len(task_types) == len(TASK_REGISTRY)

def test_django_setup():
    """Test Django can be setup"""
    from mcp_server.django_integration import setup_django
    
    try:
        setup_django()
        # If we get here, setup worked
        assert True
    except Exception as e:
        pytest.fail(f"Django setup failed: {e}")
```

## Verification Steps

1. Check imports work:
   ```bash
   python -c "from mcp_server.main import mcp, TASK_REGISTRY; print('✅ Imports successful')"
   ```

2. Run basic tests:
   ```bash
   pytest test_server_foundation.py -v
   ```

3. Try starting server (it will fail without all components, but should validate):
   ```bash
   python start_mcp.py
   ```

## Success Criteria

- [ ] Main server file created with proper Django setup
- [ ] Task registry module with static imports
- [ ] Django integration utilities
- [ ] Server runner with validation
- [ ] Basic tests passing
- [ ] No dynamic imports anywhere

## Common Issues

1. **Import errors**: Ensure Django setup happens before any Django imports
2. **Task not found**: Check task class is imported in task_registry.py
3. **Settings error**: Verify DJANGO_SETTINGS_MODULE is set correctly

## Notes

- The order of imports is CRITICAL - Django must be setup first
- All task imports must be static for security and performance
- The task registry is the single source of truth for available tasks
- Context variables will be used for tracking async operations

## Next Steps

Proceed to **003_error_handling_validation.md** to implement unified error handling and Pydantic validation models.