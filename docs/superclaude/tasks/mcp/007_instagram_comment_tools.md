# Subtask 007: Instagram Comment Management Tools

## Objective
Implement MCP tools for Instagram comment management, including retrieval, posting, and moderation features.

## Prerequisites
- Completed subtasks 001-006
- Post management tools implemented

## Implementation Steps

### 1. Create Comment Retrieval Tools

Create `mcp_server/tools/instagram_comments.py`:

```python
"""
Instagram comment management tools
"""

from typing import Dict, Any, Optional, List
from mcp_server.main import mcp
from mcp_server.decorators.error_handler import handle_tool_errors
from mcp_server.decorators.database import async_db_operation, track_sync_calls

@mcp.tool()
@handle_tool_errors
async def instagram_get_comments(
    post_id: int,
    limit: int = 50,
    offset: int = 0,
    include_replies: bool = True,
    order_by: str = "-commented_at"
) -> Dict[str, Any]:
    """
    Get comments for a specific Instagram post
    
    Args:
        post_id: Instagram post ID
        limit: Number of comments (1-100)
        offset: Skip comments
        include_replies: Include comment replies
        order_by: Sort order (-commented_at, like_count)
        
    Returns:
        List of comments with details
    """
    # Implementation for comment retrieval
    pass

@mcp.tool()
@handle_tool_errors
async def instagram_post_comment(
    username: str,
    password: str,
    post_url: str,
    comment_text: str,
    reply_to_comment_id: Optional[int] = None
) -> Dict[str, Any]:
    """
    Post a comment to an Instagram post
    
    Args:
        username: Instagram account username
        password: Instagram account password
        post_url: URL of the post
        comment_text: Comment text (max 2200 chars)
        reply_to_comment_id: ID to reply to
        
    Returns:
        Posted comment details
    """
    # Implementation for posting comments
    pass

@mcp.tool()
@handle_tool_errors
async def instagram_bulk_post_comments(
    username: str,
    password: str,
    comments: List[Dict[str, str]],
    delay_seconds: int = 30
) -> Dict[str, Any]:
    """
    Post multiple comments with delays
    
    Args:
        username: Instagram account username
        password: Instagram account password
        comments: List of {post_url, comment_text}
        delay_seconds: Delay between comments
        
    Returns:
        Bulk posting results
    """
    # Implementation for bulk commenting
    pass
```

### 2. Key Features to Implement

1. **Comment Retrieval**:
   - Get comments with pagination
   - Include/exclude replies
   - Filter by date, likes
   - Sort options

2. **Comment Posting**:
   - Single comment posting
   - Reply to comments
   - Bulk commenting with delays
   - Rate limit handling

3. **Comment Analytics**:
   - Sentiment analysis
   - Comment statistics
   - Top commenters

4. **Moderation Tools**:
   - Filter spam comments
   - Detect inappropriate content
   - Bulk actions

## Security Considerations

- Encrypt stored passwords
- Rate limit compliance
- Account rotation support
- Error handling for blocks

## Next Steps

Proceed to **008_telegram_chat_tools.md** for Telegram chat management.