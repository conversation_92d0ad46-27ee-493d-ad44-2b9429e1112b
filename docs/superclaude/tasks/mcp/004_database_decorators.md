# Subtask 004: Database Decorators and Async/Sync Handling

## Objective
Implement decorators for safe async/sync database operations, performance tracking, and connection pooling to ensure thread-safe Django ORM usage in async MCP context.

## Prerequisites
- Completed subtasks 001-003
- Understanding of Django async/sync boundaries
- Knowledge of asyncio and threading

## Implementation Steps

### 1. Create Base Database Decorators

Create `mcp_server/decorators/database.py`:

```python
"""
Database operation decorators for safe async/sync handling
CRITICAL: All Django ORM operations must use these decorators
"""

import time
import asyncio
from functools import wraps
from typing import Callable, TypeVar, Any
from contextvars import ContextVar
import logging

from asgiref.sync import sync_to_async
from django.db import transaction, connection

from ..config import SLOW_QUERY_THRESHOLD, DEBUG

logger = logging.getLogger(__name__)

# Context variables for tracking
db_operation_context: ContextVar[str] = ContextVar('db_operation', default='unknown')
query_count_context: ContextVar[int] = ContextVar('query_count', default=0)

T = TypeVar('T')

def async_db_operation(func: Callable[..., T]) -> Callable[..., asyncio.Future[T]]:
    """
    Decorator to safely convert sync Django ORM operations to async
    
    CRITICAL: Use this for ALL database operations in MCP tools
    
    Features:
    - Thread-safe async conversion
    - Automatic transaction handling
    - Performance tracking
    - Context preservation
    """
    @wraps(func)
    async def wrapper(*args, **kwargs) -> T:
        operation_name = f"{func.__module__}.{func.__name__}"
        db_operation_context.set(operation_name)
        
        try:
            # Convert to async with thread sensitivity
            result = await sync_to_async(func, thread_sensitive=True)(*args, **kwargs)
            return result
            
        except Exception as e:
            logger.error(f"Database operation failed: {operation_name}", exc_info=True)
            raise
        finally:
            # Reset context
            db_operation_context.set('unknown')
    
    return wrapper

def track_sync_calls(func: Callable[..., T]) -> Callable[..., T]:
    """
    Decorator for tracking synchronous database calls
    
    Features:
    - Slow query detection
    - Query counting
    - Debug logging
    """
    @wraps(func)
    def wrapper(*args, **kwargs) -> T:
        start_time = time.time()
        operation = db_operation_context.get()
        
        # Increment query count
        current_count = query_count_context.get()
        query_count_context.set(current_count + 1)
        
        try:
            # Execute the function
            result = func(*args, **kwargs)
            
            # Calculate duration
            duration = time.time() - start_time
            
            # Log slow queries
            if duration > SLOW_QUERY_THRESHOLD:
                logger.warning(
                    f"Slow DB operation in {operation}: "
                    f"{func.__name__} took {duration:.2f}s"
                )
            elif DEBUG:
                logger.debug(
                    f"DB operation {operation}: "
                    f"{func.__name__} completed in {duration:.3f}s"
                )
            
            return result
            
        except Exception as e:
            duration = time.time() - start_time
            logger.error(
                f"DB operation failed in {operation}: "
                f"{func.__name__} after {duration:.2f}s",
                exc_info=True
            )
            raise
    
    return wrapper

def transactional(func: Callable[..., T]) -> Callable[..., T]:
    """
    Decorator to wrap function in database transaction
    
    Use for operations that need atomic execution
    """
    @wraps(func)
    def wrapper(*args, **kwargs) -> T:
        with transaction.atomic():
            return func(*args, **kwargs)
    
    return wrapper

def async_transactional(func: Callable[..., T]) -> Callable[..., asyncio.Future[T]]:
    """
    Async version of transactional decorator
    """
    @wraps(func)
    async def wrapper(*args, **kwargs) -> T:
        @transactional
        def sync_func(*args, **kwargs):
            return func(*args, **kwargs)
        
        return await sync_to_async(sync_func, thread_sensitive=True)(*args, **kwargs)
    
    return wrapper
```

### 2. Create Repository Decorators

Create `mcp_server/decorators/repository.py`:

```python
"""
Repository-specific decorators for optimized queries
"""

from functools import wraps
from typing import Callable, List, Optional
import logging

from django.db.models import QuerySet, Prefetch
from .database import async_db_operation, track_sync_calls

logger = logging.getLogger(__name__)

def prefetch_related(*lookups: str):
    """
    Decorator to add prefetch_related to queryset
    Reduces N+1 queries
    """
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(self, *args, **kwargs):
            queryset = func(self, *args, **kwargs)
            if isinstance(queryset, QuerySet):
                return queryset.prefetch_related(*lookups)
            return queryset
        return wrapper
    return decorator

def select_related(*fields: str):
    """
    Decorator to add select_related to queryset
    Reduces query count for foreign keys
    """
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(self, *args, **kwargs):
            queryset = func(self, *args, **kwargs)
            if isinstance(queryset, QuerySet):
                return queryset.select_related(*fields)
            return queryset
        return wrapper
    return decorator

def cached_property(func: Callable) -> property:
    """
    Decorator for caching property results
    Useful for expensive calculations
    """
    attr_name = f'_cached_{func.__name__}'
    
    @wraps(func)
    def wrapper(self):
        if not hasattr(self, attr_name):
            setattr(self, attr_name, func(self))
        return getattr(self, attr_name)
    
    return property(wrapper)

def bulk_operation(chunk_size: int = 1000):
    """
    Decorator for bulk database operations
    Automatically chunks large datasets
    """
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(self, items: List, *args, **kwargs):
            if not items:
                return []
            
            results = []
            total = len(items)
            
            for i in range(0, total, chunk_size):
                chunk = items[i:i + chunk_size]
                logger.debug(
                    f"Processing chunk {i//chunk_size + 1} "
                    f"({len(chunk)} items)"
                )
                chunk_results = func(self, chunk, *args, **kwargs)
                results.extend(chunk_results)
            
            return results
        return wrapper
    return decorator
```

### 3. Create Connection Pool Manager

Create `mcp_server/database/pool.py`:

```python
"""
Database connection pool management for async operations
Optional: Use for direct async database access
"""

import asyncio
import asyncpg
from typing import Optional
import logging
from contextlib import asynccontextmanager

from django.conf import settings
from ..config import DB_POOL_MIN_SIZE, DB_POOL_MAX_SIZE, DB_COMMAND_TIMEOUT

logger = logging.getLogger(__name__)

class AsyncDatabasePool:
    """
    Async database connection pool manager
    Use when Django ORM is not sufficient
    """
    
    def __init__(self):
        self.pool: Optional[asyncpg.Pool] = None
        self._lock = asyncio.Lock()
    
    async def initialize(self):
        """Initialize the connection pool"""
        if self.pool is not None:
            return
        
        async with self._lock:
            if self.pool is not None:
                return
            
            # Get database config from Django
            db_config = settings.DATABASES['default']
            
            # Create pool
            self.pool = await asyncpg.create_pool(
                host=db_config.get('HOST', 'localhost'),
                port=db_config.get('PORT', 5432),
                user=db_config['USER'],
                password=db_config['PASSWORD'],
                database=db_config['NAME'],
                min_size=DB_POOL_MIN_SIZE,
                max_size=DB_POOL_MAX_SIZE,
                command_timeout=DB_COMMAND_TIMEOUT,
                statement_cache_size=0,  # Disable for compatibility
            )
            
            logger.info(
                f"✅ Async database pool initialized "
                f"(min={DB_POOL_MIN_SIZE}, max={DB_POOL_MAX_SIZE})"
            )
    
    async def close(self):
        """Close the connection pool"""
        if self.pool:
            await self.pool.close()
            self.pool = None
            logger.info("Database pool closed")
    
    @asynccontextmanager
    async def acquire(self):
        """Acquire a connection from the pool"""
        if not self.pool:
            await self.initialize()
        
        async with self.pool.acquire() as connection:
            yield connection
    
    async def execute(self, query: str, *args, timeout: float = None):
        """Execute a query"""
        async with self.acquire() as conn:
            return await conn.execute(query, *args, timeout=timeout)
    
    async def fetch(self, query: str, *args, timeout: float = None):
        """Fetch multiple rows"""
        async with self.acquire() as conn:
            return await conn.fetch(query, *args, timeout=timeout)
    
    async def fetchrow(self, query: str, *args, timeout: float = None):
        """Fetch a single row"""
        async with self.acquire() as conn:
            return await conn.fetchrow(query, *args, timeout=timeout)
    
    async def fetchval(self, query: str, *args, timeout: float = None):
        """Fetch a single value"""
        async with self.acquire() as conn:
            return await conn.fetchval(query, *args, timeout=timeout)

# Global pool instance
_db_pool = AsyncDatabasePool()

async def get_db_pool() -> AsyncDatabasePool:
    """Get the global database pool instance"""
    if not _db_pool.pool:
        await _db_pool.initialize()
    return _db_pool
```

### 4. Create Database Helper Functions

Create `mcp_server/database/helpers.py`:

```python
"""
Database helper functions with proper decorators
These should be used instead of direct ORM calls
"""

from typing import Optional, Dict, Any, List
from django.db.models import Q, Count

from .decorators import async_db_operation, track_sync_calls

# Instagram helpers

@async_db_operation
@track_sync_calls
def get_instagram_profile(username: str):
    """Get Instagram profile by username"""
    from instagram_manager.models import InstagramProfile
    return InstagramProfile.objects.filter(
        username__iexact=username
    ).first()

@async_db_operation
@track_sync_calls
def list_instagram_profiles(
    limit: int = 20,
    offset: int = 0,
    **filters
) -> tuple[List, int]:
    """List Instagram profiles with pagination"""
    from instagram_manager.models import InstagramProfile
    
    queryset = InstagramProfile.objects.all()
    
    # Apply filters
    if filters.get('is_active') is not None:
        queryset = queryset.filter(is_active=filters['is_active'])
    if filters.get('is_verified') is not None:
        queryset = queryset.filter(is_verified=filters['is_verified'])
    
    # Get total count
    total = queryset.count()
    
    # Get paginated results
    profiles = list(queryset[offset:offset + limit])
    
    return profiles, total

@async_db_operation
@track_sync_calls
def get_instagram_posts(
    username: str,
    limit: int = 20,
    offset: int = 0,
    **filters
) -> List:
    """Get posts for Instagram profile"""
    from instagram_manager.models import InstagramPost
    
    queryset = InstagramPost.objects.filter(
        profile__username__iexact=username
    ).select_related('profile').prefetch_related('media', 'hashtags')
    
    # Apply filters
    if filters.get('post_type'):
        queryset = queryset.filter(post_type=filters['post_type'])
    if filters.get('posted_at__gte'):
        queryset = queryset.filter(posted_at__gte=filters['posted_at__gte'])
    if filters.get('posted_at__lte'):
        queryset = queryset.filter(posted_at__lte=filters['posted_at__lte'])
    
    # Order and paginate
    queryset = queryset.order_by('-posted_at')
    
    return list(queryset[offset:offset + limit])

# Telegram helpers

@async_db_operation
@track_sync_calls
def list_telegram_chats(
    limit: int = 20,
    offset: int = 0,
    **filters
) -> tuple[List, int]:
    """List Telegram chats with pagination"""
    from telegram_manager.models import TelegramChat
    
    queryset = TelegramChat.objects.all()
    
    # Apply filters
    if filters.get('chat_type'):
        queryset = queryset.filter(chat_type=filters['chat_type'])
    
    # Get total count
    total = queryset.count()
    
    # Get paginated results
    chats = list(queryset[offset:offset + limit])
    
    return chats, total

@async_db_operation
@track_sync_calls  
def get_telegram_messages(
    chat_id: int,
    limit: int = 50,
    offset: int = 0,
    **filters
) -> List:
    """Get messages from Telegram chat"""
    from telegram_manager.models import TelegramMessage
    
    queryset = TelegramMessage.objects.filter(
        chat__chat_id=chat_id
    ).select_related('chat', 'from_user')
    
    # Apply filters
    if filters.get('date__gte'):
        queryset = queryset.filter(date__gte=filters['date__gte'])
    if filters.get('date__lte'):
        queryset = queryset.filter(date__lte=filters['date__lte'])
    
    # Order and paginate
    queryset = queryset.order_by('-date')
    
    return list(queryset[offset:offset + limit])

# Task helpers

@async_db_operation
@track_sync_calls
def get_task_result(task_id: str):
    """Get task result by ID"""
    from core.models import TaskResult
    return TaskResult.objects.filter(task_id=task_id).first()

@async_db_operation
@track_sync_calls
def list_task_results(
    status: Optional[str] = None,
    task_type: Optional[str] = None,
    limit: int = 20,
    offset: int = 0
) -> tuple[List, int]:
    """List task results with filtering"""
    from core.models import TaskResult
    
    queryset = TaskResult.objects.all()
    
    if status:
        queryset = queryset.filter(status=status)
    if task_type:
        queryset = queryset.filter(task_type=task_type)
    
    # Order by priority and creation date
    queryset = queryset.order_by('-priority', '-created_at')
    
    total = queryset.count()
    tasks = list(queryset[offset:offset + limit])
    
    return tasks, total

@async_db_operation
@track_sync_calls
def update_task_status(task_id: str, status: str, **kwargs):
    """Update task status"""
    from core.models import TaskResult
    
    task = TaskResult.objects.get(task_id=task_id)
    task.status = status
    
    # Update optional fields
    for key, value in kwargs.items():
        if hasattr(task, key):
            setattr(task, key, value)
    
    task.save()
    return task
```

### 5. Create Integration Tests

Create `tests/test_database_decorators.py`:

```python
"""Test database decorators and helpers"""

import pytest
import asyncio
from unittest.mock import Mock, patch

from mcp_server.decorators.database import (
    async_db_operation,
    track_sync_calls,
    transactional
)
from mcp_server.database.helpers import (
    get_instagram_profile,
    list_instagram_profiles
)

@pytest.mark.asyncio
async def test_async_db_operation():
    """Test async database operation decorator"""
    
    @async_db_operation
    @track_sync_calls
    def mock_db_operation():
        return {"result": "success"}
    
    # Should return coroutine
    result = await mock_db_operation()
    assert result == {"result": "success"}

@pytest.mark.asyncio
async def test_database_helpers():
    """Test database helper functions"""
    
    # Mock Django model
    with patch('instagram_manager.models.InstagramProfile') as mock_model:
        mock_profile = Mock()
        mock_profile.username = "testuser"
        mock_model.objects.filter.return_value.first.return_value = mock_profile
        
        # Test get profile
        profile = await get_instagram_profile("testuser")
        assert profile is not None

def test_transactional_decorator():
    """Test transactional decorator"""
    
    @transactional
    def db_operation():
        # This would be wrapped in transaction
        return True
    
    result = db_operation()
    assert result is True
```

## Usage Guidelines

### 1. Always Use Decorators

```python
# CORRECT - Using decorators
@async_db_operation
@track_sync_calls
def get_user_data(user_id: int):
    from myapp.models import User
    return User.objects.get(id=user_id)

# WRONG - Direct sync_to_async
# DON'T DO THIS
async def bad_example():
    user = await sync_to_async(User.objects.get)(id=1)
```

### 2. Optimize Queries

```python
# Use select_related for foreign keys
@async_db_operation
@track_sync_calls
def get_posts_with_author():
    from instagram_manager.models import InstagramPost
    return InstagramPost.objects.select_related('profile').all()

# Use prefetch_related for many-to-many
@async_db_operation
@track_sync_calls
def get_posts_with_media():
    from instagram_manager.models import InstagramPost
    return InstagramPost.objects.prefetch_related('media', 'hashtags').all()
```

### 3. Handle Transactions

```python
# For atomic operations
@async_transactional
async def create_profile_with_posts(profile_data, posts_data):
    # All operations in single transaction
    profile = Profile.objects.create(**profile_data)
    for post_data in posts_data:
        Post.objects.create(profile=profile, **post_data)
    return profile
```

## Verification Steps

1. Test decorator functionality:
   ```bash
   pytest tests/test_database_decorators.py -v
   ```

2. Verify thread safety:
   ```python
   # Should not raise any thread-related errors
   import asyncio
   from mcp_server.database.helpers import list_instagram_profiles
   
   async def test():
       profiles, total = await list_instagram_profiles()
       print(f"Found {total} profiles")
   
   asyncio.run(test())
   ```

## Success Criteria

- [ ] Database decorators implemented
- [ ] Helper functions created
- [ ] Connection pool manager ready
- [ ] Tests passing
- [ ] No thread safety issues

## Common Issues

1. **Thread safety errors**: Ensure `thread_sensitive=True` in sync_to_async
2. **Connection leaks**: Always use context managers for connections
3. **Slow queries**: Check for missing indexes and N+1 queries
4. **Transaction errors**: Don't nest transactions unnecessarily

## Notes

- All database operations MUST use the decorators
- Connection pooling improves concurrent performance
- Helper functions provide a safe abstraction layer

## Next Steps

Proceed to **005_instagram_profile_tools.md** to implement Instagram profile management endpoints.