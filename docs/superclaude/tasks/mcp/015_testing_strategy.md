# Subtask 015: Testing Strategy

## Objective
Implement comprehensive testing strategy for the MCP server, including unit tests, integration tests, end-to-end tests, performance testing, and continuous integration setup.

## Prerequisites
- Completed subtasks 001-014
- All MCP functionality implemented
- Docker configuration ready

## Implementation Steps

### 1. Create Test Configuration

Create `tests/conftest.py`:

```python
"""
Pytest configuration and fixtures for MCP server tests
"""

import pytest
import asyncio
from typing import Async<PERSON>enerator, Generator
from unittest.mock import Mock, patch
import os
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'SocialManager.settings.test')
django.setup()

from django.test import TestCase
from django.contrib.auth import get_user_model
from asgiref.sync import sync_to_async

from mcp_server.main import app, mcp

User = get_user_model()

# Event loop configuration
@pytest.fixture(scope="session")
def event_loop():
    """Create event loop for async tests"""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()

# Database fixtures
@pytest.fixture
async def db():
    """Database fixture for async tests"""
    # This ensures each test has a clean database
    from django.test import TransactionTestCase
    test_case = TransactionTestCase()
    test_case._pre_setup()
    
    yield
    
    # Cleanup
    test_case._post_teardown()

@pytest.fixture
async def user(db):
    """Create test user"""
    return await sync_to_async(User.objects.create_user)(
        username="testuser",
        email="<EMAIL>",
        password="testpass123"
    )

# MCP Server fixtures
@pytest.fixture
async def mcp_client():
    """MCP test client"""
    from fastmcp.testing import TestClient
    
    async with TestClient(mcp) as client:
        yield client

@pytest.fixture
def mock_celery():
    """Mock Celery for testing"""
    with patch('celery.current_app.send_task') as mock_send:
        mock_send.return_value = Mock(id='test-task-id')
        yield mock_send

@pytest.fixture
def mock_brightdata():
    """Mock BrightData API"""
    with patch('instagram_manager.instagram_api.client.BrightDataClient') as mock_client:
        instance = Mock()
        instance.get_profile.return_value = {
            "username": "testuser",
            "follower_count": 1000,
            "is_verified": True
        }
        mock_client.return_value = instance
        yield instance

@pytest.fixture
def mock_redis():
    """Mock Redis for testing"""
    with patch('django.core.cache.cache') as mock_cache:
        storage = {}
        
        def get(key):
            return storage.get(key)
        
        def set(key, value, timeout=None):
            storage[key] = value
            return True
        
        def delete(key):
            storage.pop(key, None)
            return True
        
        mock_cache.get = get
        mock_cache.set = set
        mock_cache.delete = delete
        
        yield mock_cache

# Test data fixtures
@pytest.fixture
def instagram_profile_data():
    """Sample Instagram profile data"""
    return {
        "id": 1,
        "username": "testprofile",
        "full_name": "Test Profile",
        "bio": "Test bio",
        "follower_count": 10000,
        "following_count": 500,
        "post_count": 100,
        "is_verified": True,
        "is_private": False,
        "is_business": True,
        "profile_pic_url": "https://example.com/pic.jpg"
    }

@pytest.fixture
def instagram_post_data():
    """Sample Instagram post data"""
    return {
        "id": 1,
        "external_id": "123456789",
        "content": "Test post content",
        "post_type": "photo",
        "post_url": "https://instagram.com/p/test",
        "like_count": 1000,
        "comment_count": 50,
        "posted_at": "2024-01-01T00:00:00Z"
    }

@pytest.fixture
def task_result_data():
    """Sample task result data"""
    return {
        "task_id": "test-task-123",
        "task_type": "instagram.profile",
        "status": "completed",
        "progress": 100,
        "result": {"imported": True}
    }

# Performance testing fixtures
@pytest.fixture
def performance_monitor():
    """Performance measurement for tests"""
    import time
    
    class PerformanceMonitor:
        def __init__(self):
            self.measurements = {}
        
        def start(self, name: str):
            self.measurements[name] = {
                "start": time.time(),
                "memory_start": self._get_memory_usage()
            }
        
        def stop(self, name: str):
            if name in self.measurements:
                self.measurements[name]["end"] = time.time()
                self.measurements[name]["memory_end"] = self._get_memory_usage()
                self.measurements[name]["duration"] = (
                    self.measurements[name]["end"] - 
                    self.measurements[name]["start"]
                )
                self.measurements[name]["memory_delta"] = (
                    self.measurements[name]["memory_end"] - 
                    self.measurements[name]["memory_start"]
                )
        
        def _get_memory_usage(self):
            import psutil
            return psutil.Process().memory_info().rss / 1024 / 1024  # MB
        
        def get_report(self):
            return self.measurements
    
    return PerformanceMonitor()

# Cleanup
@pytest.fixture(autouse=True)
async def cleanup_after_test():
    """Cleanup after each test"""
    yield
    # Cleanup test data if needed

# Test settings
@pytest.fixture(autouse=True)
def test_settings():
    """Apply test settings"""
    from django.conf import settings
    
    settings.CELERY_TASK_ALWAYS_EAGER = True
    settings.DEBUG = True
    settings.TESTING = True
    
    yield
    
    # Reset settings if needed
```

### 2. Create Unit Tests

Create `tests/unit/test_instagram_tools.py`:

```python
"""
Unit tests for Instagram MCP tools
"""

import pytest
from unittest.mock import Mock, patch

from mcp_server.tools.instagram_profiles import (
    instagram_get_profile,
    instagram_list_profiles,
    instagram_search_profiles
)

class TestInstagramProfileTools:
    """Test Instagram profile tools"""
    
    @pytest.mark.asyncio
    async def test_get_profile_success(self, mock_brightdata, instagram_profile_data):
        """Test successful profile retrieval"""
        # Mock database
        with patch('mcp_server.tools.instagram_profiles.get_profile_by_username') as mock_get:
            mock_get.return_value = Mock(**instagram_profile_data)
            
            # Test
            result = await instagram_get_profile("testprofile")
            
            # Assertions
            assert result["username"] == "testprofile"
            assert result["follower_count"] == 10000
            assert result["is_verified"] is True
            assert "statistics" in result
    
    @pytest.mark.asyncio
    async def test_get_profile_not_found(self):
        """Test profile not found"""
        with patch('mcp_server.tools.instagram_profiles.get_profile_by_username') as mock_get:
            mock_get.return_value = None
            
            result = await instagram_get_profile("nonexistent")
            
            assert result["error_type"] == "not_found"
            assert "not found" in result["message"].lower()
    
    @pytest.mark.asyncio
    async def test_get_profile_username_normalization(self):
        """Test username normalization"""
        with patch('mcp_server.tools.instagram_profiles.get_profile_by_username') as mock_get:
            mock_get.return_value = Mock(username="testuser")
            
            # Test with @ prefix
            await instagram_get_profile("@TestUser")
            
            # Should normalize to lowercase without @
            mock_get.assert_called_with("testuser")
    
    @pytest.mark.asyncio
    async def test_list_profiles_pagination(self):
        """Test profile listing with pagination"""
        mock_profiles = [
            Mock(id=i, username=f"user{i}", follower_count=1000-i)
            for i in range(5)
        ]
        
        with patch('mcp_server.tools.instagram_profiles.filter_profiles') as mock_filter:
            mock_filter.return_value = (mock_profiles, 10)
            
            result = await instagram_list_profiles(limit=5, offset=0)
            
            assert result["total"] == 10
            assert len(result["profiles"]) == 5
            assert result["has_more"] is True
    
    @pytest.mark.asyncio
    async def test_list_profiles_filters(self):
        """Test profile listing with filters"""
        with patch('mcp_server.tools.instagram_profiles.filter_profiles') as mock_filter:
            mock_filter.return_value = ([], 0)
            
            await instagram_list_profiles(
                is_verified=True,
                is_business=True,
                min_followers=1000,
                max_followers=10000
            )
            
            # Check filters passed correctly
            call_args = mock_filter.call_args[1]
            assert call_args["is_verified"] is True
            assert call_args["is_business"] is True
            assert call_args["follower_count__gte"] == 1000
            assert call_args["follower_count__lte"] == 10000
    
    @pytest.mark.asyncio
    async def test_search_profiles_validation(self):
        """Test search query validation"""
        # Too short query
        result = await instagram_search_profiles("a", limit=10)
        
        assert result["error_type"] == "validation_error"
        assert "at least 2 characters" in result["message"]
    
    @pytest.mark.asyncio
    async def test_search_profiles_success(self):
        """Test successful profile search"""
        mock_profiles = [
            Mock(id=1, username="fashion_blog", follower_count=5000)
        ]
        
        with patch('mcp_server.tools.instagram_profiles.search_profiles') as mock_search:
            mock_search.return_value = mock_profiles
            
            result = await instagram_search_profiles("fashion")
            
            assert result["query"] == "fashion"
            assert result["count"] == 1
            assert result["profiles"][0]["username"] == "fashion_blog"
```

### 3. Create Integration Tests

Create `tests/integration/test_task_workflow.py`:

```python
"""
Integration tests for complete task workflows
"""

import pytest
from datetime import datetime
import asyncio

from mcp_server.tools.task_management import (
    task_create_import,
    task_get_status,
    task_list
)
from mcp_server.tools.task_results import (
    task_get_result,
    task_export_results
)

class TestTaskWorkflow:
    """Test complete task workflow"""
    
    @pytest.mark.asyncio
    async def test_instagram_profile_import_workflow(self, db, mock_celery, mock_brightdata):
        """Test complete Instagram profile import workflow"""
        # 1. Create task
        create_result = await task_create_import(
            task_type="instagram.profile",
            parameters={"username": "testuser"},
            priority="normal"
        )
        
        assert "task_id" in create_result
        assert create_result["status"] == "created"
        
        task_id = create_result["task_id"]
        
        # 2. Check initial status
        status = await task_get_status(task_id)
        assert status["status"] in ["pending", "running"]
        
        # 3. Simulate task completion
        from core.models import TaskResult
        await sync_to_async(TaskResult.objects.filter(task_id=task_id).update)(
            status="completed",
            progress=100,
            result={"imported": True, "profile_id": 123},
            completed_at=datetime.now()
        )
        
        # 4. Get final status
        final_status = await task_get_status(task_id)
        assert final_status["status"] == "completed"
        assert final_status["progress"] == 100
        
        # 5. Get results
        result = await task_get_result(task_id)
        assert result["status"] == "completed"
        assert result["result"]["profile"]["imported"] is True
        
        # 6. Export results
        export = await task_export_results(
            task_ids=[task_id],
            format="json"
        )
        assert export["format"] == "json"
        assert export["tasks_count"] == 1
    
    @pytest.mark.asyncio
    async def test_batch_task_workflow(self, db, mock_celery):
        """Test batch task creation and monitoring"""
        # Create multiple tasks
        task_ids = []
        
        for i in range(3):
            result = await task_create_import(
                task_type="instagram.posts",
                parameters={"username": f"user{i}", "limit": 50}
            )
            task_ids.append(result["task_id"])
        
        # List tasks
        task_list_result = await task_list(
            task_type="instagram.posts",
            limit=10
        )
        
        assert task_list_result["total"] >= 3
        
        # Check task status
        for task_id in task_ids:
            status = await task_get_status(task_id=task_id)
            assert "status" in status
            assert status["task_id"] == task_id
    
    @pytest.mark.asyncio
    async def test_task_failure_handling(self, db, mock_celery):
        """Test task failure scenarios"""
        # Create task
        create_result = await task_create_import(
            task_type="instagram.profile",
            parameters={"username": "fail_test"}
        )
        
        task_id = create_result["task_id"]
        
        # Simulate failure
        from core.models import TaskResult
        await sync_to_async(TaskResult.objects.filter(task_id=task_id).update)(
            status="failed",
            error="Test error message",
            completed_at=datetime.now()
        )
        
        # Get status
        status = await task_get_status(task_id)
        assert status["status"] == "failed"
        assert status["error"] == "Test error message"
        
        # Get result should show error
        result = await task_get_result(task_id)
        assert "error" in result
        assert result["error"]["message"] == "Test error message"
```

### 4. Create End-to-End Tests

Create `tests/e2e/test_mcp_client.py`:

```python
"""
End-to-end tests using MCP client
"""

import pytest
from mcp import Client
import asyncio

@pytest.mark.e2e
class TestMCPClient:
    """Test MCP server with actual client"""
    
    @pytest.fixture
    async def mcp_server_url(self):
        """Get MCP server URL"""
        # In CI, this would be the actual server
        return "http://localhost:8001"
    
    @pytest.mark.asyncio
    async def test_complete_instagram_workflow(self, mcp_server_url):
        """Test complete Instagram workflow via MCP client"""
        async with Client(mcp_server_url) as client:
            # 1. Get profile
            profile = await client.call_tool(
                "instagram_get_profile",
                {"username": "igorkishik"}
            )
            
            assert "username" in profile
            assert "follower_count" in profile
            
            # 2. Create import task
            task = await client.call_tool(
                "task_create_import",
                {
                    "task_type": "instagram.posts",
                    "parameters": {
                        "username": profile["username"],
                        "limit": 10
                    }
                }
            )
            
            assert "task_id" in task
            
            # 3. Monitor progress
            await asyncio.sleep(1)  # Let task start
            
            progress = await client.call_tool(
                "task_monitor_progress",
                {
                    "task_id": task["task_id"],
                    "stream": False
                }
            )
            
            assert "status" in progress
            assert "progress" in progress
            
            # 4. Wait for completion (with timeout)
            max_attempts = 30
            for _ in range(max_attempts):
                status = await client.call_tool(
                    "task_get_status",
                    {"task_id": task["task_id"]}
                )
                
                if status["status"] in ["completed", "failed"]:
                    break
                
                await asyncio.sleep(2)
            
            # 5. Get results
            if status["status"] == "completed":
                result = await client.call_tool(
                    "task_get_result",
                    {"task_id": task["task_id"]}
                )
                
                assert "result" in result
                assert "summary" in result["result"]
    
    @pytest.mark.asyncio
    async def test_health_check_via_client(self, mcp_server_url):
        """Test health check endpoint"""
        async with Client(mcp_server_url) as client:
            health = await client.call_tool(
                "health_check",
                {"detailed": True}
            )
            
            assert health["status"] in ["healthy", "unhealthy"]
            assert "checks" in health
            assert "database" in health["checks"]
            assert "redis" in health["checks"]
```

### 5. Create Performance Tests

Create `tests/performance/test_load.py`:

```python
"""
Performance and load tests
"""

import pytest
import asyncio
import time
from concurrent.futures import ThreadPoolExecutor
import statistics

@pytest.mark.performance
class TestPerformance:
    """Performance testing suite"""
    
    @pytest.mark.asyncio
    async def test_concurrent_profile_requests(self, mcp_client, performance_monitor):
        """Test concurrent profile requests"""
        usernames = [f"user{i}" for i in range(100)]
        
        performance_monitor.start("concurrent_profiles")
        
        # Run concurrent requests
        tasks = [
            mcp_client.call_tool(
                "instagram_get_profile",
                {"username": username}
            )
            for username in usernames
        ]
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        performance_monitor.stop("concurrent_profiles")
        
        # Analyze results
        successful = sum(1 for r in results if not isinstance(r, Exception))
        failed = len(results) - successful
        
        report = performance_monitor.get_report()
        duration = report["concurrent_profiles"]["duration"]
        
        assert successful > 90  # At least 90% success rate
        assert duration < 10  # Should complete within 10 seconds
        
        print(f"Concurrent requests: {len(results)}")
        print(f"Successful: {successful}")
        print(f"Failed: {failed}")
        print(f"Duration: {duration:.2f}s")
        print(f"Requests/sec: {len(results) / duration:.2f}")
    
    @pytest.mark.asyncio
    async def test_task_creation_throughput(self, mcp_client, performance_monitor):
        """Test task creation throughput"""
        num_tasks = 50
        
        performance_monitor.start("task_creation")
        
        task_ids = []
        for i in range(num_tasks):
            result = await mcp_client.call_tool(
                "task_create_import",
                {
                    "task_type": "instagram.profile",
                    "parameters": {"username": f"test{i}"}
                }
            )
            task_ids.append(result["task_id"])
        
        performance_monitor.stop("task_creation")
        
        report = performance_monitor.get_report()
        duration = report["task_creation"]["duration"]
        
        assert len(task_ids) == num_tasks
        assert duration < 5  # Should create 50 tasks in under 5 seconds
        
        print(f"Created {num_tasks} tasks in {duration:.2f}s")
        print(f"Tasks/sec: {num_tasks / duration:.2f}")
    
    @pytest.mark.asyncio
    async def test_memory_usage_under_load(self, mcp_client, performance_monitor):
        """Test memory usage under sustained load"""
        import psutil
        import gc
        
        initial_memory = psutil.Process().memory_info().rss / 1024 / 1024
        
        # Perform sustained operations
        for iteration in range(10):
            performance_monitor.start(f"iteration_{iteration}")
            
            # Create tasks
            tasks = []
            for i in range(10):
                task = mcp_client.call_tool(
                    "task_create_import",
                    {
                        "task_type": "instagram.posts",
                        "parameters": {"username": f"user{i}", "limit": 100}
                    }
                )
                tasks.append(task)
            
            await asyncio.gather(*tasks)
            
            performance_monitor.stop(f"iteration_{iteration}")
            
            # Force garbage collection
            gc.collect()
            
            # Check memory
            current_memory = psutil.Process().memory_info().rss / 1024 / 1024
            memory_increase = current_memory - initial_memory
            
            print(f"Iteration {iteration}: Memory increase: {memory_increase:.2f} MB")
            
            # Memory should not grow unbounded
            assert memory_increase < 100  # Less than 100MB increase
    
    @pytest.mark.asyncio
    async def test_response_time_percentiles(self, mcp_client):
        """Test response time percentiles"""
        response_times = []
        
        # Perform 100 requests
        for i in range(100):
            start = time.time()
            
            await mcp_client.call_tool(
                "instagram_list_profiles",
                {"limit": 20, "offset": i * 20}
            )
            
            response_times.append((time.time() - start) * 1000)  # ms
        
        # Calculate percentiles
        p50 = statistics.median(response_times)
        p95 = statistics.quantiles(response_times, n=20)[18]  # 95th percentile
        p99 = statistics.quantiles(response_times, n=100)[98]  # 99th percentile
        
        print(f"Response times (ms):")
        print(f"  P50: {p50:.2f}")
        print(f"  P95: {p95:.2f}")
        print(f"  P99: {p99:.2f}")
        
        # Assert reasonable response times
        assert p50 < 100  # Median under 100ms
        assert p95 < 200  # 95th percentile under 200ms
        assert p99 < 500  # 99th percentile under 500ms
```

### 6. Create Test Utilities

Create `tests/utils/test_helpers.py`:

```python
"""
Test helper utilities
"""

from typing import Dict, Any, List
import json
from datetime import datetime, timedelta
import random

class MockDataGenerator:
    """Generate mock data for tests"""
    
    @staticmethod
    def instagram_profile(
        username: str = None,
        verified: bool = False,
        business: bool = False
    ) -> Dict[str, Any]:
        """Generate mock Instagram profile"""
        username = username or f"user_{random.randint(1000, 9999)}"
        
        return {
            "id": random.randint(1, 1000000),
            "username": username,
            "full_name": f"{username.title()} User",
            "bio": f"Bio for {username}",
            "external_id": str(random.randint(10000000, 99999999)),
            "follower_count": random.randint(100, 1000000),
            "following_count": random.randint(50, 5000),
            "post_count": random.randint(10, 1000),
            "is_verified": verified,
            "is_private": random.choice([True, False]),
            "is_business": business,
            "profile_pic_url": f"https://example.com/{username}.jpg",
            "created_at": datetime.now() - timedelta(days=random.randint(1, 365))
        }
    
    @staticmethod
    def instagram_posts(
        count: int = 10,
        profile_id: int = None
    ) -> List[Dict[str, Any]]:
        """Generate mock Instagram posts"""
        posts = []
        
        for i in range(count):
            post = {
                "id": random.randint(1, 1000000),
                "profile_id": profile_id or random.randint(1, 1000),
                "external_id": str(random.randint(10000000, 99999999)),
                "content": f"Post content {i}",
                "post_type": random.choice(["photo", "video", "album", "reel"]),
                "post_url": f"https://instagram.com/p/{random.randint(1000, 9999)}",
                "like_count": random.randint(10, 100000),
                "comment_count": random.randint(0, 5000),
                "share_count": random.randint(0, 1000),
                "view_count": random.randint(100, 1000000) if random.random() > 0.5 else None,
                "posted_at": datetime.now() - timedelta(days=random.randint(1, 30))
            }
            posts.append(post)
        
        return posts
    
    @staticmethod
    def task_result(
        task_type: str = "instagram.profile",
        status: str = "completed"
    ) -> Dict[str, Any]:
        """Generate mock task result"""
        task_id = f"task_{random.randint(1000, 9999)}"
        
        result = {
            "task_id": task_id,
            "task_type": task_type,
            "task_name": f"{task_type}_import",
            "status": status,
            "progress": 100 if status == "completed" else random.randint(0, 99),
            "created_at": datetime.now() - timedelta(minutes=10),
            "started_at": datetime.now() - timedelta(minutes=9),
            "updated_at": datetime.now() - timedelta(minutes=1)
        }
        
        if status == "completed":
            result["completed_at"] = datetime.now()
            result["result"] = {
                "success": True,
                "items_processed": random.randint(10, 100)
            }
        elif status == "failed":
            result["completed_at"] = datetime.now()
            result["error"] = "Mock error message"
        
        return result

class TestDataBuilder:
    """Build complex test scenarios"""
    
    def __init__(self):
        self.generator = MockDataGenerator()
    
    def create_profile_hierarchy(
        self,
        profile_count: int = 5,
        posts_per_profile: int = 10
    ) -> Dict[str, Any]:
        """Create profiles with posts"""
        data = {
            "profiles": [],
            "posts": []
        }
        
        for i in range(profile_count):
            profile = self.generator.instagram_profile(
                username=f"test_profile_{i}",
                verified=i == 0  # First profile is verified
            )
            data["profiles"].append(profile)
            
            # Generate posts for profile
            posts = self.generator.instagram_posts(
                count=posts_per_profile,
                profile_id=profile["id"]
            )
            data["posts"].extend(posts)
        
        return data
    
    def create_task_history(
        self,
        task_count: int = 20
    ) -> List[Dict[str, Any]]:
        """Create task execution history"""
        tasks = []
        
        # Mix of task types and statuses
        task_types = [
            "instagram.profile",
            "instagram.posts",
            "instagram.batch_posts",
            "telegram.messages"
        ]
        
        statuses = ["completed", "completed", "completed", "failed", "running"]
        
        for i in range(task_count):
            task = self.generator.task_result(
                task_type=random.choice(task_types),
                status=random.choice(statuses)
            )
            tasks.append(task)
        
        return tasks

def assert_valid_response(response: Dict[str, Any], required_fields: List[str]):
    """Assert response has required fields"""
    for field in required_fields:
        assert field in response, f"Missing required field: {field}"

def assert_error_response(response: Dict[str, Any], error_type: str = None):
    """Assert response is an error"""
    assert "error_type" in response or "error" in response
    if error_type:
        assert response.get("error_type") == error_type
```

### 7. Create CI/CD Configuration

Create `.github/workflows/test.yml`:

```yaml
name: Test Suite

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

env:
  DJANGO_SETTINGS_MODULE: SocialManager.settings.test
  DATABASE_URL: postgresql://postgres:postgres@localhost:5432/test_db
  REDIS_URL: redis://localhost:6379/0

jobs:
  test:
    runs-on: ubuntu-latest
    
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: test_db
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432
      
      redis:
        image: redis:7
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'
    
    - name: Cache dependencies
      uses: actions/cache@v3
      with:
        path: |
          ~/.cache/pip
          ~/.cache/uv
        key: ${{ runner.os }}-pip-${{ hashFiles('**/requirements.txt') }}
    
    - name: Install dependencies
      run: |
        pip install --upgrade pip uv
        uv pip install -r requirements.txt
        uv pip install -r requirements-dev.txt
    
    - name: Run migrations
      run: |
        python manage.py migrate --noinput
    
    - name: Run unit tests
      run: |
        pytest tests/unit -v --cov=mcp_server --cov-report=xml
    
    - name: Run integration tests
      run: |
        pytest tests/integration -v
    
    - name: Run linting
      run: |
        ruff check .
        black --check .
        mypy .
    
    - name: Upload coverage
      uses: codecov/codecov-action@v3
      with:
        file: ./coverage.xml
        fail_ci_if_error: true

  performance:
    runs-on: ubuntu-latest
    needs: test
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Build Docker image
      run: |
        docker build -t mcp-server:test .
    
    - name: Run performance tests
      run: |
        docker-compose -f docker-compose.test.yml up -d
        sleep 10  # Wait for services
        pytest tests/performance -v -m performance
    
    - name: Upload performance results
      uses: actions/upload-artifact@v3
      with:
        name: performance-results
        path: performance-report.json
```

### 8. Create Test Documentation

Create `tests/README.md`:

```markdown
# MCP Server Test Suite

## Overview

Comprehensive test suite for the MCP server including:
- Unit tests
- Integration tests
- End-to-end tests
- Performance tests
- Load tests

## Running Tests

### Local Development

```bash
# Run all tests
pytest

# Run specific test category
pytest tests/unit
pytest tests/integration
pytest tests/e2e
pytest tests/performance

# Run with coverage
pytest --cov=mcp_server --cov-report=html

# Run specific test
pytest tests/unit/test_instagram_tools.py::TestInstagramProfileTools::test_get_profile_success
```

### Docker

```bash
# Run tests in Docker
docker-compose -f docker-compose.test.yml run --rm test

# Run specific test suite
docker-compose -f docker-compose.test.yml run --rm test pytest tests/unit
```

## Test Categories

### Unit Tests
- Individual function/method testing
- Mocked dependencies
- Fast execution
- Located in `tests/unit/`

### Integration Tests
- Multiple component interaction
- Real database (test database)
- Some mocked external services
- Located in `tests/integration/`

### End-to-End Tests
- Complete user workflows
- Real MCP client
- Minimal mocking
- Located in `tests/e2e/`

### Performance Tests
- Load testing
- Response time analysis
- Memory usage tracking
- Located in `tests/performance/`

## Writing Tests

### Test Structure

```python
import pytest
from unittest.mock import Mock, patch

class TestFeatureName:
    """Test suite for feature"""
    
    @pytest.fixture
    def setup_data(self):
        """Setup test data"""
        return {...}
    
    @pytest.mark.asyncio
    async def test_specific_behavior(self, setup_data):
        """Test specific behavior"""
        # Arrange
        ...
        
        # Act
        result = await function_under_test()
        
        # Assert
        assert result == expected
```

### Fixtures

Common fixtures available:
- `db`: Database setup
- `mcp_client`: MCP test client
- `mock_celery`: Mocked Celery
- `mock_brightdata`: Mocked BrightData API
- `performance_monitor`: Performance tracking

## Test Data

Use test data generators:

```python
from tests.utils.test_helpers import MockDataGenerator

generator = MockDataGenerator()
profile = generator.instagram_profile(verified=True)
posts = generator.instagram_posts(count=10)
```

## Continuous Integration

Tests run automatically on:
- Push to main/develop branches
- Pull requests
- Scheduled nightly builds

## Performance Benchmarks

Target performance benchmarks:
- P50 response time: < 100ms
- P95 response time: < 200ms
- P99 response time: < 500ms
- Task creation: > 10 tasks/second
- Memory growth: < 100MB under load
```

## Testing Best Practices

1. **Test Naming**: Use descriptive names
2. **Isolation**: Each test should be independent
3. **Mocking**: Mock external dependencies
4. **Assertions**: Use specific assertions
5. **Coverage**: Aim for > 80% coverage
6. **Performance**: Track execution times
7. **Documentation**: Document complex tests

## Success Criteria

- [ ] Unit test coverage > 80%
- [ ] All integration tests passing
- [ ] E2E tests cover main workflows
- [ ] Performance benchmarks met
- [ ] CI/CD pipeline configured
- [ ] Test documentation complete
- [ ] Mock data generators ready
- [ ] Load testing framework ready

## Next Steps

Proceed to **016_deployment_guide.md** for implementing production deployment procedures and best practices.