# Subtask 005: Instagram Profile Management Tools

## Objective
Implement MCP tools for Instagram profile management, including fetching, listing, and searching profiles with proper validation and error handling.

## Prerequisites
- Completed subtasks 001-004
- Database decorators implemented
- Error handling framework ready
- Validation models created

## Implementation Steps

### 1. Create Instagram Profile Tools Module

Create `mcp_server/tools/instagram_profiles.py`:

```python
"""
Instagram profile management tools for MCP server
"""

from typing import Dict, Any, Optional, List
from datetime import datetime

from fastmcp import FastMCP
from mcp_server.main import mcp
from mcp_server.decorators.error_handler import handle_tool_errors
from mcp_server.decorators.database import async_db_operation, track_sync_calls
from mcp_server.errors import create_error_response, ErrorTypes
from mcp_server.validators.base import PaginationParams

import logging

logger = logging.getLogger(__name__)

# Database helper functions

@async_db_operation
@track_sync_calls
def get_profile_by_username(username: str):
    """Get Instagram profile by username"""
    from instagram_manager.services import InstagramProfileService
    service = InstagramProfileService()
    return service.get_by_username(username)

@async_db_operation
@track_sync_calls
def get_profile_by_id(profile_id: int):
    """Get Instagram profile by ID"""
    from instagram_manager.repositories import InstagramProfileRepository
    repo = InstagramProfileRepository()
    return repo.get(id=profile_id)

@async_db_operation
@track_sync_calls
def filter_profiles(**kwargs):
    """Filter Instagram profiles with parameters"""
    from instagram_manager.repositories import InstagramProfileRepository
    repo = InstagramProfileRepository()
    
    # Extract pagination params
    limit = kwargs.pop('limit', 20)
    offset = kwargs.pop('offset', 0)
    
    # Get queryset
    queryset = repo.filter(**kwargs)
    
    # Get total count before pagination
    total = queryset.count()
    
    # Apply pagination
    profiles = list(queryset[offset:offset + limit])
    
    return profiles, total

@async_db_operation
@track_sync_calls
def search_profiles(query: str, limit: int = 20):
    """Search profiles by username or full name"""
    from instagram_manager.models import InstagramProfile
    from django.db.models import Q
    
    queryset = InstagramProfile.objects.filter(
        Q(username__icontains=query) | 
        Q(full_name__icontains=query)
    ).order_by('-follower_count')[:limit]
    
    return list(queryset)

@async_db_operation
@track_sync_calls
def get_profile_statistics(profile_id: int):
    """Get detailed statistics for a profile"""
    from instagram_manager.models import InstagramPost
    from django.db.models import Count, Sum, Avg
    
    stats = InstagramPost.objects.filter(
        profile_id=profile_id
    ).aggregate(
        total_posts=Count('id'),
        total_likes=Sum('like_count'),
        total_comments=Sum('comment_count'),
        avg_likes=Avg('like_count'),
        avg_comments=Avg('comment_count')
    )
    
    return stats

# MCP Tool implementations

@mcp.tool()
@handle_tool_errors
async def instagram_get_profile(username: str) -> Dict[str, Any]:
    """
    Get Instagram profile information by username
    
    Args:
        username: Instagram username (with or without @)
        
    Returns:
        Profile information or error
    """
    # Normalize username
    username = username.strip().lower().lstrip('@')
    
    logger.info(f"Fetching Instagram profile: {username}")
    
    # Get profile from database
    profile = await get_profile_by_username(username)
    
    if not profile:
        return create_error_response(
            error_type=ErrorTypes.NOT_FOUND,
            message=f"Profile '{username}' not found"
        )
    
    # Get additional statistics
    stats = await get_profile_statistics(profile.id)
    
    return {
        "id": profile.id,
        "username": profile.username,
        "full_name": profile.full_name,
        "bio": profile.bio,
        "external_id": profile.external_id,
        "follower_count": profile.follower_count,
        "following_count": profile.following_count,
        "post_count": profile.post_count,
        "is_verified": profile.is_verified,
        "is_private": profile.is_private,
        "is_business": profile.is_business,
        "profile_pic_url": profile.profile_pic_url,
        "external_url": profile.external_url,
        "created_at": profile.created_at.isoformat(),
        "updated_at": profile.updated_at.isoformat(),
        "last_scraped_at": profile.last_scraped_at.isoformat() if profile.last_scraped_at else None,
        "statistics": {
            "total_posts": stats.get('total_posts', 0),
            "total_likes": stats.get('total_likes', 0),
            "total_comments": stats.get('total_comments', 0),
            "average_likes": round(stats.get('avg_likes', 0) or 0, 2),
            "average_comments": round(stats.get('avg_comments', 0) or 0, 2)
        }
    }

@mcp.tool()
@handle_tool_errors
async def instagram_list_profiles(
    limit: int = 20,
    offset: int = 0,
    is_active: Optional[bool] = None,
    is_verified: Optional[bool] = None,
    is_private: Optional[bool] = None,
    is_business: Optional[bool] = None,
    min_followers: Optional[int] = None,
    max_followers: Optional[int] = None,
    order_by: str = "-follower_count"
) -> Dict[str, Any]:
    """
    List Instagram profiles with filtering and pagination
    
    Args:
        limit: Number of profiles to return (1-100)
        offset: Number of profiles to skip
        is_active: Filter by active status
        is_verified: Filter by verification status
        is_private: Filter by private status
        is_business: Filter by business account status
        min_followers: Minimum follower count
        max_followers: Maximum follower count
        order_by: Sort order (follower_count, -follower_count, username, -updated_at)
        
    Returns:
        List of profiles with pagination info
    """
    # Validate pagination
    pagination = PaginationParams(limit=limit, offset=offset)
    
    # Build filters
    filters = {}
    if is_active is not None:
        filters['is_active'] = is_active
    if is_verified is not None:
        filters['is_verified'] = is_verified
    if is_private is not None:
        filters['is_private'] = is_private
    if is_business is not None:
        filters['is_business'] = is_business
    if min_followers is not None:
        filters['follower_count__gte'] = min_followers
    if max_followers is not None:
        filters['follower_count__lte'] = max_followers
    
    # Validate order_by
    valid_orders = [
        'follower_count', '-follower_count',
        'username', '-username',
        'updated_at', '-updated_at',
        'post_count', '-post_count'
    ]
    if order_by not in valid_orders:
        order_by = '-follower_count'
    
    filters['order_by'] = order_by
    
    logger.info(f"Listing Instagram profiles with filters: {filters}")
    
    # Get profiles
    profiles, total = await filter_profiles(
        limit=pagination.limit,
        offset=pagination.offset,
        **filters
    )
    
    return {
        "total": total,
        "offset": pagination.offset,
        "limit": pagination.limit,
        "has_more": (pagination.offset + len(profiles)) < total,
        "profiles": [
            {
                "id": p.id,
                "username": p.username,
                "full_name": p.full_name,
                "follower_count": p.follower_count,
                "following_count": p.following_count,
                "post_count": p.post_count,
                "is_verified": p.is_verified,
                "is_private": p.is_private,
                "is_business": p.is_business,
                "is_active": p.is_active,
                "profile_pic_url": p.profile_pic_url,
                "last_scraped_at": p.last_scraped_at.isoformat() if p.last_scraped_at else None
            }
            for p in profiles
        ]
    }

@mcp.tool()
@handle_tool_errors
async def instagram_search_profiles(
    query: str,
    limit: int = 20
) -> Dict[str, Any]:
    """
    Search Instagram profiles by username or full name
    
    Args:
        query: Search query (min 2 characters)
        limit: Maximum results to return (1-50)
        
    Returns:
        List of matching profiles
    """
    # Validate query
    if len(query) < 2:
        return create_error_response(
            error_type=ErrorTypes.VALIDATION_ERROR,
            message="Search query must be at least 2 characters"
        )
    
    # Validate limit
    limit = max(1, min(limit, 50))
    
    logger.info(f"Searching Instagram profiles: '{query}'")
    
    # Search profiles
    profiles = await search_profiles(query, limit)
    
    return {
        "query": query,
        "count": len(profiles),
        "profiles": [
            {
                "id": p.id,
                "username": p.username,
                "full_name": p.full_name,
                "follower_count": p.follower_count,
                "is_verified": p.is_verified,
                "is_private": p.is_private,
                "profile_pic_url": p.profile_pic_url
            }
            for p in profiles
        ]
    }

@mcp.tool()
@handle_tool_errors
async def instagram_get_profile_stats(
    username: str,
    period: str = "all"
) -> Dict[str, Any]:
    """
    Get detailed statistics for an Instagram profile
    
    Args:
        username: Instagram username
        period: Time period (all, today, week, month)
        
    Returns:
        Profile statistics
    """
    # Get profile
    profile = await get_profile_by_username(username)
    
    if not profile:
        return create_error_response(
            error_type=ErrorTypes.NOT_FOUND,
            message=f"Profile '{username}' not found"
        )
    
    # Get base statistics
    stats = await get_profile_statistics(profile.id)
    
    
    # Get engagement rate
    engagement_rate = 0.0
    if profile.follower_count > 0 and stats['total_posts'] > 0:
        total_engagement = (stats.get('total_likes', 0) + stats.get('total_comments', 0))
        engagement_rate = (total_engagement / (stats['total_posts'] * profile.follower_count)) * 100
    
    return {
        "profile": {
            "username": profile.username,
            "follower_count": profile.follower_count,
            "following_count": profile.following_count,
            "post_count": profile.post_count
        },
        "statistics": {
            "total_posts": stats.get('total_posts', 0),
            "total_likes": stats.get('total_likes', 0),
            "total_comments": stats.get('total_comments', 0),
            "average_likes": round(stats.get('avg_likes', 0) or 0, 2),
            "average_comments": round(stats.get('avg_comments', 0) or 0, 2),
            "engagement_rate": round(engagement_rate, 2)
        },
        "period": period
    }

# Function removed - no longer tracking growth metrics
        profile_id=profile_id,
        posted_at__gte=start_date
    ).aggregate(
        new_posts=Count('id'),
        likes_gained=Sum('like_count'),
        comments_gained=Sum('comment_count')
    )
    
    return {
        "new_posts": posts.get('new_posts', 0),
        "likes_gained": posts.get('likes_gained', 0),
        "comments_gained": posts.get('comments_gained', 0)
    }

@mcp.tool()
@handle_tool_errors
async def instagram_bulk_check_profiles(
    usernames: List[str]
) -> Dict[str, Any]:
    """
    Check multiple Instagram profiles existence
    
    Args:
        usernames: List of usernames to check (max 50)
        
    Returns:
        Status of each username
    """
    # Validate input
    if not usernames:
        return create_error_response(
            error_type=ErrorTypes.VALIDATION_ERROR,
            message="At least one username is required"
        )
    
    if len(usernames) > 50:
        return create_error_response(
            error_type=ErrorTypes.VALIDATION_ERROR,
            message="Maximum 50 usernames allowed"
        )
    
    # Normalize usernames
    normalized = [u.strip().lower().lstrip('@') for u in usernames]
    
    # Check each profile
    results = {}
    for username in normalized:
        profile = await get_profile_by_username(username)
        results[username] = {
            "exists": profile is not None,
            "id": profile.id if profile else None,
            "is_active": profile.is_active if profile else None,
            "last_updated": profile.updated_at.isoformat() if profile else None
        }
    
    return {
        "checked": len(results),
        "found": sum(1 for r in results.values() if r["exists"]),
        "results": results
    }
```

### 2. Create Profile Export Tool

Create `mcp_server/tools/instagram_export.py`:

```python
"""
Instagram profile export tools
"""

from typing import Dict, Any, List
import csv
import io
import json

from mcp_server.main import mcp
from mcp_server.decorators.error_handler import handle_tool_errors
from mcp_server.decorators.database import async_db_operation, track_sync_calls

@async_db_operation
@track_sync_calls
def export_profiles_data(profile_ids: List[int], format: str) -> str:
    """Export profiles data in specified format"""
    from instagram_manager.models import InstagramProfile
    
    profiles = InstagramProfile.objects.filter(
        id__in=profile_ids
    ).order_by('username')
    
    if format == "csv":
        output = io.StringIO()
        writer = csv.DictWriter(output, fieldnames=[
            'username', 'full_name', 'follower_count', 
            'following_count', 'post_count', 'is_verified',
            'is_private', 'is_business', 'bio', 'external_url'
        ])
        writer.writeheader()
        
        for profile in profiles:
            writer.writerow({
                'username': profile.username,
                'full_name': profile.full_name,
                'follower_count': profile.follower_count,
                'following_count': profile.following_count,
                'post_count': profile.post_count,
                'is_verified': profile.is_verified,
                'is_private': profile.is_private,
                'is_business': profile.is_business,
                'bio': profile.bio,
                'external_url': profile.external_url
            })
        
        return output.getvalue()
    
    elif format == "json":
        data = []
        for profile in profiles:
            data.append({
                'username': profile.username,
                'full_name': profile.full_name,
                'follower_count': profile.follower_count,
                'following_count': profile.following_count,
                'post_count': profile.post_count,
                'is_verified': profile.is_verified,
                'is_private': profile.is_private,
                'is_business': profile.is_business,
                'bio': profile.bio,
                'external_url': profile.external_url,
                'created_at': profile.created_at.isoformat(),
                'updated_at': profile.updated_at.isoformat()
            })
        
        return json.dumps(data, indent=2)
    
    else:
        raise ValueError(f"Unsupported format: {format}")

@mcp.tool()
@handle_tool_errors
async def instagram_export_profiles(
    profile_ids: List[int],
    format: str = "json"
) -> Dict[str, Any]:
    """
    Export Instagram profiles data
    
    Args:
        profile_ids: List of profile IDs to export
        format: Export format (json, csv)
        
    Returns:
        Exported data as string
    """
    if format not in ["json", "csv"]:
        return {
            "error_type": "validation_error",
            "message": "Format must be 'json' or 'csv'"
        }
    
    data = await export_profiles_data(profile_ids, format)
    
    return {
        "format": format,
        "count": len(profile_ids),
        "data": data
    }
```

### 3. Create Tests for Profile Tools

Create `tests/test_instagram_profile_tools.py`:

```python
"""Test Instagram profile tools"""

import pytest
from unittest.mock import Mock, patch

from mcp_server.tools.instagram_profiles import (
    instagram_get_profile,
    instagram_list_profiles,
    instagram_search_profiles
)

@pytest.mark.asyncio
async def test_get_profile_success():
    """Test successful profile retrieval"""
    
    # Mock profile
    mock_profile = Mock()
    mock_profile.id = 1
    mock_profile.username = "testuser"
    mock_profile.full_name = "Test User"
    mock_profile.follower_count = 1000
    mock_profile.following_count = 500
    mock_profile.post_count = 50
    mock_profile.is_verified = True
    mock_profile.is_private = False
    mock_profile.is_business = False
    mock_profile.profile_pic_url = "https://example.com/pic.jpg"
    mock_profile.bio = "Test bio"
    mock_profile.external_url = "https://example.com"
    mock_profile.external_id = "12345"
    mock_profile.created_at = Mock(isoformat=Mock(return_value="2024-01-01"))
    mock_profile.updated_at = Mock(isoformat=Mock(return_value="2024-01-01"))
    mock_profile.last_scraped_at = None
    
    with patch('mcp_server.tools.instagram_profiles.get_profile_by_username') as mock_get:
        mock_get.return_value = mock_profile
        
        with patch('mcp_server.tools.instagram_profiles.get_profile_statistics') as mock_stats:
            mock_stats.return_value = {
                'total_posts': 50,
                'total_likes': 5000,
                'total_comments': 500,
                'avg_likes': 100,
                'avg_comments': 10
            }
            
            result = await instagram_get_profile("testuser")
    
    assert result["username"] == "testuser"
    assert result["follower_count"] == 1000
    assert result["is_verified"] is True
    assert "statistics" in result

@pytest.mark.asyncio
async def test_get_profile_not_found():
    """Test profile not found"""
    
    with patch('mcp_server.tools.instagram_profiles.get_profile_by_username') as mock_get:
        mock_get.return_value = None
        
        result = await instagram_get_profile("nonexistent")
    
    assert result["error_type"] == "not_found"
    assert "not found" in result["message"]

@pytest.mark.asyncio
async def test_list_profiles():
    """Test listing profiles"""
    
    # Mock profiles
    mock_profiles = [
        Mock(
            id=1,
            username="user1",
            full_name="User One",
            follower_count=1000,
            following_count=500,
            post_count=50,
            is_verified=True,
            is_private=False,
            is_business=False,
            is_active=True,
            profile_pic_url="https://example.com/1.jpg",
            last_scraped_at=None
        )
    ]
    
    with patch('mcp_server.tools.instagram_profiles.filter_profiles') as mock_filter:
        mock_filter.return_value = (mock_profiles, 1)
        
        result = await instagram_list_profiles(limit=10, is_verified=True)
    
    assert result["total"] == 1
    assert len(result["profiles"]) == 1
    assert result["profiles"][0]["username"] == "user1"

@pytest.mark.asyncio
async def test_search_profiles():
    """Test searching profiles"""
    
    mock_profiles = [
        Mock(
            id=1,
            username="testuser",
            full_name="Test User",
            follower_count=1000,
            is_verified=True,
            is_private=False,
            profile_pic_url="https://example.com/1.jpg"
        )
    ]
    
    with patch('mcp_server.tools.instagram_profiles.search_profiles') as mock_search:
        mock_search.return_value = mock_profiles
        
        result = await instagram_search_profiles("test")
    
    assert result["query"] == "test"
    assert result["count"] == 1
    assert result["profiles"][0]["username"] == "testuser"

@pytest.mark.asyncio
async def test_search_profiles_short_query():
    """Test search with too short query"""
    
    result = await instagram_search_profiles("a")
    
    assert result["error_type"] == "validation_error"
    assert "at least 2 characters" in result["message"]
```

## Usage Examples

### Get Profile Information
```python
# Get single profile
profile = await client.call_tool("instagram_get_profile", {
    "username": "cristiano"
})

# Response:
{
    "id": 123,
    "username": "cristiano",
    "full_name": "Cristiano Ronaldo",
    "follower_count": *********,
    "is_verified": true,
    "statistics": {
        "total_posts": 3500,
        "average_likes": 5000000,
        "engagement_rate": 3.5
    }
}
```

### List Profiles with Filters
```python
# List verified business accounts
profiles = await client.call_tool("instagram_list_profiles", {
    "is_verified": true,
    "is_business": true,
    "min_followers": 10000,
    "limit": 50,
    "order_by": "-follower_count"
})
```

### Search Profiles
```python
# Search by username or name
results = await client.call_tool("instagram_search_profiles", {
    "query": "fashion",
    "limit": 20
})
```

### Get Profile Statistics
```python
# Get weekly statistics
stats = await client.call_tool("instagram_get_profile_stats", {
    "username": "natgeo",
    "period": "week"
})
```

## Verification Steps

1. Test profile retrieval:
   ```bash
   pytest tests/test_instagram_profile_tools.py::test_get_profile_success -v
   ```

2. Test error handling:
   ```bash
   pytest tests/test_instagram_profile_tools.py::test_get_profile_not_found -v
   ```

3. Test with real MCP server:
   ```python
   from mcp import Client
   
   async with Client("http://localhost:8000") as client:
       profile = await client.call_tool("instagram_get_profile", {
           "username": "test_account"
       })
       print(profile)
   ```

## Success Criteria

- [ ] Profile retrieval working
- [ ] Profile listing with filters
- [ ] Search functionality
- [ ] Statistics calculation
- [ ] Bulk operations
- [ ] Export functionality
- [ ] All tests passing

## Performance Considerations

1. **Query Optimization**: Use select_related and prefetch_related
2. **Pagination**: Always paginate list operations
3. **Caching**: Consider caching frequently accessed profiles
4. **Bulk Operations**: Limit bulk checks to 50 profiles

## Notes

- Username normalization is automatic
- All timestamps are returned in ISO format
- Statistics are calculated on-demand
- Export supports JSON and CSV formats

## Next Steps

Proceed to **006_instagram_post_tools.md** to implement Instagram post and media management endpoints.