# Subtask 010: Task Creation and Management System

## Objective
Implement MCP tools for creating and managing asynchronous tasks, including task submission, status tracking, and task listing functionality.

## Prerequisites
- Completed subtasks 001-009
- All Instagram and Telegram tools ready
- Database decorators and error handling implemented

## Implementation Steps

### 1. Create Task Management Tools

Create `mcp_server/tools/task_management.py`:

```python
"""
Task creation and management tools for MCP server
"""

from typing import Dict, Any, Optional, List
from datetime import datetime
import uuid

from mcp_server.main import mcp
from mcp_server.decorators.error_handler import handle_tool_errors
from mcp_server.decorators.database import async_db_operation, track_sync_calls
from mcp_server.errors import create_error_response, ErrorTypes
from mcp_server.validators.tasks import TaskCreateParams, TaskListParams

import logging

logger = logging.getLogger(__name__)

# Task registry (static imports for security)
TASK_REGISTRY = {
    "instagram.profile": "instagram_manager.tasks.import_tasks.ImportInstagramProfileTask",
    "instagram.posts": "instagram_manager.tasks.import_tasks.ImportInstagramPostsTask",
    "instagram.batch_posts": "instagram_manager.tasks.import_tasks.ImportBatchPostsTask",
    "instagram.comments": "instagram_manager.tasks.import_tasks.ImportInstagramCommentsTask",
    "telegram.chats": "telegram_manager.tasks.import_tasks.ImportTelegramChatsTask",
    "telegram.messages": "telegram_manager.tasks.import_tasks.ImportTelegramMessagesTask",
    "telegram.users": "telegram_manager.tasks.import_tasks.ImportTelegramUsersTask"
}

@async_db_operation
@track_sync_calls
def create_task_result(task_type: str, task_id: str, parameters: dict) -> object:
    """Create initial TaskResult record"""
    from core.models import TaskResult
    
    return TaskResult.objects.create(
        task_id=task_id,
        task_type=task_type,
        task_name=f"{task_type}_import",
        status="pending",
        progress=0,
        parameters=parameters,
        created_at=datetime.now(),
        updated_at=datetime.now()
    )

@async_db_operation
@track_sync_calls
def get_task_class(task_type: str):
    """Get task class from registry"""
    if task_type not in TASK_REGISTRY:
        raise ValueError(f"Unknown task type: {task_type}")
    
    # Import task class dynamically but safely
    module_path = TASK_REGISTRY[task_type]
    parts = module_path.rsplit('.', 1)
    module_name = parts[0]
    class_name = parts[1]
    
    module = __import__(module_name, fromlist=[class_name])
    return getattr(module, class_name)

@async_db_operation
@track_sync_calls
def submit_task(task_class, task_id: str, parameters: dict):
    """Submit task for execution"""
    task_instance = task_class()
    
    # Set task ID
    task_instance.task_id = task_id
    
    # Submit async execution
    result = task_instance.run_async(**parameters)
    
    return result

@mcp.tool()
@handle_tool_errors
async def task_create_import(
    task_type: str,
    parameters: Dict[str, Any],
    priority: str = "normal"
) -> Dict[str, Any]:
    """
    Create a new import task
    
    Args:
        task_type: Type of import task (instagram.profile, telegram.messages, etc.)
        parameters: Task-specific parameters
        priority: Task priority (low, normal, high)
        
    Returns:
        Task creation result with task_id
    """
    # Validate task type
    if task_type not in TASK_REGISTRY:
        return create_error_response(
            error_type=ErrorTypes.VALIDATION_ERROR,
            message=f"Invalid task type: {task_type}. Available types: {list(TASK_REGISTRY.keys())}"
        )
    
    # Validate parameters based on task type
    validation_result = await _validate_task_parameters(task_type, parameters)
    if "error_type" in validation_result:
        return validation_result
    
    # Generate task ID
    task_id = str(uuid.uuid4())
    
    logger.info(f"Creating {task_type} task with ID: {task_id}")
    
    try:
        # Create TaskResult record
        task_result = await create_task_result(
            task_type=task_type,
            task_id=task_id,
            parameters=parameters
        )
        
        # Get task class
        task_class = await get_task_class(task_type)
        
        # Submit task for execution
        celery_result = await submit_task(
            task_class=task_class,
            task_id=task_id,
            parameters=parameters
        )
        
        return {
            "task_id": task_id,
            "task_type": task_type,
            "status": "created",
            "priority": priority,
            "parameters": parameters,
            "created_at": task_result.created_at.isoformat(),
            "estimated_duration": _estimate_task_duration(task_type, parameters)
        }
        
    except Exception as e:
        logger.error(f"Failed to create task: {str(e)}")
        return create_error_response(
            error_type=ErrorTypes.INTERNAL_ERROR,
            message=f"Failed to create task: {str(e)}"
        )

async def _validate_task_parameters(task_type: str, parameters: dict) -> dict:
    """Validate parameters for specific task type"""
    
    if task_type == "instagram.profile":
        if "username" not in parameters:
            return create_error_response(
                error_type=ErrorTypes.VALIDATION_ERROR,
                message="Missing required parameter: username"
            )
        # Normalize username
        parameters["username"] = parameters["username"].strip().lower().lstrip('@')
        
    elif task_type == "instagram.posts":
        if "username" not in parameters:
            return create_error_response(
                error_type=ErrorTypes.VALIDATION_ERROR,
                message="Missing required parameter: username"
            )
        # Set defaults
        parameters.setdefault("limit", 50)
        
    elif task_type == "instagram.batch_posts":
        if "usernames" not in parameters or not isinstance(parameters["usernames"], list):
            return create_error_response(
                error_type=ErrorTypes.VALIDATION_ERROR,
                message="Missing or invalid parameter: usernames (must be a list)"
            )
        # Normalize usernames
        parameters["usernames"] = [u.strip().lower().lstrip('@') for u in parameters["usernames"]]
        
    elif task_type == "telegram.messages":
        if "chat_id" not in parameters:
            return create_error_response(
                error_type=ErrorTypes.VALIDATION_ERROR,
                message="Missing required parameter: chat_id"
            )
        # Set defaults
        parameters.setdefault("limit", 100)
        
    elif task_type == "telegram.chats":
        # Set defaults
        parameters.setdefault("limit", 50)
        
    return {"valid": True}

def _estimate_task_duration(task_type: str, parameters: dict) -> str:
    """Estimate task duration based on type and parameters"""
    
    estimates = {
        "instagram.profile": "30-60 seconds",
        "instagram.posts": f"{parameters.get('limit', 50) * 2} seconds",
        "instagram.batch_posts": f"{len(parameters.get('usernames', [])) * 60} seconds",
        "instagram.comments": "2-5 minutes",
        "telegram.chats": "1-2 minutes",
        "telegram.messages": f"{parameters.get('limit', 100) // 10} minutes",
        "telegram.users": "1-3 minutes"
    }
    
    return estimates.get(task_type, "Unknown")

@async_db_operation
@track_sync_calls
def get_task_by_id(task_id: str):
    """Get task by ID"""
    from core.models import TaskResult
    
    return TaskResult.objects.filter(task_id=task_id).first()

@async_db_operation
@track_sync_calls
def list_tasks(filters: dict, limit: int, offset: int):
    """List tasks with filters"""
    from core.models import TaskResult
    
    queryset = TaskResult.objects.all()
    
    # Apply filters
    if filters.get("status"):
        queryset = queryset.filter(status=filters["status"])
    if filters.get("task_type"):
        queryset = queryset.filter(task_type=filters["task_type"])
    if filters.get("created_after"):
        queryset = queryset.filter(created_at__gte=filters["created_after"])
    if filters.get("created_before"):
        queryset = queryset.filter(created_at__lte=filters["created_before"])
    
    # Order by creation time
    queryset = queryset.order_by("-created_at")
    
    # Get total count
    total = queryset.count()
    
    # Apply pagination
    tasks = list(queryset[offset:offset + limit])
    
    return tasks, total

@mcp.tool()
@handle_tool_errors
async def task_get_status(task_id: str) -> Dict[str, Any]:
    """
    Get status of a specific task
    
    Args:
        task_id: Task UUID
        
    Returns:
        Detailed task status and progress
    """
    task = await get_task_by_id(task_id)
    
    if not task:
        return create_error_response(
            error_type=ErrorTypes.NOT_FOUND,
            message=f"Task {task_id} not found"
        )
    
    # Get Celery task status if available
    celery_status = await _get_celery_status(task_id)
    
    return {
        "task_id": task.task_id,
        "task_type": task.task_type,
        "task_name": task.task_name,
        "status": task.status,
        "progress": task.progress,
        "progress_message": task.progress_message,
        "parameters": task.parameters,
        "result": task.result,
        "error": task.error,
        "created_at": task.created_at.isoformat(),
        "updated_at": task.updated_at.isoformat(),
        "started_at": task.started_at.isoformat() if task.started_at else None,
        "completed_at": task.completed_at.isoformat() if task.completed_at else None,
        "duration": _calculate_duration(task),
        "celery_status": celery_status
    }

async def _get_celery_status(task_id: str) -> dict:
    """Get Celery task status"""
    try:
        from celery.result import AsyncResult
        result = AsyncResult(task_id)
        
        return {
            "state": result.state,
            "ready": result.ready(),
            "successful": result.successful() if result.ready() else None,
            "failed": result.failed() if result.ready() else None
        }
    except Exception as e:
        logger.warning(f"Could not get Celery status: {e}")
        return None

def _calculate_duration(task) -> Optional[float]:
    """Calculate task duration in seconds"""
    if task.started_at and task.completed_at:
        return (task.completed_at - task.started_at).total_seconds()
    elif task.started_at:
        return (datetime.now() - task.started_at).total_seconds()
    return None

@mcp.tool()
@handle_tool_errors
async def task_list(
    status: Optional[str] = None,
    task_type: Optional[str] = None,
    limit: int = 20,
    offset: int = 0,
    created_after: Optional[str] = None,
    created_before: Optional[str] = None
) -> Dict[str, Any]:
    """
    List tasks with filtering
    
    Args:
        status: Filter by status (pending, running, completed, failed)
        task_type: Filter by task type
        limit: Number of tasks (1-100)
        offset: Skip tasks
        created_after: Tasks created after this date (ISO format)
        created_before: Tasks created before this date (ISO format)
        
    Returns:
        List of tasks with details
    """
    # Validate parameters
    if status and status not in ["pending", "running", "completed", "failed", "cancelled"]:
        return create_error_response(
            error_type=ErrorTypes.VALIDATION_ERROR,
            message=f"Invalid status: {status}"
        )
    
    # Build filters
    filters = {}
    if status:
        filters["status"] = status
    if task_type:
        filters["task_type"] = task_type
    if created_after:
        filters["created_after"] = datetime.fromisoformat(created_after)
    if created_before:
        filters["created_before"] = datetime.fromisoformat(created_before)
    
    # Get tasks
    tasks, total = await list_tasks(
        filters=filters,
        limit=min(limit, 100),
        offset=offset
    )
    
    return {
        "total": total,
        "offset": offset,
        "limit": limit,
        "has_more": (offset + len(tasks)) < total,
        "tasks": [
            {
                "task_id": t.task_id,
                "task_type": t.task_type,
                "task_name": t.task_name,
                "status": t.status,
                "progress": t.progress,
                "progress_message": t.progress_message,
                "created_at": t.created_at.isoformat(),
                "updated_at": t.updated_at.isoformat(),
                "duration": _calculate_duration(t)
            }
            for t in tasks
        ]
    }

@mcp.tool()
@handle_tool_errors
async def task_cancel(task_id: str) -> Dict[str, Any]:
    """
    Cancel a running task
    
    Args:
        task_id: Task UUID to cancel
        
    Returns:
        Cancellation result
    """
    task = await get_task_by_id(task_id)
    
    if not task:
        return create_error_response(
            error_type=ErrorTypes.NOT_FOUND,
            message=f"Task {task_id} not found"
        )
    
    if task.status not in ["pending", "running"]:
        return create_error_response(
            error_type=ErrorTypes.INVALID_STATE,
            message=f"Cannot cancel task in {task.status} state"
        )
    
    # Update task status
    await _update_task_status(task_id, "cancelled")
    
    # Try to revoke Celery task
    revoked = await _revoke_celery_task(task_id)
    
    return {
        "task_id": task_id,
        "status": "cancelled",
        "revoked": revoked,
        "message": "Task cancellation requested"
    }

@async_db_operation
@track_sync_calls
def _update_task_status(task_id: str, status: str):
    """Update task status"""
    from core.models import TaskResult
    
    TaskResult.objects.filter(task_id=task_id).update(
        status=status,
        updated_at=datetime.now()
    )

async def _revoke_celery_task(task_id: str) -> bool:
    """Revoke Celery task"""
    try:
        from celery import current_app
        current_app.control.revoke(task_id, terminate=True)
        return True
    except Exception as e:
        logger.warning(f"Could not revoke Celery task: {e}")
        return False

@mcp.tool()
@handle_tool_errors
async def task_retry(task_id: str) -> Dict[str, Any]:
    """
    Retry a failed task
    
    Args:
        task_id: Task UUID to retry
        
    Returns:
        New task creation result
    """
    task = await get_task_by_id(task_id)
    
    if not task:
        return create_error_response(
            error_type=ErrorTypes.NOT_FOUND,
            message=f"Task {task_id} not found"
        )
    
    if task.status != "failed":
        return create_error_response(
            error_type=ErrorTypes.INVALID_STATE,
            message=f"Can only retry failed tasks, current status: {task.status}"
        )
    
    # Create new task with same parameters
    return await task_create_import(
        task_type=task.task_type,
        parameters=task.parameters,
        priority="normal"
    )

@mcp.tool()
@handle_tool_errors
async def task_get_queue_stats() -> Dict[str, Any]:
    """
    Get task queue statistics
    
    Returns:
        Queue statistics and task counts
    """
    stats = await _get_queue_statistics()
    
    return {
        "queues": stats["queues"],
        "total_pending": stats["total_pending"],
        "total_running": stats["total_running"],
        "total_completed_today": stats["total_completed_today"],
        "total_failed_today": stats["total_failed_today"],
        "average_duration": stats["average_duration"],
        "task_types": stats["task_types"]
    }

@async_db_operation
@track_sync_calls
def _get_queue_statistics():
    """Get comprehensive queue statistics"""
    from core.models import TaskResult
    from django.db.models import Count, Avg
    from django.utils import timezone
    from datetime import timedelta
    
    now = timezone.now()
    today_start = now.replace(hour=0, minute=0, second=0, microsecond=0)
    
    # Get counts by status
    status_counts = TaskResult.objects.values('status').annotate(
        count=Count('id')
    )
    
    # Get today's completed and failed
    today_completed = TaskResult.objects.filter(
        status='completed',
        completed_at__gte=today_start
    ).count()
    
    today_failed = TaskResult.objects.filter(
        status='failed',
        updated_at__gte=today_start
    ).count()
    
    # Get average duration for completed tasks
    avg_duration = TaskResult.objects.filter(
        status='completed',
        started_at__isnull=False,
        completed_at__isnull=False
    ).aggregate(
        avg_duration=Avg('completed_at') - Avg('started_at')
    )
    
    # Get counts by task type
    type_counts = TaskResult.objects.values('task_type').annotate(
        count=Count('id'),
        pending=Count('id', filter=Q(status='pending')),
        running=Count('id', filter=Q(status='running')),
        completed=Count('id', filter=Q(status='completed')),
        failed=Count('id', filter=Q(status='failed'))
    )
    
    return {
        "queues": {
            "default": {
                "pending": next((s['count'] for s in status_counts if s['status'] == 'pending'), 0),
                "running": next((s['count'] for s in status_counts if s['status'] == 'running'), 0)
            }
        },
        "total_pending": next((s['count'] for s in status_counts if s['status'] == 'pending'), 0),
        "total_running": next((s['count'] for s in status_counts if s['status'] == 'running'), 0),
        "total_completed_today": today_completed,
        "total_failed_today": today_failed,
        "average_duration": avg_duration['avg_duration'].total_seconds() if avg_duration['avg_duration'] else 0,
        "task_types": list(type_counts)
    }
```

### 2. Create Task Validation Models

Create `mcp_server/validators/tasks.py`:

```python
"""
Task validation models
"""

from pydantic import BaseModel, Field, validator
from typing import Optional, List, Dict, Any
from datetime import datetime

class TaskCreateParams(BaseModel):
    """Task creation parameters"""
    task_type: str = Field(..., description="Task type identifier")
    parameters: Dict[str, Any] = Field(..., description="Task-specific parameters")
    priority: str = Field(default="normal", pattern="^(low|normal|high)$")
    
    @validator('task_type')
    def validate_task_type(cls, v):
        valid_types = [
            "instagram.profile",
            "instagram.posts",
            "instagram.batch_posts",
            "instagram.comments",
            "telegram.chats",
            "telegram.messages",
            "telegram.users"
        ]
        if v not in valid_types:
            raise ValueError(f"Invalid task type: {v}")
        return v

class TaskListParams(BaseModel):
    """Task listing parameters"""
    status: Optional[str] = Field(None, pattern="^(pending|running|completed|failed|cancelled)$")
    task_type: Optional[str] = None
    limit: int = Field(default=20, ge=1, le=100)
    offset: int = Field(default=0, ge=0)
    created_after: Optional[datetime] = None
    created_before: Optional[datetime] = None
```

### 3. Create Task Monitoring Utilities

Create `mcp_server/utils/task_utils.py`:

```python
"""
Task utilities for status tracking
"""

import asyncio
from typing import Dict, Any, Optional
from datetime import datetime, timedelta

import logging

logger = logging.getLogger(__name__)

class TaskMonitor:
    """Monitor running tasks and update their status"""
    
    def __init__(self):
        self.monitoring_tasks = {}
        self.update_interval = 5  # seconds
    
    async def start_monitoring(self, task_id: str):
        """Start monitoring a task"""
        if task_id not in self.monitoring_tasks:
            self.monitoring_tasks[task_id] = asyncio.create_task(
                self._monitor_task(task_id)
            )
    
    async def stop_monitoring(self, task_id: str):
        """Stop monitoring a task"""
        if task_id in self.monitoring_tasks:
            self.monitoring_tasks[task_id].cancel()
            del self.monitoring_tasks[task_id]
    
    async def _monitor_task(self, task_id: str):
        """Monitor task progress"""
        from core.models import TaskResult
        
        while True:
            try:
                # Get task status
                task = await sync_to_async(TaskResult.objects.get)(task_id=task_id)
                
                # Check if task is complete
                if task.status in ["completed", "failed", "cancelled"]:
                    await self.stop_monitoring(task_id)
                    break
                
                # Update progress if needed
                if task.status == "running":
                    # Check for stalled tasks
                    if task.updated_at < datetime.now() - timedelta(minutes=5):
                        logger.warning(f"Task {task_id} appears stalled")
                
                await asyncio.sleep(self.update_interval)
                
            except Exception as e:
                logger.error(f"Error monitoring task {task_id}: {e}")
                await self.stop_monitoring(task_id)
                break

# Global task monitor instance
task_monitor = TaskMonitor()
```

### 4. Create Task Event Broadcasting

Create `mcp_server/utils/task_events.py`:

```python
"""
Task event broadcasting for real-time updates
"""

from typing import Dict, Any, Set
import asyncio
import json

class TaskEventBroadcaster:
    """Broadcast task events to connected clients"""
    
    def __init__(self):
        self.subscribers: Dict[str, Set[asyncio.Queue]] = {}
    
    async def subscribe(self, task_id: str) -> asyncio.Queue:
        """Subscribe to task events"""
        if task_id not in self.subscribers:
            self.subscribers[task_id] = set()
        
        queue = asyncio.Queue()
        self.subscribers[task_id].add(queue)
        return queue
    
    async def unsubscribe(self, task_id: str, queue: asyncio.Queue):
        """Unsubscribe from task events"""
        if task_id in self.subscribers:
            self.subscribers[task_id].discard(queue)
            if not self.subscribers[task_id]:
                del self.subscribers[task_id]
    
    async def broadcast(self, task_id: str, event: Dict[str, Any]):
        """Broadcast event to all subscribers"""
        if task_id in self.subscribers:
            event_json = json.dumps(event)
            
            # Send to all subscribers
            for queue in list(self.subscribers[task_id]):
                try:
                    await queue.put(event_json)
                except asyncio.QueueFull:
                    # Remove full queues
                    self.subscribers[task_id].discard(queue)

# Global event broadcaster
task_events = TaskEventBroadcaster()
```

## Key Features to Implement

### 1. Task Creation
- Validate task type and parameters
- Generate unique task IDs
- Create TaskResult records
- Submit to Celery queue
- Return task ID immediately

### 2. Task Status Tracking
- Real-time progress updates
- Status transitions
- Duration calculation
- Error tracking
- Celery integration

### 3. Task Management
- List tasks with filters
- Cancel running tasks
- Retry failed tasks
- Queue statistics
- Task history

### 4. Event Broadcasting
- WebSocket support
- Server-Sent Events
- Progress notifications
- Completion alerts

## Testing Strategy

### Unit Tests
```python
# Test task creation
async def test_create_task():
    result = await task_create_import(
        task_type="instagram.profile",
        parameters={"username": "test"}
    )
    assert "task_id" in result
    assert result["status"] == "created"

# Test parameter validation
async def test_invalid_task_type():
    result = await task_create_import(
        task_type="invalid.type",
        parameters={}
    )
    assert result["error_type"] == "validation_error"

# Test task status
async def test_get_task_status():
    # Create task first
    create_result = await task_create_import(
        task_type="instagram.profile",
        parameters={"username": "test"}
    )
    
    # Get status
    status = await task_get_status(create_result["task_id"])
    assert status["task_id"] == create_result["task_id"]
    assert status["status"] in ["pending", "running"]
```

## Security Considerations

1. **Static Task Registry**: No dynamic imports
2. **Parameter Validation**: Strict validation for all inputs
3. **Rate Limiting**: Prevent task spam
4. **Authentication**: Require valid MCP session
5. **Resource Limits**: Max tasks per user/period

## Performance Optimization

1. **Async Operations**: All DB operations async
2. **Bulk Operations**: Process multiple tasks efficiently
3. **Caching**: Cache task status for active tasks
4. **Connection Pooling**: Reuse DB connections
5. **Progress Throttling**: Limit update frequency

## Integration Points

1. **Celery**: Task execution backend
2. **Redis**: Task queue and caching
3. **Django ORM**: Task persistence
4. **WebSocket**: Real-time updates
5. **Queue Management**: Task queue statistics

## Success Criteria

- [ ] Task creation with validation
- [ ] Status tracking and updates
- [ ] Task listing with filters
- [ ] Cancel and retry functionality
- [ ] Queue statistics
- [ ] Event broadcasting
- [ ] All tests passing
- [ ] Performance benchmarks met

## Next Steps

Proceed to **012_task_results_retrieval.md** for implementing task result retrieval and management features.