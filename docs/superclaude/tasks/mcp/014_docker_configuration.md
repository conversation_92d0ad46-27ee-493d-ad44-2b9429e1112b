# Subtask 014: Docker Configuration

## Objective
Create comprehensive Docker configuration for the MCP server and SocialManager project, supporting both local development (Django runs locally, services in Docker) and production deployment (everything in Docker with ASGI support).

## Prerequisites
- Completed subtasks 001-013
- All MCP functionality implemented
- Django ASGI configuration ready

## Architecture Overview

### Development Environment
- **Django Application**: Runs locally for hot-reloading and easier debugging
- **MCP Server**: Runs in Docker container
- **Infrastructure Services**: PostgreSQL, Redis, Celery, Flower run in Docker
- **Benefits**: Fast development cycle, direct code changes, easy debugging

### Production Environment
- **Everything in Docker**: All services containerized
- **Django with ASGI**: Uses uvicorn for async support
- **Orchestration**: Docker Compose or Kubernetes
- **Security**: Non-root users, secrets management

## Implementation Steps

### 1. Main Django Dockerfile (Production)

Create `Dockerfile`:

```dockerfile
# Django Production Dockerfile with ASGI support
FROM python:3.13-slim

# Copy uv from official image for fast package management
COPY --from=ghcr.io/astral-sh/uv:latest /uv /uvx /bin/

# Set environment variables
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    UV_COMPILE_BYTECODE=1 \
    UV_LINK_MODE=copy

# Install system dependencies
RUN apt-get update && apt-get install -y \
    postgresql-client \
    build-essential \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Create non-root user
RUN groupadd -r django && useradd -r -g django django

# Set work directory
WORKDIR /app

# Copy project files
COPY --chown=django:django . /app/

# Install Python dependencies
RUN uv sync --frozen --no-dev

# Create necessary directories
RUN mkdir -p /app/media /app/static /app/logs /app/telegram_sessions \
    && chown -R django:django /app

# Collect static files
RUN uv run python manage.py collectstatic --noinput

# Switch to non-root user
USER django

# Expose Django port
EXPOSE 8000

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=40s --retries=3 \
    CMD curl -f http://localhost:8000/health/ || exit 1

# Run Django with uvicorn (ASGI)
CMD ["uv", "run", "uvicorn", "SocialManager.asgi:application", \
     "--host", "0.0.0.0", \
     "--port", "8000", \
     "--workers", "4", \
     "--loop", "asyncio", \
     "--access-log"]
```

### 2. MCP Server Dockerfile

Create `Dockerfile.mcp`:

```dockerfile
# MCP Server Dockerfile
FROM python:3.13-slim

# Copy uv from official image
COPY --from=ghcr.io/astral-sh/uv:latest /uv /uvx /bin/

# Set environment variables
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    UV_COMPILE_BYTECODE=1 \
    UV_LINK_MODE=copy

# Install system dependencies
RUN apt-get update && apt-get install -y \
    postgresql-client \
    build-essential \
    && rm -rf /var/lib/apt/lists/*

# Create non-root user
RUN groupadd -r mcp && useradd -r -g mcp mcp

# Set work directory
WORKDIR /app

# Copy project files
COPY --chown=mcp:mcp . /app/

# Install dependencies including FastMCP
RUN uv sync --frozen --no-dev

# Create log directory
RUN mkdir -p /app/logs && chown -R mcp:mcp /app/logs

# Switch to non-root user
USER mcp

# Expose MCP port
EXPOSE 8001

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=40s --retries=3 \
    CMD curl -f http://localhost:8001/health || exit 1

# Run MCP server
CMD ["uv", "run", "python", "-m", "mcp_server.main"]
```

### 3. Base Docker Compose

Update current `docker-compose.yml`:

```yaml
services:
  # PostgreSQL Database
  db:
    image: postgres:latest
    volumes:
      - postgres_data:/var/lib/postgresql/data/
    environment:
      - POSTGRES_PASSWORD=postgres
      - POSTGRES_USER=postgres
      - POSTGRES_DB=socialmanager
    ports:
      - "5432:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres -d socialmanager"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis Cache and Message Broker
  redis:
    image: redis:latest
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5
      
  # Celery Worker
  celery:
    build:
      context: .
      dockerfile: Dockerfile.celery
    command: ["celery", "-A", "SocialManager", "worker", "-l", "info"]
    volumes:
      - .:/app
      - /app/.venv  # Exclude .venv from mounting
      - media_volume:/app/media
      - telegram_sessions:/app/telegram_sessions
    environment:
      - CELERY_BROKER_URL=redis://redis:6379/0
      - CELERY_RESULT_BACKEND=redis://redis:6379/1
      - DATABASE_URL=************************************/socialmanager
      - DJANGO_SETTINGS_MODULE=SocialManager.settings.local
      - PYTHONPATH=/app
      - POSTGRES_HOST=db
      - POSTGRES_PORT=5432
      - POSTGRES_DB=socialmanager
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=postgres
      - REDIS_URL=redis://redis:6379/2
    depends_on:
      db:
        condition: service_healthy
      redis:
        condition: service_healthy

  # Celery Beat Scheduler
  celery-beat:
    build:
      context: .
      dockerfile: Dockerfile.celery
    command: ["celery", "-A", "SocialManager", "beat", "-l", "info", "--scheduler", "django_celery_beat.schedulers:DatabaseScheduler"]
    volumes:
      - .:/app
      - /app/.venv  # Exclude .venv from mounting
    environment:
      - CELERY_BROKER_URL=redis://redis:6379/0
      - CELERY_RESULT_BACKEND=redis://redis:6379/1
      - DATABASE_URL=************************************/socialmanager
      - DJANGO_SETTINGS_MODULE=SocialManager.settings.local
      - PYTHONPATH=/app
      - POSTGRES_HOST=db
      - POSTGRES_PORT=5432
      - POSTGRES_DB=socialmanager
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=postgres
      - REDIS_URL=redis://redis:6379/2
    depends_on:
      db:
        condition: service_healthy
      redis:
        condition: service_healthy

  # Flower - Celery Monitoring (Optional)
  flower:
    build:
      context: .
      dockerfile: Dockerfile.celery
    command: ["celery", "-A", "SocialManager", "flower", "--port=5555"]
    ports:
      - "5555:5555"
    environment:
      - CELERY_BROKER_URL=redis://redis:6379/0
      - DJANGO_SETTINGS_MODULE=SocialManager.settings.local
      - PYTHONPATH=/app
    depends_on:
      - redis

volumes:
  postgres_data:
  redis_data:
  media_volume:
  telegram_sessions:
```

### 4. Development Docker Compose Override

Create `docker-compose.dev.yml`:

```yaml
# Development environment - Django runs locally, services in Docker
services:
  # MCP Server for development
  mcp-server:
    build:
      context: .
      dockerfile: Dockerfile.mcp
    ports:
      - "8001:8001"
    volumes:
      - ./mcp_server:/app/mcp_server
      - ./logs:/app/logs
    environment:
      - DATABASE_URL=************************************/socialmanager
      - REDIS_URL=redis://redis:6379/0
      - DJANGO_SETTINGS_MODULE=SocialManager.settings.local
      - DEBUG=True
    depends_on:
      db:
        condition: service_healthy
      redis:
        condition: service_healthy
    command: ["uv", "run", "python", "-m", "mcp_server.main", "--reload"]

  # Development database with exposed port
  db:
    ports:
      - "5432:5432"

  # Development Redis with exposed port
  redis:
    ports:
      - "6379:6379"

  # Mount local code for Celery hot-reload
  celery:
    volumes:
      - .:/app
      - /app/.venv
    environment:
      - DJANGO_SETTINGS_MODULE=SocialManager.settings.local
      - DEBUG=True

  # PgAdmin for database management
  pgadmin:
    image: dpage/pgadmin4:latest
    environment:
      PGADMIN_DEFAULT_EMAIL: <EMAIL>
      PGADMIN_DEFAULT_PASSWORD: admin
    ports:
      - "5050:80"
    depends_on:
      - db

  # MailHog for email testing
  mailhog:
    image: mailhog/mailhog:latest
    ports:
      - "1025:1025"  # SMTP
      - "8025:8025"  # Web UI
```

### 5. Production Docker Compose

Create `docker-compose.prod.yml`:

```yaml
# Production environment - Everything runs in Docker
services:
  # Django Application with ASGI
  django:
    build:
      context: .
      dockerfile: Dockerfile
    expose:
      - "8000"
    environment:
      - DJANGO_SETTINGS_MODULE=SocialManager.settings.production
      - DATABASE_URL=postgres://postgres:${DB_PASSWORD}@db:5432/socialmanager
      - REDIS_URL=redis://redis:6379/0
      - SECRET_KEY=${SECRET_KEY}
      - ALLOWED_HOSTS=${ALLOWED_HOSTS}
    volumes:
      - media_volume:/app/media
      - static_volume:/app/static
      - telegram_sessions:/app/telegram_sessions
    depends_on:
      db:
        condition: service_healthy
      redis:
        condition: service_healthy
    command: ["uv", "run", "uvicorn", "SocialManager.asgi:application", 
              "--host", "0.0.0.0", "--port", "8000", 
              "--workers", "4", "--loop", "asyncio"]

  # MCP Server
  mcp-server:
    build:
      context: .
      dockerfile: Dockerfile.mcp
    expose:
      - "8001"
    environment:
      - DATABASE_URL=postgres://postgres:${DB_PASSWORD}@db:5432/socialmanager
      - REDIS_URL=redis://redis:6379/0
      - DJANGO_SETTINGS_MODULE=SocialManager.settings.production
    depends_on:
      - django

  # Nginx Reverse Proxy
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/conf.d:/etc/nginx/conf.d:ro
      - static_volume:/app/static:ro
      - media_volume:/app/media:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
    depends_on:
      - django
      - mcp-server

  # Production Celery configuration
  celery:
    environment:
      - DJANGO_SETTINGS_MODULE=SocialManager.settings.production
      - DATABASE_URL=postgres://postgres:${DB_PASSWORD}@db:5432/socialmanager
    deploy:
      replicas: 2
      resources:
        limits:
          cpus: '1'
          memory: 1G

  # Secure PostgreSQL
  db:
    environment:
      - POSTGRES_PASSWORD=${DB_PASSWORD}
    ports: []  # Don't expose ports in production

  # Secure Redis
  redis:
    command: redis-server --requirepass ${REDIS_PASSWORD}
    ports: []  # Don't expose ports in production

volumes:
  static_volume:
  media_volume:
  postgres_data:
  redis_data:
  telegram_sessions:
```

### 6. Nginx Configuration

Create `nginx/conf.d/default.conf`:

```nginx
upstream django {
    server django:8000;
}

upstream mcp {
    server mcp-server:8001;
}

server {
    listen 80;
    server_name _;
    
    client_max_body_size 100M;
    
    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
    
    # Static files
    location /static/ {
        alias /app/static/;
        expires 30d;
        add_header Cache-Control "public, immutable";
    }
    
    # Media files
    location /media/ {
        alias /app/media/;
        expires 7d;
        add_header Cache-Control "public";
    }
    
    # Django application (ASGI)
    location / {
        proxy_pass http://django;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # WebSocket support for ASGI
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        
        # Timeouts
        proxy_connect_timeout 300s;
        proxy_send_timeout 300s;
        proxy_read_timeout 300s;
    }
    
    # MCP Server API
    location /mcp/ {
        rewrite ^/mcp/(.*) /$1 break;
        proxy_pass http://mcp;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # WebSocket support
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
    }
    
    # Health checks
    location /health {
        proxy_pass http://django/health;
        access_log off;
    }
    
    location /mcp/health {
        proxy_pass http://mcp/health;
        access_log off;
    }
}
```

### 7. Environment Configuration

Create `.env.example`:

```bash
# Django Settings
SECRET_KEY=your-secret-key-here
DEBUG=False
ALLOWED_HOSTS=localhost,127.0.0.1,yourdomain.com

# Database
DB_PASSWORD=secure-password-here
DATABASE_URL=************************************************/socialmanager

# Redis
REDIS_PASSWORD=redis-password-here
REDIS_URL=redis://:redis-password-here@redis:6379/0

# Celery
CELERY_BROKER_URL=redis://:redis-password-here@redis:6379/1
CELERY_RESULT_BACKEND=redis://:redis-password-here@redis:6379/2

# Instagram/BrightData
BRIGHTDATA_API_KEY=your-api-key
BRIGHTDATA_DATASET_ID=your-dataset-id
FIELD_ENCRYPTION_KEY=your-encryption-key

# Telegram
TELEGRAM_API_ID=your-api-id
TELEGRAM_API_HASH=your-api-hash

# Sentry (Optional)
SENTRY_DSN=your-sentry-dsn

# Google Cloud Storage (Optional)
USE_GCS=False
GCS_BUCKET_NAME=your-bucket
GOOGLE_APPLICATION_CREDENTIALS=/app/credentials/gcs-key.json
```

## Usage Commands

### Local Development

```bash
# Start infrastructure services
docker-compose -f docker-compose.yml -f docker-compose.dev.yml up -d

# Run Django locally
python manage.py runserver

# Or run Django with ASGI locally
uvicorn SocialManager.asgi:application --reload

# Access services
# - Django: http://localhost:8000
# - MCP Server: http://localhost:8001
# - PgAdmin: http://localhost:5050
# - Flower: http://localhost:5555
# - MailHog: http://localhost:8025
```

### Production Deployment

```bash
# Build all images
docker-compose -f docker-compose.yml -f docker-compose.prod.yml build

# Start production services
docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d

# Run migrations
docker-compose -f docker-compose.yml -f docker-compose.prod.yml exec django uv run python manage.py migrate

# Create superuser
docker-compose -f docker-compose.yml -f docker-compose.prod.yml exec django uv run python manage.py createsuperuser

# View logs
docker-compose -f docker-compose.yml -f docker-compose.prod.yml logs -f django

# Scale workers
docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d --scale celery=4
```

### Useful Commands

```bash
# Enter Django container shell
docker-compose exec django bash

# Django shell
docker-compose exec django uv run python manage.py shell

# Run tests
docker-compose exec django uv run pytest

# Clear Redis cache
docker-compose exec redis redis-cli FLUSHALL

# Database backup
docker-compose exec db pg_dump -U postgres socialmanager > backup.sql
```

## Best Practices

### Development
1. Use volume mounts for hot-reloading
2. Keep services minimal for faster startup
3. Use development-specific environment variables
4. Enable debug mode for better error messages

### Production
1. Use specific image tags, not 'latest'
2. Implement proper health checks
3. Use secrets management (Docker secrets or external vault)
4. Set resource limits
5. Enable logging aggregation
6. Regular security updates

### Security
1. Run containers as non-root users
2. Use read-only filesystems where possible
3. Limit network exposure
4. Rotate credentials regularly
5. Scan images for vulnerabilities

## Troubleshooting

### Common Issues

1. **Permission errors**: Ensure proper ownership of volumes
   ```bash
   docker-compose exec django chown -R django:django /app/media
   ```

2. **Database connection issues**: Check health checks and network
   ```bash
   docker-compose ps
   docker-compose exec django ping db
   ```

3. **Static files not serving**: Ensure collectstatic ran
   ```bash
   docker-compose exec django uv run python manage.py collectstatic --noinput
   ```

## Success Criteria

- [ ] Development environment works with local Django
- [ ] Production environment fully containerized
- [ ] ASGI support working correctly
- [ ] Health checks passing
- [ ] Proper logging and monitoring
- [ ] Security best practices implemented
- [ ] Documentation complete

## Next Steps

Proceed to **015_testing_strategy.md** for implementing comprehensive testing strategy and test suites.