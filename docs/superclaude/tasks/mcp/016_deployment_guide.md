# Subtask 016: Deployment Guide

## Objective
Create comprehensive deployment guide for the MCP server and SocialManager project with ASGI support, including development setup, production deployment, security hardening, scaling strategies, and maintenance procedures.

## Prerequisites
- Completed subtasks 001-015
- Docker configuration ready (from 014)
- ASGI configuration tested
- All functionality implemented

## Deployment Architecture

### Development Setup
- **Django**: Runs locally with hot-reload
- **MCP Server**: Runs in Docker container
- **Infrastructure**: PostgreSQL, Redis, Celery in Docker
- **Access**: Direct local development with containerized services

### Production Setup
- **Everything Containerized**: All services run in Docker
- **ASGI Server**: Django runs with uvicorn for async support
- **Reverse Proxy**: Nginx handles routing and static files
- **Orchestration**: Docker Compose or Kubernetes

## Development Environment Setup

### 1. Local Development Quickstart

Create `scripts/dev-setup.sh`:

```bash
#!/bin/bash

# Development environment setup script

set -euo pipefail

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m'

echo -e "${GREEN}Setting up SocialManager Development Environment${NC}"

# 1. Check prerequisites
echo -e "\n${YELLOW}Checking prerequisites...${NC}"

# Check Python
if ! command -v python3 &> /dev/null; then
    echo -e "${RED}Python 3 is not installed${NC}"
    exit 1
fi

# Check Docker
if ! command -v docker &> /dev/null; then
    echo -e "${RED}Docker is not installed${NC}"
    exit 1
fi

# Check Docker Compose
if ! command -v docker-compose &> /dev/null; then
    echo -e "${RED}Docker Compose is not installed${NC}"
    exit 1
fi

# 2. Create virtual environment
echo -e "\n${YELLOW}Creating Python virtual environment...${NC}"
python3 -m venv .venv
source .venv/bin/activate

# 3. Install uv for fast package management
echo -e "\n${YELLOW}Installing uv package manager...${NC}"
pip install --upgrade pip uv

# 4. Install dependencies
echo -e "\n${YELLOW}Installing Python dependencies...${NC}"
uv sync

# 5. Copy environment file
echo -e "\n${YELLOW}Setting up environment variables...${NC}"
if [ ! -f .env ]; then
    cp .env.example .env
    echo -e "${YELLOW}Please edit .env file with your configuration${NC}"
fi

# 6. Start Docker services
echo -e "\n${YELLOW}Starting Docker services...${NC}"
docker-compose -f docker-compose.yml -f docker-compose.dev.yml up -d

# 7. Wait for services to be ready
echo -e "\n${YELLOW}Waiting for services to be ready...${NC}"
sleep 10

# 8. Run database migrations
echo -e "\n${YELLOW}Running database migrations...${NC}"
python manage.py migrate

# 9. Create superuser
echo -e "\n${YELLOW}Creating superuser...${NC}"
python manage.py createsuperuser --no-input || true

# 10. Collect static files
echo -e "\n${YELLOW}Collecting static files...${NC}"
python manage.py collectstatic --noinput

echo -e "\n${GREEN}Development environment setup complete!${NC}"
echo -e "\nTo start developing:"
echo -e "1. Activate virtual environment: ${YELLOW}source .venv/bin/activate${NC}"
echo -e "2. Start Django server: ${YELLOW}python manage.py runserver${NC}"
echo -e "3. Or with ASGI: ${YELLOW}uvicorn SocialManager.asgi:application --reload${NC}"
echo -e "\nServices running:"
echo -e "- PostgreSQL: localhost:5432"
echo -e "- Redis: localhost:6379"
echo -e "- MCP Server: http://localhost:8001"
echo -e "- PgAdmin: http://localhost:5050"
echo -e "- Flower: http://localhost:5555"
echo -e "- MailHog: http://localhost:8025"
```

### 2. Development Workflow

```bash
# Daily development workflow

# 1. Start services
docker-compose -f docker-compose.yml -f docker-compose.dev.yml up -d

# 2. Activate virtual environment
source .venv/bin/activate

# 3. Run Django with hot-reload
python manage.py runserver

# Or run with ASGI
uvicorn SocialManager.asgi:application --reload --host 0.0.0.0 --port 8000

# 4. Run tests
uv run pytest

# 5. Check code quality
uv run black .
uv run ruff check
uv run mypy .

# 6. Stop services when done
docker-compose down
```

## Production Deployment

### 1. Pre-Deployment Checklist

Create `deployment/checklist.md`:

```markdown
# Production Deployment Checklist

## Pre-Deployment

### Code Preparation
- [ ] All tests passing
- [ ] Code review completed
- [ ] Security scan passed
- [ ] Performance benchmarks met
- [ ] Documentation updated
- [ ] Version tagged in git

### Environment Setup
- [ ] Production servers provisioned
- [ ] SSL certificates obtained
- [ ] Domain names configured
- [ ] Firewall rules configured
- [ ] Backup strategy defined
- [ ] Monitoring configured

### Django/ASGI Specific
- [ ] ASGI configuration tested
- [ ] Static files collected
- [ ] Media storage configured
- [ ] WebSocket support verified
- [ ] Health endpoints working

### Secrets Management
- [ ] Environment variables secured
- [ ] Database passwords rotated
- [ ] API keys verified
- [ ] Encryption keys backed up
- [ ] SSL certificates installed

## Deployment

### Build & Deploy
- [ ] Docker images built
- [ ] Images pushed to registry
- [ ] Containers deployed
- [ ] Health checks passing
- [ ] Migrations completed

### Verification
- [ ] Application accessible
- [ ] Admin panel working
- [ ] API endpoints responsive
- [ ] WebSocket connections stable
- [ ] Background tasks running

## Post-Deployment

### Monitoring
- [ ] Logs aggregating properly
- [ ] Metrics being collected
- [ ] Alerts configured
- [ ] Error tracking active
- [ ] Performance baseline established
```

### 2. Production Setup Script

Create `deployment/setup_production.sh`:

```bash
#!/bin/bash

# Production environment setup for Ubuntu/Debian

set -euo pipefail

# Configuration
DEPLOY_USER="socialmanager"
APP_DIR="/opt/socialmanager"
LOG_DIR="/var/log/socialmanager"

# Colors
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m'

echo -e "${GREEN}Setting up production environment${NC}"

# 1. System updates
echo -e "\n${YELLOW}Updating system packages...${NC}"
sudo apt-get update && sudo apt-get upgrade -y

# 2. Install dependencies
echo -e "\n${YELLOW}Installing system dependencies...${NC}"
sudo apt-get install -y \
    curl \
    git \
    nginx \
    supervisor \
    postgresql-client \
    redis-tools \
    certbot \
    python3-certbot-nginx

# 3. Install Docker
echo -e "\n${YELLOW}Installing Docker...${NC}"
curl -fsSL https://get.docker.com | sudo sh
sudo usermod -aG docker $USER

# 4. Install Docker Compose
echo -e "\n${YELLOW}Installing Docker Compose...${NC}"
sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" \
    -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose

# 5. Create application user
echo -e "\n${YELLOW}Creating application user...${NC}"
sudo useradd -m -s /bin/bash $DEPLOY_USER || true
sudo usermod -aG docker $DEPLOY_USER

# 6. Create directory structure
echo -e "\n${YELLOW}Creating directories...${NC}"
sudo mkdir -p $APP_DIR $LOG_DIR
sudo chown -R $DEPLOY_USER:$DEPLOY_USER $APP_DIR $LOG_DIR

# 7. Configure firewall
echo -e "\n${YELLOW}Configuring firewall...${NC}"
sudo ufw allow 22/tcp   # SSH
sudo ufw allow 80/tcp   # HTTP
sudo ufw allow 443/tcp  # HTTPS
sudo ufw --force enable

# 8. System optimization
echo -e "\n${YELLOW}Optimizing system settings...${NC}"
cat <<EOF | sudo tee /etc/sysctl.d/99-socialmanager.conf
# Network optimizations
net.core.somaxconn = 65535
net.ipv4.tcp_max_syn_backlog = 65535
net.ipv4.tcp_fin_timeout = 30
net.ipv4.ip_local_port_range = 10000 65535

# Memory optimizations
vm.overcommit_memory = 1
vm.swappiness = 10
EOF

sudo sysctl -p /etc/sysctl.d/99-socialmanager.conf

# 9. Setup logging
echo -e "\n${YELLOW}Configuring log rotation...${NC}"
cat <<EOF | sudo tee /etc/logrotate.d/socialmanager
$LOG_DIR/*.log {
    daily
    rotate 14
    compress
    delaycompress
    missingok
    notifempty
    create 0640 $DEPLOY_USER $DEPLOY_USER
}
EOF

echo -e "\n${GREEN}Production environment setup complete!${NC}"
```

### 3. Deployment Script

Create `deployment/deploy.sh`:

```bash
#!/bin/bash

# Production deployment script

set -euo pipefail

# Configuration
APP_DIR="/opt/socialmanager"
BACKUP_DIR="/opt/backups"
ENVIRONMENT="production"

# Colors
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1"
}

# Change to app directory
cd $APP_DIR

log "Starting deployment..."

# 1. Pull latest code
log "Pulling latest code..."
git fetch --all --tags
LATEST_TAG=$(git describe --tags --abbrev=0)
git checkout $LATEST_TAG

# 2. Backup current deployment
log "Creating backup..."
BACKUP_NAME="backup-$(date +%Y%m%d-%H%M%S)"
mkdir -p $BACKUP_DIR/$BACKUP_NAME

# Backup database
docker-compose exec -T db pg_dump -U postgres socialmanager | \
    gzip > $BACKUP_DIR/$BACKUP_NAME/database.sql.gz

# Backup files
cp .env $BACKUP_DIR/$BACKUP_NAME/
cp docker-compose.yml $BACKUP_DIR/$BACKUP_NAME/

# 3. Build images
log "Building Docker images..."
docker-compose -f docker-compose.yml -f docker-compose.prod.yml build

# 4. Stop services
log "Stopping services..."
docker-compose -f docker-compose.yml -f docker-compose.prod.yml down

# 5. Start services
log "Starting services..."
docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d

# 6. Run migrations
log "Running migrations..."
docker-compose -f docker-compose.yml -f docker-compose.prod.yml \
    exec -T django uv run python manage.py migrate --noinput

# 7. Collect static files
log "Collecting static files..."
docker-compose -f docker-compose.yml -f docker-compose.prod.yml \
    exec -T django uv run python manage.py collectstatic --noinput

# 8. Health check
log "Running health checks..."
sleep 10

# Check Django
if curl -f http://localhost/health >/dev/null 2>&1; then
    echo -e "${GREEN}Django health check passed${NC}"
else
    echo -e "${RED}Django health check failed${NC}"
    exit 1
fi

# Check MCP
if curl -f http://localhost/mcp/health >/dev/null 2>&1; then
    echo -e "${GREEN}MCP Server health check passed${NC}"
else
    echo -e "${RED}MCP Server health check failed${NC}"
    exit 1
fi

# 9. Clean up
log "Cleaning up old images..."
docker image prune -f

echo -e "\n${GREEN}Deployment completed successfully!${NC}"
echo -e "Version: $LATEST_TAG"
echo -e "Environment: $ENVIRONMENT"
```

### 4. SSL/TLS Configuration

Create `deployment/setup_ssl.sh`:

```bash
#!/bin/bash

# SSL certificate setup with Let's Encrypt

DOMAIN=${1:-"example.com"}
EMAIL=${2:-"<EMAIL>"}

echo "Setting up SSL for $DOMAIN"

# 1. Install certbot
sudo apt-get update
sudo apt-get install -y certbot python3-certbot-nginx

# 2. Stop nginx if running
docker-compose -f docker-compose.yml -f docker-compose.prod.yml stop nginx

# 3. Obtain certificate
sudo certbot certonly --standalone \
    -d $DOMAIN \
    -d www.$DOMAIN \
    --email $EMAIL \
    --agree-tos \
    --no-eff-email

# 4. Update nginx configuration
cat > nginx/conf.d/ssl.conf <<EOF
server {
    listen 80;
    server_name $DOMAIN www.$DOMAIN;
    return 301 https://\$server_name\$request_uri;
}

server {
    listen 443 ssl http2;
    server_name $DOMAIN www.$DOMAIN;
    
    ssl_certificate /etc/letsencrypt/live/$DOMAIN/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/$DOMAIN/privkey.pem;
    
    # SSL configuration
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    
    # HSTS
    add_header Strict-Transport-Security "max-age=63072000" always;
    
    # Include main configuration
    include /etc/nginx/conf.d/app.conf;
}
EOF

# 5. Start nginx
docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d nginx

echo "SSL setup complete!"
```

## Container Orchestration

### 1. Docker Swarm Deployment

Create `deployment/docker-swarm.yml`:

```yaml
version: '3.8'

services:
  django:
    image: socialmanager/django:latest
    deploy:
      replicas: 3
      update_config:
        parallelism: 1
        delay: 10s
      restart_policy:
        condition: on-failure
      placement:
        constraints:
          - node.role == worker
    environment:
      - DJANGO_SETTINGS_MODULE=SocialManager.settings.production
    secrets:
      - django_secret_key
      - db_password
    networks:
      - socialmanager_network

  mcp-server:
    image: socialmanager/mcp:latest
    deploy:
      replicas: 2
      update_config:
        parallelism: 1
        delay: 10s
      restart_policy:
        condition: on-failure
    networks:
      - socialmanager_network

  nginx:
    image: socialmanager/nginx:latest
    deploy:
      replicas: 2
      placement:
        constraints:
          - node.role == manager
    ports:
      - "80:80"
      - "443:443"
    networks:
      - socialmanager_network

secrets:
  django_secret_key:
    external: true
  db_password:
    external: true

networks:
  socialmanager_network:
    driver: overlay
    attachable: true
```

### 2. Kubernetes Deployment

Create `deployment/k8s/django-deployment.yaml`:

```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: django
  namespace: socialmanager
spec:
  replicas: 3
  selector:
    matchLabels:
      app: django
  template:
    metadata:
      labels:
        app: django
    spec:
      containers:
      - name: django
        image: socialmanager/django:latest
        ports:
        - containerPort: 8000
        env:
        - name: DJANGO_SETTINGS_MODULE
          value: "SocialManager.settings.production"
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: django-secrets
              key: database-url
        command:
        - "uv"
        - "run"
        - "uvicorn"
        - "SocialManager.asgi:application"
        - "--host"
        - "0.0.0.0"
        - "--port"
        - "8000"
        - "--workers"
        - "4"
        resources:
          requests:
            memory: "512Mi"
            cpu: "500m"
          limits:
            memory: "2Gi"
            cpu: "2000m"
        livenessProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 10
          periodSeconds: 5
---
apiVersion: v1
kind: Service
metadata:
  name: django
  namespace: socialmanager
spec:
  selector:
    app: django
  ports:
  - port: 8000
    targetPort: 8000
  type: ClusterIP
---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: django-hpa
  namespace: socialmanager
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: django
  minReplicas: 3
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
```

## Monitoring and Logging

### 1. Prometheus Configuration

Create `monitoring/prometheus.yml`:

```yaml
global:
  scrape_interval: 15s
  evaluation_interval: 15s

scrape_configs:
  - job_name: 'django'
    static_configs:
      - targets: ['django:8000']
    metrics_path: '/metrics'
  
  - job_name: 'mcp-server'
    static_configs:
      - targets: ['mcp-server:8001']
    metrics_path: '/metrics'
  
  - job_name: 'celery'
    static_configs:
      - targets: ['celery-exporter:9540']
  
  - job_name: 'redis'
    static_configs:
      - targets: ['redis-exporter:9121']
  
  - job_name: 'postgres'
    static_configs:
      - targets: ['postgres-exporter:9187']
```

### 2. Logging Stack

Create `docker-compose.monitoring.yml`:

```yaml
services:
  # ELK Stack for logging
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.11.0
    environment:
      - discovery.type=single-node
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data
    networks:
      - monitoring

  logstash:
    image: docker.elastic.co/logstash/logstash:8.11.0
    volumes:
      - ./logstash/pipeline:/usr/share/logstash/pipeline
    depends_on:
      - elasticsearch
    networks:
      - monitoring

  kibana:
    image: docker.elastic.co/kibana/kibana:8.11.0
    ports:
      - "5601:5601"
    environment:
      - ELASTICSEARCH_HOSTS=http://elasticsearch:9200
    depends_on:
      - elasticsearch
    networks:
      - monitoring

  # Prometheus monitoring
  prometheus:
    image: prom/prometheus:latest
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    ports:
      - "9090:9090"
    networks:
      - monitoring

  grafana:
    image: grafana/grafana:latest
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
    volumes:
      - grafana_data:/var/lib/grafana
    networks:
      - monitoring

volumes:
  elasticsearch_data:
  prometheus_data:
  grafana_data:

networks:
  monitoring:
    driver: bridge
```

## Backup and Recovery

### 1. Automated Backup Script

Create `deployment/backup/backup.sh`:

```bash
#!/bin/bash

# Automated backup script for production

set -euo pipefail

# Configuration
BACKUP_DIR="/opt/backups"
S3_BUCKET="socialmanager-backups"
RETENTION_DAYS=30
TIMESTAMP=$(date +%Y%m%d-%H%M%S)

# Create backup directory
BACKUP_PATH="$BACKUP_DIR/backup-$TIMESTAMP"
mkdir -p $BACKUP_PATH

log() {
    echo "[$(date +'%Y-%m-%d %H:%M:%S')] $1"
}

# 1. Backup database
log "Backing up database..."
docker-compose exec -T db pg_dump -U postgres socialmanager | \
    gzip > $BACKUP_PATH/database.sql.gz

# 2. Backup media files
log "Backing up media files..."
tar -czf $BACKUP_PATH/media.tar.gz -C /opt/socialmanager media/

# 3. Backup configuration
log "Backing up configuration..."
cp /opt/socialmanager/.env $BACKUP_PATH/
cp /opt/socialmanager/docker-compose.yml $BACKUP_PATH/

# 4. Backup Telegram sessions
log "Backing up Telegram sessions..."
tar -czf $BACKUP_PATH/telegram_sessions.tar.gz \
    -C /opt/socialmanager telegram_sessions/

# 5. Create manifest
cat > $BACKUP_PATH/manifest.json <<EOF
{
  "timestamp": "$TIMESTAMP",
  "version": "$(cd /opt/socialmanager && git describe --tags)",
  "files": [
    "database.sql.gz",
    "media.tar.gz",
    "telegram_sessions.tar.gz",
    ".env",
    "docker-compose.yml"
  ]
}
EOF

# 6. Upload to S3
log "Uploading to S3..."
aws s3 sync $BACKUP_PATH s3://$S3_BUCKET/backups/backup-$TIMESTAMP/

# 7. Clean up old backups
log "Cleaning up old backups..."
find $BACKUP_DIR -type d -name "backup-*" -mtime +$RETENTION_DAYS -exec rm -rf {} +

log "Backup completed successfully!"
```

### 2. Recovery Procedure

Create `deployment/backup/restore.sh`:

```bash
#!/bin/bash

# Restore from backup

set -euo pipefail

BACKUP_NAME=$1
BACKUP_DIR="/opt/backups/$BACKUP_NAME"

if [ -z "$BACKUP_NAME" ]; then
    echo "Usage: $0 <backup-name>"
    exit 1
fi

log() {
    echo "[$(date +'%Y-%m-%d %H:%M:%S')] $1"
}

log "Starting restore from $BACKUP_NAME..."

# 1. Stop services
log "Stopping services..."
cd /opt/socialmanager
docker-compose down

# 2. Restore database
log "Restoring database..."
docker-compose up -d db
sleep 10
gunzip < $BACKUP_DIR/database.sql.gz | \
    docker-compose exec -T db psql -U postgres socialmanager

# 3. Restore media files
log "Restoring media files..."
tar -xzf $BACKUP_DIR/media.tar.gz -C /opt/socialmanager/

# 4. Restore Telegram sessions
log "Restoring Telegram sessions..."
tar -xzf $BACKUP_DIR/telegram_sessions.tar.gz -C /opt/socialmanager/

# 5. Start services
log "Starting services..."
docker-compose up -d

log "Restore completed!"
```

## Maintenance Procedures

### 1. Regular Maintenance Tasks

Create `deployment/maintenance/daily.sh`:

```bash
#!/bin/bash

# Daily maintenance tasks

# 1. Clean up old logs
find /var/log/socialmanager -name "*.log" -mtime +7 -exec gzip {} \;
find /var/log/socialmanager -name "*.gz" -mtime +30 -delete

# 2. Database maintenance
docker-compose exec -T db psql -U postgres socialmanager -c "VACUUM ANALYZE;"

# 3. Clear old Celery results
docker-compose exec -T django uv run python manage.py \
    shell -c "from django_celery_results.models import TaskResult; TaskResult.objects.filter(date_created__lt=timezone.now()-timedelta(days=7)).delete()"

# 4. Docker cleanup
docker system prune -f
docker volume prune -f

# 5. Check disk space
df -h | grep -E "(^/|^Filesystem)" | mail -s "Daily Disk Usage Report" <EMAIL>
```

### 2. Security Updates

Create `deployment/maintenance/security_update.sh`:

```bash
#!/bin/bash

# Security update procedure

set -euo pipefail

log() {
    echo "[$(date +'%Y-%m-%d %H:%M:%S')] $1"
}

# 1. Update system packages
log "Updating system packages..."
sudo apt-get update
sudo apt-get upgrade -y

# 2. Update Docker images
log "Updating Docker images..."
docker-compose pull

# 3. Rebuild with latest dependencies
log "Rebuilding containers..."
docker-compose build --no-cache

# 4. Run security scan
log "Running security scan..."
docker run --rm -v /var/run/docker.sock:/var/run/docker.sock \
    aquasec/trivy image socialmanager/django:latest

# 5. Restart services with new images
log "Restarting services..."
docker-compose down
docker-compose up -d

log "Security update complete!"
```

## Performance Optimization

### 1. Django/ASGI Optimization

Update `settings/production.py`:

```python
# ASGI server configuration
ASGI_APPLICATION = "SocialManager.asgi.application"

# Database connection pooling
DATABASES["default"]["CONN_MAX_AGE"] = 600
DATABASES["default"]["OPTIONS"] = {
    "connect_timeout": 10,
    "options": "-c statement_timeout=30000"
}

# Cache configuration
CACHES = {
    "default": {
        "BACKEND": "django_redis.cache.RedisCache",
        "LOCATION": config("REDIS_URL", default="redis://redis:6379/0"),
        "OPTIONS": {
            "CLIENT_CLASS": "django_redis.client.DefaultClient",
            "CONNECTION_POOL_KWARGS": {
                "max_connections": 50,
                "retry_on_timeout": True,
            },
            "SOCKET_CONNECT_TIMEOUT": 5,
            "SOCKET_TIMEOUT": 5,
        }
    }
}

# Session configuration
SESSION_ENGINE = "django.contrib.sessions.backends.cache"
SESSION_CACHE_ALIAS = "default"

# Static files compression
STATICFILES_STORAGE = "whitenoise.storage.CompressedManifestStaticFilesStorage"
```

### 2. Nginx Optimization

Update `nginx/nginx.conf`:

```nginx
worker_processes auto;
worker_rlimit_nofile 65535;

events {
    worker_connections 4096;
    use epoll;
    multi_accept on;
}

http {
    # Performance optimizations
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    types_hash_max_size 2048;
    server_tokens off;
    
    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_types text/plain text/css text/xml text/javascript 
               application/json application/javascript application/xml+rss 
               application/x-font-ttf font/opentype image/svg+xml;
    
    # Buffer sizes
    client_body_buffer_size 128k;
    client_max_body_size 100m;
    client_header_buffer_size 1k;
    large_client_header_buffers 4 16k;
    
    # Timeouts
    client_body_timeout 60;
    client_header_timeout 60;
    send_timeout 60;
    
    # Rate limiting
    limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
    limit_req_zone $binary_remote_addr zone=login:10m rate=5r/m;
    
    include /etc/nginx/conf.d/*.conf;
}
```

## Troubleshooting Guide

### Common Issues

1. **ASGI WebSocket Connection Issues**
   ```bash
   # Check WebSocket upgrade headers
   curl -i -N -H "Connection: Upgrade" -H "Upgrade: websocket" \
        -H "Sec-WebSocket-Version: 13" \
        -H "Sec-WebSocket-Key: x3JJHMbDL1EzLkh9GBhXDw==" \
        http://localhost:8000/ws/
   
   # Check uvicorn logs
   docker-compose logs django | grep -i websocket
   ```

2. **Database Connection Pool Exhaustion**
   ```bash
   # Check current connections
   docker-compose exec db psql -U postgres -c \
       "SELECT count(*) FROM pg_stat_activity;"
   
   # Kill idle connections
   docker-compose exec db psql -U postgres -c \
       "SELECT pg_terminate_backend(pid) FROM pg_stat_activity 
        WHERE state = 'idle' AND state_change < now() - interval '10 minutes';"
   ```

3. **Celery Task Failures**
   ```bash
   # Check Celery worker status
   docker-compose exec celery celery -A SocialManager inspect active
   
   # Purge task queue
   docker-compose exec celery celery -A SocialManager purge -f
   ```

4. **Memory Issues**
   ```bash
   # Check container memory usage
   docker stats --no-stream
   
   # Increase container memory limits in docker-compose.prod.yml
   deploy:
     resources:
       limits:
         memory: 4G
   ```

## Security Best Practices

### 1. Environment Security
- Use Docker secrets for sensitive data
- Rotate credentials regularly
- Enable audit logging
- Implement network segmentation
- Use read-only root filesystems
- Run containers as non-root users

### 2. Application Security
- Enable Django security middleware
- Configure CORS properly
- Implement rate limiting
- Use HTTPS everywhere
- Regular security scans
- Dependency updates

### 3. Infrastructure Security
- Firewall configuration
- SSH key-only access
- Fail2ban for brute force protection
- Regular system updates
- Encrypted backups
- Monitoring and alerting

## Success Criteria

- [ ] Development environment runs smoothly
- [ ] Production deployment automated
- [ ] ASGI/WebSocket support verified
- [ ] SSL/TLS configured
- [ ] Monitoring and logging active
- [ ] Backup/recovery tested
- [ ] Performance optimized
- [ ] Security hardened
- [ ] Documentation complete
- [ ] Team trained

## Conclusion

This deployment guide provides a comprehensive approach to deploying the SocialManager application with ASGI support. The architecture supports both efficient local development and robust production deployment, with proper monitoring, backup, and security measures in place.

Key achievements:
- Hybrid development setup (local Django, containerized services)
- Full production containerization with ASGI
- Automated deployment procedures
- Comprehensive monitoring and logging
- Security best practices implemented
- Scalable architecture supporting growth

For ongoing operations, refer to the maintenance procedures and monitoring dashboards to ensure optimal performance and reliability.