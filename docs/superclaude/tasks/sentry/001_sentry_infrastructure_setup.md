# Задача 001: Создание инфраструктуры для интеграции Sentry

## 📋 Описание задачи
Создание базовой структуры папок и модулей для системы логирования с интеграцией Sentry. Эта задача закладывает фундамент для всей системы логирования.

## 🎯 Цели
- Создать структуру папок `core/logging/`
- Подготовить базовые модули для handlers, filters, processors
- Настроить импорты и базовые зависимости
- Создать __init__.py файлы для правильной организации пакета

## ✅ Предварительные требования
- Установлен Django и базовая структура проекта SocialManager
- Доступ к папке `core/` в проекте
- Python 3.8+ с поддержкой type hints

## 🔧 Детальные шаги реализации

### Шаг 1: Создание структуры папок
```bash
# Перейти в корень проекта
cd /path/to/SocialManager

# Создать структуру папок
mkdir -p core/logging
```

### Шаг 2: Создание базовых файлов
Создать следующие файлы в папке `core/logging/`:

#### 2.1 Создать `core/logging/__init__.py`
```python
"""
Sentry-integrated logging system for SocialManager.

This package provides:
- Context-aware logging with automatic request/task/user context
- Sensitive data filtering
- Rate limiting for duplicate messages
- Sentry integration with proper error handling
"""

from .logger import ContextLogger, get_context, set_context, clear_context

__all__ = [
    'ContextLogger',
    'get_context',
    'set_context', 
    'clear_context',
]

# Version of the logging system
__version__ = '1.0.0'
```

#### 2.2 Создать заглушку `core/logging/handlers.py`
```python
"""
Custom logging handlers for Sentry integration.

This module will contain:
- SentryHandler: Handler that sends logs to Sentry with context
- Enhanced console handlers for development
"""

import logging
from typing import Optional, Dict, Any

# TODO: Implement in task 002
class SentryHandler(logging.Handler):
    """Handler for sending logs to Sentry with context enrichment."""
    
    def __init__(self, level=logging.ERROR):
        super().__init__(level)
        # Implementation will be added in task 002
        
    def emit(self, record: logging.LogRecord) -> None:
        """Send log record to Sentry."""
        # Implementation will be added in task 002
        pass
```

#### 2.3 Создать заглушку `core/logging/filters.py`
```python
"""
Logging filters for security and performance.

This module will contain:
- SensitiveDataFilter: Removes passwords, tokens, API keys
- RateLimitFilter: Prevents log flooding
"""

import logging
from typing import List, Dict, Any

# TODO: Implement in tasks 003 and 004
class SensitiveDataFilter(logging.Filter):
    """Filter to remove sensitive data from logs."""
    
    def filter(self, record: logging.LogRecord) -> bool:
        """Filter sensitive data from log record."""
        # Implementation will be added in task 003
        return True


class RateLimitFilter(logging.Filter):
    """Filter to limit rate of duplicate messages."""
    
    def __init__(self, rate_limit_seconds: int = 60):
        super().__init__()
        self.rate_limit_seconds = rate_limit_seconds
        # Implementation will be added in task 004
        
    def filter(self, record: logging.LogRecord) -> bool:
        """Check if message should be logged based on rate limit."""
        # Implementation will be added in task 004
        return True
```

#### 2.4 Создать заглушку `core/logging/processors.py`
```python
"""
Structlog processors for enhanced logging.

This module will contain:
- Context processors
- Format processors
- Exception processors
"""

from typing import Dict, Any, Optional

# TODO: Implement with structlog integration
def add_app_context(logger, method_name, event_dict):
    """Add application context to log events."""
    # Implementation will be added with ContextLogger enhancement
    return event_dict


def filter_sensitive_keys(logger, method_name, event_dict):
    """Remove sensitive keys from event dict."""
    # Implementation will be added with ContextLogger enhancement
    return event_dict
```

#### 2.5 Создать заглушку `core/logging/testing.py`
```python
"""
Testing utilities for the logging system.

This module provides:
- Log capture fixtures
- Assertion helpers
- Mock handlers for testing
"""

import logging
from typing import List, Dict, Any
from unittest.mock import MagicMock

class LogCapture:
    """Utility for capturing logs in tests."""
    
    def __init__(self):
        self.records: List[logging.LogRecord] = []
        self.messages: List[str] = []
        
    def capture(self, record: logging.LogRecord):
        """Capture a log record."""
        self.records.append(record)
        self.messages.append(record.getMessage())
        
    def reset(self):
        """Clear captured logs."""
        self.records.clear()
        self.messages.clear()
        
    def assert_logged(self, message: str, level: Optional[int] = None):
        """Assert that a message was logged."""
        if message not in self.messages:
            raise AssertionError(f"Message '{message}' not found in logs")
            
        if level is not None:
            for record in self.records:
                if record.getMessage() == message and record.levelno != level:
                    raise AssertionError(
                        f"Message '{message}' logged with level {record.levelname}, "
                        f"expected {logging.getLevelName(level)}"
                    )
```

### Шаг 3: Обновление зависимостей
Добавить в `requirements.txt` или `pyproject.toml`:

```toml
# В pyproject.toml
[tool.poetry.dependencies]
sentry-sdk = "^1.40.0"
structlog = "^24.1.0"
python-json-logger = "^2.0.7"
```

Или в `requirements.txt`:
```
sentry-sdk>=1.40.0
structlog>=24.1.0
python-json-logger>=2.0.7
```

### Шаг 4: Проверка импортов
Создать временный скрипт для проверки:

```python
# check_imports.py
try:
    from core.logging import ContextLogger, get_context, set_context, clear_context
    print("✅ Импорты из core.logging работают")
    
    from core.logging.handlers import SentryHandler
    print("✅ SentryHandler импортируется")
    
    from core.logging.filters import SensitiveDataFilter, RateLimitFilter
    print("✅ Фильтры импортируются")
    
    from core.logging.processors import add_app_context, filter_sensitive_keys
    print("✅ Процессоры импортируются")
    
    from core.logging.testing import LogCapture
    print("✅ Тестовые утилиты импортируются")
    
except ImportError as e:
    print(f"❌ Ошибка импорта: {e}")
```

## 🧪 Проверка выполнения

### 1. Проверка структуры
```bash
# Должна быть создана следующая структура:
tree core/logging/
# core/logging/
# ├── __init__.py
# ├── handlers.py
# ├── filters.py
# ├── processors.py
# └── testing.py
```

### 2. Проверка импортов
```bash
python manage.py shell
>>> from core.logging import ContextLogger
>>> from core.logging.handlers import SentryHandler
>>> from core.logging.filters import SensitiveDataFilter, RateLimitFilter
>>> # Все импорты должны работать без ошибок
```

### 3. Проверка зависимостей
```bash
pip show sentry-sdk structlog python-json-logger
# Все пакеты должны быть установлены
```

## ⚠️ Потенциальные проблемы

### Проблема 1: Конфликт с существующими логгерами
**Решение**: Проверить, что в `core/` нет других модулей с именем `logging`

### Проблема 2: Циклические импорты
**Решение**: Использовать отложенные импорты (TYPE_CHECKING) где необходимо

### Проблема 3: Отсутствие прав на создание папок
**Решение**: Убедиться в правах доступа к папке `core/`

## 📊 Критерии успешного завершения
- ✅ Создана структура папок `core/logging/`
- ✅ Созданы все 5 базовых файлов
- ✅ Файлы содержат заглушки с правильными импортами
- ✅ Установлены необходимые зависимости
- ✅ Все импорты работают без ошибок
- ✅ Добавлены docstrings и type hints

## ⏱️ Оценка времени
**30 минут** - создание структуры и базовых файлов

## 🔗 Связанные задачи
- **Следующая**: 002_sentry_handler_implementation.md - Реализация SentryHandler
- **Зависимые**: Все последующие задачи зависят от этой инфраструктуры

## 📝 Дополнительные заметки
- Эта задача создает только структуру, реализация будет в последующих задачах
- Важно следовать соглашениям об именовании проекта
- Все файлы должны иметь docstrings для документации
- Type hints обязательны для всех публичных методов