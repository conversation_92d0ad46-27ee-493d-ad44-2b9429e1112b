# Задача 009: Конфигурация Django LOGGING

## 📋 Описание задачи
Настроить конфигурацию Django LOGGING для интеграции всех разработанных компонентов: <PERSON><PERSON><PERSON><PERSON><PERSON>, фильтров чувствительных данных и rate limiting, ContextLogger. Обеспечить правильную работу в разных окружениях (development, staging, production).

## 🎯 Цели
- Создать полную конфигурацию LOGGING в settings
- Интегрировать SentryHandler с фильтрами
- Настроить форматирование для разных handlers
- Обеспечить правильную иерархию логгеров
- Настроить уровни логирования по окружениям
- Добавить ротацию файловых логов

## ✅ Предварительные требования
- Выполнены задачи 002-008
- Все компоненты (handlers, filters) реализованы
- Понимание Django logging configuration
- Знание logging.config.dictConfig

## 🔧 Детальные шаги реализации

### Шаг 1: Создать базовую конфигурацию

```python
# settings/logging_config.py
"""
Logging configuration for the project.
"""

import os
from pathlib import Path

# Базовая директория для логов
BASE_DIR = Path(__file__).resolve().parent.parent
LOG_DIR = BASE_DIR / 'logs'
LOG_DIR.mkdir(exist_ok=True)

# Уровни логирования по окружениям
LOGGING_LEVELS = {
    'development': {
        'django': 'DEBUG',
        'django.db.backends': 'DEBUG',
        'core': 'DEBUG',
        'instagram_manager': 'DEBUG',
        'telegram_manager': 'DEBUG',
        'root': 'INFO',
    },
    'staging': {
        'django': 'INFO',
        'django.db.backends': 'INFO',
        'core': 'INFO',
        'instagram_manager': 'INFO',
        'telegram_manager': 'INFO',
        'root': 'INFO',
    },
    'production': {
        'django': 'WARNING',
        'django.db.backends': 'WARNING',
        'core': 'INFO',
        'instagram_manager': 'INFO',
        'telegram_manager': 'INFO',
        'root': 'WARNING',
    }
}


def get_logging_config(environment='development', sentry_dsn=None):
    """
    Generate logging configuration based on environment.
    
    Args:
        environment: Environment name (development/staging/production)
        sentry_dsn: Sentry DSN if available
        
    Returns:
        Dict with logging configuration
    """
    levels = LOGGING_LEVELS.get(environment, LOGGING_LEVELS['development'])
    
    config = {
        'version': 1,
        'disable_existing_loggers': False,
        
        # Форматеры
        'formatters': {
            'verbose': {
                'format': (
                    '%(asctime)s [%(levelname)s] %(name)s '
                    '%(funcName)s:%(lineno)d | %(message)s | '
                    'request_id=%(request_id)s user_id=%(user_id)s'
                ),
                'datefmt': '%Y-%m-%d %H:%M:%S',
                'defaults': {
                    'request_id': 'N/A',
                    'user_id': 'N/A',
                },
            },
            'simple': {
                'format': '%(asctime)s [%(levelname)s] %(name)s: %(message)s',
                'datefmt': '%H:%M:%S',
            },
            'json': {
                '()': 'pythonjsonlogger.jsonlogger.JsonFormatter',
                'format': (
                    '%(asctime)s %(name)s %(levelname)s %(funcName)s '
                    '%(lineno)d %(message)s %(request_id)s %(user_id)s'
                ),
                'defaults': {
                    'request_id': None,
                    'user_id': None,
                },
            },
            'colored': {
                '()': 'colorlog.ColoredFormatter',
                'format': (
                    '%(log_color)s%(asctime)s [%(levelname)s]%(reset)s '
                    '%(blue)s%(name)s%(reset)s %(message)s'
                ),
                'datefmt': '%H:%M:%S',
                'log_colors': {
                    'DEBUG': 'cyan',
                    'INFO': 'green',
                    'WARNING': 'yellow',
                    'ERROR': 'red',
                    'CRITICAL': 'red,bg_white',
                },
            },
        },
        
        # Фильтры
        'filters': {
            'sensitive_data': {
                '()': 'core.logging.filters.DjangoSensitiveDataFilter',
                'enabled': True,
                'preset': 'strict' if environment == 'production' else 'balanced',
            },
            'rate_limit': {
                '()': 'core.logging.filters.RateLimitFilter',
                'rate': 10,  # 10 одинаковых сообщений
                'per': 60,   # в минуту
                'burst': 20,  # максимум 20 в burst
            },
            'require_debug_true': {
                '()': 'django.utils.log.RequireDebugTrue',
            },
            'require_debug_false': {
                '()': 'django.utils.log.RequireDebugFalse',
            },
        },
        
        # Обработчики
        'handlers': {
            'console': {
                'level': 'DEBUG',
                'class': 'logging.StreamHandler',
                'formatter': 'colored' if environment == 'development' else 'verbose',
                'filters': ['sensitive_data'],
            },
            'file_debug': {
                'level': 'DEBUG',
                'class': 'logging.handlers.RotatingFileHandler',
                'filename': str(LOG_DIR / 'debug.log'),
                'maxBytes': 10 * 1024 * 1024,  # 10MB
                'backupCount': 5,
                'formatter': 'verbose',
                'filters': ['sensitive_data', 'require_debug_true'],
            },
            'file_error': {
                'level': 'ERROR',
                'class': 'logging.handlers.RotatingFileHandler',
                'filename': str(LOG_DIR / 'error.log'),
                'maxBytes': 10 * 1024 * 1024,  # 10MB
                'backupCount': 10,
                'formatter': 'verbose',
                'filters': ['sensitive_data'],
            },
            'file_json': {
                'level': 'INFO',
                'class': 'logging.handlers.RotatingFileHandler',
                'filename': str(LOG_DIR / 'app.json'),
                'maxBytes': 50 * 1024 * 1024,  # 50MB
                'backupCount': 7,
                'formatter': 'json',
                'filters': ['sensitive_data'],
            },
            'mail_admins': {
                'level': 'ERROR',
                'class': 'django.utils.log.AdminEmailHandler',
                'filters': ['sensitive_data', 'require_debug_false'],
                'include_html': False,
            },
        },
        
        # Логгеры
        'loggers': {
            'django': {
                'handlers': ['console', 'file_error'],
                'level': levels['django'],
                'propagate': False,
            },
            'django.request': {
                'handlers': ['file_error', 'mail_admins'],
                'level': 'ERROR',
                'propagate': False,
            },
            'django.db.backends': {
                'handlers': ['console'],
                'level': levels['django.db.backends'],
                'propagate': False,
            },
            'django.security': {
                'handlers': ['file_error', 'mail_admins'],
                'level': 'ERROR',
                'propagate': False,
            },
            
            # Наши приложения
            'core': {
                'handlers': ['console', 'file_json'],
                'level': levels['core'],
                'propagate': False,
            },
            'instagram_manager': {
                'handlers': ['console', 'file_json'],
                'level': levels['instagram_manager'],
                'propagate': False,
            },
            'telegram_manager': {
                'handlers': ['console', 'file_json'],
                'level': levels['telegram_manager'],
                'propagate': False,
            },
            
            # Celery
            'celery': {
                'handlers': ['console', 'file_json'],
                'level': 'INFO',
                'propagate': False,
            },
            
            # Корневой логгер
            'root': {
                'handlers': ['console', 'file_error'],
                'level': levels['root'],
            },
        },
    }
    
    # Добавляем Sentry handler если есть DSN
    if sentry_dsn:
        config['handlers']['sentry'] = {
            'level': 'WARNING' if environment == 'production' else 'ERROR',
            'class': 'core.logging.handlers.BufferedSentryHandler',
            'filters': ['sensitive_data', 'rate_limit'],
            'buffer_size': 100,
            'flush_timeout': 10.0,
            'flush_level': 'ERROR',
        }
        
        # Добавляем sentry handler во все логгеры
        for logger_name in config['loggers']:
            if 'sentry' not in config['loggers'][logger_name]['handlers']:
                config['loggers'][logger_name]['handlers'].append('sentry')
    
    # Специальная конфигурация для development
    if environment == 'development':
        # SQL запросы в консоль
        config['loggers']['django.db.backends']['handlers'] = ['console']
        
        # Добавляем debug файл
        config['loggers']['django']['handlers'].append('file_debug')
        config['loggers']['core']['handlers'].append('file_debug')
    
    return config
```

### Шаг 2: Интегрировать в Django settings

```python
# settings/base.py
import os
from .logging_config import get_logging_config

# Определяем окружение
ENVIRONMENT = os.getenv('DJANGO_ENV', 'development')

# Sentry DSN из переменной окружения
SENTRY_DSN = os.getenv('SENTRY_DSN')

# Генерируем конфигурацию логирования
LOGGING = get_logging_config(
    environment=ENVIRONMENT,
    sentry_dsn=SENTRY_DSN
)

# Дополнительные настройки логирования
LOGGING_CONFIG = 'logging.config.dictConfig'

# Для structlog
STRUCTLOG_CONFIGURE = True
```

### Шаг 3: Создать настройки для разных окружений

```python
# settings/development.py
from .base import *

# Включаем SQL логирование в development
LOGGING['loggers']['django.db.backends']['level'] = 'DEBUG'

# Добавляем специальный handler для SQL
LOGGING['handlers']['sql_file'] = {
    'level': 'DEBUG',
    'class': 'logging.handlers.RotatingFileHandler',
    'filename': str(LOG_DIR / 'sql.log'),
    'maxBytes': 10 * 1024 * 1024,
    'backupCount': 3,
    'formatter': 'simple',
}

LOGGING['loggers']['django.db.backends']['handlers'].append('sql_file')

# Отключаем rate limiting в development
LOGGING['filters']['rate_limit']['rate'] = 1000  # Практически без ограничений
```

```python
# settings/production.py
from .base import *

# Более строгая фильтрация в production
LOGGING['filters']['sensitive_data']['preset'] = 'strict'

# Увеличиваем размер файлов в production
LOGGING['handlers']['file_json']['maxBytes'] = 100 * 1024 * 1024  # 100MB
LOGGING['handlers']['file_json']['backupCount'] = 30  # Месяц логов

# Добавляем CloudWatch handler для AWS
if os.getenv('AWS_DEFAULT_REGION'):
    LOGGING['handlers']['cloudwatch'] = {
        'level': 'INFO',
        'class': 'watchtower.CloudWatchLogHandler',
        'log_group': f"/aws/django/{os.getenv('DEPLOYMENT_ID', 'unknown')}",
        'stream_name': os.getenv('HOSTNAME', 'unknown'),
        'formatter': 'json',
        'filters': ['sensitive_data', 'rate_limit'],
    }
    
    # Добавляем во все логгеры
    for logger_name in LOGGING['loggers']:
        LOGGING['loggers'][logger_name]['handlers'].append('cloudwatch')
```

### Шаг 4: Создать инициализацию при старте Django

```python
# core/apps.py
from django.apps import AppConfig
import logging
import structlog
from django.conf import settings


class CoreConfig(AppConfig):
    default_auto_field = 'django.db.models.BigAutoField'
    name = 'core'
    
    def ready(self):
        """Initialize logging when Django starts."""
        # Инициализируем Sentry если настроен
        if hasattr(settings, 'SENTRY_DSN') and settings.SENTRY_DSN:
            from core.logging.sentry_config import init_sentry
            init_sentry(environment=settings.ENVIRONMENT)
            
        # Конфигурируем structlog
        if getattr(settings, 'STRUCTLOG_CONFIGURE', True):
            from core.logging import configure_structlog
            configure_structlog()
            
        # Логируем успешную инициализацию
        logger = logging.getLogger(__name__)
        logger.info(
            "Logging system initialized",
            extra={
                'environment': settings.ENVIRONMENT,
                'sentry_enabled': bool(settings.SENTRY_DSN),
                'log_level': settings.LOGGING['root']['level'],
            }
        )
```

### Шаг 5: Создать management command для тестирования

```python
# core/management/commands/test_logging.py
"""
Test logging configuration.
"""

from django.core.management.base import BaseCommand
from django.conf import settings
import logging
import time
from core.logging import ContextLogger, context_logging


class Command(BaseCommand):
    help = 'Test logging configuration'
    
    def add_arguments(self, parser):
        parser.add_argument(
            '--level',
            choices=['debug', 'info', 'warning', 'error', 'critical'],
            default='all',
            help='Log level to test'
        )
        parser.add_argument(
            '--logger',
            default='all',
            help='Logger name to test (all, django, core, etc.)'
        )
        parser.add_argument(
            '--count',
            type=int,
            default=1,
            help='Number of messages to log'
        )
        
    def handle(self, *args, **options):
        level = options['level']
        logger_name = options['logger']
        count = options['count']
        
        # Определяем логгеры для тестирования
        if logger_name == 'all':
            logger_names = ['django', 'core', 'instagram_manager', 'telegram_manager']
        else:
            logger_names = [logger_name]
            
        self.stdout.write("Testing logging configuration...")
        self.stdout.write(f"Environment: {settings.ENVIRONMENT}")
        self.stdout.write(f"Sentry enabled: {bool(settings.SENTRY_DSN)}")
        self.stdout.write("-" * 50)
        
        for name in logger_names:
            self.test_logger(name, level, count)
            
        self.stdout.write("-" * 50)
        self.stdout.write(self.style.SUCCESS("Logging test completed!"))
        
    def test_logger(self, logger_name, level, count):
        """Test specific logger."""
        # Используем ContextLogger
        logger = ContextLogger.get_logger(logger_name)
        
        self.stdout.write(f"\nTesting logger: {logger_name}")
        
        # Тестируем с контекстом
        with context_logging(
            test_run=True,
            logger_name=logger_name,
            test_id="test_123"
        ):
            for i in range(count):
                if level in ['debug', 'all']:
                    logger.debug(f"Debug message #{i+1}", extra_data={'index': i})
                    
                if level in ['info', 'all']:
                    logger.info(f"Info message #{i+1}", status='testing')
                    
                if level in ['warning', 'all']:
                    logger.warning(
                        f"Warning message #{i+1}",
                        password="secret123",  # Должно быть отфильтровано
                        api_key="test_key_12345"  # Тоже должно быть отфильтровано
                    )
                    
                if level in ['error', 'all']:
                    logger.error(f"Error message #{i+1}", error_code=f"ERR_{i}")
                    
                if level in ['critical', 'all']:
                    logger.critical(f"Critical message #{i+1}")
                    
                # Небольшая задержка для rate limiting
                if count > 1:
                    time.sleep(0.1)
                    
        # Тестируем exception
        if level in ['error', 'all']:
            try:
                1 / 0
            except ZeroDivisionError:
                logger.exception("Test exception logging")
                
        self.stdout.write(f"✓ {logger_name} tested")
```

### Шаг 6: Создать настройки для Celery

```python
# settings/celery_logging.py
"""
Celery-specific logging configuration.
"""

import os
from .logging_config import get_logging_config


def get_celery_logging_config(environment='development', sentry_dsn=None):
    """
    Get logging configuration for Celery workers.
    
    Args:
        environment: Environment name
        sentry_dsn: Sentry DSN
        
    Returns:
        Logging configuration dict
    """
    # Базовая конфигурация
    config = get_logging_config(environment, sentry_dsn)
    
    # Специфичные для Celery изменения
    config['handlers']['celery_file'] = {
        'level': 'INFO',
        'class': 'logging.handlers.RotatingFileHandler',
        'filename': str(LOG_DIR / 'celery.log'),
        'maxBytes': 50 * 1024 * 1024,  # 50MB
        'backupCount': 10,
        'formatter': 'verbose',
        'filters': ['sensitive_data'],
    }
    
    # Настройка логгеров Celery
    config['loggers'].update({
        'celery': {
            'handlers': ['console', 'celery_file', 'file_error'],
            'level': 'INFO',
            'propagate': False,
        },
        'celery.task': {
            'handlers': ['console', 'celery_file'],
            'level': 'INFO',
            'propagate': False,
        },
        'celery.beat': {
            'handlers': ['console', 'celery_file'],
            'level': 'INFO',
            'propagate': False,
        },
    })
    
    if sentry_dsn:
        for logger_name in ['celery', 'celery.task', 'celery.beat']:
            config['loggers'][logger_name]['handlers'].append('sentry')
    
    return config


# В celery.py
from celery.signals import setup_logging


@setup_logging.connect
def config_loggers(*args, **kwargs):
    """Configure Celery logging."""
    from django.conf import settings
    import logging.config
    
    config = get_celery_logging_config(
        environment=settings.ENVIRONMENT,
        sentry_dsn=settings.SENTRY_DSN
    )
    
    logging.config.dictConfig(config)
```

### Шаг 7: Создать настройки для тестов

```python
# settings/test.py
from .base import *

# Упрощенная конфигурация для тестов
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'simple': {
            'format': '%(levelname)s %(message)s'
        },
    },
    'handlers': {
        'null': {
            'class': 'logging.NullHandler',
        },
        'console': {
            'class': 'logging.StreamHandler',
            'formatter': 'simple',
            'level': 'ERROR',  # Только ошибки в тестах
        },
    },
    'root': {
        'handlers': ['null'],
    },
    'loggers': {
        'django': {
            'handlers': ['null'],
            'propagate': False,
        },
        'core': {
            'handlers': ['console'],
            'level': 'ERROR',
            'propagate': False,
        },
    },
}

# Отключаем Sentry в тестах
SENTRY_DSN = None
```

## 🧪 Тестирование конфигурации

### Проверка логирования:

```bash
# Тестируем все уровни
python manage.py test_logging

# Тестируем конкретный логгер
python manage.py test_logging --logger=core --level=error

# Тестируем rate limiting
python manage.py test_logging --count=50 --level=warning
```

### Проверка файлов логов:

```bash
# Проверяем созданные файлы
ls -la logs/

# Смотрим последние логи
tail -f logs/debug.log
tail -f logs/error.log

# Проверяем JSON логи
tail -f logs/app.json | jq '.'
```

### Проверка фильтрации:

```python
# В Django shell
from core.logging import ContextLogger

logger = ContextLogger.get_logger('test')

# Должны быть отфильтрованы
logger.warning("Password is: secret123")
logger.info("API key: abcdef1234567890")

# Проверяем в логах что пароли заменены на [REDACTED]
```

## ⚠️ Потенциальные проблемы

### Проблема 1: ImportError при импорте handlers
**Симптом**: Django не может найти core.logging.handlers
**Решение**: Убедиться что PYTHONPATH настроен правильно

### Проблема 2: Permission denied для файлов логов
**Симптом**: OSError при создании log файлов
**Решение**: Создать директорию logs с правильными правами

### Проблема 3: Слишком много логов в production
**Симптом**: Быстрый рост размера логов
**Решение**: Настроить более строгие уровни и rate limiting

### Проблема 4: Потеря логов при ротации
**Симптом**: Старые логи исчезают
**Решение**: Увеличить backupCount или настроить архивацию

## 📊 Критерии успешного завершения
- ✅ Создана полная конфигурация LOGGING
- ✅ Интегрированы все компоненты (handlers, filters)
- ✅ Настроены разные конфигурации для окружений
- ✅ Работает ротация файлов
- ✅ Фильтруются чувствительные данные
- ✅ Работает rate limiting
- ✅ Логи отправляются в Sentry
- ✅ Создана команда для тестирования

## ⏱️ Оценка времени
**2 часа** - создание и тестирование конфигурации

## 🔗 Связанные задачи
- **Предыдущая**: 008_sentry_sdk_configuration.md
- **Следующая**: 010_unit_tests_filters.md
- **Использует**: Все предыдущие компоненты (002-008)

## 📝 Дополнительные заметки
- Регулярно проверять размер логов и настраивать ротацию
- Мониторить производительность логирования
- Рассмотреть использование ELK стека для анализа логов
- Настроить алерты на критические ошибки
- Документировать все кастомные настройки логирования