# Задача 011: Unit тесты для handlers

## 📋 Описание задачи
Написать полный набор unit тестов для SentryHandler и его вариантов из задачи 002. Тесты должны покрывать все методы, граничные случаи, обработку ошибок и интеграцию с Sentry SDK.

## 🎯 Цели
- Достичь 100% покрытия кода handlers
- Протестировать интеграцию с Sentry SDK
- Проверить обогащение контекстом
- Протестировать буферизацию и условную отправку
- Проверить обработку ошибок
- Создать моки для Sentry SDK

## ✅ Предварительные требования
- Выполнена задача 002 (реализован SentryHandler)
- Установлен pytest и pytest-mock
- Установлен pytest-cov для coverage
- Понимание работы mocking в Python

## 🔧 Детальные шаги реализации

### Шаг 1: Создать базовый тестовый файл с fixtures

```python
# tests/core/logging/test_sentry_handler.py
import pytest
import logging
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List
from unittest.mock import Mock, MagicMock, patch, call
import sentry_sdk
from freezegun import freeze_time

from core.logging.handlers import (
    SentryHandler,
    BufferedSentryHandler,
    ConditionalSentryHandler,
    create_sentry_handler
)


@pytest.fixture
def mock_sentry_sdk():
    """Mock Sentry SDK components."""
    with patch('core.logging.handlers.sentry_sdk') as mock_sdk:
        # Mock Hub and client
        mock_hub = Mock()
        mock_client = Mock()
        mock_hub.client = mock_client
        mock_sdk.Hub.current = mock_hub
        
        # Mock scope
        mock_scope = MagicMock()
        mock_sdk.push_scope.return_value.__enter__.return_value = mock_scope
        
        yield {
            'sdk': mock_sdk,
            'hub': mock_hub,
            'client': mock_client,
            'scope': mock_scope
        }


@pytest.fixture
def mock_context():
    """Mock get_context function."""
    with patch('core.logging.handlers.get_context') as mock_get_context:
        mock_get_context.return_value = {
            'request_id': 'req-123',
            'user_id': 42,
            'username': 'testuser',
            'user_email': '<EMAIL>',
            'correlation_id': 'corr-456',
            'task_id': 'task-789',
            'task_name': 'process_data',
            'environment': 'test',
            'service': 'api',
            'version': '1.0.0'
        }
        yield mock_get_context


@pytest.fixture
def mock_empty_context():
    """Mock empty context."""
    with patch('core.logging.handlers.get_context') as mock_get_context:
        mock_get_context.return_value = {}
        yield mock_get_context


@pytest.fixture
def log_record():
    """Create a test log record."""
    def _create_record(
        msg="Test message",
        level=logging.ERROR,
        exc_info=None,
        extra_attrs=None
    ):
        record = logging.LogRecord(
            name="test.logger",
            level=level,
            pathname="test.py",
            lineno=10,
            msg=msg,
            args=(),
            exc_info=exc_info
        )
        
        # Add extra attributes
        if extra_attrs:
            for key, value in extra_attrs.items():
                setattr(record, key, value)
                
        return record
    
    return _create_record


@pytest.fixture
def exception_info():
    """Create exception info for testing."""
    try:
        1 / 0
    except ZeroDivisionError:
        import sys
        return sys.exc_info()
```

### Шаг 2: Тесты для базового SentryHandler

```python
class TestSentryHandler:
    """Test cases for SentryHandler."""
    
    def test_init_default_params(self):
        """Test handler initialization with defaults."""
        handler = SentryHandler()
        
        assert handler.level == logging.ERROR
        assert handler.capture_message_level == logging.ERROR
        assert handler.add_breadcrumbs is True
    
    def test_init_custom_params(self):
        """Test handler initialization with custom parameters."""
        handler = SentryHandler(
            level=logging.WARNING,
            capture_message_level=logging.INFO,
            add_breadcrumbs=False
        )
        
        assert handler.level == logging.WARNING
        assert handler.capture_message_level == logging.INFO
        assert handler.add_breadcrumbs is False
    
    def test_emit_no_sentry_client(self, mock_sentry_sdk, log_record):
        """Test emit when Sentry is not initialized."""
        # Simulate no Sentry client
        mock_sentry_sdk['hub'].client = None
        
        handler = SentryHandler()
        record = log_record()
        
        # Should not raise error
        handler.emit(record)
        
        # Should not call any Sentry methods
        mock_sentry_sdk['sdk'].capture_message.assert_not_called()
        mock_sentry_sdk['sdk'].capture_exception.assert_not_called()
    
    def test_emit_with_exception(
        self, mock_sentry_sdk, mock_context, log_record, exception_info
    ):
        """Test emit with exception info."""
        handler = SentryHandler()
        record = log_record(exc_info=exception_info)
        
        handler.emit(record)
        
        # Should capture exception
        mock_sentry_sdk['sdk'].capture_exception.assert_called_once_with(
            exception_info[1]
        )
        
        # Should set context
        scope = mock_sentry_sdk['scope']
        scope.set_context.assert_called()
        scope.set_user.assert_called_with({
            'id': '42',
            'username': 'testuser',
            'email': '<EMAIL>'
        })
    
    def test_emit_with_message_error_level(
        self, mock_sentry_sdk, mock_context, log_record
    ):
        """Test emit with error message."""
        handler = SentryHandler()
        record = log_record(msg="Error occurred", level=logging.ERROR)
        
        handler.emit(record)
        
        # Should capture message
        mock_sentry_sdk['sdk'].capture_message.assert_called_once()
        call_args = mock_sentry_sdk['sdk'].capture_message.call_args
        assert "Error occurred" in call_args[0][0]
        assert call_args[1]['level'] == 'error'
    
    def test_emit_with_breadcrumb(
        self, mock_sentry_sdk, mock_context, log_record
    ):
        """Test emit with info level as breadcrumb."""
        handler = SentryHandler(add_breadcrumbs=True)
        record = log_record(msg="Info message", level=logging.INFO)
        
        handler.emit(record)
        
        # Should add breadcrumb, not capture message
        mock_sentry_sdk['sdk'].add_breadcrumb.assert_called_once()
        mock_sentry_sdk['sdk'].capture_message.assert_not_called()
        
        # Check breadcrumb data
        breadcrumb_call = mock_sentry_sdk['sdk'].add_breadcrumb.call_args
        assert breadcrumb_call[1]['message'] == "Info message"
        assert breadcrumb_call[1]['level'] == 'info'
        assert breadcrumb_call[1]['category'] == 'test.logger'
    
    def test_emit_error_handling(
        self, mock_sentry_sdk, mock_context, log_record
    ):
        """Test error handling in emit."""
        # Make capture_message raise exception
        mock_sentry_sdk['sdk'].capture_message.side_effect = Exception("Test error")
        
        handler = SentryHandler()
        with patch.object(handler, 'handleError') as mock_handle_error:
            record = log_record()
            
            # Should not raise exception
            handler.emit(record)
            
            # Should call handleError
            mock_handle_error.assert_called_once_with(record)
    
    def test_add_context_to_scope_full(
        self, mock_sentry_sdk, mock_context, log_record
    ):
        """Test adding full context to scope."""
        handler = SentryHandler()
        scope = mock_sentry_sdk['scope']
        record = log_record()
        
        handler._add_context_to_scope(scope, record)
        
        # Check request context
        scope.set_context.assert_any_call('request', {
            'request_id': 'req-123',
            'correlation_id': 'corr-456'
        })
        
        # Check user context
        scope.set_user.assert_called_with({
            'id': '42',
            'username': 'testuser',
            'email': '<EMAIL>'
        })
        
        # Check task context
        scope.set_context.assert_any_call('task', {
            'task_id': 'task-789',
            'task_name': 'process_data'
        })
        
        # Check app context
        scope.set_context.assert_any_call('app', {
            'environment': 'test',
            'service': 'api',
            'version': '1.0.0'
        })
    
    def test_add_context_to_scope_empty(
        self, mock_sentry_sdk, mock_empty_context, log_record
    ):
        """Test adding empty context to scope."""
        handler = SentryHandler()
        scope = mock_sentry_sdk['scope']
        record = log_record()
        
        handler._add_context_to_scope(scope, record)
        
        # Should not set any context
        scope.set_context.assert_not_called()
        scope.set_user.assert_not_called()
    
    def test_add_tags_to_scope(
        self, mock_sentry_sdk, mock_context, log_record
    ):
        """Test adding tags to scope."""
        handler = SentryHandler()
        scope = mock_sentry_sdk['scope']
        record = log_record(extra_attrs={'tags': {'custom': 'value'}})
        
        handler._add_tags_to_scope(scope, record)
        
        # Check base tags
        scope.set_tag.assert_any_call('logger', 'test.logger')
        scope.set_tag.assert_any_call('level', 'ERROR')
        
        # Check context tags
        scope.set_tag.assert_any_call('environment', 'test')
        scope.set_tag.assert_any_call('service', 'api')
        scope.set_tag.assert_any_call('has_user', 'true')
        scope.set_tag.assert_any_call('task_name', 'process_data')
        
        # Check custom tags
        scope.set_tag.assert_any_call('custom', 'value')
    
    def test_add_extra_to_scope(
        self, mock_sentry_sdk, mock_context, log_record
    ):
        """Test adding extra data to scope."""
        handler = SentryHandler()
        scope = mock_sentry_sdk['scope']
        record = log_record(extra_attrs={'custom_field': 'custom_value'})
        
        handler._add_extra_to_scope(scope, record)
        
        # Check standard extra data
        scope.set_extra.assert_any_call('logger', 'test.logger')
        scope.set_extra.assert_any_call('module', 'test')
        scope.set_extra.assert_any_call('function', 'test_add_extra_to_scope')
        scope.set_extra.assert_any_call('line_number', 10)
        
        # Check custom extra data
        scope.set_extra.assert_any_call('custom_field', 'custom_value')
    
    def test_level_mapping(self):
        """Test logging level mapping."""
        assert SentryHandler.LEVEL_MAPPING[logging.DEBUG] == 'debug'
        assert SentryHandler.LEVEL_MAPPING[logging.INFO] == 'info'
        assert SentryHandler.LEVEL_MAPPING[logging.WARNING] == 'warning'
        assert SentryHandler.LEVEL_MAPPING[logging.ERROR] == 'error'
        assert SentryHandler.LEVEL_MAPPING[logging.CRITICAL] == 'fatal'
```

### Шаг 3: Тесты для BufferedSentryHandler

```python
class TestBufferedSentryHandler:
    """Test cases for BufferedSentryHandler."""
    
    def test_init_defaults(self):
        """Test initialization with defaults."""
        handler = BufferedSentryHandler()
        
        assert handler.buffer_size == 10
        assert handler.flush_interval == 5.0
        assert handler.buffer == []
        assert handler.last_flush is not None
    
    def test_buffering_below_threshold(
        self, mock_sentry_sdk, mock_context, log_record
    ):
        """Test buffering when below size threshold."""
        handler = BufferedSentryHandler(buffer_size=5)
        
        # Add 3 records (below buffer_size)
        for i in range(3):
            record = log_record(msg=f"Message {i}", level=logging.WARNING)
            handler.emit(record)
        
        # Should not capture any messages yet
        mock_sentry_sdk['sdk'].capture_message.assert_not_called()
        assert len(handler.buffer) == 3
    
    def test_flush_on_buffer_size(
        self, mock_sentry_sdk, mock_context, log_record
    ):
        """Test automatic flush when buffer is full."""
        handler = BufferedSentryHandler(buffer_size=3)
        
        # Add 3 records to fill buffer
        for i in range(3):
            record = log_record(msg=f"Message {i}", level=logging.WARNING)
            handler.emit(record)
        
        # Should flush all messages
        assert mock_sentry_sdk['sdk'].capture_message.call_count == 3
        assert len(handler.buffer) == 0
    
    @freeze_time("2024-01-01 12:00:00")
    def test_flush_on_time_interval(
        self, mock_sentry_sdk, mock_context, log_record
    ):
        """Test automatic flush on time interval."""
        handler = BufferedSentryHandler(
            buffer_size=10,
            flush_interval=5.0
        )
        
        # Add first record
        record1 = log_record(msg="Message 1", level=logging.WARNING)
        handler.emit(record1)
        
        # Move time forward 6 seconds
        with freeze_time("2024-01-01 12:00:06"):
            # Add second record - should trigger flush
            record2 = log_record(msg="Message 2", level=logging.WARNING)
            handler.emit(record2)
        
        # Should flush both messages
        assert mock_sentry_sdk['sdk'].capture_message.call_count == 2
        assert len(handler.buffer) == 0
    
    def test_immediate_flush_on_error(
        self, mock_sentry_sdk, mock_context, log_record
    ):
        """Test immediate flush for error level."""
        handler = BufferedSentryHandler(buffer_size=10)
        
        # Add warning (should buffer)
        warning_record = log_record(msg="Warning", level=logging.WARNING)
        handler.emit(warning_record)
        
        # Add error (should flush immediately)
        error_record = log_record(msg="Error", level=logging.ERROR)
        handler.emit(error_record)
        
        # Should flush both messages
        assert mock_sentry_sdk['sdk'].capture_message.call_count == 2
        assert len(handler.buffer) == 0
    
    def test_manual_flush(self, mock_sentry_sdk, mock_context, log_record):
        """Test manual flush."""
        handler = BufferedSentryHandler(buffer_size=10)
        
        # Add some records
        for i in range(3):
            record = log_record(msg=f"Message {i}", level=logging.WARNING)
            handler.emit(record)
        
        # Manual flush
        handler.flush()
        
        # Should flush all messages
        assert mock_sentry_sdk['sdk'].capture_message.call_count == 3
        assert len(handler.buffer) == 0
    
    def test_flush_empty_buffer(self, mock_sentry_sdk):
        """Test flush with empty buffer."""
        handler = BufferedSentryHandler()
        
        # Flush empty buffer
        handler.flush()
        
        # Should not call capture_message
        mock_sentry_sdk['sdk'].capture_message.assert_not_called()
    
    def test_close_handler(self, mock_sentry_sdk, mock_context, log_record):
        """Test closing handler flushes buffer."""
        handler = BufferedSentryHandler(buffer_size=10)
        
        # Add some records
        for i in range(3):
            record = log_record(msg=f"Message {i}", level=logging.WARNING)
            handler.emit(record)
        
        # Close handler
        handler.close()
        
        # Should flush all messages
        assert mock_sentry_sdk['sdk'].capture_message.call_count == 3
        assert len(handler.buffer) == 0
    
    def test_error_handling_during_flush(
        self, mock_sentry_sdk, mock_context, log_record
    ):
        """Test error handling during flush."""
        handler = BufferedSentryHandler(buffer_size=2)
        
        # Make first capture fail
        mock_sentry_sdk['sdk'].capture_message.side_effect = [
            Exception("Test error"),
            None
        ]
        
        with patch.object(handler, 'handleError') as mock_handle_error:
            # Add records to trigger flush
            record1 = log_record(msg="Message 1")
            record2 = log_record(msg="Message 2")
            handler.emit(record1)
            handler.emit(record2)
            
            # First record should error, second should succeed
            mock_handle_error.assert_called_once()
            assert mock_sentry_sdk['sdk'].capture_message.call_count == 2
            assert len(handler.buffer) == 0
```

### Шаг 4: Тесты для ConditionalSentryHandler

```python
class TestConditionalSentryHandler:
    """Test cases for ConditionalSentryHandler."""
    
    def test_init_defaults(self):
        """Test initialization with defaults."""
        handler = ConditionalSentryHandler()
        
        assert handler.exclude_loggers == set()
        assert handler.include_loggers == set()
        assert handler.condition_func is None
    
    def test_exclude_loggers(
        self, mock_sentry_sdk, mock_context, log_record
    ):
        """Test excluding specific loggers."""
        handler = ConditionalSentryHandler(
            exclude_loggers=['urllib3', 'requests']
        )
        
        # Test excluded logger
        excluded_record = log_record()
        excluded_record.name = 'urllib3.connectionpool'
        handler.emit(excluded_record)
        
        # Should not capture
        mock_sentry_sdk['sdk'].capture_message.assert_not_called()
        
        # Test allowed logger
        allowed_record = log_record()
        allowed_record.name = 'myapp.module'
        handler.emit(allowed_record)
        
        # Should capture
        mock_sentry_sdk['sdk'].capture_message.assert_called_once()
    
    def test_include_loggers(
        self, mock_sentry_sdk, mock_context, log_record
    ):
        """Test including only specific loggers."""
        handler = ConditionalSentryHandler(
            include_loggers=['myapp', 'important']
        )
        
        # Test excluded logger
        excluded_record = log_record()
        excluded_record.name = 'django.request'
        handler.emit(excluded_record)
        
        # Should not capture
        mock_sentry_sdk['sdk'].capture_message.assert_not_called()
        
        # Test included logger
        included_record = log_record()
        included_record.name = 'myapp'
        handler.emit(included_record)
        
        # Should capture
        mock_sentry_sdk['sdk'].capture_message.assert_called_once()
    
    def test_custom_condition_func(
        self, mock_sentry_sdk, mock_context, log_record
    ):
        """Test custom condition function."""
        def condition(record):
            # Only send if message contains "critical"
            return "critical" in record.getMessage().lower()
        
        handler = ConditionalSentryHandler(condition_func=condition)
        
        # Test non-matching message
        normal_record = log_record(msg="Normal error")
        handler.emit(normal_record)
        
        # Should not capture
        mock_sentry_sdk['sdk'].capture_message.assert_not_called()
        
        # Test matching message
        critical_record = log_record(msg="Critical system failure")
        handler.emit(critical_record)
        
        # Should capture
        mock_sentry_sdk['sdk'].capture_message.assert_called_once()
    
    def test_combined_conditions(
        self, mock_sentry_sdk, mock_context, log_record
    ):
        """Test combination of include and condition function."""
        def has_user_context(record):
            return hasattr(record, 'user_id')
        
        handler = ConditionalSentryHandler(
            include_loggers=['myapp'],
            condition_func=has_user_context
        )
        
        # Test: right logger, no user context
        record1 = log_record()
        record1.name = 'myapp'
        handler.emit(record1)
        
        # Should not capture
        mock_sentry_sdk['sdk'].capture_message.assert_not_called()
        
        # Test: right logger, has user context
        record2 = log_record(extra_attrs={'user_id': 123})
        record2.name = 'myapp'
        handler.emit(record2)
        
        # Should capture
        mock_sentry_sdk['sdk'].capture_message.assert_called_once()
    
    def test_should_emit_method(self):
        """Test should_emit logic directly."""
        handler = ConditionalSentryHandler(
            exclude_loggers=['excluded'],
            include_loggers=['included']
        )
        
        # Test excluded logger
        excluded_record = Mock(name='excluded')
        assert not handler.should_emit(excluded_record)
        
        # Test included logger
        included_record = Mock(name='included')
        assert handler.should_emit(included_record)
        
        # Test non-included logger when include list is set
        other_record = Mock(name='other')
        assert not handler.should_emit(other_record)
```

### Шаг 5: Тесты для фабричной функции

```python
class TestCreateSentryHandler:
    """Test cases for create_sentry_handler factory."""
    
    def test_create_basic_handler(self):
        """Test creating basic handler."""
        handler = create_sentry_handler()
        
        assert isinstance(handler, SentryHandler)
        assert handler.level == logging.ERROR
    
    def test_create_with_string_level(self):
        """Test creating handler with string level."""
        handler = create_sentry_handler(level='WARNING')
        
        assert handler.level == logging.WARNING
    
    def test_create_buffered_handler(self):
        """Test creating buffered handler."""
        handler = create_sentry_handler(
            buffered=True,
            buffer_size=20,
            flush_interval=10.0
        )
        
        assert isinstance(handler, BufferedSentryHandler)
        assert handler.buffer_size == 20
        assert handler.flush_interval == 10.0
    
    def test_create_conditional_handler(self):
        """Test creating conditional handler."""
        handler = create_sentry_handler(
            conditional=True,
            exclude_loggers=['test']
        )
        
        assert isinstance(handler, ConditionalSentryHandler)
        assert 'test' in handler.exclude_loggers
    
    def test_create_invalid_combination(self):
        """Test error for invalid handler combination."""
        with pytest.raises(ValueError, match="Cannot use both"):
            create_sentry_handler(buffered=True, conditional=True)
    
    def test_pass_through_kwargs(self):
        """Test passing through additional kwargs."""
        handler = create_sentry_handler(
            level='INFO',
            capture_message_level=logging.WARNING,
            add_breadcrumbs=False
        )
        
        assert handler.level == logging.INFO
        assert handler.capture_message_level == logging.WARNING
        assert handler.add_breadcrumbs is False
```

### Шаг 6: Интеграционные тесты

```python
class TestSentryIntegration:
    """Integration tests with real Sentry SDK."""
    
    @pytest.mark.integration
    def test_full_flow_with_context(self, log_record):
        """Test full flow with context and Sentry."""
        with patch('sentry_sdk.init') as mock_init:
            with patch('sentry_sdk.capture_message') as mock_capture:
                with patch('sentry_sdk.push_scope') as mock_push_scope:
                    # Mock scope
                    mock_scope = MagicMock()
                    mock_push_scope.return_value.__enter__.return_value = mock_scope
                    
                    # Setup context
                    with patch('core.logging.handlers.get_context') as mock_context:
                        mock_context.return_value = {
                            'request_id': 'test-123',
                            'user_id': 1
                        }
                        
                        # Create handler and logger
                        handler = SentryHandler(level=logging.ERROR)
                        logger = logging.getLogger('test.integration')
                        logger.addHandler(handler)
                        logger.setLevel(logging.DEBUG)
                        
                        # Log error
                        logger.error("Integration test error", extra={'custom': 'data'})
                        
                        # Verify Sentry calls
                        mock_capture.assert_called_once()
                        mock_scope.set_context.assert_called()
                        mock_scope.set_tag.assert_called()
    
    @pytest.mark.integration  
    def test_exception_capture_flow(self):
        """Test exception capture flow."""
        with patch('sentry_sdk.capture_exception') as mock_capture_exc:
            with patch('sentry_sdk.push_scope'):
                handler = SentryHandler()
                logger = logging.getLogger('test.exception')
                logger.addHandler(handler)
                
                try:
                    # Cause an exception
                    result = 1 / 0
                except ZeroDivisionError:
                    logger.exception("Division by zero")
                
                # Verify exception was captured
                mock_capture_exc.assert_called_once()
                exc_arg = mock_capture_exc.call_args[0][0]
                assert isinstance(exc_arg, ZeroDivisionError)
```

### Шаг 7: Тесты производительности и многопоточности

```python
class TestPerformanceAndThreading:
    """Test performance and thread safety."""
    
    def test_handler_performance(
        self, mock_sentry_sdk, mock_context, log_record
    ):
        """Test handler performance with many records."""
        import time
        
        handler = SentryHandler()
        records = [log_record(msg=f"Message {i}") for i in range(1000)]
        
        start = time.time()
        for record in records:
            handler.emit(record)
        elapsed = time.time() - start
        
        # Should handle 1000 records quickly (< 1 second)
        assert elapsed < 1.0
        assert mock_sentry_sdk['sdk'].capture_message.call_count == 1000
    
    def test_buffered_handler_memory(
        self, mock_sentry_sdk, mock_context, log_record
    ):
        """Test buffered handler memory usage."""
        import sys
        
        handler = BufferedSentryHandler(buffer_size=1000)
        
        # Fill buffer
        for i in range(999):
            record = log_record(msg=f"Message {i}", level=logging.INFO)
            handler.emit(record)
        
        # Check buffer size in memory
        buffer_size = sys.getsizeof(handler.buffer)
        # Should be reasonable (< 1MB for 999 records)
        assert buffer_size < 1024 * 1024
    
    def test_thread_safety(
        self, mock_sentry_sdk, mock_context, log_record
    ):
        """Test thread safety of handler."""
        import threading
        import queue
        
        handler = BufferedSentryHandler(buffer_size=100)
        errors = queue.Queue()
        
        def log_messages(thread_id):
            try:
                for i in range(10):
                    record = log_record(
                        msg=f"Thread {thread_id} message {i}",
                        level=logging.WARNING
                    )
                    handler.emit(record)
            except Exception as e:
                errors.put(e)
        
        # Create multiple threads
        threads = []
        for i in range(10):
            thread = threading.Thread(target=log_messages, args=(i,))
            threads.append(thread)
            thread.start()
        
        # Wait for all threads
        for thread in threads:
            thread.join()
        
        # Ensure final flush
        handler.flush()
        
        # Check no errors occurred
        assert errors.empty()
        
        # Should have captured all messages
        assert mock_sentry_sdk['sdk'].capture_message.call_count == 100
```

### Шаг 8: Добавить вспомогательные утилиты для тестов

```python
# tests/core/logging/test_utils.py
"""Utilities for testing Sentry handlers."""

def assert_sentry_context_called(mock_scope, expected_context):
    """Assert Sentry scope was called with expected context."""
    for call in mock_scope.set_context.call_args_list:
        context_type, context_data = call[0]
        if context_type in expected_context:
            for key, value in expected_context[context_type].items():
                assert context_data.get(key) == value


def assert_sentry_tags_called(mock_scope, expected_tags):
    """Assert Sentry scope was called with expected tags."""
    actual_tags = {}
    for call in mock_scope.set_tag.call_args_list:
        tag_name, tag_value = call[0]
        actual_tags[tag_name] = tag_value
    
    for key, value in expected_tags.items():
        assert actual_tags.get(key) == value


def create_mock_record_with_exception():
    """Create a log record with real exception info."""
    try:
        raise ValueError("Test exception")
    except ValueError:
        import sys
        record = logging.LogRecord(
            name="test",
            level=logging.ERROR,
            pathname="test.py",
            lineno=1,
            msg="Error with exception",
            args=(),
            exc_info=sys.exc_info()
        )
        return record
```

### Шаг 9: Конфигурационный файл для pytest

```ini
# pytest.ini (дополнение)
[tool:pytest]
markers =
    integration: Integration tests requiring external services
    performance: Performance tests
    threading: Threading and concurrency tests

testpaths = tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*

# Coverage settings for handlers
[coverage:run]
omit =
    */tests/*
    */migrations/*
    */venv/*
    */.venv/*
```

## 🧪 Тестирование

### Команды для запуска тестов:

```bash
# Запустить все тесты handlers
uv run pytest tests/core/logging/test_sentry_handler.py -v

# Запустить с покрытием
uv run pytest tests/core/logging/test_sentry_handler.py --cov=core.logging.handlers --cov-report=html

# Запустить только unit тесты
uv run pytest tests/core/logging/test_sentry_handler.py -v -m "not integration"

# Запустить с отладкой
uv run pytest tests/core/logging/test_sentry_handler.py -vvs -k "test_emit_with_exception"

# Запустить тесты производительности
uv run pytest tests/core/logging/test_sentry_handler.py -v -m performance
```

## ⚠️ Потенциальные проблемы

### Проблема 1: Моки Sentry SDK
**Симптом**: Тесты падают из-за неправильных моков
**Решение**: Проверить структуру SDK и обновить моки

### Проблема 2: Таймауты в тестах
**Симптом**: Тесты с freeze_time работают медленно
**Решение**: Использовать mock для datetime вместо freeze_time

### Проблема 3: Утечки памяти в тестах
**Симптом**: Память растет при повторных запусках
**Решение**: Явно очищать буферы и закрывать handlers

### Проблема 4: Race conditions в многопоточных тестах
**Симптом**: Нестабильные результаты тестов
**Решение**: Добавить синхронизацию и увеличить таймауты

## 📊 Критерии успешного завершения
- ✅ Покрытие кода handlers > 95%
- ✅ Все методы SentryHandler протестированы
- ✅ BufferedSentryHandler тесты включают граничные случаи
- ✅ ConditionalSentryHandler тесты покрывают все условия
- ✅ Фабричная функция протестирована
- ✅ Интеграционные тесты с Sentry SDK
- ✅ Тесты производительности и многопоточности
- ✅ Обработка ошибок протестирована

## ⏱️ Оценка времени
**2.5 часа** - написание всех тестов

## 🔗 Связанные задачи
- **Предыдущая**: 010_unit_tests_filters.md
- **Следующая**: 012_integration_tests.md
- **Тестирует**: 002_sentry_handler_implementation.md

## 📝 Дополнительные заметки
- Важно тестировать все пути кода в handlers
- Моки должны точно соответствовать Sentry SDK API
- Тесты производительности помогают выявить узкие места
- Многопоточные тесты критичны для production