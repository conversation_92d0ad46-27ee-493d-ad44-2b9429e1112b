# Задача 007: Миграция на ContextLogger

## 📋 Описание задачи
Мигрировать существующий код с использования стандартного `logging.getLogger()` на новую систему `ContextLogger`. Создать автоматический скрипт миграции и руководство для ручной миграции сложных случаев.

## 🎯 Цели
- Создать скрипт автоматической миграции
- Написать руководство по ручной миграции
- Обеспечить обратную совместимость
- Минимизировать изменения в существующем коде
- Сохранить все существующие логи

## ✅ Предварительные требования
- Выполнена задача 005 (реализован ContextLogger)
- Понимание AST (Abstract Syntax Tree) для автоматической миграции
- Резервная копия кода перед миграцией
- Полное покрытие тестами

## 🔧 Детальные шаги реализации

### Шаг 1: Создать скрипт автоматической миграции

```python
# scripts/migrate_to_context_logger.py
#!/usr/bin/env python3
"""
Migration script to convert logging.getLogger() to ContextLogger.

Usage:
    python migrate_to_context_logger.py [--dry-run] [--backup] <path>
    
Options:
    --dry-run    Show what would be changed without modifying files
    --backup     Create .bak files before modification
    <path>       Directory or file to migrate
"""

import ast
import astor
import os
import sys
import argparse
import shutil
from pathlib import Path
from typing import List, Tuple, Set, Optional
import difflib
from datetime import datetime


class LoggerMigrationTransformer(ast.NodeTransformer):
    """
    AST transformer to migrate logging code to ContextLogger.
    
    Transformations:
    - logging.getLogger() -> ContextLogger.get_logger()
    - import logging -> also import ContextLogger
    - logger = logging.getLogger(__name__) -> logger = ContextLogger.get_logger(__name__)
    """
    
    def __init__(self):
        self.imports_to_add: Set[str] = set()
        self.has_logging_import = False
        self.has_context_logger_import = False
        self.logger_variables: Set[str] = set()
        self.changes_made = False
        
    def visit_Import(self, node: ast.Import) -> ast.Import:
        """Handle 'import logging' statements."""
        for alias in node.names:
            if alias.name == 'logging':
                self.has_logging_import = True
        return node
    
    def visit_ImportFrom(self, node: ast.ImportFrom) -> ast.ImportFrom:
        """Handle 'from logging import ...' statements."""
        if node.module == 'logging':
            self.has_logging_import = True
        elif node.module == 'core.logging' or node.module == 'core.logging.logger':
            # Check if ContextLogger is already imported
            for alias in node.names:
                if alias.name == 'ContextLogger' or alias.name == 'get_logger':
                    self.has_context_logger_import = True
        return node
    
    def visit_Assign(self, node: ast.Assign) -> ast.Assign:
        """Transform logger assignments."""
        # Check for pattern: logger = logging.getLogger(...)
        if (isinstance(node.value, ast.Call) and
            isinstance(node.value.func, ast.Attribute) and
            isinstance(node.value.func.value, ast.Name) and
            node.value.func.value.id == 'logging' and
            node.value.func.attr == 'getLogger'):
            
            # Remember the variable name(s)
            for target in node.targets:
                if isinstance(target, ast.Name):
                    self.logger_variables.add(target.id)
            
            # Transform to ContextLogger.get_logger(...)
            new_func = ast.Attribute(
                value=ast.Name(id='ContextLogger', ctx=ast.Load()),
                attr='get_logger',
                ctx=ast.Load()
            )
            node.value.func = new_func
            self.imports_to_add.add('ContextLogger')
            self.changes_made = True
            
        return node
    
    def visit_Call(self, node: ast.Call) -> ast.Call:
        """Transform direct logging.getLogger() calls."""
        if (isinstance(node.func, ast.Attribute) and
            isinstance(node.func.value, ast.Name) and
            node.func.value.id == 'logging' and
            node.func.attr == 'getLogger'):
            
            # Transform to ContextLogger.get_logger()
            new_func = ast.Attribute(
                value=ast.Name(id='ContextLogger', ctx=ast.Load()),
                attr='get_logger',
                ctx=ast.Load()
            )
            node.func = new_func
            self.imports_to_add.add('ContextLogger')
            self.changes_made = True
            
        # Also handle getLogger() without logging prefix (if imported)
        elif (isinstance(node.func, ast.Name) and
              node.func.id == 'getLogger'):
            # Replace with get_logger from core.logging
            node.func.id = 'get_logger'
            self.imports_to_add.add('get_logger')
            self.changes_made = True
            
        return node


def add_imports_to_tree(tree: ast.Module, imports_to_add: Set[str]) -> None:
    """Add necessary imports to the AST."""
    if not imports_to_add:
        return
        
    # Find the position to insert imports (after existing imports)
    insert_position = 0
    for i, node in enumerate(tree.body):
        if isinstance(node, (ast.Import, ast.ImportFrom)):
            insert_position = i + 1
        elif isinstance(node, ast.Expr) and isinstance(node.value, ast.Str):
            # Skip module docstrings
            insert_position = i + 1
        else:
            break
    
    # Create import statement
    import_node = ast.ImportFrom(
        module='core.logging',
        names=[ast.alias(name=name, asname=None) for name in sorted(imports_to_add)],
        level=0
    )
    
    # Insert the import
    tree.body.insert(insert_position, import_node)


def migrate_file(file_path: Path, dry_run: bool = False, backup: bool = False) -> Optional[str]:
    """
    Migrate a single Python file.
    
    Args:
        file_path: Path to the file to migrate
        dry_run: If True, don't modify the file
        backup: If True, create a backup before modifying
        
    Returns:
        Diff of changes if any were made, None otherwise
    """
    # Read the file
    with open(file_path, 'r', encoding='utf-8') as f:
        original_content = f.read()
    
    # Parse the AST
    try:
        tree = ast.parse(original_content, str(file_path))
    except SyntaxError as e:
        print(f"Syntax error in {file_path}: {e}")
        return None
    
    # Transform the AST
    transformer = LoggerMigrationTransformer()
    new_tree = transformer.visit(tree)
    
    if not transformer.changes_made:
        return None
    
    # Add necessary imports
    add_imports_to_tree(new_tree, transformer.imports_to_add)
    
    # Convert back to source code
    new_content = astor.to_source(new_tree)
    
    # Generate diff
    diff = '\n'.join(difflib.unified_diff(
        original_content.splitlines(keepends=True),
        new_content.splitlines(keepends=True),
        fromfile=str(file_path),
        tofile=str(file_path),
        n=3
    ))
    
    # Apply changes if not dry run
    if not dry_run:
        if backup:
            backup_path = file_path.with_suffix(file_path.suffix + '.bak')
            shutil.copy2(file_path, backup_path)
            
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(new_content)
    
    return diff


def migrate_directory(directory: Path, dry_run: bool = False, 
                     backup: bool = False, exclude_patterns: List[str] = None) -> int:
    """
    Migrate all Python files in a directory.
    
    Args:
        directory: Directory to process
        dry_run: If True, don't modify files
        backup: If True, create backups
        exclude_patterns: List of patterns to exclude
        
    Returns:
        Number of files modified
    """
    exclude_patterns = exclude_patterns or ['__pycache__', '.git', 'migrations', 'venv', '.venv']
    modified_count = 0
    
    for file_path in directory.rglob('*.py'):
        # Check exclusions
        if any(pattern in str(file_path) for pattern in exclude_patterns):
            continue
            
        print(f"Processing {file_path}...")
        diff = migrate_file(file_path, dry_run, backup)
        
        if diff:
            modified_count += 1
            print(f"{'Would modify' if dry_run else 'Modified'} {file_path}")
            if dry_run:
                print(diff)
                print("-" * 80)
    
    return modified_count


def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(
        description='Migrate logging.getLogger() to ContextLogger'
    )
    parser.add_argument(
        'path',
        help='File or directory to migrate'
    )
    parser.add_argument(
        '--dry-run',
        action='store_true',
        help='Show what would be changed without modifying files'
    )
    parser.add_argument(
        '--backup',
        action='store_true',
        help='Create .bak files before modification'
    )
    parser.add_argument(
        '--exclude',
        action='append',
        help='Patterns to exclude (can be used multiple times)'
    )
    
    args = parser.parse_args()
    
    path = Path(args.path)
    
    if not path.exists():
        print(f"Error: Path {path} does not exist")
        sys.exit(1)
    
    print(f"Migration started at {datetime.now()}")
    print(f"Mode: {'DRY RUN' if args.dry_run else 'LIVE'}")
    print(f"Backup: {'Enabled' if args.backup else 'Disabled'}")
    print("-" * 80)
    
    if path.is_file():
        diff = migrate_file(path, args.dry_run, args.backup)
        if diff:
            print(f"{'Would modify' if args.dry_run else 'Modified'} {path}")
            if args.dry_run:
                print(diff)
            modified_count = 1
        else:
            print(f"No changes needed in {path}")
            modified_count = 0
    else:
        modified_count = migrate_directory(
            path, 
            args.dry_run, 
            args.backup,
            args.exclude
        )
    
    print("-" * 80)
    print(f"Migration completed: {modified_count} files {'would be' if args.dry_run else ''} modified")
    
    if args.dry_run and modified_count > 0:
        print("\nRun without --dry-run to apply changes")


if __name__ == '__main__':
    main()
```

### Шаг 2: Создать валидатор миграции

```python
# scripts/validate_migration.py
"""
Validator to ensure migration was successful and logging still works.
"""

import ast
import sys
from pathlib import Path
from typing import List, Tuple, Set


class LoggingUsageValidator(ast.NodeVisitor):
    """Validate that logging is used correctly after migration."""
    
    def __init__(self):
        self.issues: List[Tuple[int, str]] = []
        self.has_context_logger = False
        self.old_logging_usage = False
        
    def visit_ImportFrom(self, node: ast.ImportFrom):
        """Check imports."""
        if node.module == 'core.logging':
            for alias in node.names:
                if alias.name in ['ContextLogger', 'get_logger']:
                    self.has_context_logger = True
        self.generic_visit(node)
        
    def visit_Call(self, node: ast.Call):
        """Check for old logging patterns."""
        # Check for logging.getLogger()
        if (isinstance(node.func, ast.Attribute) and
            isinstance(node.func.value, ast.Name) and
            node.func.value.id == 'logging' and
            node.func.attr == 'getLogger'):
            self.issues.append((node.lineno, "Still using logging.getLogger()"))
            self.old_logging_usage = True
            
        self.generic_visit(node)


def validate_file(file_path: Path) -> List[Tuple[int, str]]:
    """Validate a single file."""
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
        
    try:
        tree = ast.parse(content, str(file_path))
    except SyntaxError:
        return [(0, "Syntax error in file")]
        
    validator = LoggingUsageValidator()
    validator.visit(tree)
    
    return validator.issues


def validate_directory(directory: Path) -> bool:
    """Validate all Python files in directory."""
    all_valid = True
    
    for file_path in directory.rglob('*.py'):
        if '__pycache__' in str(file_path):
            continue
            
        issues = validate_file(file_path)
        if issues:
            all_valid = False
            print(f"\n{file_path}:")
            for line_no, issue in issues:
                print(f"  Line {line_no}: {issue}")
                
    return all_valid
```

### Шаг 3: Руководство по ручной миграции

```python
# docs/migration_guide.md
"""
# Руководство по миграции на ContextLogger

## Автоматическая миграция

```bash
# Сначала проверьте изменения в dry-run режиме
python scripts/migrate_to_context_logger.py --dry-run .

# Создайте резервные копии и выполните миграцию
python scripts/migrate_to_context_logger.py --backup .

# Проверьте результаты
python scripts/validate_migration.py .
```

## Ручная миграция

### 1. Базовая замена

**Было:**
```python
import logging

logger = logging.getLogger(__name__)
```

**Стало:**
```python
from core.logging import ContextLogger

logger = ContextLogger.get_logger(__name__)
```

### 2. Использование контекста

**Было:**
```python
logger.info("Processing user", extra={"user_id": user.id})
```

**Стало:**
```python
logger.info("Processing user", user_id=user.id)
# или
logger.add_context(user_id=user.id)
logger.info("Processing user")
```

### 3. Временный контекст

**Было:**
```python
def process_request(request):
    logger.info(f"Processing request {request.id}")
    # ... много кода с логированием
```

**Стало:**
```python
from core.logging import context_logging

def process_request(request):
    with context_logging(request_id=request.id):
        logger.info("Processing request")
        # ... весь код автоматически включает request_id
```

### 4. В Django views

**Было:**
```python
def my_view(request):
    logger = logging.getLogger(__name__)
    logger.info(f"View called by user {request.user.id}")
```

**Стало:**
```python
from core.logging import get_logger

def my_view(request):
    logger = get_logger(__name__)
    # request_id и user_id уже добавлены middleware
    logger.info("View called")
```

### 5. В Celery tasks

**Было:**
```python
@app.task
def my_task(arg1, arg2):
    logger = logging.getLogger(__name__)
    logger.info(f"Task started with args: {arg1}, {arg2}")
```

**Стало:**
```python
from core.logging import get_logger, with_logging_context

@app.task
@with_logging_context(task_type="data_processing")
def my_task(arg1, arg2):
    logger = get_logger(__name__)
    logger.info("Task started", arg1=arg1, arg2=arg2)
```

### 6. Сложные случаи

#### Динамические логгеры

**Было:**
```python
def get_logger_for_module(module_name):
    return logging.getLogger(f"app.{module_name}")
```

**Стало:**
```python
from core.logging import ContextLogger

def get_logger_for_module(module_name):
    return ContextLogger.get_logger(f"app.{module_name}")
```

#### Кастомные handlers

**Было:**
```python
handler = logging.StreamHandler()
handler.setFormatter(logging.Formatter('%(asctime)s - %(message)s'))
logger.addHandler(handler)
```

**Стало:**
```python
# ContextLogger поддерживает стандартные handlers
handler = logging.StreamHandler()
handler.setFormatter(logging.Formatter('%(asctime)s - %(message)s'))
logger._stdlib_logger.addHandler(handler)
```

#### Конфигурация через dictConfig

**Было:**
```python
logging.config.dictConfig(LOGGING_CONFIG)
```

**Стало:**
```python
# dictConfig все еще работает для базовой конфигурации
logging.config.dictConfig(LOGGING_CONFIG)

# Но также нужно настроить structlog
from core.logging import configure_structlog
configure_structlog()
```

## Проверка после миграции

1. **Запустите тесты**
   ```bash
   python manage.py test
   ```

2. **Проверьте логи**
   - Убедитесь, что контекст добавляется
   - Проверьте, что уровни логирования работают
   - Убедитесь, что Sentry получает логи

3. **Проверьте производительность**
   - Сравните время выполнения тестов
   - Проверьте использование памяти

## Откат миграции

Если нужно откатиться:

```bash
# Восстановить из резервных копий
find . -name "*.py.bak" -exec sh -c 'mv "$1" "${1%.bak}"' _ {} \;

# Или используйте git
git checkout -- "*.py"
```

## Частые проблемы

### ImportError: cannot import name 'ContextLogger'

**Решение:** Убедитесь, что core.logging добавлен в PYTHONPATH

### Контекст не добавляется в логи

**Решение:** Проверьте, что LoggingContextMiddleware активирован

### Двойное логирование

**Решение:** Удалите дублирующиеся handlers в LOGGING конфигурации
"""
```

### Шаг 4: Создать совместимые алиасы

```python
# core/logging/compat.py
"""
Compatibility layer for smooth migration.
"""

import logging
import warnings
from core.logging import ContextLogger, get_logger as new_get_logger


def getLogger(name: str = None) -> ContextLogger:
    """
    Deprecated compatibility function.
    
    Use ContextLogger.get_logger() instead.
    """
    warnings.warn(
        "getLogger is deprecated, use ContextLogger.get_logger() instead",
        DeprecationWarning,
        stacklevel=2
    )
    return new_get_logger(name)


# Monkey-patch logging.getLogger для обратной совместимости
# (опционально, только для переходного периода)
_original_get_logger = logging.getLogger


def _patched_get_logger(name: str = None) -> logging.Logger:
    """
    Patched getLogger that shows migration warning.
    """
    warnings.warn(
        f"logging.getLogger('{name}') should be migrated to ContextLogger.get_logger('{name}')",
        DeprecationWarning,
        stacklevel=2
    )
    return _original_get_logger(name)


def enable_migration_warnings():
    """Enable warnings for old logging usage."""
    logging.getLogger = _patched_get_logger


def disable_migration_warnings():
    """Disable migration warnings."""
    logging.getLogger = _original_get_logger
```

### Шаг 5: Создать тесты миграции

```python
# tests/core/test_migration.py
import pytest
import tempfile
import shutil
from pathlib import Path
from scripts.migrate_to_context_logger import migrate_file, LoggerMigrationTransformer
import ast


class TestMigration:
    """Test migration script."""
    
    def test_simple_migration(self, tmp_path):
        """Test basic logger migration."""
        # Создаем тестовый файл
        test_file = tmp_path / "test.py"
        test_file.write_text("""
import logging

logger = logging.getLogger(__name__)

def test_function():
    logger.info("Test message")
""")
        
        # Выполняем миграцию
        diff = migrate_file(test_file, dry_run=False)
        
        # Проверяем результат
        content = test_file.read_text()
        assert "from core.logging import ContextLogger" in content
        assert "ContextLogger.get_logger(__name__)" in content
        assert diff is not None
        
    def test_complex_migration(self, tmp_path):
        """Test migration with multiple patterns."""
        test_file = tmp_path / "complex.py"
        test_file.write_text("""
import logging
from logging import getLogger

# Different patterns
logger1 = logging.getLogger(__name__)
logger2 = getLogger('custom.logger')
logger3 = logging.getLogger()

class MyClass:
    def __init__(self):
        self.logger = logging.getLogger(self.__class__.__name__)
""")
        
        # Миграция
        migrate_file(test_file, dry_run=False)
        
        # Проверка
        content = test_file.read_text()
        assert "ContextLogger.get_logger" in content
        assert content.count("ContextLogger.get_logger") >= 3
        
    def test_ast_transformer(self):
        """Test AST transformer directly."""
        code = """
import logging
logger = logging.getLogger(__name__)
"""
        tree = ast.parse(code)
        transformer = LoggerMigrationTransformer()
        new_tree = transformer.visit(tree)
        
        assert transformer.changes_made
        assert 'ContextLogger' in transformer.imports_to_add
        
    def test_dry_run(self, tmp_path):
        """Test dry run mode."""
        test_file = tmp_path / "dry_run.py"
        original_content = """
import logging
logger = logging.getLogger(__name__)
"""
        test_file.write_text(original_content)
        
        # Dry run не должен изменять файл
        diff = migrate_file(test_file, dry_run=True)
        
        assert diff is not None
        assert test_file.read_text() == original_content
        
    def test_backup_creation(self, tmp_path):
        """Test backup file creation."""
        test_file = tmp_path / "backup_test.py"
        test_file.write_text("""
import logging
logger = logging.getLogger(__name__)
""")
        
        # Миграция с backup
        migrate_file(test_file, dry_run=False, backup=True)
        
        # Проверяем backup
        backup_file = tmp_path / "backup_test.py.bak"
        assert backup_file.exists()
        assert "logging.getLogger" in backup_file.read_text()
```

## 🧪 Тестирование миграции

### Контрольный список для тестирования:

1. **Запустить миграцию в dry-run режиме**
   ```bash
   python scripts/migrate_to_context_logger.py --dry-run .
   ```

2. **Проверить diff для критических файлов**
   - Убедиться, что изменения корректны
   - Проверить, что импорты добавлены правильно

3. **Создать резервную копию**
   ```bash
   cp -r . ../project_backup
   ```

4. **Выполнить миграцию с backup**
   ```bash
   python scripts/migrate_to_context_logger.py --backup .
   ```

5. **Запустить валидацию**
   ```bash
   python scripts/validate_migration.py .
   ```

6. **Запустить все тесты**
   ```bash
   python manage.py test
   ```

7. **Проверить логи в development**
   - Убедиться, что контекст добавляется
   - Проверить Sentry события

## ⚠️ Потенциальные проблемы

### Проблема 1: Сложные импорты
**Симптом**: AST не может правильно обработать импорт
**Решение**: Ручная миграция для сложных случаев

### Проблема 2: Динамическое создание логгеров
**Симптом**: getLogger с вычисляемым именем
**Решение**: Обновить код вручную

### Проблема 3: Кастомные logging классы
**Симптом**: Наследники logging.Logger
**Решение**: Адаптировать под ContextLogger

### Проблема 4: Monkey-patching logging
**Симптом**: Код модифицирует logging модуль
**Решение**: Обновить monkey-patch код

## 📊 Критерии успешного завершения
- ✅ Создан скрипт автоматической миграции
- ✅ Поддержка dry-run и backup режимов
- ✅ Создан валидатор для проверки миграции
- ✅ Написано подробное руководство
- ✅ Реализован compatibility layer
- ✅ Все тесты проходят после миграции
- ✅ Логирование работает корректно
- ✅ Контекст добавляется во все логи

## ⏱️ Оценка времени
**3 часа** - создание скриптов и тестирование миграции

## 🔗 Связанные задачи
- **Предыдущая**: 006_logging_middleware_activation.md
- **Следующая**: 008_sentry_sdk_configuration.md
- **Зависит от**: 005_context_logger_enhancement.md

## 📝 Дополнительные заметки
- Миграция должна быть обратимой
- Важно сохранить все существующие настройки логирования
- Рекомендуется выполнять миграцию поэтапно
- После миграции включить deprecation warnings на неделю