# Задача 003: Реализация фильтра чувствительных данных

## 📋 Описание задачи
Реализовать фильтр для автоматического удаления чувствительных данных из логов перед отправкой в Sentry. Фильтр должен обнаруживать и маскировать пароли, API ключи, токены, email адреса и другую конфиденциальную информацию.

## 🎯 Цели
- Создать класс SensitiveDataFilter наследующий logging.Filter
- Реализовать паттерны для обнаружения различных типов чувствительных данных
- Обеспечить рекурсивную очистку сложных структур (dict, list)
- Добавить конфигурируемые списки чувствительных полей
- Минимизировать влияние на производительность

## ✅ Предварительные требования
- Выполнена задача 001 (создана структура папок)
- Понимание работы logging.Filter в Python
- Знание регулярных выражений
- Понимание типов чувствительных данных в проекте

## 🔧 Детальные шаги реализации

### Шаг 1: Обновить imports и создать dataclass для паттернов

```python
# core/logging/filters.py
import re
import logging
from typing import List, Dict, Any, Union, Pattern, Optional, Set, Tuple
from dataclasses import dataclass, field
from functools import lru_cache
import json
```

### Шаг 2: Создать класс SensitivePattern

```python
@dataclass
class SensitivePattern:
    """
    Pattern for detecting sensitive data in logs.
    
    Attributes:
        name: Descriptive name of the pattern
        pattern: Compiled regex pattern
        replacement: Text to replace sensitive data with
        groups: Which regex groups contain sensitive data (default: all)
    """
    name: str
    pattern: Pattern[str]
    replacement: str = "[REDACTED]"
    groups: Optional[List[int]] = None
    
    def replace(self, text: str) -> str:
        """Replace sensitive data in text using this pattern."""
        if self.groups:
            # Заменяем только указанные группы
            def replacer(match):
                result = match.group(0)
                for group_num in self.groups:
                    if group_num <= len(match.groups()):
                        group_start = match.start(group_num)
                        group_end = match.end(group_num)
                        # Вычисляем относительные позиции внутри match
                        rel_start = group_start - match.start(0)
                        rel_end = group_end - match.start(0)
                        result = result[:rel_start] + self.replacement + result[rel_end:]
                return result
            return self.pattern.sub(replacer, text)
        else:
            # Простая замена всего match
            return self.pattern.sub(self.replacement, text)
```

### Шаг 3: Реализовать основной класс SensitiveDataFilter

```python
class SensitiveDataFilter(logging.Filter):
    """
    Filter to remove sensitive data from log records.
    
    This filter:
    - Detects and masks passwords, API keys, tokens, etc.
    - Recursively cleans dict and list structures
    - Handles both message text and extra fields
    - Configurable patterns and field names
    """
    
    # Дефолтные паттерны для обнаружения чувствительных данных
    DEFAULT_PATTERNS = [
        # API ключи и токены
        SensitivePattern(
            name="api_key",
            pattern=re.compile(
                r'\b(api[-_]?key|apikey|api[-_]?token|access[-_]?key)'
                r'[\s"\':=]+([a-zA-Z0-9\-_]{20,})\b',
                re.IGNORECASE
            ),
            replacement="[API_KEY]",
            groups=[2]  # Заменяем только значение, не ключ
        ),
        
        # Пароли
        SensitivePattern(
            name="password",
            pattern=re.compile(
                r'\b(password|passwd|pwd|pass|пароль)'
                r'[\s"\':=]+([^\s"\'<>&,;]+)',
                re.IGNORECASE
            ),
            replacement="[PASSWORD]",
            groups=[2]
        ),
        
        # Bearer токены
        SensitivePattern(
            name="bearer_token",
            pattern=re.compile(
                r'\b(bearer|authorization)[\s:]+([a-zA-Z0-9\-_.]+)\b',
                re.IGNORECASE
            ),
            replacement="[TOKEN]",
            groups=[2]
        ),
        
        # JWT токены
        SensitivePattern(
            name="jwt",
            pattern=re.compile(
                r'\b(eyJ[a-zA-Z0-9\-_]+\.eyJ[a-zA-Z0-9\-_]+\.[a-zA-Z0-9\-_]+)\b'
            ),
            replacement="[JWT_TOKEN]"
        ),
        
        # Секретные ключи
        SensitivePattern(
            name="secret",
            pattern=re.compile(
                r'\b(secret[-_]?key|private[-_]?key|secret)'
                r'[\s"\':=]+([a-zA-Z0-9\-_/+=]{16,})\b',
                re.IGNORECASE
            ),
            replacement="[SECRET]",
            groups=[2]
        ),
        
        # Sentry DSN
        SensitivePattern(
            name="sentry_dsn",
            pattern=re.compile(
                r'https?://[a-f0-9]+@[^/]+\.sentry\.io/\d+',
                re.IGNORECASE
            ),
            replacement="[SENTRY_DSN]"
        ),
        
        # Database URLs
        SensitivePattern(
            name="database_url",
            pattern=re.compile(
                r'(postgres|postgresql|mysql|mongodb|redis)://'
                r'([^:]+):([^@]+)@([^/]+)/([^\s]+)',
                re.IGNORECASE
            ),
            replacement=r'\1://[USER]:[PASSWORD]@\4/\5'
        ),
        
        # Email адреса
        SensitivePattern(
            name="email",
            pattern=re.compile(
                r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'
            ),
            replacement="[EMAIL]"
        ),
        
        # Кредитные карты
        SensitivePattern(
            name="credit_card",
            pattern=re.compile(
                r'\b(?:\d[ -]*?){13,19}\b'
            ),
            replacement="[CREDIT_CARD]"
        ),
        
        # SSH ключи
        SensitivePattern(
            name="ssh_key",
            pattern=re.compile(
                r'-----BEGIN (RSA |DSA |EC |OPENSSH )?PRIVATE KEY-----'
                r'.*?'
                r'-----END (RSA |DSA |EC |OPENSSH )?PRIVATE KEY-----',
                re.DOTALL
            ),
            replacement="[PRIVATE_KEY]"
        ),
        
        # AWS ключи
        SensitivePattern(
            name="aws_key",
            pattern=re.compile(
                r'\b(AKIA[0-9A-Z]{16}|aws_access_key_id|aws_secret_access_key)'
                r'[\s"\':=]+([a-zA-Z0-9/+=]{20,})\b',
                re.IGNORECASE
            ),
            replacement="[AWS_KEY]",
            groups=[2] if "aws_" in "aws_access_key_id" else None
        ),
    ]
    
    # Дефолтные имена полей, которые содержат чувствительные данные
    DEFAULT_SENSITIVE_FIELDS = {
        # Аутентификация
        'password', 'passwd', 'pwd', 'pass', 'secret', 'token',
        'api_key', 'apikey', 'api_token', 'access_token', 'auth_token',
        'refresh_token', 'private_key', 'secret_key', 'session_key',
        
        # HTTP headers
        'authorization', 'x-api-key', 'x-auth-token', 'x-access-token',
        'cookie', 'set-cookie', 'x-csrf-token', 'x-xsrf-token',
        
        # Проект-специфичные
        'BRIGHTDATA_API_TOKEN', 'FIELD_ENCRYPTION_KEY', 'SENTRY_DSN',
        'DATABASE_URL', 'REDIS_URL', 'SECRET_KEY', 'API_HASH', 'API_ID',
        'CELERY_BROKER_URL', 'AWS_SECRET_ACCESS_KEY', 'AWS_ACCESS_KEY_ID',
        
        # Платежные данные
        'credit_card', 'card_number', 'cvv', 'cvc', 'card_code',
        'account_number', 'routing_number', 'iban', 'swift',
        
        # Персональные данные
        'ssn', 'social_security', 'passport', 'driver_license',
        'tax_id', 'national_id',
    }
    
    def __init__(self, 
                 patterns: Optional[List[SensitivePattern]] = None,
                 sensitive_fields: Optional[Set[str]] = None,
                 additional_patterns: Optional[List[SensitivePattern]] = None,
                 additional_fields: Optional[Set[str]] = None,
                 max_value_length: int = 10000,
                 enabled: bool = True):
        """
        Initialize SensitiveDataFilter.
        
        Args:
            patterns: Replace default patterns (if None, use defaults)
            sensitive_fields: Replace default sensitive fields
            additional_patterns: Add to default patterns
            additional_fields: Add to default sensitive fields
            max_value_length: Max length of values to process (performance)
            enabled: Whether filter is enabled
        """
        super().__init__()
        
        # Настройка паттернов
        if patterns is not None:
            self.patterns = patterns
        else:
            self.patterns = self.DEFAULT_PATTERNS.copy()
            
        if additional_patterns:
            self.patterns.extend(additional_patterns)
            
        # Настройка полей
        if sensitive_fields is not None:
            self.sensitive_fields = sensitive_fields
        else:
            self.sensitive_fields = self.DEFAULT_SENSITIVE_FIELDS.copy()
            
        if additional_fields:
            self.sensitive_fields.update(additional_fields)
            
        # Нормализуем имена полей (lowercase)
        self.sensitive_fields = {f.lower() for f in self.sensitive_fields}
        
        self.max_value_length = max_value_length
        self.enabled = enabled
        
        # Кэш для производительности
        self._field_check_cache = {}
    
    def filter(self, record: logging.LogRecord) -> bool:
        """
        Filter sensitive data from log record.
        
        Args:
            record: Log record to filter
            
        Returns:
            True (always pass the record through)
        """
        if not self.enabled:
            return True
            
        try:
            # Очищаем основное сообщение
            if hasattr(record, 'msg'):
                record.msg = self._scrub_value(record.msg)
                
            # Очищаем аргументы
            if hasattr(record, 'args') and record.args:
                record.args = self._scrub_value(record.args)
                
            # Очищаем extra поля
            self._scrub_record_dict(record)
            
        except Exception:
            # Не ломаем логирование из-за ошибок в фильтре
            pass
            
        return True
    
    def _scrub_value(self, value: Any) -> Any:
        """
        Recursively scrub sensitive data from value.
        
        Args:
            value: Value to scrub
            
        Returns:
            Scrubbed value
        """
        if value is None:
            return None
            
        # Строки
        if isinstance(value, str):
            return self._scrub_string(value)
            
        # Словари
        elif isinstance(value, dict):
            return self._scrub_dict(value)
            
        # Списки и кортежи
        elif isinstance(value, (list, tuple)):
            scrubbed = [self._scrub_value(item) for item in value]
            return type(value)(scrubbed)
            
        # Числа и булевы значения - не трогаем
        elif isinstance(value, (int, float, bool)):
            return value
            
        # Объекты с __dict__
        elif hasattr(value, '__dict__'):
            # Пытаемся очистить атрибуты объекта
            for attr_name in dir(value):
                if not attr_name.startswith('_'):
                    try:
                        attr_value = getattr(value, attr_name)
                        if isinstance(attr_value, (str, dict, list)):
                            setattr(value, attr_name, self._scrub_value(attr_value))
                    except (AttributeError, TypeError):
                        pass
            return value
            
        # Все остальное преобразуем в строку и очищаем
        else:
            try:
                str_value = str(value)
                if len(str_value) <= self.max_value_length:
                    return self._scrub_string(str_value)
            except Exception:
                pass
                
        return value
    
    @lru_cache(maxsize=1024)
    def _scrub_string(self, text: str) -> str:
        """
        Scrub sensitive data from string.
        
        Args:
            text: String to scrub
            
        Returns:
            Scrubbed string
        """
        if not text or len(text) > self.max_value_length:
            return text
            
        # Применяем все паттерны
        result = text
        for pattern in self.patterns:
            try:
                result = pattern.replace(result)
            except Exception:
                # Игнорируем ошибки в отдельных паттернах
                continue
                
        return result
    
    def _scrub_dict(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Scrub sensitive data from dictionary.
        
        Args:
            data: Dictionary to scrub
            
        Returns:
            Scrubbed dictionary
        """
        result = {}
        
        for key, value in data.items():
            # Проверяем, является ли ключ чувствительным
            if self._is_sensitive_field(key):
                result[key] = "[REDACTED]"
            else:
                # Рекурсивно очищаем значение
                result[key] = self._scrub_value(value)
                
        return result
    
    def _is_sensitive_field(self, field_name: str) -> bool:
        """
        Check if field name indicates sensitive data.
        
        Args:
            field_name: Field name to check
            
        Returns:
            True if field is sensitive
        """
        if not field_name:
            return False
            
        # Используем кэш для производительности
        cache_key = field_name.lower()
        if cache_key in self._field_check_cache:
            return self._field_check_cache[cache_key]
            
        # Проверяем точное совпадение
        is_sensitive = cache_key in self.sensitive_fields
        
        # Проверяем частичное совпадение
        if not is_sensitive:
            for sensitive_field in self.sensitive_fields:
                if sensitive_field in cache_key or cache_key in sensitive_field:
                    is_sensitive = True
                    break
                    
        # Кэшируем результат
        self._field_check_cache[cache_key] = is_sensitive
        
        return is_sensitive
    
    def _scrub_record_dict(self, record: logging.LogRecord) -> None:
        """
        Scrub sensitive data from record's __dict__.
        
        Args:
            record: Log record to scrub
        """
        # Список стандартных атрибутов LogRecord, которые не трогаем
        standard_attrs = {
            'name', 'msg', 'args', 'created', 'filename', 'funcName',
            'levelname', 'levelno', 'lineno', 'module', 'msecs',
            'pathname', 'process', 'processName', 'relativeCreated',
            'thread', 'threadName', 'exc_info', 'exc_text', 'stack_info',
            'getMessage', 'message'
        }
        
        # Обрабатываем дополнительные атрибуты
        for attr_name in list(record.__dict__.keys()):
            if attr_name not in standard_attrs:
                attr_value = getattr(record, attr_name, None)
                if attr_value is not None:
                    setattr(record, attr_name, self._scrub_value(attr_value))
```

### Шаг 4: Добавить вспомогательные функции

```python
    def add_pattern(self, pattern: SensitivePattern) -> None:
        """Add a new pattern to the filter."""
        self.patterns.append(pattern)
        
    def add_sensitive_field(self, field_name: str) -> None:
        """Add a new sensitive field name."""
        self.sensitive_fields.add(field_name.lower())
        
    def remove_pattern(self, pattern_name: str) -> None:
        """Remove a pattern by name."""
        self.patterns = [p for p in self.patterns if p.name != pattern_name]
        
    def list_patterns(self) -> List[str]:
        """Get list of pattern names."""
        return [p.name for p in self.patterns]
        
    def test_string(self, text: str) -> str:
        """Test the filter on a string (for debugging)."""
        return self._scrub_string(text)


def create_sensitive_data_filter(**kwargs) -> SensitiveDataFilter:
    """
    Factory function to create SensitiveDataFilter.
    
    Common configurations:
    - strict: Maximum security, may over-redact
    - balanced: Good security with less false positives
    - minimal: Only obvious sensitive data
    
    Args:
        **kwargs: Arguments for SensitiveDataFilter
        
    Returns:
        Configured filter instance
    """
    # Предустановленные конфигурации
    presets = {
        'strict': {
            'additional_patterns': [
                SensitivePattern(
                    name="number_sequence",
                    pattern=re.compile(r'\b\d{6,}\b'),
                    replacement="[NUMBER]"
                ),
                SensitivePattern(
                    name="hex_string",
                    pattern=re.compile(r'\b[a-fA-F0-9]{16,}\b'),
                    replacement="[HEX]"
                ),
            ],
            'additional_fields': {
                'id', 'uuid', 'guid', 'key', 'hash', 'signature'
            }
        },
        'balanced': {
            # Используем дефолтные настройки
        },
        'minimal': {
            'patterns': [
                p for p in SensitiveDataFilter.DEFAULT_PATTERNS
                if p.name in ['password', 'api_key', 'credit_card']
            ],
            'sensitive_fields': {
                'password', 'api_key', 'token', 'secret'
            }
        }
    }
    
    # Применяем preset если указан
    preset = kwargs.pop('preset', None)
    if preset and preset in presets:
        preset_config = presets[preset]
        # Merge preset с переданными параметрами
        for key, value in preset_config.items():
            if key not in kwargs:
                kwargs[key] = value
                
    return SensitiveDataFilter(**kwargs)
```

### Шаг 5: Создать специализированные фильтры

```python
class DjangoSensitiveDataFilter(SensitiveDataFilter):
    """
    Sensitive data filter with Django-specific patterns.
    """
    
    def __init__(self, **kwargs):
        # Добавляем Django-специфичные паттерны
        django_patterns = [
            SensitivePattern(
                name="django_secret_key",
                pattern=re.compile(
                    r"SECRET_KEY\s*=\s*['\"]([^'\"]+)['\"]"
                ),
                replacement='SECRET_KEY = "[DJANGO_SECRET]"'
            ),
            SensitivePattern(
                name="csrf_token",
                pattern=re.compile(
                    r'csrfmiddlewaretoken["\']?\s*[:=]\s*["\']?([a-zA-Z0-9]+)'
                ),
                replacement="csrfmiddlewaretoken=[CSRF]"
            ),
            SensitivePattern(
                name="session_id",
                pattern=re.compile(
                    r'sessionid["\']?\s*[:=]\s*["\']?([a-zA-Z0-9]+)'
                ),
                replacement="sessionid=[SESSION]"
            ),
        ]
        
        # Добавляем Django-специфичные поля
        django_fields = {
            'csrfmiddlewaretoken', 'sessionid', 'django_secret',
            '_auth_user_id', '_auth_user_backend', '_auth_user_hash',
        }
        
        # Обновляем kwargs
        additional_patterns = kwargs.get('additional_patterns', [])
        additional_patterns.extend(django_patterns)
        kwargs['additional_patterns'] = additional_patterns
        
        additional_fields = kwargs.get('additional_fields', set())
        additional_fields.update(django_fields)
        kwargs['additional_fields'] = additional_fields
        
        super().__init__(**kwargs)
```

## 🧪 Тестирование реализации

### Создать тестовый файл tests/core/test_sensitive_filter.py:

```python
import pytest
import logging
from core.logging.filters import (
    SensitiveDataFilter, 
    SensitivePattern,
    create_sensitive_data_filter
)

class TestSensitiveDataFilter:
    """Test cases for SensitiveDataFilter."""
    
    def test_basic_password_filtering(self):
        """Test basic password filtering."""
        filter = SensitiveDataFilter()
        
        # Тестовые случаи
        test_cases = [
            ("password=secret123", "password=[PASSWORD]"),
            ("Password: mysecret", "Password: [PASSWORD]"),
            ('{"password": "test123"}', '{"password": "test123"}'),  # В строке
            ("PWD=qwerty", "PWD=[PASSWORD]"),
        ]
        
        for input_text, expected in test_cases:
            result = filter.test_string(input_text)
            assert result == expected, f"Failed for: {input_text}"
    
    def test_api_key_filtering(self):
        """Test API key filtering."""
        filter = SensitiveDataFilter()
        
        test_cases = [
            ("api_key=abcd1234567890abcdef123456", "api_key=[API_KEY]"),
            ("API-KEY: xyz789abc123def456ghi789", "API-KEY: [API_KEY]"),
            ("apikey='super_secret_key_12345'", "apikey='[API_KEY]'"),
        ]
        
        for input_text, expected in test_cases:
            result = filter.test_string(input_text)
            assert result == expected
    
    def test_email_filtering(self):
        """Test email address filtering."""
        filter = SensitiveDataFilter()
        
        test_cases = [
            ("Contact: <EMAIL>", "Contact: [EMAIL]"),
            ("<NAME_EMAIL>", "Email is [EMAIL]"),
        ]
        
        for input_text, expected in test_cases:
            result = filter.test_string(input_text)
            assert result == expected
    
    def test_dict_filtering(self):
        """Test filtering of dictionary values."""
        filter = SensitiveDataFilter()
        
        test_dict = {
            "username": "john_doe",
            "password": "secret123",
            "api_key": "abcdef123456789012345",
            "data": {
                "token": "bearer_token_value",
                "public_info": "This is public"
            }
        }
        
        result = filter._scrub_dict(test_dict)
        
        assert result["username"] == "john_doe"
        assert result["password"] == "[REDACTED]"
        assert result["api_key"] == "[REDACTED]"
        assert result["data"]["token"] == "[REDACTED]"
        assert result["data"]["public_info"] == "This is public"
    
    def test_log_record_filtering(self):
        """Test filtering of actual log records."""
        filter = SensitiveDataFilter()
        
        # Создаем тестовую запись
        record = logging.LogRecord(
            name="test",
            level=logging.INFO,
            pathname="test.py",
            lineno=10,
            msg="User login with password=secret123",
            args=(),
            exc_info=None
        )
        
        # Добавляем extra поля
        record.user_password = "another_secret"
        record.request_data = {
            "username": "test",
            "api_token": "token123456"
        }
        
        # Применяем фильтр
        filter.filter(record)
        
        # Проверяем результаты
        assert "secret123" not in record.msg
        assert record.user_password == "[REDACTED]"
        assert record.request_data["api_token"] == "[REDACTED]"
        assert record.request_data["username"] == "test"
    
    def test_performance(self):
        """Test filter performance with large data."""
        import time
        
        filter = SensitiveDataFilter()
        
        # Большой текст с чувствительными данными
        large_text = " ".join([
            f"password=secret{i} " for i in range(1000)
        ])
        
        start_time = time.time()
        result = filter.test_string(large_text)
        elapsed = time.time() - start_time
        
        # Должно быть быстро (< 100ms для 1000 паролей)
        assert elapsed < 0.1
        assert "secret" not in result
    
    def test_custom_patterns(self):
        """Test adding custom patterns."""
        filter = SensitiveDataFilter()
        
        # Добавляем кастомный паттерн
        custom_pattern = SensitivePattern(
            name="employee_id",
            pattern=re.compile(r'EMP\d{6}'),
            replacement="[EMPLOYEE_ID]"
        )
        filter.add_pattern(custom_pattern)
        
        result = filter.test_string("Employee: EMP123456")
        assert result == "Employee: [EMPLOYEE_ID]"
    
    def test_preset_configurations(self):
        """Test preset filter configurations."""
        # Strict режим
        strict_filter = create_sensitive_data_filter(preset='strict')
        result = strict_filter.test_string("Code: 123456789")
        assert "123456789" not in result  # Числа тоже скрыты
        
        # Minimal режим
        minimal_filter = create_sensitive_data_filter(preset='minimal')
        result = minimal_filter.test_string("Code: 123456789")
        assert "123456789" in result  # Числа не скрыты
```

## ⚠️ Потенциальные проблемы

### Проблема 1: Ложные срабатывания
**Симптом**: Обычный текст заменяется на [REDACTED]
**Решение**: Улучшить регулярные выражения, использовать границы слов

### Проблема 2: Производительность на больших объемах
**Симптом**: Замедление логирования
**Решение**: Использовать кэширование, ограничить max_value_length

### Проблема 3: Пропуск чувствительных данных
**Симптом**: Пароли или ключи попадают в логи
**Решение**: Регулярно обновлять паттерны, мониторить логи

### Проблема 4: Рекурсия при обработке сложных объектов
**Симптом**: RecursionError
**Решение**: Добавить защиту от циклических ссылок

## 📊 Критерии успешного завершения
- ✅ Реализован класс SensitiveDataFilter
- ✅ Созданы паттерны для всех типов чувствительных данных
- ✅ Работает рекурсивная очистка dict и list
- ✅ Добавлена поддержка кастомных паттернов
- ✅ Реализованы preset конфигурации
- ✅ Создан DjangoSensitiveDataFilter
- ✅ Написаны comprehensive тесты
- ✅ Производительность < 100ms на 1000 элементов

## ⏱️ Оценка времени
**2 часа** - полная реализация с тестами

## 🔗 Связанные задачи
- **Предыдущая**: 002_sentry_handler_implementation.md
- **Следующая**: 004_rate_limit_filter.md
- **Используется в**: 009_logging_configuration.md

## 📝 Дополнительные заметки
- Фильтр должен быть "fail-safe" - лучше пропустить, чем сломать логирование
- Регулярно обновлять паттерны при появлении новых типов данных
- Рассмотреть интеграцию с системой аудита для отслеживания попыток логирования чувствительных данных
- В production использовать preset='strict' для максимальной защиты