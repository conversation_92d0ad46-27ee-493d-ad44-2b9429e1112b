# Задача 005: Улучшение ContextLogger со structlog

## 📋 Описание задачи
Улучшить существующий ContextLogger в проекте SocialManager, интегрировав его со structlog для структурированного логирования. Добавить автоматическое обогащение контекстом, поддержку bind/unbind операций и интеграцию с Sentry.

## 🎯 Цели
- Интегрировать structlog с существующим ContextLogger
- Реализовать контекстные переменные для хранения метаданных
- Добавить процессоры для обогащения логов
- Обеспечить совместимость с существующим кодом
- Создать удобный API для работы с контекстом

## ✅ Предварительные требования
- Выполнена задача 001 (создана структура папок)
- Установлен пакет structlog
- Существующий файл core/logging/logger.py
- Понимание работы contextvars в Python

## 🔧 Детальные шаги реализации

### Шаг 1: Проверить существующий ContextLogger

```bash
# Проверяем существующую реализацию
cat core/logging/logger.py
```

### Шаг 2: Обновить или создать logger.py

```python
# core/logging/logger.py
import logging
import contextvars
from typing import Dict, Any, Optional, Union, List, Callable
import structlog
from functools import wraps
import json
import sys
from datetime import datetime

# Контекстная переменная для хранения данных логирования
_context_var: contextvars.ContextVar[Dict[str, Any]] = contextvars.ContextVar(
    'logging_context',
    default={}
)

# Глобальный процессор для добавления контекста
def add_context_processor(logger, method_name, event_dict):
    """Add context data to all log messages."""
    context = get_context()
    if context:
        # Добавляем контекст, но не перезаписываем существующие ключи
        for key, value in context.items():
            if key not in event_dict:
                event_dict[key] = value
    return event_dict


# Процессор для добавления метаданных
def add_metadata_processor(logger, method_name, event_dict):
    """Add standard metadata to logs."""
    # Добавляем метаданные о вызове
    if 'timestamp' not in event_dict:
        event_dict['timestamp'] = datetime.utcnow().isoformat()
    
    # Добавляем информацию о методе логирования
    event_dict['log_level'] = method_name
    
    return event_dict


# Процессор для фильтрации None значений
def filter_none_processor(logger, method_name, event_dict):
    """Remove None values from event dict."""
    return {k: v for k, v in event_dict.items() if v is not None}


class ContextLogger:
    """
    Enhanced logger with automatic context injection using structlog.
    
    This logger:
    - Automatically adds context from contextvars
    - Supports structured logging with structlog
    - Provides bind/unbind for temporary context
    - Integrates seamlessly with Django/Celery
    - Maintains backward compatibility
    """
    
    # Глобальная конфигурация процессоров
    DEFAULT_PROCESSORS = [
        structlog.stdlib.filter_by_level,
        structlog.stdlib.add_logger_name,
        structlog.stdlib.add_log_level,
        structlog.stdlib.PositionalArgumentsFormatter(),
        structlog.processors.TimeStamper(fmt="iso"),
        structlog.processors.StackInfoRenderer(),
        structlog.processors.format_exc_info,
        add_context_processor,
        add_metadata_processor,
        filter_none_processor,
        structlog.processors.UnicodeDecoder(),
    ]
    
    # Кэш для логгеров
    _loggers_cache: Dict[str, 'ContextLogger'] = {}
    
    def __init__(self, name: str, processors: Optional[List[Callable]] = None):
        """
        Initialize ContextLogger.
        
        Args:
            name: Logger name (usually __name__)
            processors: Custom processors list (if None, use defaults)
        """
        self.name = name
        self._stdlib_logger = logging.getLogger(name)
        
        # Используем кастомные процессоры или дефолтные
        if processors is None:
            processors = self.DEFAULT_PROCESSORS.copy()
        
        # Создаем structlog wrapper
        self._logger = structlog.wrap_logger(
            self._stdlib_logger,
            processors=processors,
            context_class=dict,
            logger_factory=structlog.stdlib.LoggerFactory(),
            cache_logger_on_first_use=True,
        )
        
        # Для backward compatibility
        self.logger = self._stdlib_logger
    
    @classmethod
    def get_logger(cls, name: str) -> 'ContextLogger':
        """
        Factory method to get or create a logger.
        
        Args:
            name: Logger name
            
        Returns:
            ContextLogger instance
        """
        if name not in cls._loggers_cache:
            cls._loggers_cache[name] = cls(name)
        return cls._loggers_cache[name]
    
    def bind(self, **kwargs) -> 'BoundContextLogger':
        """
        Create a new logger with additional context.
        
        Args:
            **kwargs: Context to bind
            
        Returns:
            BoundContextLogger with additional context
            
        Example:
            logger = ContextLogger(__name__)
            request_logger = logger.bind(request_id="123", user_id=456)
            request_logger.info("Processing request")
        """
        bound_logger = self._logger.bind(**kwargs)
        return BoundContextLogger(self.name, bound_logger, self)
    
    def unbind(self, *keys) -> 'BoundContextLogger':
        """
        Create a new logger with keys removed from context.
        
        Args:
            *keys: Keys to remove
            
        Returns:
            BoundContextLogger with keys removed
        """
        bound_logger = self._logger.unbind(*keys)
        return BoundContextLogger(self.name, bound_logger, self)
    
    def new(self, **kwargs) -> 'BoundContextLogger':
        """
        Create a new logger with only the specified context.
        
        Args:
            **kwargs: New context (replaces all existing)
            
        Returns:
            BoundContextLogger with new context
        """
        bound_logger = self._logger.new(**kwargs)
        return BoundContextLogger(self.name, bound_logger, self)
    
    # Logging methods
    def debug(self, msg: str, *args, **kwargs):
        """Log debug message with context."""
        self._log('debug', msg, *args, **kwargs)
    
    def info(self, msg: str, *args, **kwargs):
        """Log info message with context."""
        self._log('info', msg, *args, **kwargs)
    
    def warning(self, msg: str, *args, **kwargs):
        """Log warning message with context."""
        self._log('warning', msg, *args, **kwargs)
    
    def error(self, msg: str, *args, **kwargs):
        """Log error message with context."""
        self._log('error', msg, *args, **kwargs)
    
    def critical(self, msg: str, *args, **kwargs):
        """Log critical message with context."""
        self._log('critical', msg, *args, **kwargs)
    
    def exception(self, msg: str, *args, **kwargs):
        """Log exception with traceback."""
        kwargs['exc_info'] = kwargs.get('exc_info', True)
        self._log('error', msg, *args, **kwargs)
    
    def log(self, level: Union[int, str], msg: str, *args, **kwargs):
        """Log message at specified level."""
        if isinstance(level, int):
            level = logging.getLevelName(level).lower()
        self._log(level, msg, *args, **kwargs)
    
    def _log(self, level: str, msg: str, *args, **kwargs):
        """Internal logging method."""
        # Извлекаем стандартные параметры
        exc_info = kwargs.pop('exc_info', None)
        stack_info = kwargs.pop('stack_info', False)
        extra = kwargs.pop('extra', {})
        
        # Объединяем extra с kwargs
        log_kwargs = {**extra, **kwargs}
        
        # Добавляем стандартные параметры обратно если нужно
        if exc_info:
            log_kwargs['exc_info'] = exc_info
        if stack_info:
            log_kwargs['stack_info'] = stack_info
        
        # Используем structlog
        getattr(self._logger, level)(msg, *args, **log_kwargs)
    
    # Дополнительные методы для удобства
    def add_context(self, **kwargs):
        """
        Add context that will be included in all subsequent logs.
        
        This modifies the global context for the current async context.
        
        Args:
            **kwargs: Context to add
            
        Example:
            logger.add_context(request_id="123")
            logger.info("Processing")  # Will include request_id
        """
        current = get_context()
        updated = {**current, **kwargs}
        set_context(**updated)
    
    def remove_context(self, *keys):
        """
        Remove keys from the global context.
        
        Args:
            *keys: Keys to remove
        """
        current = get_context()
        for key in keys:
            current.pop(key, None)
        set_context(**current)
    
    @property
    def level(self) -> int:
        """Get current logging level."""
        return self._stdlib_logger.level
    
    @level.setter
    def level(self, value: Union[int, str]):
        """Set logging level."""
        if isinstance(value, str):
            value = getattr(logging, value.upper())
        self._stdlib_logger.setLevel(value)


class BoundContextLogger:
    """
    Logger bound with specific context.
    
    Created by ContextLogger.bind() method.
    """
    
    def __init__(self, name: str, bound_logger, parent: ContextLogger):
        self.name = name
        self._logger = bound_logger
        self._parent = parent
    
    def bind(self, **kwargs) -> 'BoundContextLogger':
        """Add more context to bound logger."""
        bound_logger = self._logger.bind(**kwargs)
        return BoundContextLogger(self.name, bound_logger, self._parent)
    
    def unbind(self, *keys) -> 'BoundContextLogger':
        """Remove context from bound logger."""
        bound_logger = self._logger.unbind(*keys)
        return BoundContextLogger(self.name, bound_logger, self._parent)
    
    # Proxy all logging methods
    def debug(self, msg: str, *args, **kwargs):
        self._logger.debug(msg, *args, **kwargs)
    
    def info(self, msg: str, *args, **kwargs):
        self._logger.info(msg, *args, **kwargs)
    
    def warning(self, msg: str, *args, **kwargs):
        self._logger.warning(msg, *args, **kwargs)
    
    def error(self, msg: str, *args, **kwargs):
        self._logger.error(msg, *args, **kwargs)
    
    def critical(self, msg: str, *args, **kwargs):
        self._logger.critical(msg, *args, **kwargs)
    
    def exception(self, msg: str, *args, **kwargs):
        kwargs['exc_info'] = kwargs.get('exc_info', True)
        self._logger.error(msg, *args, **kwargs)


# Глобальные функции для работы с контекстом
def get_context() -> Dict[str, Any]:
    """
    Get current logging context.
    
    Returns:
        Dictionary with current context
    """
    return _context_var.get().copy()


def set_context(**kwargs):
    """
    Set logging context for current async context.
    
    Args:
        **kwargs: Context to set (replaces existing)
        
    Example:
        set_context(request_id="123", user_id=456)
    """
    _context_var.set(kwargs)


def add_context(**kwargs):
    """
    Add to existing logging context.
    
    Args:
        **kwargs: Context to add (merges with existing)
        
    Example:
        add_context(request_id="123")
        add_context(user_id=456)  # Both will be in context
    """
    current = get_context()
    updated = {**current, **kwargs}
    _context_var.set(updated)


def clear_context():
    """Clear all logging context."""
    _context_var.set({})


def update_context(**kwargs):
    """Alias for add_context for backward compatibility."""
    add_context(**kwargs)


# Контекстный менеджер для временного контекста
class context_logging:
    """
    Context manager for temporary logging context.
    
    Example:
        with context_logging(operation="user_import", user_id=123):
            logger.info("Starting import")  # Will have operation and user_id
    """
    
    def __init__(self, **kwargs):
        self.context = kwargs
        self.token = None
        self.previous_context = None
    
    def __enter__(self):
        self.previous_context = get_context()
        updated = {**self.previous_context, **self.context}
        self.token = _context_var.set(updated)
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        if self.token:
            _context_var.reset(self.token)


# Декоратор для добавления контекста к функциям
def with_logging_context(**context):
    """
    Decorator to add logging context to a function.
    
    Args:
        **context: Static context to add
        
    Example:
        @with_logging_context(operation="data_processing")
        def process_data(data):
            logger.info("Processing")  # Will have operation in context
    """
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            # Можем добавить динамический контекст из аргументов
            dynamic_context = {}
            
            # Пример: если первый аргумент - request, добавляем request_id
            if args and hasattr(args[0], 'META'):
                request = args[0]
                if hasattr(request, 'request_id'):
                    dynamic_context['request_id'] = request.request_id
            
            # Объединяем статический и динамический контекст
            full_context = {**context, **dynamic_context}
            
            with context_logging(**full_context):
                return func(*args, **kwargs)
        
        return wrapper
    return decorator


# Настройка structlog при импорте модуля
def configure_structlog(
    processors: Optional[List[Callable]] = None,
    logger_factory: Optional[Callable] = None,
    cache_logger_on_first_use: bool = True
):
    """
    Configure structlog globally.
    
    Args:
        processors: List of processors
        logger_factory: Logger factory
        cache_logger_on_first_use: Whether to cache loggers
    """
    if processors is None:
        processors = ContextLogger.DEFAULT_PROCESSORS
    
    structlog.configure(
        processors=processors + [
            structlog.stdlib.ProcessorFormatter.wrap_for_formatter,
        ],
        context_class=dict,
        logger_factory=logger_factory or structlog.stdlib.LoggerFactory(),
        cache_logger_on_first_use=cache_logger_on_first_use,
    )


# Автоматическая конфигурация при импорте
configure_structlog()


# Для обратной совместимости
def get_logger(name: str) -> ContextLogger:
    """
    Get a ContextLogger instance.
    
    Args:
        name: Logger name
        
    Returns:
        ContextLogger instance
    """
    return ContextLogger.get_logger(name)


# Экспорт для использования
__all__ = [
    'ContextLogger',
    'BoundContextLogger',
    'get_context',
    'set_context',
    'add_context',
    'clear_context',
    'update_context',
    'context_logging',
    'with_logging_context',
    'configure_structlog',
    'get_logger',
]
```

### Шаг 3: Создать процессоры для интеграции с Django/Celery

```python
# core/logging/processors.py
"""
Additional processors for structlog integration.
"""

import structlog
from typing import Dict, Any, Optional
from django.http import HttpRequest
from django.contrib.auth.models import AnonymousUser


def django_request_processor(logger, method_name, event_dict):
    """
    Add Django request information to logs.
    
    Extracts information from Django request if available.
    """
    # Пытаемся получить request из контекста
    context = event_dict.get('context', {})
    request = context.get('request') or event_dict.get('request')
    
    if request and isinstance(request, HttpRequest):
        event_dict['django_request'] = {
            'method': request.method,
            'path': request.path,
            'user_id': request.user.id if hasattr(request.user, 'id') and not isinstance(request.user, AnonymousUser) else None,
            'session_key': request.session.session_key if hasattr(request, 'session') else None,
            'remote_addr': request.META.get('REMOTE_ADDR'),
            'user_agent': request.META.get('HTTP_USER_AGENT'),
        }
        
        # Удаляем request объект чтобы не сериализовать его целиком
        event_dict.pop('request', None)
        if 'context' in event_dict:
            event_dict['context'].pop('request', None)
    
    return event_dict


def celery_task_processor(logger, method_name, event_dict):
    """
    Add Celery task information to logs.
    
    Extracts information from Celery task context if available.
    """
    # Проверяем, выполняется ли в контексте Celery задачи
    try:
        from celery import current_task
        
        if current_task:
            event_dict['celery_task'] = {
                'id': current_task.request.id,
                'name': current_task.name,
                'args': str(current_task.request.args)[:100],  # Ограничиваем длину
                'kwargs': str(current_task.request.kwargs)[:100],
                'retries': current_task.request.retries,
                'eta': current_task.request.eta,
            }
    except ImportError:
        pass
    except Exception:
        pass
    
    return event_dict


def exception_info_processor(logger, method_name, event_dict):
    """
    Enhanced exception processor that extracts more details.
    """
    exc_info = event_dict.get('exc_info')
    
    if exc_info and exc_info is not True:
        import traceback
        
        exc_type, exc_value, exc_tb = exc_info
        
        event_dict['exception'] = {
            'type': exc_type.__name__ if exc_type else None,
            'value': str(exc_value),
            'traceback': traceback.format_tb(exc_tb),
            'locals': {}  # Можно добавить локальные переменные для отладки
        }
        
        # Извлекаем локальные переменные из последнего фрейма
        if exc_tb:
            tb_frame = exc_tb.tb_frame
            while tb_frame.f_back:
                tb_frame = tb_frame.f_back
            
            # Безопасно извлекаем некоторые переменные
            safe_locals = {}
            for key, value in tb_frame.f_locals.items():
                if key.startswith('_'):
                    continue
                try:
                    # Пробуем сериализовать в JSON
                    import json
                    json.dumps(value)
                    safe_locals[key] = value
                except (TypeError, ValueError):
                    safe_locals[key] = str(type(value))
            
            event_dict['exception']['locals'] = safe_locals
    
    return event_dict


def performance_processor(logger, method_name, event_dict):
    """
    Add performance metrics to logs.
    """
    import time
    import psutil
    import os
    
    # Добавляем метрики производительности
    event_dict['performance'] = {
        'timestamp': time.time(),
        'process_id': os.getpid(),
        'thread_id': threading.get_ident() if 'threading' in globals() else None,
        'cpu_percent': psutil.Process().cpu_percent(),
        'memory_mb': psutil.Process().memory_info().rss / 1024 / 1024,
    }
    
    return event_dict


# Список всех кастомных процессоров
CUSTOM_PROCESSORS = [
    django_request_processor,
    celery_task_processor,
    exception_info_processor,
    # performance_processor,  # Опционально, может быть тяжелым
]
```

### Шаг 4: Создать утилиты для тестирования

```python
# core/logging/testing.py (дополнение к существующему)
import structlog
from structlog.testing import LogCapture
from contextlib import contextmanager
from typing import List, Dict, Any


class StructuredLogCapture(LogCapture):
    """Enhanced log capture for testing with structlog."""
    
    def assert_logged(self, 
                     level: str,
                     message: str = None,
                     **kwargs):
        """
        Assert that a specific log was captured.
        
        Args:
            level: Log level
            message: Message to find (partial match)
            **kwargs: Additional fields to match
        """
        for entry in self.entries:
            if entry['log_level'] != level:
                continue
                
            if message and message not in entry.get('event', ''):
                continue
                
            # Проверяем дополнительные поля
            matches = True
            for key, value in kwargs.items():
                if key not in entry or entry[key] != value:
                    matches = False
                    break
                    
            if matches:
                return True
                
        raise AssertionError(
            f"No log found with level={level}, message='{message}', "
            f"fields={kwargs}. Captured: {self.entries}"
        )
    
    def get_logs(self, level: str = None) -> List[Dict[str, Any]]:
        """Get all logs, optionally filtered by level."""
        if level:
            return [e for e in self.entries if e.get('log_level') == level]
        return self.entries


@contextmanager
def capture_logs() -> StructuredLogCapture:
    """
    Context manager for capturing structured logs in tests.
    
    Example:
        with capture_logs() as logs:
            logger.info("Test", user_id=123)
            
        logs.assert_logged('info', 'Test', user_id=123)
    """
    cap = StructuredLogCapture()
    
    # Временно заменяем процессоры
    old_processors = structlog.get_config()['processors']
    structlog.configure(
        processors=[cap],
        context_class=dict,
        logger_factory=structlog.PrintLoggerFactory(),
    )
    
    try:
        yield cap
    finally:
        # Восстанавливаем процессоры
        structlog.configure(
            processors=old_processors,
            context_class=dict,
            logger_factory=structlog.stdlib.LoggerFactory(),
        )
```

### Шаг 5: Создать примеры использования

```python
# examples/logging_usage.py
"""
Examples of using the enhanced ContextLogger.
"""

from core.logging import (
    ContextLogger, 
    get_logger,
    set_context, 
    add_context,
    context_logging,
    with_logging_context
)

# Простое использование
logger = ContextLogger(__name__)
logger.info("Application started")

# С контекстом
set_context(request_id="req-123", user_id=42)
logger.info("Processing request")  # Автоматически включает request_id и user_id

# Временный контекст
with context_logging(operation="data_import", source="csv"):
    logger.info("Starting import")
    # ... операция импорта
    logger.info("Import completed", records_count=1000)

# Bound logger для компонента
db_logger = logger.bind(component="database", db_name="postgres")
db_logger.info("Connecting to database")
db_logger.error("Connection failed", error="timeout")

# Декоратор для функций
@with_logging_context(operation="user_registration")
def register_user(email: str):
    logger.info("Registering user", email=email)
    # ... логика регистрации
    return user_id

# В Django views
def my_view(request):
    # Добавляем request context
    logger = get_logger(__name__)
    logger.add_context(
        request_id=getattr(request, 'request_id', None),
        user_id=request.user.id if request.user.is_authenticated else None,
        path=request.path
    )
    
    logger.info("Processing view")
    # ... обработка
    
# В Celery tasks
from celery import Task

class LoggedTask(Task):
    def __call__(self, *args, **kwargs):
        logger = get_logger(self.name)
        logger.add_context(
            task_id=self.request.id,
            task_name=self.name
        )
        
        logger.info("Task started")
        try:
            result = self.run(*args, **kwargs)
            logger.info("Task completed successfully")
            return result
        except Exception as e:
            logger.exception("Task failed")
            raise
```

## 🧪 Тестирование реализации

### Создать тесты в tests/core/test_context_logger.py:

```python
import pytest
from core.logging import (
    ContextLogger,
    get_context,
    set_context,
    clear_context,
    context_logging
)
from core.logging.testing import capture_logs


class TestContextLogger:
    """Test cases for enhanced ContextLogger."""
    
    def setup_method(self):
        """Clear context before each test."""
        clear_context()
    
    def test_basic_logging(self):
        """Test basic logging functionality."""
        logger = ContextLogger("test")
        
        with capture_logs() as logs:
            logger.info("Test message")
            
        logs.assert_logged('info', 'Test message')
    
    def test_context_injection(self):
        """Test automatic context injection."""
        logger = ContextLogger("test")
        
        set_context(request_id="123", user_id=456)
        
        with capture_logs() as logs:
            logger.info("With context")
            
        logs.assert_logged('info', 'With context', 
                          request_id="123", 
                          user_id=456)
    
    def test_bound_logger(self):
        """Test bound logger functionality."""
        logger = ContextLogger("test")
        bound = logger.bind(component="auth", version="1.0")
        
        with capture_logs() as logs:
            bound.info("Bound message")
            
        logs.assert_logged('info', 'Bound message',
                          component="auth",
                          version="1.0")
    
    def test_context_manager(self):
        """Test context logging manager."""
        logger = ContextLogger("test")
        
        with capture_logs() as logs:
            logger.info("Before context")
            
            with context_logging(operation="test_op"):
                logger.info("Inside context")
            
            logger.info("After context")
        
        entries = logs.get_logs('info')
        assert 'operation' not in entries[0]  # Before
        assert entries[1]['operation'] == 'test_op'  # Inside
        assert 'operation' not in entries[2]  # After
    
    def test_exception_logging(self):
        """Test exception logging with traceback."""
        logger = ContextLogger("test")
        
        with capture_logs() as logs:
            try:
                1 / 0
            except ZeroDivisionError:
                logger.exception("Math error")
        
        entries = logs.get_logs('error')
        assert len(entries) == 1
        assert 'Math error' in entries[0]['event']
        assert 'exc_info' in entries[0]
```

## ⚠️ Потенциальные проблемы

### Проблема 1: Конфликт с существующим ContextLogger
**Симптом**: ImportError или дублирование функциональности
**Решение**: Проверить существующую реализацию и адаптировать

### Проблема 2: Производительность structlog
**Симптом**: Замедление логирования
**Решение**: Оптимизировать процессоры, использовать кэширование

### Проблема 3: Несовместимость с Django logging
**Симптом**: Логи не появляются в нужных handlers
**Решение**: Правильно настроить logger factory

### Проблема 4: Утечка контекста между запросами
**Симптом**: Контекст одного запроса появляется в другом
**Решение**: Использовать contextvars правильно

## 📊 Критерии успешного завершения
- ✅ Интегрирован structlog с ContextLogger
- ✅ Работает хранение контекста через contextvars
- ✅ Реализованы bind/unbind операции
- ✅ Добавлены процессоры для Django/Celery
- ✅ Создан контекстный менеджер и декоратор
- ✅ Написаны тесты
- ✅ Сохранена обратная совместимость
- ✅ Добавлены примеры использования

## ⏱️ Оценка времени
**2 часа** - полная реализация с тестами

## 🔗 Связанные задачи
- **Предыдущая**: 004_rate_limit_filter.md
- **Следующая**: 006_logging_middleware_activation.md
- **Используется в**: Всех последующих задачах

## 📝 Дополнительные заметки
- ContextLogger - центральный компонент системы логирования
- Важно сохранить совместимость с существующим кодом
- Рассмотреть добавление метрик производительности
- Контекст должен быть изолирован между запросами/задачами