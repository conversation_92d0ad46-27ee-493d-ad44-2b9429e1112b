# Задача 002: Реализация SentryHandler

## 📋 Описание задачи
Реализовать кастомный logging handler для отправки логов в Sentry с автоматическим добавлением контекста из ContextLogger. Handler должен обогащать события дополнительными данными о запросе, пользователе и задаче.

## 🎯 Цели
- Реализовать класс SentryHandler наследующий logging.Handler
- Интегрировать с ContextLogger для получения контекста
- Добавить маппинг уровней логирования Python на Sentry
- Обеспечить корректную отправку исключений и breadcrumbs
- Добавить теги и контекст для улучшенной фильтрации в Sentry UI

## ✅ Предварительные требования
- Выполнена задача 001 (создана структура папок)
- Установлен пакет sentry-sdk
- Понимание работы logging.Handler в Python
- Базовое знание Sentry SDK API

## 🔧 Детальные шаги реализации

### Шаг 1: Обновить imports в handlers.py
```python
# core/logging/handlers.py
import logging
import sentry_sdk
from typing import Optional, Dict, Any, Union
from datetime import datetime

# Импортируем функцию получения контекста
# Она будет создана в задаче 005, пока используем заглушку
try:
    from core.logging.logger import get_context
except ImportError:
    # Временная заглушка до реализации ContextLogger
    def get_context() -> Dict[str, Any]:
        return {}
```

### Шаг 2: Реализовать класс SentryHandler

```python
class SentryHandler(logging.Handler):
    """
    Custom logging handler that sends logs to Sentry with enriched context.
    
    This handler:
    - Adds request context (request_id, user_id, etc.)
    - Maps Python logging levels to Sentry levels
    - Handles exceptions with full traceback
    - Adds custom tags for better filtering
    - Supports breadcrumbs for context
    """
    
    # Маппинг уровней логирования
    LEVEL_MAPPING = {
        logging.DEBUG: "debug",
        logging.INFO: "info",
        logging.WARNING: "warning",
        logging.ERROR: "error",
        logging.CRITICAL: "fatal",
    }
    
    def __init__(self, level: int = logging.ERROR, 
                 capture_message_level: int = logging.ERROR,
                 add_breadcrumbs: bool = True):
        """
        Initialize SentryHandler.
        
        Args:
            level: Minimum level for handling records
            capture_message_level: Minimum level for sending as Sentry events
            add_breadcrumbs: Whether to add lower level logs as breadcrumbs
        """
        super().__init__(level)
        self.capture_message_level = capture_message_level
        self.add_breadcrumbs = add_breadcrumbs
        
    def emit(self, record: logging.LogRecord) -> None:
        """
        Send log record to Sentry with context enrichment.
        
        Args:
            record: The log record to process
        """
        try:
            # Проверяем, инициализирован ли Sentry SDK
            if not sentry_sdk.Hub.current.client:
                return
                
            with sentry_sdk.push_scope() as scope:
                # Добавляем контекст из ContextLogger
                self._add_context_to_scope(scope, record)
                
                # Добавляем теги
                self._add_tags_to_scope(scope, record)
                
                # Добавляем extra данные
                self._add_extra_to_scope(scope, record)
                
                # Обрабатываем запись
                if record.exc_info and record.exc_info[0] is not None:
                    # Если есть исключение, отправляем его
                    self._capture_exception(record)
                elif record.levelno >= self.capture_message_level:
                    # Отправляем как сообщение
                    self._capture_message(record)
                elif self.add_breadcrumbs and record.levelno >= logging.INFO:
                    # Добавляем как breadcrumb
                    self._add_breadcrumb(record)
                    
        except Exception:
            # Не позволяем ошибкам в handler'е ломать приложение
            self.handleError(record)
    
    def _add_context_to_scope(self, scope: sentry_sdk.Scope, 
                             record: logging.LogRecord) -> None:
        """Add ContextLogger context to Sentry scope."""
        context = get_context()
        
        if context:
            # Основной контекст запроса
            request_context = {
                "request_id": context.get("request_id"),
                "correlation_id": context.get("correlation_id"),
                "session_id": context.get("session_id"),
            }
            # Убираем None значения
            request_context = {k: v for k, v in request_context.items() if v is not None}
            if request_context:
                scope.set_context("request", request_context)
            
            # Контекст пользователя
            user_id = context.get("user_id")
            if user_id:
                scope.set_user({
                    "id": str(user_id),
                    "username": context.get("username"),
                    "email": context.get("user_email"),
                })
            
            # Контекст задачи (для Celery)
            task_context = {
                "task_id": context.get("task_id"),
                "task_name": context.get("task_name"),
                "task_args": context.get("task_args"),
                "task_kwargs": context.get("task_kwargs"),
            }
            task_context = {k: v for k, v in task_context.items() if v is not None}
            if task_context:
                scope.set_context("task", task_context)
            
            # Дополнительный контекст
            app_context = {
                "environment": context.get("environment"),
                "service": context.get("service"),
                "version": context.get("version"),
            }
            app_context = {k: v for k, v in app_context.items() if v is not None}
            if app_context:
                scope.set_context("app", app_context)
    
    def _add_tags_to_scope(self, scope: sentry_sdk.Scope, 
                          record: logging.LogRecord) -> None:
        """Add tags for filtering in Sentry UI."""
        # Базовые теги
        scope.set_tag("logger", record.name)
        scope.set_tag("level", record.levelname)
        
        # Теги из контекста
        context = get_context()
        if context:
            # Важные теги для фильтрации
            if "environment" in context:
                scope.set_tag("environment", context["environment"])
            if "service" in context:
                scope.set_tag("service", context["service"]) 
            if "user_id" in context:
                scope.set_tag("has_user", "true")
            if "task_name" in context:
                scope.set_tag("task_name", context["task_name"])
                
        # Теги из record атрибутов
        if hasattr(record, "tags") and isinstance(record.tags, dict):
            for key, value in record.tags.items():
                scope.set_tag(key, str(value))
    
    def _add_extra_to_scope(self, scope: sentry_sdk.Scope, 
                           record: logging.LogRecord) -> None:
        """Add extra data to scope."""
        # Данные из record
        extra_data = {
            "logger": record.name,
            "module": record.module,
            "function": record.funcName,
            "line_number": record.lineno,
            "thread": record.thread,
            "thread_name": record.threadName,
            "process": record.process,
        }
        
        # Дополнительные атрибуты из record
        for key, value in record.__dict__.items():
            if key not in [
                'name', 'msg', 'args', 'created', 'filename', 'funcName',
                'levelname', 'levelno', 'lineno', 'module', 'msecs',
                'pathname', 'process', 'processName', 'relativeCreated',
                'thread', 'threadName', 'exc_info', 'exc_text', 'stack_info'
            ]:
                extra_data[key] = value
                
        # Добавляем в scope
        for key, value in extra_data.items():
            if value is not None:
                scope.set_extra(key, value)
    
    def _capture_exception(self, record: logging.LogRecord) -> None:
        """Capture exception with traceback."""
        # exc_info это tuple (type, value, traceback)
        if record.exc_info and record.exc_info[0] is not None:
            sentry_sdk.capture_exception(record.exc_info[1])
    
    def _capture_message(self, record: logging.LogRecord) -> None:
        """Capture log message as Sentry event."""
        message = self.format(record)
        level = self.LEVEL_MAPPING.get(record.levelno, "error")
        
        sentry_sdk.capture_message(
            message,
            level=level,
        )
    
    def _add_breadcrumb(self, record: logging.LogRecord) -> None:
        """Add log record as breadcrumb for context."""
        sentry_sdk.add_breadcrumb(
            message=record.getMessage(),
            level=self.LEVEL_MAPPING.get(record.levelno, "info"),
            category=record.name,
            timestamp=datetime.fromtimestamp(record.created),
            data={
                "logger": record.name,
                "module": record.module,
                "function": record.funcName,
                "line": record.lineno,
            }
        )
```

### Шаг 3: Добавить вспомогательные классы

```python
class BufferedSentryHandler(SentryHandler):
    """
    Buffered version of SentryHandler for better performance.
    
    Collects multiple log records and sends them in batches.
    """
    
    def __init__(self, level: int = logging.ERROR,
                 buffer_size: int = 10,
                 flush_interval: float = 5.0,
                 **kwargs):
        """
        Initialize BufferedSentryHandler.
        
        Args:
            level: Minimum level for handling records
            buffer_size: Maximum number of records to buffer
            flush_interval: Maximum seconds between flushes
            **kwargs: Additional arguments for SentryHandler
        """
        super().__init__(level=level, **kwargs)
        self.buffer_size = buffer_size
        self.flush_interval = flush_interval
        self.buffer: List[logging.LogRecord] = []
        self.last_flush = datetime.now()
        
    def emit(self, record: logging.LogRecord) -> None:
        """Buffer the record and flush if needed."""
        self.buffer.append(record)
        
        # Проверяем условия для flush
        should_flush = (
            len(self.buffer) >= self.buffer_size or
            (datetime.now() - self.last_flush).total_seconds() > self.flush_interval or
            record.levelno >= logging.ERROR  # Немедленно отправляем ошибки
        )
        
        if should_flush:
            self.flush()
    
    def flush(self) -> None:
        """Flush all buffered records to Sentry."""
        if not self.buffer:
            return
            
        # Отправляем каждую запись
        for record in self.buffer:
            try:
                super().emit(record)
            except Exception:
                self.handleError(record)
                
        # Очищаем буфер
        self.buffer.clear()
        self.last_flush = datetime.now()
    
    def close(self) -> None:
        """Flush and close the handler."""
        self.flush()
        super().close()


class ConditionalSentryHandler(SentryHandler):
    """
    Conditional handler that only sends to Sentry based on conditions.
    
    Useful for filtering out certain types of errors or loggers.
    """
    
    def __init__(self, level: int = logging.ERROR,
                 exclude_loggers: Optional[List[str]] = None,
                 include_loggers: Optional[List[str]] = None,
                 condition_func: Optional[callable] = None,
                 **kwargs):
        """
        Initialize ConditionalSentryHandler.
        
        Args:
            level: Minimum level for handling records
            exclude_loggers: List of logger names to exclude
            include_loggers: List of logger names to include (if set, only these are included)
            condition_func: Custom function to determine if record should be sent
            **kwargs: Additional arguments for SentryHandler
        """
        super().__init__(level=level, **kwargs)
        self.exclude_loggers = set(exclude_loggers or [])
        self.include_loggers = set(include_loggers or [])
        self.condition_func = condition_func
        
    def should_emit(self, record: logging.LogRecord) -> bool:
        """Determine if record should be sent to Sentry."""
        # Проверяем exclude список
        if record.name in self.exclude_loggers:
            return False
            
        # Проверяем include список
        if self.include_loggers and record.name not in self.include_loggers:
            return False
            
        # Проверяем кастомное условие
        if self.condition_func and not self.condition_func(record):
            return False
            
        return True
    
    def emit(self, record: logging.LogRecord) -> None:
        """Conditionally emit the record."""
        if self.should_emit(record):
            super().emit(record)
```

### Шаг 4: Создать фабричную функцию

```python
def create_sentry_handler(level: Union[int, str] = logging.ERROR,
                         buffered: bool = False,
                         conditional: bool = False,
                         **kwargs) -> SentryHandler:
    """
    Factory function to create appropriate Sentry handler.
    
    Args:
        level: Logging level (int or string)
        buffered: Whether to use buffered handler
        conditional: Whether to use conditional handler
        **kwargs: Additional arguments for handler
        
    Returns:
        Configured SentryHandler instance
    """
    # Преобразуем строковый уровень в int
    if isinstance(level, str):
        level = getattr(logging, level.upper())
    
    # Выбираем класс handler'а
    if buffered and conditional:
        raise ValueError("Cannot use both buffered and conditional handlers")
    elif buffered:
        handler_class = BufferedSentryHandler
    elif conditional:
        handler_class = ConditionalSentryHandler
    else:
        handler_class = SentryHandler
    
    # Создаем handler
    return handler_class(level=level, **kwargs)
```

### Шаг 5: Добавить документацию и примеры использования

```python
"""
Examples:
    Basic usage:
    >>> handler = SentryHandler(level=logging.ERROR)
    >>> logger.addHandler(handler)
    
    Buffered handler for performance:
    >>> handler = BufferedSentryHandler(
    ...     level=logging.WARNING,
    ...     buffer_size=20,
    ...     flush_interval=10.0
    ... )
    
    Conditional handler to exclude noisy loggers:
    >>> handler = ConditionalSentryHandler(
    ...     level=logging.ERROR,
    ...     exclude_loggers=['urllib3', 'requests']
    ... )
    
    Using factory function:
    >>> handler = create_sentry_handler(
    ...     level='ERROR',
    ...     buffered=True,
    ...     buffer_size=50
    ... )
"""
```

## 🧪 Тестирование реализации

### Модульный тест (добавить в tests/core/test_sentry_handler.py):
```python
import pytest
import logging
from unittest.mock import patch, MagicMock, call
from core.logging.handlers import SentryHandler, BufferedSentryHandler

class TestSentryHandler:
    @patch('sentry_sdk.capture_message')
    @patch('sentry_sdk.push_scope')
    @patch('core.logging.handlers.get_context')
    def test_emit_message(self, mock_get_context, mock_push_scope, mock_capture):
        # Настройка
        mock_get_context.return_value = {
            'request_id': 'test-123',
            'user_id': 42
        }
        mock_scope = MagicMock()
        mock_push_scope.return_value.__enter__.return_value = mock_scope
        
        handler = SentryHandler(level=logging.ERROR)
        record = logging.LogRecord(
            name="test.logger",
            level=logging.ERROR,
            pathname="test.py",
            lineno=10,
            msg="Test error message",
            args=(),
            exc_info=None
        )
        
        # Выполнение
        handler.emit(record)
        
        # Проверки
        mock_scope.set_context.assert_called()
        mock_scope.set_tag.assert_any_call('logger', 'test.logger')
        mock_scope.set_tag.assert_any_call('level', 'ERROR')
        mock_capture.assert_called_once()
```

### Интеграционный тест:
```python
@pytest.mark.integration
def test_sentry_integration():
    with patch('sentry_sdk.init') as mock_init:
        # Инициализация Sentry
        import sentry_sdk
        sentry_sdk.init(dsn="https://<EMAIL>/123")
        
        # Создание и использование handler
        handler = SentryHandler()
        logger = logging.getLogger('test')
        logger.addHandler(handler)
        
        # Логирование ошибки
        try:
            1 / 0
        except ZeroDivisionError:
            logger.exception("Division error occurred")
```

## ⚠️ Потенциальные проблемы

### Проблема 1: Sentry SDK не инициализирован
**Симптом**: Handler молча не отправляет логи
**Решение**: Проверка `sentry_sdk.Hub.current.client` перед отправкой

### Проблема 2: Циклические импорты с ContextLogger
**Симптом**: ImportError при импорте
**Решение**: Использование try/except с заглушкой для get_context

### Проблема 3: Производительность при большом объеме логов
**Симптом**: Задержки в приложении
**Решение**: Использование BufferedSentryHandler

### Проблема 4: Утечка памяти в BufferedSentryHandler
**Симптом**: Рост потребления памяти
**Решение**: Обязательный вызов flush() при завершении

## 📊 Критерии успешного завершения
- ✅ Реализован базовый SentryHandler с методом emit()
- ✅ Добавлена интеграция с контекстом из ContextLogger
- ✅ Реализован маппинг уровней логирования
- ✅ Добавлена поддержка тегов и extra данных
- ✅ Реализованы BufferedSentryHandler и ConditionalSentryHandler
- ✅ Добавлена фабричная функция create_sentry_handler()
- ✅ Написаны docstrings и примеры использования
- ✅ Обработка ошибок не ломает приложение

## ⏱️ Оценка времени
**1.5 часа** - полная реализация с тестами

## 🔗 Связанные задачи
- **Предыдущая**: 001_sentry_infrastructure_setup.md
- **Следующая**: 003_sensitive_data_filter.md
- **Зависит от**: 005_context_logger_enhancement.md (для полной функциональности)

## 📝 Дополнительные заметки
- Handler должен быть устойчив к ошибкам и не ломать приложение
- Важно правильно обрабатывать None значения в контексте
- BufferedSentryHandler требует явного вызова flush() при завершении
- Рекомендуется использовать ConditionalSentryHandler для production