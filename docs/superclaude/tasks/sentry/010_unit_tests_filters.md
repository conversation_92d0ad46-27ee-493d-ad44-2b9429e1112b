# Задача 010: Unit тесты для фильтров

## 📋 Описание задачи
Написать полный набор unit тестов для фильтров логирования: SensitiveDataFilter (задача 003) и RateLimitFilter (задача 004). Тесты должны покрывать все сценарии использования, граничные случаи и потенциальные проблемы.

## 🎯 Цели
- Достичь 100% покрытия кода фильтров
- Протестировать все паттерны чувствительных данных
- Проверить работу rate limiting с разными настройками
- Протестировать производительность фильтров
- Проверить обработку ошибок и edge cases
- Создать fixtures и helpers для переиспользования

## ✅ Предварительные требования
- Выполнены задачи 003 и 004 (фильтры реализованы)
- Установлен pytest и pytest-cov
- Установлен freezegun для работы со временем
- Понимание работы logging.Filter

## 🔧 Детальные шаги реализации

### Шаг 1: Создать базовый тестовый файл и fixtures

```python
# tests/core/logging/test_sensitive_data_filter.py
import pytest
import logging
import json
import re
from typing import Dict, List, Any
from unittest.mock import Mock, patch
import time

from core.logging.filters import (
    SensitiveDataFilter,
    SensitivePattern,
    create_sensitive_data_filter,
    DjangoSensitiveDataFilter
)


@pytest.fixture
def basic_filter():
    """Create basic SensitiveDataFilter instance."""
    return SensitiveDataFilter()


@pytest.fixture
def strict_filter():
    """Create strict preset filter."""
    return create_sensitive_data_filter(preset='strict')


@pytest.fixture
def minimal_filter():
    """Create minimal preset filter."""
    return create_sensitive_data_filter(preset='minimal')


@pytest.fixture
def log_record():
    """Create a basic log record for testing."""
    def _create_record(msg="Test message", level=logging.INFO, **kwargs):
        record = logging.LogRecord(
            name="test.logger",
            level=level,
            pathname="test.py",
            lineno=10,
            msg=msg,
            args=(),
            exc_info=None
        )
        # Add any extra attributes
        for key, value in kwargs.items():
            setattr(record, key, value)
        return record
    return _create_record


@pytest.fixture
def sensitive_data_samples():
    """Sample sensitive data for testing."""
    return {
        'passwords': [
            ("password=secret123", "password=[PASSWORD]"),
            ("Password: mysecret", "Password: [PASSWORD]"),
            ("pwd=test@123", "pwd=[PASSWORD]"),
            ("PASSWD='complex!pass123'", "PASSWD='[PASSWORD]'"),
            ("пароль=секрет123", "пароль=[PASSWORD]"),  # Russian
        ],
        'api_keys': [
            ("api_key=sk_test_1234567890abcdef1234567890", "api_key=[API_KEY]"),
            ("API-KEY: abcdef1234567890abcdef1234567890", "API-KEY: [API_KEY]"),
            ("apikey='1234567890abcdef1234567890abcdef'", "apikey='[API_KEY]'"),
            ("x-api-key: Bearer abc123def456ghi789jkl012mno345", "x-api-key: Bearer [API_KEY]"),
        ],
        'tokens': [
            ("token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c",
             "token=[JWT_TOKEN]"),
            ("Authorization: Bearer mytoken123456", "Authorization: Bearer [TOKEN]"),
            ("refresh_token=rt_1234567890abcdef", "refresh_token=[TOKEN]"),
        ],
        'emails': [
            ("Contact: <EMAIL>", "Contact: [EMAIL]"),
            ("Email: <EMAIL>", "Email: [EMAIL]"),
            ("<NAME_EMAIL>", "Send to [EMAIL]"),
        ],
        'credit_cards': [
            ("Card: ****************", "Card: [CREDIT_CARD]"),
            ("CC: 5500 0000 0000 0004", "CC: [CREDIT_CARD]"),
            ("Payment: 3400-000000-00009", "Payment: [CREDIT_CARD]"),
        ],
        'database_urls': [
            ("postgres://user:pass123@localhost:5432/mydb",
             "postgres://[USER]:[PASSWORD]@localhost:5432/mydb"),
            ("mysql://admin:<EMAIL>/production",
             "mysql://[USER]:[PASSWORD]@db.example.com/production"),
            ("mongodb://root:<EMAIL>/app?retryWrites=true",
             "mongodb://[USER]:[PASSWORD]@cluster.mongodb.net/app?retryWrites=true"),
        ],
        'aws_keys': [
            ("AWS_ACCESS_KEY_ID=AKIAIOSFODNN7EXAMPLE", "AWS_ACCESS_KEY_ID=[AWS_KEY]"),
            ("aws_secret_access_key=wJalrXUtnFEMI/K7MDENG/bPxRfiCYEXAMPLEKEY",
             "aws_secret_access_key=[AWS_KEY]"),
        ],
        'sentry_dsn': [
            ("SENTRY_DSN=https://<EMAIL>/1234567",
             "SENTRY_DSN=[SENTRY_DSN]"),
            ("Configure: https://<EMAIL>/42",
             "Configure: [SENTRY_DSN]"),
        ],
    }
```

### Шаг 2: Тесты для SensitivePattern

```python
class TestSensitivePattern:
    """Test cases for SensitivePattern class."""
    
    def test_simple_pattern_replacement(self):
        """Test simple pattern matching and replacement."""
        pattern = SensitivePattern(
            name="test",
            pattern=re.compile(r"secret\d+"),
            replacement="[HIDDEN]"
        )
        
        assert pattern.replace("my secret123 text") == "my [HIDDEN] text"
        assert pattern.replace("no secrets here") == "no secrets here"
        assert pattern.replace("secret1 and secret2") == "[HIDDEN] and [HIDDEN]"
    
    def test_group_replacement(self):
        """Test replacement of specific regex groups."""
        pattern = SensitivePattern(
            name="key_value",
            pattern=re.compile(r"(key)=(\w+)"),
            replacement="[VALUE]",
            groups=[2]  # Only replace the value part
        )
        
        assert pattern.replace("key=secret123") == "key=[VALUE]"
        assert pattern.replace("other=value key=hidden") == "other=value key=[VALUE]"
    
    def test_multiple_groups(self):
        """Test replacement of multiple groups."""
        pattern = SensitivePattern(
            name="auth",
            pattern=re.compile(r"(user):(\w+)@(pass):(\w+)"),
            replacement="[REDACTED]",
            groups=[2, 4]  # Replace username and password
        )
        
        result = pattern.replace("user:john@pass:secret123")
        assert "john" not in result
        assert "secret123" not in result
        assert "user:" in result
        assert "@pass:" in result


class TestSensitiveDataFilter:
    """Test cases for SensitiveDataFilter."""
    
    def test_initialization(self, basic_filter):
        """Test filter initialization."""
        assert basic_filter.enabled is True
        assert len(basic_filter.patterns) > 0
        assert len(basic_filter.sensitive_fields) > 0
        assert basic_filter.max_value_length == 10000
    
    def test_custom_initialization(self):
        """Test filter with custom settings."""
        custom_pattern = SensitivePattern(
            name="custom",
            pattern=re.compile(r"custom_secret"),
            replacement="[CUSTOM]"
        )
        
        filter = SensitiveDataFilter(
            patterns=[custom_pattern],
            sensitive_fields={"custom_field"},
            max_value_length=100,
            enabled=False
        )
        
        assert len(filter.patterns) == 1
        assert filter.patterns[0].name == "custom"
        assert filter.sensitive_fields == {"custom_field"}
        assert filter.max_value_length == 100
        assert filter.enabled is False
    
    @pytest.mark.parametrize("data_type,test_cases", [
        ("passwords", "passwords"),
        ("api_keys", "api_keys"),
        ("tokens", "tokens"),
        ("emails", "emails"),
        ("credit_cards", "credit_cards"),
        ("database_urls", "database_urls"),
        ("aws_keys", "aws_keys"),
        ("sentry_dsn", "sentry_dsn"),
    ])
    def test_pattern_detection(self, basic_filter, sensitive_data_samples, data_type, test_cases):
        """Test detection of various sensitive data patterns."""
        for input_text, expected in sensitive_data_samples[test_cases]:
            result = basic_filter._scrub_string(input_text)
            assert result == expected, f"Failed for {data_type}: {input_text}"
    
    def test_dict_scrubbing(self, basic_filter):
        """Test scrubbing of dictionary values."""
        test_dict = {
            "username": "john_doe",
            "password": "secret123",
            "api_key": "sk_test_1234567890abcdef",
            "metadata": {
                "token": "bearer_abc123",
                "public_key": "pk_test_123",
                "secret_key": "sk_live_xyz789"
            },
            "tags": ["public", "api_key=hidden123", "normal"]
        }
        
        result = basic_filter._scrub_dict(test_dict)
        
        # Check sensitive fields are redacted
        assert result["password"] == "[REDACTED]"
        assert result["api_key"] == "[REDACTED]"
        assert result["metadata"]["token"] == "[REDACTED]"
        assert result["metadata"]["secret_key"] == "[REDACTED]"
        
        # Check non-sensitive fields are preserved
        assert result["username"] == "john_doe"
        assert result["metadata"]["public_key"] == "pk_test_123"
        
        # Check list processing
        assert result["tags"][0] == "public"
        assert "hidden123" not in result["tags"][1]
        assert result["tags"][2] == "normal"
    
    def test_list_scrubbing(self, basic_filter):
        """Test scrubbing of list values."""
        test_list = [
            "normal text",
            "password=secret123",
            {"api_key": "test_key_123"},
            ["nested", "token=abc123"],
            12345,
            True,
            None
        ]
        
        result = basic_filter._scrub_value(test_list)
        
        assert isinstance(result, list)
        assert result[0] == "normal text"
        assert "secret123" not in result[1]
        assert result[2]["api_key"] == "[REDACTED]"
        assert "abc123" not in result[3][1]
        assert result[4] == 12345
        assert result[5] is True
        assert result[6] is None
    
    def test_log_record_filtering(self, basic_filter, log_record):
        """Test filtering of actual log records."""
        # Create record with sensitive data
        record = log_record(
            msg="Login attempt with password=secret123",
            user_data={
                "username": "test_user",
                "password": "user_password",
                "session_token": "sess_abc123xyz"
            },
            api_key="sk_test_1234567890",
            safe_field="This is public information"
        )
        
        # Apply filter
        result = basic_filter.filter(record)
        assert result is True  # Filter should always return True
        
        # Check message was scrubbed
        assert "secret123" not in record.msg
        assert "[PASSWORD]" in record.msg
        
        # Check extra fields
        assert record.user_data["password"] == "[REDACTED]"
        assert record.user_data["session_token"] == "[REDACTED]"
        assert record.user_data["username"] == "test_user"
        assert record.api_key == "[REDACTED]"
        assert record.safe_field == "This is public information"
    
    def test_disabled_filter(self, log_record):
        """Test that disabled filter doesn't process records."""
        filter = SensitiveDataFilter(enabled=False)
        record = log_record(msg="password=secret123")
        
        filter.filter(record)
        
        # Should not be modified
        assert "secret123" in record.msg
    
    def test_exception_handling(self, basic_filter, log_record):
        """Test filter handles exceptions gracefully."""
        # Create record with problematic data
        record = log_record(msg="Test message")
        
        # Add an object that will cause issues
        class ProblematicClass:
            def __str__(self):
                raise ValueError("Cannot convert to string")
        
        record.problem_field = ProblematicClass()
        
        # Filter should not raise exception
        result = basic_filter.filter(record)
        assert result is True
    
    def test_circular_reference_handling(self, basic_filter):
        """Test handling of circular references."""
        # Create circular reference
        dict1 = {"key": "value", "password": "secret"}
        dict2 = {"ref": dict1, "token": "abc123"}
        dict1["circular"] = dict2
        
        # Should handle without infinite recursion
        result = basic_filter._scrub_value(dict1)
        assert isinstance(result, dict)
        # Note: Current implementation might not perfectly handle circular refs,
        # but it shouldn't crash
    
    def test_performance(self, basic_filter):
        """Test filter performance with large data."""
        import time
        
        # Create large dataset
        large_data = {
            f"field_{i}": f"password=secret{i}" if i % 10 == 0 else f"value{i}"
            for i in range(1000)
        }
        
        start_time = time.time()
        result = basic_filter._scrub_dict(large_data)
        elapsed = time.time() - start_time
        
        # Should process 1000 fields in under 100ms
        assert elapsed < 0.1
        
        # Verify some fields were scrubbed
        assert result["field_0"] == "password=[PASSWORD]"
        assert result["field_1"] == "value1"
    
    def test_max_value_length(self):
        """Test max_value_length limitation."""
        filter = SensitiveDataFilter(max_value_length=50)
        
        # Short string - should be processed
        short_text = "password=secret123"
        assert "secret123" not in filter._scrub_string(short_text)
        
        # Long string - should not be processed
        long_text = "x" * 60 + " password=secret123"
        result = filter._scrub_string(long_text)
        assert result == long_text  # Unchanged
```

### Шаг 3: Тесты для preset конфигураций

```python
class TestPresetConfigurations:
    """Test preset filter configurations."""
    
    def test_strict_preset(self, strict_filter):
        """Test strict preset configuration."""
        # Strict mode should redact more aggressively
        test_cases = [
            ("Code: 123456789", True),  # Number sequences
            ("Hash: abcdef1234567890abcdef", True),  # Hex strings
            ("ID: 42", False),  # Short numbers OK
            ("Regular text", False),
        ]
        
        for text, should_redact in test_cases:
            result = strict_filter._scrub_string(text)
            if should_redact:
                assert text != result, f"Expected redaction for: {text}"
            else:
                assert text == result, f"Unexpected redaction for: {text}"
    
    def test_minimal_preset(self, minimal_filter):
        """Test minimal preset configuration."""
        # Minimal mode should only redact obvious sensitive data
        test_cases = [
            ("password=secret123", True),
            ("api_key=abc123def456", True),
            ("Card: ****************", True),
            ("<EMAIL>", False),  # Emails not in minimal
            ("token=abc123", False),  # Generic tokens not in minimal
        ]
        
        for text, should_redact in test_cases:
            result = minimal_filter._scrub_string(text)
            if should_redact:
                assert text != result, f"Expected redaction for: {text}"
            else:
                assert text == result, f"Unexpected redaction for: {text}"
    
    def test_balanced_preset(self):
        """Test balanced (default) preset."""
        balanced_filter = create_sensitive_data_filter(preset='balanced')
        
        # Should have default patterns
        pattern_names = [p.name for p in balanced_filter.patterns]
        assert "password" in pattern_names
        assert "api_key" in pattern_names
        assert "email" in pattern_names
        assert "credit_card" in pattern_names
```

### Шаг 4: Тесты для дополнительных методов

```python
class TestFilterMethods:
    """Test additional filter methods."""
    
    def test_add_pattern(self, basic_filter):
        """Test adding custom patterns."""
        initial_count = len(basic_filter.patterns)
        
        custom_pattern = SensitivePattern(
            name="employee_id",
            pattern=re.compile(r"EMP\d{6}"),
            replacement="[EMPLOYEE_ID]"
        )
        
        basic_filter.add_pattern(custom_pattern)
        
        assert len(basic_filter.patterns) == initial_count + 1
        assert basic_filter.test_string("Employee: EMP123456") == "Employee: [EMPLOYEE_ID]"
    
    def test_remove_pattern(self, basic_filter):
        """Test removing patterns."""
        initial_count = len(basic_filter.patterns)
        
        # Remove password pattern
        basic_filter.remove_pattern("password")
        
        assert len(basic_filter.patterns) == initial_count - 1
        
        # Password should not be filtered anymore
        result = basic_filter.test_string("password=secret123")
        assert "secret123" in result
    
    def test_add_sensitive_field(self, basic_filter):
        """Test adding sensitive field names."""
        basic_filter.add_sensitive_field("custom_secret")
        
        test_dict = {
            "custom_secret": "my_value",
            "normal_field": "public_value"
        }
        
        result = basic_filter._scrub_dict(test_dict)
        assert result["custom_secret"] == "[REDACTED]"
        assert result["normal_field"] == "public_value"
    
    def test_list_patterns(self, basic_filter):
        """Test listing pattern names."""
        pattern_names = basic_filter.list_patterns()
        
        assert isinstance(pattern_names, list)
        assert "password" in pattern_names
        assert "api_key" in pattern_names
        assert "email" in pattern_names
    
    def test_field_check_caching(self, basic_filter):
        """Test that field checking is cached for performance."""
        # First call - should cache
        result1 = basic_filter._is_sensitive_field("test_field")
        assert "test_field" in basic_filter._field_check_cache
        
        # Second call - should use cache
        with patch.object(basic_filter, 'sensitive_fields') as mock_fields:
            result2 = basic_filter._is_sensitive_field("test_field")
            # sensitive_fields should not be accessed due to cache
            mock_fields.__contains__.assert_not_called()
        
        assert result1 == result2
```

### Шаг 5: Тесты для DjangoSensitiveDataFilter

```python
class TestDjangoSensitiveDataFilter:
    """Test Django-specific sensitive data filter."""
    
    @pytest.fixture
    def django_filter(self):
        """Create Django filter instance."""
        return DjangoSensitiveDataFilter()
    
    def test_django_patterns(self, django_filter):
        """Test Django-specific patterns."""
        test_cases = [
            # Django secret key
            ('SECRET_KEY = "django-insecure-abc123xyz"',
             'SECRET_KEY = "[DJANGO_SECRET]"'),
            
            # CSRF token
            ('csrfmiddlewaretoken=abcdef123456',
             'csrfmiddlewaretoken=[CSRF]'),
            
            # Session ID
            ('sessionid=xyz789abc123',
             'sessionid=[SESSION]'),
        ]
        
        for input_text, expected in test_cases:
            result = django_filter.test_string(input_text)
            assert result == expected
    
    def test_django_fields(self, django_filter):
        """Test Django-specific sensitive fields."""
        test_dict = {
            "csrfmiddlewaretoken": "token123",
            "sessionid": "session456",
            "_auth_user_id": "789",
            "_auth_user_backend": "django.contrib.auth.backends.ModelBackend",
            "_auth_user_hash": "hash123",
            "normal_field": "public_data"
        }
        
        result = django_filter._scrub_dict(test_dict)
        
        # Django fields should be redacted
        assert result["csrfmiddlewaretoken"] == "[REDACTED]"
        assert result["sessionid"] == "[REDACTED]"
        assert result["_auth_user_id"] == "[REDACTED]"
        assert result["_auth_user_backend"] == "[REDACTED]"
        assert result["_auth_user_hash"] == "[REDACTED]"
        
        # Normal field preserved
        assert result["normal_field"] == "public_data"
    
    def test_django_inherits_base_patterns(self, django_filter):
        """Test that Django filter includes base patterns."""
        # Should still filter basic passwords, API keys, etc.
        result = django_filter.test_string("password=secret123")
        assert "secret123" not in result
        
        result = django_filter.test_string("api_key=test_key_123")
        assert "test_key_123" not in result
```

### Шаг 6: Integration тесты

```python
class TestFilterIntegration:
    """Integration tests for filters with logging system."""
    
    def test_with_real_logger(self, basic_filter, caplog):
        """Test filter with actual Python logger."""
        # Create logger and add filter
        logger = logging.getLogger("test.integration")
        logger.addFilter(basic_filter)
        
        # Log message with sensitive data
        with caplog.at_level(logging.INFO):
            logger.info(
                "User login",
                extra={
                    "username": "john_doe",
                    "password": "secret123",
                    "session_id": "sess_abc123"
                }
            )
        
        # Check captured logs
        assert len(caplog.records) == 1
        record = caplog.records[0]
        
        # Verify sensitive data was filtered
        assert record.password == "[REDACTED]"
        assert record.session_id == "[REDACTED]"
        assert record.username == "john_doe"  # Not sensitive
    
    def test_with_formatter(self, basic_filter):
        """Test filter works with log formatters."""
        # Create handler with formatter
        handler = logging.StreamHandler()
        formatter = logging.Formatter(
            '%(levelname)s - %(message)s - password=%(password)s'
        )
        handler.setFormatter(formatter)
        
        # Create logger with filter
        logger = logging.getLogger("test.formatter")
        logger.addHandler(handler)
        logger.addFilter(basic_filter)
        
        # Create record with sensitive data
        record = logging.LogRecord(
            name="test",
            level=logging.INFO,
            pathname="test.py",
            lineno=1,
            msg="Test",
            args=(),
            exc_info=None
        )
        record.password = "secret123"
        
        # Apply filter
        basic_filter.filter(record)
        
        # Format message
        formatted = formatter.format(record)
        
        # Password should be redacted in formatted output
        assert "secret123" not in formatted
        assert "[REDACTED]" in formatted
    
    @pytest.mark.parametrize("log_level", [
        logging.DEBUG,
        logging.INFO,
        logging.WARNING,
        logging.ERROR,
        logging.CRITICAL
    ])
    def test_all_log_levels(self, basic_filter, log_record, log_level):
        """Test filter works with all log levels."""
        record = log_record(
            msg="API call with key=secret_key_123",
            level=log_level
        )
        
        basic_filter.filter(record)
        
        assert "secret_key_123" not in record.msg
        assert record.levelno == log_level
```

### Шаг 7: Тесты производительности и нагрузки

```python
class TestFilterPerformance:
    """Performance tests for filters."""
    
    @pytest.mark.performance
    def test_large_message_performance(self, basic_filter):
        """Test performance with large messages."""
        import time
        
        # Create large message with multiple sensitive items
        large_msg = " ".join([
            f"Entry {i}: password=pass{i}, api_key=key{i}, email=user{i}@example.com"
            for i in range(100)
        ])
        
        start_time = time.time()
        result = basic_filter._scrub_string(large_msg)
        elapsed = time.time() - start_time
        
        # Should process in reasonable time
        assert elapsed < 0.5  # 500ms for 100 entries
        
        # Verify filtering worked
        assert "pass0" not in result
        assert "key0" not in result
        assert "@example.com" not in result
    
    @pytest.mark.performance
    def test_deep_nesting_performance(self, basic_filter):
        """Test performance with deeply nested structures."""
        import time
        
        # Create deeply nested structure
        def create_nested_dict(depth, current=0):
            if current >= depth:
                return {
                    "password": f"secret{current}",
                    "data": f"value{current}"
                }
            return {
                "level": current,
                "password": f"pass{current}",
                "nested": create_nested_dict(depth, current + 1)
            }
        
        deep_dict = create_nested_dict(50)  # 50 levels deep
        
        start_time = time.time()
        result = basic_filter._scrub_value(deep_dict)
        elapsed = time.time() - start_time
        
        # Should handle deep nesting
        assert elapsed < 1.0  # 1 second for 50 levels
        
        # Verify filtering at multiple levels
        assert result["password"] == "[REDACTED]"
        assert result["nested"]["password"] == "[REDACTED]"
    
    @pytest.mark.performance
    def test_cache_effectiveness(self, basic_filter):
        """Test caching improves performance."""
        import time
        
        # First run - no cache
        test_string = "password=secret123 " * 100
        
        start_time = time.time()
        result1 = basic_filter._scrub_string(test_string)
        first_run = time.time() - start_time
        
        # Second run - with cache
        start_time = time.time()
        result2 = basic_filter._scrub_string(test_string)
        second_run = time.time() - start_time
        
        # Cache should make second run faster
        assert second_run <= first_run
        assert result1 == result2
```

### Шаг 8: Тесты граничных случаев

```python
class TestEdgeCases:
    """Test edge cases and boundary conditions."""
    
    def test_empty_values(self, basic_filter):
        """Test handling of empty values."""
        assert basic_filter._scrub_string("") == ""
        assert basic_filter._scrub_string(None) is None
        assert basic_filter._scrub_dict({}) == {}
        assert basic_filter._scrub_value([]) == []
    
    def test_special_characters(self, basic_filter):
        """Test handling of special characters."""
        test_cases = [
            "password=test@#$%^&*()",
            "api_key=test\nwith\nnewlines",
            "token=test\twith\ttabs",
            "secret=test with spaces",
            'password="quoted value"',
            "key='single quoted'",
        ]
        
        for test in test_cases:
            result = basic_filter._scrub_string(test)
            # Should handle special chars without errors
            assert isinstance(result, str)
    
    def test_unicode_handling(self, basic_filter):
        """Test handling of unicode characters."""
        test_cases = [
            "пароль=секрет123",  # Russian
            "密码=secret123",  # Chinese
            "パスワード=secret123",  # Japanese
            "🔐 password=secret123",  # Emoji
        ]
        
        for test in test_cases:
            result = basic_filter._scrub_string(test)
            assert "secret123" not in result
    
    def test_binary_data(self, basic_filter):
        """Test handling of binary data."""
        # Binary data that can't be decoded
        binary_data = b"\x80\x81\x82password=secret"
        
        # Should handle gracefully
        result = basic_filter._scrub_value(binary_data)
        assert result == binary_data  # Unchanged
    
    def test_None_in_structures(self, basic_filter):
        """Test handling of None values in structures."""
        test_dict = {
            "field1": None,
            "password": None,
            "nested": {
                "key": None,
                "token": "value"
            },
            "list": [None, "password=secret", None]
        }
        
        result = basic_filter._scrub_dict(test_dict)
        
        assert result["field1"] is None
        assert result["password"] == "[REDACTED]"  # Field name is sensitive
        assert result["nested"]["key"] is None
        assert result["list"][0] is None
        assert "secret" not in str(result["list"][1])
        assert result["list"][2] is None


class TestErrorHandling:
    """Test error handling and resilience."""
    
    def test_regex_errors(self, basic_filter):
        """Test handling of regex errors."""
        # Add a pattern with potential regex issues
        bad_pattern = SensitivePattern(
            name="bad",
            pattern=re.compile(r"(?P<invalid"),  # Invalid regex
            replacement="[BAD]"
        )
        
        # Pattern compilation should fail during creation,
        # but let's test runtime errors
        with patch.object(bad_pattern, 'replace', side_effect=re.error("regex error")):
            basic_filter.patterns.append(bad_pattern)
            
            # Should not crash
            result = basic_filter._scrub_string("test string")
            assert isinstance(result, str)
    
    def test_recursion_limit(self, basic_filter):
        """Test handling of recursion limits."""
        # Create structure that could cause deep recursion
        data = {"level": 0}
        current = data
        
        # Create 1000 levels (more than default recursion limit)
        for i in range(1000):
            current["next"] = {"level": i + 1, "password": f"secret{i}"}
            current = current["next"]
        
        # Should handle without RecursionError
        # (implementation may need recursion limit handling)
        try:
            result = basic_filter._scrub_value(data)
            assert isinstance(result, dict)
        except RecursionError:
            pytest.skip("Implementation doesn't handle deep recursion")
    
    def test_memory_usage(self, basic_filter):
        """Test memory usage with large data."""
        import sys
        
        # Create large structure
        large_list = [
            {"password": f"secret{i}", "data": "x" * 1000}
            for i in range(1000)
        ]
        
        # Get size before
        size_before = sys.getsizeof(large_list)
        
        # Process
        result = basic_filter._scrub_value(large_list)
        
        # Size should not explode
        size_after = sys.getsizeof(result)
        assert size_after < size_before * 2  # Should not double memory
```

### Шаг 9: Создать pytest fixtures и conftest

```python
# tests/core/logging/conftest.py
import pytest
import logging


@pytest.fixture(autouse=True)
def reset_logging():
    """Reset logging configuration after each test."""
    yield
    # Clear any handlers/filters added during tests
    for logger in logging.Logger.manager.loggerDict.values():
        if isinstance(logger, logging.Logger):
            logger.handlers.clear()
            logger.filters.clear()


@pytest.fixture
def mock_time():
    """Mock time for consistent testing."""
    with patch('time.time', return_value=1234567890.0):
        yield


# Markers for test categories
def pytest_configure(config):
    config.addinivalue_line(
        "markers", "performance: mark test as performance test"
    )
    config.addinivalue_line(
        "markers", "integration: mark test as integration test"
    )
```

## 🧪 Запуск тестов

### Команды для запуска:

```bash
# Запустить все тесты для фильтров
pytest tests/core/logging/test_sensitive_data_filter.py -v

# Запустить с покрытием кода
pytest tests/core/logging/test_sensitive_data_filter.py --cov=core.logging.filters --cov-report=html

# Запустить только быстрые тесты (без performance)
pytest tests/core/logging/test_sensitive_data_filter.py -v -m "not performance"

# Запустить только performance тесты
pytest tests/core/logging/test_sensitive_data_filter.py -v -m performance

# Запустить с подробным выводом для отладки
pytest tests/core/logging/test_sensitive_data_filter.py -vvs

# Запустить конкретный тест
pytest tests/core/logging/test_sensitive_data_filter.py::TestSensitiveDataFilter::test_dict_scrubbing -v
```

### Проверка покрытия:

```bash
# Генерация HTML отчета
pytest --cov=core.logging.filters --cov-report=html

# Просмотр в браузере
open htmlcov/index.html

# Консольный отчет
pytest --cov=core.logging.filters --cov-report=term-missing
```

## ⚠️ Потенциальные проблемы

### Проблема 1: Тесты влияют друг на друга
**Симптом**: Тесты проходят по отдельности, но падают вместе
**Решение**: Использовать reset_logging fixture, изолировать тесты

### Проблема 2: Медленные performance тесты
**Симптом**: Тесты выполняются слишком долго
**Решение**: Использовать pytest marks и запускать отдельно

### Проблема 3: Flaky тесты с временем
**Симптом**: Тесты иногда проходят, иногда нет
**Решение**: Использовать freezegun или mock для времени

### Проблема 4: Утечки памяти в тестах
**Симптом**: Память растет при запуске тестов
**Решение**: Очищать большие структуры после тестов

## 📊 Критерии успешного завершения
- ✅ Покрытие кода фильтров > 95%
- ✅ Все паттерны протестированы
- ✅ Тесты для всех preset конфигураций
- ✅ Performance тесты проходят
- ✅ Edge cases покрыты
- ✅ Integration тесты с logging
- ✅ Тесты изолированы и независимы
- ✅ CI/CD готовые тесты

## ⏱️ Оценка времени
**2 часа** - написание всех тестов и достижение покрытия

## 🔗 Связанные задачи
- **Предыдущая**: 009_logging_configuration.md
- **Следующая**: 011_unit_tests_handlers.md
- **Тестирует**: 003_sensitive_data_filter.md, 004_rate_limit_filter.md

## 📝 Дополнительные заметки
- Тесты должны быть быстрыми и надежными
- Использовать параметризацию для похожих тестов
- Покрыть как позитивные, так и негативные сценарии
- Обязательно тестировать граничные случаи
- Performance тесты помогут выявить узкие места

---

# Часть 2: Тесты для RateLimitFilter

## 🔧 Детальные шаги реализации для RateLimitFilter

### Шаг 1: Создать тестовый файл для RateLimitFilter

```python
# tests/core/logging/test_rate_limit_filter.py
import pytest
import logging
import time
import threading
from unittest.mock import Mock, patch, MagicMock
from freezegun import freeze_time
import asyncio
from datetime import datetime, timedelta

from core.logging.filters import (
    RateLimitFilter,
    DuplicateFilter,
    BurstRateLimitFilter,
    AsyncRateLimitFilter,
    create_rate_limit_filter
)


@pytest.fixture
def basic_rate_filter():
    """Create basic RateLimitFilter instance."""
    return RateLimitFilter(rate=5, per=60)  # 5 messages per minute


@pytest.fixture
def burst_filter():
    """Create BurstRateLimitFilter instance."""
    return BurstRateLimitFilter(rate=10, per=60, burst=20)


@pytest.fixture
def duplicate_filter():
    """Create DuplicateFilter instance."""
    return DuplicateFilter(cache_size=100, ttl=60)


@pytest.fixture
def log_record():
    """Factory for creating log records."""
    def _create_record(msg="Test message", level=logging.INFO, **kwargs):
        record = logging.LogRecord(
            name="test.logger",
            level=level,
            pathname="test.py",
            lineno=10,
            msg=msg,
            args=(),
            exc_info=None
        )
        for key, value in kwargs.items():
            setattr(record, key, value)
        return record
    return _create_record


@pytest.fixture
def mock_time():
    """Mock time for consistent testing."""
    with patch('time.time', return_value=1000.0) as mock:
        yield mock
```

### Шаг 2: Тесты для базового RateLimitFilter

```python
class TestRateLimitFilter:
    """Test cases for RateLimitFilter."""
    
    def test_initialization(self):
        """Test filter initialization with various parameters."""
        # Default initialization
        filter1 = RateLimitFilter()
        assert filter1.rate == 10
        assert filter1.per == 60
        assert filter1.enabled is True
        
        # Custom initialization
        filter2 = RateLimitFilter(rate=5, per=30, enabled=False)
        assert filter2.rate == 5
        assert filter2.per == 30
        assert filter2.enabled is False
    
    def test_basic_rate_limiting(self, basic_rate_filter, log_record, mock_time):
        """Test basic rate limiting functionality."""
        # First 5 messages should pass
        for i in range(5):
            record = log_record(msg=f"Message {i}")
            assert basic_rate_filter.filter(record) is True
        
        # 6th message should be blocked
        record = log_record(msg="Message 6")
        assert basic_rate_filter.filter(record) is False
    
    def test_time_window_reset(self, basic_rate_filter, log_record, mock_time):
        """Test that rate limit resets after time window."""
        # Fill up the limit
        for i in range(5):
            record = log_record(msg=f"Message {i}")
            assert basic_rate_filter.filter(record) is True
        
        # Next message blocked
        assert basic_rate_filter.filter(log_record()) is False
        
        # Move time forward past the window
        mock_time.return_value = 1061.0  # 61 seconds later
        
        # Should allow messages again
        for i in range(5):
            record = log_record(msg=f"New message {i}")
            assert basic_rate_filter.filter(record) is True
    
    def test_different_message_tracking(self, basic_rate_filter, log_record, mock_time):
        """Test that different messages are tracked separately."""
        # 5 of message A
        for i in range(5):
            record = log_record(msg="Message A")
            assert basic_rate_filter.filter(record) is True
        
        # 6th message A blocked
        assert basic_rate_filter.filter(log_record(msg="Message A")) is False
        
        # But message B should still be allowed
        for i in range(5):
            record = log_record(msg="Message B")
            assert basic_rate_filter.filter(record) is True
    
    def test_message_key_generation(self, basic_rate_filter, log_record):
        """Test message key generation for different records."""
        # Same message content = same key
        record1 = log_record(msg="Test message")
        record2 = log_record(msg="Test message")
        key1 = basic_rate_filter._get_message_key(record1)
        key2 = basic_rate_filter._get_message_key(record2)
        assert key1 == key2
        
        # Different logger name = different key
        record3 = log_record(msg="Test message")
        record3.name = "other.logger"
        key3 = basic_rate_filter._get_message_key(record3)
        assert key3 != key1
        
        # Different level = different key
        record4 = log_record(msg="Test message", level=logging.ERROR)
        key4 = basic_rate_filter._get_message_key(record4)
        assert key4 != key1
    
    def test_disabled_filter(self, log_record):
        """Test that disabled filter doesn't limit."""
        filter = RateLimitFilter(rate=1, per=60, enabled=False)
        
        # Should allow unlimited messages
        for i in range(100):
            record = log_record(msg=f"Message {i}")
            assert filter.filter(record) is True
    
    def test_sliding_window(self, basic_rate_filter, log_record):
        """Test sliding window implementation."""
        with patch('time.time') as mock_time:
            # Spread messages over time
            times = [0, 10, 20, 30, 40, 50, 60, 70]
            
            for t in times[:6]:  # First 6 messages
                mock_time.return_value = t
                record = log_record(msg="Test")
                if t < 60:
                    # Within first minute - only 5 allowed
                    expected = basic_rate_filter._message_counts[basic_rate_filter._get_message_key(record)] < 5
                else:
                    # After 60s, first message expires
                    expected = True
                
                result = basic_rate_filter.filter(record)
                if t == 50:  # 6th message in first minute
                    assert result is False
                else:
                    assert result is expected
    
    def test_cache_cleanup(self, basic_rate_filter, log_record, mock_time):
        """Test that old entries are cleaned up."""
        # Create many different messages
        for i in range(1000):
            record = log_record(msg=f"Message {i}")
            basic_rate_filter.filter(record)
        
        # Move time forward
        mock_time.return_value = 2000.0  # Way past the window
        
        # Trigger cleanup
        basic_rate_filter._cleanup_old_entries()
        
        # Check that old entries were removed
        assert len(basic_rate_filter._message_counts) == 0
        assert len(basic_rate_filter._message_times) == 0
    
    def test_thread_safety(self, basic_rate_filter, log_record):
        """Test thread safety of rate limiting."""
        import threading
        import queue
        
        results = queue.Queue()
        
        def worker(worker_id):
            for i in range(10):
                record = log_record(msg="Shared message")
                result = basic_rate_filter.filter(record)
                results.put((worker_id, i, result))
                time.sleep(0.001)  # Small delay
        
        # Start multiple threads
        threads = []
        for i in range(5):
            t = threading.Thread(target=worker, args=(i,))
            threads.append(t)
            t.start()
        
        # Wait for completion
        for t in threads:
            t.join()
        
        # Collect results
        allowed_count = 0
        while not results.empty():
            worker_id, msg_num, allowed = results.get()
            if allowed:
                allowed_count += 1
        
        # Should have limited to 5 messages total
        assert allowed_count == 5
```

### Шаг 3: Тесты для DuplicateFilter

```python
class TestDuplicateFilter:
    """Test cases for DuplicateFilter."""
    
    def test_duplicate_detection(self, duplicate_filter, log_record):
        """Test basic duplicate detection."""
        # First occurrence should pass
        record1 = log_record(msg="Duplicate message")
        assert duplicate_filter.filter(record1) is True
        
        # Immediate duplicate should be blocked
        record2 = log_record(msg="Duplicate message")
        assert duplicate_filter.filter(record2) is False
        
        # Different message should pass
        record3 = log_record(msg="Different message")
        assert duplicate_filter.filter(record3) is True
    
    def test_ttl_expiration(self, duplicate_filter, log_record):
        """Test that duplicates are allowed after TTL."""
        with freeze_time("2023-01-01 00:00:00") as frozen_time:
            # First message
            record1 = log_record(msg="TTL test")
            assert duplicate_filter.filter(record1) is True
            
            # Duplicate blocked
            record2 = log_record(msg="TTL test")
            assert duplicate_filter.filter(record2) is False
            
            # Move past TTL
            frozen_time.tick(delta=timedelta(seconds=61))
            
            # Should be allowed again
            record3 = log_record(msg="TTL test")
            assert duplicate_filter.filter(record3) is True
    
    def test_cache_size_limit(self, log_record):
        """Test cache size limiting."""
        filter = DuplicateFilter(cache_size=10, ttl=3600)
        
        # Add more than cache_size unique messages
        for i in range(20):
            record = log_record(msg=f"Message {i}")
            assert filter.filter(record) is True
        
        # Cache should not exceed size limit
        assert len(filter._cache) <= 10
        
        # Oldest messages should have been evicted
        # Message 0 should be allowed again (evicted)
        record = log_record(msg="Message 0")
        assert filter.filter(record) is True
    
    def test_lru_eviction(self, log_record):
        """Test LRU eviction policy."""
        filter = DuplicateFilter(cache_size=3, ttl=3600)
        
        # Fill cache
        for i in range(3):
            record = log_record(msg=f"Message {i}")
            assert filter.filter(record) is True
        
        # Access Message 0 to make it recently used
        record = log_record(msg="Message 0")
        assert filter.filter(record) is False  # Duplicate
        
        # Add new message - should evict Message 1 (least recently used)
        record = log_record(msg="Message 3")
        assert filter.filter(record) is True
        
        # Message 1 should be allowed (evicted)
        record = log_record(msg="Message 1")
        assert filter.filter(record) is True
        
        # Message 0 should still be blocked (was accessed)
        record = log_record(msg="Message 0")
        assert filter.filter(record) is False
    
    def test_different_levels_same_message(self, duplicate_filter, log_record):
        """Test that same message with different levels are tracked separately."""
        # INFO level
        record1 = log_record(msg="Same message", level=logging.INFO)
        assert duplicate_filter.filter(record1) is True
        
        # ERROR level - should be treated as different
        record2 = log_record(msg="Same message", level=logging.ERROR)
        assert duplicate_filter.filter(record2) is True
        
        # Another INFO - should be blocked
        record3 = log_record(msg="Same message", level=logging.INFO)
        assert duplicate_filter.filter(record3) is False
```

### Шаг 4: Тесты для BurstRateLimitFilter

```python
class TestBurstRateLimitFilter:
    """Test cases for BurstRateLimitFilter with token bucket."""
    
    def test_burst_capacity(self, burst_filter, log_record, mock_time):
        """Test burst capacity allows temporary spikes."""
        # Should allow burst of 20 messages immediately
        for i in range(20):
            record = log_record(msg="Burst message")
            assert burst_filter.filter(record) is True
        
        # 21st message should be blocked
        record = log_record(msg="Burst message")
        assert burst_filter.filter(record) is False
    
    def test_token_refill(self, burst_filter, log_record):
        """Test token bucket refill over time."""
        with patch('time.time') as mock_time:
            mock_time.return_value = 1000.0
            
            # Use all burst capacity
            for i in range(20):
                record = log_record(msg="Test")
                assert burst_filter.filter(record) is True
            
            # No tokens left
            assert burst_filter.filter(log_record(msg="Test")) is False
            
            # Move forward 6 seconds (should generate 1 token at 10/min rate)
            mock_time.return_value = 1006.0
            
            # Should allow 1 message
            assert burst_filter.filter(log_record(msg="Test")) is True
            assert burst_filter.filter(log_record(msg="Test")) is False
    
    def test_refill_rate_calculation(self, log_record):
        """Test correct refill rate calculation."""
        # 60 messages per minute = 1 per second
        filter = BurstRateLimitFilter(rate=60, per=60, burst=10)
        
        with patch('time.time') as mock_time:
            mock_time.return_value = 1000.0
            
            # Use all tokens
            for i in range(10):
                filter.filter(log_record(msg="Test"))
            
            # After 5 seconds, should have 5 new tokens
            mock_time.return_value = 1005.0
            
            allowed = 0
            for i in range(10):
                if filter.filter(log_record(msg="Test")):
                    allowed += 1
            
            assert allowed == 5
    
    def test_burst_capacity_limit(self, log_record):
        """Test that tokens don't exceed burst capacity."""
        filter = BurstRateLimitFilter(rate=60, per=60, burst=5)
        
        with patch('time.time') as mock_time:
            mock_time.return_value = 1000.0
            
            # Don't use any tokens for 60 seconds
            mock_time.return_value = 1060.0
            
            # Should still be limited to burst capacity
            allowed = 0
            for i in range(100):
                if filter.filter(log_record(msg="Test")):
                    allowed += 1
            
            assert allowed == 5  # Burst capacity
    
    def test_per_message_buckets(self, burst_filter, log_record):
        """Test separate token buckets per message."""
        with patch('time.time') as mock_time:
            mock_time.return_value = 1000.0
            
            # Use all tokens for message A
            for i in range(20):
                assert burst_filter.filter(log_record(msg="Message A")) is True
            
            assert burst_filter.filter(log_record(msg="Message A")) is False
            
            # Message B should have its own bucket
            for i in range(20):
                assert burst_filter.filter(log_record(msg="Message B")) is True
```

### Шаг 5: Тесты для AsyncRateLimitFilter

```python
class TestAsyncRateLimitFilter:
    """Test cases for AsyncRateLimitFilter."""
    
    @pytest.fixture
    def async_filter(self):
        """Create AsyncRateLimitFilter instance."""
        return AsyncRateLimitFilter(rate=5, per=60)
    
    @pytest.mark.asyncio
    async def test_async_rate_limiting(self, async_filter, log_record):
        """Test async rate limiting."""
        # First 5 messages should pass
        for i in range(5):
            record = log_record(msg="Async message")
            result = await async_filter.filter_async(record)
            assert result is True
        
        # 6th message should be blocked
        record = log_record(msg="Async message")
        result = await async_filter.filter_async(record)
        assert result is False
    
    @pytest.mark.asyncio
    async def test_concurrent_requests(self, async_filter, log_record):
        """Test handling of concurrent async requests."""
        async def make_request(msg_id):
            record = log_record(msg="Concurrent message")
            return await async_filter.filter_async(record)
        
        # Make 10 concurrent requests
        tasks = [make_request(i) for i in range(10)]
        results = await asyncio.gather(*tasks)
        
        # Only 5 should be allowed
        allowed_count = sum(1 for r in results if r)
        assert allowed_count == 5
    
    @pytest.mark.asyncio
    async def test_async_cleanup(self, async_filter, log_record):
        """Test async cleanup task."""
        # Add some messages
        for i in range(3):
            record = log_record(msg=f"Message {i}")
            await async_filter.filter_async(record)
        
        # Manually trigger cleanup
        await async_filter._cleanup_old_entries_async()
        
        # Should not crash and should work normally
        record = log_record(msg="After cleanup")
        result = await async_filter.filter_async(record)
        assert result is True
    
    def test_sync_fallback(self, async_filter, log_record):
        """Test sync filter method works."""
        # Should work in sync context
        for i in range(5):
            record = log_record(msg="Sync message")
            assert async_filter.filter(record) is True
        
        # 6th blocked
        assert async_filter.filter(log_record(msg="Sync message")) is False
```

### Шаг 6: Тесты для фабричной функции

```python
class TestFilterFactory:
    """Test filter factory function."""
    
    def test_create_basic_filter(self):
        """Test creating basic rate limit filter."""
        filter = create_rate_limit_filter(
            filter_type="basic",
            rate=20,
            per=120
        )
        
        assert isinstance(filter, RateLimitFilter)
        assert filter.rate == 20
        assert filter.per == 120
    
    def test_create_burst_filter(self):
        """Test creating burst rate limit filter."""
        filter = create_rate_limit_filter(
            filter_type="burst",
            rate=10,
            per=60,
            burst=30
        )
        
        assert isinstance(filter, BurstRateLimitFilter)
        assert filter.rate == 10
        assert filter.burst == 30
    
    def test_create_duplicate_filter(self):
        """Test creating duplicate filter."""
        filter = create_rate_limit_filter(
            filter_type="duplicate",
            cache_size=200,
            ttl=120
        )
        
        assert isinstance(filter, DuplicateFilter)
        assert filter.cache_size == 200
        assert filter.ttl == 120
    
    def test_create_async_filter(self):
        """Test creating async filter."""
        filter = create_rate_limit_filter(
            filter_type="async",
            rate=15,
            per=60
        )
        
        assert isinstance(filter, AsyncRateLimitFilter)
        assert filter.rate == 15
    
    def test_invalid_filter_type(self):
        """Test error handling for invalid filter type."""
        with pytest.raises(ValueError, match="Unknown filter type"):
            create_rate_limit_filter(filter_type="invalid")
    
    def test_preset_configurations(self):
        """Test preset configurations."""
        # Strict preset
        filter = create_rate_limit_filter(preset="strict")
        assert filter.rate == 1
        assert filter.per == 60
        
        # Moderate preset
        filter = create_rate_limit_filter(preset="moderate")
        assert filter.rate == 10
        assert filter.per == 60
        
        # Relaxed preset
        filter = create_rate_limit_filter(preset="relaxed")
        assert filter.rate == 100
        assert filter.per == 60
```

### Шаг 7: Performance тесты

```python
class TestRateLimitPerformance:
    """Performance tests for rate limit filters."""
    
    @pytest.mark.performance
    def test_high_volume_performance(self, basic_rate_filter, log_record):
        """Test performance with high message volume."""
        import time
        
        start_time = time.time()
        
        # Process 10,000 messages
        for i in range(10000):
            record = log_record(msg=f"Message {i % 100}")  # 100 different messages
            basic_rate_filter.filter(record)
        
        elapsed = time.time() - start_time
        
        # Should process 10k messages in under 1 second
        assert elapsed < 1.0
    
    @pytest.mark.performance
    def test_memory_usage(self, log_record):
        """Test memory usage doesn't grow unbounded."""
        import psutil
        import os
        
        filter = RateLimitFilter(rate=10, per=60)
        process = psutil.Process(os.getpid())
        
        # Get initial memory
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        # Process many unique messages
        for i in range(100000):
            record = log_record(msg=f"Unique message {i}")
            filter.filter(record)
            
            # Trigger cleanup periodically
            if i % 10000 == 0:
                filter._cleanup_old_entries()
        
        # Get final memory
        final_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        # Memory growth should be reasonable (< 50MB)
        memory_growth = final_memory - initial_memory
        assert memory_growth < 50
    
    @pytest.mark.performance
    def test_concurrent_performance(self, basic_rate_filter, log_record):
        """Test performance under concurrent load."""
        import concurrent.futures
        import time
        
        def process_messages(worker_id):
            for i in range(1000):
                record = log_record(msg=f"Worker {worker_id} message {i}")
                basic_rate_filter.filter(record)
        
        start_time = time.time()
        
        # Run with multiple threads
        with concurrent.futures.ThreadPoolExecutor(max_workers=10) as executor:
            futures = [executor.submit(process_messages, i) for i in range(10)]
            concurrent.futures.wait(futures)
        
        elapsed = time.time() - start_time
        
        # Should handle 10k messages from 10 threads in under 2 seconds
        assert elapsed < 2.0
```

### Шаг 8: Integration тесты

```python
class TestRateLimitIntegration:
    """Integration tests with logging system."""
    
    def test_multiple_filters_chain(self, log_record):
        """Test chaining multiple filters."""
        # Create a logger with multiple filters
        logger = logging.getLogger("test.chain")
        
        # Add filters
        rate_filter = RateLimitFilter(rate=10, per=60)
        duplicate_filter = DuplicateFilter(ttl=30)
        
        logger.addFilter(rate_filter)
        logger.addFilter(duplicate_filter)
        
        # Both filters should work
        handler = Mock()
        logger.addHandler(handler)
        logger.setLevel(logging.INFO)
        
        # First unique message passes both filters
        logger.info("Test message 1")
        assert handler.handle.called
        
        # Duplicate blocked by duplicate filter
        handler.reset_mock()
        logger.info("Test message 1")
        assert not handler.handle.called
        
        # Different message passes
        handler.reset_mock()
        logger.info("Test message 2")
        assert handler.handle.called
    
    def test_with_sentry_handler(self, log_record, caplog):
        """Test rate limiting with Sentry handler."""
        from unittest.mock import Mock
        
        # Mock Sentry handler
        sentry_handler = Mock()
        sentry_handler.level = logging.ERROR
        
        # Create logger with rate limit
        logger = logging.getLogger("test.sentry")
        logger.addHandler(sentry_handler)
        
        rate_filter = RateLimitFilter(rate=2, per=60)
        logger.addFilter(rate_filter)
        
        # First 2 errors go to Sentry
        logger.error("Error message")
        logger.error("Error message")
        assert sentry_handler.handle.call_count == 2
        
        # 3rd error blocked
        logger.error("Error message")
        assert sentry_handler.handle.call_count == 2  # Still 2
    
    def test_filter_order_matters(self, log_record):
        """Test that filter order affects behavior."""
        logger = logging.getLogger("test.order")
        handler = Mock()
        logger.addHandler(handler)
        logger.setLevel(logging.INFO)
        
        # Scenario 1: Rate limit then duplicate
        rate_filter = RateLimitFilter(rate=5, per=60)
        dup_filter = DuplicateFilter(ttl=60)
        
        logger.addFilter(rate_filter)
        logger.addFilter(dup_filter)
        
        # Send same message 10 times
        for i in range(10):
            logger.info("Same message")
        
        # Only 1 should get through (first passes rate, then dup blocks rest)
        assert handler.handle.call_count == 1
        
        # Clear filters
        logger.filters.clear()
        handler.reset_mock()
        
        # Scenario 2: Duplicate then rate limit (reverse order)
        logger.addFilter(dup_filter)
        logger.addFilter(rate_filter)
        
        # Send different messages
        for i in range(10):
            logger.info(f"Message {i}")
        
        # First 5 should get through (dup passes all, rate limits to 5)
        assert handler.handle.call_count == 5
```

## 🧪 Запуск всех тестов фильтров

### Создать общий запускающий файл:

```bash
# run_filter_tests.sh
#!/bin/bash

echo "Running Sensitive Data Filter tests..."
pytest tests/core/logging/test_sensitive_data_filter.py -v

echo "\nRunning Rate Limit Filter tests..."
pytest tests/core/logging/test_rate_limit_filter.py -v

echo "\nRunning all filter tests with coverage..."
pytest tests/core/logging/test_*filter*.py --cov=core.logging.filters --cov-report=html --cov-report=term-missing

echo "\nRunning performance tests..."
pytest tests/core/logging/ -v -m performance

echo "\nChecking for flaky tests (run 3 times)..."
pytest tests/core/logging/test_*filter*.py --count=3
```

### Tox конфигурация для тестирования:

```ini
# tox.ini
[tox]
envlist = py38,py39,py310,py311,coverage,performance

[testenv]
deps =
    pytest
    pytest-cov
    pytest-asyncio
    freezegun
    psutil
commands =
    pytest tests/core/logging/test_*filter*.py -v

[testenv:coverage]
commands =
    pytest tests/core/logging/test_*filter*.py --cov=core.logging.filters --cov-report=html --cov-report=term

[testenv:performance]
commands =
    pytest tests/core/logging/test_*filter*.py -v -m performance
```