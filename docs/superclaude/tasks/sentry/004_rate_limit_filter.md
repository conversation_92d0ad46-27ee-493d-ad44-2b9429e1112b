# Задача 004: Реализация фильтра ограничения частоты

## 📋 Описание задачи
Реализовать фильтр для предотвращения флуда одинаковых сообщений в логах. Фильтр должен ограничивать частоту идентичных логов, сохраняя информацию о количестве подавленных сообщений.

## 🎯 Цели
- Создать класс RateLimitFilter наследующий logging.Filter
- Реализовать механизм отслеживания дубликатов с временным окном
- Добавить агрегацию подавленных сообщений
- Обеспечить эффективное управление памятью
- Создать конфигурируемые стратегии ограничения

## ✅ Предварительные требования
- Выполнена задача 001 (создана структура папок)
- Понимание работы logging.Filter
- Знание структур данных для кэширования
- Понимание проблемы log flooding

## 🔧 Детальные шаги реализации

### Шаг 1: Дополнить imports в filters.py

```python
# Добавить к существующим импортам в core/logging/filters.py
import time
import hashlib
from collections import defaultdict, deque
from threading import Lock
from typing import NamedTuple, Deque
import weakref
```

### Шаг 2: Создать вспомогательные классы

```python
class LogMessage(NamedTuple):
    """Represents a unique log message for rate limiting."""
    logger_name: str
    level: int
    message: str
    
    @classmethod
    def from_record(cls, record: logging.LogRecord) -> 'LogMessage':
        """Create LogMessage from LogRecord."""
        # Получаем сообщение без форматирования времени
        try:
            message = record.getMessage()
        except Exception:
            message = str(record.msg)
            
        return cls(
            logger_name=record.name,
            level=record.levelno,
            message=message
        )
    
    def get_hash(self) -> str:
        """Get hash of the message for deduplication."""
        # Используем hash для быстрого сравнения
        content = f"{self.logger_name}:{self.level}:{self.message}"
        return hashlib.md5(content.encode('utf-8')).hexdigest()


class MessageStats:
    """Statistics for a rate-limited message."""
    
    def __init__(self):
        self.count = 0
        self.first_seen = time.time()
        self.last_seen = time.time()
        self.suppressed_count = 0
        
    def update(self, suppressed: bool = False):
        """Update statistics for this message."""
        self.last_seen = time.time()
        if suppressed:
            self.suppressed_count += 1
        else:
            self.count += 1
            
    def get_summary(self) -> str:
        """Get summary of suppressed messages."""
        if self.suppressed_count > 0:
            return (f" [Suppressed {self.suppressed_count} similar messages "
                   f"in the last {int(self.last_seen - self.first_seen)}s]")
        return ""
```

### Шаг 3: Реализовать основной класс RateLimitFilter

```python
class RateLimitFilter(logging.Filter):
    """
    Filter to limit rate of duplicate log messages.
    
    This filter:
    - Prevents log flooding from repeated messages
    - Aggregates suppressed message counts
    - Configurable time windows and limits
    - Memory-efficient with automatic cleanup
    """
    
    def __init__(self,
                 rate: int = 1,
                 per: float = 60.0,
                 burst: int = 5,
                 max_cache_size: int = 10000,
                 cleanup_interval: float = 300.0):
        """
        Initialize RateLimitFilter.
        
        Args:
            rate: Number of allowed messages
            per: Time period in seconds
            burst: Burst allowance for rapid messages
            max_cache_size: Maximum number of unique messages to track
            cleanup_interval: How often to clean old entries (seconds)
        """
        super().__init__()
        
        self.rate = rate
        self.per = per
        self.burst = burst
        self.max_cache_size = max_cache_size
        self.cleanup_interval = cleanup_interval
        
        # Хранилище для отслеживания сообщений
        self._message_times: Dict[str, Deque[float]] = defaultdict(
            lambda: deque(maxlen=burst)
        )
        self._message_stats: Dict[str, MessageStats] = {}
        
        # Для потокобезопасности
        self._lock = Lock()
        
        # Время последней очистки
        self._last_cleanup = time.time()
        
        # Кэш для производительности
        self._recent_hashes: Deque[str] = deque(maxlen=100)
    
    def filter(self, record: logging.LogRecord) -> bool:
        """
        Determine if the record should be logged.
        
        Args:
            record: Log record to check
            
        Returns:
            True if message should be logged, False to suppress
        """
        # Быстрая проверка для критических сообщений
        if record.levelno >= logging.ERROR:
            # Всегда пропускаем ошибки, но считаем их
            self._update_stats(record, suppressed=False)
            return True
            
        with self._lock:
            # Создаем идентификатор сообщения
            log_msg = LogMessage.from_record(record)
            msg_hash = log_msg.get_hash()
            
            # Быстрая проверка в недавних
            if msg_hash in self._recent_hashes:
                # Уже видели недавно, проверяем rate limit
                return self._check_rate_limit(msg_hash, record)
            
            # Добавляем в недавние
            self._recent_hashes.append(msg_hash)
            
            # Проверяем rate limit
            allowed = self._check_rate_limit(msg_hash, record)
            
            # Периодическая очистка
            self._maybe_cleanup()
            
            return allowed
    
    def _check_rate_limit(self, msg_hash: str, record: logging.LogRecord) -> bool:
        """
        Check if message passes rate limit.
        
        Args:
            msg_hash: Hash of the message
            record: Log record
            
        Returns:
            True if allowed, False if rate limited
        """
        current_time = time.time()
        
        # Получаем историю времен для этого сообщения
        time_deque = self._message_times[msg_hash]
        
        # Удаляем старые времена вне окна
        cutoff_time = current_time - self.per
        while time_deque and time_deque[0] < cutoff_time:
            time_deque.popleft()
        
        # Проверяем, можем ли логировать
        if len(time_deque) < self.rate:
            # Под лимитом, разрешаем
            time_deque.append(current_time)
            self._update_stats_for_hash(msg_hash, suppressed=False)
            
            # Добавляем summary если были подавленные
            self._add_suppression_summary(msg_hash, record)
            
            return True
        else:
            # Превышен лимит, подавляем
            self._update_stats_for_hash(msg_hash, suppressed=True)
            return False
    
    def _update_stats(self, record: logging.LogRecord, suppressed: bool):
        """Update statistics for a log record."""
        log_msg = LogMessage.from_record(record)
        msg_hash = log_msg.get_hash()
        self._update_stats_for_hash(msg_hash, suppressed)
    
    def _update_stats_for_hash(self, msg_hash: str, suppressed: bool):
        """Update statistics for a message hash."""
        if msg_hash not in self._message_stats:
            self._message_stats[msg_hash] = MessageStats()
        self._message_stats[msg_hash].update(suppressed)
    
    def _add_suppression_summary(self, msg_hash: str, record: logging.LogRecord):
        """Add suppression summary to log record if needed."""
        stats = self._message_stats.get(msg_hash)
        if stats and stats.suppressed_count > 0:
            # Добавляем информацию о подавленных сообщениях
            summary = stats.get_summary()
            record.msg = f"{record.msg}{summary}"
            # Сбрасываем счетчик
            stats.suppressed_count = 0
    
    def _maybe_cleanup(self):
        """Periodically clean up old entries to prevent memory growth."""
        current_time = time.time()
        
        if current_time - self._last_cleanup < self.cleanup_interval:
            return
            
        self._last_cleanup = current_time
        
        # Очищаем старые записи
        cutoff_time = current_time - self.per * 2
        
        # Очищаем времена сообщений
        hashes_to_remove = []
        for msg_hash, times in self._message_times.items():
            if not times or times[-1] < cutoff_time:
                hashes_to_remove.append(msg_hash)
                
        for msg_hash in hashes_to_remove:
            del self._message_times[msg_hash]
            if msg_hash in self._message_stats:
                del self._message_stats[msg_hash]
        
        # Ограничиваем размер кэша
        if len(self._message_times) > self.max_cache_size:
            # Удаляем самые старые
            items = sorted(
                self._message_times.items(),
                key=lambda x: x[1][-1] if x[1] else 0
            )
            
            for msg_hash, _ in items[:len(items) // 2]:
                del self._message_times[msg_hash]
                if msg_hash in self._message_stats:
                    del self._message_stats[msg_hash]
```

### Шаг 4: Создать расширенные версии фильтра

```python
class BurstRateLimitFilter(RateLimitFilter):
    """
    Rate limit filter with burst allowance.
    
    Allows burst of messages followed by steady rate.
    """
    
    def __init__(self,
                 steady_rate: int = 1,
                 burst_rate: int = 10,
                 per: float = 60.0,
                 **kwargs):
        """
        Initialize BurstRateLimitFilter.
        
        Args:
            steady_rate: Steady state rate
            burst_rate: Burst allowance
            per: Time period
            **kwargs: Additional arguments for parent
        """
        super().__init__(rate=steady_rate, per=per, burst=burst_rate, **kwargs)
        self.steady_rate = steady_rate
        self.burst_rate = burst_rate
        
        # Token bucket для burst control
        self._token_buckets: Dict[str, float] = {}
        self._last_refill: Dict[str, float] = {}
    
    def _check_rate_limit(self, msg_hash: str, record: logging.LogRecord) -> bool:
        """Check rate limit with token bucket algorithm."""
        current_time = time.time()
        
        # Инициализация bucket
        if msg_hash not in self._token_buckets:
            self._token_buckets[msg_hash] = float(self.burst_rate)
            self._last_refill[msg_hash] = current_time
        
        # Пополнение токенов
        time_passed = current_time - self._last_refill[msg_hash]
        tokens_to_add = time_passed * (self.steady_rate / self.per)
        
        self._token_buckets[msg_hash] = min(
            self.burst_rate,
            self._token_buckets[msg_hash] + tokens_to_add
        )
        self._last_refill[msg_hash] = current_time
        
        # Проверка доступности токена
        if self._token_buckets[msg_hash] >= 1.0:
            self._token_buckets[msg_hash] -= 1.0
            self._update_stats_for_hash(msg_hash, suppressed=False)
            self._add_suppression_summary(msg_hash, record)
            return True
        else:
            self._update_stats_for_hash(msg_hash, suppressed=True)
            return False


class SamplingRateLimitFilter(RateLimitFilter):
    """
    Rate limit filter that samples messages when limit is exceeded.
    
    Instead of completely suppressing, it samples messages.
    """
    
    def __init__(self,
                 rate: int = 10,
                 per: float = 60.0,
                 sample_rate: float = 0.1,
                 **kwargs):
        """
        Initialize SamplingRateLimitFilter.
        
        Args:
            rate: Number of allowed messages before sampling
            per: Time period
            sample_rate: Fraction of messages to allow when limited
            **kwargs: Additional arguments
        """
        super().__init__(rate=rate, per=per, **kwargs)
        self.sample_rate = sample_rate
        self._sample_counter: Dict[str, int] = defaultdict(int)
    
    def _check_rate_limit(self, msg_hash: str, record: logging.LogRecord) -> bool:
        """Check rate limit with sampling."""
        # Сначала проверяем обычный rate limit
        if super()._check_rate_limit(msg_hash, record):
            return True
        
        # Если превышен лимит, применяем sampling
        self._sample_counter[msg_hash] += 1
        
        # Пропускаем каждое N-ое сообщение
        sample_every = int(1.0 / self.sample_rate) if self.sample_rate > 0 else 0
        
        if sample_every > 0 and self._sample_counter[msg_hash] % sample_every == 0:
            # Добавляем пометку о sampling
            record.msg = f"[SAMPLED {sample_every}:1] {record.msg}"
            return True
            
        return False


class LoggerSpecificRateLimitFilter(RateLimitFilter):
    """
    Rate limit filter with per-logger configuration.
    
    Allows different rate limits for different loggers.
    """
    
    def __init__(self,
                 default_rate: int = 10,
                 default_per: float = 60.0,
                 logger_configs: Optional[Dict[str, Dict[str, Any]]] = None,
                 **kwargs):
        """
        Initialize LoggerSpecificRateLimitFilter.
        
        Args:
            default_rate: Default rate for unlisted loggers
            default_per: Default time period
            logger_configs: Per-logger configurations
            **kwargs: Additional arguments
            
        Example:
            logger_configs = {
                'django.request': {'rate': 5, 'per': 60},
                'celery.task': {'rate': 20, 'per': 60},
                'noisy.module': {'rate': 1, 'per': 300}
            }
        """
        super().__init__(rate=default_rate, per=default_per, **kwargs)
        self.default_rate = default_rate
        self.default_per = default_per
        self.logger_configs = logger_configs or {}
        
        # Кэш конфигураций для производительности
        self._config_cache: Dict[str, Tuple[int, float]] = {}
    
    def _get_logger_config(self, logger_name: str) -> Tuple[int, float]:
        """Get rate limit configuration for a logger."""
        if logger_name in self._config_cache:
            return self._config_cache[logger_name]
        
        # Проверяем точное совпадение
        if logger_name in self.logger_configs:
            config = self.logger_configs[logger_name]
            result = (config.get('rate', self.default_rate),
                     config.get('per', self.default_per))
        else:
            # Проверяем родительские логгеры
            parts = logger_name.split('.')
            for i in range(len(parts), 0, -1):
                parent_name = '.'.join(parts[:i])
                if parent_name in self.logger_configs:
                    config = self.logger_configs[parent_name]
                    result = (config.get('rate', self.default_rate),
                             config.get('per', self.default_per))
                    break
            else:
                result = (self.default_rate, self.default_per)
        
        self._config_cache[logger_name] = result
        return result
    
    def filter(self, record: logging.LogRecord) -> bool:
        """Apply logger-specific rate limiting."""
        # Временно меняем параметры для этого logger
        original_rate, original_per = self.rate, self.per
        self.rate, self.per = self._get_logger_config(record.name)
        
        try:
            return super().filter(record)
        finally:
            self.rate, self.per = original_rate, original_per
```

### Шаг 5: Добавить фабричную функцию

```python
def create_rate_limit_filter(
    filter_type: str = 'basic',
    **kwargs
) -> RateLimitFilter:
    """
    Factory function to create rate limit filters.
    
    Args:
        filter_type: Type of filter ('basic', 'burst', 'sampling', 'logger_specific')
        **kwargs: Configuration for the filter
        
    Returns:
        Configured rate limit filter
        
    Examples:
        # Basic rate limiting
        filter = create_rate_limit_filter('basic', rate=10, per=60)
        
        # Burst allowance
        filter = create_rate_limit_filter('burst', steady_rate=1, burst_rate=10)
        
        # Sampling when limited
        filter = create_rate_limit_filter('sampling', rate=10, sample_rate=0.1)
        
        # Per-logger config
        filter = create_rate_limit_filter(
            'logger_specific',
            logger_configs={
                'django': {'rate': 5, 'per': 60},
                'celery': {'rate': 20, 'per': 60}
            }
        )
    """
    filter_classes = {
        'basic': RateLimitFilter,
        'burst': BurstRateLimitFilter,
        'sampling': SamplingRateLimitFilter,
        'logger_specific': LoggerSpecificRateLimitFilter
    }
    
    filter_class = filter_classes.get(filter_type, RateLimitFilter)
    return filter_class(**kwargs)
```

## 🧪 Тестирование реализации

### Создать тестовый файл tests/core/test_rate_limit_filter.py:

```python
import pytest
import logging
import time
from unittest.mock import patch
from core.logging.filters import (
    RateLimitFilter,
    BurstRateLimitFilter,
    SamplingRateLimitFilter,
    create_rate_limit_filter
)

class TestRateLimitFilter:
    """Test cases for rate limit filters."""
    
    def test_basic_rate_limiting(self):
        """Test basic rate limiting functionality."""
        # 2 сообщения за 1 секунду
        filter = RateLimitFilter(rate=2, per=1.0)
        
        # Создаем тестовые записи
        record = logging.LogRecord(
            name="test", level=logging.INFO,
            pathname="", lineno=0,
            msg="Test message", args=(), exc_info=None
        )
        
        # Первые 2 должны пройти
        assert filter.filter(record) is True
        assert filter.filter(record) is True
        
        # Третье должно быть заблокировано
        assert filter.filter(record) is False
        
        # После паузы снова должно пройти
        time.sleep(1.1)
        assert filter.filter(record) is True
    
    def test_different_messages(self):
        """Test that different messages have separate limits."""
        filter = RateLimitFilter(rate=1, per=60)
        
        record1 = logging.LogRecord(
            name="test", level=logging.INFO,
            pathname="", lineno=0,
            msg="Message 1", args=(), exc_info=None
        )
        
        record2 = logging.LogRecord(
            name="test", level=logging.INFO,
            pathname="", lineno=0,
            msg="Message 2", args=(), exc_info=None
        )
        
        # Оба разных сообщения должны пройти
        assert filter.filter(record1) is True
        assert filter.filter(record2) is True
        
        # Повторы должны быть заблокированы
        assert filter.filter(record1) is False
        assert filter.filter(record2) is False
    
    def test_error_messages_always_pass(self):
        """Test that ERROR level messages always pass."""
        filter = RateLimitFilter(rate=1, per=60)
        
        error_record = logging.LogRecord(
            name="test", level=logging.ERROR,
            pathname="", lineno=0,
            msg="Error message", args=(), exc_info=None
        )
        
        # Все ошибки должны проходить
        for _ in range(5):
            assert filter.filter(error_record) is True
    
    def test_suppression_summary(self):
        """Test that suppression summary is added."""
        filter = RateLimitFilter(rate=1, per=60)
        
        record = logging.LogRecord(
            name="test", level=logging.INFO,
            pathname="", lineno=0,
            msg="Test message", args=(), exc_info=None
        )
        
        # Первое проходит
        assert filter.filter(record) is True
        
        # Следующие 5 блокируются
        for _ in range(5):
            assert filter.filter(record) is False
        
        # Ждем и отправляем еще одно
        time.sleep(0.1)  # Небольшая пауза
        
        # Создаем новую запись для следующего разрешенного сообщения
        record2 = logging.LogRecord(
            name="test", level=logging.INFO,
            pathname="", lineno=0,
            msg="Test message", args=(), exc_info=None
        )
        
        # При следующем разрешенном должен быть summary
        with patch.object(filter, '_add_suppression_summary') as mock_summary:
            filter.filter(record2)
            mock_summary.assert_called()
    
    def test_burst_filter(self):
        """Test burst rate limit filter."""
        filter = BurstRateLimitFilter(
            steady_rate=1,
            burst_rate=5,
            per=1.0
        )
        
        record = logging.LogRecord(
            name="test", level=logging.INFO,
            pathname="", lineno=0,
            msg="Test message", args=(), exc_info=None
        )
        
        # Burst из 5 сообщений должен пройти
        for i in range(5):
            assert filter.filter(record) is True, f"Burst message {i+1} failed"
        
        # 6-е должно быть заблокировано
        assert filter.filter(record) is False
        
        # После паузы должен восстановиться 1 токен
        time.sleep(1.1)
        assert filter.filter(record) is True
        assert filter.filter(record) is False
    
    def test_sampling_filter(self):
        """Test sampling rate limit filter."""
        filter = SamplingRateLimitFilter(
            rate=2,
            per=1.0,
            sample_rate=0.5  # 50% sampling
        )
        
        record = logging.LogRecord(
            name="test", level=logging.INFO,
            pathname="", lineno=0,
            msg="Test message", args=(), exc_info=None
        )
        
        # Первые 2 проходят нормально
        assert filter.filter(record) is True
        assert filter.filter(record) is True
        
        # Следующие должны сэмплироваться
        passed = 0
        for _ in range(10):
            if filter.filter(record):
                passed += 1
        
        # Примерно 50% должны пройти (±2 для статистической погрешности)
        assert 3 <= passed <= 7
    
    def test_memory_cleanup(self):
        """Test automatic memory cleanup."""
        filter = RateLimitFilter(
            rate=1,
            per=1.0,
            max_cache_size=10,
            cleanup_interval=0.1
        )
        
        # Создаем много разных сообщений
        for i in range(20):
            record = logging.LogRecord(
                name="test", level=logging.INFO,
                pathname="", lineno=0,
                msg=f"Message {i}", args=(), exc_info=None
            )
            filter.filter(record)
        
        # Ждем cleanup
        time.sleep(0.2)
        
        # Еще одно сообщение для триггера cleanup
        record = logging.LogRecord(
            name="test", level=logging.INFO,
            pathname="", lineno=0,
            msg="Trigger cleanup", args=(), exc_info=None
        )
        filter.filter(record)
        
        # Проверяем, что размер кэша ограничен
        assert len(filter._message_times) <= filter.max_cache_size
```

## ⚠️ Потенциальные проблемы

### Проблема 1: Утечка памяти при большом количестве уникальных сообщений
**Симптом**: Рост потребления памяти
**Решение**: Регулярная очистка и ограничение max_cache_size

### Проблема 2: Потеря важных сообщений
**Симптом**: Критические логи не появляются
**Решение**: ERROR и выше всегда проходят, использовать SamplingFilter

### Проблема 3: Неточность при высокой нагрузке
**Симптом**: Пропускается больше сообщений чем настроено
**Решение**: Использовать Lock для синхронизации

### Проблема 4: Сложность отладки с rate limiting
**Симптом**: Не видно всех логов при отладке
**Решение**: Добавить debug режим или условное отключение

## 📊 Критерии успешного завершения
- ✅ Реализован базовый RateLimitFilter
- ✅ Работает отслеживание дубликатов по времени
- ✅ Добавлена информация о подавленных сообщениях
- ✅ Реализованы BurstRateLimitFilter и SamplingRateLimitFilter
- ✅ Создан LoggerSpecificRateLimitFilter
- ✅ Автоматическая очистка памяти
- ✅ Написаны тесты
- ✅ Потокобезопасная реализация

## ⏱️ Оценка времени
**1.5 часа** - полная реализация с тестами

## 🔗 Связанные задачи
- **Предыдущая**: 003_sensitive_data_filter.md
- **Следующая**: 005_context_logger_enhancement.md
- **Используется в**: 009_logging_configuration.md

## 📝 Дополнительные заметки
- Rate limiting не должен применяться к ERROR и CRITICAL сообщениям
- Важно добавлять summary о подавленных сообщениях
- Рассмотреть интеграцию с метриками для мониторинга подавления
- В production рекомендуется использовать BurstRateLimitFilter для баланса