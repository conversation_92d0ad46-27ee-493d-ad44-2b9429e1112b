# Задача 013: Тесты Celery логирования

## 📋 Описание задачи
Написать специализированные тесты для интеграции системы логирования с Celery. Тесты должны покрывать контекст задач, обработку ошибок, производительность и корректную передачу контекста между процессами.

## 🎯 Цели
- Протестировать CeleryContextPlugin
- Проверить передачу контекста между процессами
- Валидировать логирование в задачах
- Протестировать обработку ошибок в задачах
- Проверить производительность с Celery
- Убедиться в корректной очистке контекста

## ✅ Предварительные требования
- Выполнены задачи 001-008
- Установлен celery[pytest]
- Установлен redis (для тестового брокера)
- Понимание работы Celery с Django
- Настроен CeleryContextPlugin

## 🔧 Детальные шаги реализации

### Шаг 1: Создать конфигурацию для тестов Celery

```python
# tests/core/logging/test_celery_logging.py
import pytest
import logging
import json
import time
from typing import Dict, List, Any, Optional
from unittest.mock import Mock, patch, MagicMock
from celery import Celery, Task, current_task
from celery.result import AsyncResult
from celery.signals import (
    task_prerun,
    task_postrun,
    task_failure,
    task_retry,
    task_success
)
import sentry_sdk
from kombu import Queue

from core.logging import (
    ContextLogger,
    set_context,
    clear_context,
    get_context,
    add_context
)
from core.logging.celery_plugin import CeleryContextPlugin
from core.logging.handlers import SentryHandler


# Test Celery app configuration
@pytest.fixture(scope='module')
def celery_app():
    """Create test Celery app."""
    app = Celery('test_app')
    
    # Configure for testing
    app.conf.update(
        broker_url='memory://',
        result_backend='cache+memory://',
        task_serializer='json',
        accept_content=['json'],
        result_serializer='json',
        timezone='UTC',
        enable_utc=True,
        
        # Testing specific
        task_always_eager=False,  # We want real async behavior
        task_eager_propagates=True,
        task_store_eager_result=True,
        
        # Queues
        task_routes={
            'tests.tasks.*': {'queue': 'test'},
        },
        task_queues=(
            Queue('test', routing_key='test'),
            Queue('celery', routing_key='celery'),
        ),
    )
    
    # Initialize context plugin
    context_plugin = CeleryContextPlugin()
    context_plugin.install(app)
    
    return app


@pytest.fixture
def celery_worker(celery_app):
    """Start Celery worker for tests."""
    from celery.contrib.testing.worker import start_worker
    
    with start_worker(celery_app, perform_ping_check=False) as worker:
        yield worker


@pytest.fixture
def task_context():
    """Mock task context."""
    return {
        'request_id': 'req-123',
        'user_id': 42,
        'correlation_id': 'corr-456',
        'source': 'web',
        'api_version': 'v2'
    }


@pytest.fixture
def capture_logs():
    """Capture log records for testing."""
    records = []
    
    class TestHandler(logging.Handler):
        def emit(self, record):
            records.append(record)
    
    handler = TestHandler()
    handler.setLevel(logging.DEBUG)
    
    # Add to root logger
    logger = logging.getLogger()
    logger.addHandler(handler)
    
    yield records
    
    # Cleanup
    logger.removeHandler(handler)


@pytest.fixture
def mock_sentry():
    """Mock Sentry for capturing events."""
    events = []
    breadcrumbs = []
    
    with patch('sentry_sdk.capture_event') as mock_capture:
        mock_capture.side_effect = lambda event: events.append(event)
        
        with patch('sentry_sdk.add_breadcrumb') as mock_breadcrumb:
            mock_breadcrumb.side_effect = lambda crumb: breadcrumbs.append(crumb)
            
            yield {
                'events': events,
                'breadcrumbs': breadcrumbs,
                'capture_event': mock_capture,
                'add_breadcrumb': mock_breadcrumb
            }
```

### Шаг 2: Тесты базовой функциональности

```python
class TestCeleryBasicLogging:
    """Test basic Celery logging functionality."""
    
    def test_task_context_setup(self, celery_app, task_context):
        """Test that task context is properly set up."""
        @celery_app.task
        def test_task():
            logger = ContextLogger.get_logger(__name__)
            logger.info("Task executing")
            
            # Get current context
            ctx = get_context()
            
            # Should have task context
            assert 'task_id' in ctx
            assert 'task_name' in ctx
            assert ctx['task_name'] == 'test_task'
            
            return ctx
        
        # Set initial context
        set_context(**task_context)
        
        # Execute task
        result = test_task.apply_async()
        task_ctx = result.get(timeout=5)
        
        # Verify context was passed
        assert task_ctx['request_id'] == 'req-123'
        assert task_ctx['user_id'] == 42
        assert task_ctx['correlation_id'] == 'corr-456'
    
    def test_task_logging_with_context(self, celery_app, capture_logs):
        """Test logging within task includes context."""
        @celery_app.task
        def logging_task(message):
            logger = ContextLogger.get_logger(__name__)
            
            # Add task-specific context
            add_context(
                processing_message=message,
                task_start_time=time.time()
            )
            
            logger.info(f"Processing: {message}")
            logger.debug("Debug information", extra={'debug_data': 'test'})
            
            return "completed"
        
        # Execute task
        set_context(request_id='req-789')
        result = logging_task.apply_async(args=["test message"])
        result.get(timeout=5)
        
        # Check captured logs
        info_logs = [r for r in capture_logs if r.levelname == 'INFO']
        assert len(info_logs) > 0
        
        # Check context in logs
        for record in info_logs:
            if hasattr(record, 'request_id'):
                assert record.request_id == 'req-789'
            if hasattr(record, 'task_name'):
                assert record.task_name == 'logging_task'
    
    def test_task_exception_logging(self, celery_app, mock_sentry):
        """Test exception logging in tasks."""
        @celery_app.task
        def failing_task(fail_type='zero_division'):
            logger = ContextLogger.get_logger(__name__)
            logger.info("Task started")
            
            if fail_type == 'zero_division':
                return 1 / 0
            elif fail_type == 'value_error':
                raise ValueError("Test error")
            else:
                raise Exception("Unknown error")
        
        # Execute task
        set_context(user_id=123, request_id='req-error')
        result = failing_task.apply_async(args=['value_error'])
        
        with pytest.raises(ValueError):
            result.get(timeout=5)
        
        # Check Sentry captured exception with context
        assert len(mock_sentry['events']) > 0
        event = mock_sentry['events'][0]
        
        # Should have exception
        assert 'exception' in event
        assert event['exception']['values'][0]['type'] == 'ValueError'
        
        # Should have context
        assert event['contexts']['task']['task_name'] == 'failing_task'
        assert event['user']['id'] == '123'
```

### Шаг 3: Тесты передачи контекста между задачами

```python
class TestCeleryContextPropagation:
    """Test context propagation between tasks."""
    
    def test_chain_context_propagation(self, celery_app):
        """Test context propagation in task chains."""
        @celery_app.task
        def first_task(value):
            logger = ContextLogger.get_logger(__name__)
            logger.info(f"First task: {value}")
            
            # Add to context
            add_context(first_task_value=value)
            
            return value * 2
        
        @celery_app.task
        def second_task(value):
            logger = ContextLogger.get_logger(__name__)
            logger.info(f"Second task: {value}")
            
            # Check context from first task
            ctx = get_context()
            assert 'first_task_value' in ctx
            
            # Add to context
            add_context(second_task_value=value)
            
            return value * 2
        
        @celery_app.task
        def final_task(value):
            logger = ContextLogger.get_logger(__name__)
            logger.info(f"Final task: {value}")
            
            # Check complete context
            ctx = get_context()
            assert 'first_task_value' in ctx
            assert 'second_task_value' in ctx
            assert 'request_id' in ctx
            
            return ctx
        
        # Create chain
        from celery import chain
        
        set_context(request_id='chain-123', workflow='test_chain')
        
        workflow = chain(
            first_task.s(5),
            second_task.s(),
            final_task.s()
        )
        
        result = workflow.apply_async()
        final_context = result.get(timeout=10)
        
        # Verify context propagated through chain
        assert final_context['request_id'] == 'chain-123'
        assert final_context['workflow'] == 'test_chain'
        assert final_context['first_task_value'] == 5
        assert final_context['second_task_value'] == 10
    
    def test_group_context_isolation(self, celery_app):
        """Test context isolation in task groups."""
        results = []
        
        @celery_app.task
        def group_task(task_num):
            logger = ContextLogger.get_logger(__name__)
            
            # Each task should have its own context
            add_context(task_number=task_num)
            
            logger.info(f"Group task {task_num}")
            
            # Simulate work
            time.sleep(0.1)
            
            # Return context
            return get_context()
        
        # Create group
        from celery import group
        
        set_context(request_id='group-456', group_id='test_group')
        
        job = group(
            group_task.s(i) for i in range(5)
        )
        
        result = job.apply_async()
        contexts = result.get(timeout=10)
        
        # Check each task had isolated context
        task_numbers = [ctx['task_number'] for ctx in contexts]
        assert sorted(task_numbers) == list(range(5))
        
        # But shared context should be same
        for ctx in contexts:
            assert ctx['request_id'] == 'group-456'
            assert ctx['group_id'] == 'test_group'
    
    def test_chord_context_flow(self, celery_app):
        """Test context in chord (group + callback)."""
        @celery_app.task
        def process_item(item):
            logger = ContextLogger.get_logger(__name__)
            logger.info(f"Processing item: {item}")
            
            # Process and return result with context
            return {
                'item': item,
                'processed': item.upper(),
                'context': get_context()
            }
        
        @celery_app.task
        def summarize(results):
            logger = ContextLogger.get_logger(__name__)
            logger.info(f"Summarizing {len(results)} results")
            
            # Check all results have context
            for result in results:
                assert 'context' in result
                assert result['context']['request_id'] == 'chord-789'
            
            return {
                'total_items': len(results),
                'items': [r['processed'] for r in results],
                'final_context': get_context()
            }
        
        # Create chord
        from celery import chord
        
        set_context(request_id='chord-789', operation='batch_process')
        
        items = ['apple', 'banana', 'cherry']
        
        workflow = chord(
            (process_item.s(item) for item in items),
            summarize.s()
        )
        
        result = workflow.apply_async()
        summary = result.get(timeout=10)
        
        # Verify context flowed through chord
        assert summary['total_items'] == 3
        assert summary['final_context']['request_id'] == 'chord-789'
        assert summary['final_context']['operation'] == 'batch_process'
```

### Шаг 4: Тесты обработки ошибок и retry

```python
class TestCeleryErrorHandling:
    """Test error handling and retry with logging."""
    
    def test_task_retry_logging(self, celery_app, capture_logs):
        """Test logging during task retries."""
        retry_count = {'count': 0}
        
        @celery_app.task(bind=True, max_retries=3)
        def retry_task(self, should_fail=True):
            logger = ContextLogger.get_logger(__name__)
            
            retry_count['count'] += 1
            current_retry = self.request.retries
            
            logger.info(
                f"Task attempt {current_retry + 1}",
                retry_count=current_retry,
                max_retries=self.max_retries
            )
            
            if should_fail and current_retry < 2:
                logger.warning("Task failing, will retry")
                raise self.retry(
                    exc=Exception("Temporary failure"),
                    countdown=0  # Immediate retry for testing
                )
            
            logger.info("Task succeeded")
            return {
                'success': True,
                'attempts': retry_count['count'],
                'context': get_context()
            }
        
        # Execute with retries
        set_context(request_id='retry-123', operation='test_retry')
        result = retry_task.apply_async()
        
        outcome = result.get(timeout=10)
        
        # Check result
        assert outcome['success'] is True
        assert outcome['attempts'] == 3
        assert outcome['context']['request_id'] == 'retry-123'
        
        # Check logs
        retry_logs = [
            r for r in capture_logs 
            if 'retry' in str(r.getMessage()).lower()
        ]
        assert len(retry_logs) >= 2  # At least 2 retry warnings
    
    def test_task_failure_logging(self, celery_app, mock_sentry):
        """Test logging on permanent task failure."""
        @celery_app.task(bind=True, max_retries=1)
        def permanent_failure_task(self):
            logger = ContextLogger.get_logger(__name__)
            logger.info("Task starting")
            
            # Always fail
            if self.request.retries < self.max_retries:
                raise self.retry(exc=Exception("Still failing"))
            else:
                logger.error("Task failed permanently")
                raise Exception("Permanent failure")
        
        # Execute
        set_context(
            request_id='fail-456',
            user_id=789,
            critical_operation=True
        )
        
        result = permanent_failure_task.apply_async()
        
        with pytest.raises(Exception) as exc_info:
            result.get(timeout=10)
        
        assert "Permanent failure" in str(exc_info.value)
        
        # Check Sentry events
        error_events = [
            e for e in mock_sentry['events']
            if e.get('level') == 'error'
        ]
        
        assert len(error_events) > 0
        
        # Check context in error
        error_event = error_events[-1]
        assert error_event['contexts']['task']['retries'] == 1
        assert error_event['extra']['critical_operation'] is True
    
    def test_task_timeout_logging(self, celery_app, capture_logs):
        """Test logging when task times out."""
        @celery_app.task(
            time_limit=1,  # Hard timeout
            soft_time_limit=0.5  # Soft timeout
        )
        def timeout_task():
            logger = ContextLogger.get_logger(__name__)
            logger.info("Long running task started")
            
            try:
                # Simulate long work
                time.sleep(2)
            except Exception as e:
                logger.exception(f"Task interrupted: {e}")
                raise
            
            return "Should not reach here"
        
        # Execute
        set_context(request_id='timeout-789')
        result = timeout_task.apply_async()
        
        # Should timeout
        from celery.exceptions import TimeLimitExceeded
        with pytest.raises(TimeLimitExceeded):
            result.get(timeout=5)
        
        # Check timeout was logged
        timeout_logs = [
            r for r in capture_logs
            if 'timeout' in str(r.getMessage()).lower() or
               'interrupted' in str(r.getMessage()).lower()
        ]
        assert len(timeout_logs) > 0
```

### Шаг 5: Тесты производительности с Celery

```python
class TestCeleryPerformance:
    """Test performance with Celery logging."""
    
    def test_high_volume_task_logging(self, celery_app):
        """Test performance with many concurrent tasks."""
        task_count = 100
        messages_per_task = 10
        
        @celery_app.task
        def volume_task(task_index):
            logger = ContextLogger.get_logger(__name__)
            
            for i in range(messages_per_task):
                logger.info(
                    f"Task {task_index} message {i}",
                    task_index=task_index,
                    message_index=i
                )
            
            return task_index
        
        # Start timing
        start_time = time.time()
        
        # Launch tasks
        set_context(request_id='volume-test', batch_size=task_count)
        
        results = []
        for i in range(task_count):
            result = volume_task.apply_async(args=[i])
            results.append(result)
        
        # Wait for completion
        completed = [r.get(timeout=30) for r in results]
        
        elapsed = time.time() - start_time
        
        # Check performance
        assert len(completed) == task_count
        assert elapsed < 30  # Should complete in reasonable time
        
        # Calculate throughput
        total_messages = task_count * messages_per_task
        throughput = total_messages / elapsed
        
        # Should handle at least 100 messages/second
        assert throughput > 100
    
    def test_context_overhead(self, celery_app):
        """Test overhead of context propagation."""
        
        @celery_app.task
        def minimal_task(value):
            return value * 2
        
        @celery_app.task
        def context_task(value):
            logger = ContextLogger.get_logger(__name__)
            logger.debug("Processing", value=value)
            
            # Add context
            add_context(
                processed_value=value,
                timestamp=time.time()
            )
            
            return value * 2
        
        # Measure minimal task
        iterations = 100
        
        # Without context
        start = time.time()
        for i in range(iterations):
            result = minimal_task.apply_async(args=[i])
            result.get()
        minimal_time = time.time() - start
        
        # With context
        set_context(
            request_id='perf-test',
            user_id=123,
            session_id='sess-456',
            extra_data={'key': 'value'} 
        )
        
        start = time.time()
        for i in range(iterations):
            result = context_task.apply_async(args=[i])
            result.get()
        context_time = time.time() - start
        
        # Calculate overhead
        overhead_percent = ((context_time - minimal_time) / minimal_time) * 100
        
        # Overhead should be reasonable (< 50%)
        assert overhead_percent < 50
    
    def test_memory_usage_in_workers(self, celery_app):
        """Test memory usage doesn't grow excessively."""
        import psutil
        import os
        
        @celery_app.task
        def memory_task(index):
            logger = ContextLogger.get_logger(__name__)
            
            # Log with context
            logger.info(
                f"Processing item {index}",
                large_data='x' * 1000,  # 1KB of data
                index=index
            )
            
            # Add more context
            add_context(
                task_data={'index': index, 'data': 'y' * 100}
            )
            
            return index
        
        # Get initial memory
        process = psutil.Process(os.getpid())
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        # Run many tasks
        set_context(request_id='memory-test')
        
        results = []
        for i in range(500):
            result = memory_task.apply_async(args=[i])
            results.append(result)
            
            # Wait for some to complete to avoid queue buildup
            if i % 50 == 0:
                for r in results[-50:]:
                    r.get(timeout=5)
        
        # Wait for all
        for r in results:
            r.get(timeout=5)
        
        # Check final memory
        final_memory = process.memory_info().rss / 1024 / 1024  # MB
        memory_increase = final_memory - initial_memory
        
        # Memory increase should be reasonable (< 100MB)
        assert memory_increase < 100
```

### Шаг 6: Тесты сигналов Celery

```python
class TestCelerySignals:
    """Test Celery signals integration with logging."""
    
    def test_task_signals_logging(self, celery_app, capture_logs):
        """Test that task signals trigger appropriate logging."""
        signal_events = []
        
        @task_prerun.connect
        def capture_prerun(sender=None, task_id=None, task=None, **kwargs):
            signal_events.append(('prerun', task.name, get_context()))
        
        @task_postrun.connect
        def capture_postrun(sender=None, task_id=None, task=None, **kwargs):
            signal_events.append(('postrun', task.name, get_context()))
        
        @task_success.connect
        def capture_success(sender=None, result=None, **kwargs):
            signal_events.append(('success', sender.name, get_context()))
        
        @celery_app.task
        def signal_task(value):
            logger = ContextLogger.get_logger(__name__)
            logger.info(f"Task processing: {value}")
            return value * 2
        
        try:
            # Execute task
            set_context(request_id='signal-123')
            result = signal_task.apply_async(args=[5])
            result.get(timeout=5)
            
            # Check signals fired with context
            assert len(signal_events) >= 3
            
            # Check prerun
            prerun = next(e for e in signal_events if e[0] == 'prerun')
            assert prerun[2]['request_id'] == 'signal-123'
            
            # Check postrun
            postrun = next(e for e in signal_events if e[0] == 'postrun')
            assert postrun[2]['request_id'] == 'signal-123'
            
        finally:
            # Cleanup signal handlers
            task_prerun.disconnect(capture_prerun)
            task_postrun.disconnect(capture_postrun)
            task_success.disconnect(capture_success)
    
    def test_failure_signal_context(self, celery_app, mock_sentry):
        """Test context in failure signals."""
        failure_context = None
        
        @task_failure.connect
        def capture_failure(sender=None, task_id=None, exception=None, **kwargs):
            nonlocal failure_context
            failure_context = get_context().copy()
        
        @celery_app.task
        def failing_signal_task():
            logger = ContextLogger.get_logger(__name__)
            logger.info("About to fail")
            raise ValueError("Intentional failure")
        
        try:
            # Execute
            set_context(
                request_id='fail-signal-456',
                user_id=999,
                critical=True
            )
            
            result = failing_signal_task.apply_async()
            
            with pytest.raises(ValueError):
                result.get(timeout=5)
            
            # Check failure signal had context
            assert failure_context is not None
            assert failure_context['request_id'] == 'fail-signal-456'
            assert failure_context['user_id'] == 999
            assert failure_context['critical'] is True
            
            # Check Sentry event
            assert len(mock_sentry['events']) > 0
            
        finally:
            task_failure.disconnect(capture_failure)
```

### Шаг 7: Тесты с реальными сценариями

```python
class TestCeleryRealScenarios:
    """Test real-world Celery scenarios."""
    
    def test_email_notification_workflow(self, celery_app):
        """Test email notification workflow with logging."""
        
        @celery_app.task
        def fetch_user_data(user_id):
            logger = ContextLogger.get_logger(__name__)
            logger.info(f"Fetching user data for {user_id}")
            
            # Simulate data fetch
            user_data = {
                'id': user_id,
                'email': f'user{user_id}@example.com',
                'name': f'User {user_id}'
            }
            
            add_context(user_email=user_data['email'])
            return user_data
        
        @celery_app.task
        def render_email_template(user_data, template_name):
            logger = ContextLogger.get_logger(__name__)
            logger.info(
                f"Rendering template {template_name}",
                user_id=user_data['id']
            )
            
            # Simulate rendering
            return {
                'subject': f"Hello {user_data['name']}",
                'body': f"Email for {user_data['email']}",
                'template': template_name
            }
        
        @celery_app.task
        def send_email(email_data, user_data):
            logger = ContextLogger.get_logger(__name__)
            logger.info(
                "Sending email",
                to=user_data['email'],
                subject=email_data['subject']
            )
            
            # Simulate send
            return {
                'sent': True,
                'timestamp': time.time(),
                'context': get_context()
            }
        
        # Create workflow
        from celery import chain
        
        set_context(
            request_id='email-workflow-789',
            workflow_type='user_notification',
            initiated_by='system'
        )
        
        workflow = chain(
            fetch_user_data.s(123),
            render_email_template.s('welcome.html'),
            send_email.s()
        )
        
        result = workflow.apply_async()
        email_result = result.get(timeout=10)
        
        # Verify workflow completed with context
        assert email_result['sent'] is True
        assert email_result['context']['request_id'] == 'email-workflow-789'
        assert email_result['context']['user_email'] == '<EMAIL>'
    
    def test_data_processing_pipeline(self, celery_app):
        """Test data processing pipeline with logging."""
        
        @celery_app.task
        def validate_data(data):
            logger = ContextLogger.get_logger(__name__)
            logger.info("Validating data", data_size=len(data))
            
            if not data:
                raise ValueError("Empty data")
            
            add_context(data_validated=True)
            return data
        
        @celery_app.task
        def transform_data(data):
            logger = ContextLogger.get_logger(__name__)
            logger.info("Transforming data")
            
            transformed = [
                {'original': item, 'processed': item.upper()}
                for item in data
            ]
            
            add_context(items_transformed=len(transformed))
            return transformed
        
        @celery_app.task
        def save_results(results):
            logger = ContextLogger.get_logger(__name__)
            logger.info("Saving results", count=len(results))
            
            # Simulate save
            save_id = f"save_{int(time.time())}"
            
            return {
                'save_id': save_id,
                'count': len(results),
                'context': get_context()
            }
        
        # Run pipeline
        set_context(
            request_id='pipeline-101',
            pipeline_name='data_processing',
            source='api'
        )
        
        from celery import chain
        
        test_data = ['apple', 'banana', 'cherry']
        
        pipeline = chain(
            validate_data.s(test_data),
            transform_data.s(),
            save_results.s()
        )
        
        result = pipeline.apply_async()
        save_result = result.get(timeout=10)
        
        # Verify pipeline context
        ctx = save_result['context']
        assert ctx['request_id'] == 'pipeline-101'
        assert ctx['data_validated'] is True
        assert ctx['items_transformed'] == 3
        assert 'save_id' in save_result
```

### Шаг 8: Вспомогательные функции для тестирования

```python
# tests/core/logging/celery_test_utils.py
"""Utilities for testing Celery logging integration."""

from contextlib import contextmanager
from typing import Dict, Any, List
import time


class CeleryTestContext:
    """Helper for managing test context in Celery tests."""
    
    def __init__(self):
        self.contexts_captured = []
        self.tasks_executed = []
        
    def capture_task_context(self, task_name: str):
        """Capture context when task executes."""
        from core.logging import get_context
        
        ctx = get_context().copy()
        ctx['task_name'] = task_name
        ctx['captured_at'] = time.time()
        
        self.contexts_captured.append(ctx)
        self.tasks_executed.append(task_name)
        
        return ctx
    
    def get_task_contexts(self, task_name: str) -> List[Dict[str, Any]]:
        """Get all contexts for a specific task."""
        return [
            ctx for ctx in self.contexts_captured
            if ctx.get('task_name') == task_name
        ]
    
    def assert_context_propagated(self, expected_keys: List[str]):
        """Assert expected keys exist in all captured contexts."""
        for ctx in self.contexts_captured:
            for key in expected_keys:
                assert key in ctx, f"Key '{key}' not found in context"


@contextmanager
def celery_test_mode(app):
    """Context manager for Celery test mode."""
    original_always_eager = app.conf.task_always_eager
    original_eager_propagates = app.conf.task_eager_propagates
    
    try:
        # Enable test mode
        app.conf.task_always_eager = True
        app.conf.task_eager_propagates = True
        
        yield app
        
    finally:
        # Restore original settings
        app.conf.task_always_eager = original_always_eager
        app.conf.task_eager_propagates = original_eager_propagates


def create_test_task(app, name: str = None):
    """Factory for creating test tasks."""
    def task_decorator(func):
        task = app.task(name=name or func.__name__)
        return task(func)
    
    return task_decorator


class MockCeleryBackend:
    """Mock backend for testing result storage."""
    
    def __init__(self):
        self.results = {}
        
    def store_result(self, task_id: str, result: Any, state: str):
        """Store task result."""
        self.results[task_id] = {
            'result': result,
            'state': state,
            'timestamp': time.time()
        }
    
    def get_result(self, task_id: str) -> Any:
        """Get task result."""
        if task_id in self.results:
            return self.results[task_id]['result']
        return None
    
    def get_state(self, task_id: str) -> str:
        """Get task state."""
        if task_id in self.results:
            return self.results[task_id]['state']
        return 'PENDING'
```

## 🧪 Тестирование

### Команды для запуска тестов:

```bash
# Запустить все Celery тесты
uv run pytest tests/core/logging/test_celery_logging.py -v

# Запустить с реальным Celery worker
uv run pytest tests/core/logging/test_celery_logging.py -v --celery-worker

# Запустить только базовые тесты
uv run pytest tests/core/logging/test_celery_logging.py::TestCeleryBasicLogging -v

# Запустить с отладкой
uv run pytest tests/core/logging/test_celery_logging.py -v -s --log-cli-level=DEBUG

# Запустить тесты производительности
uv run pytest tests/core/logging/test_celery_logging.py::TestCeleryPerformance -v

# С покрытием
uv run pytest tests/core/logging/test_celery_logging.py --cov=core.logging.celery_plugin
```

## ⚠️ Потенциальные проблемы

### Проблема 1: Celery worker не запускается в тестах
**Симптом**: Timeout при выполнении задач
**Решение**: Использовать celery.contrib.testing.worker

### Проблема 2: Контекст не передается между задачами
**Симптом**: Потеря контекста в chain/group
**Решение**: Проверить сериализацию контекста

### Проблема 3: Race conditions в тестах
**Симптом**: Нестабильные результаты
**Решение**: Добавить явные ожидания и синхронизацию

### Проблема 4: Утечки памяти при большом количестве задач
**Симптом**: Рост памяти в тестах
**Решение**: Очищать результаты и контексты

## 📊 Критерии успешного завершения
- ✅ Протестирована базовая интеграция с Celery
- ✅ Проверена передача контекста между процессами
- ✅ Валидированы chain, group, chord workflows
- ✅ Протестирована обработка ошибок и retry
- ✅ Проверена производительность с множеством задач
- ✅ Протестированы сигналы Celery
- ✅ Реализованы реальные сценарии использования
- ✅ Созданы вспомогательные утилиты

## ⏱️ Оценка времени
**2.5 часа** - написание всех тестов Celery интеграции

## 🔗 Связанные задачи
- **Предыдущая**: 012_integration_tests.md
- **Следующая**: 014_deployment_strategy.md
- **Связано с**: 005_context_logger_enhancement.md (CeleryContextPlugin)

## 📝 Дополнительные заметки
- Важно тестировать асинхронное поведение
- Celery worker должен быть изолирован для тестов
- Контекст должен корректно сериализоваться
- Обязательно тестировать производительность с реальными workers