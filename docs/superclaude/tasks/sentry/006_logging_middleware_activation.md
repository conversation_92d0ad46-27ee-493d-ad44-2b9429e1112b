# Задача 006: Активация LoggingContextMiddleware

## 📋 Описание задачи
Активировать и настроить LoggingContextMiddleware в Django для автоматического добавления контекста запроса во все логи. Middleware должен добавлять request_id, user_id и другую полезную информацию.

## 🎯 Цели
- Проверить существующий LoggingContextMiddleware
- Активировать middleware в настройках Django
- Добавить генерацию уникального request_id
- Интегрировать с ContextLogger
- Обеспечить очистку контекста после запроса

## ✅ Предварительные требования
- Выполнена задача 005 (улучшен ContextLogger)
- Существующий файл core/middleware/logging_middleware.py
- Понимание Django middleware
- Установлен пакет uuid для генерации request_id

## 🔧 Детальные шаги реализации

### Шаг 1: Проверить существующий middleware

```bash
# Проверяем наличие файла
ls -la core/middleware/logging_middleware.py

# Смотрим содержимое
cat core/middleware/logging_middleware.py
```

### Шаг 2: Обновить или создать LoggingContextMiddleware

```python
# core/middleware/logging_middleware.py
import uuid
import time
import logging
from typing import Callable, Optional
from django.http import HttpRequest, HttpResponse
from django.utils.deprecation import MiddlewareMixin
from django.contrib.auth.models import AnonymousUser

from core.logging import (
    set_context,
    clear_context,
    add_context,
    ContextLogger
)

# Логгер для самого middleware
logger = ContextLogger.get_logger(__name__)


class LoggingContextMiddleware(MiddlewareMixin):
    """
    Middleware to add request context to all logs during request processing.
    
    This middleware:
    - Generates unique request_id for each request
    - Adds user information to logging context
    - Tracks request timing
    - Ensures context cleanup after request
    - Logs request start/end with timing
    """
    
    def __init__(self, get_response: Optional[Callable] = None):
        """
        Initialize middleware.
        
        Args:
            get_response: The next middleware or view
        """
        self.get_response = get_response
        super().__init__(get_response)
        
        # Конфигурация
        self.log_request_start = True
        self.log_request_end = True
        self.log_slow_requests = True
        self.slow_request_threshold = 1.0  # секунды
        
    def process_request(self, request: HttpRequest) -> Optional[HttpResponse]:
        """
        Process incoming request and set up logging context.
        
        Args:
            request: The HTTP request
            
        Returns:
            None to continue processing
        """
        # Генерируем уникальный request_id
        request_id = self._generate_request_id()
        request.request_id = request_id
        
        # Сохраняем время начала
        request._logging_start_time = time.time()
        
        # Устанавливаем базовый контекст
        context = {
            'request_id': request_id,
            'method': request.method,
            'path': request.path,
            'remote_addr': self._get_client_ip(request),
            'user_agent': request.META.get('HTTP_USER_AGENT', ''),
        }
        
        # Добавляем информацию о пользователе
        if hasattr(request, 'user') and request.user.is_authenticated:
            context.update({
                'user_id': request.user.id,
                'username': request.user.username,
                'user_email': getattr(request.user, 'email', None),
            })
        
        # Добавляем дополнительные headers если нужно
        self._add_custom_headers(request, context)
        
        # Устанавливаем контекст
        set_context(**context)
        
        # Логируем начало запроса
        if self.log_request_start:
            logger.info(
                "Request started",
                extra_headers=self._get_relevant_headers(request)
            )
        
        return None
    
    def process_response(self, request: HttpRequest, response: HttpResponse) -> HttpResponse:
        """
        Process response and log request completion.
        
        Args:
            request: The HTTP request
            response: The HTTP response
            
        Returns:
            The response object
        """
        # Вычисляем время выполнения
        duration = None
        if hasattr(request, '_logging_start_time'):
            duration = time.time() - request._logging_start_time
            
        # Добавляем информацию о response
        add_context(
            status_code=response.status_code,
            response_size=len(response.content) if hasattr(response, 'content') else None,
            duration_ms=int(duration * 1000) if duration else None
        )
        
        # Логируем завершение запроса
        if self.log_request_end:
            log_data = {
                'status_code': response.status_code,
            }
            
            if duration is not None:
                log_data['duration_seconds'] = round(duration, 3)
                
                # Предупреждение о медленных запросах
                if self.log_slow_requests and duration > self.slow_request_threshold:
                    logger.warning(
                        f"Slow request detected: {duration:.2f}s",
                        **log_data
                    )
                else:
                    logger.info("Request completed", **log_data)
            else:
                logger.info("Request completed", **log_data)
        
        # Очищаем контекст
        clear_context()
        
        return response
    
    def process_exception(self, request: HttpRequest, exception: Exception) -> Optional[HttpResponse]:
        """
        Process exceptions and log them with context.
        
        Args:
            request: The HTTP request
            exception: The exception that occurred
            
        Returns:
            None to continue with default exception handling
        """
        # Логируем исключение с полным контекстом
        logger.exception(
            f"Unhandled exception in view: {exception.__class__.__name__}",
            exception_type=exception.__class__.__name__,
            exception_message=str(exception),
            view_name=self._get_view_name(request)
        )
        
        # Django продолжит обработку исключения
        return None
    
    def _generate_request_id(self) -> str:
        """
        Generate unique request ID.
        
        Returns:
            Unique request identifier
        """
        # Можно использовать разные форматы
        # return uuid.uuid4().hex  # 32 символа без дефисов
        return str(uuid.uuid4())  # С дефисами для читаемости
    
    def _get_client_ip(self, request: HttpRequest) -> str:
        """
        Get client IP address from request.
        
        Handles X-Forwarded-For and X-Real-IP headers.
        
        Args:
            request: The HTTP request
            
        Returns:
            Client IP address
        """
        # Проверяем заголовки прокси
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            # Берем первый IP из списка
            ip = x_forwarded_for.split(',')[0].strip()
        else:
            # Проверяем X-Real-IP
            ip = request.META.get('HTTP_X_REAL_IP')
            if not ip:
                # Используем REMOTE_ADDR
                ip = request.META.get('REMOTE_ADDR', '')
        
        return ip
    
    def _get_relevant_headers(self, request: HttpRequest) -> dict:
        """
        Extract relevant headers for logging.
        
        Args:
            request: The HTTP request
            
        Returns:
            Dictionary of relevant headers
        """
        relevant_headers = {
            'Accept': request.META.get('HTTP_ACCEPT', ''),
            'Accept-Language': request.META.get('HTTP_ACCEPT_LANGUAGE', ''),
            'Content-Type': request.META.get('CONTENT_TYPE', ''),
            'Referer': request.META.get('HTTP_REFERER', ''),
        }
        
        # Убираем пустые значения
        return {k: v for k, v in relevant_headers.items() if v}
    
    def _add_custom_headers(self, request: HttpRequest, context: dict) -> None:
        """
        Add custom headers to context if present.
        
        Args:
            request: The HTTP request
            context: Context dictionary to update
        """
        # Correlation ID для распределенного трейсинга
        correlation_id = request.META.get('HTTP_X_CORRELATION_ID')
        if correlation_id:
            context['correlation_id'] = correlation_id
            
        # Session ID если есть
        if hasattr(request, 'session') and request.session.session_key:
            context['session_id'] = request.session.session_key
            
        # API версия
        api_version = request.META.get('HTTP_X_API_VERSION')
        if api_version:
            context['api_version'] = api_version
    
    def _get_view_name(self, request: HttpRequest) -> Optional[str]:
        """
        Get view name from request.
        
        Args:
            request: The HTTP request
            
        Returns:
            View name if available
        """
        if hasattr(request, 'resolver_match') and request.resolver_match:
            if request.resolver_match.func:
                return f"{request.resolver_match.func.__module__}.{request.resolver_match.func.__name__}"
            elif request.resolver_match.view_name:
                return request.resolver_match.view_name
        return None


class RequestIDMiddleware(LoggingContextMiddleware):
    """
    Simplified middleware that only adds request_id.
    
    Use this if you already have other middleware for logging
    but just need request_id functionality.
    """
    
    def __init__(self, get_response: Optional[Callable] = None):
        super().__init__(get_response)
        self.log_request_start = False
        self.log_request_end = False
    
    def process_request(self, request: HttpRequest) -> Optional[HttpResponse]:
        """Only set request_id without full logging."""
        request_id = self._generate_request_id()
        request.request_id = request_id
        set_context(request_id=request_id)
        return None
    
    def process_response(self, request: HttpRequest, response: HttpResponse) -> HttpResponse:
        """Clear context without logging."""
        clear_context()
        return response


class APILoggingMiddleware(LoggingContextMiddleware):
    """
    Extended middleware for API endpoints with additional logging.
    
    Adds:
    - Request/response body logging (with size limits)
    - API key/token information
    - Response time SLA tracking
    """
    
    def __init__(self, get_response: Optional[Callable] = None):
        super().__init__(get_response)
        self.log_request_body = True
        self.log_response_body = True
        self.max_body_length = 1000  # Максимальная длина тела для логирования
        self.api_sla_ms = 200  # SLA для API в миллисекундах
    
    def process_request(self, request: HttpRequest) -> Optional[HttpResponse]:
        """Process API request with body logging."""
        # Вызываем родительский метод
        result = super().process_request(request)
        
        # Добавляем API-специфичный контекст
        if request.path.startswith('/api/'):
            # API ключ или токен
            api_key = request.META.get('HTTP_X_API_KEY')
            if api_key:
                # Маскируем ключ для безопасности
                masked_key = f"{api_key[:4]}...{api_key[-4:]}" if len(api_key) > 8 else "***"
                add_context(api_key=masked_key)
            
            # Логируем тело запроса для POST/PUT/PATCH
            if self.log_request_body and request.method in ['POST', 'PUT', 'PATCH']:
                try:
                    body = request.body.decode('utf-8')
                    if len(body) > self.max_body_length:
                        body = body[:self.max_body_length] + '...(truncated)'
                    logger.debug("API request body", body=body)
                except Exception:
                    logger.debug("API request body", body="<binary or unparseable>")
        
        return result
    
    def process_response(self, request: HttpRequest, response: HttpResponse) -> HttpResponse:
        """Process API response with SLA checking."""
        # Проверяем SLA для API
        if request.path.startswith('/api/') and hasattr(request, '_logging_start_time'):
            duration_ms = int((time.time() - request._logging_start_time) * 1000)
            
            if duration_ms > self.api_sla_ms:
                logger.warning(
                    f"API SLA exceeded: {duration_ms}ms > {self.api_sla_ms}ms",
                    duration_ms=duration_ms,
                    sla_ms=self.api_sla_ms
                )
        
        # Вызываем родительский метод
        return super().process_response(request, response)
```

### Шаг 3: Активировать middleware в настройках

```python
# settings/base.py или settings/local.py, settings/production.py

MIDDLEWARE = [
    'django.middleware.security.SecurityMiddleware',
    
    # Добавляем LoggingContextMiddleware как можно раньше
    # чтобы он обрабатывал все последующие middleware
    'core.middleware.logging_middleware.LoggingContextMiddleware',
    
    'django.contrib.sessions.middleware.SessionMiddleware',
    'django.middleware.common.CommonMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
    
    # Другие middleware проекта
    'whitenoise.middleware.WhiteNoiseMiddleware',  # если используется
]

# Для API endpoints можно использовать специализированный middleware
# MIDDLEWARE.append('core.middleware.logging_middleware.APILoggingMiddleware')
```

### Шаг 4: Создать декоратор для views

```python
# core/middleware/logging_decorators.py
from functools import wraps
from core.logging import add_context, ContextLogger

logger = ContextLogger.get_logger(__name__)


def log_view_access(view_name: str = None):
    """
    Decorator to log view access with custom context.
    
    Args:
        view_name: Optional custom view name
        
    Example:
        @log_view_access("user_profile")
        def profile_view(request, user_id):
            ...
    """
    def decorator(view_func):
        @wraps(view_func)
        def wrapper(request, *args, **kwargs):
            # Добавляем контекст view
            context = {
                'view_name': view_name or view_func.__name__,
                'view_module': view_func.__module__,
            }
            
            # Добавляем аргументы view если они простые
            if args:
                context['view_args'] = str(args)[:100]
            if kwargs:
                # Безопасно логируем kwargs (без паролей и т.д.)
                safe_kwargs = {k: v for k, v in kwargs.items() 
                              if k not in ['password', 'token', 'secret']}
                context['view_kwargs'] = str(safe_kwargs)[:100]
            
            add_context(**context)
            
            logger.info(f"Accessing view: {context['view_name']}")
            
            try:
                response = view_func(request, *args, **kwargs)
                logger.info(f"View completed successfully: {context['view_name']}")
                return response
            except Exception as e:
                logger.exception(f"View failed: {context['view_name']}")
                raise
        
        return wrapper
    return decorator


def track_api_usage(api_name: str, version: str = "v1"):
    """
    Decorator to track API endpoint usage.
    
    Args:
        api_name: Name of the API endpoint
        version: API version
        
    Example:
        @track_api_usage("users_list", "v2")
        def users_api(request):
            ...
    """
    def decorator(view_func):
        @wraps(view_func)
        def wrapper(request, *args, **kwargs):
            add_context(
                api_name=api_name,
                api_version=version,
                api_method=request.method
            )
            
            # Можно добавить метрики
            logger.info(f"API called: {api_name}", 
                       extra={'metric_type': 'api_call',
                              'metric_name': f'api.{api_name}.{request.method.lower()}'})
            
            return view_func(request, *args, **kwargs)
        
        return wrapper
    return decorator
```

### Шаг 5: Создать templatetags для шаблонов

```python
# core/templatetags/logging_tags.py
from django import template
from django.utils.safestring import mark_safe

register = template.Library()


@register.simple_tag(takes_context=True)
def request_id(context):
    """
    Get request_id from request context.
    
    Usage in template:
        {% load logging_tags %}
        <meta name="request-id" content="{% request_id %}">
    """
    request = context.get('request')
    if request and hasattr(request, 'request_id'):
        return request.request_id
    return ''


@register.inclusion_tag('core/logging_debug.html', takes_context=True)
def logging_debug_panel(context):
    """
    Show logging debug information in development.
    
    Usage:
        {% load logging_tags %}
        {% if debug %}
            {% logging_debug_panel %}
        {% endif %}
    """
    request = context.get('request')
    if not request:
        return {}
        
    return {
        'request_id': getattr(request, 'request_id', None),
        'user_id': request.user.id if request.user.is_authenticated else None,
        'session_id': request.session.session_key if hasattr(request, 'session') else None,
        'path': request.path,
        'method': request.method,
    }
```

Template файл:
```html
<!-- templates/core/logging_debug.html -->
<div class="logging-debug-panel" style="background: #f0f0f0; padding: 10px; margin: 10px 0; font-size: 12px;">
    <h4>Logging Context Debug</h4>
    <ul>
        <li>Request ID: <code>{{ request_id }}</code></li>
        <li>User ID: <code>{{ user_id|default:"Anonymous" }}</code></li>
        <li>Session ID: <code>{{ session_id|default:"None" }}</code></li>
        <li>Path: <code>{{ path }}</code></li>
        <li>Method: <code>{{ method }}</code></li>
    </ul>
</div>
```

## 🧪 Тестирование middleware

### Создать тесты в tests/core/test_logging_middleware.py:

```python
import pytest
from django.test import RequestFactory, TestCase
from django.contrib.auth.models import User
from unittest.mock import patch, MagicMock

from core.middleware.logging_middleware import (
    LoggingContextMiddleware,
    RequestIDMiddleware,
    APILoggingMiddleware
)
from core.logging import get_context, clear_context


class TestLoggingContextMiddleware(TestCase):
    """Test cases for LoggingContextMiddleware."""
    
    def setUp(self):
        self.factory = RequestFactory()
        self.middleware = LoggingContextMiddleware(lambda x: x)
        self.user = User.objects.create_user('testuser')
        clear_context()
    
    def tearDown(self):
        clear_context()
    
    def test_request_id_generation(self):
        """Test that request_id is generated and added."""
        request = self.factory.get('/test/')
        
        self.middleware.process_request(request)
        
        # Проверяем request_id
        self.assertIsNotNone(request.request_id)
        self.assertTrue(len(request.request_id) > 0)
        
        # Проверяем контекст
        context = get_context()
        self.assertEqual(context['request_id'], request.request_id)
    
    def test_user_context(self):
        """Test that user information is added to context."""
        request = self.factory.get('/test/')
        request.user = self.user
        
        self.middleware.process_request(request)
        
        context = get_context()
        self.assertEqual(context['user_id'], self.user.id)
        self.assertEqual(context['username'], 'testuser')
    
    def test_anonymous_user(self):
        """Test handling of anonymous users."""
        from django.contrib.auth.models import AnonymousUser
        
        request = self.factory.get('/test/')
        request.user = AnonymousUser()
        
        self.middleware.process_request(request)
        
        context = get_context()
        self.assertNotIn('user_id', context)
        self.assertNotIn('username', context)
    
    def test_context_cleanup(self):
        """Test that context is cleared after response."""
        request = self.factory.get('/test/')
        response = MagicMock(status_code=200, content=b'OK')
        
        self.middleware.process_request(request)
        
        # Контекст должен быть установлен
        self.assertIsNotNone(get_context().get('request_id'))
        
        self.middleware.process_response(request, response)
        
        # Контекст должен быть очищен
        self.assertEqual(get_context(), {})
    
    def test_exception_logging(self):
        """Test exception logging with context."""
        request = self.factory.get('/test/')
        exception = ValueError("Test exception")
        
        self.middleware.process_request(request)
        
        with patch('core.middleware.logging_middleware.logger') as mock_logger:
            self.middleware.process_exception(request, exception)
            
            mock_logger.exception.assert_called_once()
            call_args = mock_logger.exception.call_args
            self.assertIn('ValueError', call_args[0][0])
    
    def test_slow_request_warning(self):
        """Test slow request warning."""
        request = self.factory.get('/test/')
        response = MagicMock(status_code=200, content=b'OK')
        
        # Устанавливаем низкий threshold для теста
        self.middleware.slow_request_threshold = 0.001
        
        self.middleware.process_request(request)
        
        # Имитируем задержку
        import time
        time.sleep(0.01)
        
        with patch('core.middleware.logging_middleware.logger') as mock_logger:
            self.middleware.process_response(request, response)
            
            # Должен быть warning о медленном запросе
            mock_logger.warning.assert_called()
            call_args = mock_logger.warning.call_args
            self.assertIn('Slow request', call_args[0][0])


class TestAPILoggingMiddleware(TestCase):
    """Test cases for APILoggingMiddleware."""
    
    def setUp(self):
        self.factory = RequestFactory()
        self.middleware = APILoggingMiddleware(lambda x: x)
        clear_context()
    
    def test_api_key_masking(self):
        """Test that API keys are masked in logs."""
        request = self.factory.post(
            '/api/users/',
            data={'name': 'test'},
            HTTP_X_API_KEY='sk_test_1234567890abcdef'
        )
        
        self.middleware.process_request(request)
        
        context = get_context()
        self.assertEqual(context.get('api_key'), 'sk_t...cdef')
    
    def test_sla_warning(self):
        """Test SLA warning for slow API calls."""
        request = self.factory.get('/api/users/')
        response = MagicMock(status_code=200)
        
        # Устанавливаем низкий SLA для теста
        self.middleware.api_sla_ms = 1
        
        self.middleware.process_request(request)
        
        # Имитируем задержку
        import time
        time.sleep(0.01)
        
        with patch('core.middleware.logging_middleware.logger') as mock_logger:
            self.middleware.process_response(request, response)
            
            # Должен быть warning о превышении SLA
            mock_logger.warning.assert_called()
            call_args = mock_logger.warning.call_args
            self.assertIn('SLA exceeded', call_args[0][0])
```

## ⚠️ Потенциальные проблемы

### Проблема 1: Конфликт с существующим middleware
**Симптом**: Ошибки импорта или дублирование функциональности
**Решение**: Проверить существующий код и адаптировать

### Проблема 2: Утечка контекста между запросами
**Симптом**: Данные одного запроса появляются в другом
**Решение**: Всегда очищать контекст в process_response

### Проблема 3: Производительность при большом трафике
**Симптом**: Замедление обработки запросов
**Решение**: Оптимизировать логирование, использовать асинхронные handlers

### Проблема 4: Большой объем логов
**Симптом**: Быстрый рост логов
**Решение**: Настроить уровни логирования, использовать rate limiting

## 📊 Критерии успешного завершения
- ✅ LoggingContextMiddleware активирован в MIDDLEWARE
- ✅ Генерируется уникальный request_id для каждого запроса
- ✅ Контекст пользователя добавляется в логи
- ✅ Контекст очищается после обработки запроса
- ✅ Исключения логируются с полным контекстом
- ✅ Написаны тесты
- ✅ Добавлены вспомогательные декораторы
- ✅ Работает с существующим кодом

## ⏱️ Оценка времени
**1 час** - реализация и тестирование

## 🔗 Связанные задачи
- **Предыдущая**: 005_context_logger_enhancement.md
- **Следующая**: 007_migration_to_context_logger.md
- **Зависит от**: ContextLogger из задачи 005

## 📝 Дополнительные заметки
- Middleware должен быть максимально надежным
- Важно правильно обрабатывать исключения
- Рассмотреть добавление метрик производительности
- Для высоконагруженных проектов использовать асинхронное логирование