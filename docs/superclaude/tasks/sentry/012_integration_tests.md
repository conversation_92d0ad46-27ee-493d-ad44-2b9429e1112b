# Задача 012: Integration тесты

## 📋 Описание задачи
Написать полный набор интеграционных тестов для системы логирования с Sentry. Тесты должны проверять взаимодействие всех компонентов: ContextLogger, SentryHandler, фильтры, middleware и конфигурация Django.

## 🎯 Цели
- Протестировать полный flow от логирования до Sentry
- Проверить интеграцию с Django middleware
- Протестировать работу с Celery tasks
- Проверить корректность контекста в разных сценариях
- Валидировать производительность системы
- Убедиться в правильной фильтрации данных

## ✅ Предварительные требования
- Выполнены задачи 001-011
- Установлен pytest-django
- Установлен pytest-mock
- Настроен тестовый Sentry проект
- Понимание интеграционного тестирования

## 🔧 Детальные шаги реализации

### Шаг 1: Создать базовый файл с настройками для тестов

```python
# tests/core/logging/test_integration.py
import pytest
import logging
import json
from django.test import TestCase, TransactionTestCase, override_settings
from django.test.client import Client
from django.contrib.auth.models import User
from django.urls import reverse
from unittest.mock import Mock, patch, MagicMock
import sentry_sdk
from sentry_sdk.integrations.django import DjangoIntegration
from sentry_sdk.integrations.celery import CeleryIntegration
import time
import threading
from contextlib import contextmanager

from core.logging import (
    ContextLogger,
    set_context,
    clear_context,
    get_context
)
from core.logging.handlers import SentryHandler
from core.logging.filters import SensitiveDataFilter, RateLimitFilter
from core.middleware.logging_middleware import LoggingContextMiddleware


@pytest.fixture(scope='module')
def django_db_setup(django_db_setup, django_db_blocker):
    """Setup test database."""
    with django_db_blocker.unblock():
        # Create test user
        User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )


@pytest.fixture
def mock_sentry_transport():
    """Mock Sentry transport to capture events."""
    events = []
    breadcrumbs = []
    
    class MockTransport:
        def capture_event(self, event):
            events.append(event)
            
        def capture_envelope(self, envelope):
            # Handle envelope format
            for item in envelope.items:
                if item.type == 'event':
                    events.append(item.get_event())
                elif item.type == 'breadcrumb':
                    breadcrumbs.append(item.payload)
    
    transport = MockTransport()
    
    with patch('sentry_sdk.Hub.current.client.transport', transport):
        yield {
            'transport': transport,
            'events': events,
            'breadcrumbs': breadcrumbs
        }


@pytest.fixture
def test_client():
    """Create authenticated test client."""
    client = Client()
    client.login(username='testuser', password='testpass123')
    return client


@pytest.fixture
def configure_test_logging():
    """Configure logging for tests."""
    # Save original configuration
    original_handlers = logging.root.handlers[:]
    original_level = logging.root.level
    
    # Configure test logging
    logging.root.handlers = []
    logging.root.setLevel(logging.DEBUG)
    
    # Add our handlers
    sentry_handler = SentryHandler(level=logging.ERROR)
    sentry_handler.addFilter(SensitiveDataFilter())
    sentry_handler.addFilter(RateLimitFilter())
    
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.DEBUG)
    
    logging.root.addHandler(sentry_handler)
    logging.root.addHandler(console_handler)
    
    yield
    
    # Restore original configuration
    logging.root.handlers = original_handlers
    logging.root.level = original_level


@contextmanager
def capture_sentry_events():
    """Context manager to capture Sentry events."""
    events = []
    
    def before_send(event, hint):
        events.append(event)
        return event
    
    # Temporarily override before_send
    client = sentry_sdk.Hub.current.client
    if client:
        original_before_send = client.options.get('before_send')
        client.options['before_send'] = before_send
        
        try:
            yield events
        finally:
            client.options['before_send'] = original_before_send
    else:
        yield events
```

### Шаг 2: Тесты Django View интеграции

```python
class TestDjangoViewIntegration(TestCase):
    """Test logging integration with Django views."""
    
    def setUp(self):
        self.client = Client()
        self.user = User.objects.get(username='testuser')
        
    @override_settings(
        MIDDLEWARE=[
            'core.middleware.logging_middleware.LoggingContextMiddleware',
            'django.contrib.sessions.middleware.SessionMiddleware',
            'django.contrib.auth.middleware.AuthenticationMiddleware',
        ]
    )
    def test_request_context_propagation(self):
        """Test that request context propagates through the system."""
        with patch('core.logging.logger.ContextLogger.get_logger') as mock_get_logger:
            mock_logger = Mock()
            mock_get_logger.return_value = mock_logger
            
            # Make a request
            response = self.client.get('/api/test/')
            
            # Check that context was set
            calls = mock_logger.info.call_args_list
            
            # Should have logged request start
            self.assertTrue(any('Request started' in str(call) for call in calls))
            
            # Check context in logs
            for call in calls:
                if call[1].get('extra'):
                    extra = call[1]['extra']
                    # Should have request_id
                    self.assertIn('request_id', extra)
                    # Should have method and path
                    self.assertEqual(extra.get('method'), 'GET')
                    self.assertEqual(extra.get('path'), '/api/test/')
    
    def test_authenticated_user_context(self):
        """Test that authenticated user info is added to context."""
        self.client.login(username='testuser', password='testpass123')
        
        with capture_sentry_events() as events:
            # Trigger an error in a view
            with patch('myapp.views.test_view') as mock_view:
                mock_view.side_effect = ValueError("Test error")
                
                try:
                    response = self.client.get('/test/')
                except ValueError:
                    pass
            
            # Check captured event
            self.assertEqual(len(events), 1)
            event = events[0]
            
            # Check user context
            self.assertEqual(event['user']['id'], str(self.user.id))
            self.assertEqual(event['user']['username'], 'testuser')
            self.assertEqual(event['user']['email'], '<EMAIL>')
            
            # Check request context
            self.assertIn('request', event['contexts'])
            self.assertIn('request_id', event['contexts']['request'])
    
    def test_exception_handling_in_view(self):
        """Test exception logging from views."""
        with capture_sentry_events() as events:
            # Create a view that raises exception
            from django.http import HttpResponse
            from django.views import View
            
            class ErrorView(View):
                def get(self, request):
                    logger = ContextLogger.get_logger(__name__)
                    logger.info("Processing request")
                    
                    # This will be logged
                    try:
                        result = 1 / 0
                    except ZeroDivisionError:
                        logger.exception("Division by zero in view")
                        raise
            
            # Mount the view
            with patch('django.urls.resolvers.get_resolver') as mock_resolver:
                view = ErrorView.as_view()
                mock_resolver.return_value.resolve.return_value.func = view
                
                # Make request
                with self.assertRaises(ZeroDivisionError):
                    self.client.get('/error/')
            
            # Check events
            self.assertGreater(len(events), 0)
            
            # Find the exception event
            exc_event = next(
                (e for e in events if e.get('exception')), 
                None
            )
            self.assertIsNotNone(exc_event)
            
            # Check exception details
            exc_values = exc_event['exception']['values']
            self.assertEqual(exc_values[0]['type'], 'ZeroDivisionError')
            self.assertIn('Division by zero', str(exc_values[0]['value']))


class TestMiddlewareIntegration(TransactionTestCase):
    """Test middleware integration with logging."""
    
    def test_request_id_generation(self):
        """Test that unique request_id is generated."""
        request_ids = set()
        
        # Make multiple requests
        for _ in range(10):
            response = self.client.get('/test/')
            
            # Get request_id from context
            with patch('core.logging.get_context') as mock_get_context:
                mock_get_context.return_value = {'request_id': 'test-id'}
                ctx = mock_get_context()
                
            if 'request_id' in ctx:
                request_ids.add(ctx['request_id'])
        
        # All request_ids should be unique
        self.assertEqual(len(request_ids), 10)
    
    def test_context_isolation_between_requests(self):
        """Test that context doesn't leak between requests."""
        contexts = []
        
        def capture_context(*args, **kwargs):
            contexts.append(get_context().copy())
            return HttpResponse("OK")
        
        with patch('myapp.views.test_view', capture_context):
            # Make requests with different users
            for i in range(3):
                if i > 0:
                    user = User.objects.create_user(f'user{i}', f'user{i}@test.com')
                    self.client.login(username=f'user{i}', password='pass')
                
                self.client.get('/test/')
        
        # Check that contexts are different
        self.assertEqual(len(contexts), 3)
        request_ids = [ctx.get('request_id') for ctx in contexts]
        self.assertEqual(len(set(request_ids)), 3)  # All unique
        
        # Check user isolation
        user_ids = [ctx.get('user_id') for ctx in contexts]
        self.assertEqual(len(set(user_ids)), 3)  # All different
    
    def test_slow_request_logging(self):
        """Test slow request detection and logging."""
        with patch('time.time') as mock_time:
            # Simulate slow request
            mock_time.side_effect = [0, 0, 2.5, 2.5]  # 2.5 second request
            
            with patch('core.logging.logger.warning') as mock_warning:
                response = self.client.get('/slow/')
                
                # Check warning was logged
                mock_warning.assert_called()
                call_args = mock_warning.call_args
                self.assertIn('Slow request', call_args[0][0])
```

### Шаг 3: Тесты фильтрации чувствительных данных

```python
class TestSensitiveDataFilteringIntegration(TestCase):
    """Test sensitive data filtering in real scenarios."""
    
    def test_password_filtering_in_logs(self):
        """Test that passwords are filtered in actual logs."""
        with capture_sentry_events() as events:
            logger = ContextLogger.get_logger(__name__)
            
            # Log message with password
            logger.error(
                "Login failed for user with password=secret123",
                extra={
                    'user_data': {
                        'username': 'testuser',
                        'password': 'another_secret',
                        'api_key': 'sk_test_123456789'
                    }
                }
            )
            
            # Force flush
            sentry_sdk.flush()
            
            # Check event
            self.assertEqual(len(events), 1)
            event = events[0]
            
            # Check message is filtered
            self.assertNotIn('secret123', event['message'])
            self.assertIn('[PASSWORD]', event['message'])
            
            # Check extra data is filtered
            extra_data = event.get('extra', {})
            if 'user_data' in extra_data:
                self.assertEqual(extra_data['user_data']['password'], '[REDACTED]')
                self.assertEqual(extra_data['user_data']['api_key'], '[REDACTED]')
                self.assertEqual(extra_data['user_data']['username'], 'testuser')
    
    def test_filter_performance_under_load(self):
        """Test filter performance with high log volume."""
        import time
        
        # Create logger with filters
        logger = ContextLogger.get_logger(__name__)
        handler = SentryHandler()
        handler.addFilter(SensitiveDataFilter())
        logger.addHandler(handler)
        
        # Generate many log messages with sensitive data
        start_time = time.time()
        
        for i in range(1000):
            logger.info(
                f"Processing user_{i} with password=pass{i} and token=tok{i}",
                extra={
                    'api_key': f'key_{i}',
                    'credit_card': '****************',
                    'email': f'user{i}@example.com'
                }
            )
        
        elapsed = time.time() - start_time
        
        # Should complete in reasonable time (< 1 second for 1000 messages)
        self.assertLess(elapsed, 1.0)
    
    def test_recursive_filtering(self):
        """Test filtering of nested data structures."""
        with capture_sentry_events() as events:
            logger = ContextLogger.get_logger(__name__)
            
            complex_data = {
                'level1': {
                    'password': 'secret1',
                    'level2': {
                        'api_key': 'key123456789012345678',
                        'level3': {
                            'token': 'bearer_abc123',
                            'safe_data': 'This is safe'
                        }
                    }
                },
                'users': [
                    {'name': 'user1', 'password': 'pass1'},
                    {'name': 'user2', 'password': 'pass2'}
                ]
            }
            
            logger.error("Complex data error", extra={'data': complex_data})
            sentry_sdk.flush()
            
            # Check filtering
            event = events[0]
            filtered_data = event['extra']['data']
            
            # Check nested filtering
            self.assertEqual(
                filtered_data['level1']['password'], 
                '[REDACTED]'
            )
            self.assertEqual(
                filtered_data['level1']['level2']['api_key'], 
                '[REDACTED]'
            )
            self.assertEqual(
                filtered_data['level1']['level2']['level3']['token'], 
                '[REDACTED]'
            )
            self.assertEqual(
                filtered_data['level1']['level2']['level3']['safe_data'], 
                'This is safe'
            )
```

### Шаг 4: Тесты Celery интеграции

```python
class TestCeleryIntegration(TransactionTestCase):
    """Test logging integration with Celery tasks."""
    
    def setUp(self):
        # Mock Celery app
        from celery import Celery
        self.app = Celery('test')
        self.app.conf.update(
            task_always_eager=True,
            task_eager_propagates=True,
        )
        
    def test_task_context_propagation(self):
        """Test that task context is properly set."""
        from celery import Task
        
        contexts_captured = []
        
        class TestTask(Task):
            def run(self, *args, **kwargs):
                logger = ContextLogger.get_logger(__name__)
                logger.info("Task started")
                
                # Capture context
                contexts_captured.append(get_context().copy())
                
                # Simulate some work
                result = sum(args)
                
                logger.info("Task completed", result=result)
                return result
        
        # Register and run task
        task = TestTask()
        task.name = 'test_task'
        self.app.register_task(task)
        
        # Run task
        result = task.apply_async(args=(1, 2, 3)).get()
        
        # Check result
        self.assertEqual(result, 6)
        
        # Check context
        self.assertEqual(len(contexts_captured), 1)
        ctx = contexts_captured[0]
        self.assertIn('task_id', ctx)
        self.assertIn('task_name', ctx)
        self.assertEqual(ctx['task_name'], 'test_task')
    
    def test_task_exception_logging(self):
        """Test exception logging in Celery tasks."""
        from celery import Task
        
        with capture_sentry_events() as events:
            class FailingTask(Task):
                def run(self, *args, **kwargs):
                    logger = ContextLogger.get_logger(__name__)
                    logger.info("Task processing")
                    
                    # This will fail
                    return 1 / 0
            
            task = FailingTask()
            task.name = 'failing_task'
            self.app.register_task(task)
            
            # Run task
            with self.assertRaises(ZeroDivisionError):
                task.apply_async().get()
            
            # Check events
            sentry_sdk.flush()
            
            # Should have exception event
            exc_events = [e for e in events if 'exception' in e]
            self.assertGreater(len(exc_events), 0)
            
            # Check task context in exception
            exc_event = exc_events[0]
            self.assertIn('task', exc_event['contexts'])
            self.assertEqual(
                exc_event['contexts']['task']['task_name'], 
                'failing_task'
            )
    
    def test_task_rate_limiting(self):
        """Test rate limiting in Celery tasks."""
        from celery import Task
        
        class NoisyTask(Task):
            def run(self, *args, **kwargs):
                logger = ContextLogger.get_logger(__name__)
                
                # Generate many similar messages
                for i in range(100):
                    logger.warning("Task warning message")
                
                return "done"
        
        with capture_sentry_events() as events:
            task = NoisyTask()
            task.name = 'noisy_task'
            self.app.register_task(task)
            
            # Run task
            task.apply_async().get()
            sentry_sdk.flush()
            
            # Check rate limiting worked
            warning_events = [
                e for e in events 
                if e.get('level') == 'warning'
            ]
            
            # Should have fewer events due to rate limiting
            self.assertLess(len(warning_events), 100)
```

### Шаг 5: Тесты производительности и нагрузки

```python
class TestPerformanceIntegration(TestCase):
    """Test system performance under load."""
    
    def test_concurrent_request_handling(self):
        """Test handling multiple concurrent requests."""
        import concurrent.futures
        
        def make_request(index):
            client = Client()
            response = client.get(f'/test/?index={index}')
            return response.status_code
        
        # Make concurrent requests
        with concurrent.futures.ThreadPoolExecutor(max_workers=10) as executor:
            futures = [
                executor.submit(make_request, i) 
                for i in range(50)
            ]
            
            results = [
                future.result() 
                for future in concurrent.futures.as_completed(futures)
            ]
        
        # All requests should succeed
        self.assertEqual(len(results), 50)
        self.assertTrue(all(status == 200 for status in results))
    
    def test_memory_usage_under_load(self):
        """Test memory usage doesn't grow excessively."""
        import psutil
        import os
        
        process = psutil.Process(os.getpid())
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        # Generate lots of logs
        logger = ContextLogger.get_logger(__name__)
        
        for i in range(10000):
            set_context(
                request_id=f'req-{i}',
                user_id=i,
                extra_data={'index': i, 'data': 'x' * 100}
            )
            
            logger.info(f"Message {i}")
            
            if i % 100 == 0:
                logger.error(f"Error {i}", extra={'error_data': 'y' * 1000})
            
            clear_context()
        
        # Check memory usage
        final_memory = process.memory_info().rss / 1024 / 1024  # MB
        memory_increase = final_memory - initial_memory
        
        # Memory increase should be reasonable (< 100MB)
        self.assertLess(memory_increase, 100)
    
    def test_logging_latency(self):
        """Test logging doesn't add significant latency."""
        import statistics
        
        logger = ContextLogger.get_logger(__name__)
        
        # Measure baseline
        baseline_times = []
        for _ in range(100):
            start = time.perf_counter()
            # Simulate work
            _ = sum(range(1000))
            baseline_times.append(time.perf_counter() - start)
        
        baseline_avg = statistics.mean(baseline_times)
        
        # Measure with logging
        logging_times = []
        for i in range(100):
            start = time.perf_counter()
            
            set_context(request_id=f'req-{i}')
            logger.info("Processing", extra={'index': i})
            
            # Simulate work
            _ = sum(range(1000))
            
            logger.info("Completed")
            clear_context()
            
            logging_times.append(time.perf_counter() - start)
        
        logging_avg = statistics.mean(logging_times)
        
        # Logging overhead should be minimal (< 10% increase)
        overhead_percent = ((logging_avg - baseline_avg) / baseline_avg) * 100
        self.assertLess(overhead_percent, 10)
```

### Шаг 6: End-to-end тесты

```python
class TestEndToEndIntegration(TransactionTestCase):
    """Test complete user scenarios."""
    
    @override_settings(
        SENTRY_DSN='https://<EMAIL>/123',
        LOGGING={
            'version': 1,
            'disable_existing_loggers': False,
            'handlers': {
                'sentry': {
                    'class': 'core.logging.handlers.SentryHandler',
                    'level': 'ERROR',
                },
            },
            'root': {
                'level': 'INFO',
                'handlers': ['sentry'],
            },
        }
    )
    def test_complete_user_flow(self):
        """Test complete user flow from login to error."""
        with capture_sentry_events() as events:
            # User logs in
            response = self.client.post('/login/', {
                'username': 'testuser',
                'password': 'testpass123'
            })
            self.assertEqual(response.status_code, 302)
            
            # User accesses protected resource
            response = self.client.get('/profile/')
            self.assertEqual(response.status_code, 200)
            
            # User triggers an error
            with patch('myapp.views.profile_view') as mock_view:
                mock_view.side_effect = Exception("Profile error")
                
                with self.assertRaises(Exception):
                    self.client.get('/profile/')
            
            sentry_sdk.flush()
            
            # Check events
            error_events = [e for e in events if e.get('level') == 'error']
            self.assertGreater(len(error_events), 0)
            
            # Check last error has full context
            last_error = error_events[-1]
            
            # Should have user context
            self.assertEqual(last_error['user']['username'], 'testuser')
            
            # Should have request context
            self.assertIn('request_id', last_error['contexts']['request'])
            
            # Should not have sensitive data
            self.assertNotIn('testpass123', str(last_error))
    
    def test_api_endpoint_monitoring(self):
        """Test API endpoint monitoring and alerting."""
        # Configure API monitoring
        with override_settings(
            MIDDLEWARE=[
                'core.middleware.logging_middleware.APILoggingMiddleware',
            ]
        ):
            with capture_sentry_events() as events:
                # Make API calls
                headers = {'HTTP_X_API_KEY': 'sk_test_123456789'}
                
                # Successful call
                response = self.client.get('/api/users/', **headers)
                
                # Slow call
                with patch('time.time') as mock_time:
                    mock_time.side_effect = [0, 0.5]  # 500ms
                    response = self.client.get('/api/users/1/', **headers)
                
                # Failed call
                response = self.client.post('/api/users/', 
                    data={'invalid': 'data'}, 
                    **headers
                )
                
                sentry_sdk.flush()
                
                # Check monitoring events
                # Should have SLA warning
                sla_warnings = [
                    e for e in events 
                    if 'SLA exceeded' in e.get('message', '')
                ]
                self.assertGreater(len(sla_warnings), 0)
                
                # API key should be masked
                for event in events:
                    event_str = str(event)
                    self.assertNotIn('sk_test_123456789', event_str)
                    if 'api_key' in event_str:
                        self.assertIn('[API_KEY]', event_str)
```

### Шаг 7: Тесты конфигурации и настройки

```python
class TestConfigurationIntegration(TestCase):
    """Test different configuration scenarios."""
    
    def test_minimal_configuration(self):
        """Test with minimal configuration."""
        with override_settings(
            LOGGING={
                'version': 1,
                'handlers': {
                    'sentry': {
                        'class': 'core.logging.handlers.SentryHandler',
                    },
                },
                'root': {
                    'handlers': ['sentry'],
                },
            }
        ):
            logger = logging.getLogger()
            
            # Should work with defaults
            logger.error("Test error")
            
            # Check handler configuration
            sentry_handler = next(
                h for h in logger.handlers 
                if isinstance(h, SentryHandler)
            )
            self.assertEqual(sentry_handler.level, logging.ERROR)
    
    def test_custom_configuration(self):
        """Test with custom configuration."""
        with override_settings(
            LOGGING={
                'version': 1,
                'filters': {
                    'sensitive': {
                        'class': 'core.logging.filters.SensitiveDataFilter',
                        'preset': 'strict',
                    },
                    'rate_limit': {
                        'class': 'core.logging.filters.RateLimitFilter',
                        'rate': 10,
                        'per': 60,
                    },
                },
                'handlers': {
                    'sentry': {
                        'class': 'core.logging.handlers.BufferedSentryHandler',
                        'level': 'WARNING',
                        'filters': ['sensitive', 'rate_limit'],
                        'buffer_size': 50,
                        'flush_interval': 10.0,
                    },
                },
                'loggers': {
                    'myapp': {
                        'handlers': ['sentry'],
                        'level': 'DEBUG',
                    },
                },
            }
        ):
            logger = logging.getLogger('myapp')
            
            # Check configuration applied
            handler = logger.handlers[0]
            self.assertIsInstance(handler, BufferedSentryHandler)
            self.assertEqual(handler.buffer_size, 50)
            
            # Check filters applied
            filter_classes = [f.__class__.__name__ for f in handler.filters]
            self.assertIn('SensitiveDataFilter', filter_classes)
            self.assertIn('RateLimitFilter', filter_classes)
    
    def test_environment_specific_config(self):
        """Test environment-specific configurations."""
        # Test production config
        with override_settings(
            DEBUG=False,
            ENVIRONMENT='production',
            SENTRY_DSN='https://<EMAIL>/123'
        ):
            from django.conf import settings
            from core.logging.config import setup_logging
            
            setup_logging()
            
            # Should use production settings
            logger = logging.getLogger()
            handlers = [h for h in logger.handlers if isinstance(h, SentryHandler)]
            self.assertGreater(len(handlers), 0)
            
            # Should have strict filtering in production
            handler = handlers[0]
            sensitive_filter = next(
                f for f in handler.filters 
                if isinstance(f, SensitiveDataFilter)
            )
            self.assertIsNotNone(sensitive_filter)
```

### Шаг 8: Вспомогательные утилиты для тестов

```python
# tests/core/logging/integration_helpers.py
"""Helper utilities for integration tests."""

import time
import threading
from contextlib import contextmanager
from unittest.mock import patch
import sentry_sdk


@contextmanager
def temporary_sentry_client(dsn='https://<EMAIL>/123'):
    """Create temporary Sentry client for testing."""
    # Save current client
    hub = sentry_sdk.Hub.current
    old_client = hub.client
    
    try:
        # Initialize test client
        sentry_sdk.init(
            dsn=dsn,
            environment='test',
            traces_sample_rate=1.0,
            send_default_pii=False,
        )
        
        yield sentry_sdk.Hub.current.client
        
    finally:
        # Restore original client
        hub.client = old_client


def simulate_concurrent_logging(num_threads=10, messages_per_thread=100):
    """Simulate concurrent logging from multiple threads."""
    from core.logging import ContextLogger
    
    results = {'errors': [], 'completed': 0}
    
    def log_messages(thread_id):
        try:
            logger = ContextLogger.get_logger(f'thread.{thread_id}')
            
            for i in range(messages_per_thread):
                set_context(
                    thread_id=thread_id,
                    message_index=i
                )
                
                logger.info(f"Thread {thread_id} message {i}")
                
                if i % 10 == 0:
                    logger.warning(f"Thread {thread_id} warning {i}")
                
                clear_context()
            
            results['completed'] += 1
            
        except Exception as e:
            results['errors'].append((thread_id, str(e)))
    
    threads = []
    for i in range(num_threads):
        thread = threading.Thread(target=log_messages, args=(i,))
        threads.append(thread)
        thread.start()
    
    for thread in threads:
        thread.join()
    
    return results


def generate_test_events(count=10, include_errors=True):
    """Generate test events for integration testing."""
    from core.logging import ContextLogger
    
    logger = ContextLogger.get_logger('test.generator')
    events_generated = []
    
    for i in range(count):
        event_data = {
            'event_id': f'evt-{i}',
            'user_id': i % 5,
            'action': ['view', 'create', 'update', 'delete'][i % 4],
            'resource': ['user', 'post', 'comment'][i % 3],
        }
        
        set_context(**event_data)
        
        if include_errors and i % 5 == 0:
            try:
                # Simulate error
                if i % 10 == 0:
                    raise ValueError(f"Test error {i}")
                else:
                    1 / 0
            except Exception:
                logger.exception(f"Error in event {i}")
                events_generated.append(('error', event_data))
        else:
            logger.info(f"Event {i} processed", extra=event_data)
            events_generated.append(('info', event_data))
        
        clear_context()
    
    return events_generated
```

## 🧪 Тестирование

### Команды для запуска тестов:

```bash
# Запустить все интеграционные тесты
uv run pytest tests/core/logging/test_integration.py -v

# Запустить только Django интеграцию
uv run pytest tests/core/logging/test_integration.py::TestDjangoViewIntegration -v

# Запустить с отладкой Sentry
uv run pytest tests/core/logging/test_integration.py -v --log-cli-level=DEBUG

# Запустить тесты производительности
uv run pytest tests/core/logging/test_integration.py::TestPerformanceIntegration -v

# Запустить с измерением покрытия
uv run pytest tests/core/logging/test_integration.py --cov=core.logging --cov-report=html
```

## ⚠️ Потенциальные проблемы

### Проблема 1: Тесты влияют друг на друга
**Симптом**: Нестабильные результаты тестов
**Решение**: Использовать TransactionTestCase и изоляцию

### Проблема 2: Моки Sentry не работают
**Симптом**: События не перехватываются
**Решение**: Проверить правильность патчей

### Проблема 3: Таймауты в тестах
**Симптом**: Тесты зависают
**Решение**: Добавить таймауты и flush()

### Проблема 4: Утечки памяти в тестах
**Симптом**: Тесты потребляют много памяти
**Решение**: Очищать контекст и логгеры

## 📊 Критерии успешного завершения
- ✅ Протестирован полный flow Django → Logging → Sentry
- ✅ Проверена изоляция контекста между запросами
- ✅ Валидирована фильтрация чувствительных данных
- ✅ Протестирована интеграция с Celery
- ✅ Проверена производительность под нагрузкой
- ✅ Протестированы различные конфигурации
- ✅ Созданы helper функции для тестирования
- ✅ Покрытие интеграционных сценариев > 80%

## ⏱️ Оценка времени
**3 часа** - написание всех интеграционных тестов

## 🔗 Связанные задачи
- **Предыдущая**: 011_unit_tests_handlers.md
- **Следующая**: 013_celery_logging_tests.md
- **Использует**: Все предыдущие компоненты (001-011)

## 📝 Дополнительные заметки
- Интеграционные тесты должны быть независимыми
- Важно тестировать реальные сценарии использования
- Использовать моки только для внешних сервисов
- Обязательно тестировать граничные случаи и ошибки