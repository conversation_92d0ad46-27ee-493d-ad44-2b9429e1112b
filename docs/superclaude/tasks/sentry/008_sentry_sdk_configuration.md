# Задача 008: Настройка Sentry SDK

## 📋 Описание задачи
Настроить и инициализировать Sentry SDK в проекте Django с правильными интеграциями, фильтрами и настройками производительности. Обеспечить корректную работу с Django, Celery и другими компонентами системы.

## 🎯 Цели
- Создать модуль инициализации Sentry SDK
- Настроить интеграции (Django, Celery, Redis, etc.)
- Добавить before_send и before_send_transaction хуки
- Настроить sampling для производительности
- Обеспечить разные конфигурации для окружений
- Интегрировать с кастомными фильтрами

## ✅ Предварительные требования
- Установлен sentry-sdk[django,celery]
- Переменная окружения SENTRY_DSN
- Выполнены задачи 002, 003, 004 (handlers и filters)
- Понимание Sentry SDK configuration

## 🔧 Детальные шаги реализации

### Шаг 1: Создать модуль конфигурации Sentry

```python
# core/logging/sentry_config.py
"""
Sentry SDK configuration and initialization.
"""

import os
import logging
import sentry_sdk
from sentry_sdk.integrations.django import DjangoIntegration
from sentry_sdk.integrations.celery import CeleryIntegration
from sentry_sdk.integrations.redis import RedisIntegration
from sentry_sdk.integrations.logging import LoggingIntegration
from sentry_sdk.integrations.stdlib import StdlibIntegration
from sentry_sdk.integrations.excepthook import ExcepthookIntegration
from sentry_sdk.integrations.dedupe import DedupeIntegration
from sentry_sdk.integrations.atexit import AtexitIntegration
from typing import Dict, Any, Optional, List
from django.conf import settings

# Импортируем наши фильтры
from core.logging.filters import SensitiveDataFilter


class SentryConfig:
    """
    Sentry SDK configuration manager.
    
    Handles environment-specific settings and integrations.
    """
    
    # Базовые настройки по окружениям
    ENVIRONMENT_DEFAULTS = {
        'development': {
            'debug': True,
            'traces_sample_rate': 0.1,  # 10% транзакций
            'profiles_sample_rate': 0.1,  # 10% профилей
            'attach_stacktrace': True,
            'send_default_pii': False,  # Не отправляем PII в dev
            'environment': 'development',
        },
        'staging': {
            'debug': False,
            'traces_sample_rate': 0.2,  # 20% транзакций
            'profiles_sample_rate': 0.1,
            'attach_stacktrace': True,
            'send_default_pii': False,
            'environment': 'staging',
        },
        'production': {
            'debug': False,
            'traces_sample_rate': 0.05,  # 5% транзакций
            'profiles_sample_rate': 0.01,  # 1% профилей
            'attach_stacktrace': True,
            'send_default_pii': False,
            'environment': 'production',
        }
    }
    
    def __init__(self, environment: str = None):
        """
        Initialize Sentry configuration.
        
        Args:
            environment: Environment name (development/staging/production)
        """
        self.environment = environment or os.getenv('DJANGO_ENV', 'development')
        self.dsn = os.getenv('SENTRY_DSN')
        self.sensitive_filter = SensitiveDataFilter()
        
    def get_integrations(self) -> List[Any]:
        """Get list of Sentry integrations."""
        integrations = [
            # Django интеграция
            DjangoIntegration(
                transaction_style='url',  # Использовать URL для имен транзакций
                middleware_spans=True,    # Создавать spans для middleware
                signals_spans=True,       # Создавать spans для Django signals
                cache_spans=True,         # Создавать spans для cache операций
            ),
            
            # Celery интеграция
            CeleryIntegration(
                monitor_beat_tasks=True,  # Мониторить periodic tasks
                propagate_traces=True,    # Передавать trace между tasks
            ),
            
            # Redis интеграция (если используется)
            RedisIntegration(
                max_data_size=1024,  # Максимальный размер данных для логирования
            ),
            
            # Logging интеграция с нашим уровнем
            LoggingIntegration(
                level=logging.INFO,        # Минимальный уровень для breadcrumbs
                event_level=logging.ERROR  # Минимальный уровень для событий
            ),
            
            # Стандартные интеграции
            StdlibIntegration(),
            ExcepthookIntegration(always_run=True),
            DedupeIntegration(),
            AtexitIntegration(callback=self._shutdown_callback),
        ]
        
        return integrations
    
    def get_config(self) -> Dict[str, Any]:
        """Get Sentry SDK configuration."""
        # Базовая конфигурация для окружения
        config = self.ENVIRONMENT_DEFAULTS.get(
            self.environment, 
            self.ENVIRONMENT_DEFAULTS['development']
        ).copy()
        
        # Основные настройки
        config.update({
            'dsn': self.dsn,
            'integrations': self.get_integrations(),
            'max_breadcrumbs': 100,
            'attach_stacktrace': True,
            'send_client_reports': False,  # Не отправлять отчеты о rate limiting
            'shutdown_timeout': 5,  # Таймаут при завершении
            
            # Release tracking
            'release': self._get_release_version(),
            
            # Request data
            'request_bodies': 'medium',  # small/medium/always
            'with_locals': self.environment == 'development',
            
            # Callbacks
            'before_send': self.before_send,
            'before_send_transaction': self.before_send_transaction,
            'traces_sampler': self.traces_sampler,
            
            # Tags
            'initial_scope': {
                'tags': {
                    'service': 'social-manager',
                    'component': getattr(settings, 'COMPONENT_NAME', 'web'),
                },
                'contexts': {
                    'app': {
                        'app_version': self._get_app_version(),
                        'framework': 'django',
                    }
                }
            }
        })
        
        # Добавляем игнорируемые ошибки
        config['ignore_errors'] = self._get_ignored_errors()
        
        # Добавляем in-app includes
        config['in_app_include'] = self._get_in_app_includes()
        config['in_app_exclude'] = self._get_in_app_excludes()
        
        return config
    
    def before_send(self, event: Dict[str, Any], hint: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        Process event before sending to Sentry.
        
        Args:
            event: Event data
            hint: Additional information about the event
            
        Returns:
            Modified event or None to drop
        """
        # Фильтруем чувствительные данные
        event = self._filter_sensitive_data(event)
        
        # Обогащаем дополнительным контекстом
        event = self._enrich_event(event, hint)
        
        # Фильтруем по критериям
        if self._should_drop_event(event, hint):
            return None
            
        return event
    
    def before_send_transaction(self, event: Dict[str, Any], hint: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        Process transaction before sending to Sentry.
        
        Args:
            event: Transaction data
            hint: Additional information
            
        Returns:
            Modified transaction or None to drop
        """
        # Фильтруем слишком быстрые транзакции
        if event.get('timestamp', 0) - event.get('start_timestamp', 0) < 0.01:
            return None  # Меньше 10ms - не интересно
            
        # Добавляем метаданные
        if 'contexts' not in event:
            event['contexts'] = {}
            
        # Можно добавить дополнительную логику
        
        return event
    
    def traces_sampler(self, sampling_context: Dict[str, Any]) -> float:
        """
        Dynamic sampling for transactions.
        
        Args:
            sampling_context: Context for sampling decision
            
        Returns:
            Sample rate (0.0 to 1.0)
        """
        # Получаем информацию о транзакции
        transaction_context = sampling_context.get('transaction_context', {})
        transaction_name = transaction_context.get('name', '')
        
        # Health checks - не семплируем
        if 'health' in transaction_name or 'ping' in transaction_name:
            return 0
            
        # Admin panel - выше семплирование
        if '/admin/' in transaction_name:
            return 0.5
            
        # API endpoints - среднее семплирование
        if '/api/' in transaction_name:
            return 0.2
            
        # Celery tasks - зависит от задачи
        if sampling_context.get('parent_sampled') is True:
            # Если родитель засемплирован, продолжаем trace
            return 1.0
            
        # Используем базовый rate для окружения
        base_rate = self.ENVIRONMENT_DEFAULTS[self.environment]['traces_sample_rate']
        
        # Для важных операций увеличиваем
        if any(keyword in transaction_name.lower() 
               for keyword in ['payment', 'order', 'auth', 'register']):
            return min(base_rate * 5, 1.0)
            
        return base_rate
    
    def _filter_sensitive_data(self, event: Dict[str, Any]) -> Dict[str, Any]:
        """Filter sensitive data from event."""
        # Используем наш SensitiveDataFilter
        if 'request' in event:
            request_data = event['request']
            
            # Фильтруем cookies
            if 'cookies' in request_data:
                request_data['cookies'] = self._filter_dict(request_data['cookies'])
                
            # Фильтруем headers
            if 'headers' in request_data:
                request_data['headers'] = self._filter_dict(request_data['headers'])
                
            # Фильтруем data
            if 'data' in request_data:
                request_data['data'] = self._filter_dict(request_data['data'])
                
        # Фильтруем extra контекст
        if 'extra' in event:
            event['extra'] = self._filter_dict(event['extra'])
            
        # Фильтруем user контекст
        if 'user' in event and isinstance(event['user'], dict):
            # Оставляем только безопасные поля
            safe_user_fields = {'id', 'username', 'email'}
            event['user'] = {
                k: v for k, v in event['user'].items() 
                if k in safe_user_fields
            }
            
        return event
    
    def _filter_dict(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Filter sensitive data from dictionary."""
        if not isinstance(data, dict):
            return data
            
        filtered = {}
        for key, value in data.items():
            if self.sensitive_filter._is_sensitive_field(key):
                filtered[key] = '[REDACTED]'
            elif isinstance(value, dict):
                filtered[key] = self._filter_dict(value)
            elif isinstance(value, str):
                filtered[key] = self.sensitive_filter._scrub_string(value)
            else:
                filtered[key] = value
                
        return filtered
    
    def _enrich_event(self, event: Dict[str, Any], hint: Dict[str, Any]) -> Dict[str, Any]:
        """Add additional context to event."""
        # Добавляем информацию о сервере
        if 'server_name' not in event:
            event['server_name'] = os.getenv('SERVER_NAME', 'unknown')
            
        # Добавляем дополнительные теги
        if 'tags' not in event:
            event['tags'] = {}
            
        event['tags'].update({
            'deployment': os.getenv('DEPLOYMENT_ID', 'unknown'),
            'pod_name': os.getenv('POD_NAME', 'unknown'),
        })
        
        return event
    
    def _should_drop_event(self, event: Dict[str, Any], hint: Dict[str, Any]) -> bool:
        """Determine if event should be dropped."""
        # Получаем исключение если есть
        exc_info = hint.get('exc_info')
        if exc_info:
            exc_type, exc_value, tb = exc_info
            
            # Игнорируем некоторые исключения в production
            if self.environment == 'production':
                # 404 ошибки
                if exc_type.__name__ == 'Http404':
                    return True
                    
                # Отмененные запросы
                if exc_type.__name__ in ['RequestAborted', 'ConnectionResetError']:
                    return True
                    
        # Проверяем fingerprint для дедупликации
        fingerprint = event.get('fingerprint', [])
        if fingerprint and self._is_duplicate_event(fingerprint):
            return True
            
        return False
    
    def _is_duplicate_event(self, fingerprint: List[str]) -> bool:
        """Check if event is duplicate (implement caching logic)."""
        # TODO: Implement deduplication logic with Redis/cache
        return False
    
    def _get_ignored_errors(self) -> List[Any]:
        """Get list of errors to ignore."""
        ignored = [
            # Django
            'django.core.exceptions.DisallowedHost',
            'django.core.exceptions.RequestAborted',
            'django.security.DisallowedHost',
            
            # Python
            KeyboardInterrupt,
            SystemExit,
            
            # Network
            ConnectionError,
            ConnectionResetError,
            BrokenPipeError,
        ]
        
        # В production добавляем больше
        if self.environment == 'production':
            ignored.extend([
                'django.http.Http404',
                'rest_framework.exceptions.NotFound',
                'rest_framework.exceptions.PermissionDenied',
            ])
            
        return ignored
    
    def _get_in_app_includes(self) -> List[str]:
        """Get list of modules to consider in-app."""
        return [
            'core',
            'instagram_manager', 
            'telegram_manager',
            # Добавьте другие app модули
        ]
    
    def _get_in_app_excludes(self) -> List[str]:
        """Get list of modules to exclude from in-app."""
        return [
            'django',
            'rest_framework',
            'celery',
            'sentry_sdk',
        ]
    
    def _get_release_version(self) -> Optional[str]:
        """Get release version for Sentry."""
        # Пробуем разные источники
        release = (
            os.getenv('SENTRY_RELEASE') or
            os.getenv('GIT_COMMIT_SHA') or
            os.getenv('HEROKU_SLUG_COMMIT') or
            getattr(settings, 'VERSION', None)
        )
        
        if release:
            return f"social-manager@{release}"
            
        return None
    
    def _get_app_version(self) -> str:
        """Get application version."""
        return getattr(settings, 'VERSION', 'unknown')
    
    def _shutdown_callback(self, pending: int, timeout: int) -> None:
        """Callback for graceful shutdown."""
        print(f"Sentry: Flushing {pending} events (timeout: {timeout}s)")


def init_sentry(environment: str = None, **kwargs) -> None:
    """
    Initialize Sentry SDK with configuration.
    
    Args:
        environment: Environment name
        **kwargs: Additional configuration overrides
    """
    config_manager = SentryConfig(environment)
    config = config_manager.get_config()
    
    # Применяем переопределения
    config.update(kwargs)
    
    # Инициализируем только если есть DSN
    if config.get('dsn'):
        sentry_sdk.init(**config)
        
        # Добавляем глобальный контекст
        with sentry_sdk.configure_scope() as scope:
            scope.set_context("initialization", {
                "environment": environment,
                "timestamp": datetime.now().isoformat(),
            })
            
        logging.info(f"Sentry initialized for {environment} environment")
    else:
        logging.warning("Sentry DSN not found, skipping initialization")
```

### Шаг 2: Создать настройки для разных окружений

```python
# settings/sentry.py
"""
Sentry-specific settings.
"""

import os
from django.conf import settings

# Sentry DSN
SENTRY_DSN = os.getenv('SENTRY_DSN')

# Environment
SENTRY_ENVIRONMENT = os.getenv('DJANGO_ENV', 'development')

# Release tracking
SENTRY_RELEASE = os.getenv('SENTRY_RELEASE')

# Organization/Project
SENTRY_ORG = os.getenv('SENTRY_ORG', 'social-manager')
SENTRY_PROJECT = os.getenv('SENTRY_PROJECT', 'backend')

# Performance monitoring
SENTRY_TRACES_SAMPLE_RATE = float(os.getenv('SENTRY_TRACES_SAMPLE_RATE', '0.1'))
SENTRY_PROFILES_SAMPLE_RATE = float(os.getenv('SENTRY_PROFILES_SAMPLE_RATE', '0.1'))

# Security
SENTRY_SEND_DEFAULT_PII = os.getenv('SENTRY_SEND_DEFAULT_PII', 'false').lower() == 'true'

# Integrations
SENTRY_INTEGRATIONS = {
    'django': True,
    'celery': True,
    'redis': True,
    'logging': True,
}

# Event filtering
SENTRY_IGNORE_ERRORS = [
    'django.core.exceptions.DisallowedHost',
    'django.core.exceptions.RequestAborted',
]

# In production, ignore more errors
if SENTRY_ENVIRONMENT == 'production':
    SENTRY_IGNORE_ERRORS.extend([
        'django.http.Http404',
        'rest_framework.exceptions.NotFound',
    ])

# Transport options
SENTRY_TRANSPORT_OPTIONS = {
    'shutdown_timeout': 5,
}

# Breadcrumbs
SENTRY_MAX_BREADCRUMBS = 100

# Request bodies
SENTRY_REQUEST_BODIES = 'medium'  # 'never', 'small', 'medium', 'always'

# Debug
SENTRY_DEBUG = settings.DEBUG and SENTRY_ENVIRONMENT == 'development'
```

### Шаг 3: Интеграция в Django settings

```python
# settings/base.py (добавить в конец)

# Import Sentry settings
from .sentry import *

# Initialize Sentry
if SENTRY_DSN:
    from core.logging.sentry_config import init_sentry
    
    init_sentry(
        environment=SENTRY_ENVIRONMENT,
        debug=SENTRY_DEBUG,
        traces_sample_rate=SENTRY_TRACES_SAMPLE_RATE,
        profiles_sample_rate=SENTRY_PROFILES_SAMPLE_RATE,
        send_default_pii=SENTRY_SEND_DEFAULT_PII,
    )
```

### Шаг 4: Создать management command для тестирования

```python
# core/management/commands/test_sentry.py
"""
Test Sentry integration.
"""

from django.core.management.base import BaseCommand
from django.conf import settings
import sentry_sdk
import logging
from core.logging import ContextLogger


class Command(BaseCommand):
    help = 'Test Sentry integration'
    
    def add_arguments(self, parser):
        parser.add_argument(
            '--type',
            choices=['error', 'message', 'transaction', 'all'],
            default='all',
            help='Type of test to run'
        )
        
    def handle(self, *args, **options):
        test_type = options['type']
        
        self.stdout.write("Testing Sentry integration...")
        
        if test_type in ['error', 'all']:
            self.test_error()
            
        if test_type in ['message', 'all']:
            self.test_message()
            
        if test_type in ['transaction', 'all']:
            self.test_transaction()
            
        self.stdout.write(self.style.SUCCESS("Test completed!"))
        
    def test_error(self):
        """Test error capturing."""
        self.stdout.write("Testing error capture...")
        
        logger = ContextLogger.get_logger(__name__)
        
        # Test with context
        logger.add_context(
            test_type="error",
            user_id=123,
            action="test_sentry_command"
        )
        
        try:
            # Генерируем ошибку
            1 / 0
        except ZeroDivisionError:
            logger.exception("Test error from management command")
            sentry_sdk.capture_exception()
            
        self.stdout.write("Error sent to Sentry")
        
    def test_message(self):
        """Test message capturing."""
        self.stdout.write("Testing message capture...")
        
        # Отправляем сообщение
        sentry_sdk.capture_message(
            "Test message from Django management command",
            level="info",
            tags={
                "test": True,
                "command": "test_sentry"
            }
        )
        
        # Через logger
        logger = ContextLogger.get_logger(__name__)
        logger.error("Test error message through logger")
        
        self.stdout.write("Message sent to Sentry")
        
    def test_transaction(self):
        """Test transaction/performance monitoring."""
        self.stdout.write("Testing transaction capture...")
        
        with sentry_sdk.start_transaction(
            op="test",
            name="test_sentry_command"
        ) as transaction:
            with transaction.start_child(op="step") as span:
                span.set_tag("test", True)
                span.set_data("command", "test_sentry")
                
                # Симулируем работу
                import time
                time.sleep(0.1)
                
        self.stdout.write("Transaction sent to Sentry")
```

### Шаг 5: Создать middleware для Sentry контекста

```python
# core/middleware/sentry_middleware.py
"""
Middleware to enhance Sentry context.
"""

import sentry_sdk
from django.utils.deprecation import MiddlewareMixin


class SentryContextMiddleware(MiddlewareMixin):
    """
    Add additional context to Sentry scope.
    
    Should be placed after AuthenticationMiddleware.
    """
    
    def process_request(self, request):
        """Add request context to Sentry."""
        with sentry_sdk.configure_scope() as scope:
            # Добавляем request_id если есть
            if hasattr(request, 'request_id'):
                scope.set_tag('request_id', request.request_id)
                scope.set_context('request', {
                    'request_id': request.request_id
                })
                
            # Добавляем информацию о пользователе
            if hasattr(request, 'user') and request.user.is_authenticated:
                scope.set_user({
                    'id': request.user.id,
                    'username': request.user.username,
                    'email': getattr(request.user, 'email', None),
                })
                
            # Добавляем дополнительные теги
            scope.set_tag('ip_address', self._get_client_ip(request))
            scope.set_tag('method', request.method)
            scope.set_tag('path', request.path)
            
            # Добавляем custom контекст
            scope.set_context('http_request', {
                'method': request.method,
                'path': request.path,
                'query_string': request.META.get('QUERY_STRING', ''),
                'user_agent': request.META.get('HTTP_USER_AGENT', ''),
                'referer': request.META.get('HTTP_REFERER', ''),
            })
            
    def _get_client_ip(self, request):
        """Get client IP address."""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0].strip()
        else:
            ip = request.META.get('REMOTE_ADDR', '')
        return ip
```

### Шаг 6: Создать декоратор для Celery tasks

```python
# core/decorators/sentry_decorators.py
"""
Sentry-related decorators.
"""

import functools
import sentry_sdk
from typing import Callable, Any


def monitor_celery_task(func: Callable) -> Callable:
    """
    Decorator to monitor Celery task with Sentry.
    
    Adds performance monitoring and error tracking.
    """
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        # Получаем task instance
        task = args[0] if args and hasattr(args[0], 'request') else None
        task_id = task.request.id if task else 'unknown'
        task_name = func.__name__
        
        # Начинаем транзакцию
        with sentry_sdk.start_transaction(
            op="celery.task",
            name=task_name
        ) as transaction:
            transaction.set_tag("task.id", task_id)
            transaction.set_tag("task.name", task_name)
            
            if task:
                transaction.set_context("task", {
                    "id": task_id,
                    "name": task_name,
                    "args": str(task.request.args)[:200],
                    "kwargs": str(task.request.kwargs)[:200],
                    "retries": task.request.retries,
                })
            
            try:
                # Выполняем задачу
                result = func(*args, **kwargs)
                transaction.set_status("ok")
                return result
                
            except Exception as e:
                transaction.set_status("internal_error")
                sentry_sdk.capture_exception(e)
                raise
                
    return wrapper


def capture_errors(level: str = "error", fingerprint: List[str] = None):
    """
    Decorator to capture exceptions with custom handling.
    
    Args:
        level: Sentry level for the error
        fingerprint: Custom fingerprint for grouping
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except Exception as e:
                with sentry_sdk.push_scope() as scope:
                    scope.level = level
                    
                    if fingerprint:
                        scope.fingerprint = fingerprint
                    
                    # Добавляем контекст функции
                    scope.set_context("function", {
                        "name": func.__name__,
                        "module": func.__module__,
                        "args": str(args)[:200],
                        "kwargs": str(kwargs)[:200],
                    })
                    
                    sentry_sdk.capture_exception(e)
                raise
                
        return wrapper
    return decorator
```

## 🧪 Тестирование конфигурации

### Проверка инициализации:

```python
# В Django shell
from django.conf import settings
import sentry_sdk

# Проверяем инициализацию
print(sentry_sdk.Hub.current.client)
print(sentry_sdk.Hub.current.client.dsn)

# Проверяем интеграции
for integration in sentry_sdk.Hub.current.client.integrations:
    print(type(integration).__name__)

# Тестовое сообщение
sentry_sdk.capture_message("Test from Django shell")

# Тестовая ошибка
try:
    1/0
except:
    sentry_sdk.capture_exception()
```

### Запуск тестовой команды:

```bash
# Тест всех типов
python manage.py test_sentry

# Только ошибки
python manage.py test_sentry --type=error

# Только транзакции
python manage.py test_sentry --type=transaction
```

## ⚠️ Потенциальные проблемы

### Проблема 1: Sentry не инициализируется
**Симптом**: sentry_sdk.Hub.current.client is None
**Решение**: Проверить SENTRY_DSN и порядок импортов в settings

### Проблема 2: Чувствительные данные в Sentry
**Симптом**: Пароли или токены видны в событиях
**Решение**: Проверить работу before_send и фильтров

### Проблема 3: Слишком много событий
**Симптом**: Превышение лимитов Sentry
**Решение**: Настроить sample_rate и ignore_errors

### Проблема 4: Потеря контекста в async
**Симптом**: request_id не передается в async задачи
**Решение**: Использовать sentry_sdk.Hub.current.bind_client()

## 📊 Критерии успешного завершения
- ✅ Создан модуль конфигурации Sentry
- ✅ Настроены все необходимые интеграции
- ✅ Реализованы before_send хуки
- ✅ Настроен динамический sampling
- ✅ Работает фильтрация чувствительных данных
- ✅ Создана тестовая команда
- ✅ Добавлен SentryContextMiddleware
- ✅ События появляются в Sentry UI

## ⏱️ Оценка времени
**2 часа** - реализация и тестирование

## 🔗 Связанные задачи
- **Предыдущая**: 007_migration_to_context_logger.md
- **Следующая**: 009_logging_configuration.md
- **Зависит от**: 002, 003, 004 (handlers и filters)

## 📝 Дополнительные заметки
- Важно тестировать в разных окружениях
- Регулярно проверять использование квот
- Настроить alerts в Sentry UI
- Рассмотреть использование Sentry Profiling в production