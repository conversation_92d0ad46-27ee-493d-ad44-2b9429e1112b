# Задача 015: Мониторинг и валидация

## 📋 Описание задачи
Создать систему мониторинга и валидации для Sentry интеграции, включая метрики, дашборды, алерты и процедуры проверки корректности работы системы логирования.

## 🎯 Цели
- Настроить мониторинг здоровья Sentry интеграции
- Создать метрики для отслеживания производительности
- Разработать дашборды для визуализации
- Настроить алерты для критических ситуаций
- Создать автоматические проверки корректности
- Разработать процедуры валидации после deploy

## ✅ Предварительные требования
- Выполнены задачи 001-014
- Доступ к системам мониторинга (Prometheus/Grafana)
- Понимание метрик и алертинга
- Sentry развернут в production
- Настроен доступ к Sentry API

## 🔧 Детальные шаги реализации

### Шаг 1: Создать систему метрик

```python
# core/logging/metrics.py
"""
Metrics collection for Sentry integration monitoring.
"""
import time
import logging
from typing import Dict, Any, Optional, Callable
from functools import wraps
from collections import defaultdict, deque
from datetime import datetime, timedelta
import threading

# Prometheus клиент (если используется)
try:
    from prometheus_client import Counter, Histogram, Gauge, Summary
    PROMETHEUS_AVAILABLE = True
except ImportError:
    PROMETHEUS_AVAILABLE = False
    # Заглушки для работы без Prometheus
    class Counter:
        def __init__(self, *args, **kwargs): pass
        def inc(self, *args, **kwargs): pass
        def labels(self, **kwargs): return self
    
    class Histogram:
        def __init__(self, *args, **kwargs): pass
        def observe(self, *args, **kwargs): pass
        def labels(self, **kwargs): return self
    
    class Gauge:
        def __init__(self, *args, **kwargs): pass
        def set(self, *args, **kwargs): pass
        def inc(self, *args, **kwargs): pass
        def dec(self, *args, **kwargs): pass
        def labels(self, **kwargs): return self


class SentryMetrics:
    """Collect and expose metrics for Sentry integration."""
    
    def __init__(self):
        # Счетчики событий
        self.events_sent = Counter(
            'sentry_events_sent_total',
            'Total number of events sent to Sentry',
            ['level', 'status']
        )
        
        self.events_filtered = Counter(
            'sentry_events_filtered_total',
            'Total number of events filtered out',
            ['filter_type', 'reason']
        )
        
        self.api_calls = Counter(
            'sentry_api_calls_total',
            'Total number of Sentry API calls',
            ['endpoint', 'status_code']
        )
        
        # Гистограммы времени
        self.event_processing_time = Histogram(
            'sentry_event_processing_seconds',
            'Time spent processing events',
            ['event_type'],
            buckets=[0.001, 0.005, 0.01, 0.05, 0.1, 0.5, 1.0]
        )
        
        self.api_response_time = Histogram(
            'sentry_api_response_seconds',
            'Sentry API response time',
            ['endpoint'],
            buckets=[0.1, 0.5, 1.0, 2.5, 5.0, 10.0]
        )
        
        # Текущие значения
        self.active_handlers = Gauge(
            'sentry_active_handlers',
            'Number of active Sentry handlers'
        )
        
        self.buffer_size = Gauge(
            'sentry_buffer_size',
            'Current size of event buffer',
            ['handler_type']
        )
        
        self.error_rate = Gauge(
            'sentry_error_rate_percent',
            'Current error rate percentage'
        )
        
        # Внутренние счетчики для расчета rate
        self._event_counts = defaultdict(lambda: deque(maxlen=60))  # 60 секунд
        self._error_counts = defaultdict(int)
        self._lock = threading.Lock()
        
    def record_event_sent(self, level: str, success: bool):
        """Record that an event was sent to Sentry."""
        status = 'success' if success else 'failed'
        self.events_sent.labels(level=level, status=status).inc()
        
        # Обновляем счетчики для rate
        with self._lock:
            now = time.time()
            self._event_counts['total'].append((now, 1))
            if level in ['ERROR', 'CRITICAL']:
                self._event_counts['errors'].append((now, 1))
    
    def record_event_filtered(self, filter_type: str, reason: str):
        """Record that an event was filtered out."""
        self.events_filtered.labels(
            filter_type=filter_type,
            reason=reason
        ).inc()
    
    def record_processing_time(self, event_type: str, duration: float):
        """Record event processing time."""
        self.event_processing_time.labels(
            event_type=event_type
        ).observe(duration)
    
    def update_buffer_size(self, handler_type: str, size: int):
        """Update current buffer size."""
        self.buffer_size.labels(handler_type=handler_type).set(size)
    
    def calculate_rates(self):
        """Calculate current rates."""
        with self._lock:
            now = time.time()
            cutoff = now - 60  # Последние 60 секунд
            
            # Подсчет событий за последнюю минуту
            total_count = sum(
                1 for ts, _ in self._event_counts['total']
                if ts > cutoff
            )
            
            error_count = sum(
                1 for ts, _ in self._event_counts['errors']
                if ts > cutoff
            )
            
            # Расчет error rate
            if total_count > 0:
                error_rate = (error_count / total_count) * 100
                self.error_rate.set(error_rate)
            else:
                self.error_rate.set(0)
    
    def get_stats(self) -> Dict[str, Any]:
        """Get current statistics."""
        self.calculate_rates()
        
        return {
            'events': {
                'total_sent': self._get_counter_value(self.events_sent),
                'total_filtered': self._get_counter_value(self.events_filtered),
                'current_buffer_sizes': self._get_gauge_values(self.buffer_size),
                'error_rate_percent': self._get_gauge_value(self.error_rate),
            },
            'performance': {
                'avg_processing_time': self._get_histogram_stats(
                    self.event_processing_time
                ),
                'api_response_times': self._get_histogram_stats(
                    self.api_response_time
                ),
            },
            'health': {
                'active_handlers': self._get_gauge_value(self.active_handlers),
                'last_successful_event': self._get_last_success_time(),
            }
        }
    
    def _get_counter_value(self, counter):
        """Get counter value (mock for non-Prometheus)."""
        # В реальности нужно использовать Prometheus API
        return 0
    
    def _get_gauge_value(self, gauge):
        """Get gauge value (mock for non-Prometheus)."""
        return 0
    
    def _get_gauge_values(self, gauge):
        """Get all gauge values by label."""
        return {}
    
    def _get_histogram_stats(self, histogram):
        """Get histogram statistics."""
        return {
            'count': 0,
            'sum': 0,
            'avg': 0,
            'p50': 0,
            'p95': 0,
            'p99': 0,
        }
    
    def _get_last_success_time(self):
        """Get timestamp of last successful event."""
        # Implement based on your storage
        return datetime.now().isoformat()


# Глобальный экземпляр
sentry_metrics = SentryMetrics()


# Декоратор для измерения времени
def measure_time(metric_name: str):
    """Decorator to measure function execution time."""
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(*args, **kwargs):
            start_time = time.time()
            try:
                result = func(*args, **kwargs)
                return result
            finally:
                duration = time.time() - start_time
                sentry_metrics.record_processing_time(metric_name, duration)
        return wrapper
    return decorator
```

### Шаг 2: Создать health check endpoint

```python
# core/logging/health.py
"""
Health check endpoint for Sentry integration.
"""
from django.http import JsonResponse
from django.views import View
from django.utils.decorators import method_decorator
from django.views.decorators.csrf import csrf_exempt
import sentry_sdk
from datetime import datetime, timedelta
import logging

from core.logging.metrics import sentry_metrics
from core.logging import ContextLogger


logger = ContextLogger.get_logger(__name__)


@method_decorator(csrf_exempt, name='dispatch')
class SentryHealthCheckView(View):
    """Health check endpoint for monitoring Sentry integration."""
    
    def get(self, request):
        """Perform health check and return status."""
        health_status = self._check_health()
        
        # Определяем HTTP статус
        if health_status['status'] == 'healthy':
            status_code = 200
        elif health_status['status'] == 'degraded':
            status_code = 200  # Или 503 в зависимости от требований
        else:
            status_code = 503
        
        return JsonResponse(health_status, status=status_code)
    
    def _check_health(self) -> dict:
        """Perform comprehensive health check."""
        checks = {
            'sentry_sdk': self._check_sentry_sdk(),
            'handlers': self._check_handlers(),
            'connectivity': self._check_connectivity(),
            'performance': self._check_performance(),
            'errors': self._check_error_rate(),
        }
        
        # Определяем общий статус
        if all(check['healthy'] for check in checks.values()):
            overall_status = 'healthy'
        elif any(check['critical'] for check in checks.values()):
            overall_status = 'unhealthy'
        else:
            overall_status = 'degraded'
        
        return {
            'status': overall_status,
            'timestamp': datetime.now().isoformat(),
            'checks': checks,
            'metrics': sentry_metrics.get_stats(),
        }
    
    def _check_sentry_sdk(self) -> dict:
        """Check if Sentry SDK is properly initialized."""
        try:
            hub = sentry_sdk.Hub.current
            client = hub.client
            
            if client is None:
                return {
                    'healthy': False,
                    'critical': True,
                    'message': 'Sentry SDK not initialized',
                }
            
            return {
                'healthy': True,
                'critical': False,
                'message': 'Sentry SDK initialized',
                'dsn': client.dsn if hasattr(client, 'dsn') else 'Unknown',
            }
        except Exception as e:
            return {
                'healthy': False,
                'critical': True,
                'message': f'Error checking Sentry SDK: {str(e)}',
            }
    
    def _check_handlers(self) -> dict:
        """Check if logging handlers are properly configured."""
        try:
            root_logger = logging.getLogger()
            sentry_handlers = [
                h for h in root_logger.handlers
                if 'sentry' in h.__class__.__name__.lower()
            ]
            
            if not sentry_handlers:
                return {
                    'healthy': False,
                    'critical': True,
                    'message': 'No Sentry handlers found',
                }
            
            return {
                'healthy': True,
                'critical': False,
                'message': f'Found {len(sentry_handlers)} Sentry handlers',
                'handlers': [h.__class__.__name__ for h in sentry_handlers],
            }
        except Exception as e:
            return {
                'healthy': False,
                'critical': True,
                'message': f'Error checking handlers: {str(e)}',
            }
    
    def _check_connectivity(self) -> dict:
        """Check connectivity to Sentry."""
        try:
            # Отправляем тестовое сообщение
            test_id = f'health_check_{datetime.now().timestamp()}'
            
            with sentry_sdk.push_scope() as scope:
                scope.set_tag('health_check', 'true')
                scope.set_extra('test_id', test_id)
                
                sentry_sdk.capture_message(
                    'Health check test message',
                    level='info'
                )
            
            return {
                'healthy': True,
                'critical': False,
                'message': 'Successfully sent test message',
                'test_id': test_id,
            }
        except Exception as e:
            return {
                'healthy': False,
                'critical': True,
                'message': f'Failed to send test message: {str(e)}',
            }
    
    def _check_performance(self) -> dict:
        """Check performance metrics."""
        stats = sentry_metrics.get_stats()
        perf_data = stats.get('performance', {})
        
        # Проверяем среднее время обработки
        avg_time = perf_data.get('avg_processing_time', {}).get('avg', 0)
        
        if avg_time > 1.0:  # Более 1 секунды
            return {
                'healthy': False,
                'critical': False,
                'message': f'High processing time: {avg_time:.2f}s',
                'avg_processing_time': avg_time,
            }
        
        return {
            'healthy': True,
            'critical': False,
            'message': 'Performance within acceptable range',
            'avg_processing_time': avg_time,
        }
    
    def _check_error_rate(self) -> dict:
        """Check current error rate."""
        stats = sentry_metrics.get_stats()
        error_rate = stats.get('events', {}).get('error_rate_percent', 0)
        
        if error_rate > 10:  # Более 10% ошибок
            return {
                'healthy': False,
                'critical': error_rate > 25,  # Критично если > 25%
                'message': f'High error rate: {error_rate:.1f}%',
                'error_rate': error_rate,
            }
        
        return {
            'healthy': True,
            'critical': False,
            'message': 'Error rate acceptable',
            'error_rate': error_rate,
        }


# URL configuration
from django.urls import path

urlpatterns = [
    path('health/sentry/', SentryHealthCheckView.as_view(), name='sentry_health'),
]
```

### Шаг 3: Создать валидаторы конфигурации

```python
# core/logging/validators.py
"""
Validators for Sentry configuration and integration.
"""
import logging
from typing import Dict, List, Tuple, Optional
from django.conf import settings
import sentry_sdk

from core.logging import ContextLogger
from core.logging.handlers import SentryHandler
from core.logging.filters import SensitiveDataFilter, RateLimitFilter


logger = ContextLogger.get_logger(__name__)


class SentryConfigValidator:
    """Validate Sentry configuration and setup."""
    
    def __init__(self):
        self.errors: List[str] = []
        self.warnings: List[str] = []
        self.info: List[str] = []
    
    def validate(self) -> Tuple[bool, Dict[str, List[str]]]:
        """
        Run all validation checks.
        
        Returns:
            Tuple of (is_valid, validation_results)
        """
        self.errors = []
        self.warnings = []
        self.info = []
        
        # Запускаем все проверки
        self._validate_django_settings()
        self._validate_sentry_sdk()
        self._validate_logging_config()
        self._validate_handlers()
        self._validate_filters()
        self._validate_middleware()
        self._validate_celery_integration()
        self._validate_performance()
        
        # Результаты
        is_valid = len(self.errors) == 0
        
        return is_valid, {
            'errors': self.errors,
            'warnings': self.warnings,
            'info': self.info,
        }
    
    def _validate_django_settings(self):
        """Validate Django settings."""
        # Проверка SENTRY_DSN
        dsn = getattr(settings, 'SENTRY_DSN', None)
        if not dsn:
            self.errors.append('SENTRY_DSN not configured in settings')
        else:
            self.info.append(f'SENTRY_DSN configured: {dsn[:20]}...')
        
        # Проверка ENVIRONMENT
        env = getattr(settings, 'ENVIRONMENT', None)
        if not env:
            self.warnings.append('ENVIRONMENT not set, using "development"')
        else:
            self.info.append(f'Environment: {env}')
        
        # Проверка DEBUG
        if settings.DEBUG:
            self.warnings.append('DEBUG=True, Sentry may not capture all errors')
    
    def _validate_sentry_sdk(self):
        """Validate Sentry SDK initialization."""
        try:
            hub = sentry_sdk.Hub.current
            client = hub.client
            
            if client is None:
                self.errors.append('Sentry SDK not initialized')
                return
            
            self.info.append('Sentry SDK initialized successfully')
            
            # Проверка опций
            options = client.options
            
            # Traces sample rate
            traces_rate = options.get('traces_sample_rate', 0)
            if traces_rate == 0:
                self.warnings.append('Performance monitoring disabled (traces_sample_rate=0)')
            else:
                self.info.append(f'Performance monitoring enabled (rate={traces_rate})')
            
            # Integrations
            integrations = options.get('integrations', [])
            integration_names = [i.__class__.__name__ for i in integrations]
            self.info.append(f'Integrations: {", ".join(integration_names)}')
            
            if 'DjangoIntegration' not in integration_names:
                self.warnings.append('DjangoIntegration not enabled')
            
            if 'CeleryIntegration' not in integration_names:
                self.warnings.append('CeleryIntegration not enabled')
            
        except Exception as e:
            self.errors.append(f'Error validating Sentry SDK: {str(e)}')
    
    def _validate_logging_config(self):
        """Validate LOGGING configuration."""
        logging_config = getattr(settings, 'LOGGING', None)
        
        if not logging_config:
            self.errors.append('LOGGING not configured in settings')
            return
        
        # Проверка версии
        if logging_config.get('version') != 1:
            self.warnings.append('LOGGING version should be 1')
        
        # Проверка handlers
        handlers = logging_config.get('handlers', {})
        sentry_handlers = [
            name for name, config in handlers.items()
            if 'sentry' in config.get('class', '').lower()
        ]
        
        if not sentry_handlers:
            self.errors.append('No Sentry handlers configured in LOGGING')
        else:
            self.info.append(f'Sentry handlers: {", ".join(sentry_handlers)}')
        
        # Проверка root logger
        root = logging_config.get('root', {})
        root_handlers = root.get('handlers', [])
        
        if not any(h in sentry_handlers for h in root_handlers):
            self.warnings.append('Root logger does not use Sentry handler')
    
    def _validate_handlers(self):
        """Validate actual handler instances."""
        root_logger = logging.getLogger()
        
        sentry_handlers = []
        for handler in root_logger.handlers:
            if isinstance(handler, SentryHandler):
                sentry_handlers.append(handler)
        
        if not sentry_handlers:
            self.errors.append('No SentryHandler instances found in root logger')
            return
        
        self.info.append(f'Found {len(sentry_handlers)} SentryHandler instances')
        
        # Проверка конфигурации handlers
        for handler in sentry_handlers:
            # Проверка уровня
            if handler.level > logging.ERROR:
                self.warnings.append(
                    f'Handler {handler.__class__.__name__} has high level: {handler.level}'
                )
            
            # Проверка фильтров
            filter_types = [f.__class__.__name__ for f in handler.filters]
            if 'SensitiveDataFilter' not in filter_types:
                self.warnings.append(
                    f'Handler {handler.__class__.__name__} missing SensitiveDataFilter'
                )
    
    def _validate_filters(self):
        """Validate filter configuration."""
        # Проверяем фильтры на всех handlers
        root_logger = logging.getLogger()
        
        for handler in root_logger.handlers:
            if not isinstance(handler, SentryHandler):
                continue
            
            has_sensitive_filter = False
            has_rate_limit = False
            
            for filter_obj in handler.filters:
                if isinstance(filter_obj, SensitiveDataFilter):
                    has_sensitive_filter = True
                    # Проверяем настройки фильтра
                    if not filter_obj.patterns:
                        self.warnings.append('SensitiveDataFilter has no patterns')
                
                elif isinstance(filter_obj, RateLimitFilter):
                    has_rate_limit = True
            
            if not has_sensitive_filter:
                self.errors.append(
                    f'Handler {handler.__class__.__name__} missing SensitiveDataFilter'
                )
            
            if not has_rate_limit:
                self.warnings.append(
                    f'Handler {handler.__class__.__name__} missing RateLimitFilter'
                )
    
    def _validate_middleware(self):
        """Validate middleware configuration."""
        middleware = getattr(settings, 'MIDDLEWARE', [])
        
        logging_middleware = [
            m for m in middleware
            if 'logging' in m.lower()
        ]
        
        if not logging_middleware:
            self.warnings.append('No logging middleware configured')
        else:
            self.info.append(f'Logging middleware: {", ".join(logging_middleware)}')
            
            # Проверка позиции middleware
            for m in logging_middleware:
                pos = middleware.index(m)
                if pos > 5:  # Должен быть в начале
                    self.warnings.append(
                        f'{m} should be placed earlier in MIDDLEWARE'
                    )
    
    def _validate_celery_integration(self):
        """Validate Celery integration if applicable."""
        try:
            from celery import current_app
            
            # Проверяем signals
            from celery.signals import task_failure
            receivers = task_failure.receivers
            
            if not receivers:
                self.warnings.append('No Celery error signal handlers configured')
            else:
                self.info.append('Celery error signals configured')
                
        except ImportError:
            self.info.append('Celery not installed, skipping Celery validation')
    
    def _validate_performance(self):
        """Validate performance-related settings."""
        # Проверка buffering
        logging_config = getattr(settings, 'LOGGING', {})
        handlers = logging_config.get('handlers', {})
        
        for name, config in handlers.items():
            if 'BufferedSentryHandler' in config.get('class', ''):
                buffer_size = config.get('buffer_size', 10)
                if buffer_size < 10:
                    self.warnings.append(
                        f'Handler {name} has small buffer_size: {buffer_size}'
                    )
                
                flush_interval = config.get('flush_interval', 5.0)
                if flush_interval > 30:
                    self.warnings.append(
                        f'Handler {name} has long flush_interval: {flush_interval}s'
                    )
```

### Шаг 4: Создать мониторинг дашборд

```yaml
# monitoring/grafana/sentry_dashboard.json
{
  "dashboard": {
    "title": "Sentry Integration Monitoring",
    "panels": [
      {
        "title": "Events Sent to Sentry",
        "targets": [
          {
            "expr": "rate(sentry_events_sent_total[5m])",
            "legendFormat": "{{level}} - {{status}}"
          }
        ],
        "type": "graph"
      },
      {
        "title": "Error Rate",
        "targets": [
          {
            "expr": "sentry_error_rate_percent",
            "legendFormat": "Error Rate %"
          }
        ],
        "type": "gauge",
        "thresholds": {
          "mode": "absolute",
          "steps": [
            {"color": "green", "value": null},
            {"color": "yellow", "value": 5},
            {"color": "red", "value": 10}
          ]
        }
      },
      {
        "title": "Event Processing Time",
        "targets": [
          {
            "expr": "histogram_quantile(0.95, rate(sentry_event_processing_seconds_bucket[5m]))",
            "legendFormat": "p95 - {{event_type}}"
          }
        ],
        "type": "graph"
      },
      {
        "title": "Filtered Events",
        "targets": [
          {
            "expr": "rate(sentry_events_filtered_total[5m])",
            "legendFormat": "{{filter_type}} - {{reason}}"
          }
        ],
        "type": "graph"
      },
      {
        "title": "Buffer Sizes",
        "targets": [
          {
            "expr": "sentry_buffer_size",
            "legendFormat": "{{handler_type}}"
          }
        ],
        "type": "graph"
      },
      {
        "title": "Active Handlers",
        "targets": [
          {
            "expr": "sentry_active_handlers"
          }
        ],
        "type": "stat"
      }
    ]
  }
}
```

### Шаг 5: Создать алерты

```yaml
# monitoring/prometheus/sentry_alerts.yml
groups:
  - name: sentry_integration
    interval: 30s
    rules:
      # Критические алерты
      - alert: SentrySDKNotInitialized
        expr: sentry_active_handlers == 0
        for: 2m
        labels:
          severity: critical
          team: backend
        annotations:
          summary: "Sentry SDK is not initialized"
          description: "No active Sentry handlers detected for 2 minutes"
          
      - alert: SentryHighErrorRate
        expr: sentry_error_rate_percent > 25
        for: 5m
        labels:
          severity: critical
          team: backend
        annotations:
          summary: "High error rate detected"
          description: "Error rate is {{ $value }}% (threshold: 25%)"
          
      - alert: SentryEventSendFailure
        expr: rate(sentry_events_sent_total{status="failed"}[5m]) > 0.1
        for: 5m
        labels:
          severity: critical
          team: backend
        annotations:
          summary: "Sentry events failing to send"
          description: "{{ $value }} events/sec are failing"
      
      # Предупреждения
      - alert: SentrySlowProcessing
        expr: histogram_quantile(0.95, rate(sentry_event_processing_seconds_bucket[5m])) > 0.5
        for: 10m
        labels:
          severity: warning
          team: backend
        annotations:
          summary: "Slow Sentry event processing"
          description: "95th percentile processing time is {{ $value }}s"
          
      - alert: SentryHighFilterRate
        expr: rate(sentry_events_filtered_total[5m]) / rate(sentry_events_sent_total[5m]) > 0.5
        for: 10m
        labels:
          severity: warning
          team: backend
        annotations:
          summary: "High event filter rate"
          description: "{{ $value | humanizePercentage }} of events are being filtered"
          
      - alert: SentryBufferNearFull
        expr: sentry_buffer_size / 100 > 0.8
        for: 5m
        labels:
          severity: warning
          team: backend
        annotations:
          summary: "Sentry buffer near capacity"
          description: "Buffer for {{ $labels.handler_type }} is {{ $value }}% full"
```

### Шаг 6: Создать скрипт валидации

```python
# scripts/validate_sentry_integration.py
#!/usr/bin/env python
"""
Validate Sentry integration after deployment.
"""
import os
import sys
import django
import argparse
import json
import time
import requests
from datetime import datetime

# Django setup
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'settings.production')
django.setup()

from core.logging.validators import SentryConfigValidator
from core.logging import ContextLogger
import sentry_sdk


logger = ContextLogger.get_logger(__name__)


class SentryIntegrationValidator:
    """Comprehensive validation of Sentry integration."""
    
    def __init__(self, sentry_api_token=None):
        self.sentry_api_token = sentry_api_token
        self.validation_results = {
            'timestamp': datetime.now().isoformat(),
            'status': 'unknown',
            'checks': {},
            'summary': {}
        }
    
    def run_validation(self):
        """Run all validation checks."""
        print("🔍 Starting Sentry integration validation...\n")
        
        # 1. Конфигурация
        self._validate_configuration()
        
        # 2. Connectivity
        self._validate_connectivity()
        
        # 3. Функциональность
        self._validate_functionality()
        
        # 4. Performance
        self._validate_performance()
        
        # 5. API проверки
        if self.sentry_api_token:
            self._validate_sentry_api()
        
        # 6. Health endpoint
        self._validate_health_endpoint()
        
        # Итоговый статус
        self._calculate_overall_status()
        
        # Вывод результатов
        self._print_results()
        
        return self.validation_results
    
    def _validate_configuration(self):
        """Validate configuration."""
        print("1️⃣ Validating configuration...")
        
        validator = SentryConfigValidator()
        is_valid, results = validator.validate()
        
        self.validation_results['checks']['configuration'] = {
            'valid': is_valid,
            'errors': results['errors'],
            'warnings': results['warnings'],
            'info': results['info']
        }
        
        if is_valid:
            print("   ✅ Configuration valid")
        else:
            print("   ❌ Configuration has errors")
            for error in results['errors']:
                print(f"      - {error}")
    
    def _validate_connectivity(self):
        """Test connectivity to Sentry."""
        print("\n2️⃣ Testing Sentry connectivity...")
        
        test_id = f"validation_{int(time.time())}"
        
        try:
            # Отправляем тестовое событие
            with sentry_sdk.push_scope() as scope:
                scope.set_tag('validation', 'true')
                scope.set_extra('test_id', test_id)
                
                event_id = sentry_sdk.capture_message(
                    'Integration validation test',
                    level='info'
                )
            
            print(f"   ✅ Test event sent: {event_id}")
            
            self.validation_results['checks']['connectivity'] = {
                'success': True,
                'event_id': event_id,
                'test_id': test_id
            }
            
        except Exception as e:
            print(f"   ❌ Failed to send test event: {e}")
            self.validation_results['checks']['connectivity'] = {
                'success': False,
                'error': str(e)
            }
    
    def _validate_functionality(self):
        """Test core functionality."""
        print("\n3️⃣ Testing core functionality...")
        
        tests = {
            'error_logging': self._test_error_logging,
            'context_propagation': self._test_context_propagation,
            'filtering': self._test_filtering,
            'breadcrumbs': self._test_breadcrumbs,
        }
        
        results = {}
        
        for test_name, test_func in tests.items():
            try:
                print(f"   Testing {test_name}...", end='')
                test_func()
                print(" ✅")
                results[test_name] = {'success': True}
            except Exception as e:
                print(f" ❌ {str(e)}")
                results[test_name] = {'success': False, 'error': str(e)}
        
        self.validation_results['checks']['functionality'] = results
    
    def _test_error_logging(self):
        """Test error logging."""
        logger = ContextLogger.get_logger('validation.test')
        try:
            1 / 0
        except ZeroDivisionError:
            logger.exception("Test exception for validation")
    
    def _test_context_propagation(self):
        """Test context propagation."""
        from core.logging import set_context, get_context, clear_context
        
        set_context(
            validation_run=True,
            test_id='context_test',
            user_id=999
        )
        
        logger = ContextLogger.get_logger('validation.context')
        logger.info("Testing context propagation")
        
        ctx = get_context()
        assert ctx['validation_run'] is True
        assert ctx['test_id'] == 'context_test'
        
        clear_context()
    
    def _test_filtering(self):
        """Test sensitive data filtering."""
        logger = ContextLogger.get_logger('validation.filter')
        
        # Не должно попасть в Sentry
        logger.error(
            "User login with password=secret123",
            extra={'api_key': 'sk_test_123456'}
        )
    
    def _test_breadcrumbs(self):
        """Test breadcrumb functionality."""
        logger = ContextLogger.get_logger('validation.breadcrumbs')
        
        logger.info("Breadcrumb 1")
        logger.info("Breadcrumb 2")
        logger.warning("Breadcrumb warning")
        
        # Это должно включить breadcrumbs
        logger.error("Error with breadcrumbs")
    
    def _validate_performance(self):
        """Test performance characteristics."""
        print("\n4️⃣ Testing performance...")
        
        import statistics
        
        logger = ContextLogger.get_logger('validation.performance')
        times = []
        
        # Измеряем время логирования
        for i in range(100):
            start = time.perf_counter()
            logger.info(f"Performance test {i}", extra={'index': i})
            elapsed = time.perf_counter() - start
            times.append(elapsed * 1000)  # в миллисекундах
        
        avg_time = statistics.mean(times)
        p95_time = statistics.quantiles(times, n=20)[18]  # 95th percentile
        
        print(f"   Average logging time: {avg_time:.2f}ms")
        print(f"   95th percentile: {p95_time:.2f}ms")
        
        self.validation_results['checks']['performance'] = {
            'avg_ms': avg_time,
            'p95_ms': p95_time,
            'acceptable': p95_time < 10  # < 10ms
        }
        
        if p95_time < 10:
            print("   ✅ Performance acceptable")
        else:
            print("   ⚠️ Performance may be slow")
    
    def _validate_sentry_api(self):
        """Validate using Sentry API."""
        print("\n5️⃣ Checking Sentry API...")
        
        # Здесь должна быть реальная проверка через Sentry API
        # Например, проверка что события действительно приходят
        
        self.validation_results['checks']['api'] = {
            'skipped': True,
            'reason': 'API token required'
        }
        
        print("   ⏭️ Skipped (requires API token)")
    
    def _validate_health_endpoint(self):
        """Check health endpoint."""
        print("\n6️⃣ Checking health endpoint...")
        
        try:
            response = requests.get(
                'http://localhost:8000/health/sentry/',
                timeout=5
            )
            
            if response.status_code == 200:
                data = response.json()
                print(f"   ✅ Health status: {data['status']}")
                
                self.validation_results['checks']['health'] = {
                    'success': True,
                    'status': data['status'],
                    'checks': data['checks']
                }
            else:
                print(f"   ❌ Health check failed: {response.status_code}")
                self.validation_results['checks']['health'] = {
                    'success': False,
                    'status_code': response.status_code
                }
                
        except Exception as e:
            print(f"   ❌ Cannot reach health endpoint: {e}")
            self.validation_results['checks']['health'] = {
                'success': False,
                'error': str(e)
            }
    
    def _calculate_overall_status(self):
        """Calculate overall validation status."""
        checks = self.validation_results['checks']
        
        # Критические проверки
        critical_checks = ['configuration', 'connectivity']
        critical_passed = all(
            checks.get(check, {}).get('valid', False) or 
            checks.get(check, {}).get('success', False)
            for check in critical_checks
        )
        
        # Все проверки
        total_checks = len(checks)
        passed_checks = sum(
            1 for check in checks.values()
            if check.get('valid', False) or check.get('success', False)
        )
        
        if not critical_passed:
            self.validation_results['status'] = 'failed'
        elif passed_checks == total_checks:
            self.validation_results['status'] = 'passed'
        else:
            self.validation_results['status'] = 'partial'
        
        self.validation_results['summary'] = {
            'total_checks': total_checks,
            'passed_checks': passed_checks,
            'critical_passed': critical_passed
        }
    
    def _print_results(self):
        """Print validation results."""
        print("\n" + "="*50)
        print("📊 VALIDATION RESULTS")
        print("="*50)
        
        status = self.validation_results['status']
        summary = self.validation_results['summary']
        
        # Статус
        status_icon = {
            'passed': '✅',
            'partial': '⚠️',
            'failed': '❌'
        }[status]
        
        print(f"\nOverall Status: {status_icon} {status.upper()}")
        print(f"Checks Passed: {summary['passed_checks']}/{summary['total_checks']}")
        
        # Детали по каждой проверке
        print("\nDetailed Results:")
        for check_name, check_data in self.validation_results['checks'].items():
            if check_data.get('valid') or check_data.get('success'):
                print(f"  ✅ {check_name}")
            else:
                print(f"  ❌ {check_name}")
                if 'errors' in check_data:
                    for error in check_data['errors']:
                        print(f"     - {error}")
                elif 'error' in check_data:
                    print(f"     - {check_data['error']}")
        
        # Рекомендации
        if status != 'passed':
            print("\n💡 Recommendations:")
            if not summary['critical_passed']:
                print("  - Fix critical issues (configuration/connectivity) first")
            if 'configuration' in self.validation_results['checks']:
                config = self.validation_results['checks']['configuration']
                if config['warnings']:
                    print("  - Address configuration warnings for better reliability")


def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(
        description='Validate Sentry integration'
    )
    parser.add_argument(
        '--api-token',
        help='Sentry API token for advanced checks'
    )
    parser.add_argument(
        '--output',
        help='Output file for results (JSON)'
    )
    parser.add_argument(
        '--fail-on-warning',
        action='store_true',
        help='Fail validation on warnings'
    )
    
    args = parser.parse_args()
    
    # Запуск валидации
    validator = SentryIntegrationValidator(
        sentry_api_token=args.api_token
    )
    
    results = validator.run_validation()
    
    # Сохранение результатов
    if args.output:
        with open(args.output, 'w') as f:
            json.dump(results, f, indent=2)
        print(f"\n📄 Results saved to: {args.output}")
    
    # Exit code
    if results['status'] == 'passed':
        sys.exit(0)
    elif results['status'] == 'partial' and not args.fail_on_warning:
        sys.exit(0)
    else:
        sys.exit(1)


if __name__ == '__main__':
    main()
```

### Шаг 7: Создать runbook для поддержки

```markdown
# docs/sentry_monitoring_runbook.md

# Sentry Integration Monitoring Runbook

## Quick Status Check

```bash
# 1. Check health endpoint
curl http://localhost:8000/health/sentry/

# 2. Run validation script
python scripts/validate_sentry_integration.py

# 3. Check metrics
curl http://localhost:9090/metrics | grep sentry_
```

## Common Alerts and Actions

### 🚨 SentrySDKNotInitialized

**Symptoms:**
- No events in Sentry dashboard
- Health check returns "unhealthy"
- No active handlers in metrics

**Actions:**
1. Check SENTRY_DSN in environment:
   ```bash
   echo $SENTRY_DSN
   ```

2. Verify Django settings:
   ```python
   python manage.py shell
   >>> from django.conf import settings
   >>> print(settings.SENTRY_DSN)
   ```

3. Restart application:
   ```bash
   kubectl rollout restart deployment/app
   ```

4. Check logs for initialization errors:
   ```bash
   kubectl logs -l app=myapp | grep -i sentry
   ```

### ⚠️ SentryHighErrorRate

**Symptoms:**
- Error rate > 25%
- Spike in error events
- Performance degradation

**Actions:**
1. Check recent deployments:
   ```bash
   kubectl rollout history deployment/app
   ```

2. Analyze error patterns:
   ```python
   python scripts/analyze_sentry_errors.py --last-hour
   ```

3. Consider rollback if deployment-related:
   ```bash
   kubectl rollout undo deployment/app
   ```

4. Scale up if load-related:
   ```bash
   kubectl scale deployment/app --replicas=10
   ```

### ⚠️ SentrySlowProcessing

**Symptoms:**
- Event processing > 500ms
- Increased latency
- Buffer buildup

**Actions:**
1. Check buffer sizes:
   ```bash
   curl http://localhost:9090/metrics | grep buffer_size
   ```

2. Increase buffer flush frequency:
   ```python
   # In Django settings
   LOGGING['handlers']['sentry']['flush_interval'] = 2.0
   ```

3. Enable sampling if volume too high:
   ```python
   # Temporary measure
   sentry_sdk.init(
       traces_sample_rate=0.1,  # Sample 10%
   )
   ```

## Monitoring Dashboard

### Key Metrics to Watch

1. **Event Rate**
   - Normal: 10-100 events/min
   - Warning: >500 events/min
   - Critical: >1000 events/min

2. **Error Rate**
   - Normal: <5%
   - Warning: 5-10%
   - Critical: >10%

3. **Processing Time**
   - Normal: <100ms
   - Warning: 100-500ms
   - Critical: >500ms

4. **Buffer Usage**
   - Normal: <50%
   - Warning: 50-80%
   - Critical: >80%

### Grafana Queries

```promql
# Event rate by level
rate(sentry_events_sent_total[5m])

# Error percentage
100 * (
  rate(sentry_events_sent_total{level="ERROR"}[5m]) / 
  rate(sentry_events_sent_total[5m])
)

# Processing latency p95
histogram_quantile(0.95, 
  rate(sentry_event_processing_seconds_bucket[5m])
)

# Failed events
rate(sentry_events_sent_total{status="failed"}[5m])
```

## Troubleshooting

### No Events in Sentry

1. **Check SDK initialization:**
   ```python
   import sentry_sdk
   print(sentry_sdk.Hub.current.client)
   ```

2. **Verify network connectivity:**
   ```bash
   curl -I https://sentry.io
   ```

3. **Check for filtering:**
   ```python
   # See what's being filtered
   tail -f logs/app.log | grep "filtered"
   ```

### Memory Growth

1. **Check buffer sizes:**
   ```python
   from core.logging.handlers import BufferedSentryHandler
   for handler in logging.root.handlers:
       if isinstance(handler, BufferedSentryHandler):
           print(f"Buffer size: {len(handler.buffer)}")
   ```

2. **Force flush:**
   ```python
   import sentry_sdk
   sentry_sdk.flush()
   ```

3. **Reduce retention:**
   ```python
   # In settings
   SENTRY_MAX_BREADCRUMBS = 50  # Default is 100
   ```

### Performance Issues

1. **Enable profiling:**
   ```python
   import cProfile
   profiler = cProfile.Profile()
   profiler.enable()
   # ... logging operations ...
   profiler.disable()
   profiler.print_stats()
   ```

2. **Check synchronous operations:**
   ```bash
   # Look for blocking calls
   grep -r "sentry_sdk.capture" --include="*.py"
   ```

3. **Use async transport:**
   ```python
   sentry_sdk.init(
       transport=sentry_sdk.transport.HttpTransport,
       # or use background worker transport
   )
   ```

## Maintenance Tasks

### Daily
- [ ] Check health endpoint
- [ ] Review error rate trends
- [ ] Verify no critical alerts

### Weekly
- [ ] Run full validation script
- [ ] Review Sentry quota usage
- [ ] Check for any new patterns in filtered events

### Monthly
- [ ] Update filtering rules
- [ ] Review and tune rate limits
- [ ] Performance baseline review
- [ ] Update monitoring thresholds

## Emergency Procedures

### Complete Sentry Failure

1. **Disable Sentry temporarily:**
   ```python
   # Set environment variable
   SENTRY_ENABLED=false
   ```

2. **Switch to fallback logging:**
   ```python
   # Emergency logging config
   LOGGING['handlers']['file'] = {
       'class': 'logging.FileHandler',
       'filename': '/var/log/emergency.log',
   }
   ```

3. **Notify team:**
   ```bash
   # Send alert
   ./scripts/notify_team.sh "Sentry integration failed"
   ```

### Data Leak Detected

1. **Immediate actions:**
   ```bash
   # Disable all logging
   kubectl set env deployment/app SENTRY_ENABLED=false
   ```

2. **Clean Sentry data:**
   ```python
   # Use Sentry API to delete events
   python scripts/delete_sentry_events.py --since 1h
   ```

3. **Update filters:**
   ```python
   # Add pattern to sensitive data filter
   SENSITIVE_PATTERNS.append(r'new_pattern_to_filter')
   ```

## Contact Information

- **On-call Engineer**: See PagerDuty
- **Sentry Support**: <EMAIL>
- **Internal Team**: #backend-oncall (Slack)
```

## 🧪 Тестирование

### Команды для проверки мониторинга:

```bash
# Запустить валидацию
python scripts/validate_sentry_integration.py

# Проверить health endpoint
curl http://localhost:8000/health/sentry/ | jq

# Посмотреть метрики
curl http://localhost:9090/metrics | grep sentry_

# Запустить нагрузочный тест
python scripts/load_test_logging.py --duration 60 --rate 100

# Проверить дашборд
open http://localhost:3000/d/sentry-monitoring
```

## ⚠️ Потенциальные проблемы

### Проблема 1: Overhead от метрик
**Симптом**: Замедление приложения
**Решение**: Использовать sampling для метрик

### Проблема 2: False positive алерты
**Симптом**: Много ложных срабатываний
**Решение**: Настроить пороги based on baseline

### Проблема 3: Метрики не экспортируются
**Симптом**: Нет данных в Prometheus
**Решение**: Проверить endpoint и scraping

## 📊 Критерии успешного завершения
- ✅ Реализована система метрик
- ✅ Создан health check endpoint
- ✅ Разработаны валидаторы конфигурации
- ✅ Настроены дашборды мониторинга
- ✅ Созданы алерты для критических ситуаций
- ✅ Написан скрипт автоматической валидации
- ✅ Создан runbook для поддержки
- ✅ Документированы процедуры emergency response

## ⏱️ Оценка времени
**2.5 часа** - полная реализация системы мониторинга

## 🔗 Связанные задачи
- **Предыдущая**: 014_deployment_strategy.md
- **Основано на**: Всех предыдущих задачах (001-014)
- **Завершает**: Полный цикл интеграции Sentry

## 📝 Дополнительные заметки
- Мониторинг критичен для production
- Регулярно обновлять пороги алертов
- Автоматизировать валидацию в CI/CD
- Проводить регулярные учения по runbook