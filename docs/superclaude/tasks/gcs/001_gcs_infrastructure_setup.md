# Task 001: GCS Infrastructure Setup

**Приоритет**: High  
**Время выполнения**: 2 часа  
**Блокирует**: Tasks 003-007  
**Исполнитель**: DevOps / Backend Developer

## 📋 Описание задачи
Настроить Google Cloud Storage bucket и создать service account для хранения медиа файлов из Instagram. Bucket должен быть публичным для чтения, чтобы избежать сложности с signed URLs в MVP.

## ✅ Критерии приемки
1. GCS bucket создан и доступен
2. Service account создан с минимальными правами
3. JSON ключ скачан и сохранен безопасно
4. Переменные окружения настроены
5. Тестовый файл успешно загружен и доступен по публичному URL

## 📝 Пошаговая инструкция

### Шаг 1: Создание GCS Bucket (30 минут)

1. **Открыть Google Cloud Console**
   ```
   https://console.cloud.google.com/storage
   ```

2. **Создать новый bucket**
   - Нажать "CREATE BUCKET"
   - Name: `socialmanager-media-prod` (или другое уникальное имя)
   - Location type: `Multi-region`
   - Location: `us` (или ближайший регион)
   - Storage class: `Standard`
   - Access control: `Uniform`
   - Protection tools: оставить по умолчанию

3. **Настроить публичный доступ**
   ```bash
   # Через gcloud CLI:
   gcloud storage buckets update gs://socialmanager-media-prod --no-uniform-bucket-level-access
   
   # Или через Console:
   # 1. Перейти в Permissions
   # 2. Нажать "ADD PRINCIPAL"
   # 3. New principals: allUsers
   # 4. Role: Storage Object Viewer
   # 5. Save
   ```

4. **Создать структуру папок**
   ```bash
   # Создать папку для Instagram медиа
   gsutil mkdir gs://socialmanager-media-prod/instagram/
   ```

### Шаг 2: Создание Service Account (30 минут)

1. **Открыть IAM & Admin**
   ```
   https://console.cloud.google.com/iam-admin/serviceaccounts
   ```

2. **Создать service account**
   - Name: `socialmanager-gcs-uploader`
   - Description: `Service account for uploading media files to GCS`
   - Service account ID: будет сгенерирован автоматически

3. **Назначить роли**
   - Выбрать созданный service account
   - Нажать "GRANT ACCESS"
   - Добавить роль: `Storage Object Creator` (roles/storage.objectCreator)
   - НЕ добавлять роль Admin или Delete!

4. **Создать и скачать ключ**
   - Перейти в Keys
   - ADD KEY → Create new key
   - Key type: JSON
   - CREATE
   - Файл автоматически скачается

### Шаг 3: Настройка локального окружения (30 минут)

1. **Сохранить ключ безопасно**
   ```bash
   # Создать директорию для секретов
   mkdir -p ~/socialmanager/secrets/
   
   # Переместить ключ
   mv ~/Downloads/socialmanager-*.json ~/socialmanager/secrets/gcs-key.json
   
   # Установить правильные права
   chmod 600 ~/socialmanager/secrets/gcs-key.json
   ```

2. **Добавить в .env.local**
   ```bash
   # GCS Configuration
   GCS_BUCKET_NAME=socialmanager-media-prod
   GOOGLE_APPLICATION_CREDENTIALS=/Users/<USER>/socialmanager/secrets/gcs-key.json
   ```

3. **Обновить .env.example**
   ```bash
   # Добавить в конец файла:
   
   # Google Cloud Storage (optional)
   # GCS_BUCKET_NAME=your-bucket-name
   # GOOGLE_APPLICATION_CREDENTIALS=/path/to/service-account-key.json
   ```

4. **Добавить в .gitignore**
   ```bash
   # Убедиться что есть:
   *.json
   secrets/
   ```

### Шаг 4: Тестирование доступа (30 минут)

1. **Установить Google Cloud SDK (если еще нет)**
   ```bash
   # macOS
   brew install google-cloud-sdk
   
   # Linux
   curl https://sdk.cloud.google.com | bash
   ```

2. **Тест через gsutil**
   ```bash
   # Установить credentials
   export GOOGLE_APPLICATION_CREDENTIALS=~/socialmanager/secrets/gcs-key.json
   
   # Тест записи
   echo "Test file" > test.txt
   gsutil cp test.txt gs://socialmanager-media-prod/instagram/test.txt
   
   # Тест чтения
   curl https://storage.googleapis.com/socialmanager-media-prod/instagram/test.txt
   # Должен вернуть: "Test file"
   
   # Удалить тестовый файл
   gsutil rm gs://socialmanager-media-prod/instagram/test.txt
   ```

3. **Тест через Python**
   ```python
   # test_gcs_access.py
   import os
   from google.cloud import storage
   
   os.environ['GOOGLE_APPLICATION_CREDENTIALS'] = '/path/to/gcs-key.json'
   
   client = storage.Client()
   bucket = client.bucket('socialmanager-media-prod')
   
   # Test upload
   blob = bucket.blob('instagram/test_python.txt')
   blob.upload_from_string('Hello from Python!')
   
   print(f"Uploaded! URL: {blob.public_url}")
   
   # Test that URL works
   import requests
   response = requests.get(blob.public_url)
   print(f"Content: {response.text}")
   
   # Cleanup
   blob.delete()
   print("Deleted test file")
   ```

## 🚨 Важные моменты

1. **Безопасность**
   - НИКОГДА не коммитить JSON ключ в git
   - Использовать переменные окружения для путей
   - Service account должен иметь ТОЛЬКО права на создание объектов

2. **Naming Convention**
   - Bucket name должен быть глобально уникальным
   - Использовать префикс проекта: `socialmanager-`
   - Добавить суффикс окружения: `-prod`, `-staging`

3. **Публичный доступ**
   - В MVP используем публичный bucket для простоты
   - В будущем можно перейти на signed URLs
   - Убедиться что bucket действительно публичный для чтения

## 📊 Чек-лист для проверки

- [ ] Bucket создан и имя записано в .env
- [ ] Service account создан с правами `Storage Object Creator`
- [ ] JSON ключ скачан и сохранен в безопасном месте
- [ ] Путь к ключу добавлен в .env.local
- [ ] .env.example обновлен с примерами
- [ ] Тест через gsutil прошел успешно
- [ ] Тест через Python прошел успешно
- [ ] Публичный URL работает без аутентификации
- [ ] Документация обновлена с именем bucket

## 🔗 Результаты задачи

После выполнения задачи должны быть:
1. Рабочий GCS bucket: `socialmanager-media-prod`
2. Service account key: `~/socialmanager/secrets/gcs-key.json`
3. Обновленные файлы:
   - `.env.local` (с реальными значениями)
   - `.env.example` (с примерами)
4. Успешные тесты доступа

## ⏭️ Следующие шаги
После завершения этой задачи можно приступать к [Task 002: Database Migration](002_database_migration.md)