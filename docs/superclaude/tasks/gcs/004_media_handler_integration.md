# Task 004: MediaHandler Integration with GCS

**Приоритет**: High  
**Время выполнения**: 3 часа  
**Зависит от**: Task 003 (GCSService)  
**Блокирует**: Task 007 (Testing)  
**Исполнитель**: Backend Developer

## 📋 Описание задачи
Интегрировать GCSService в MediaHandler для опциональной загрузки медиа файлов в Google Cloud Storage. При включенной опции файлы должны загружаться в GCS, при ошибке - fallback на локальное хранилище.

## ✅ Критерии приемки
1. MediaHandler принимает параметр `save_to_gcs`
2. При `save_to_gcs=True` файлы загружаются в GCS
3. URL из GCS сохраняется в поле `gcs_url`
4. При ошибке GCS происходит fallback на локальное сохранение
5. Логирование всех операций
6. Тесты обновлены и проходят

## 📝 Пошаговая инструкция

### Шаг 1: Анализ текущего кода (30 минут)

1. **Изучить текущую реализацию MediaHandler**
   ```bash
   vim instagram_manager/instagram_api/data_handlers/media_handler.py
   ```

2. **Найти метод download_media**
   ```python
   # Текущая сигнатура (примерно)
   def download_media(self, media: InstagramMedia) -> bool:
       """Download media file and save locally."""
   ```

3. **Понять текущий флоу**
   - Скачивание через requests
   - Сохранение через ContentFile
   - Обновление полей модели
   - Обработка ошибок

4. **Найти где создается MediaHandler**
   ```bash
   # Поиск использования
   grep -r "MediaHandler(" instagram_manager/
   ```

### Шаг 2: Обновление конструктора (30 минут)

1. **Добавить параметр в __init__**
   ```python
   # instagram_manager/instagram_api/data_handlers/media_handler.py
   
   import logging
   from typing import Optional
   from django.conf import settings
   from django.core.files.base import ContentFile
   import requests
   
   # NEW: Import GCSService
   from core.storage.gcs_service import GCSService
   
   logger = logging.getLogger(__name__)
   
   
   class MediaHandler:
       """Handler for downloading and processing Instagram media."""
       
       def __init__(self, save_to_gcs: bool = False):
           """
           Initialize media handler.
           
           Args:
               save_to_gcs: If True, save media to Google Cloud Storage
           """
           self.save_to_gcs = save_to_gcs
           self._gcs_service: Optional[GCSService] = None
           
           # Initialize GCS service if needed
           if self.save_to_gcs and settings.GCS_BUCKET_NAME:
               try:
                   self._gcs_service = GCSService()
                   logger.info("GCS service initialized for media uploads")
               except Exception as e:
                   logger.warning(
                       f"Failed to initialize GCS service, "
                       f"will fallback to local storage: {e}"
                   )
                   self._gcs_service = None
   ```

### Шаг 3: Обновление метода download_media (1 час)

1. **Модифицировать основной метод**
   ```python
   def download_media(self, media: InstagramMedia) -> bool:
       """
       Download media file and save to GCS or locally.
       
       Args:
           media: InstagramMedia instance to download
           
       Returns:
           True if successful, False otherwise
       """
       if not media.media_url:
           logger.warning(f"No media URL for {media.external_id}")
           return False
       
       if media.is_downloaded:
           logger.info(f"Media {media.external_id} already downloaded")
           return True
       
       try:
           # Download file content
           logger.info(f"Downloading media from: {media.media_url}")
           response = requests.get(
               media.media_url,
               timeout=30,
               headers={
                   'User-Agent': 'Mozilla/5.0 (compatible; SocialManager/1.0)'
               }
           )
           response.raise_for_status()
           
           # Get file info
           content = response.content
           content_type = response.headers.get('Content-Type', 'image/jpeg')
           
           # Generate filename
           filename = self._generate_filename(media, content_type)
           
           # Try GCS upload first if enabled
           if self.save_to_gcs and self._gcs_service:
               gcs_url = self._try_gcs_upload(
                   content, 
                   filename, 
                   content_type, 
                   media
               )
               
               if gcs_url:
                   # Success - save GCS URL and mark as downloaded
                   media.gcs_url = gcs_url
                   media.is_downloaded = True
                   media.file_size = len(content)
                   media.save()
                   
                   logger.info(
                       f"Successfully uploaded to GCS: {media.external_id} "
                       f"-> {gcs_url}"
                   )
                   return True
               else:
                   # GCS failed, continue to local storage
                   logger.warning(
                       f"GCS upload failed for {media.external_id}, "
                       f"falling back to local storage"
                   )
           
           # Save locally (original logic)
           return self._save_locally(content, filename, media)
           
       except requests.RequestException as e:
           logger.error(f"Failed to download media {media.external_id}: {e}")
           media.download_error = str(e)
           media.save()
           return False
       except Exception as e:
           logger.error(
               f"Unexpected error downloading media {media.external_id}: {e}"
           )
           media.download_error = str(e)
           media.save()
           return False
   ```

2. **Добавить метод для GCS upload**
   ```python
   def _try_gcs_upload(
       self, 
       content: bytes, 
       filename: str, 
       content_type: str,
       media: InstagramMedia
   ) -> Optional[str]:
       """
       Try to upload file to GCS.
       
       Returns:
           GCS URL if successful, None if failed
       """
       try:
           # Use external_id as part of the filename for uniqueness
           gcs_filename = f"{media.external_id}_{filename}"
           
           # Upload to GCS
           gcs_url = self._gcs_service.upload_file(
               file_content=content,
               original_filename=gcs_filename,
               content_type=content_type,
               folder="instagram"
           )
           
           return gcs_url
           
       except Exception as e:
           logger.error(
               f"GCS upload failed for media {media.external_id}: {e}",
               exc_info=True
           )
           return None
   ```

3. **Добавить метод для локального сохранения**
   ```python
   def _save_locally(
       self, 
       content: bytes, 
       filename: str, 
       media: InstagramMedia
   ) -> bool:
       """Save file locally (existing logic extracted)."""
       try:
           file_content = ContentFile(content)
           media.local_path.save(filename, file_content)
           media.is_downloaded = True
           media.file_size = len(content)
           media.save()
           
           logger.info(
               f"Successfully saved locally: {media.external_id} "
               f"-> {media.local_path.name}"
           )
           return True
           
       except Exception as e:
           logger.error(
               f"Failed to save media locally {media.external_id}: {e}"
           )
           media.download_error = str(e)
           media.save()
           return False
   ```

4. **Добавить helper метод для имени файла**
   ```python
   def _generate_filename(
       self, 
       media: InstagramMedia, 
       content_type: str
   ) -> str:
       """Generate filename based on media type and external_id."""
       # Map content type to extension
       extension_map = {
           'image/jpeg': '.jpg',
           'image/png': '.png',
           'image/gif': '.gif',
           'image/webp': '.webp',
           'video/mp4': '.mp4',
           'video/quicktime': '.mov',
       }
       
       ext = extension_map.get(content_type, '.jpg')
       
       # Use external_id for unique filename
       return f"{media.external_id}{ext}"
   ```

### Шаг 4: Обновление PostHandler (30 минут)

1. **Найти где PostHandler использует MediaHandler**
   ```bash
   vim instagram_manager/instagram_api/data_handlers/post_handler.py
   ```

2. **Обновить создание MediaHandler**
   ```python
   # В методе где обрабатываются медиа файлы
   def process_post_media(
       self, 
       post: InstagramPost, 
       media_data: list,
       download_media: bool = True,
       save_to_gcs: bool = False  # NEW parameter
   ):
       """Process media files for a post."""
       if not download_media:
           return
       
       # Create media handler with GCS flag
       media_handler = MediaHandler(save_to_gcs=save_to_gcs)
       
       for media_item in media_data:
           # ... existing logic to create InstagramMedia
           
           # Download media
           media_handler.download_media(media)
   ```

### Шаг 5: Создание тестов (1 час)

1. **Обновить тесты MediaHandler**
   ```python
   # tests/instagram_manager/test_media_handler.py
   
   import pytest
   from unittest.mock import Mock, patch, MagicMock
   from django.test import override_settings
   
   from instagram_manager.models import InstagramMedia
   from instagram_manager.instagram_api.data_handlers.media_handler import MediaHandler
   
   
   class TestMediaHandlerGCS:
       """Test MediaHandler with GCS integration."""
       
       @pytest.fixture
       def mock_media(self):
           """Create mock InstagramMedia."""
           media = Mock(spec=InstagramMedia)
           media.external_id = "123456"
           media.media_url = "https://instagram.com/media.jpg"
           media.is_downloaded = False
           media.save = Mock()
           return media
       
       @override_settings(GCS_BUCKET_NAME='test-bucket')
       @patch('instagram_manager.instagram_api.data_handlers.media_handler.GCSService')
       @patch('requests.get')
       def test_download_with_gcs_success(self, mock_get, mock_gcs_class):
           """Test successful GCS upload."""
           # Setup mocks
           mock_response = Mock()
           mock_response.content = b'image data'
           mock_response.headers = {'Content-Type': 'image/jpeg'}
           mock_response.raise_for_status = Mock()
           mock_get.return_value = mock_response
           
           mock_gcs = Mock()
           mock_gcs.upload_file.return_value = 'https://storage.googleapis.com/bucket/file.jpg'
           mock_gcs_class.return_value = mock_gcs
           
           # Test
           handler = MediaHandler(save_to_gcs=True)
           media = self.mock_media
           
           result = handler.download_media(media)
           
           # Assertions
           assert result is True
           assert media.gcs_url == 'https://storage.googleapis.com/bucket/file.jpg'
           assert media.is_downloaded is True
           assert media.save.called
           
           # GCS should be called
           mock_gcs.upload_file.assert_called_once()
           
       @override_settings(GCS_BUCKET_NAME='test-bucket')
       @patch('instagram_manager.instagram_api.data_handlers.media_handler.GCSService')
       @patch('requests.get')
       def test_download_with_gcs_fallback(self, mock_get, mock_gcs_class):
           """Test fallback to local when GCS fails."""
           # Setup mocks
           mock_response = Mock()
           mock_response.content = b'image data'
           mock_response.headers = {'Content-Type': 'image/jpeg'}
           mock_get.return_value = mock_response
           
           # GCS fails
           mock_gcs = Mock()
           mock_gcs.upload_file.side_effect = Exception("GCS Error")
           mock_gcs_class.return_value = mock_gcs
           
           # Test
           handler = MediaHandler(save_to_gcs=True)
           media = self.mock_media
           media.local_path = Mock()
           
           result = handler.download_media(media)
           
           # Should fallback to local
           assert result is True
           assert media.local_path.save.called
           assert media.is_downloaded is True
           assert not hasattr(media, 'gcs_url') or media.gcs_url is None
       
       def test_download_without_gcs(self):
           """Test normal download without GCS."""
           handler = MediaHandler(save_to_gcs=False)
           assert handler._gcs_service is None
   ```

## 🚨 Важные моменты

1. **Fallback Strategy**
   - ВСЕГДА пытаться локальное сохранение при ошибке GCS
   - Не прерывать процесс импорта из-за GCS
   - Логировать все ошибки для отладки

2. **Backward Compatibility**
   - По умолчанию save_to_gcs=False
   - Существующий код продолжает работать
   - Новый функционал опциональный

3. **Error Handling**
   - GCS ошибки не должны ломать импорт
   - Детальное логирование для troubleshooting
   - Сохранение ошибок в download_error

4. **Performance**
   - Не делать retry для GCS (MVP)
   - Быстрый fallback при ошибке
   - Таймауты для HTTP запросов

## 📊 Чек-лист для проверки

- [ ] MediaHandler обновлен с параметром save_to_gcs
- [ ] GCSService импортируется и инициализируется
- [ ] download_media поддерживает GCS upload
- [ ] Fallback на локальное хранение работает
- [ ] Логирование добавлено для всех сценариев
- [ ] Helper методы созданы и работают
- [ ] Unit тесты написаны и проходят
- [ ] Backward compatibility сохранена

## 🔗 Результаты задачи

После выполнения:
1. MediaHandler с поддержкой GCS
2. Рабочий fallback механизм
3. Обновленные тесты
4. Готовность к интеграции с UI

## ⏭️ Следующие шаги
После завершения интеграции MediaHandler можно приступать к [Task 005: Form UI Updates](005_form_ui_updates.md)