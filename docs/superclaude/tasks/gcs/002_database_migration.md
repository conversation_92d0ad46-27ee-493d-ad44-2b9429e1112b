# Task 002: Database Migration - Add GCS URL Field

**Приоритет**: High  
**Время выполнения**: 1 час  
**Зависит от**: Task 001 (для тестирования)  
**Блокирует**: Tasks 003-006  
**Исполнитель**: Backend Developer

## 📋 Описание задачи
Добавить поле `gcs_url` в модель `InstagramMedia` для хранения URL файлов загруженных в Google Cloud Storage. Также обновить метод `get_display_url()` для приоритетного возврата GCS URL.

## ✅ Критерии приемки
1. Поле `gcs_url` добавлено в модель InstagramMedia
2. Миграция создана и применена успешно
3. Метод `get_display_url()` возвращает GCS URL в первую очередь
4. Существующие данные не затронуты
5. Admin отображение работает корректно

## 📝 Пошаговая инструкция

### Шаг 1: Обновление модели (15 минут)

1. **Открыть файл модели**
   ```bash
   # Открыть в редакторе
   vim instagram_manager/models.py
   ```

2. **Найти класс InstagramMedia**
   ```python
   # Искать около строки 200-300
   class InstagramMedia(SocialMediaMedia):
   ```

3. **Добавить новое поле после существующих полей**
   ```python
   class InstagramMedia(SocialMediaMedia):
       # ... существующие поля ...
       
       # GCS Integration (MVP)
       gcs_url = models.CharField(
           max_length=1024,
           null=True,
           blank=True,
           help_text="Google Cloud Storage URL for the media file",
           verbose_name="GCS URL"
       )
   ```

4. **Обновить метод get_display_url()**
   ```python
   def get_display_url(self) -> str:
       """
       Returns the URL to display the media.
       Priority: GCS URL > Local file > External URL
       """
       # NEW: Check GCS URL first
       if self.gcs_url:
           return self.gcs_url
       
       # Existing logic
       if self.is_downloaded and self.local_path:
           return self.local_path.url
       return self.media_url
   ```

5. **Обновить __str__ метод (опционально)**
   ```python
   def __str__(self):
       storage = "GCS" if self.gcs_url else ("Local" if self.is_downloaded else "External")
       return f"Media {self.external_id} ({storage})"
   ```

### Шаг 2: Создание миграции (10 минут)

1. **Активировать виртуальное окружение**
   ```bash
   # С использованием uv
   source .venv/bin/activate
   ```

2. **Создать миграцию**
   ```bash
   # Создать миграцию с понятным именем
   python manage.py makemigrations instagram_manager \
       --name add_gcs_url_field
   ```

3. **Проверить созданную миграцию**
   ```bash
   # Посмотреть что будет сделано
   python manage.py sqlmigrate instagram_manager 00XX
   ```

   Ожидаемый SQL:
   ```sql
   ALTER TABLE "instagram_media" 
   ADD COLUMN "gcs_url" varchar(1024) NULL;
   ```

4. **Проверить содержимое файла миграции**
   ```bash
   # Открыть последнюю миграцию
   ls -la instagram_manager/migrations/
   vim instagram_manager/migrations/00XX_add_gcs_url_field.py
   ```

   Должно быть:
   ```python
   class Migration(migrations.Migration):
       dependencies = [
           ('instagram_manager', '00XX_previous_migration'),
       ]

       operations = [
           migrations.AddField(
               model_name='instagrammedia',
               name='gcs_url',
               field=models.CharField(
                   blank=True, 
                   help_text='Google Cloud Storage URL for the media file',
                   max_length=1024, 
                   null=True, 
                   verbose_name='GCS URL'
               ),
           ),
       ]
   ```

### Шаг 3: Применение миграции (10 минут)

1. **Бэкап базы данных (для безопасности)**
   ```bash
   # Для PostgreSQL
   pg_dump socialmanager > backup_before_gcs_$(date +%Y%m%d_%H%M%S).sql
   ```

2. **Применить миграцию**
   ```bash
   python manage.py migrate instagram_manager
   ```

   Ожидаемый вывод:
   ```
   Operations to perform:
     Apply all migrations: instagram_manager
   Running migrations:
     Applying instagram_manager.00XX_add_gcs_url_field... OK
   ```

3. **Проверить что поле создано**
   ```bash
   python manage.py dbshell
   ```
   
   ```sql
   -- В PostgreSQL
   \d instagram_media;
   -- Должно показать новое поле gcs_url
   
   -- Проверить что поле пустое для существующих записей
   SELECT COUNT(*) FROM instagram_media WHERE gcs_url IS NOT NULL;
   -- Должно вернуть 0
   ```

### Шаг 4: Обновление Admin (10 минут)

1. **Открыть admin.py**
   ```bash
   vim instagram_manager/admin.py
   ```

2. **Найти InstagramMediaAdmin**
   ```python
   @admin.register(InstagramMedia)
   class InstagramMediaAdmin(admin.ModelAdmin):
   ```

3. **Добавить gcs_url в отображение**
   ```python
   class InstagramMediaAdmin(admin.ModelAdmin):
       list_display = [
           'id', 'post', 'media_type', 'is_downloaded',
           'has_gcs',  # NEW
           'created_at'
       ]
       
       list_filter = [
           'media_type', 'is_downloaded',
           'has_gcs',  # NEW
           'created_at'
       ]
       
       readonly_fields = [
           'external_id', 'media_url', 'gcs_url',  # Added gcs_url
           'width', 'height', 'file_size'
       ]
       
       # NEW: Add method to show GCS status
       def has_gcs(self, obj):
           return bool(obj.gcs_url)
       has_gcs.boolean = True
       has_gcs.short_description = 'In GCS'
       
       # Optional: Add to fieldsets
       fieldsets = (
           ('Identification', {
               'fields': ('external_id', 'post', 'media_type')
           }),
           ('URLs', {
               'fields': ('media_url', 'gcs_url', 'local_path')
           }),
           # ... other fieldsets
       )
   ```

### Шаг 5: Тестирование (15 минут)

1. **Тест в Django Shell**
   ```python
   python manage.py shell
   
   # Import модели
   from instagram_manager.models import InstagramMedia
   
   # Проверить что поле существует
   media = InstagramMedia.objects.first()
   print(media.gcs_url)  # Should print None
   
   # Тест get_display_url с разными сценариями
   # Сценарий 1: Только external URL
   media.gcs_url = None
   media.is_downloaded = False
   print(media.get_display_url())  # Should return media_url
   
   # Сценарий 2: Локальный файл
   media.gcs_url = None
   media.is_downloaded = True
   print(media.get_display_url())  # Should return local_path.url
   
   # Сценарий 3: GCS URL (приоритет)
   media.gcs_url = "https://storage.googleapis.com/test/file.jpg"
   print(media.get_display_url())  # Should return GCS URL
   
   # Не сохраняем изменения
   exit()
   ```

2. **Тест через Admin**
   ```bash
   python manage.py runserver
   ```
   
   - Открыть http://localhost:8000/admin/
   - Перейти в Instagram Media
   - Проверить что новая колонка "In GCS" отображается
   - Открыть любую запись
   - Проверить что поле GCS URL отображается

3. **Тест обратной совместимости**
   ```python
   # Убедиться что старый код работает
   from instagram_manager.services import MediaService
   
   # Должно работать без ошибок
   service = MediaService()
   media_list = service.get_all()[:5]
   
   for media in media_list:
       url = media.get_display_url()
       print(f"{media.id}: {url}")
   ```

## 🚨 Важные моменты

1. **Обратная совместимость**
   - Поле должно быть nullable для существующих записей
   - get_display_url() должен работать для всех сценариев
   - Не ломать существующую логику

2. **Размер поля**
   - 1024 символов достаточно для GCS URLs
   - Типичный GCS URL: ~100-150 символов

3. **Производительность**
   - Добавление поля не требует переиндексации
   - get_display_url() использует простую логику if/else

## 📊 Чек-лист для проверки

- [ ] Поле gcs_url добавлено в модель
- [ ] Миграция создана с понятным именем
- [ ] Миграция применена без ошибок
- [ ] get_display_url() обновлен с приоритетом GCS
- [ ] Admin отображает новое поле
- [ ] Shell тесты пройдены
- [ ] Существующие данные не затронуты
- [ ] Обратная совместимость сохранена

## 🔗 Результаты задачи

После выполнения:
1. Новое поле `gcs_url` в таблице `instagram_media`
2. Обновленная модель с поддержкой GCS
3. Рабочий метод get_display_url() с приоритетами
4. Обновленный admin интерфейс

## ⏭️ Следующие шаги
После завершения миграции можно приступать к [Task 003: GCSService Implementation](003_gcs_service_implementation.md)