# Task 005: Form UI Updates - Add GCS Checkbox

**Приоритет**: Medium  
**Время выполнения**: 2 часа  
**Зависит от**: Task 002 (для настроек)  
**Блокирует**: Task 006  
**Исполнитель**: Backend Developer

## 📋 Описание задачи
Добавить чекбокс "Save media to Google Cloud Storage" в формы batch импорта постов. Чекбокс должен отображаться только когда GCS настроен.

## ✅ Критерии приемки
1. Чекбокс добавлен в MediaDownloadOptionsMixin
2. Чекбокс скрыт если GCS не настроен
3. Чекбокс отображается во всех формах импорта
4. Help text понятный для пользователя
5. Стили соответствуют остальным полям формы

## 📝 Пошаговая инструкция

### Шаг 1: Анализ существующих форм (20 минут)

1. **Найти MediaDownloadOptionsMixin**
   ```bash
   # Поиск mixin
   find . -name "*.py" -exec grep -l "MediaDownloadOptionsMixin" {} \;
   
   # Открыть файл
   vim core/forms/mixins.py
   ```

2. **Изучить текущую структуру**
   ```python
   # Текущий mixin (примерно)
   class MediaDownloadOptionsMixin:
       download_media = forms.BooleanField(
           required=False,
           initial=True,
           label="Download media files"
       )
   ```

3. **Найти где используется mixin**
   ```bash
   grep -r "MediaDownloadOptionsMixin" instagram_manager/
   ```
   
   Ожидаемые формы:
   - BatchImportPostsForm
   - ImportPostsForm
   - И другие формы импорта

### Шаг 2: Обновление MediaDownloadOptionsMixin (30 минут)

1. **Добавить новое поле**
   ```python
   # core/forms/mixins.py
   
   from django import forms
   from django.conf import settings
   
   
   class MediaDownloadOptionsMixin:
       """Mixin for forms that handle media download options."""
       
       download_media = forms.BooleanField(
           required=False,
           initial=True,
           label="Download media files",
           help_text="Download and store media files locally"
       )
       
       # NEW: GCS option
       save_media_to_gcs = forms.BooleanField(
           required=False,
           initial=False,
           label="Save media to Google Cloud Storage",
           help_text="Store media files in GCS instead of local storage (requires GCS configuration)"
       )
       
       def __init__(self, *args, **kwargs):
           super().__init__(*args, **kwargs)
           
           # Hide GCS checkbox if not configured
           if not getattr(settings, 'GCS_BUCKET_NAME', ''):
               # Option 1: Completely remove field
               # self.fields.pop('save_media_to_gcs', None)
               
               # Option 2: Hide with HiddenInput (preferred for consistency)
               self.fields['save_media_to_gcs'].widget = forms.HiddenInput()
               self.fields['save_media_to_gcs'].initial = False
           
           # Make GCS option dependent on download_media
           self._setup_field_dependencies()
       
       def _setup_field_dependencies(self):
           """Setup JavaScript dependencies between fields."""
           if 'save_media_to_gcs' in self.fields and 'download_media' in self.fields:
               # Add data attributes for JavaScript
               self.fields['save_media_to_gcs'].widget.attrs.update({
                   'data-depends-on': 'id_download_media',
                   'data-depends-on-value': 'true'
               })
       
       def clean(self):
           """Validate form data."""
           cleaned_data = super().clean()
           
           # If not downloading media, can't save to GCS
           if not cleaned_data.get('download_media') and cleaned_data.get('save_media_to_gcs'):
               cleaned_data['save_media_to_gcs'] = False
               
           return cleaned_data
   ```

### Шаг 3: Добавление JavaScript для зависимостей (30 минут)

1. **Создать JavaScript файл**
   ```bash
   # Создать директорию если нет
   mkdir -p static/js/forms/
   
   # Создать файл
   touch static/js/forms/media-options.js
   ```

2. **Написать JavaScript логику**
   ```javascript
   // static/js/forms/media-options.js
   
   document.addEventListener('DOMContentLoaded', function() {
       'use strict';
       
       // Handle field dependencies
       function setupFieldDependencies() {
           const dependentFields = document.querySelectorAll('[data-depends-on]');
           
           dependentFields.forEach(field => {
               const dependsOn = field.getAttribute('data-depends-on');
               const dependsOnValue = field.getAttribute('data-depends-on-value');
               const parentField = document.getElementById(dependsOn);
               
               if (!parentField) return;
               
               // Function to update visibility
               function updateVisibility() {
                   const parentValue = parentField.type === 'checkbox' 
                       ? parentField.checked.toString() 
                       : parentField.value;
                   
                   const shouldShow = parentValue === dependsOnValue;
                   const fieldRow = field.closest('.form-row, .form-group');
                   
                   if (fieldRow) {
                       fieldRow.style.display = shouldShow ? '' : 'none';
                       
                       // Also disable field when hidden
                       field.disabled = !shouldShow;
                       
                       // Uncheck if hidden
                       if (!shouldShow && field.type === 'checkbox') {
                           field.checked = false;
                       }
                   }
               }
               
               // Initial state
               updateVisibility();
               
               // Listen for changes
               parentField.addEventListener('change', updateVisibility);
           });
       }
       
       // Initialize
       setupFieldDependencies();
       
       // Also handle dynamic form additions (if using formsets)
       document.addEventListener('formset:added', setupFieldDependencies);
   });
   ```

3. **Добавить стили (опционально)**
   ```css
   /* static/css/forms/media-options.css */
   
   /* Smooth transitions for showing/hiding fields */
   .form-row, .form-group {
       transition: opacity 0.3s ease;
   }
   
   /* Style for GCS checkbox when visible */
   .field-save_media_to_gcs {
       margin-left: 20px; /* Indent to show dependency */
   }
   
   /* Help text styling */
   .field-save_media_to_gcs .help {
       color: #666;
       font-size: 0.9em;
   }
   ```

### Шаг 4: Обновление шаблонов форм (30 минут)

1. **Найти шаблоны форм импорта**
   ```bash
   find templates -name "*import*.html" | grep instagram
   ```

2. **Обновить базовый шаблон формы**
   ```django
   {# templates/instagram_manager/admin/batch_import_posts.html #}
   
   {% extends "admin/base_site.html" %}
   {% load static %}
   
   {% block extrahead %}
       {{ block.super }}
       <script src="{% static 'js/forms/media-options.js' %}"></script>
       <link rel="stylesheet" href="{% static 'css/forms/media-options.css' %}">
   {% endblock %}
   
   {% block content %}
       <form method="post" novalidate>
           {% csrf_token %}
           
           <fieldset class="module aligned">
               <h2>Import Options</h2>
               
               {# Profile selection fields... #}
               
               <div class="form-row field-download_media">
                   {{ form.download_media.errors }}
                   {{ form.download_media.label_tag }}
                   {{ form.download_media }}
                   {% if form.download_media.help_text %}
                       <div class="help">{{ form.download_media.help_text }}</div>
                   {% endif %}
               </div>
               
               {% if form.save_media_to_gcs.widget.input_type != 'hidden' %}
                   <div class="form-row field-save_media_to_gcs">
                       {{ form.save_media_to_gcs.errors }}
                       {{ form.save_media_to_gcs.label_tag }}
                       {{ form.save_media_to_gcs }}
                       {% if form.save_media_to_gcs.help_text %}
                           <div class="help">{{ form.save_media_to_gcs.help_text }}</div>
                       {% endif %}
                       
                       {% if settings.GCS_BUCKET_NAME %}
                           <div class="help">
                               <strong>Bucket:</strong> {{ settings.GCS_BUCKET_NAME }}
                           </div>
                       {% endif %}
                   </div>
               {% else %}
                   {# Hidden field still needs to be included #}
                   {{ form.save_media_to_gcs }}
               {% endif %}
               
               {# Other form fields... #}
           </fieldset>
           
           <div class="submit-row">
               <input type="submit" value="Start Import" class="default">
           </div>
       </form>
   {% endblock %}
   ```

### Шаг 5: Тестирование UI (40 минут)

1. **Тест с настроенным GCS**
   ```bash
   # Убедиться что GCS настроен
   export GCS_BUCKET_NAME="test-bucket"
   
   # Запустить сервер
   python manage.py runserver
   ```
   
   Проверить:
   - [ ] Чекбокс отображается
   - [ ] Help text читаемый
   - [ ] Чекбокс скрывается при снятии "Download media"
   - [ ] Значение сохраняется при submit

2. **Тест без GCS**
   ```bash
   # Убрать настройку
   unset GCS_BUCKET_NAME
   
   # Перезапустить сервер
   python manage.py runserver
   ```
   
   Проверить:
   - [ ] Чекбокс НЕ отображается
   - [ ] Форма работает как раньше
   - [ ] Нет JavaScript ошибок

3. **Тест отправки формы**
   ```python
   # В Django shell проверить что значения приходят
   
   # С GCS
   form = BatchImportPostsForm(data={
       'profiles': [1],
       'download_media': True,
       'save_media_to_gcs': True,
       'limit': 10
   })
   assert form.is_valid()
   assert form.cleaned_data['save_media_to_gcs'] is True
   
   # Без download_media
   form = BatchImportPostsForm(data={
       'profiles': [1],
       'download_media': False,
       'save_media_to_gcs': True,  # Should be ignored
       'limit': 10
   })
   assert form.is_valid()
   assert form.cleaned_data['save_media_to_gcs'] is False
   ```

## 🚨 Важные моменты

1. **Progressive Enhancement**
   - Форма работает без JavaScript
   - JavaScript улучшает UX
   - Серверная валидация обязательна

2. **Условное отображение**
   - Показывать только если GCS настроен
   - Зависимость от download_media
   - Не ломать существующие формы

3. **User Experience**
   - Понятные labels и help text
   - Визуальная индикация зависимости
   - Плавные переходы

4. **Безопасность**
   - Не показывать sensitive данные (credentials)
   - Показать только имя bucket (опционально)
   - Валидация на сервере

## 📊 Чек-лист для проверки

- [ ] MediaDownloadOptionsMixin обновлен
- [ ] Новое поле save_media_to_gcs добавлено
- [ ] Поле скрывается если GCS не настроен
- [ ] JavaScript для зависимостей работает
- [ ] Шаблоны обновлены
- [ ] Форма отправляется корректно
- [ ] Backward compatibility сохранена
- [ ] Нет визуальных глюков

## 🔗 Результаты задачи

После выполнения:
1. Обновленный MediaDownloadOptionsMixin
2. Рабочий чекбокс в формах импорта
3. JavaScript для улучшения UX
4. Готовность к интеграции с views

## ⏭️ Следующие шаги
После завершения обновления форм можно приступать к [Task 006: View and Task Updates](006_view_task_integration.md)