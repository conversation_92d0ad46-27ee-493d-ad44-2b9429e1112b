# Task 006: View and Task Integration

**Приоритет**: High  
**Время выполнения**: 2 часа  
**Зависит от**: Task 005 (Form UI)  
**Блокирует**: Task 007 (Testing)  
**Исполнитель**: Backend Developer

## 📋 Описание задачи
Обновить views и Celery tasks чтобы передавать флаг `save_to_gcs` от формы через всю цепочку до MediaHandler. Обеспечить правильную передачу параметра через все слои.

## ✅ Критерии приемки
1. View извлекает `save_to_gcs` из формы
2. Параметр передается в Celery task
3. Task передает параметр в MediaHandler
4. Логирование на каждом этапе
5. Backward compatibility сохранена

## 📝 Пошаговая инструкция

### Шаг 1: Анализ текущего флоу (20 минут)

1. **Найти view для batch import**
   ```bash
   # Поиск admin views
   find instagram_manager -name "admin_views.py" -o -name "views.py" | xargs grep -l "batch.*import"
   
   # Открыть файл
   vim instagram_manager/views/admin_views.py
   ```

2. **Проследить флоу данных**
   ```
   Form (UI) 
     ↓ save_to_gcs checkbox
   View (admin_batch_import_posts_do_import)
     ↓ form.cleaned_data
   Task Data (словарь)
     ↓ celery.send_task
   Celery Task (ImportBatchPostsTask)
     ↓ execute(**kwargs)
   PostHandler
     ↓ process with MediaHandler
   MediaHandler(save_to_gcs=True)
   ```

3. **Найти все точки изменения**
   - admin_views.py - извлечь из формы
   - import_tasks.py - принять параметр
   - post_handler.py - передать в MediaHandler

### Шаг 2: Обновление View (30 минут)

1. **Найти метод обработки формы**
   ```python
   # instagram_manager/views/admin_views.py
   
   def admin_batch_import_posts_do_import(request):
       """Process batch import form submission."""
   ```

2. **Добавить извлечение параметра**
   ```python
   def admin_batch_import_posts_do_import(request):
       """Process batch import form submission."""
       if request.method == 'POST':
           form = BatchImportPostsForm(request.POST)
           
           if form.is_valid():
               # Extract form data
               profiles = form.cleaned_data['profiles']
               limit = form.cleaned_data.get('limit', 10)
               download_media = form.cleaned_data.get('download_media', True)
               
               # NEW: Extract GCS flag
               save_to_gcs = form.cleaned_data.get('save_media_to_gcs', False)
               
               # Log the decision
               logger.info(
                   f"Starting batch import: profiles={len(profiles)}, "
                   f"limit={limit}, download_media={download_media}, "
                   f"save_to_gcs={save_to_gcs}"
               )
               
               # Prepare task data
               task_data = {
                   'profile_ids': [p.id for p in profiles],
                   'limit': limit,
                   'download_media': download_media,
                   'save_to_gcs': save_to_gcs,  # NEW
                   'user_id': request.user.id,
                   'started_at': timezone.now().isoformat(),
               }
               
               # Send to Celery
               from instagram_manager.tasks import ImportBatchPostsTask
               task = ImportBatchPostsTask()
               result = task.run_async(**task_data)
               
               # Success message
               messages.success(
                   request,
                   f"Import started for {len(profiles)} profiles. "
                   f"Task ID: {result.id}"
               )
               
               # Add note about GCS if enabled
               if save_to_gcs:
                   messages.info(
                       request,
                       "Media files will be saved to Google Cloud Storage."
                   )
               
               return redirect('admin:instagram_manager_instagrampost_changelist')
   ```

3. **Обновить другие import views (если есть)**
   ```bash
   # Найти другие views с импортом
   grep -r "ImportPostsTask\|ImportCommentsTask" instagram_manager/views/
   ```
   
   Повторить для каждого найденного view.

### Шаг 3: Обновление Celery Tasks (40 минут)

1. **Обновить ImportBatchPostsTask**
   ```python
   # instagram_manager/tasks/import_tasks.py
   
   from core.tasks.base import BaseTask
   from instagram_manager.services import PostService
   from instagram_manager.instagram_api.data_handlers import PostHandler
   
   
   class ImportBatchPostsTask(BaseTask):
       """Task for importing posts for multiple profiles."""
       
       name = "import_batch_posts"
       description = "Import Instagram posts for multiple profiles"
       
       def execute(
           self,
           profile_ids: list,
           limit: int = 10,
           download_media: bool = True,
           save_to_gcs: bool = False,  # NEW parameter
           user_id: int = None,
           **kwargs
       ):
           """Execute batch import."""
           self.logger.info(
               f"Starting batch import: profiles={len(profile_ids)}, "
               f"limit={limit}, download_media={download_media}, "
               f"save_to_gcs={save_to_gcs}"
           )
           
           results = {
               'success': 0,
               'failed': 0,
               'profiles': []
           }
           
           # Process each profile
           for i, profile_id in enumerate(profile_ids):
               try:
                   self.update_progress(
                       current=i,
                       total=len(profile_ids),
                       message=f"Processing profile {profile_id}"
                   )
                   
                   # Import posts for profile
                   profile_result = self._import_profile_posts(
                       profile_id=profile_id,
                       limit=limit,
                       download_media=download_media,
                       save_to_gcs=save_to_gcs  # NEW: Pass parameter
                   )
                   
                   results['profiles'].append(profile_result)
                   results['success'] += 1
                   
               except Exception as e:
                   self.logger.error(
                       f"Failed to import posts for profile {profile_id}: {e}"
                   )
                   results['failed'] += 1
                   results['profiles'].append({
                       'profile_id': profile_id,
                       'error': str(e)
                   })
           
           return results
       
       def _import_profile_posts(
           self,
           profile_id: int,
           limit: int,
           download_media: bool,
           save_to_gcs: bool  # NEW parameter
       ):
           """Import posts for a single profile."""
           from instagram_manager.models import InstagramProfile
           
           # Get profile
           profile = InstagramProfile.objects.get(id=profile_id)
           
           # Create handler with GCS flag
           post_handler = PostHandler(
               download_media=download_media,
               save_to_gcs=save_to_gcs  # NEW: Pass to handler
           )
           
           # Import posts
           posts = post_handler.import_posts_for_profile(
               profile=profile,
               limit=limit
           )
           
           return {
               'profile_id': profile_id,
               'username': profile.username,
               'posts_imported': len(posts),
               'media_in_gcs': save_to_gcs
           }
   ```

2. **Обновить другие import tasks**
   ```python
   class ImportInstagramPostsTask(BaseTask):
       """Task for importing posts for a single profile."""
       
       def execute(
           self,
           profile_id: int,
           limit: int = 10,
           download_media: bool = True,
           save_to_gcs: bool = False,  # NEW
           **kwargs
       ):
           """Execute import for single profile."""
           # Similar updates...
   ```

### Шаг 4: Обновление PostHandler (30 минут)

1. **Модифицировать конструктор PostHandler**
   ```python
   # instagram_manager/instagram_api/data_handlers/post_handler.py
   
   class PostHandler:
       """Handler for Instagram posts."""
       
       def __init__(
           self,
           download_media: bool = True,
           save_to_gcs: bool = False  # NEW parameter
       ):
           """
           Initialize post handler.
           
           Args:
               download_media: Whether to download media files
               save_to_gcs: Whether to save media to GCS
           """
           self.download_media = download_media
           self.save_to_gcs = save_to_gcs
           self.media_handler = None
           
           # Create media handler if needed
           if self.download_media:
               from .media_handler import MediaHandler
               self.media_handler = MediaHandler(save_to_gcs=save_to_gcs)
   ```

2. **Обновить методы обработки медиа**
   ```python
   def process_post_media(self, post: InstagramPost, media_data: list):
       """Process media files for a post."""
       if not self.download_media or not self.media_handler:
           return
       
       self.logger.info(
           f"Processing {len(media_data)} media files for post {post.external_id}"
           f" (GCS: {self.save_to_gcs})"
       )
       
       for media_item in media_data:
           try:
               # Create InstagramMedia instance
               media = self._create_media_instance(post, media_item)
               
               # Download media
               success = self.media_handler.download_media(media)
               
               if success:
                   storage_type = "GCS" if media.gcs_url else "Local"
                   self.logger.info(
                       f"Media {media.external_id} saved to {storage_type}"
                   )
               
           except Exception as e:
               self.logger.error(
                   f"Failed to process media {media_item.get('id')}: {e}"
               )
   ```

### Шаг 5: Добавление логирования (20 минут)

1. **Создать middleware для отслеживания GCS usage**
   ```python
   # core/middleware/gcs_tracking.py
   
   import logging
   from django.utils.deprecation import MiddlewareMixin
   
   logger = logging.getLogger('gcs_tracking')
   
   
   class GCSTrackingMiddleware(MiddlewareMixin):
       """Track GCS usage in requests."""
       
       def process_view(self, request, view_func, view_args, view_kwargs):
           """Log GCS-related requests."""
           if request.path.startswith('/instagram/admin/') and request.method == 'POST':
               if 'save_media_to_gcs' in request.POST:
                   logger.info(
                       f"GCS option in request: "
                       f"path={request.path}, "
                       f"user={request.user}, "
                       f"save_to_gcs={request.POST.get('save_media_to_gcs')}"
                   )
           return None
   ```

2. **Настроить логирование**
   ```python
   # settings/base.py
   
   LOGGING = {
       'version': 1,
       'disable_existing_loggers': False,
       'formatters': {
           'verbose': {
               'format': '{levelname} {asctime} {module} {message}',
               'style': '{',
           },
       },
       'handlers': {
           'gcs_file': {
               'level': 'INFO',
               'class': 'logging.FileHandler',
               'filename': 'logs/gcs_usage.log',
               'formatter': 'verbose',
           },
       },
       'loggers': {
           'gcs_tracking': {
               'handlers': ['gcs_file'],
               'level': 'INFO',
               'propagate': False,
           },
           'core.storage.gcs_service': {
               'handlers': ['gcs_file'],
               'level': 'INFO',
           },
       },
   }
   ```

## 🚨 Важные моменты

1. **Parameter Passing**
   - Убедиться что параметр передается через всю цепочку
   - Не терять параметр при сериализации для Celery
   - Default значение False для backward compatibility

2. **Logging**
   - Логировать решение на каждом уровне
   - Отслеживать успешные GCS uploads
   - Мониторить fallback на локальное хранение

3. **Error Handling**
   - GCS флаг не должен ломать импорт
   - Graceful degradation при ошибках
   - Информативные сообщения пользователю

4. **Testing Points**
   - View правильно извлекает параметр
   - Task получает и передает параметр
   - Handler использует правильный MediaHandler

## 📊 Чек-лист для проверки

- [ ] View извлекает save_to_gcs из формы
- [ ] Параметр добавлен в task_data
- [ ] ImportBatchPostsTask принимает параметр
- [ ] PostHandler инициализируется с параметром
- [ ] MediaHandler создается с правильным флагом
- [ ] Логирование работает на всех уровнях
- [ ] Messages показывают GCS статус
- [ ] Backward compatibility сохранена

## 🔗 Результаты задачи

После выполнения:
1. Полная цепочка передачи параметра
2. Логирование GCS использования
3. Информативные сообщения пользователю
4. Готовность к полному тестированию

## ⏭️ Следующие шаги
После завершения интеграции можно приступать к финальному [Task 007: Testing and Validation](007_testing_validation.md)