# Task 003: GCSService Implementation

**Приоритет**: High  
**Время выполнения**: 3 часа  
**Зависит от**: Task 001, Task 002  
**Блокирует**: Task 004  
**Исполнитель**: Backend Developer

## 📋 Описание задачи
Реализовать сервис для загрузки файлов в Google Cloud Storage. Сервис должен быть простым (MVP), но надежным, с базовой валидацией и обработкой ошибок.

## ✅ Критерии приемки
1. GCSService класс создан и работает
2. Метод upload_file загружает файлы в GCS
3. Базовая валидация размера и типа файлов
4. Возвращает публичный URL загруженного файла
5. Корректная обработка ошибок
6. Unit тесты написаны и проходят

## 📝 Пошаговая инструкция

### Шаг 1: Установка зависимости (15 минут)

1. **Активировать виртуальное окружение**
   ```bash
   source .venv/bin/activate
   ```

2. **Установить google-cloud-storage**
   ```bash
   # Используем uv для установки
   uv add google-cloud-storage
   ```

3. **Проверить установку**
   ```bash
   # Проверить что пакет установлен
   python -c "import google.cloud.storage; print('OK')"
   
   # Проверить версию
   pip show google-cloud-storage
   # Version должна быть 2.x.x
   ```

4. **Обновить requirements**
   ```bash
   # Если используется requirements.txt
   uv pip freeze | grep google-cloud-storage >> requirements.txt
   ```

### Шаг 2: Создание структуры (15 минут)

1. **Создать директорию для storage сервисов**
   ```bash
   mkdir -p core/storage
   touch core/storage/__init__.py
   ```

2. **Создать файл для GCSService**
   ```bash
   touch core/storage/gcs_service.py
   ```

3. **Обновить настройки**
   ```bash
   vim settings/base.py
   ```
   
   Добавить в конец файла:
   ```python
   # Google Cloud Storage Configuration
   GCS_BUCKET_NAME = env('GCS_BUCKET_NAME', default='')
   
   # Optional: для будущих расширений
   GCS_PROJECT_ID = env('GCS_PROJECT_ID', default='')
   GCS_LOCATION = env('GCS_LOCATION', default='us')
   ```

### Шаг 3: Реализация GCSService (1 час)

1. **Создать базовую структуру класса**
   ```python
   # core/storage/gcs_service.py
   
   import logging
   import uuid
   from typing import Optional
   
   from django.conf import settings
   from google.cloud import storage
   from google.cloud.exceptions import GoogleCloudError
   
   logger = logging.getLogger(__name__)
   
   
   class GCSService:
       """
       Service for uploading files to Google Cloud Storage.
       MVP implementation with basic validation and error handling.
       """
       
       # MVP: Simple validation constants
       ALLOWED_CONTENT_TYPES = {
           'image/jpeg',
           'image/png', 
           'image/gif',
           'image/webp',
           'video/mp4',
           'video/quicktime',  # .mov files
       }
       
       ALLOWED_EXTENSIONS = {
           '.jpg', '.jpeg', '.png', '.gif', '.webp',
           '.mp4', '.mov'
       }
       
       MAX_FILE_SIZE = 100 * 1024 * 1024  # 100MB
       
       def __init__(self, bucket_name: Optional[str] = None):
           """
           Initialize GCS service.
           
           Args:
               bucket_name: Override default bucket from settings
           """
           self.bucket_name = bucket_name or settings.GCS_BUCKET_NAME
           
           if not self.bucket_name:
               raise ValueError("GCS_BUCKET_NAME not configured")
           
           try:
               self.client = storage.Client()
               self.bucket = self.client.bucket(self.bucket_name)
           except Exception as e:
               logger.error(f"Failed to initialize GCS client: {e}")
               raise
   ```

2. **Реализовать метод upload_file**
   ```python
       def upload_file(
           self, 
           file_content: bytes, 
           original_filename: str,
           content_type: str,
           folder: str = "instagram"
       ) -> str:
           """
           Upload file to GCS and return public URL.
           
           Args:
               file_content: File content as bytes
               original_filename: Original filename (for extension)
               content_type: MIME type of the file
               folder: Folder in bucket (default: instagram)
               
           Returns:
               Public URL of uploaded file
               
           Raises:
               ValueError: If file validation fails
               GoogleCloudError: If upload fails
           """
           # Validate file
           self._validate_file(file_content, original_filename, content_type)
           
           # Generate unique filename
           blob_name = self._generate_blob_name(original_filename, folder)
           
           try:
               # Create blob
               blob = self.bucket.blob(blob_name)
               
               # Upload file
               blob.upload_from_string(
                   file_content,
                   content_type=content_type
               )
               
               # Make sure it's publicly accessible
               # (bucket should already be public, but just in case)
               blob.make_public()
               
               logger.info(
                   f"Successfully uploaded file to GCS: {blob_name} "
                   f"({len(file_content)} bytes)"
               )
               
               return blob.public_url
               
           except GoogleCloudError as e:
               logger.error(f"GCS upload failed: {e}")
               raise
           except Exception as e:
               logger.error(f"Unexpected error during GCS upload: {e}")
               raise
   ```

3. **Добавить вспомогательные методы**
   ```python
       def _validate_file(
           self, 
           file_content: bytes, 
           filename: str, 
           content_type: str
       ) -> None:
           """Validate file before upload."""
           # Check size
           file_size = len(file_content)
           if file_size > self.MAX_FILE_SIZE:
               raise ValueError(
                   f"File too large: {file_size} bytes "
                   f"(max: {self.MAX_FILE_SIZE} bytes)"
               )
           
           # Check content type
           if content_type not in self.ALLOWED_CONTENT_TYPES:
               raise ValueError(
                   f"Content type not allowed: {content_type}. "
                   f"Allowed types: {', '.join(self.ALLOWED_CONTENT_TYPES)}"
               )
           
           # Check extension
           import os
           ext = os.path.splitext(filename)[1].lower()
           if ext not in self.ALLOWED_EXTENSIONS:
               raise ValueError(
                   f"File extension not allowed: {ext}. "
                   f"Allowed extensions: {', '.join(self.ALLOWED_EXTENSIONS)}"
               )
       
       def _generate_blob_name(self, original_filename: str, folder: str) -> str:
           """Generate unique blob name with folder structure."""
           import os
           from datetime import datetime
           
           # Get extension
           ext = os.path.splitext(original_filename)[1].lower()
           
           # Generate unique name with timestamp
           timestamp = datetime.utcnow().strftime('%Y%m%d_%H%M%S')
           unique_id = str(uuid.uuid4())[:8]
           
           # Format: folder/YYYYMMDD_HHMMSS_UUID_original.ext
           safe_filename = original_filename.replace(' ', '_')
           safe_filename = ''.join(
               c for c in safe_filename 
               if c.isalnum() or c in ('_', '-', '.')
           )
           
           blob_name = f"{folder}/{timestamp}_{unique_id}_{safe_filename}"
           
           return blob_name
   ```

4. **Добавить метод для проверки доступности**
   ```python
       def is_available(self) -> bool:
           """Check if GCS service is available and configured."""
           try:
               # Try to get bucket metadata
               self.bucket.reload()
               return True
           except Exception as e:
               logger.warning(f"GCS service not available: {e}")
               return False
       
       def delete_file(self, blob_name: str) -> bool:
           """
           Delete file from GCS (for cleanup/tests).
           
           Args:
               blob_name: Full blob name including folder
               
           Returns:
               True if deleted, False if not found
           """
           try:
               blob = self.bucket.blob(blob_name)
               blob.delete()
               logger.info(f"Deleted blob from GCS: {blob_name}")
               return True
           except Exception as e:
               logger.error(f"Failed to delete blob: {e}")
               return False
   ```

### Шаг 4: Создание тестов (1 час)

1. **Создать файл тестов**
   ```bash
   mkdir -p tests/core/storage
   touch tests/core/storage/__init__.py
   touch tests/core/storage/test_gcs_service.py
   ```

2. **Написать unit тесты**
   ```python
   # tests/core/storage/test_gcs_service.py
   
   import pytest
   from unittest.mock import Mock, patch, MagicMock
   from django.test import override_settings
   
   from core.storage.gcs_service import GCSService
   
   
   class TestGCSService:
       """Test GCS Service functionality."""
       
       @override_settings(GCS_BUCKET_NAME='test-bucket')
       @patch('core.storage.gcs_service.storage.Client')
       def test_init_success(self, mock_client):
           """Test successful initialization."""
           service = GCSService()
           assert service.bucket_name == 'test-bucket'
           mock_client.assert_called_once()
       
       def test_init_no_bucket_name(self):
           """Test initialization fails without bucket name."""
           with override_settings(GCS_BUCKET_NAME=''):
               with pytest.raises(ValueError, match="GCS_BUCKET_NAME not configured"):
                   GCSService()
       
       @override_settings(GCS_BUCKET_NAME='test-bucket')
       @patch('core.storage.gcs_service.storage.Client')
       def test_file_validation(self, mock_client):
           """Test file validation logic."""
           service = GCSService()
           
           # Valid file
           service._validate_file(
               b'content', 
               'test.jpg', 
               'image/jpeg'
           )
           
           # File too large
           with pytest.raises(ValueError, match="File too large"):
               service._validate_file(
                   b'x' * 200_000_000,
                   'big.jpg',
                   'image/jpeg'
               )
           
           # Invalid content type
           with pytest.raises(ValueError, match="Content type not allowed"):
               service._validate_file(
                   b'content',
                   'test.exe',
                   'application/exe'
               )
           
           # Invalid extension
           with pytest.raises(ValueError, match="File extension not allowed"):
               service._validate_file(
                   b'content',
                   'test.xyz',
                   'image/jpeg'
               )
       
       @override_settings(GCS_BUCKET_NAME='test-bucket')
       @patch('core.storage.gcs_service.storage.Client')
       def test_upload_success(self, mock_client):
           """Test successful file upload."""
           # Setup mocks
           mock_blob = Mock()
           mock_blob.public_url = 'https://storage.googleapis.com/test-bucket/test.jpg'
           
           mock_bucket = Mock()
           mock_bucket.blob.return_value = mock_blob
           
           mock_client.return_value.bucket.return_value = mock_bucket
           
           # Test upload
           service = GCSService()
           url = service.upload_file(
               b'test content',
               'photo.jpg',
               'image/jpeg'
           )
           
           # Assertions
           assert url == 'https://storage.googleapis.com/test-bucket/test.jpg'
           mock_blob.upload_from_string.assert_called_once_with(
               b'test content',
               content_type='image/jpeg'
           )
           mock_blob.make_public.assert_called_once()
       
       @override_settings(GCS_BUCKET_NAME='test-bucket') 
       @patch('core.storage.gcs_service.storage.Client')
       def test_blob_name_generation(self, mock_client):
           """Test blob name generation."""
           service = GCSService()
           
           # Test normal filename
           blob_name = service._generate_blob_name('photo.jpg', 'instagram')
           assert blob_name.startswith('instagram/')
           assert blob_name.endswith('_photo.jpg')
           
           # Test filename with spaces
           blob_name = service._generate_blob_name('my photo.jpg', 'instagram')
           assert 'my_photo.jpg' in blob_name
           assert ' ' not in blob_name
   ```

### Шаг 5: Интеграционное тестирование (30 минут)

1. **Создать скрипт для ручного тестирования**
   ```python
   # scripts/test_gcs_upload.py
   
   import os
   import sys
   import django
   
   # Setup Django
   sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
   os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'socialmgr.settings')
   django.setup()
   
   from core.storage.gcs_service import GCSService
   
   def test_gcs_upload():
       """Test GCS upload with real service."""
       print("Testing GCS Service...")
       
       try:
           # Initialize service
           service = GCSService()
           print(f"✓ Service initialized with bucket: {service.bucket_name}")
           
           # Check availability
           if service.is_available():
               print("✓ GCS service is available")
           else:
               print("✗ GCS service not available")
               return
           
           # Test upload
           test_content = b"Hello from GCS Service test!"
           test_filename = "test_upload.txt"
           
           url = service.upload_file(
               test_content,
               test_filename,
               "text/plain",  # Это вызовет ошибку валидации!
               folder="test"
           )
           
           print(f"✓ File uploaded successfully!")
           print(f"  URL: {url}")
           
           # Test with image
           with open('path/to/test/image.jpg', 'rb') as f:
               image_content = f.read()
           
           image_url = service.upload_file(
               image_content,
               "test_image.jpg",
               "image/jpeg"
           )
           
           print(f"✓ Image uploaded successfully!")
           print(f"  URL: {image_url}")
           
       except Exception as e:
           print(f"✗ Error: {e}")
           import traceback
           traceback.print_exc()
   
   if __name__ == "__main__":
       test_gcs_upload()
   ```

2. **Запустить тесты**
   ```bash
   # Unit tests
   uv run pytest tests/core/storage/test_gcs_service.py -v
   
   # Manual integration test
   python scripts/test_gcs_upload.py
   ```

## 🚨 Важные моменты

1. **Безопасность**
   - Валидация типов файлов критична
   - Не разрешать исполняемые файлы
   - Ограничение размера обязательно

2. **Naming Convention**
   - Уникальные имена с timestamp + UUID
   - Сохранение оригинального имени для читаемости
   - Папки для организации (instagram/, test/)

3. **Error Handling**
   - Четкие сообщения об ошибках
   - Логирование всех операций
   - Graceful fallback в MediaHandler

4. **Performance**
   - upload_from_string загружает все в память
   - Для больших файлов в будущем использовать streaming
   - 100MB лимит подходит для Instagram медиа

## 📊 Чек-лист для проверки

- [ ] google-cloud-storage установлен через uv
- [ ] Директория core/storage создана
- [ ] GCSService класс реализован (~100 строк)
- [ ] Валидация файлов работает
- [ ] Генерация уникальных имен работает
- [ ] Unit тесты написаны и проходят
- [ ] Интеграционный тест с реальным GCS пройден
- [ ] Логирование добавлено для всех операций
- [ ] Документация в docstrings

## 🔗 Результаты задачи

После выполнения:
1. Рабочий `GCSService` класс
2. Прошедшие unit тесты
3. Успешная загрузка тестового файла в GCS
4. Готовность к интеграции с MediaHandler

## ⏭️ Следующие шаги
После завершения реализации GCSService можно приступать к [Task 004: MediaHandler Integration](004_media_handler_integration.md)