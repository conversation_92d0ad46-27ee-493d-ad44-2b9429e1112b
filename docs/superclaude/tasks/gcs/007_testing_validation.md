# Task 007: Testing and Validation

**Приоритет**: Critical  
**Время выполнения**: 3 часа  
**Зависит от**: Tasks 001-006  
**Блокирует**: Production deployment  
**Исполнитель**: Backend Developer / QA

## 📋 Описание задачи
Провести полное end-to-end тестирование GCS интеграции, включая unit тесты, интеграционные тесты, ручное тестирование и валидацию production readiness.

## ✅ Критерии приемки
1. Все unit тесты написаны и проходят
2. E2E сценарий работает от UI до GCS
3. Fallback на локальное хранение работает
4. Backward compatibility подтверждена
5. Performance не деградировал
6. Production checklist пройден

## 📝 Пошаговая инструкция

### Шаг 1: Unit Tests (45 минут)

1. **Запустить существующие тесты**
   ```bash
   # Убедиться что ничего не сломали
   uv run pytest tests/ -v
   
   # Если есть падения - исправить
   ```

2. **Добавить тесты для GCSService**
   ```bash
   # Уже должны быть из Task 003
   uv run pytest tests/core/storage/test_gcs_service.py -v
   ```

3. **Добавить тесты для обновленной модели**
   ```python
   # tests/instagram_manager/test_models_gcs.py
   
   import pytest
   from instagram_manager.models import InstagramMedia
   
   
   class TestInstagramMediaGCS:
       """Test GCS-related functionality in InstagramMedia."""
       
       def test_gcs_url_field_exists(self):
           """Test that gcs_url field exists."""
           media = InstagramMedia()
           assert hasattr(media, 'gcs_url')
           assert media.gcs_url is None
       
       def test_get_display_url_priority(self):
           """Test URL priority: GCS > Local > External."""
           media = InstagramMedia(
               external_id='123',
               media_url='https://instagram.com/photo.jpg'
           )
           
           # Only external URL
           assert media.get_display_url() == 'https://instagram.com/photo.jpg'
           
           # Add local file
           media.is_downloaded = True
           media.local_path.name = 'media/instagram/123.jpg'
           assert 'media/instagram/123.jpg' in media.get_display_url()
           
           # Add GCS URL (should take priority)
           media.gcs_url = 'https://storage.googleapis.com/bucket/123.jpg'
           assert media.get_display_url() == 'https://storage.googleapis.com/bucket/123.jpg'
       
       @pytest.mark.django_db
       def test_gcs_url_saves_correctly(self):
           """Test that gcs_url can be saved to database."""
           media = InstagramMedia.objects.create(
               external_id='test123',
               media_url='https://instagram.com/test.jpg',
               gcs_url='https://storage.googleapis.com/bucket/test.jpg'
           )
           
           # Reload from DB
           media_from_db = InstagramMedia.objects.get(id=media.id)
           assert media_from_db.gcs_url == 'https://storage.googleapis.com/bucket/test.jpg'
   ```

4. **Добавить тесты для форм**
   ```python
   # tests/core/forms/test_mixins_gcs.py
   
   from django.test import override_settings
   from core.forms.mixins import MediaDownloadOptionsMixin
   from django import forms
   
   
   class TestForm(MediaDownloadOptionsMixin, forms.Form):
       """Test form with mixin."""
       pass
   
   
   class TestMediaDownloadOptionsMixinGCS:
       """Test GCS checkbox in forms."""
       
       @override_settings(GCS_BUCKET_NAME='test-bucket')
       def test_gcs_checkbox_visible_when_configured(self):
           """Test checkbox is visible when GCS is configured."""
           form = TestForm()
           assert 'save_media_to_gcs' in form.fields
           assert form.fields['save_media_to_gcs'].widget.input_type != 'hidden'
       
       @override_settings(GCS_BUCKET_NAME='')
       def test_gcs_checkbox_hidden_when_not_configured(self):
           """Test checkbox is hidden when GCS not configured."""
           form = TestForm()
           assert 'save_media_to_gcs' in form.fields
           assert form.fields['save_media_to_gcs'].widget.input_type == 'hidden'
       
       def test_gcs_requires_download_media(self):
           """Test that GCS requires download_media to be True."""
           form = TestForm(data={
               'download_media': False,
               'save_media_to_gcs': True
           })
           assert form.is_valid()
           assert form.cleaned_data['save_media_to_gcs'] is False
   ```

### Шаг 2: Integration Tests (45 минут)

1. **Создать E2E тест**
   ```python
   # tests/instagram_manager/test_gcs_integration.py
   
   import pytest
   from unittest.mock import Mock, patch
   from django.test import override_settings
   from django.contrib.auth import get_user_model
   
   from instagram_manager.models import InstagramProfile, InstagramPost, InstagramMedia
   from instagram_manager.tasks import ImportBatchPostsTask
   
   User = get_user_model()
   
   
   @pytest.mark.django_db
   class TestGCSIntegrationE2E:
       """End-to-end tests for GCS integration."""
       
       @pytest.fixture
       def profile(self):
           """Create test profile."""
           return InstagramProfile.objects.create(
               username='testuser',
               external_id='12345'
           )
       
       @pytest.fixture
       def admin_user(self):
           """Create admin user."""
           return User.objects.create_superuser(
               username='admin',
               email='<EMAIL>',
               password='testpass'
           )
       
       @override_settings(
           GCS_BUCKET_NAME='test-bucket',
           CELERY_TASK_ALWAYS_EAGER=True  # Run tasks synchronously
       )
       @patch('core.storage.gcs_service.storage.Client')
       @patch('requests.get')
       def test_full_flow_with_gcs(self, mock_get, mock_gcs_client):
           """Test complete flow from form to GCS."""
           # Setup mocks
           mock_response = Mock()
           mock_response.content = b'fake image data'
           mock_response.headers = {'Content-Type': 'image/jpeg'}
           mock_response.raise_for_status = Mock()
           mock_get.return_value = mock_response
           
           mock_blob = Mock()
           mock_blob.public_url = 'https://storage.googleapis.com/test-bucket/image.jpg'
           
           mock_bucket = Mock()
           mock_bucket.blob.return_value = mock_blob
           
           mock_gcs_client.return_value.bucket.return_value = mock_bucket
           
           # Create profile
           profile = InstagramProfile.objects.create(
               username='testprofile',
               external_id='123'
           )
           
           # Simulate form submission
           from instagram_manager.forms import BatchImportPostsForm
           form_data = {
               'profiles': [profile.id],
               'limit': 1,
               'download_media': True,
               'save_media_to_gcs': True
           }
           
           form = BatchImportPostsForm(data=form_data)
           assert form.is_valid()
           
           # Execute task
           task = ImportBatchPostsTask()
           
           # Mock BrightData response
           with patch('instagram_manager.instagram_api.client.BrightDataClient.get_posts') as mock_posts:
               mock_posts.return_value = [{
                   'id': 'post123',
                   'caption': 'Test post',
                   'media': [{
                       'id': 'media123',
                       'media_url': 'https://instagram.com/image.jpg',
                       'media_type': 'IMAGE'
                   }]
               }]
               
               # Run task
               result = task.execute(
                   profile_ids=[profile.id],
                   limit=1,
                   download_media=True,
                   save_to_gcs=True
               )
           
           # Verify results
           assert result['success'] == 1
           assert result['failed'] == 0
           
           # Check media was created with GCS URL
           media = InstagramMedia.objects.get(external_id='media123')
           assert media.gcs_url == 'https://storage.googleapis.com/test-bucket/image.jpg'
           assert media.is_downloaded is True
           
           # Verify GCS was called
           mock_blob.upload_from_string.assert_called_once()
           mock_blob.make_public.assert_called_once()
   ```

2. **Тест Fallback сценария**
   ```python
   @override_settings(GCS_BUCKET_NAME='test-bucket')
   @patch('core.storage.gcs_service.storage.Client')
   @patch('requests.get')
   def test_fallback_to_local_on_gcs_error(self, mock_get, mock_gcs_client):
       """Test fallback to local storage when GCS fails."""
       # Setup mock for failed GCS
       mock_gcs_client.side_effect = Exception("GCS Connection Error")
       
       # Rest of the test to verify local storage is used...
   ```

### Шаг 3: Manual Testing Checklist (30 минут)

1. **Подготовка тестового окружения**
   ```bash
   # Настроить реальный GCS
   export GCS_BUCKET_NAME="socialmanager-test"
   export GOOGLE_APPLICATION_CREDENTIALS="/path/to/test-key.json"
   
   # Запустить сервер
   python manage.py runserver
   
   # В другом терминале - Celery
   celery -A socialmgr worker -l info
   ```

2. **Выполнить manual test scenarios**
   
   **Scenario 1: Happy Path**
   - [ ] Login как admin
   - [ ] Перейти на `/instagram/admin/batch-import-posts/`
   - [ ] Выбрать тестовый профиль
   - [ ] ✅ Поставить "Download media files"
   - [ ] ✅ Поставить "Save media to Google Cloud Storage"
   - [ ] Limit: 2
   - [ ] Submit
   - [ ] Дождаться завершения (проверить Celery logs)
   - [ ] Открыть Instagram Media в админке
   - [ ] Проверить что URL начинается с `https://storage.googleapis.com/`
   - [ ] Кликнуть на URL - изображение должно открыться
   
   **Scenario 2: Fallback Test**
   - [ ] Временно сломать GCS (неверный bucket name в .env)
   - [ ] Повторить импорт
   - [ ] Проверить Celery logs на "GCS failed, saving locally"
   - [ ] Проверить что файлы сохранены локально
   - [ ] Проверить что импорт завершился успешно
   
   **Scenario 3: Backward Compatibility**
   - [ ] Убрать GCS_BUCKET_NAME из окружения
   - [ ] Перезапустить сервер
   - [ ] Проверить что чекбокс НЕ отображается
   - [ ] Сделать обычный импорт
   - [ ] Проверить что все работает как раньше

### Шаг 4: Performance Testing (30 минут)

1. **Создать performance benchmark**
   ```python
   # scripts/gcs_performance_test.py
   
   import time
   import statistics
   from django.test import override_settings
   from instagram_manager.instagram_api.data_handlers.media_handler import MediaHandler
   from instagram_manager.models import InstagramMedia
   
   def benchmark_media_download(use_gcs=False, iterations=10):
       """Benchmark media download performance."""
       handler = MediaHandler(save_to_gcs=use_gcs)
       times = []
       
       for i in range(iterations):
           media = InstagramMedia.objects.create(
               external_id=f'perf_test_{i}',
               media_url='https://picsum.photos/800/600'  # Random image
           )
           
           start = time.time()
           handler.download_media(media)
           end = time.time()
           
           times.append(end - start)
           
           # Cleanup
           media.delete()
       
       return {
           'mean': statistics.mean(times),
           'median': statistics.median(times),
           'min': min(times),
           'max': max(times)
       }
   
   # Run benchmarks
   print("Benchmark: Local Storage")
   local_stats = benchmark_media_download(use_gcs=False)
   print(f"  Mean: {local_stats['mean']:.2f}s")
   print(f"  Median: {local_stats['median']:.2f}s")
   
   print("\nBenchmark: GCS Storage")
   gcs_stats = benchmark_media_download(use_gcs=True)
   print(f"  Mean: {gcs_stats['mean']:.2f}s")
   print(f"  Median: {gcs_stats['median']:.2f}s")
   
   # Performance should not degrade more than 20%
   degradation = (gcs_stats['mean'] - local_stats['mean']) / local_stats['mean'] * 100
   print(f"\nPerformance impact: {degradation:.1f}%")
   assert degradation < 20, "Performance degradation too high!"
   ```

### Шаг 5: Production Readiness Checklist (30 минут)

1. **Security Check**
   - [ ] GCS credentials НЕ в репозитории
   - [ ] Service account имеет минимальные права
   - [ ] Bucket настроен правильно (public read)
   - [ ] Валидация файлов работает

2. **Monitoring Check**
   ```bash
   # Проверить логи
   tail -f logs/gcs_usage.log
   
   # Должны быть записи:
   # - Successful uploads
   # - Failed uploads with reasons
   # - Fallback events
   ```

3. **Database Check**
   ```sql
   -- Проверить миграции
   SELECT * FROM django_migrations 
   WHERE app = 'instagram_manager' 
   ORDER BY applied DESC LIMIT 5;
   
   -- Проверить данные
   SELECT COUNT(*) FROM instagram_media WHERE gcs_url IS NOT NULL;
   SELECT COUNT(*) FROM instagram_media WHERE gcs_url IS NULL;
   ```

4. **Deployment Checklist**
   - [ ] Environment variables documented
   - [ ] GCS bucket created in production
   - [ ] Service account created with production credentials
   - [ ] Deployment scripts updated
   - [ ] Rollback plan ready

### Шаг 6: Smoke Test Script (10 минут)

1. **Создать smoke test**
   ```bash
   vim scripts/gcs_smoke_test_production.py
   ```
   
   ```python
   #!/usr/bin/env python
   """Production smoke test for GCS integration."""
   
   import os
   import sys
   import django
   
   sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
   os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'socialmgr.settings')
   django.setup()
   
   from django.conf import settings
   from core.storage.gcs_service import GCSService
   from instagram_manager.models import InstagramMedia
   
   def run_smoke_test():
       print("=== GCS Integration Smoke Test ===\n")
       
       # 1. Check configuration
       print("1. Checking configuration...")
       if not settings.GCS_BUCKET_NAME:
           print("❌ GCS_BUCKET_NAME not configured")
           return False
       print(f"✅ GCS bucket: {settings.GCS_BUCKET_NAME}")
       
       # 2. Test GCS connection
       print("\n2. Testing GCS connection...")
       try:
           service = GCSService()
           if service.is_available():
               print("✅ GCS service is available")
           else:
               print("❌ GCS service not available")
               return False
       except Exception as e:
           print(f"❌ GCS initialization failed: {e}")
           return False
       
       # 3. Test upload
       print("\n3. Testing file upload...")
       try:
           test_content = b"GCS smoke test content"
           url = service.upload_file(
               test_content,
               "smoke_test.txt",
               "text/plain",
               folder="test"
           )
           print(f"✅ Upload successful: {url}")
           
           # Cleanup
           blob_name = url.split('/')[-2:]  # Get folder/filename
           blob_name = '/'.join(blob_name)
           service.delete_file(blob_name)
           print("✅ Test file cleaned up")
           
       except Exception as e:
           print(f"❌ Upload test failed: {e}")
           return False
       
       # 4. Check recent uploads
       print("\n4. Checking recent GCS uploads...")
       recent_gcs = InstagramMedia.objects.filter(
           gcs_url__isnull=False
       ).order_by('-created_at')[:5]
       
       if recent_gcs:
           print(f"✅ Found {recent_gcs.count()} recent GCS uploads")
           for media in recent_gcs:
               print(f"  - {media.external_id}: {media.gcs_url[:50]}...")
       else:
           print("ℹ️  No GCS uploads found yet")
       
       print("\n✅ All smoke tests passed!")
       return True
   
   if __name__ == "__main__":
       success = run_smoke_test()
       sys.exit(0 if success else 1)
   ```

## 🚨 Важные моменты

1. **Test Coverage**
   - Unit tests для каждого компонента
   - Integration tests для полного флоу
   - Manual tests для UI/UX
   - Performance tests для SLA

2. **Edge Cases**
   - GCS недоступен при старте
   - GCS отваливается во время работы
   - Неверные credentials
   - Превышение квот

3. **Monitoring**
   - Следить за количеством fallbacks
   - Мониторить время upload
   - Алерты на критические ошибки

## 📊 Чек-лист Go/No-Go

### ✅ GO (можно в production)
- [ ] Все unit tests проходят (0 failures)
- [ ] E2E тест проходит
- [ ] Manual happy path работает
- [ ] Fallback работает при ошибке GCS
- [ ] Performance degradation < 20%
- [ ] Backward compatibility подтверждена
- [ ] Smoke test проходит

### ❌ NO-GO (нужны исправления)
- [ ] Любой критический тест падает
- [ ] Fallback не работает
- [ ] Performance degradation > 20%
- [ ] Security issues найдены
- [ ] Backward compatibility нарушена

## 🔗 Результаты задачи

После выполнения:
1. Полный набор тестов
2. Performance benchmarks
3. Production readiness подтверждена
4. Smoke test для мониторинга
5. Документация для ops team

## 🎉 Поздравляем!
Если все тесты пройдены - GCS интеграция готова к production deployment!