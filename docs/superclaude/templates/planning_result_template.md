# Планирование: [Название функционала]

**Дата**: [дата планирования]  
**Автор**: [имя планировщика]  
**Версия плана**: 1.0  
**Статус**: Draft / Review / Approved / In Progress / Completed

## Executive Summary

[1-2 абзаца краткого описания:
- Что планируем реализовать
- Ключевые технические решения
- Основные риски
- Ожидаемые сроки и ресурсы]

## Результаты анализа (Шаг 1)

### Исследование технологий
- **[Технология/API 1]**: 
  - Возможности: [что предоставляет]
  - Ограничения: [rate limits, доступность]
  - Вывод: [подходит/не подходит и почему]

### Анализ существующей архитектуры
- **Переиспользуемые компоненты**:
  - [Компонент 1]: [как можем использовать]
  - [Компонент 2]: [описание]
- **Необходимые изменения**:
  - [Изменение 1]: [что и зачем менять]

### Технические ограничения
| Ограничение | Описание | Влияние на решение |
|-------------|----------|-------------------|
| [Rate limits] | [100 req/min] | [Нужна очередь обработки] |
| [Ограничение 2] | [описание] | [как учитываем] |

### Производительность
- **Ожидаемая нагрузка**: [X операций/час]
- **Требования к отклику**: [< Y секунд]
- **Узкие места**: [где могут быть проблемы]

## Архитектурное решение (Шаг 2)

### Диаграмма компонентов
```
[Текстовая диаграмма или описание]
┌─────────────┐     ┌──────────────┐     ┌─────────────┐
│   Client    │────▶│     API      │────▶│   Service   │
└─────────────┘     └──────────────┘     └─────────────┘
                           │                      │
                           ▼                      ▼
                    ┌──────────────┐     ┌─────────────┐
                    │    Queue     │     │ Repository  │
                    └──────────────┘     └─────────────┘
```

### Новые модели
```python
# instagram_manager/models.py
class ModerationRule(TimestampedModel):
    """Правило модерации для профиля"""
    profile = models.ForeignKey(InstagramProfile, on_delete=models.CASCADE)
    name = models.CharField(max_length=100)
    rule_type = models.CharField(max_length=50, choices=RULE_TYPES)
    configuration = models.JSONField()
    is_active = models.BooleanField(default=True)
    priority = models.IntegerField(default=0)
    
    class Meta:
        ordering = ['-priority', 'created_at']

class ModerationAction(TimestampedModel):
    """Действие модерации над комментарием"""
    comment = models.ForeignKey(InstagramComment, on_delete=models.CASCADE)
    rule = models.ForeignKey(ModerationRule, on_delete=models.SET_NULL, null=True)
    action_type = models.CharField(max_length=50, choices=ACTION_TYPES)
    confidence_score = models.FloatField()
    reason = models.TextField()
    reviewed_by = models.ForeignKey(User, null=True, blank=True)
```

### Новые сервисы
```python
# instagram_manager/services/moderation_service.py
class ModerationService:
    """Сервис модерации комментариев"""
    
    def __init__(self):
        self.ml_service = MLAnalysisService()
        self.rule_repo = ModerationRuleRepository()
        self.action_repo = ModerationActionRepository()
    
    def moderate_comment(self, comment: InstagramComment) -> Optional[ModerationAction]:
        """Модерирует один комментарий"""
        pass
    
    def batch_moderate(self, profile: InstagramProfile, limit: int = 100) -> List[ModerationAction]:
        """Модерирует пакет комментариев"""
        pass
    
    def apply_rules(self, comment: InstagramComment, rules: List[ModerationRule]) -> Optional[ModerationAction]:
        """Применяет правила к комментарию"""
        pass
```

### Celery задачи
```python
# instagram_manager/tasks/moderation_tasks.py
class ModerateCommentsTask(BaseTask):
    """Асинхронная модерация комментариев"""
    
    name = "moderate_comments"
    max_retries = 3
    
    def execute(self, profile_id: int, batch_size: int = 100):
        """Модерирует новые комментарии профиля"""
        pass
```

### API Endpoints
- `POST /api/v1/moderation/rules/` - создание правила
- `GET /api/v1/moderation/rules/{profile_id}/` - получение правил профиля
- `PUT /api/v1/moderation/rules/{id}/` - обновление правила
- `DELETE /api/v1/moderation/rules/{id}/` - удаление правила
- `POST /api/v1/moderation/moderate/` - запуск модерации
- `GET /api/v1/moderation/actions/` - история действий
- `POST /api/v1/moderation/actions/{id}/review/` - human review

## Анализ рисков (Шаг 3)

### Матрица рисков
| Риск | Вероятность | Влияние | Стратегия митигации | Мониторинг |
|------|-------------|---------|-------------------|------------|
| False positive блокировка | Высокая | Высокое | - Настраиваемый confidence threshold<br>- Human review queue<br>- Whitelist для доверенных пользователей | - % false positives<br>- Время на review |
| Превышение API rate limit | Средняя | Среднее | - Экспоненциальный backoff<br>- Приоритизация обработки<br>- Кеширование результатов | - API usage metrics<br>- Queue length |
| ML модель дает плохие результаты | Средняя | Высокое | - A/B тестирование моделей<br>- Fallback на rule-based<br>- Continuous learning | - Accuracy metrics<br>- User feedback |
| GDPR compliance issues | Низкая | Критичное | - Аудит лог всех действий<br>- Право на обжалование<br>- Анонимизация данных для ML | - Audit completeness<br>- Appeal requests |

### План действий при инцидентах
1. **False positive spike**: 
   - Немедленно повысить confidence threshold
   - Уведомить модераторов
   - Rollback последних изменений правил

2. **API недоступен**:
   - Переключиться на очередь с отложенной обработкой
   - Уведомить пользователей о задержке
   - Использовать кешированные решения

## План реализации (Шаг 4)

### Фаза 1: Инфраструктура (3 дня)
- [ ] **MOD-001**: Создать модели ModerationRule, ModerationAction с миграциями (6ч, backend)
- [ ] **MOD-002**: Создать админку для новых моделей (4ч, backend)
- [ ] **MOD-003**: Реализовать ModerationRepository с CRUD операциями (6ч, backend)
- [ ] **MOD-004**: Написать фабрики и fixtures для тестов (4ч, backend)

### Фаза 2: ML интеграция (5 дней)
- [ ] **MOD-005**: Research и выбор ML модели для sentiment analysis (8ч, ml) 
- [ ] **MOD-006**: Создать MLAnalysisService с методами analyze_sentiment, detect_spam (8ч, ml, backend)
- [ ] **MOD-007**: Настроить Redis кеширование для ML результатов (4ч, backend, devops)
- [ ] **MOD-008**: Создать бенчмарки производительности ML (6ч, ml, qa)
- [ ] **MOD-009**: Реализовать fallback механизм при недоступности ML (4ч, backend)

### Фаза 3: Бизнес-логика (4 дня)
- [ ] **MOD-010**: Реализовать ModerationService.moderate_comment() (8ч, backend) [зависит от: MOD-003, MOD-006]
- [ ] **MOD-011**: Реализовать ModerationService.batch_moderate() (6ч, backend) [зависит от: MOD-010]
- [ ] **MOD-012**: Создать Celery task ModerateCommentsTask (6ч, backend) [зависит от: MOD-011]
- [ ] **MOD-013**: Реализовать webhook handler для real-time модерации (8ч, backend) [зависит от: MOD-010]

### Фаза 4: API и интеграция (3 дня)
- [ ] **MOD-014**: Создать API endpoints для управления правилами (8ч, backend)
- [ ] **MOD-015**: Создать API для просмотра и review действий модерации (6ч, backend)
- [ ] **MOD-016**: Интегрировать модерацию в существующий comment flow (6ч, backend) [зависит от: MOD-013]
- [ ] **MOD-017**: Добавить настройки модерации в InstagramProfile (4ч, backend)

### Фаза 5: Тестирование (4 дня)
- [ ] **MOD-018**: Unit тесты для ModerationService (8ч, qa) [зависит от: MOD-010]
- [ ] **MOD-019**: Unit тесты для MLAnalysisService (6ч, qa) [зависит от: MOD-006]
- [ ] **MOD-020**: Integration тесты с mock внешних сервисов (8ч, qa)
- [ ] **MOD-021**: Performance тесты для batch обработки (6ч, qa, devops)
- [ ] **MOD-022**: E2E тесты основных сценариев (6ч, qa)

### Фаза 6: Документация и deployment (2 дня)
- [ ] **MOD-023**: Написать API документацию (4ч, backend)
- [ ] **MOD-024**: Создать runbook для операций (4ч, devops)
- [ ] **MOD-025**: Настроить мониторинг и алерты (6ч, devops)
- [ ] **MOD-026**: Подготовить deployment план с rollback (4ч, devops)

### Критический путь
```
MOD-005 → MOD-006 → MOD-010 → MOD-011 → MOD-012 → MOD-018
        ↘
          MOD-007 → MOD-013 → MOD-016
```

## Оценка времени (Шаг 5)

### Сводная оценка
| Сценарий | Длительность | Обоснование |
|----------|--------------|-------------|
| **Оптимистичный** | 3 недели (15 рабочих дней) | Все идет по плану, ML модель работает сразу |
| **Реалистичный** | 4 недели (20 рабочих дней) | +25% на непредвиденные проблемы |
| **Пессимистичный** | 6 недель (30 рабочих дней) | Проблемы с ML, изменение требований |

### Детальная разбивка
| Фаза | Оптимистично | Реалистично | Пессимистично |
|------|--------------|-------------|---------------|
| Инфраструктура | 3 дня | 3 дня | 4 дня |
| ML интеграция | 5 дней | 7 дней | 10 дней |
| Бизнес-логика | 4 дня | 5 дней | 7 дней |
| API и интеграция | 3 дня | 3 дня | 4 дня |
| Тестирование | 4 дня | 5 дней | 7 дней |
| Документация | 2 дня | 2 дня | 3 дня |
| **Итого** | **21 день** | **25 дней** | **35 дней** |

### Факторы влияющие на сроки
1. **Выбор ML модели** (+3-7 дней): Может потребоваться тестирование нескольких вариантов
2. **Производительность** (+2-5 дней): Оптимизация для требуемого response time
3. **Изменение требований** (+5-10 дней): Вероятность 30%
4. **Проблемы с внешними API** (+2-3 дня): Rate limits, недоступность

### Необходимые ресурсы
| Роль | Загрузка | Период | Критичность |
|------|----------|--------|-------------|
| Backend Developer | 100% | Весь проект | Критично |
| ML Engineer | 50% | Фаза 2 | Критично |
| QA Engineer | 100% | Фаза 5 | Критично |
| DevOps | 30% | Фазы 2, 6 | Важно |
| Product Owner | 20% | Весь проект | Важно |

## Метрики успеха

### Технические метрики
| Метрика | Целевое значение | Способ измерения |
|---------|-----------------|------------------|
| Точность модерации | > 95% | (TP + TN) / Total |
| False positive rate | < 2% | FP / (FP + TN) |
| Время обработки комментария | < 100ms | 95th percentile |
| API availability | > 99.9% | Uptime monitoring |

### Бизнес-метрики
| Метрика | Целевое значение | Способ измерения |
|---------|-----------------|------------------|
| Снижение спама | -80% | Количество спам-комментариев |
| Удовлетворенность пользователей | +20% | NPS опросы |
| Время на модерацию | -60% | Часы работы модераторов |
| Engagement | +10% | Активность в комментариях |

## Решения и альтернативы

### Выбранные решения
1. **ML модель**: Transformers-based sentiment analysis
   - **Почему**: Высокая точность, поддержка множества языков
   - **Альтернатива**: Rule-based система
   - **Почему не выбрана**: Низкая точность, сложность поддержки

2. **Архитектура**: Асинхронная обработка через Celery
   - **Почему**: Масштабируемость, надежность
   - **Альтернатива**: Синхронная обработка
   - **Почему не выбрана**: Блокирует UI, не масштабируется

3. **Хранение правил**: PostgreSQL JSONB
   - **Почему**: Гибкость, производительность, транзакции
   - **Альтернатива**: Отдельные таблицы для каждого типа правил
   - **Почему не выбрана**: Сложность изменения схемы

## Открытые вопросы

- [ ] Какой confidence threshold использовать по умолчанию?
- [ ] Нужна ли интеграция с внешними сервисами модерации?
- [ ] Как долго хранить историю модерации?
- [ ] Нужны ли email уведомления о заблокированных комментариях?
- [ ] Поддерживать ли модерацию на нескольких языках сразу?

## Следующие шаги

1. **Review плана с командой** (1 день)
2. **Финализация выбора ML модели** (2 дня)
3. **Создание задач в Jira** (0.5 дня)
4. **Kick-off встреча** (0.5 дня)
5. **Начало разработки**

## Приложения

### A. Примеры правил модерации
```json
{
  "rule_type": "spam_keywords",
  "configuration": {
    "keywords": ["buy now", "click here", "free money"],
    "action": "hide",
    "confidence_threshold": 0.8
  }
}
```

### B. Пример ML анализа
```json
{
  "text": "This product is terrible!",
  "sentiment": {
    "label": "negative",
    "score": 0.95
  },
  "spam_probability": 0.12,
  "language": "en"
}
```

### C. Ссылки
- [API Documentation Draft]()
- [ML Model Evaluation]()
- [Performance Benchmarks]()
- [UI Mockups]()

---

## История версий плана

| Версия | Дата | Автор | Изменения |
|--------|------|-------|-----------|
| 1.0 | [дата] | [имя] | Первая версия плана |