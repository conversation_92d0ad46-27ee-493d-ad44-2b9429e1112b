# Функционал: [Название функционала]

**Версия**: 1.0  
**Дата**: [дата создания]  
**Автор**: [имя автора]  
**Статус**: Draft / In Review / Approved

## Описание

[1-2 абзаца описания функционала. Что это, для чего нужно, какую проблему решает]

## Бизнес-требования

### Основные требования
- [ ] [Требование 1 - что должен уметь делать пользователь]
- [ ] [Требование 2]
- [ ] [Требование 3]

### Дополнительные требования (nice to have)
- [ ] [Опциональное требование 1]
- [ ] [Опциональное требование 2]

## Технические детали

### Интеграции
- **[Название API/сервиса]**: [как используется, для чего]
- **[Название API/сервиса 2]**: [описание]

### Алгоритмы и модели
- **[Алгоритм/модель]**: [описание, почему выбран]
- **[Алгоритм/модель 2]**: [описание]

### Технологический стек
- [Технология 1]: [обоснование выбора]
- [Технология 2]: [обоснование]

## Затрагиваемые компоненты

### Django Apps
- **[app_name]**:
  - Models: [список моделей]
  - Services: [список сервисов]
  - Repositories: [список репозиториев]

### Celery Tasks
- [TaskName]: [описание задачи]
- [TaskName2]: [описание]

### Внешние API
- [API Name]: [endpoints, которые будем использовать]

### База данных
- Новые таблицы: [список]
- Изменения в существующих: [список]
- Индексы: [какие нужны и почему]

## Ограничения и риски

### Технические ограничения
- **[Ограничение 1]**: [описание, например rate limits]
- **[Ограничение 2]**: [описание]

### Бизнес-ограничения
- **[Ограничение 1]**: [например, бюджет, сроки]
- **[Ограничение 2]**: [описание]

### Риски
| Риск | Вероятность | Влияние | Описание |
|------|-------------|---------|----------|
| [Название риска] | В/С/Н | В/С/Н | [Описание риска] |
| [Риск 2] | В/С/Н | В/С/Н | [Описание] |

## Пользовательские сценарии (User Stories)

### Сценарий 1: [Название]
**Как** [тип пользователя]  
**Я хочу** [действие]  
**Чтобы** [цель/выгода]

**Критерии приемки**:
- [ ] [Критерий 1]
- [ ] [Критерий 2]

### Сценарий 2: [Название]
**Как** [тип пользователя]  
**Я хочу** [действие]  
**Чтобы** [цель/выгода]

**Критерии приемки**:
- [ ] [Критерий 1]
- [ ] [Критерий 2]

## Критерии успеха

### Количественные метрики
- **[Метрика 1]**: целевое значение [X], способ измерения
- **[Метрика 2]**: целевое значение [Y], способ измерения

### Качественные критерии
- [Критерий 1]
- [Критерий 2]

## Этапы реализации (высокоуровнево)

1. **Фаза 1**: [Название] - [описание что делаем]
2. **Фаза 2**: [Название] - [описание]
3. **Фаза 3**: [Название] - [описание]

## Альтернативные решения

### Вариант 1: [Название]
- **Плюсы**: [список]
- **Минусы**: [список]
- **Почему не выбран**: [обоснование]

### Вариант 2: [Название]
- **Плюсы**: [список]
- **Минусы**: [список]
- **Почему не выбран**: [обоснование]

## Открытые вопросы

- [ ] [Вопрос 1 - что нужно уточнить]
- [ ] [Вопрос 2]
- [ ] [Вопрос 3]

## Зависимости

### Внешние зависимости
- [Зависимость 1]: [описание, критичность]
- [Зависимость 2]: [описание]

### Внутренние зависимости
- [Компонент/функционал]: [почему зависим]
- [Компонент 2]: [описание]

## Примеры данных

### Входные данные
```json
{
  "example": "data",
  "format": "json"
}
```

### Ожидаемый результат
```json
{
  "result": "example",
  "status": "success"
}
```

## Дополнительные материалы

- [Ссылка на макеты/дизайн]
- [Ссылка на спецификацию API]
- [Ссылка на исследование]
- [Ссылка на прототип]

---

## История изменений

| Версия | Дата | Автор | Изменения |
|--------|------|-------|-----------|
| 1.0 | [дата] | [имя] | Первая версия |