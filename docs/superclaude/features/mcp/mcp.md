## Обновленная реализация MCP сервера с использованием официального Python SDK

Для использования официального Python SDK от ModelContextProtocol (https://github.com/modelcontextprotocol/python-sdk), вот обновленный подход:

### 1. Установка зависимостей

Обновите `pyproject.toml`, добавив официальный MCP SDK:

```toml
dependencies = [
    # ... существующие зависимости
    "mcp>=1.0.0",  # Официальный MCP Python SDK
    "uvicorn>=0.30.0",  # для запуска сервера (если нужен HTTP транспорт)
]
```

### 2. Создание MCP приложения

Создайте новое Django приложение:

```bash
python manage.py startapp mcp_server
```

### 3. Структура MCP сервера

```
mcp_server/
├── __init__.py
├── apps.py
├── server.py              # Основной MCP сервер
├── tools/                 # MCP инструменты
│   ├── __init__.py
│   ├── instagram_tools.py
│   ├── telegram_tools.py
│   └── core_tools.py
├── resources/             # MCP ресурсы
│   ├── __init__.py
│   ├── instagram_resources.py
│   ├── telegram_resources.py
│   └── core_resources.py
└── management/
    └── commands/
        └── run_mcp_server.py
```

### 4. Основной MCP сервер (`mcp_server/server.py`)

```python
import asyncio
import logging
from typing import Any, Sequence

import django
from django.conf import settings

# Настройка Django
import os
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'SocialManager.settings.local')
django.setup()

from mcp.server.models import InitializationOptions
from mcp.server import NotificationOptions, Server
from mcp.server.stdio import stdio_server
from mcp.types import (
    Resource,
    Tool,
    TextContent,
    ImageContent,
    EmbeddedResource,
    CallToolResult,
    ListResourcesResult,
    ListToolsResult,
    ReadResourceResult,
)

from .tools.instagram_tools import InstagramTools
from .tools.telegram_tools import TelegramTools
from .tools.core_tools import CoreTools
from .resources.instagram_resources import InstagramResources
from .resources.telegram_resources import TelegramResources
from .resources.core_resources import CoreResources

logger = logging.getLogger(__name__)

class SocialManagerMCPServer:
    def __init__(self):
        self.server = Server("socialmanager-mcp")
        
        # Инициализация инструментов и ресурсов
        self.instagram_tools = InstagramTools()
        self.telegram_tools = TelegramTools()
        self.core_tools = CoreTools()
        self.instagram_resources = InstagramResources()
        self.telegram_resources = TelegramResources()
        self.core_resources = CoreResources()
        
        self._setup_handlers()
    
    def _setup_handlers(self):
        """Настройка обработчиков MCP с использованием официального SDK"""
        
        @self.server.list_tools()
        async def handle_list_tools() -> ListToolsResult:
            """Список доступных инструментов"""
            tools = []
            tools.extend(await self.instagram_tools.get_tools())
            tools.extend(await self.telegram_tools.get_tools())
            tools.extend(await self.core_tools.get_tools())
            
            return ListToolsResult(tools=tools)
        
        @self.server.call_tool()
        async def handle_call_tool(name: str, arguments: dict[str, Any] | None) -> CallToolResult:
            """Выполнение инструментов"""
            try:
                if name.startswith("instagram_"):
                    content = await self.instagram_tools.call_tool(name, arguments or {})
                elif name.startswith("telegram_"):
                    content = await self.telegram_tools.call_tool(name, arguments or {})
                elif name.startswith("core_"):
                    content = await self.core_tools.call_tool(name, arguments or {})
                else:
                    raise ValueError(f"Unknown tool: {name}")
                
                return CallToolResult(content=content)
            except Exception as e:
                logger.error(f"Error calling tool {name}: {e}")
                return CallToolResult(
                    content=[TextContent(type="text", text=f"Error: {str(e)}")],
                    isError=True
                )
        
        @self.server.list_resources()
        async def handle_list_resources() -> ListResourcesResult:
            """Список доступных ресурсов"""
            resources = []
            resources.extend(await self.instagram_resources.get_resources())
            resources.extend(await self.telegram_resources.get_resources())
            resources.extend(await self.core_resources.get_resources())
            
            return ListResourcesResult(resources=resources)
        
        @self.server.read_resource()
        async def handle_read_resource(uri: str) -> ReadResourceResult:
            """Чтение ресурсов"""
            try:
                if uri.startswith("instagram://"):
                    content = await self.instagram_resources.read_resource(uri)
                elif uri.startswith("telegram://"):
                    content = await self.telegram_resources.read_resource(uri)
                elif uri.startswith("core://"):
                    content = await self.core_resources.read_resource(uri)
                else:
                    raise ValueError(f"Unknown resource URI: {uri}")
                
                return ReadResourceResult(contents=[TextContent(type="text", text=content)])
            except Exception as e:
                logger.error(f"Error reading resource {uri}: {e}")
                return ReadResourceResult(
                    contents=[TextContent(type="text", text=f"Error: {str(e)}")],
                    isError=True
                )

    async def run(self):
        """Запуск MCP сервера"""
        async with stdio_server() as (read_stream, write_stream):
            await self.server.run(
                read_stream,
                write_stream,
                InitializationOptions(
                    server_name="socialmanager-mcp",
                    server_version="1.0.0",
                    capabilities=self.server.get_capabilities(
                        notification_options=NotificationOptions(),
                        experimental_capabilities={},
                    ),
                ),
            )

async def main():
    """Главная функция для запуска сервера"""
    logging.basicConfig(level=logging.INFO)
    server = SocialManagerMCPServer()
    await server.run()

if __name__ == "__main__":
    asyncio.run(main())
```

### 5. Instagram инструменты (`mcp_server/tools/instagram_tools.py`)

```python
from typing import Any, List
from mcp.types import Tool, TextContent
from django.db.models import Q
from instagram_manager.models import (
    InstagramProfile, InstagramPost, InstagramComment, 
    InstagramMedia, InstagramScrapingTask
)

class InstagramTools:
    async def get_tools(self) -> List[Tool]:
        """Возвращает список доступных Instagram инструментов"""
        return [
            Tool(
                name="instagram_get_profiles",
                description="Получить список Instagram профилей с возможностью поиска",
                inputSchema={
                    "type": "object",
                    "properties": {
                        "limit": {
                            "type": "integer", 
                            "default": 10,
                            "description": "Максимальное количество профилей для возврата"
                        },
                        "search": {
                            "type": "string", 
                            "description": "Поиск по username профиля"
                        }
                    },
                    "additionalProperties": False
                }
            ),
            Tool(
                name="instagram_get_posts",
                description="Получить посты Instagram с фильтрацией по профилю",
                inputSchema={
                    "type": "object",
                    "properties": {
                        "profile_username": {
                            "type": "string",
                            "description": "Username профиля для фильтрации постов"
                        },
                        "limit": {
                            "type": "integer", 
                            "default": 10,
                            "description": "Максимальное количество постов"
                        }
                    },
                    "additionalProperties": False
                }
            ),
            Tool(
                name="instagram_get_comments",
                description="Получить комментарии к конкретному посту",
                inputSchema={
                    "type": "object",
                    "properties": {
                        "post_id": {
                            "type": "integer",
                            "description": "ID поста для получения комментариев"
                        },
                        "limit": {
                            "type": "integer", 
                            "default": 10,
                            "description": "Максимальное количество комментариев"
                        }
                    },
                    "required": ["post_id"],
                    "additionalProperties": False
                }
            ),
            Tool(
                name="instagram_get_scraping_tasks",
                description="Получить задачи скрапинга Instagram с фильтрацией по статусу",
                inputSchema={
                    "type": "object",
                    "properties": {
                        "status": {
                            "type": "string", 
                            "enum": ["pending", "running", "completed", "failed"],
                            "description": "Статус задач для фильтрации"
                        },
                        "limit": {
                            "type": "integer", 
                            "default": 10,
                            "description": "Максимальное количество задач"
                        }
                    },
                    "additionalProperties": False
                }
            ),
            Tool(
                name="instagram_get_profile_stats",
                description="Получить статистику по Instagram профилю",
                inputSchema={
                    "type": "object",
                    "properties": {
                        "username": {
                            "type": "string",
                            "description": "Username профиля для получения статистики"
                        }
                    },
                    "required": ["username"],
                    "additionalProperties": False
                }
            )
        ]
    
    async def call_tool(self, name: str, arguments: dict[str, Any]) -> List[TextContent]:
        """Выполнение Instagram инструментов"""
        if name == "instagram_get_profiles":
            return await self._get_profiles(arguments)
        elif name == "instagram_get_posts":
            return await self._get_posts(arguments)
        elif name == "instagram_get_comments":
            return await self._get_comments(arguments)
        elif name == "instagram_get_scraping_tasks":
            return await self._get_scraping_tasks(arguments)
        elif name == "instagram_get_profile_stats":
            return await self._get_profile_stats(arguments)
        else:
            raise ValueError(f"Unknown Instagram tool: {name}")
    
    async def _get_profiles(self, args: dict) -> List[TextContent]:
        """Получение профилей Instagram"""
        limit = args.get("limit", 10)
        search = args.get("search")
        
        queryset = InstagramProfile.objects.all()
        if search:
            queryset = queryset.filter(username__icontains=search)
        
        profiles = queryset.select_related().prefetch_related('posts')[:limit]
        
        result = "# Instagram Profiles\n\n"
        for profile in profiles:
            result += f"## @{profile.username}\n"
            result += f"- **Full Name:** {profile.full_name or 'N/A'}\n"
            result += f"- **Followers:** {profile.followers_count:,}\n"
            result += f"- **Following:** {profile.following_count:,}\n"
            result += f"- **Posts:** {profile.posts_count:,}\n"
            result += f"- **Bio:** {profile.bio[:100]}{'...' if len(profile.bio or '') > 100 else ''}\n"
            result += f"- **Verified:** {'✅' if profile.is_verified else '❌'}\n"
            result += f"- **Private:** {'🔒' if profile.is_private else '🔓'}\n"
            result += f"- **Created:** {profile.created_at.strftime('%Y-%m-%d %H:%M')}\n"
            result += f"- **Updated:** {profile.updated_at.strftime('%Y-%m-%d %H:%M')}\n\n"
        
        return [TextContent(type="text", text=result)]
    
    async def _get_posts(self, args: dict) -> List[TextContent]:
        """Получение постов Instagram"""
        profile_username = args.get("profile_username")
        limit = args.get("limit", 10)
        
        queryset = InstagramPost.objects.select_related('author')
        if profile_username:
            queryset = queryset.filter(author__username=profile_username)
        
        posts = queryset.order_by('-created_at')[:limit]
        
        result = "# Instagram Posts\n\n"
        for post in posts:
            result += f"## Post {post.external_id}\n"
            result += f"- **Author:** @{post.author.username}\n"
            result += f"- **Caption:** {(post.caption or '')[:150]}{'...' if len(post.caption or '') > 150 else ''}\n"
            result += f"- **Likes:** {post.likes_count:,}\n"
            result += f"- **Comments:** {post.comments_count:,}\n"
            result += f"- **Type:** {post.post_type}\n"
            result += f"- **Created:** {post.created_at.strftime('%Y-%m-%d %H:%M')}\n"
            result += f"- **URL:** https://instagram.com/p/{post.external_id}/\n\n"
        
        return [TextContent(type="text", text=result)]
    
    async def _get_comments(self, args: dict) -> List[TextContent]:
        """Получение комментариев к посту"""
        post_id = args["post_id"]
        limit = args.get("limit", 10)
        
        try:
            post = InstagramPost.objects.get(id=post_id)
            comments = InstagramComment.objects.filter(post=post).select_related('author').order_by('-created_at')[:limit]
            
            result = f"# Comments for Post {post.external_id}\n\n"
            result += f"**Post by @{post.author.username}:** {(post.caption or '')[:100]}...\n\n"
            
            for comment in comments:
                result += f"## Comment by @{comment.author.username}\n"
                result += f"- **Text:** {comment.text}\n"
                result += f"- **Likes:** {comment.likes_count:,}\n"
                result += f"- **Created:** {comment.created_at.strftime('%Y-%m-%d %H:%M')}\n\n"
            
            return [TextContent(type="text", text=result)]
        except InstagramPost.DoesNotExist:
            return [TextContent(type="text", text=f"Post with ID {post_id} not found")]
    
    async def _get_scraping_tasks(self, args: dict) -> List[TextContent]:
        """Получение задач скрапинга"""
        status = args.get("status")
        limit = args.get("limit", 10)
        
        queryset = InstagramScrapingTask.objects.all()
        if status:
            queryset = queryset.filter(status=status)
        
        tasks = queryset.order_by('-created_at')[:limit]
        
        result = "# Instagram Scraping Tasks\n\n"
        for task in tasks:
            result += f"## Task {task.id}\n"
            result += f"- **Type:** {task.task_type}\n"
            result += f"- **Status:** {task.status}\n"
            result += f"- **Target:** {task.target_username or task.target_url}\n"
            result += f"- **Progress:** {task.progress}%\n"
            result += f"- **Created:** {task.created_at.strftime('%Y-%m-%d %H:%M')}\n"
            if task.error_message:
                result += f"- **Error:** {task.error_message}\n"
            result += "\n"
        
        return [TextContent(type="text", text=result)]
    
    async def _get_profile_stats(self, args: dict) -> List[TextContent]:
        """Получение статистики профиля"""
        username = args["username"]
        
        try:
            profile = InstagramProfile.objects.get(username=username)
            posts_count = InstagramPost.objects.filter(author=profile).count()
            comments_count = InstagramComment.objects.filter(author=profile).count()
            
            result = f"# Statistics for @{username}\n\n"
            result += f"## Profile Info\n"
            result += f"- **Full Name:** {profile.full_name or 'N/A'}\n"
            result += f"- **Followers:** {profile.followers_count:,}\n"
            result += f"- **Following:** {profile.following_count:,}\n"
            result += f"- **Posts (Instagram):** {profile.posts_count:,}\n"
            result += f"- **Verified:** {'✅' if profile.is_verified else '❌'}\n"
            result += f"- **Private:** {'🔒' if profile.is_private else '🔓'}\n\n"
            
            result += f"## Our Database Stats\n"
            result += f"- **Posts Collected:** {posts_count:,}\n"
            result += f"- **Comments Made:** {comments_count:,}\n"
            result += f"- **Profile Added:** {profile.created_at.strftime('%Y-%m-%d %H:%M')}\n"
            result += f"- **Last Updated:** {profile.updated_at.strftime('%Y-%m-%d %H:%M')}\n"
            
            return [TextContent(type="text", text=result)]
        except InstagramProfile.DoesNotExist:
            return [TextContent(type="text", text=f"Profile @{username} not found in database")]
```

### 6. Telegram инструменты (`mcp_server/tools/telegram_tools.py`)

```python
from typing import Any, List
from mcp.types import Tool, TextContent
from django.db.models import Q, Count
from telegram_manager.models import TelegramChat, TelegramUser, TelegramMessage

class TelegramTools:
    async def get_tools(self) -> List[Tool]:
        """Возвращает список доступных Telegram инструментов"""
        return [
            Tool(
                name="telegram_get_chats",
                description="Получить список Telegram чатов с возможностью поиска",
                inputSchema={
                    "type": "object",
                    "properties": {
                        "limit": {
                            "type": "integer", 
                            "default": 10,
                            "description": "Максимальное количество чатов"
                        },
                        "search": {
                            "type": "string",
                            "description": "Поиск по названию или username чата"
                        },
                        "chat_type": {
                            "type": "string",
                            "enum": ["channel", "group", "all"],
                            "default": "all",
                            "description": "Тип чата для фильтрации"
                        }
                    },
                    "additionalProperties": False
                }
            ),
            Tool(
                name="telegram_get_messages",
                description="Получить сообщения из конкретного чата",
                inputSchema={
                    "type": "object",
                    "properties": {
                        "chat_id": {
                            "type": "integer",
                            "description": "ID чата для получения сообщений"
                        },
                        "limit": {
                            "type": "integer", 
                            "default": 10,
                            "description": "Максимальное количество сообщений"
                        },
                        "search": {
                            "type": "string",
                            "description": "Поиск по тексту сообщений"
                        }
                    },
                    "required": ["chat_id"],
                    "additionalProperties": False
                }
            ),
            Tool(
                name="telegram_get_users",
                description="Получить пользователей Telegram",
                inputSchema={
                    "type": "object",
                    "properties": {
                        "limit": {
                            "type": "integer", 
                            "default": 10,
                            "description": "Максимальное количество пользователей"
                        },
                        "search": {
                            "type": "string",
                            "description": "Поиск по username или имени"
                        },
                        "verified_only": {
                            "type": "boolean",
                            "default": False,
                            "description": "Показать только верифицированных пользователей"
                        }
                    },
                    "additionalProperties": False
                }
            ),
            Tool(
                name="telegram_get_chat_stats",
                description="Получить статистику по чату",
                inputSchema={
                    "type": "object",
                    "properties": {
                        "chat_id": {
                            "type": "integer",
                            "description": "ID чата для получения статистики"
                        }
                    },
                    "required": ["chat_id"],
                    "additionalProperties": False
                }
            )
        ]
    
    async def call_tool(self, name: str, arguments: dict[str, Any]) -> List[TextContent]:
        """Выполнение Telegram инструментов"""
        if name == "telegram_get_chats":
            return await self._get_chats(arguments)
        elif name == "telegram_get_messages":
            return await self._get_messages(arguments)
        elif name == "telegram_get_users":
            return await self._get_users(arguments)
        elif name == "telegram_get_chat_stats":
            return await self._get_chat_stats(arguments)
        else:
            raise ValueError(f"Unknown Telegram tool: {name}")
    
    async def _get_chats(self, args: dict) -> List[TextContent]:
        """Получение чатов Telegram"""
        limit = args.get("limit", 10)
        search = args.get("search")
        chat_type = args.get("chat_type", "all")
        
        queryset = TelegramChat.objects.all()
        
        if search:
            queryset = queryset.filter(
                Q(title__icontains=search) | Q(username__icontains=search)
            )
        
        if chat_type == "channel":
            queryset = queryset.filter(broadcast=True)
        elif chat_type == "group":
            queryset = queryset.filter(broadcast=False)
        
        chats = queryset.annotate(
            messages_count=Count('messages')
        ).order_by('-participants_count')[:limit]
        
        result = "# Telegram Chats\n\n"
        for chat in chats:
            result += f"## {chat.title}\n"
            if chat.username:
                result += f"- **Username:** @{chat.username}\n"
            result += f"- **Type:** {'📢 Channel' if chat.broadcast else '👥 Group'}\n"
            result += f"- **Participants:** {chat.participants_count:,}\n" if chat.participants_count else ""
            result += f"- **Messages in DB:** {chat.messages_count:,}\n"
            result += f"- **Verified:** {'✅' if chat.verified else '❌'}\n"
            result += f"- **Created:** {chat.created_at.strftime('%Y-%m-%d %H:%M')}\n"
            result += f"- **Chat ID:** `{chat.chat_id}`\n\n"
        
        return [TextContent(type="text", text=result)]
    
    async def _get_messages(self, args: dict) -> List[TextContent]:
        """Получение сообщений из чата"""
        chat_id = args["chat_id"]
        limit = args.get("limit", 10)
        search = args.get("search")
        
        try:
            chat = TelegramChat.objects.get(chat_id=chat_id)
            queryset = TelegramMessage.objects.filter(chat=chat).select_related('from_user')
            
            if search:
                queryset = queryset.filter(text__icontains=search)
            
            messages = queryset.order_by('-date')[:limit]
            
            result = f"# Messages from {chat.title}\n\n"
            
            for message in messages:
                result += f"## Message {message.message_id}\n"
                if message.from_user:
                    result += f"- **From:** {message.from_user}\n"
                result += f"- **Date:** {message.date.strftime('%Y-%m-%d %H:%M:%S')}\n"
                if message.text:
                    text_preview = message.text[:200] + "..." if len(message.text) > 200 else message.text
                    result += f"- **Text:** {text_preview}\n"
                if message.media_type:
                    result += f"- **Media:** {message.media_type}\n"
                if message.is_forward:
                    result += f"- **Forwarded:** ✅\n"
                if message.is_reply:
                    result += f"- **Reply:** ✅\n"
                result += "\n"
            
            return [TextContent(type="text", text=result)]
        except TelegramChat.DoesNotExist:
            return [TextContent(type="text", text=f"Chat with ID {chat_id} not found")]
    
    async def _get_users(self, args: dict) -> List[TextContent]:
        """Получение пользователей Telegram"""
        limit = args.get("limit", 10)
        search = args.get("search")
        verified_only = args.get("verified_only", False)
        
        queryset = TelegramUser.objects.all()
        
        if search:
            queryset = queryset.filter(
                Q(username__icontains=search) | 
                Q(first_name__icontains=search) | 
                Q(last_name__icontains=search)
            )
        
        if verified_only:
            queryset = queryset.filter(is_verified=True)
        
        users = queryset.annotate(
            messages_count=Count('sent_messages')
        ).order_by('-messages_count')[:limit]
        
        result = "# Telegram Users\n\n"
        for user in users:
            result += f"## {user}\n"
            result += f"- **Full Name:** {user.full_name}\n"
            result += f"- **User ID:** `{user.user_id}`\n"
            if user.phone:
                result += f"- **Phone:** {user.phone}\n"
            result += f"- **Bot:** {'🤖' if user.is_bot else '👤'}\n"
            result += f"- **Verified:** {'✅' if user.is_verified else '❌'}\n"
            result += f"- **Premium:** {'💎' if user.is_premium else '❌'}\n"
            result += f"- **Messages Sent:** {user.messages_count:,}\n"
            if user.last_seen_date:
                result += f"- **Last Seen:** {user.last_seen_date.strftime('%Y-%m-%d %H:%M')}\n"
            result += f"- **Added to DB:** {user.created_at.strftime('%Y-%m-%d %H:%M')}\n\n"
        
        return [TextContent(type="text", text=result)]
    
    async def _get_chat_stats(self, args: dict) -> List[TextContent]:
        """Получение статистики чата"""
        chat_id = args["chat_id"]
        
        try:
            chat = TelegramChat.objects.get(chat_id=chat_id)
            
            # Статистика сообщений
            total_messages = TelegramMessage.objects.filter(chat=chat).count()
            messages_with_media = TelegramMessage.objects.filter(chat=chat).exclude(media_type='').count()
            forwarded_messages = TelegramMessage.objects.filter(chat=chat, is_forward=True).count()
            
            # Топ пользователей по активности
            top_users = TelegramMessage.objects.filter(chat=chat).values(
                'from_user__username', 'from_user__first_name', 'from_user__last_name'
            ).annotate(
                message_count=Count('id')
            ).order_by('-message_count')[:5]
            
            result = f"# Statistics for {chat.title}\n\n"
            result += f"## Chat Info\n"
            result += f"- **Type:** {'📢 Channel' if chat.broadcast else '👥 Group'}\n"
            result += f"- **Participants:** {chat.participants_count:,}\n" if chat.participants_count else ""
            result += f"- **Verified:** {'✅' if chat.verified else '❌'}\n"
            if chat.username:
                result += f"- **Username:** @{chat.username}\n"
            result += f"- **Chat ID:** `{chat.chat_id}`\n\n"
            
            result += f"## Message Statistics\n"
            result += f"- **Total Messages:** {total_messages:,}\n"
            result += f"- **Messages with Media:** {messages_with_media:,}\n"
            result += f"- **Forwarded Messages:** {forwarded_messages:,}\n\n"
            
            if top_users:
                result += f"## Top Active Users\n"
                for i, user in enumerate(top_users, 1):
                    username = user['from_user__username']
                    first_name = user['from_user__first_name']
                    last_name = user['from_user__last_name']
                    
                    display_name = f"@{username}" if username else f"{first_name} {last_name or ''}".strip()
                    result += f"{i}. **{display_name}** - {user['message_count']:,} messages\n"
            
            return [TextContent(type="text", text=result)]
        except TelegramChat.DoesNotExist:
            return [TextContent(type="text", text=f"Chat with ID {chat_id} not found")]
```

### 7. Core инструменты (`mcp_server/tools/core_tools.py`)

```python
from typing import Any, List
from mcp.types import Tool, TextContent
from django.db.models import Count
from django.utils import timezone
from datetime import timedelta

from instagram_manager.models import InstagramProfile, InstagramPost, InstagramScrapingTask
from telegram_manager.models import TelegramChat, TelegramUser, TelegramMessage
from core.models import ImportTask

class CoreTools:
    async def get_tools(self) -> List[Tool]:
        """Возвращает список основных инструментов системы"""
        return [
            Tool(
                name="core_get_system_stats",
                description="Получить общую статистику системы",
                inputSchema={
                    "type": "object",
                    "properties": {},
                    "additionalProperties": False
                }
            ),
            Tool(
                name="core_get_import_tasks",
                description="Получить задачи импорта данных",
                inputSchema={
                    "type": "object",
                    "properties": {
                        "limit": {
                            "type": "integer", 
                            "default": 10,
                            "description": "Максимальное количество задач"
                        },
                        "status": {
                            "type": "string",
                            "enum": ["pending", "running", "completed", "failed"],
                            "description": "Фильтр по статусу задач"
                        }
                    },
                    "additionalProperties": False
                }
            ),
            Tool(
                name="core_get_recent_activity",
                description="Получить недавнюю активность в системе",
                inputSchema={
                    "type": "object",
                    "properties": {
                        "hours": {
                            "type": "integer", 
                            "default": 24,
                            "description": "Количество часов для анализа активности"
                        }
                    },
                    "additionalProperties": False
                }
            )
        ]
    
    async def call_tool(self, name: str, arguments: dict[str, Any]) -> List[TextContent]:
        """Выполнение основных инструментов"""
        if name == "core_get_system_stats":
            return await self._get_system_stats(arguments)
        elif name == "core_get_import_tasks":
            return await self._get_import_tasks(arguments)
        elif name == "core_get_recent_activity":
            return await self._get_recent_activity(arguments)
        else:
            raise ValueError(f"Unknown core tool: {name}")
    
    async def _get_system_stats(self, args: dict) -> List[TextContent]:
        """Получение общей статистики системы"""
        # Instagram статистика
        instagram_profiles = InstagramProfile.objects.count()
        instagram_posts = InstagramPost.objects.count()
        instagram_tasks = InstagramScrapingTask.objects.count()
        
        # Telegram статистика
        telegram_chats = TelegramChat.objects.count()
        telegram_users = TelegramUser.objects.count()
        telegram_messages = TelegramMessage.objects.count()
        
        # Общие задачи импорта
        import_tasks = ImportTask.objects.count()
        
        result = "# System Statistics\n\n"
        
        result += "## Instagram Data\n"
        result += f"- **Profiles:** {instagram_profiles:,}\n"
        result += f"- **Posts:** {instagram_posts:,}\n"
        result += f"- **Scraping Tasks:** {instagram_tasks:,}\n\n"
        
        result += "## Telegram Data\n"
        result += f"- **Chats:** {telegram_chats:,}\n"
        result += f"- **Users:** {telegram_users:,}\n"
        result += f"- **Messages:** {telegram_messages:,}\n\n"
        
        result += "## System Tasks\n"
        result += f"- **Import Tasks:** {import_tasks:,}\n\n"
        
        # Недавняя активность
        last_24h = timezone.now() - timedelta(hours=24)
        recent_posts = InstagramPost.objects.filter(created_at__gte=last_24h).count()
        recent_messages = TelegramMessage.objects.filter(created_at__gte=last_24h).count()
        
        result += "## Recent Activity (24h)\n"
        result += f"- **New Instagram Posts:** {recent_posts:,}\n"
        result += f"- **New Telegram Messages:** {recent_messages:,}\n"
        
        return [TextContent(type="text", text=result)]
    
    async def _get_import_tasks(self, args: dict) -> List[TextContent]:
        """Получение задач импорта"""
        limit = args.get("limit", 10)
        status = args.get("status")
        
        queryset = ImportTask.objects.all()
        if status:
            queryset = queryset.filter(status=status)
        
        tasks = queryset.order_by('-created_at')[:limit]
        
        result = "# Import Tasks\n\n"
        for task in tasks:
            result += f"## Task {task.id}\n"
            result += f"- **Type:** {task.task_type}\n"
            result += f"- **Status:** {task.status}\n"
            result += f"- **Progress:** {task.progress}%\n"
            result += f"- **Created:** {task.created_at.strftime('%Y-%m-%d %H:%M')}\n"
            if task.completed_at:
                result += f"- **Completed:** {task.completed_at.strftime('%Y-%m-%d %H:%M')}\n"
            if task.error_message:
                result += f"- **Error:** {task.error_message}\n"
            result += "\n"
        
        return [TextContent(type="text", text=result)]
    
    async def _get_recent_activity(self, args: dict) -> List[TextContent]:
        """Получение недавней активности"""
        hours = args.get("hours", 24)
        since = timezone.now() - timedelta(hours=hours)
        
        # Instagram активность
        new_profiles = InstagramProfile.objects.filter(created_at__gte=since).count()
        new_posts = InstagramPost.objects.filter(created_at__gte=since).count()
        
        # Telegram активность
        new_chats = TelegramChat.objects.filter(created_at__gte=since).count()
        new_users = TelegramUser.objects.filter(created_at__gte=since).count()
        new_messages = TelegramMessage.objects.filter(created_at__gte=since).count()
        
        # Задачи
        new_tasks = ImportTask.objects.filter(created_at__gte=since).count()
        completed_tasks = ImportTask.objects.filter(
            completed_at__gte=since, 
            status='completed'
        ).count()
        
        result = f"# Recent Activity (Last {hours} hours)\n\n"
        
        result += "## New Data\n"
        result += f"- **Instagram Profiles:** {new_profiles:,}\n"
        result += f"- **Instagram Posts:** {new_posts:,}\n"
        result += f"- **Telegram Chats:** {new_chats:,}\n"
        result += f"- **Telegram Users:** {new_users:,}\n"
        result += f"- **Telegram Messages:** {new_messages:,}\n\n"
        
        result += "## Task Activity\n"
        result += f"- **New Tasks:** {new_tasks:,}\n"
        result += f"- **Completed Tasks:** {completed_tasks:,}\n"
        
        return [TextContent(type="text", text=result)]
```

### 8. Management команда (`mcp_server/management/commands/run_mcp_server.py`)

```python
from django.core.management.base import BaseCommand
import asyncio
from mcp_server.server import main

class Command(BaseCommand):
    help = 'Запуск MCP сервера для SocialManager'
    
    def add_arguments(self, parser):
        parser.add_argument(
            '--log-level',
            type=str,
            default='INFO',
            choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'],
            help='Уровень логирования'
        )
    
    def handle(self, *args, **options):
        import logging
        logging.basicConfig(level=getattr(logging, options['log_level']))
        
        self.stdout.write(
            self.style.SUCCESS('Запуск MCP сервера SocialManager...')
        )
        
        try:
            asyncio.run(main())
        except KeyboardInterrupt:
            self.stdout.write(
                self.style.WARNING('MCP сервер остановлен пользователем')
            )
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'Ошибка MCP сервера: {e}')
            )
```

### 9. Регистрация приложения

Добавьте в `SocialManager/settings/base.py`:

```python
INSTALLED_APPS = [
    # ... существующие приложения
    'mcp_server',
]
```

### 10. Запуск MCP сервера

```bash
# Через management команду
python manage.py run_mcp_server

# Или напрямую
python mcp_server/server.py
```

### 11. Конфигурация для Claude Desktop

Создайте или обновите файл конфигурации Claude Desktop (`~/Library/Application Support/Claude/claude_desktop_config.json` на macOS):

```json
{
  "mcpServers": {
    "socialmanager": {
      "command": "python",
      "args": ["/path/to/your/SocialManager/manage.py", "run_mcp_server"],
      "env": {
        "DJANGO_SETTINGS_MODULE": "SocialManager.settings.local"
      }
    }
  }
}
```

### Основные преимущества использования официального SDK:

1. **Официальная поддержка** - Использует стандартный MCP протокол
2. **Типизация** - Полная поддержка типов с помощью Pydantic
3. **Стабильность** - Официальный SDK обеспечивает совместимость
4. **Документация** - Хорошо документированный API
5. **Расширяемость** - Легко добавлять новые инструменты и ресурсы

### Доступные инструменты после реализации:

**Instagram:**
- `instagram_get_profiles` - Получение профилей с поиском
- `instagram_get_posts` - Получение постов с фильтрацией
- `instagram_get_comments` - Получение комментариев к постам
- `instagram_get_scraping_tasks` - Управление задачами скрапинга
- `instagram_get_profile_stats` - Детальная статистика профилей

**Telegram:**
- `telegram_get_chats` - Получение чатов с фильтрацией
- `telegram_get_messages` - Получение сообщений с поиском
- `telegram_get_users` - Получение пользователей
- `telegram_get_chat_stats` - Статистика по чатам

**Core:**
- `core_get_system_stats` - Общая статистика системы
- `core_get_import_tasks` - Управление задачами импорта
- `core_get_recent_activity` - Анализ недавней активности

Этот подход обеспечивает полную интеграцию с вашим Django приложением через официальный MCP SDK, предоставляя безопасный и эффективный доступ ко всем данным.