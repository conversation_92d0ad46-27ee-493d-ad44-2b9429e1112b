# MCP Server Implementation Plan for SocialManager

## Overview

This document outlines a comprehensive plan for implementing an MCP (Model Context Protocol) server for the SocialManager Django application. The server will provide remote access to Instagram and Telegram management functionality with integrated task management for long-running operations.

## Architecture

### Transport Layer
- **Transport**: Streamable HTTP (for remote access)
- **Framework**: FastMCP with async/await support
- **Security**: Initially disabled (to be added in future phase)

### Django Integration
- **Async Safety**: All Django ORM operations wrapped with centralized decorators
- **Database**: Direct access to Django models with connection pooling
- **Settings**: Load Django settings from environment
- **Validation**: Pydantic models for all input parameters
- **Error Handling**: Unified error format across all endpoints

## Core Components

### 1. Server Configuration

```python
# mcp_server.py
import os
import sys
import time
import traceback
from pathlib import Path
from functools import wraps
from typing import Optional, List, Dict, Any
from datetime import datetime
from contextvars import ContextVar

# Add project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Django setup
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "SocialManager.settings.production")
import django
django.setup()

from fastmcp import FastMCP
from asgiref.sync import sync_to_async
from pydantic import BaseModel, Field, ValidationError
import asyncio
import asyncpg
import logging

# Import all task classes statically
from instagram_manager.tasks.import_tasks import (
    ImportInstagramProfileTask,
    ImportInstagramPostsTask,
    ImportBatchPostsTask,
    ImportInstagramCommentsTask
)
from telegram_manager.tasks.import_tasks import (
    ImportTelegramChatsTask,
    ImportTelegramMessagesTask,
    ImportTelegramUsersTask
)
from core.tasks.base import BaseTask

# Initialize logger
logger = logging.getLogger(__name__)

# Initialize MCP server
mcp = FastMCP("SocialManager MCP", version="1.0.0")

# Context variable for tracking DB calls
db_call_context: ContextVar[str] = ContextVar('db_call', default='unknown')

# Debug mode from environment
DEBUG = os.getenv('DEBUG', 'False').lower() == 'true'

# Static task registry
TASK_REGISTRY = {
    "instagram.profile": ImportInstagramProfileTask,
    "instagram.posts": ImportInstagramPostsTask,
    "instagram.batch_posts": ImportBatchPostsTask,
    "instagram.comments": ImportInstagramCommentsTask,
    "telegram.chats": ImportTelegramChatsTask,
    "telegram.messages": ImportTelegramMessagesTask,
    "telegram.users": ImportTelegramUsersTask,
}
```

### 2. Centralized Async/Sync Handling

```python
# Decorator for async database operations
def async_db_operation(func):
    """Decorator to safely convert sync Django ORM operations to async"""
    @wraps(func)
    async def wrapper(*args, **kwargs):
        return await sync_to_async(func, thread_sensitive=True)(*args, **kwargs)
    return wrapper

# Performance tracking decorator
def track_sync_calls(func):
    """Decorator for tracking synchronous database calls"""
    @wraps(func)
    def wrapper(*args, **kwargs):
        start = time.time()
        context = db_call_context.get()
        
        result = func(*args, **kwargs)
        
        duration = time.time() - start
        if duration > 0.1:  # Warning for slow calls
            logger.warning(
                f"Slow sync DB call in {context}: {func.__name__} took {duration:.2f}s"
            )
        
        return result
    return wrapper
```

### 3. Unified Error Handling

```python
class MCPError(BaseModel):
    """Unified error response format"""
    error_type: str
    message: str
    details: Optional[Dict[str, Any]] = None
    traceback: Optional[str] = None

def handle_tool_errors(func):
    """Decorator for unified error handling across all tools"""
    @wraps(func)
    async def wrapper(*args, **kwargs):
        try:
            db_call_context.set(func.__name__)
            return await func(*args, **kwargs)
        except ValidationError as e:
            return MCPError(
                error_type="validation_error",
                message="Invalid parameters",
                details=e.errors()
            ).dict()
        except Exception as e:
            logger.exception(f"Tool error in {func.__name__}")
            return MCPError(
                error_type="internal_error",
                message=str(e),
                traceback=traceback.format_exc() if DEBUG else None
            ).dict()
    return wrapper
```

### 4. Parameter Validation Models

```python
# Instagram task parameter models
class InstagramProfileTaskParams(BaseModel):
    username: str = Field(..., min_length=1, max_length=30)

class InstagramPostsTaskParams(BaseModel):
    username: str = Field(..., min_length=1, max_length=30)
    limit: Optional[int] = Field(100, ge=1, le=1000)
    start_date: Optional[datetime] = None
    end_date: Optional[datetime] = None
    post_types: Optional[List[str]] = []

class InstagramBatchPostsTaskParams(BaseModel):
    usernames: List[str] = Field(..., min_items=1, max_items=50)
    batch_size: int = Field(10, ge=1, le=20)
    limit: Optional[int] = Field(None, ge=1, le=1000)
    import_comments: bool = False
    skip_media_download: bool = False
    save_media_to_gcs: bool = False

class InstagramCommentsTaskParams(BaseModel):
    post_id: int = Field(..., gt=0)
    limit: Optional[int] = Field(100, ge=1, le=1000)
    include_replies: bool = True

# Telegram task parameter models
class TelegramChatsTaskParams(BaseModel):
    chat_limit: str = Field("all")
    custom_limit: Optional[int] = Field(None, ge=1)
    include_private: bool = True
    include_groups: bool = True
    include_supergroups: bool = True
    include_channels: bool = True

class TelegramMessagesTaskParams(BaseModel):
    category: str = Field("all", regex="^(all|today|week|month|custom)$")
    date_from: Optional[datetime] = None
    date_to: Optional[datetime] = None
    message_limit: int = Field(1000, ge=1, le=100000)
    skip_user_fetch: bool = False

class TelegramUsersTaskParams(BaseModel):
    extract_from_messages: bool = False
    from_chat: Optional[int] = None
    update_existing: bool = False
    limit: Optional[int] = Field(None, ge=1)

# Parameter validator registry
PARAM_VALIDATORS = {
    "instagram.profile": InstagramProfileTaskParams,
    "instagram.posts": InstagramPostsTaskParams,
    "instagram.batch_posts": InstagramBatchPostsTaskParams,
    "instagram.comments": InstagramCommentsTaskParams,
    "telegram.chats": TelegramChatsTaskParams,
    "telegram.messages": TelegramMessagesTaskParams,
    "telegram.users": TelegramUsersTaskParams,
}
```

### 5. Instagram Management Tools

#### 5.1 Profile Management

```python
# Database helper functions
@async_db_operation
@track_sync_calls
def get_profile_by_username(username: str):
    from instagram_manager.services import InstagramProfileService
    service = InstagramProfileService()
    return service.get_by_username(username)

@async_db_operation
@track_sync_calls
def filter_profiles(**kwargs):
    from instagram_manager.repositories import InstagramProfileRepository
    repo = InstagramProfileRepository()
    return repo.filter(**kwargs)

@async_db_operation
@track_sync_calls
def count_profiles(**kwargs):
    from instagram_manager.repositories import InstagramProfileRepository
    repo = InstagramProfileRepository()
    return repo.count(**kwargs)

# MCP Tools
@mcp.tool()
@handle_tool_errors
async def instagram_get_profile(username: str) -> Dict[str, Any]:
    """Get Instagram profile information"""
    profile = await get_profile_by_username(username)
    
    if not profile:
        return MCPError(
            error_type="not_found",
            message=f"Profile {username} not found"
        ).dict()
    
    return {
        "id": profile.id,
        "username": profile.username,
        "full_name": profile.full_name,
        "bio": profile.bio,
        "follower_count": profile.follower_count,
        "following_count": profile.following_count,
        "post_count": profile.post_count,
        "is_verified": profile.is_verified,
        "is_private": profile.is_private,
        "created_at": profile.created_at.isoformat(),
        "updated_at": profile.updated_at.isoformat()
    }

@mcp.tool()
@handle_tool_errors
async def instagram_list_profiles(
    limit: int = 20,
    offset: int = 0,
    is_active: Optional[bool] = None,
    is_verified: Optional[bool] = None
) -> Dict[str, Any]:
    """List Instagram profiles with filtering"""
    filters = {}
    if is_active is not None:
        filters["is_active"] = is_active
    if is_verified is not None:
        filters["is_verified"] = is_verified
    
    profiles = await filter_profiles(limit=limit, offset=offset, **filters)
    total = await count_profiles(**filters)
    
    return {
        "total": total,
        "offset": offset,
        "limit": limit,
        "profiles": [
            {
                "id": p.id,
                "username": p.username,
                "full_name": p.full_name,
                "follower_count": p.follower_count,
                "is_verified": p.is_verified,
                "is_active": p.is_active
            }
            for p in profiles
        ]
    }
```

#### 5.2 Post Management

```python
@async_db_operation
@track_sync_calls
def get_posts_by_username(username: str, filters: dict, limit: int, offset: int):
    from instagram_manager.services import InstagramPostService
    service = InstagramPostService()
    return service.get_posts_by_username(username, filters=filters, limit=limit, offset=offset)

@mcp.tool()
@handle_tool_errors
async def instagram_get_posts(
    username: str,
    limit: int = 20,
    offset: int = 0,
    post_type: Optional[str] = None,
    start_date: Optional[str] = None,
    end_date: Optional[str] = None
) -> Dict[str, Any]:
    """Get posts for a specific Instagram profile"""
    # Parse dates if provided
    filters = {}
    if start_date:
        filters["posted_at__gte"] = datetime.fromisoformat(start_date)
    if end_date:
        filters["posted_at__lte"] = datetime.fromisoformat(end_date)
    if post_type:
        filters["post_type"] = post_type
    
    posts = await get_posts_by_username(username, filters, limit, offset)
    
    return {
        "username": username,
        "total": len(posts),
        "posts": [
            {
                "id": p.id,
                "external_id": p.external_id,
                "content": p.content[:200] + "..." if len(p.content) > 200 else p.content,
                "post_type": p.post_type,
                "like_count": p.like_count,
                "comment_count": p.comment_count,
                "posted_at": p.posted_at.isoformat(),
                "media_count": await sync_to_async(p.media.count)()
            }
            for p in posts
        ]
    }
```

#### 5.3 Comment Management

```python
@async_db_operation
@track_sync_calls
def get_comments_by_post(post_id: int, limit: int, include_replies: bool):
    from instagram_manager.services import InstagramCommentService
    service = InstagramCommentService()
    return service.get_comments_by_post(post_id, limit=limit, include_replies=include_replies)

@async_db_operation
@track_sync_calls
def post_comment(username: str, password: str, post_url: str, comment_text: str):
    from instagram_manager.services import CommentService
    service = CommentService()
    return service.post_comment(
        username=username,
        password=password,
        post_url=post_url,
        comment_text=comment_text
    )

@mcp.tool()
@handle_tool_errors
async def instagram_get_comments(
    post_id: int,
    limit: int = 50,
    include_replies: bool = True
) -> Dict[str, Any]:
    """Get comments for a specific Instagram post"""
    comments = await get_comments_by_post(post_id, limit, include_replies)
    
    return {
        "post_id": post_id,
        "total": len(comments),
        "comments": [
            {
                "id": c.id,
                "author_username": c.author.username if c.author else "Unknown",
                "content": c.content,
                "like_count": c.like_count,
                "reply_count": c.reply_count,
                "commented_at": c.commented_at.isoformat()
            }
            for c in comments
        ]
    }

@mcp.tool()
@handle_tool_errors
async def instagram_post_comment(
    username: str,
    password: str,
    post_url: str,
    comment_text: str
) -> Dict[str, Any]:
    """Post a comment to an Instagram post"""
    result = await post_comment(username, password, post_url, comment_text)
    
    return {
        "success": True,
        "comment_id": result.get("comment_id"),
        "posted_at": datetime.now().isoformat()
    }
```

### 6. Telegram Management Tools

#### 6.1 Chat Management

```python
@async_db_operation
@track_sync_calls
def filter_chats(**kwargs):
    from telegram_manager.repositories import TelegramChatRepository
    repo = TelegramChatRepository()
    return repo.filter(**kwargs)

@async_db_operation
@track_sync_calls
def count_chats(**kwargs):
    from telegram_manager.repositories import TelegramChatRepository
    repo = TelegramChatRepository()
    return repo.count(**kwargs)

@mcp.tool()
@handle_tool_errors
async def telegram_list_chats(
    limit: int = 20,
    offset: int = 0,
    chat_type: Optional[str] = None
) -> Dict[str, Any]:
    """List Telegram chats"""
    filters = {}
    if chat_type:
        filters["chat_type"] = chat_type
    
    chats = await filter_chats(limit=limit, offset=offset, **filters)
    total = await count_chats(**filters)
    
    return {
        "total": total,
        "offset": offset,
        "limit": limit,
        "chats": [
            {
                "id": c.id,
                "chat_id": c.chat_id,
                "title": c.title,
                "chat_type": c.chat_type,
                "member_count": c.member_count,
                "is_active": c.is_active
            }
            for c in chats
        ]
    }
```

#### 6.2 Message Management

```python
@async_db_operation
@track_sync_calls
def get_messages_by_chat(chat_id: int, filters: dict, limit: int, offset: int):
    from telegram_manager.services import TelegramMessageService
    service = TelegramMessageService()
    return service.get_messages_by_chat(chat_id, filters=filters, limit=limit, offset=offset)

@mcp.tool()
@handle_tool_errors
async def telegram_get_messages(
    chat_id: int,
    limit: int = 50,
    offset: int = 0,
    start_date: Optional[str] = None,
    end_date: Optional[str] = None
) -> Dict[str, Any]:
    """Get messages from a Telegram chat"""
    filters = {}
    if start_date:
        filters["date__gte"] = datetime.fromisoformat(start_date)
    if end_date:
        filters["date__lte"] = datetime.fromisoformat(end_date)
    
    messages = await get_messages_by_chat(chat_id, filters, limit, offset)
    
    return {
        "chat_id": chat_id,
        "total": len(messages),
        "messages": [
            {
                "id": m.id,
                "message_id": m.message_id,
                "text": m.text[:200] + "..." if len(m.text) > 200 else m.text,
                "from_user": m.from_user.username if m.from_user else "Unknown",
                "date": m.date.isoformat(),
                "views": m.views,
                "forwards": m.forwards
            }
            for m in messages
        ]
    }
```

### 7. Task Management Tools

#### 7.1 Task Creation

```python
@mcp.tool()
@handle_tool_errors
async def task_create_import(
    task_type: str,
    parameters: Dict[str, Any]
) -> Dict[str, Any]:
    """Create an import task (Instagram or Telegram)"""
    
    if task_type not in TASK_REGISTRY:
        return MCPError(
            error_type="invalid_task_type",
            message=f"Unknown task type: {task_type}",
            details={"available_types": list(TASK_REGISTRY.keys())}
        ).dict()
    
    # Validate parameters using Pydantic
    if task_type not in PARAM_VALIDATORS:
        return MCPError(
            error_type="configuration_error",
            message=f"No parameter validator configured for task type: {task_type}"
        ).dict()
    
    # Validate parameters
    validator_class = PARAM_VALIDATORS[task_type]
    try:
        validated_params = validator_class(**parameters).dict()
    except ValidationError as e:
        return MCPError(
            error_type="validation_error",
            message="Invalid task parameters",
            details=e.errors()
        ).dict()
    
    # Create and run task
    task_class = TASK_REGISTRY[task_type]
    task = task_class()
    
    # Run task asynchronously via Celery
    @async_db_operation
    def run_task():
        return task.run_async(**validated_params)
    
    result = await run_task()
    
    return {
        "success": True,
        "task_id": task.task_id,
        "celery_task_id": result.id,
        "status": "pending",
        "message": f"Task {task_type} created successfully"
    }
```

#### 7.2 Task Monitoring

```python
@async_db_operation
@track_sync_calls
def get_task_by_id(task_id: str):
    from core.models import TaskResult
    return TaskResult.objects.get(task_id=task_id)

@async_db_operation
def sync_task_with_celery(task):
    task.sync_with_celery()
    return task

@mcp.tool()
@handle_tool_errors
async def task_get_status(task_id: str) -> Dict[str, Any]:
    """Get status of a specific task"""
    try:
        task = await get_task_by_id(task_id)
        
        # Sync with Celery
        task = await sync_task_with_celery(task)
        
        return {
            "task_id": task.task_id,
            "task_type": task.task_type,
            "status": task.status,
            "progress_percentage": task.progress_percentage,
            "progress_message": task.progress_message,
            "total_items": task.total_items,
            "processed_items": task.processed_items,
            "failed_items": task.failed_items,
            "started_at": task.started_at.isoformat() if task.started_at else None,
            "completed_at": task.completed_at.isoformat() if task.completed_at else None,
            "error_message": task.error_message,
            "result": task.result
        }
    except Exception as e:
        if "DoesNotExist" in str(type(e)):
            return MCPError(
                error_type="not_found",
                message=f"Task {task_id} not found"
            ).dict()
        raise

@async_db_operation
@track_sync_calls
def list_tasks(status: Optional[str], task_type: Optional[str], limit: int, offset: int):
    from core.models import TaskResult
    
    queryset = TaskResult.objects.all()
    
    if status:
        queryset = queryset.filter(status=status)
    if task_type:
        queryset = queryset.filter(task_type=task_type)
    
    queryset = queryset.order_by("-created_at")
    
    total = queryset.count()
    tasks = list(queryset[offset:offset + limit])
    
    return total, tasks

@mcp.tool()
@handle_tool_errors
async def task_list(
    status: Optional[str] = None,
    task_type: Optional[str] = None,
    limit: int = 20,
    offset: int = 0
) -> Dict[str, Any]:
    """List tasks with filtering"""
    total, tasks = await list_tasks(status, task_type, limit, offset)
    
    return {
        "total": total,
        "offset": offset,
        "limit": limit,
        "tasks": [
            {
                "task_id": t.task_id,
                "task_type": t.task_type,
                "status": t.status,
                "progress_percentage": t.progress_percentage,
                "created_at": t.created_at.isoformat(),
                "completed_at": t.completed_at.isoformat() if t.completed_at else None
            }
            for t in tasks
        ]
    }
```

#### 7.3 Task Control

```python
@async_db_operation
def revoke_task(task):
    return task.revoke_celery_task(terminate=True)

@mcp.tool()
@handle_tool_errors
async def task_cancel(task_id: str) -> Dict[str, Any]:
    """Cancel a running task"""
    try:
        task = await get_task_by_id(task_id)
        
        if task.status not in ["pending", "running"]:
            return MCPError(
                error_type="invalid_state",
                message=f"Task is {task.status}, cannot cancel"
            ).dict()
        
        # Revoke Celery task
        success = await revoke_task(task)
        
        return {
            "success": success,
            "message": f"Task {task_id} cancelled" if success else "Failed to cancel task"
        }
        
    except Exception as e:
        if "DoesNotExist" in str(type(e)):
            return MCPError(
                error_type="not_found",
                message=f"Task {task_id} not found"
            ).dict()
        raise

@mcp.tool()
@handle_tool_errors
async def task_retry(task_id: str) -> Dict[str, Any]:
    """Retry a failed task"""
    try:
        task = await get_task_by_id(task_id)
        
        if task.status != "failed":
            return MCPError(
                error_type="invalid_state",
                message=f"Task is {task.status}, can only retry failed tasks"
            ).dict()
        
        if not task.can_retry():
            return MCPError(
                error_type="retry_limit_exceeded",
                message=f"Task has reached max retries ({task.max_retries})"
            ).dict()
        
        # Re-run the task using static registry
        if task.task_type not in TASK_REGISTRY:
            return MCPError(
                error_type="configuration_error",
                message=f"Task type {task.task_type} not found in registry"
            ).dict()
        
        task_class = TASK_REGISTRY[task.task_type]
        new_task = task_class(task_id=task.task_id)
        
        @async_db_operation
        def retry_task():
            return new_task.run_async(**task.parameters)
        
        result = await retry_task()
        
        return {
            "success": True,
            "message": f"Task {task_id} queued for retry",
            "celery_task_id": result.id
        }
        
    except Exception as e:
        if "DoesNotExist" in str(type(e)):
            return MCPError(
                error_type="not_found",
                message=f"Task {task_id} not found"
            ).dict()
        raise
```

#### 7.4 Task Results

```python
@mcp.tool()
@handle_tool_errors
async def task_get_result_data(
    task_id: str,
    data_type: str,
    limit: int = 100,
    offset: int = 0
) -> Dict[str, Any]:
    """Get imported data from a completed task"""
    try:
        task = await get_task_by_id(task_id)
        
        if task.status != "completed":
            return MCPError(
                error_type="invalid_state",
                message=f"Task is {task.status}, not completed"
            ).dict()
        
        # Based on task type, fetch the imported data
        if task.task_type.startswith("instagram."):
            return await _get_instagram_task_data(task, data_type, limit, offset)
        elif task.task_type.startswith("telegram."):
            return await _get_telegram_task_data(task, data_type, limit, offset)
        else:
            return MCPError(
                error_type="invalid_task_type",
                message=f"Unknown task type: {task.task_type}"
            ).dict()
            
    except Exception as e:
        if "DoesNotExist" in str(type(e)):
            return MCPError(
                error_type="not_found",
                message=f"Task {task_id} not found"
            ).dict()
        raise

async def _get_instagram_task_data(
    task: Any, 
    data_type: str, 
    limit: int, 
    offset: int
) -> Dict[str, Any]:
    """Helper to get Instagram task results"""
    
    if data_type == "profiles" and task.task_type == "instagram.profile":
        # Get imported profile
        username = task.parameters.get("username")
        return await instagram_get_profile(username)
        
    elif data_type == "posts" and task.task_type in ["instagram.posts", "instagram.batch_posts"]:
        # Get imported posts
        usernames = task.parameters.get("usernames", [])
        if usernames:
            return await instagram_get_posts(usernames[0], limit=limit, offset=offset)
            
    elif data_type == "comments" and task.task_type == "instagram.comments":
        # Get imported comments
        post_id = task.parameters.get("post_id")
        if post_id:
            return await instagram_get_comments(post_id, limit=limit)
    
    return MCPError(
        error_type="invalid_data_type",
        message=f"Invalid data_type '{data_type}' for task type '{task.task_type}'"
    ).dict()

async def _get_telegram_task_data(
    task: Any,
    data_type: str,
    limit: int,
    offset: int
) -> Dict[str, Any]:
    """Helper to get Telegram task results"""
    
    if data_type == "chats" and task.task_type == "telegram.chats":
        return await telegram_list_chats(limit=limit, offset=offset)
        
    elif data_type == "messages" and task.task_type == "telegram.messages":
        # Get messages from imported chats
        return await telegram_list_chats(limit=1)  # Example
        
    return MCPError(
        error_type="invalid_data_type",
        message=f"Invalid data_type '{data_type}' for task type '{task.task_type}'"
    ).dict()
```

### 8. Health Check and Monitoring

```python
@mcp.tool()
@handle_tool_errors
async def health_check() -> Dict[str, Any]:
    """Health check endpoint for monitoring"""
    try:
        # Check database connection
        @async_db_operation
        def check_db():
            from django.db import connection
            with connection.cursor() as cursor:
                cursor.execute("SELECT 1")
                return cursor.fetchone()
        
        db_result = await check_db()
        db_healthy = db_result is not None
        
        # Check Redis connection
        redis_healthy = True
        try:
            from django.core.cache import cache
            await sync_to_async(cache.set)("health_check", "ok", 1)
            redis_healthy = await sync_to_async(cache.get)("health_check") == "ok"
        except:
            redis_healthy = False
        
        return {
            "status": "healthy" if db_healthy and redis_healthy else "unhealthy",
            "database": "ok" if db_healthy else "error",
            "redis": "ok" if redis_healthy else "error",
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        return {
            "status": "unhealthy",
            "error": str(e),
            "timestamp": datetime.now().isoformat()
        }
```

## Task Types and Parameters

### Instagram Tasks

1. **instagram.profile**
   - `username`: Instagram username to import

2. **instagram.posts**
   - `username`: Instagram username
   - `limit`: Number of posts to import (1-1000, default: 100)
   - `start_date`: Filter posts from this date
   - `end_date`: Filter posts until this date
   - `post_types`: List of post types to import

3. **instagram.batch_posts**
   - `usernames`: List of Instagram usernames (1-50 items)
   - `batch_size`: Number of profiles to process in parallel (1-20, default: 10)
   - `limit`: Posts per profile (1-1000)
   - `import_comments`: Whether to import comments
   - `skip_media_download`: Skip downloading media files
   - `save_media_to_gcs`: Save media to Google Cloud Storage

4. **instagram.comments**
   - `post_id`: Instagram post ID
   - `limit`: Number of comments to import (1-1000, default: 100)
   - `include_replies`: Import comment replies

### Telegram Tasks

1. **telegram.chats**
   - `chat_limit`: Number of chats to import ("all" or custom number)
   - `custom_limit`: Custom limit if chat_limit is "custom"
   - `include_private`: Include private chats
   - `include_groups`: Include group chats
   - `include_supergroups`: Include supergroups
   - `include_channels`: Include channels

2. **telegram.messages**
   - `category`: Time range (all, today, week, month, custom)
   - `date_from`: Start date for custom range
   - `date_to`: End date for custom range
   - `message_limit`: Max messages per chat (1-100000, default: 1000)
   - `skip_user_fetch`: Skip fetching user details

3. **telegram.users**
   - `extract_from_messages`: Extract from existing messages
   - `from_chat`: Import from specific chat ID
   - `update_existing`: Update existing users
   - `limit`: Max users to import

## Server Startup

```python
# start_mcp.py
import uvicorn
from mcp_server import mcp, TASK_REGISTRY, PARAM_VALIDATORS

def validate_task_registry():
    """Validate all tasks at server startup"""
    print("🔍 Validating task registry...")
    
    for task_type, task_class in TASK_REGISTRY.items():
        # Check that class inherits from BaseTask
        if not issubclass(task_class, BaseTask):
            raise ValueError(f"Task {task_type} must inherit from BaseTask")
        
        # Check required methods
        required_methods = ['validate_params', 'execute_task']
        for method in required_methods:
            if not hasattr(task_class, method):
                raise ValueError(f"Task {task_type} missing {method} method")
        
        # Check parameter validator exists
        if task_type not in PARAM_VALIDATORS:
            raise ValueError(f"No parameter validator for {task_type}")
    
    print(f"✅ Validated {len(TASK_REGISTRY)} task types")

def setup_database_pool():
    """Configure database connection pooling"""
    import os
    from django.conf import settings
    
    # Add connection pool settings
    if hasattr(settings, 'DATABASES'):
        settings.DATABASES['default']['OPTIONS'] = {
            'connect_timeout': 10,
            'keepalives': 1,
            'keepalives_idle': 30,
            'keepalives_interval': 10,
            'keepalives_count': 5,
        }
    
    print("✅ Database connection pooling configured")

if __name__ == "__main__":
    # Validate configuration
    validate_task_registry()
    setup_database_pool()
    
    # Run server
    print("🚀 Starting MCP server on http://0.0.0.0:8000")
    uvicorn.run(
        mcp.get_asgi_app(),
        host="0.0.0.0",
        port=8000,
        log_level="info"
    )
```

## Docker Configuration

```dockerfile
# Dockerfile.mcp
FROM python:3.11-slim

WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    gcc \
    python3-dev \
    libpq-dev \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy application
COPY . .

# Set environment variables
ENV DJANGO_SETTINGS_MODULE=SocialManager.settings.production
ENV PYTHONPATH=/app
ENV PYTHONUNBUFFERED=1

# Expose MCP port
EXPOSE 8000

# Start MCP server
CMD ["python", "start_mcp.py"]
```

## Docker Compose Integration

```yaml
# docker-compose.yml addition
services:
  mcp:
    build:
      context: .
      dockerfile: Dockerfile.mcp
    ports:
      - "8000:8000"
    environment:
      - DJANGO_SETTINGS_MODULE=SocialManager.settings.production
      - DATABASE_URL=${DATABASE_URL}
      - REDIS_URL=${REDIS_URL}
      - BRIGHTDATA_API_TOKEN=${BRIGHTDATA_API_TOKEN}
      - FIELD_ENCRYPTION_KEY=${FIELD_ENCRYPTION_KEY}
      - DEBUG=${DEBUG:-False}
    depends_on:
      - db
      - redis
    networks:
      - socialmanager-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
```

## Client Usage Example

```python
from mcp import Client

# Connect to remote MCP server
async with Client("http://your-server:8000") as client:
    # List Instagram profiles
    profiles = await client.call_tool("instagram_list_profiles", {
        "limit": 10,
        "is_verified": True
    })
    
    if "error_type" in profiles:
        print(f"Error: {profiles['message']}")
        return
    
    # Create import task
    task = await client.call_tool("task_create_import", {
        "task_type": "instagram.batch_posts",
        "parameters": {
            "usernames": ["user1", "user2"],
            "limit": 100,
            "import_comments": True
        }
    })
    
    if not task.get("success"):
        print(f"Failed to create task: {task.get('message')}")
        return
    
    # Monitor task progress
    task_id = task["task_id"]
    while True:
        status = await client.call_tool("task_get_status", {
            "task_id": task_id
        })
        
        if "error_type" in status:
            print(f"Error getting status: {status['message']}")
            break
        
        if status["status"] in ["completed", "failed"]:
            break
            
        print(f"Progress: {status['progress_percentage']}%")
        await asyncio.sleep(5)
    
    # Get imported data
    if status["status"] == "completed":
        posts = await client.call_tool("task_get_result_data", {
            "task_id": task_id,
            "data_type": "posts",
            "limit": 50
        })
```

## Testing

```python
# test_mcp.py
import pytest
from fastmcp.testing import create_test_client
from mcp_server import mcp

@pytest.mark.asyncio
async def test_health_check():
    async with create_test_client(mcp) as client:
        result = await client.call_tool("health_check", {})
        assert result["status"] in ["healthy", "unhealthy"]
        assert "database" in result
        assert "redis" in result

@pytest.mark.asyncio
async def test_instagram_profile():
    async with create_test_client(mcp) as client:
        result = await client.call_tool(
            "instagram_get_profile",
            {"username": "test_user"}
        )
        # Check for either success or proper error format
        if "error_type" in result:
            assert "message" in result
        else:
            assert "username" in result

@pytest.mark.asyncio
async def test_task_creation_validation():
    async with create_test_client(mcp) as client:
        # Test with invalid parameters
        result = await client.call_tool(
            "task_create_import",
            {
                "task_type": "instagram.profile",
                "parameters": {"username": ""}  # Empty username
            }
        )
        assert result.get("error_type") == "validation_error"
        assert "details" in result

@pytest.mark.asyncio
async def test_task_creation_success():
    async with create_test_client(mcp) as client:
        result = await client.call_tool(
            "task_create_import",
            {
                "task_type": "instagram.profile",
                "parameters": {"username": "test_user"}
            }
        )
        assert result["success"] is True
        assert "task_id" in result
        assert "celery_task_id" in result
```

## Development Guidelines

### Required Practices

1. **All ORM Operations Must Use Decorators**
   ```python
   # CORRECT
   @async_db_operation
   @track_sync_calls
   def get_data():
       return Model.objects.all()
   
   # WRONG - Never use sync_to_async directly
   data = await sync_to_async(Model.objects.all)()
   ```

2. **All Parameters Must Be Validated**
   ```python
   # Define Pydantic model for every task type
   class MyTaskParams(BaseModel):
       field: str = Field(..., min_length=1)
   
   # Add to PARAM_VALIDATORS
   PARAM_VALIDATORS["my.task"] = MyTaskParams
   ```

3. **All Tools Must Use Error Handler**
   ```python
   @mcp.tool()
   @handle_tool_errors  # Always use this decorator
   async def my_tool(...):
       # Tool implementation
   ```

4. **Static Task Registry Only**
   - Import all task classes at module level
   - Add to TASK_REGISTRY dictionary
   - Never use dynamic imports

## Monitoring and Logging

- All MCP requests logged with correlation IDs via context variables
- Task execution tracked in Django admin
- Error tracking with detailed stack traces (only in debug mode)
- Health check endpoint at `/health`
- Connection pool monitoring via Django settings

## Critical Performance Considerations

1. **Connection Pooling**: Database connections are pooled with keepalive settings
2. **Async Boundaries**: All sync operations wrapped with `thread_sensitive=True`
3. **Slow Query Detection**: Warnings logged for queries taking >100ms
4. **Task Validation**: All tasks validated at startup to fail fast
5. **Error Serialization**: Consistent error format across all endpoints

## Notes

1. All database operations use centralized decorators for safety and monitoring
2. Static task registry prevents runtime errors and security issues
3. Pydantic validation ensures type safety for all inputs
4. Unified error handling provides consistent client experience
5. Long-running operations return immediately with task IDs
6. Task results stored in database for later retrieval
7. Server can be scaled horizontally with load balancer
8. Celery workers handle actual task execution