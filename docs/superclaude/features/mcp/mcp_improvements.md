# Критические улучшения для MCP плана

## 1. Замена динамической загрузки на статический реестр

```python
# ВМЕСТО динамического импорта:
task_mapping = {
    "instagram.profile": "instagram_manager.tasks.import_tasks.ImportInstagramProfileTask",
    # ...
}

# ИСПОЛЬЗОВАТЬ статический реестр:
from instagram_manager.tasks.import_tasks import (
    ImportInstagramProfileTask,
    ImportInstagramPostsTask,
    ImportBatchPostsTask,
    ImportInstagramCommentsTask
)
from telegram_manager.tasks.import_tasks import (
    ImportTelegramChatsTask,
    ImportTelegramMessagesTask,
    ImportTelegramUsersTask
)

TASK_REGISTRY = {
    "instagram.profile": ImportInstagramProfileTask,
    "instagram.posts": ImportInstagramPostsTask,
    "instagram.batch_posts": ImportBatchPostsTask,
    "instagram.comments": ImportInstagramCommentsTask,
    "telegram.chats": ImportTelegramChatsTask,
    "telegram.messages": ImportTelegramMessagesTask,
    "telegram.users": ImportTelegramUsersTask,
}
```

## 2. Pydantic валидация для всех параметров

```python
from pydantic import BaseModel, Field
from typing import Optional, List
from datetime import datetime

class InstagramProfileTaskParams(BaseModel):
    username: str = Field(..., min_length=1, max_length=30)
    
class InstagramPostsTaskParams(BaseModel):
    username: str = Field(..., min_length=1, max_length=30)
    limit: Optional[int] = Field(100, ge=1, le=1000)
    start_date: Optional[datetime] = None
    end_date: Optional[datetime] = None
    post_types: Optional[List[str]] = []
    
class InstagramBatchPostsTaskParams(BaseModel):
    usernames: List[str] = Field(..., min_items=1, max_items=50)
    batch_size: int = Field(10, ge=1, le=20)
    limit: Optional[int] = Field(None, ge=1, le=1000)
    import_comments: bool = False
    skip_media_download: bool = False
    save_media_to_gcs: bool = False

# Валидация в task_create_import:
@mcp.tool()
async def task_create_import(
    task_type: str,
    parameters: Dict[str, Any]
) -> Dict[str, Any]:
    if task_type not in TASK_REGISTRY:
        return {"success": False, "error": f"Unknown task type: {task_type}"}
    
    # Валидация параметров через Pydantic
    param_validators = {
        "instagram.profile": InstagramProfileTaskParams,
        "instagram.posts": InstagramPostsTaskParams,
        "instagram.batch_posts": InstagramBatchPostsTaskParams,
        # ...
    }
    
    try:
        validator = param_validators[task_type]
        validated_params = validator(**parameters).dict()
    except ValidationError as e:
        return {"success": False, "error": e.json()}
    
    # Создание задачи с валидированными параметрами
    task_class = TASK_REGISTRY[task_type]
    task = task_class()
    result = await sync_to_async(task.run_async)(**validated_params)
    
    return {
        "success": True,
        "task_id": task.task_id,
        "celery_task_id": result.id
    }
```

## 3. Централизованная обработка async/sync

```python
from asgiref.sync import sync_to_async
from functools import wraps

# Создать декоратор для всех ORM операций
def async_db_operation(func):
    @wraps(func)
    async def wrapper(*args, **kwargs):
        return await sync_to_async(func, thread_sensitive=True)(*args, **kwargs)
    return wrapper

# Использование:
@async_db_operation
def get_profile_by_username(username: str):
    return InstagramProfile.objects.get(username=username)

# В MCP tools:
profile = await get_profile_by_username(username)
```

## 4. Connection pooling конфигурация

```python
# В settings/production.py добавить:
DATABASES['default']['OPTIONS'] = {
    'connect_timeout': 10,
    'keepalives': 1,
    'keepalives_idle': 30,
    'keepalives_interval': 10,
    'keepalives_count': 5,
}

# Для async контекста использовать отдельный пул:
import asyncpg

async def create_async_pool():
    return await asyncpg.create_pool(
        database=os.getenv('DB_NAME'),
        user=os.getenv('DB_USER'),
        password=os.getenv('DB_PASSWORD'),
        host=os.getenv('DB_HOST'),
        port=os.getenv('DB_PORT', 5432),
        min_size=10,
        max_size=20,
        command_timeout=60
    )
```

## 5. Единая обработка ошибок

```python
class MCPError(BaseModel):
    error_type: str
    message: str
    details: Optional[Dict[str, Any]] = None
    traceback: Optional[str] = None

async def handle_tool_errors(func):
    @wraps(func)
    async def wrapper(*args, **kwargs):
        try:
            return await func(*args, **kwargs)
        except ValidationError as e:
            return MCPError(
                error_type="validation_error",
                message="Invalid parameters",
                details=e.errors()
            ).dict()
        except Exception as e:
            logger.exception(f"Tool error in {func.__name__}")
            return MCPError(
                error_type="internal_error",
                message=str(e),
                traceback=traceback.format_exc() if DEBUG else None
            ).dict()
    return wrapper

# Применить ко всем tools:
@mcp.tool()
@handle_tool_errors
async def instagram_get_profile(username: str) -> Dict[str, Any]:
    # ...
```

## 6. Проверка задач при старте

```python
# В start_mcp.py:
def validate_task_registry():
    """Проверить все задачи при старте сервера"""
    for task_type, task_class in TASK_REGISTRY.items():
        # Проверить что класс наследуется от BaseTask
        if not issubclass(task_class, BaseTask):
            raise ValueError(f"Task {task_type} must inherit from BaseTask")
        
        # Проверить наличие необходимых методов
        required_methods = ['validate_params', 'execute_task']
        for method in required_methods:
            if not hasattr(task_class, method):
                raise ValueError(f"Task {task_type} missing {method} method")
        
        # Проверить наличие валидатора параметров
        if task_type not in param_validators:
            raise ValueError(f"No parameter validator for {task_type}")
    
    print(f"✅ Validated {len(TASK_REGISTRY)} task types")

# Вызвать при старте
if __name__ == "__main__":
    validate_task_registry()
    uvicorn.run(mcp.get_asgi_app(), host="0.0.0.0", port=8000)
```

## 7. Мониторинг async/sync границ (минимальный)

```python
import time
from contextvars import ContextVar

# Контекстная переменная для отслеживания
db_call_context: ContextVar[str] = ContextVar('db_call', default='unknown')

def track_sync_calls(func):
    """Декоратор для отслеживания синхронных вызовов"""
    @wraps(func)
    def wrapper(*args, **kwargs):
        start = time.time()
        context = db_call_context.get()
        
        result = func(*args, **kwargs)
        
        duration = time.time() - start
        if duration > 0.1:  # Предупреждение для медленных вызовов
            logger.warning(
                f"Slow sync DB call in {context}: {func.__name__} took {duration:.2f}s"
            )
        
        return result
    return wrapper

# Применить к репозиториям
class BaseRepository:
    @track_sync_calls
    def get(self, **kwargs):
        # ...
```

## Итоговые рекомендации

1. **Немедленно внедрить**:
   - Статический реестр задач
   - Pydantic валидацию параметров
   - Централизованную обработку ошибок

2. **Внедрить в первую неделю**:
   - Connection pooling
   - Проверку задач при старте
   - Базовый мониторинг async/sync вызовов

3. **Строгие правила разработки**:
   - Все ORM вызовы ТОЛЬКО через sync_to_async
   - Все параметры ТОЛЬКО через Pydantic
   - Все ошибки ТОЛЬКО через единый формат

Эти изменения критически важны для стабильной работы MCP сервера под нагрузкой.