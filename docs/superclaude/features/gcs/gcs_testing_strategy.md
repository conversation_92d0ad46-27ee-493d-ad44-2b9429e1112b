# Стратегия тестирования: GCS интеграция

**Версия**: 1.0  
**Дата**: 2025-01-10  
**Охват**: Unit, Integration, E2E, Manual тесты

## 1. Обзор тестирования

### 1.1 Цели тестирования
- ✅ Убедиться что GCS upload работает корректно
- ✅ Проверить fallback на локальное хранение при ошибках
- ✅ Валидировать правильность URL в админке и экспортах
- ✅ Гарантировать обратную совместимость
- ✅ Проверить производительность

### 1.2 Критические сценарии
1. Upload медиа в GCS при batch import
2. Отображение GCS URL в админке
3. Экспорт с GCS URLs
4. Fallback при недоступности GCS
5. Удаление файлов из GCS

## 2. Unit Tests

### 2.1 Test GCSService

**Файл**: `tests/core/storage/test_gcs_service.py`

```python
import pytest
from unittest.mock import Mock, patch
from core.storage.gcs_service import GCSService


class TestGCSService:
    """Unit tests for GCS service"""
    
    @pytest.fixture
    def mock_storage_client(self):
        """Mock Google Cloud Storage client"""
        with patch('core.storage.gcs_service.storage.Client') as mock:
            yield mock
    
    @pytest.fixture
    def gcs_service(self, mock_storage_client, settings):
        """GCS service instance with mocked client"""
        settings.GCS_BUCKET_NAME = 'test-bucket'
        return GCSService()
    
    def test_upload_file_success(self, gcs_service, mock_storage_client):
        """Test successful file upload"""
        # Arrange
        mock_blob = Mock()
        mock_blob.public_url = 'https://storage.googleapis.com/test-bucket/test.jpg'
        gcs_service.bucket.blob.return_value = mock_blob
        
        # Act
        url = gcs_service.upload_file(
            file_content=b'test content',
            original_name='test.jpg',
            folder='test/folder'
        )
        
        # Assert
        assert url == 'https://storage.googleapis.com/test-bucket/test.jpg'
        mock_blob.upload_from_string.assert_called_once_with(
            b'test content',
            content_type='image/jpeg'
        )
    
    def test_upload_file_with_content_type(self, gcs_service):
        """Test upload with explicit content type"""
        mock_blob = Mock()
        gcs_service.bucket.blob.return_value = mock_blob
        
        gcs_service.upload_file(
            file_content=b'video content',
            original_name='video.mp4',
            content_type='video/mp4'
        )
        
        mock_blob.upload_from_string.assert_called_with(
            b'video content',
            content_type='video/mp4'
        )
    
    def test_delete_file_success(self, gcs_service):
        """Test successful file deletion"""
        mock_blob = Mock()
        gcs_service.bucket.blob.return_value = mock_blob
        
        result = gcs_service.delete_file(
            'https://storage.googleapis.com/test-bucket/test/file.jpg'
        )
        
        assert result is True
        gcs_service.bucket.blob.assert_called_with('test/file.jpg')
        mock_blob.delete.assert_called_once()
    
    def test_delete_file_invalid_url(self, gcs_service):
        """Test deletion with invalid URL"""
        result = gcs_service.delete_file('https://example.com/file.jpg')
        assert result is False
```

### 2.2 Test InstagramMedia Model

**Файл**: `tests/instagram_manager/test_models_gcs.py`

```python
import pytest
from instagram_manager.models import InstagramMedia


class TestInstagramMediaGCS:
    """Test InstagramMedia GCS functionality"""
    
    def test_get_display_url_priority_gcs(self):
        """Test that GCS URL has highest priority"""
        media = InstagramMedia(
            external_id='123',
            media_url='https://instagram.com/media.jpg',
            gcs_url='https://storage.googleapis.com/bucket/media.jpg',
            is_downloaded=True
        )
        # Should return GCS URL even if local_path exists
        assert media.get_display_url() == 'https://storage.googleapis.com/bucket/media.jpg'
    
    def test_get_display_url_fallback_local(self):
        """Test fallback to local URL when no GCS"""
        media = InstagramMedia(
            external_id='123',
            media_url='https://instagram.com/media.jpg',
            is_downloaded=True
        )
        media.local_path.name = 'instagram/media/123.jpg'
        
        with patch.object(media.local_path, 'url', '/media/instagram/media/123.jpg'):
            assert media.get_display_url() == '/media/instagram/media/123.jpg'
    
    def test_get_display_url_fallback_external(self):
        """Test fallback to external URL"""
        media = InstagramMedia(
            external_id='123',
            media_url='https://instagram.com/media.jpg',
            is_downloaded=False
        )
        assert media.get_display_url() == 'https://instagram.com/media.jpg'
```

### 2.3 Test MediaHandler

**Файл**: `tests/instagram_manager/test_media_handler_gcs.py`

```python
import pytest
from unittest.mock import Mock, patch
from instagram_manager.instagram_api.data_handlers.media_handler import MediaHandler
from instagram_manager.models import InstagramMedia


class TestMediaHandlerGCS:
    """Test MediaHandler GCS functionality"""
    
    @pytest.fixture
    def media_handler_gcs(self, settings):
        """Media handler with GCS enabled"""
        settings.GCS_BUCKET_NAME = 'test-bucket'
        return MediaHandler(save_to_gcs=True)
    
    @pytest.fixture
    def sample_media(self):
        """Sample InstagramMedia instance"""
        return InstagramMedia(
            external_id='123',
            media_url='https://instagram.com/media.jpg',
            media_type='photo'
        )
    
    @patch('requests.get')
    def test_download_media_to_gcs(self, mock_get, media_handler_gcs, sample_media):
        """Test downloading media to GCS"""
        # Mock HTTP response
        mock_response = Mock()
        mock_response.content = b'image content'
        mock_response.headers = {'Content-Type': 'image/jpeg'}
        mock_response.raise_for_status = Mock()
        mock_get.return_value = mock_response
        
        # Mock GCS service
        with patch.object(media_handler_gcs, 'gcs_service') as mock_gcs:
            mock_gcs.upload_file.return_value = 'https://storage.googleapis.com/bucket/123.jpg'
            
            # Execute
            result = media_handler_gcs.download_media(sample_media)
            
            # Assert
            assert result is True
            assert sample_media.gcs_url == 'https://storage.googleapis.com/bucket/123.jpg'
            assert sample_media.is_downloaded is True
            mock_gcs.upload_file.assert_called_once()
    
    @patch('requests.get')
    def test_download_media_gcs_fallback(self, mock_get, media_handler_gcs, sample_media):
        """Test fallback to local storage when GCS fails"""
        # Mock HTTP response
        mock_response = Mock()
        mock_response.content = b'image content'
        mock_response.headers = {'Content-Type': 'image/jpeg'}
        mock_get.return_value = mock_response
        
        # Mock GCS failure
        with patch.object(media_handler_gcs, 'gcs_service') as mock_gcs:
            mock_gcs.upload_file.side_effect = Exception("GCS error")
            
            # Execute
            result = media_handler_gcs.download_media(sample_media)
            
            # Assert - should fallback to local storage
            assert result is True
            assert sample_media.gcs_url is None
            assert sample_media.is_downloaded is True
```

## 3. Integration Tests

### 3.1 Test Form Integration

**Файл**: `tests/instagram_manager/test_forms_gcs.py`

```python
import pytest
from django.test import TestCase
from instagram_manager.forms import BatchImportPostsForm


class TestBatchImportFormGCS(TestCase):
    """Test batch import form with GCS option"""
    
    def test_form_has_gcs_field(self):
        """Test that form includes GCS checkbox"""
        form = BatchImportPostsForm()
        assert 'save_media_to_gcs' in form.fields
        assert form.fields['save_media_to_gcs'].label == 'Save media to Google Cloud Storage'
    
    def test_form_hides_gcs_when_not_configured(self):
        """Test GCS field is hidden when not configured"""
        with self.settings(GCS_BUCKET_NAME=''):
            form = BatchImportPostsForm()
            assert isinstance(
                form.fields['save_media_to_gcs'].widget,
                forms.HiddenInput
            )
    
    def test_form_valid_with_gcs(self):
        """Test form validation with GCS option"""
        form_data = {
            'profiles': ['test_profile'],
            'download_media': True,
            'save_media_to_gcs': True,
            'posts_limit': 10
        }
        form = BatchImportPostsForm(data=form_data)
        assert form.is_valid()
        assert form.cleaned_data['save_media_to_gcs'] is True
```

### 3.2 Test View Integration

**Файл**: `tests/instagram_manager/test_views_gcs.py`

```python
import pytest
from django.test import TestCase, Client
from django.contrib.auth import get_user_model
from unittest.mock import patch, Mock


class TestBatchImportViewGCS(TestCase):
    """Test batch import view with GCS support"""
    
    def setUp(self):
        self.client = Client()
        self.user = get_user_model().objects.create_superuser(
            username='admin',
            password='admin123'
        )
        self.client.login(username='admin', password='admin123')
    
    @patch('instagram_manager.tasks.ImportBatchPostsTask.run_async')
    def test_batch_import_with_gcs(self, mock_task):
        """Test batch import with GCS option enabled"""
        response = self.client.post(
            '/instagram/admin/batch-import-posts/do-import/',
            {
                'profiles': ['test_profile'],
                'download_media': True,
                'save_media_to_gcs': True,
                'posts_limit': 10
            }
        )
        
        # Check task was called with GCS flag
        mock_task.assert_called_once()
        call_args = mock_task.call_args[1]
        assert call_args['save_to_gcs'] is True
```

### 3.3 Test Celery Task Integration

**Файл**: `tests/instagram_manager/test_tasks_gcs.py`

```python
import pytest
from unittest.mock import Mock, patch
from instagram_manager.tasks import ImportBatchPostsTask


class TestImportTasksGCS:
    """Test import tasks with GCS support"""
    
    @patch('instagram_manager.tasks.MediaHandler')
    def test_import_task_with_gcs(self, mock_media_handler_class):
        """Test import task creates MediaHandler with GCS flag"""
        # Setup
        task = ImportBatchPostsTask()
        mock_handler = Mock()
        mock_media_handler_class.return_value = mock_handler
        
        # Execute
        with patch.object(task, 'update_progress'):
            task.execute(
                profiles=['test_profile'],
                download_media=True,
                save_to_gcs=True
            )
        
        # Assert MediaHandler created with save_to_gcs=True
        mock_media_handler_class.assert_called_with(save_to_gcs=True)
```

## 4. End-to-End Tests

### 4.1 Test Complete Flow

**Файл**: `tests/instagram_manager/test_e2e_gcs.py`

```python
import pytest
from django.test import TransactionTestCase
from unittest.mock import patch, Mock
from instagram_manager.models import InstagramProfile, InstagramPost, InstagramMedia


class TestGCSEndToEnd(TransactionTestCase):
    """End-to-end test for GCS integration"""
    
    @patch('instagram_manager.instagram_api.client.BrightDataClient')
    @patch('core.storage.gcs_service.storage.Client')
    def test_complete_import_flow_with_gcs(self, mock_gcs_client, mock_bright_data):
        """Test complete import flow with GCS storage"""
        # Setup mocks
        self._setup_brightdata_mock(mock_bright_data)
        self._setup_gcs_mock(mock_gcs_client)
        
        # Import profile with GCS
        from instagram_manager.services import PostService
        service = PostService()
        
        posts = service.import_posts_for_profile(
            profile_username='test_user',
            download_media=True,
            save_to_gcs=True
        )
        
        # Verify results
        assert len(posts) == 1
        media = posts[0].media.first()
        assert media.gcs_url == 'https://storage.googleapis.com/test-bucket/media/123.jpg'
        assert media.is_downloaded is True
        
        # Test display URL
        assert media.get_display_url() == media.gcs_url
    
    def _setup_brightdata_mock(self, mock_client):
        """Setup BrightData API mock"""
        mock_instance = Mock()
        mock_client.return_value = mock_instance
        
        # Mock profile data
        mock_instance.get_instagram_profile.return_value = {
            'username': 'test_user',
            'full_name': 'Test User',
            'follower_count': 100
        }
        
        # Mock posts data
        mock_instance.get_instagram_posts.return_value = [{
            'id': '123',
            'caption': 'Test post',
            'media': [{
                'id': '456',
                'media_url': 'https://instagram.com/media.jpg',
                'media_type': 'IMAGE'
            }]
        }]
    
    def _setup_gcs_mock(self, mock_client):
        """Setup GCS mock"""
        mock_bucket = Mock()
        mock_blob = Mock()
        mock_blob.public_url = 'https://storage.googleapis.com/test-bucket/media/123.jpg'
        
        mock_client.return_value.bucket.return_value = mock_bucket
        mock_bucket.blob.return_value = mock_blob
```

## 5. Manual Testing Plan

### 5.1 Pre-requisites
- [ ] GCS bucket created and configured
- [ ] Service account with write permissions
- [ ] Environment variables set

### 5.2 Test Scenarios

#### Scenario 1: Basic GCS Upload
1. Navigate to `/instagram/admin/batch-import-posts/`
2. Select a test profile
3. Check "Download media files"
4. Check "Save media to Google Cloud Storage"
5. Set posts limit to 5
6. Submit form
7. **Expected**: Task completes, media URLs show GCS paths

#### Scenario 2: Admin Display
1. Go to Instagram Posts in admin
2. Click on a post with GCS media
3. **Expected**: Media preview shows images from GCS URLs
4. Hover over media preview
5. **Expected**: URL shows storage.googleapis.com

#### Scenario 3: Export with GCS
1. Go to Majila export
2. Select posts with GCS media
3. Export to JSON
4. **Expected**: JSON contains GCS URLs for media

#### Scenario 4: GCS Failure Handling
1. Temporarily misconfigure GCS credentials
2. Try batch import with GCS option
3. **Expected**: Import succeeds, media saved locally
4. Check logs for GCS fallback messages

#### Scenario 5: Mixed Storage
1. Import some posts with local storage
2. Import new posts with GCS storage
3. View all posts in admin
4. **Expected**: Both local and GCS media display correctly

### 5.3 Performance Testing
- [ ] Import 100 posts with GCS enabled
- [ ] Measure time vs local storage
- [ ] Monitor memory usage
- [ ] Check GCS bandwidth usage

## 6. Test Data

### 6.1 Test Fixtures

**Файл**: `tests/fixtures/gcs_test_data.json`

```json
{
  "test_profiles": [
    {
      "username": "gcs_test_user",
      "full_name": "GCS Test User",
      "profile_pic_url": "https://example.com/profile.jpg"
    }
  ],
  "test_posts": [
    {
      "external_id": "gcs_test_post_1",
      "caption": "Test post for GCS",
      "media": [
        {
          "external_id": "gcs_media_1",
          "media_url": "https://example.com/test1.jpg",
          "media_type": "photo"
        }
      ]
    }
  ]
}
```

## 7. CI/CD Integration

### 7.1 GitHub Actions

```yaml
name: GCS Integration Tests

on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v2
    
    - name: Set up Python
      uses: actions/setup-python@v2
      with:
        python-version: 3.11
    
    - name: Install dependencies
      run: |
        pip install uv
        uv sync
    
    - name: Run GCS tests
      env:
        GCS_BUCKET_NAME: test-bucket
        GOOGLE_APPLICATION_CREDENTIALS: ${{ secrets.GCS_TEST_CREDENTIALS }}
      run: |
        uv run pytest tests/ -k "gcs" -v
```

## 8. Monitoring & Validation

### 8.1 Production Checks
```python
# Management command: validate_gcs_media.py
from django.core.management.base import BaseCommand
from instagram_manager.models import InstagramMedia

class Command(BaseCommand):
    def handle(self, *args, **options):
        # Check GCS media accessibility
        gcs_media = InstagramMedia.objects.filter(
            gcs_url__isnull=False
        )
        
        for media in gcs_media:
            response = requests.head(media.gcs_url)
            if response.status_code != 200:
                self.stdout.write(
                    self.style.ERROR(f'Invalid GCS URL: {media.gcs_url}')
                )
```

## 9. Rollback Testing

### 9.1 Rollback Scenario
1. Deploy with GCS enabled
2. Upload some media to GCS
3. Disable GCS in settings
4. **Expected**: Existing GCS media still accessible
5. New imports use local storage
6. No errors in admin or exports