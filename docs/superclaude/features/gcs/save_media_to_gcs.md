# Функционал: Опциональное сохранение media файлов в Google Cloud Storage

## Бизнес-требования
- Нам нужно добавить на /instagram/admin/batch-import-posts/ и остальных местах где вызывается импорт параметр - "Save media in GCS"
- Если этот параметр True то надо сохранять все медиа на Google Cloud Storage а не локально!
- Все файлы после загрузки на GCS дожны правильно прописаться во всех профилях/постах и остальном что бы правильно отображатся в админке 
- Так же если фaйлы сохранены в GCS то при экспорте в maji и любом другом экспорте должны проставлятся все правильные url на GCS
- Не должен пострадать текущий функционал
- Необходимо покрытие тестами нового функционала

## Технические детали
вот пример кода из другого нашего проекта как работаем с GCS
models.py

```python
"""
File model for handling file uploads to Google Cloud Storage (GCS).
"""
import uuid
from io import BytesIO
from typing import Optional, Tuple
from urllib.parse import unquote

from PIL import Image
from django.conf import settings
from django.db import IntegrityError, models
from google.cloud import storage
from loguru import logger

from users.models import User


class RelationChoices(models.TextChoices):
    """
    TextChoices for file relation types.
    """

    AVATAR = "avatar"
    COVER = "cover"
    IMAGE = "image"
    LINK_IMAGE = "link_image"
    PREVIEW = "preview"
    ICON = "icon"
    QR_CODE = "qr_code"
    FILE = "file"


class File(models.Model):
    """
    Model for handling file uploads to Google Cloud Storage (GCS).
    """

    name = models.CharField(max_length=255)
    url = models.CharField(max_length=1024)
    owner = models.ForeignKey(
        User, on_delete=models.SET_NULL, null=True, related_name="files"
    )
    # TODO: make a relation mandatory
    relation = models.CharField(
        max_length=255,
        choices=RelationChoices,
        default=RelationChoices.FILE,
        null=True,
        blank=True,
    )
    width = models.PositiveIntegerField(null=True, blank=True)
    height = models.PositiveIntegerField(null=True, blank=True)

    original_name = models.CharField(max_length=255, null=True, blank=True)
    file_size = models.PositiveIntegerField(null=True, blank=True)

    def __str__(self):
        return self.name

    @staticmethod
    def _upload_file_to_gcs(
        file_contents: bytes, blob_name: str, content_type: str
    ) -> str:
        """
        Uploads file contents to GCS and returns public URL.
        """
        try:
            client = storage.Client()
            bucket = client.bucket(settings.GCS_BUCKET_NAME)
            blob = bucket.blob(blob_name)
            blob.upload_from_string(file_contents, content_type=content_type)
            return blob.public_url
        except Exception as err:
            logger.error(f"GCS upload failed: {err}")
            raise

    @staticmethod
    def base_path(relation: str) -> str:
        """
        Returns the base path for the file in GCS based on the relation.
        """
        return f"{relation}s"

    @classmethod
    def create_file(cls, file, owner, relation=""):
        """
        Creates Image object and uploads file to GCS.
        """
        try:
            unique_name = f"{uuid.uuid4()}_{file.name}"
            blob_path = f"{cls.base_path(relation)}/{unique_name}"
            contents = file.read()
            width = None
            height = None
            if file.content_type and file.content_type.startswith("image/"):
                image = Image.open(BytesIO(contents))
                width, height = image.size

            public_url = cls._upload_file_to_gcs(
                file_contents=contents,
                blob_name=blob_path,
                content_type=file.content_type or "application/octet-stream",
            )

            return cls.objects.create(
                name=unique_name,
                url=public_url,
                owner=owner,
                relation=relation,
                width=width,
                height=height,
                file_size=file.size,
                original_name=file.name,
            )
        except ValueError as err:
            logger.error(f"ValueError: {err}")
            raise
        except IntegrityError as err:
            logger.error(f"IntegrityError: {err}")
            raise
        except Exception as err:
            logger.error(f"Unexpected error: {err}")
            raise

    @classmethod
    def create_image_from_bytes(cls, image_bytes: BytesIO, image_name: str):
        """
        Creates Image object from raw bytes and uploads to GCS.
        """
        try:
            unique_name = f"{uuid.uuid4()}_{image_name}"
            blob_path = f"images/{unique_name}"
            contents = image_bytes.getvalue()
            from PIL import Image

            image = Image.open(BytesIO(contents))
            width, height = image.size

            public_url = cls._upload_file_to_gcs(
                file_contents=contents, blob_name=blob_path, content_type="image/jpeg"
            )

            return cls.objects.create(
                name=unique_name, url=public_url, width=width, height=height
            )
        except ValueError as err:
            logger.error(f"ValueError: {err}")
            raise
        except IntegrityError as err:
            logger.error(f"IntegrityError: {err}")
            raise
        except Exception as err:
            logger.error(f"Unexpected error: {err}")
            raise

    @classmethod
    def create_video_from_bytes(cls, video_bytes: BytesIO, video_name: str):
        """
        Creates Video object from raw bytes and uploads to GCS.
        """
        try:
            unique_name = f"{uuid.uuid4()}_{video_name}"
            blob_path = f"videos/{unique_name}"
            contents = video_bytes.getvalue()

            public_url = cls._upload_file_to_gcs(
                file_contents=contents, blob_name=blob_path, content_type="video/mp4"
            )

            return cls.objects.create(
                name=unique_name, url=public_url, relation=RelationChoices.FILE
            )
        except ValueError as err:
            logger.error(f"ValueError: {err}")
            raise
        except IntegrityError as err:
            logger.error(f"IntegrityError: {err}")
            raise
        except Exception as err:
            logger.error(f"Unexpected error: {err}")
            raise

    def delete(self, using=None, keep_parents=False):
        """
        Removes file from GCS, then deletes DB record.
        """
        try:
            client = storage.Client()
            bucket = client.bucket(settings.GCS_BUCKET_NAME)
            # decode from special symbols
            decoded_name = unquote(self.name)
            blob = bucket.blob(f"{self.base_path(self.relation)}/{decoded_name}")
            blob.delete()
        except Exception as err:
            logger.error(f"GCS delete failed: {err}")
        super().delete(using=using, keep_parents=keep_parents)

    def retrieve_dimensions_from_gcs(
        self,
        bucket_name: Optional[str] = settings.GCS_BUCKET_NAME,
    ) -> Tuple[Optional[int], Optional[int]]:
        """
        Downloads the file from GCS and returns dimensions as a tuple (width, height).
        Returns (None, None) if an error occurs or if the file is not an image.
        """
        try:
            # Check if the file is an image
            if self.relation == RelationChoices.FILE:
                logger.warning("File is not an image; cannot retrieve dimensions.")
                return None, None
            # Connect to GCS
            client = storage.Client()
            bucket = client.bucket(bucket_name)
            # Construct blob path in the same way as done during upload and deletion
            # If image uploaded to community gcs_bucket
            if bucket_name == settings.GCS_BUCKET_NAME:
                blob_path = f"{self.base_path(self.relation)}/{self.name}"
                blob = bucket.blob(blob_path)
                # Download the file contents
                file_contents = blob.download_as_bytes()
                # Open the image using PIL
                image = Image.open(BytesIO(file_contents))
                # Return the dimensions
                return image.size
            # If image uploaded other gcs_bucket
            else:
                image_url = self.url
                blob_path = image_url.replace(
                    f"https://storage.googleapis.com/{bucket_name}/", ""
                )
                blob = bucket.blob(blob_path)
                # Download the file contents
                file_contents = blob.download_as_bytes()
                # Open the image using PIL
                image = Image.open(BytesIO(file_contents))
                # Return the dimensions
                return image.size
        except Exception as err:
            logger.error(f"Error retrieving image dimensions from GCS: {err}")
            return None, None

    def move_file_to_correct_destination(self):
        """
        Moves the file to the correct GCS destination based on its relation.
        This method retrieves the current blob path from the public URL,
        determines the correct destination using the relation logic,
        copies the file to the new location, deletes the old blob,
        updates the file record with the new public URL, and saves the changes.
        """
        try:
            # Connect to GCS
            client = storage.Client()
            bucket = client.bucket(settings.GCS_BUCKET_NAME)
            # Construct the new blob path based on the relation
            new_blob_path = f"{self.base_path(self.relation)}/{self.name}"

            # Derive the old blob path from the current public URL
            bucket_name = settings.GCS_BUCKET_NAME
            prefix = f"https://storage.googleapis.com/{bucket_name}/"
            if self.url.startswith(prefix):
                old_blob_path = self.url[len(prefix) :]
            else:
                logger.error("Public URL does not match expected GCS URL format.")
                return

            # If already in the correct destination, do nothing
            if old_blob_path == new_blob_path:
                logger.info("File is already in the correct destination.")
                return

            # Get the old and new blobs
            old_blob = bucket.blob(old_blob_path)
            new_blob = bucket.blob(new_blob_path)

            # Copy the file from the old blob to the new blob
            new_blob.rewrite(old_blob)

            # Delete the old blob after copying
            old_blob.delete()

            # Update the file record with the new public URL and save
            self.url = new_blob.public_url
            self.save(update_fields=["url"])
            logger.info(f"File moved from {old_blob_path} to {new_blob_path}.")
        except Exception as err:
            logger.error(f"Error moving file to correct destination: {err}")

    def update_file(self, new_file):
        """Update file in gcs"""
        try:
            # TODO: подумать насколько безопасно удалять файл из gcs при обновлении
            # delete current file object from gcs
            client = storage.Client()
            bucket = client.bucket(settings.GCS_BUCKET_NAME)
            # get relation from url, bcs relation can be changed before upload new file
            file_relation = self.url.split("/")[-2]
            # decode from special symbols
            decoded_name = unquote(self.name)
            old_blob_path = f"{file_relation}/{decoded_name}"
            bucket.blob(old_blob_path).delete()

            # generate new name
            new_name = f"{uuid.uuid4()}_{new_file.name}"
            new_blob_path = f"{self.base_path(self.relation)}/{new_name}"

            # if it image, update dimensions
            width = height = None
            if new_file.content_type.startswith("image/"):
                image = Image.open(BytesIO(new_file.read()))
                width, height = image.size
                new_file.seek(0)  # ensure we are at the start of the stream

            # upload file to gcs
            public_url = self._upload_file_to_gcs(
                file_contents=new_file.read(),
                blob_name=new_blob_path,
                content_type=new_file.content_type,
            )

            # update current file info
            self.name = new_name
            self.url = public_url
            self.width = width
            self.height = height
            self.file_size = new_file.size
            self.original_name = new_file.name
            self.save()

        except Exception as err:
            logger.error(f"File update failed: {err}")
            raise


```

```python
from io import BytesIO
from typing import Optional
from urllib.parse import urlparse
from uuid import uuid4

import requests
from loguru import logger
from mongoengine import Q, QuerySet

from files.models import File
from posts.mongo_models import Post
from social.utils import get_proxy


class Downloader:
    """
    Responsible for downloading media files for social media posts,
    and saving them to the database.
    """

    @staticmethod
    def get_posts_with_media(posts: QuerySet) -> QuerySet:
        """
        Get the posts that have media.
        """
        posts = posts.filter(
            Q(content__images__exists=True)
            | Q(content__videos__exists=True)
            | Q(content__videos__exists=True)
        )
        return posts

    @staticmethod
    def download_image(image_url: str) -> Optional[BytesIO]:
        """Download media related to the posts."""
        try:
            response = requests.get(image_url, proxies=get_proxy())
        except requests.RequestException as e:
            logger.error(f"Failed to download image from {image_url}: {e}")
            return None
        if response.status_code == 200:
            image_file = BytesIO(response.content)
            return image_file
        elif response.status_code == 404:
            return None
        else:
            raise response.raise_for_status()

    @staticmethod
    def save_image(image_data: BytesIO, image_url: str) -> File:
        """
        Saves the media to the database.
        """
        logger.debug(f"Saving image to the database, image_url: {image_url}")
        parsed_url = urlparse(image_url)
        image_name = parsed_url.path.split("/")[-1]
        logger.debug(f"Image name: {image_name}")
        image = File.create_image_from_bytes(image_data, image_name)
        return image

    @staticmethod
    def download_video(video_url: str) -> Optional[BytesIO]:
        """Download video related to the posts."""
        try:
            response = requests.get(video_url, proxies=get_proxy())
        except requests.RequestException as e:
            logger.error(f"Failed to download video from {video_url}: {e}")
            return None
        if response.status_code == 200:
            video_file = BytesIO(response.content)
            return video_file
        elif response.status_code == 404:
            return None
        else:
            raise response.raise_for_status()

    @staticmethod
    def save_video_file(video_data: BytesIO, video_url: str) -> File:
        """
        Saves the video to the database.
        """
        logger.debug(f"Saving video to the database, video_url: {video_url}")
        parsed_url = urlparse(video_url)
        video_name = parsed_url.path.split("/")[-1]
        logger.debug(f"Video name: {video_name}")
        video = File.create_video_from_bytes(video_data, video_name)
        return video

    @classmethod
    def download_media(cls, post: Post):
        """
        Downloads media files for social media posts.
        """
        identifier = uuid4()

        logger.debug(
            f"Downloading media for post: {post.post_uuid}. "
            f"Logs identifier: {identifier}."
        )
        # Download the images if any
        if post.content.images:
            for image in post.content.images:
                # TODO: check if the image is already downloaded
                #  (check url is it in the bucket (settings.GCS_BUCKET_NAME))
                if image.image_id:
                    logger.debug(f"{identifier}: Image already downloaded: {image}")
                    continue
                logger.debug(f"{identifier}: Downloading image: {image}")
                image_data = cls.download_image(image.image_url)
                if image_data:
                    # Save the image to the database
                    stored_image = cls.save_image(image_data, image.image_url)
                    image.image_id = stored_image.id
                    image.image_url = stored_image.url
                else:
                    logger.error(
                        f"{identifier}: Failed to download image: {image.image_url}"
                    )

        # Download the videos previews if any
        if post.content.videos:
            for video in post.content.videos:
                # TODO: check if the video is already downloaded
                #  (check url is it in the bucket (settings.GCS_BUCKET_NAME))
                if not video.preview_url or video.preview_id:
                    logger.debug(
                        f"{identifier}: Video already downloaded: "
                        f"{video} or no preview exists"
                    )
                    continue
                logger.debug(f"{identifier}: Downloading image for video: {video}")
                video_preview_data = cls.download_image(video.preview_url)
                if video_preview_data:
                    # Save the image to the database
                    stored_image = cls.save_image(video_preview_data, video.preview_url)
                    video.preview_id = stored_image.id
                    video.preview_url = stored_image.url
                else:
                    logger.error(
                        f"{identifier}: Failed to download video preview: {video.preview_url}"
                    )
                if not video.video_url or video.video_id:
                    logger.debug(
                        f"{identifier}: Video already downloaded: {video} or no video exists"
                    )
                    continue
                logger.debug(f"{identifier}: Downloading video: {video}")
                video_data = cls.download_video(video.video_url)
                if video_data:
                    # Save the video to the database
                    stored_video = cls.save_video_file(video_data, video.video_url)
                    video.video_id = stored_video.id
                    video.video_url = stored_video.url
                else:
                    logger.error(
                        f"{identifier}: Failed to download video: {video.video_url}"
                    )

        # Download the links images if any
        if post.content.links:
            for link in post.content.links:
                if not link.link_image_url:
                    logger.debug(
                        f"{identifier}: Link image url is empty: {link.link_image_url}"
                    )
                    continue

                # TODO: check if the image is already downloaded
                #  (check url is it in the bucket (settings.GCS_BUCKET_NAME))
                if link.link_image_id:
                    logger.debug(f"{identifier}: Link image already downloaded: {link}")
                    continue
                logger.debug(f"{identifier}: Downloading image for link: {link}")
                link_image_data = cls.download_image(link.link_image_url)
                if link_image_data:
                    # Save the image to the database
                    stored_image = cls.save_image(link_image_data, link.link_image_url)
                    link.link_image_id = stored_image.id
                    link.link_image_url = stored_image.url
                else:
                    logger.error(
                        f"{identifier}: Failed to download link image: {link.link_image_url}"
                    )
        post.save()

```

app/config/settings.py

```python
# Google Cloud Storage configuration
GCS_BUCKET_NAME = env("GCS_BUCKET_NAME", default="")
```

prod.yaml

```yaml
secretEnv:
  -name: GCS_BUCKET_NAME
  value: ENC[AES256_GCM,data:4aOCBkBUdqGLv8EYihV95ubnUfFXVdhq1GqP,iv:fC9bufulQPOWxh2x9f0EoXoEEKWMY3quO6nZD2JjqmU=,tag:RhRdNUjsSZwHEObPZDQRtw==,type:str]
  -name: COMMUNITY_GOOGLE_API_CLIENT_SECRET
   value: ENC[AES256_GCM,data:4/UhCpqwUZKjNgZT9DhtdUkxzNUn0Nuj6aqE3DgS2vOpJ2U=,iv:O5+Y8u7IHy0iHn8FPCrscPaZSvfdfhrt9Jvj/YmjvGY=,tag:bXHfopuekC8K0lo2k7C1OQ==,type:str]

secretMounts:
    - secretName: passport-key-public
      mountFilePath: ENC[AES256_GCM,data:hLJZlXXJpqrSbKihjDoExGYwowvumw261E4R/xPP9Wg=,iv:ORgj4AF1qSD1Nj7Wy9AhRSTDgIhuvE2YllQrP/WvIDs=,tag:Q23lMdbBNMCq55rqkLyivg==,type:str]
      secretKey: ENC[AES256_GCM,data:nWs7KFFT6odclas41BmHzR8UpeTIheAenH6J,iv:yH05Aa/gOmjcRD92/u+2J86wcSVLKref6eBYU3yHaqE=,tag:N8v/qrWd/knLqSWbXIWoEA==,type:str]
      secretFileContent: ENC[AES256_GCM,data:3upF5E9S14ARvDhvVCqmbpfPRTanhRyLuVpMURTIjj3ZCAXSSKfirrHpCtnjLrEbcu3qKOM1gkxC5jmx0iPSy5lmRJk+MQqK5RsoMnSnRR/dOKQadoQzK3g0hu+0ODdeXBP2laF1vDrIvPqVQQjnGtdoEMBRMbQzVXAjuIcaD7tnsjyd0rfFjqZUHsslQdbEwYzTtyJ5yHP9xishBcjAEYR/FSPmNHNenm0/GcdfxMP8qjZpqY0p+VJ9qyN1qibkWgUAVYTNM7OasYUk+S86gJmgOP6qFjNzcNAgvSfwkyINk7NDFyFDxoosfkKiMT6YptjeIwoUhGAXzI0+gEimr3oH4eQg6W+m/SWnN2F0ktfrlfJubJoBwT9Ei7YpxOAA8CoDR3lMQ9m/CNhEcI6D2pbeFssvDaKxKo0IDOhUTY7v+QenDGC6MNXFMDtn0XlN9yYC2KxlDjD55VfgWJXDr0bg8GF9fWm5XT9Q6J9117ThF9lHKXg0S0awpHn9T9PE1F6PYQIsnInWmNWoxjfVRJyDUproL5rCTZ+WeSrFYkWJEWS5v5fr0Ucnb8RvcLE3w8OevOZfmiy48i/DtppHYEymmw==,iv:swzGApk5xWtUHiYQOtggrj19T/QtIkxNUfAOJzigpSc=,tag:Xz+R09E5NgwAG2oTO61cAw==,type:str]
    - secretName: google-auth
      mountFilePath: ENC[AES256_GCM,data:3sBRUwXpv9/ixmjCucfgmM8m1w==,iv:o3HnZh5dC1TjeKieDfLJUPT7oJ6PW8fxIqOKFRgKOg4=,tag:ta2QLBA07ucM4XxD/YNt4A==,type:str]
      secretKey: ENC[AES256_GCM,data:GpgIgdFLJ2OmoCJW4qSSV20P2VynRQ==,iv:hV5yMX9ziuAcBlhgVcgnWE9wCJFypFHuw+hAxw+JB7o=,tag:DMNuyG0/1CatC/uOTknPUA==,type:str]
      secretFileContent: ENC[AES256_GCM,data: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,iv:mXeKJDy9dsO6YwhnRILRX8FKB1albX1gN8ZsSIe3V0E=,tag:zxZ9k7aMfm1iffAVVYe7kA==,type:str]

```