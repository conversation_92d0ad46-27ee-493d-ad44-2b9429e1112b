# GCS Integration: Quick Reference

**Feature**: Опциональное сохранение Instagram медиа в Google Cloud Storage

## 🚀 Quick Start

### 1. Настройка окружения
```bash
export GCS_BUCKET_NAME="socialmanager-media"
export GOOGLE_APPLICATION_CREDENTIALS="/path/to/service-account.json"
```

### 2. Установка зависимости
```bash
uv add google-cloud-storage
```

### 3. Миграция БД
```bash
python manage.py makemigrations instagram_manager
python manage.py migrate
```

## 📝 Минимальные изменения кода

### 1. Модель (1 поле)
```python
# instagram_manager/models.py
class InstagramMedia:
    gcs_url = models.CharField(max_length=1024, null=True, blank=True)
    
    def get_display_url(self):
        if self.gcs_url:
            return self.gcs_url
        # ... existing logic
```

### 2. GCS Service (1 новый файл)
```python
# core/storage/gcs_service.py
class GCSService:
    def upload_file(self, file_content, original_name, folder):
        # Upload to GCS and return public URL
```

### 3. MediaHandler (модификация метода)
```python
# instagram_manager/instagram_api/data_handlers/media_handler.py
def download_media(self, media, save_to_gcs=False):
    if save_to_gcs:
        # Upload to GCS
    else:
        # Existing local save
```

### 4. Form Mixin (1 чекбокс)
```python
# core/forms/mixins.py
class MediaDownloadOptionsMixin:
    save_media_to_gcs = forms.BooleanField(
        label="Save media to Google Cloud Storage"
    )
```

## ✅ Что работает автоматически

- ✅ Отображение в админке (через get_display_url)
- ✅ Экспорт в Majila (уже использует get_display_url)
- ✅ Все шаблоны (используют get_display_url)
- ✅ Удаление файлов (через сигналы)

## 🔧 Ключевые файлы

1. **План**: `/docs/superclaude/features/gcs_implementation_plan.md`
2. **Техспека**: `/docs/superclaude/features/gcs_technical_spec.md`
3. **Тесты**: `/docs/superclaude/features/gcs_testing_strategy.md`

## 📊 Статус компонентов

| Компонент | Изменения | Сложность |
|-----------|-----------|-----------|
| Model | +1 поле | ⭐ |
| Service | +1 класс | ⭐⭐ |
| Handler | Модификация | ⭐⭐ |
| Forms | +1 чекбокс | ⭐ |
| Views | Передача флага | ⭐ |
| Tasks | Передача флага | ⭐ |

## 🎯 Критический путь

```
Form ➜ View ➜ Task ➜ Handler ➜ GCS ➜ Model ➜ Display
```

## ⚡ Команды для тестирования

```bash
# Unit tests
uv run pytest tests/ -k "gcs" -v

# Integration tests  
uv run pytest tests/instagram_manager/test_e2e_gcs.py

# Manual test
1. Go to /instagram/admin/batch-import-posts/
2. Check "Save media to GCS"
3. Import and verify URLs
```

## 🐛 Troubleshooting

1. **GCS недоступен**: Проверить credentials и bucket permissions
2. **Нет чекбокса**: Проверить GCS_BUCKET_NAME в settings
3. **Fallback на local**: Смотреть логи для GCS ошибок

## 📈 Метрики успеха

- Import с GCS работает без ошибок
- URL в админке показывает storage.googleapis.com
- Экспорт содержит GCS URLs
- Производительность не деградирует >10%

---
**Время реализации**: 5-6 дней
**Риск**: Низкий (есть fallback)
**Обратная совместимость**: 100%