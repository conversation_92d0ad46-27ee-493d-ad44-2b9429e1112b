# План реализации GCS хранилища для Instagram Media

**Версия**: 1.0  
**Дата**: 2025-01-10  
**Автор**: <PERSON><PERSON><PERSON><PERSON> (Architect + System_Thinker)

## 🎯 Цель
Добавить опцию сохранения медиафайлов в Google Cloud Storage с минимальными изменениями существующего кода.

## 📋 Минимальный план реализации

### Фаза 1: Подготовка (1 день)

#### 1.1 Добавление зависимостей
```bash
uv add google-cloud-storage
```

#### 1.2 Конфигурация
В `settings/base.py`:
```python
# GCS Configuration
GCS_BUCKET_NAME = env('GCS_BUCKET_NAME', default='')
GOOGLE_APPLICATION_CREDENTIALS = env('GOOGLE_APPLICATION_CREDENTIALS', default='')
```

#### 1.3 Миграция БД
Добавить одно поле в `InstagramMedia`:
```python
gcs_url = models.CharField(max_length=1024, null=True, blank=True)
```

### Фаза 2: Реализация ядра (2 дня)

#### 2.1 GCS Service
Создать `/core/storage/gcs_service.py`:
```python
import uuid
from google.cloud import storage
from django.conf import settings

class GCSService:
    """Минимальный сервис для работы с GCS"""
    
    def __init__(self):
        self.client = storage.Client()
        self.bucket = self.client.bucket(settings.GCS_BUCKET_NAME)
    
    def upload_file(self, file_content: bytes, original_name: str, 
                   folder: str = 'instagram/media') -> str:
        """Загружает файл и возвращает публичный URL"""
        unique_name = f"{uuid.uuid4()}_{original_name}"
        blob_path = f"{folder}/{unique_name}"
        
        blob = self.bucket.blob(blob_path)
        blob.upload_from_string(file_content)
        
        return blob.public_url
```

#### 2.2 Модификация MediaHandler
В `instagram_manager/instagram_api/data_handlers/media_handler.py`:

Изменить метод `download_media()`:
```python
def download_media(self, media: InstagramMedia, save_to_gcs: bool = False) -> bool:
    """Download media with optional GCS storage"""
    try:
        response = requests.get(media.media_url, timeout=30)
        response.raise_for_status()
        
        if save_to_gcs and settings.GCS_BUCKET_NAME:
            # Сохраняем в GCS
            from core.storage.gcs_service import GCSService
            gcs = GCSService()
            
            file_name = f"{media.external_id}{self._get_extension(response)}"
            gcs_url = gcs.upload_file(
                response.content, 
                file_name,
                f'instagram/media/{media.media_type}'
            )
            
            media.gcs_url = gcs_url
            media.is_downloaded = True
            media.save()
            
        else:
            # Существующая логика локального сохранения
            self._save_local_file(media, response.content)
            
        return True
        
    except Exception as e:
        media.download_error = str(e)
        media.save()
        return False
```

#### 2.3 Обновление InstagramMedia.get_display_url()
```python
def get_display_url(self) -> str:
    """Returns appropriate URL based on storage location"""
    # Приоритет: GCS -> Local -> External
    if self.gcs_url:
        return self.gcs_url
    elif self.is_downloaded and self.local_path:
        return self.local_path.url
    return self.media_url
```

### Фаза 3: Интеграция форм (1 день)

#### 3.1 Добавление чекбокса в форму
В `core/forms/mixins.py` модифицировать `MediaDownloadOptionsMixin`:
```python
class MediaDownloadOptionsMixin:
    """Mixin for forms that handle media download options"""
    
    download_media = forms.BooleanField(
        required=False,
        initial=False,
        label="Download media files",
        help_text="Download and store media files locally"
    )
    
    save_media_to_gcs = forms.BooleanField(
        required=False,
        initial=False,
        label="Save media to Google Cloud Storage",
        help_text="Store media files in GCS instead of local storage"
    )
```

#### 3.2 Передача флага в сервисы
В `instagram_manager/views/admin_views.py`:
```python
def admin_batch_import_posts_do_import(request):
    # ... existing code ...
    
    download_media = form.cleaned_data.get('download_media', False)
    save_to_gcs = form.cleaned_data.get('save_media_to_gcs', False)
    
    # Передаем флаг в задачу
    task_data = {
        'profiles': profiles,
        'download_media': download_media,
        'save_to_gcs': save_to_gcs,
        # ... other params
    }
```

#### 3.3 Обновление Celery задач
В `instagram_manager/tasks/import_tasks.py`:
```python
class ImportBatchPostsTask(BaseTask):
    def execute(self, profiles, download_media=True, save_to_gcs=False, **kwargs):
        # ... existing code ...
        
        # Передаем флаг в PostHandler
        handler = PostHandler(
            skip_media_download=not download_media,
            save_to_gcs=save_to_gcs
        )
```

### Фаза 4: Тестирование (2 дня)

#### 4.1 Unit тесты
- Тест GCSService с моками
- Тест обновленного get_display_url()
- Тест MediaHandler с GCS опцией

#### 4.2 Integration тесты
- Тест полного флоу импорта с GCS
- Тест отображения в админке
- Тест экспорта с GCS URLs

#### 4.3 Manual тесты
- Проверка формы batch import
- Загрузка разных типов медиа
- Проверка отображения в админке

## 📦 Список изменяемых файлов

1. **Новые файлы**:
   - `/core/storage/gcs_service.py` - GCS сервис

2. **Модифицируемые файлы**:
   - `instagram_manager/models.py` - добавить поле gcs_url
   - `instagram_manager/instagram_api/data_handlers/media_handler.py` - поддержка GCS
   - `core/forms/mixins.py` - добавить чекбокс
   - `instagram_manager/views/admin_views.py` - передача флага
   - `instagram_manager/tasks/import_tasks.py` - передача флага
   - `settings/base.py` - GCS конфигурация

## ⚡ Критический путь

```mermaid
graph LR
    A[Форма с чекбоксом] --> B[View передает флаг]
    B --> C[Task получает флаг]
    C --> D[PostHandler с флагом]
    D --> E[MediaHandler.download_media]
    E --> F{save_to_gcs?}
    F -->|Yes| G[GCSService.upload_file]
    F -->|No| H[Local save]
    G --> I[Save gcs_url]
    H --> J[Save local_path]
    I --> K[get_display_url returns GCS]
    J --> K[get_display_url returns local]
```

## ✅ Критерии успеха

1. Чекбокс "Save media to GCS" появляется в форме batch import
2. При включенном чекбоксе медиа сохраняется в GCS
3. URL из GCS корректно отображается в админке
4. Экспорт использует GCS URLs автоматически
5. Существующий функционал не сломан
6. Все тесты проходят

## 🚀 Быстрый старт для разработчика

1. Настроить GCS credentials:
   ```bash
   export GOOGLE_APPLICATION_CREDENTIALS="/path/to/service-account.json"
   export GCS_BUCKET_NAME="your-bucket-name"
   ```

2. Выполнить миграцию:
   ```bash
   python manage.py makemigrations instagram_manager
   python manage.py migrate
   ```

3. Запустить и проверить в админке

## ⏱️ Оценка времени

- **Общее время**: 5-6 рабочих дней
- **Минимальная рабочая версия**: 3 дня
- **С полным тестированием**: 6 дней

## 🎯 Преимущества подхода

1. **Минимальные изменения**: Только 6 файлов
2. **Обратная совместимость**: Старые медиа продолжают работать
3. **Простота**: Нет сложных абстракций
4. **Быстрота**: Можно реализовать за неделю
5. **Прозрачность**: get_display_url() скрывает детали реализации