# Руководство по использованию Google Cloud Storage

**Дата создания**: 2025-01-10  
**Автор**: SuperClaude

## Обзор

Проект SocialManager поддерживает сохранение медиафайлов в Google Cloud Storage (GCS). Это обеспечивает надежное хранение, быструю доставку контента и снижение нагрузки на сервер.

## Структура папок

Файлы в GCS организованы по типам в следующей структуре:

- `images/` - изображения (фотографии из постов)
- `videos/` - видеофайлы
- `previews/` - превью/миниатюры для видео

### Формат имен файлов

Все файлы сохраняются в формате:
```
{folder}/{timestamp}_{unique_id}_{original_filename}
```

Пример:
```
images/20250110_142530_a1b2c3d4_media_file.jpg
videos/20250110_142531_b2c3d4e5_video.mp4
previews/20250110_142532_c3d4e5f6_thumbnail.jpg
```

## Использование

### 1. Базовая загрузка файла

```python
from core.storage.gcs_service import GCSService

# Инициализация сервиса
gcs = GCSService()

# Загрузка изображения
with open('photo.jpg', 'rb') as f:
    content = f.read()
    
url = gcs.upload_file(
    file_content=content,
    original_filename='photo.jpg',
    content_type='image/jpeg',
    folder='images'  # По умолчанию 'images'
)

print(f"File uploaded to: {url}")
```

### 2. Определение папки по типу медиа

```python
from core.storage.gcs_service import GCSService

# Для изображений
folder = GCSService.get_folder_by_media_type('image')  # 'images'

# Для видео
folder = GCSService.get_folder_by_media_type('video')  # 'videos'

# Для превью
folder = GCSService.get_folder_by_media_type('image', is_thumbnail=True)  # 'previews'
```

### 3. Использование MediaHandler

MediaHandler автоматически определяет правильную папку при загрузке:

```python
from instagram_manager.instagram_api.data_handlers.media_handler import MediaHandler

# Создание handler с поддержкой GCS
handler = MediaHandler(save_to_gcs=True)

# Загрузка медиа (автоматически выберет правильную папку)
success = handler.download_media(media_object)
```

### 4. Проверка доступности GCS

```python
from core.storage.gcs_service import GCSService

gcs = GCSService()
if gcs.is_available():
    print("GCS is available and configured")
else:
    print("GCS is not available")
```

### 5. Удаление файла

```python
# Удаление по blob name
success = gcs.delete_file('images/20250110_142530_a1b2c3d4_media_file.jpg')
```

## Конфигурация

Для работы с GCS необходимо настроить следующие переменные окружения:

```bash
# Название bucket в GCS
GCS_BUCKET_NAME=your-bucket-name

# Путь к файлу с credentials
GOOGLE_APPLICATION_CREDENTIALS=/path/to/service-account-key.json
```

## Валидация файлов

GCSService выполняет валидацию перед загрузкой:

1. **Размер файла**: максимум 100MB
2. **Типы контента**: 
   - Изображения: `image/jpeg`, `image/png`, `image/gif`, `image/webp`
   - Видео: `video/mp4`, `video/quicktime`
3. **Расширения файлов**: `.jpg`, `.jpeg`, `.png`, `.gif`, `.webp`, `.mp4`, `.mov`

## Тестирование

Для тестирования работы с GCS используйте скрипт:

```bash
python scripts/test_gcs_upload.py
```

Скрипт проверит:
- Доступность GCS
- Загрузку в правильные папки
- Валидацию файлов
- Удаление тестовых файлов

## Миграция существующих файлов

Существующие файлы в папке `instagram/` продолжат работать. Новые файлы будут сохраняться в новой структуре папок.

## Мониторинг

В логах приложения можно отслеживать операции с GCS:

```
[INFO] Successfully uploaded file to GCS: images/20250110_142530_a1b2c3d4_photo.jpg (87823 bytes)
[INFO] GCS upload failed for media IMG123: Connection timeout
[WARNING] Failed to initialize GCS service, will fallback to local storage
```

## Обработка ошибок

При ошибке загрузки в GCS, MediaHandler автоматически переключается на локальное хранение:

```python
# MediaHandler с fallback на локальное хранение
handler = MediaHandler(save_to_gcs=True)

# Если GCS недоступен, файл сохранится локально
success = handler.download_media(media)
```

## Безопасность

1. **Публичный доступ**: Все файлы в bucket доступны публично по URL
2. **Валидация**: Строгая проверка типов и размеров файлов
3. **Санитизация имен**: Специальные символы удаляются из имен файлов

## Производительность

- Загрузка выполняется асинхронно
- Таймаут на операции: 30 секунд
- Поддержка больших файлов до 100MB
- Автоматические повторные попытки при сетевых ошибках

## Будущие улучшения

1. Поддержка дополнительных папок (`avatars/`, `covers/`)
2. Настраиваемые политики хранения для разных типов
3. Интеграция с CDN для ускорения доставки
4. Автоматическая оптимизация изображений