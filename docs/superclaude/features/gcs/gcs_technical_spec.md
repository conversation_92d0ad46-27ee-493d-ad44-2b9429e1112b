# Техническая спецификация: GCS интеграция для Instagram Media

**Версия**: 1.0  
**Дата**: 2025-01-10  
**Статус**: Draft

## 1. Обзор изменений

### 1.1 Новые компоненты
- `GCSService` - сервис для работы с Google Cloud Storage
- Поле `gcs_url` в модели `InstagramMedia`
- Чекбокс `save_media_to_gcs` в формах импорта

### 1.2 Модифицируемые компоненты
- `MediaHandler.download_media()` - добавление GCS upload логики
- `InstagramMedia.get_display_url()` - приоритет GCS URL
- `MediaDownloadOptionsMixin` - новый чекбокс
- Batch import views - передача GCS флага
- Import tasks - поддержка GCS параметра

## 2. Детальная реализация

### 2.1 Модель InstagramMedia

**Файл**: `instagram_manager/models.py`

```python
class InstagramMedia(SocialMediaMedia):
    """Instagram media model with GCS support"""
    
    # Existing fields...
    
    # NEW: GCS storage URL
    gcs_url = models.CharField(
        max_length=1024, 
        null=True, 
        blank=True,
        help_text="Google Cloud Storage URL if media is stored in GCS"
    )
    
    def get_display_url(self) -> str:
        """
        Returns the appropriate URL for displaying the media.
        Priority: GCS URL > Local URL > External URL
        """
        # NEW: Check GCS URL first
        if self.gcs_url:
            return self.gcs_url
            
        # Existing logic
        if self.is_downloaded and self.local_path:
            return self.local_path.url
        return self.media_url
    
    def get_thumbnail_display_url(self) -> str:
        """Returns the URL for displaying video thumbnail"""
        # TODO: В будущем можно добавить gcs_thumbnail_url
        # Пока используем существующую логику
        if self.media_type == 'video':
            if self.is_downloaded and self.local_thumbnail_path:
                return self.local_thumbnail_path.url
            return self.thumbnail_url or ''
        return ''
```

### 2.2 GCS Service

**Файл**: `core/storage/gcs_service.py`

```python
"""
Google Cloud Storage service for media uploads
"""
import uuid
import mimetypes
from typing import Optional
from google.cloud import storage
from google.cloud.exceptions import GoogleCloudError
from django.conf import settings
import logging

logger = logging.getLogger(__name__)


class GCSService:
    """Service for handling Google Cloud Storage operations"""
    
    def __init__(self):
        """Initialize GCS client and bucket"""
        try:
            self.client = storage.Client()
            self.bucket_name = settings.GCS_BUCKET_NAME
            self.bucket = self.client.bucket(self.bucket_name)
        except Exception as e:
            logger.error(f"Failed to initialize GCS client: {e}")
            raise
    
    def upload_file(
        self, 
        file_content: bytes, 
        original_name: str,
        folder: str = 'instagram/media',
        content_type: Optional[str] = None
    ) -> str:
        """
        Upload file to GCS and return public URL
        
        Args:
            file_content: File content as bytes
            original_name: Original filename
            folder: GCS folder path
            content_type: MIME type (optional, will be guessed if not provided)
            
        Returns:
            Public URL of uploaded file
            
        Raises:
            GoogleCloudError: If upload fails
        """
        try:
            # Generate unique filename
            unique_name = f"{uuid.uuid4()}_{original_name}"
            blob_path = f"{folder}/{unique_name}"
            
            # Create blob
            blob = self.bucket.blob(blob_path)
            
            # Set content type
            if not content_type:
                content_type = mimetypes.guess_type(original_name)[0] or 'application/octet-stream'
            
            # Upload with content type
            blob.upload_from_string(
                file_content,
                content_type=content_type
            )
            
            # Return public URL
            public_url = blob.public_url
            logger.info(f"Successfully uploaded {original_name} to GCS: {public_url}")
            
            return public_url
            
        except GoogleCloudError as e:
            logger.error(f"GCS upload failed for {original_name}: {e}")
            raise
        except Exception as e:
            logger.error(f"Unexpected error during GCS upload: {e}")
            raise
    
    def delete_file(self, gcs_url: str) -> bool:
        """
        Delete file from GCS by URL
        
        Args:
            gcs_url: Public URL of the file
            
        Returns:
            True if deleted successfully, False otherwise
        """
        try:
            # Extract blob path from URL
            # Format: https://storage.googleapis.com/bucket-name/path/to/file
            prefix = f"https://storage.googleapis.com/{self.bucket_name}/"
            if not gcs_url.startswith(prefix):
                logger.warning(f"Invalid GCS URL format: {gcs_url}")
                return False
            
            blob_path = gcs_url[len(prefix):]
            blob = self.bucket.blob(blob_path)
            blob.delete()
            
            logger.info(f"Successfully deleted from GCS: {gcs_url}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to delete from GCS: {e}")
            return False
```

### 2.3 MediaHandler с поддержкой GCS

**Файл**: `instagram_manager/instagram_api/data_handlers/media_handler.py`

```python
class MediaHandler:
    """Handler for Instagram media operations"""
    
    def __init__(self, save_to_gcs: bool = False):
        """Initialize handler with GCS option"""
        self.save_to_gcs = save_to_gcs
        self._gcs_service = None
    
    @property
    def gcs_service(self):
        """Lazy initialization of GCS service"""
        if self._gcs_service is None and settings.GCS_BUCKET_NAME:
            from core.storage.gcs_service import GCSService
            self._gcs_service = GCSService()
        return self._gcs_service
    
    def download_media(self, media: InstagramMedia) -> bool:
        """
        Download media from external URL and store it
        
        Args:
            media: InstagramMedia instance
            
        Returns:
            True if successful, False otherwise
        """
        if media.is_downloaded:
            logger.info(f"Media {media.external_id} already downloaded")
            return True
        
        try:
            # Download from external URL
            response = requests.get(media.media_url, timeout=30)
            response.raise_for_status()
            
            # Determine file extension
            content_type = response.headers.get('Content-Type', '')
            ext = self._get_extension_from_content_type(content_type)
            file_name = f"{media.external_id}{ext}"
            
            # NEW: Check if we should save to GCS
            if self.save_to_gcs and self.gcs_service:
                try:
                    # Upload to GCS
                    folder = f'instagram/media/{media.media_type}'
                    gcs_url = self.gcs_service.upload_file(
                        file_content=response.content,
                        original_name=file_name,
                        folder=folder,
                        content_type=content_type
                    )
                    
                    # Update media record
                    media.gcs_url = gcs_url
                    media.is_downloaded = True
                    media.download_error = None
                    media.save()
                    
                    logger.info(f"Media {media.external_id} uploaded to GCS")
                    return True
                    
                except Exception as e:
                    logger.error(f"GCS upload failed, falling back to local: {e}")
                    # Fall through to local storage
            
            # Existing local storage logic
            file_content = ContentFile(response.content)
            media.local_path.save(file_name, file_content, save=False)
            media.is_downloaded = True
            media.download_error = None
            media.save()
            
            logger.info(f"Media {media.external_id} saved locally")
            return True
            
        except requests.RequestException as e:
            error_msg = f"Failed to download media: {str(e)}"
            logger.error(f"Media {media.external_id}: {error_msg}")
            media.download_error = error_msg
            media.save()
            return False
        except Exception as e:
            error_msg = f"Unexpected error: {str(e)}"
            logger.error(f"Media {media.external_id}: {error_msg}")
            media.download_error = error_msg
            media.save()
            return False
```

### 2.4 Form Mixin Update

**Файл**: `core/forms/mixins.py`

```python
class MediaDownloadOptionsMixin:
    """Mixin for forms that handle media download options"""
    
    download_media = forms.BooleanField(
        required=False,
        initial=False,
        label="Download media files",
        help_text="Download and store media files"
    )
    
    # NEW: GCS storage option
    save_media_to_gcs = forms.BooleanField(
        required=False,
        initial=False,
        label="Save media to Google Cloud Storage",
        help_text="Store media files in GCS instead of local storage (requires GCS configuration)"
    )
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        
        # Hide GCS option if not configured
        if not getattr(settings, 'GCS_BUCKET_NAME', None):
            if 'save_media_to_gcs' in self.fields:
                self.fields['save_media_to_gcs'].widget = forms.HiddenInput()
```

### 2.5 View Update для Batch Import

**Файл**: `instagram_manager/views/admin_views.py`

```python
@staff_member_required
def admin_batch_import_posts_do_import(request):
    """Process batch import form submission"""
    if request.method != 'POST':
        return HttpResponseNotAllowed(['POST'])
    
    form = BatchImportPostsForm(request.POST)
    if form.is_valid():
        # Get form data
        profiles = form.cleaned_data['profiles']
        download_media = form.cleaned_data.get('download_media', False)
        save_to_gcs = form.cleaned_data.get('save_media_to_gcs', False)  # NEW
        
        # Create scraping task
        task = InstagramScrapingTask.objects.create(
            task_type='batch_import',
            status='pending',
            created_by=request.user,
            task_data={
                'profiles': [p.username for p in profiles],
                'download_media': download_media,
                'save_to_gcs': save_to_gcs,  # NEW
                'import_comments': form.cleaned_data.get('import_comments', True),
                'posts_limit': form.cleaned_data.get('posts_limit', 20),
            }
        )
        
        # Queue async task
        from instagram_manager.tasks import ImportBatchPostsTask
        import_task = ImportBatchPostsTask()
        import_task.run_async(
            task_id=task.id,
            profiles=[p.username for p in profiles],
            download_media=download_media,
            save_to_gcs=save_to_gcs,  # NEW
            # ... other params
        )
        
        messages.success(request, f'Started batch import for {len(profiles)} profiles')
        return redirect('admin:instagram_manager_instagramscrapingtask_change', task.id)
    
    # ... error handling
```

### 2.6 Celery Task Update

**Файл**: `instagram_manager/tasks/import_tasks.py`

```python
class ImportBatchPostsTask(BaseTask):
    """Task for batch importing posts from multiple profiles"""
    
    name = "import_batch_posts"
    description = "Import posts from multiple Instagram profiles"
    
    def execute(self, profiles: List[str], download_media: bool = True, 
                save_to_gcs: bool = False, **kwargs) -> Dict[str, Any]:
        """Execute batch import with GCS support"""
        
        results = {
            'total_profiles': len(profiles),
            'successful_profiles': 0,
            'total_posts': 0,
            'total_media': 0,
            'errors': []
        }
        
        # Initialize handlers
        profile_handler = ProfileHandler()
        post_handler = PostHandler()
        
        # NEW: Initialize media handler with GCS option
        media_handler = MediaHandler(save_to_gcs=save_to_gcs)
        
        for i, username in enumerate(profiles):
            try:
                self.update_progress((i / len(profiles)) * 100)
                
                # Import profile
                profile = profile_handler.get_or_create_profile(username)
                
                # Import posts
                posts = post_handler.import_posts_for_profile(
                    profile,
                    limit=kwargs.get('posts_limit', 20)
                )
                
                # Download media if requested
                if download_media:
                    for post in posts:
                        for media in post.media.all():
                            if media_handler.download_media(media):
                                results['total_media'] += 1
                
                results['successful_profiles'] += 1
                results['total_posts'] += len(posts)
                
            except Exception as e:
                logger.error(f"Failed to import {username}: {e}")
                results['errors'].append(f"{username}: {str(e)}")
        
        return results
```

### 2.7 PostHandler Update

**Файл**: `instagram_manager/instagram_api/data_handlers/post_handler.py`

```python
class PostHandler:
    """Handler for Instagram post operations"""
    
    def __init__(self, skip_media_download: bool = True, save_to_gcs: bool = False):
        self.client = BrightDataClient()
        self.skip_media_download = skip_media_download
        self.save_to_gcs = save_to_gcs  # NEW
        self.media_handler = MediaHandler(save_to_gcs=save_to_gcs)  # NEW
    
    def _auto_download_media(self, post: InstagramPost) -> None:
        """Automatically download media if configured"""
        if self.skip_media_download:
            return
        
        # Use media handler with GCS support
        for media in post.media.all():
            try:
                self.media_handler.download_media(media)
            except Exception as e:
                logger.error(f"Failed to download media {media.id}: {e}")
```

## 3. Конфигурация

### 3.1 Settings

**Файл**: `SocialManager/settings/base.py`

```python
# Google Cloud Storage
GCS_BUCKET_NAME = env('GCS_BUCKET_NAME', default='')
# Path to service account JSON key (optional if using default credentials)
GOOGLE_APPLICATION_CREDENTIALS = env('GOOGLE_APPLICATION_CREDENTIALS', default='')

# Feature flag for GCS
INSTAGRAM_ENABLE_GCS_STORAGE = env.bool('INSTAGRAM_ENABLE_GCS_STORAGE', default=bool(GCS_BUCKET_NAME))
```

### 3.2 Environment Variables

```env
# Google Cloud Storage
GCS_BUCKET_NAME=socialmanager-media-prod
GOOGLE_APPLICATION_CREDENTIALS=/path/to/service-account-key.json

# Optional: Use Application Default Credentials instead
# https://cloud.google.com/docs/authentication/production#automatically
```

## 4. Миграция

**Файл**: `instagram_manager/migrations/00XX_add_gcs_url.py`

```python
from django.db import migrations, models

class Migration(migrations.Migration):
    dependencies = [
        ('instagram_manager', '00XX_previous_migration'),
    ]

    operations = [
        migrations.AddField(
            model_name='instagrammedia',
            name='gcs_url',
            field=models.CharField(
                max_length=1024, 
                null=True, 
                blank=True,
                help_text='Google Cloud Storage URL if media is stored in GCS'
            ),
        ),
    ]
```

## 5. Сигналы для очистки

**Файл**: `instagram_manager/signals.py`

```python
@receiver(pre_delete, sender=InstagramMedia)
def delete_media_files(sender, instance, **kwargs):
    """Delete media files when InstagramMedia is deleted"""
    
    # Existing local file deletion...
    
    # NEW: Delete from GCS if exists
    if instance.gcs_url and settings.GCS_BUCKET_NAME:
        try:
            from core.storage.gcs_service import GCSService
            gcs = GCSService()
            gcs.delete_file(instance.gcs_url)
            logger.info(f"Deleted GCS file: {instance.gcs_url}")
        except Exception as e:
            logger.error(f"Failed to delete GCS file: {e}")
```

## 6. Примеры использования

### 6.1 Batch Import с GCS

```python
# В админке при batch import:
# 1. Пользователь отмечает "Save media to GCS"
# 2. Система загружает медиа в GCS
# 3. URL автоматически используется при отображении

# Программно:
from instagram_manager.services import PostService

service = PostService()
posts = service.import_posts_for_profile(
    profile_username="example",
    download_media=True,
    save_to_gcs=True  # Новый параметр
)
```

### 6.2 Проверка URL в шаблонах

```django
{# Работает автоматически благодаря get_display_url() #}
<img src="{{ media.get_display_url }}" alt="{{ media.caption }}">
```

## 7. Обработка ошибок

1. **GCS недоступен**: Автоматический fallback на локальное хранение
2. **Неверные credentials**: Логирование ошибки, сохранение локально
3. **Превышение квоты**: Retry с exponential backoff (будущее улучшение)

## 8. Безопасность

1. **Service Account**: Использовать с минимальными правами (только запись в bucket)
2. **Bucket настройки**: 
   - Public read для медиа файлов
   - Или signed URLs для приватного контента (будущее)
3. **Валидация**: Проверка типов файлов перед загрузкой

## 9. Производительность

1. **Асинхронная загрузка**: Через Celery tasks
2. **Без блокировки**: Импорт продолжается даже если GCS недоступен
3. **CDN**: GCS автоматически использует Google's global CDN

## 10. Мониторинг

Добавить логирование:
- Успешные загрузки в GCS
- Ошибки загрузки с fallback на local
- Время загрузки
- Размер файлов