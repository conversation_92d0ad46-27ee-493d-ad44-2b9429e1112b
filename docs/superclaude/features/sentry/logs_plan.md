# План интеграции логирования с Sentry

## Анализ текущего состояния

### Существующая система логирования
1. В проекте уже есть базовая настройка Sentry в `production.py`, но она используется только для отлавливания исключений
2. Существует продвинутая система логирования (ContextLogger, structlog) в `core/logging/`, но она не активирована
3. Используются обычные логгеры Python (`logging.getLogger`)
4. В продакшне логи пишутся в файлы в папке `/var/log/socialmanager/`
5. Файловое логирование используется в dev окружении (папка `logs/`)

### Статистика использования логирования
- **19 файлов** уже используют ContextLogger
- **82 файла** используют стандартный logging
- **MCP Server** имеет собственную систему логирования

### Проект в Sentry
- Организация: `argo-tv`
- Проект: `social-manager`
- **Доступные DSN** (оба с именем "Default"):
  1. `https://<EMAIL>/4509667853926400`
  2. `https://<EMAIL>/4509667853926400`
- **Важно**: Необходимо выбрать актуальный DSN перед внедрением

## План интеграции

### 1. Унификация системы логирования

#### 1.1 Активация ContextLogger
- Активировать существующую систему `ContextLogger` из `core/logging/`
- Заменить все вызовы `logging.getLogger(__name__)` на `ContextLogger(__name__)`
- Это обеспечит единообразие и автоматическое добавление контекста (request_id, user_id, task_id)

#### 1.2 Активация LoggingContextMiddleware
- Добавить `'core.middleware.logging_middleware.LoggingContextMiddleware'` в MIDDLEWARE
- Это автоматически добавит request_id и user_id ко всем логам HTTP запросов

### 2. Интеграция с Sentry

#### 2.1 Обновление конфигурации Sentry
Обновить инициализацию Sentry в `production.py`:
```python
sentry_sdk.init(
    dsn=SENTRY_DSN,
    integrations=[
        DjangoIntegration(),
        LoggingIntegration(
            level=logging.INFO,        # Минимальный уровень для breadcrumbs
            event_level=logging.ERROR  # Только ERROR и CRITICAL в Sentry
        ),
        RedisIntegration(),
        CeleryIntegration(),          # Для отслеживания ошибок в Celery задачах
    ],
    traces_sample_rate=0.1,
    send_default_pii=False,
    environment="production",
    before_send=before_send_filter,
    attach_stacktrace=True,           # Стектрейсы для всех событий
)
```

#### 2.2 Создание before_send фильтра
```python
def before_send_filter(event, hint):
    # Фильтрация PII данных
    if 'request' in event and 'cookies' in event['request']:
        event['request']['cookies'] = '[FILTERED]'
    
    # Пропускаем известные некритичные ошибки
    if 'logger' in event and event['logger'] in ['django.security.DisallowedHost']:
        return None
        
    return event
```

#### 2.3 Создание кастомного Sentry Handler
- Создать `SentryHandler` в `core/logging/handlers.py`
- Интегрировать с ContextLogger для передачи контекста
- Настроить отправку только важных ошибок в Sentry

### 3. Конфигурация логирования для разных окружений

#### 3.1 Production (production.py)
- ERROR и выше → Sentry
- INFO и выше → stdout (для подхвата существующей системой)
- **УБРАТЬ файловые handlers** (file, error_file, performance_file)
- Оставить только console handler с JSON форматированием
- **Важно**: Использовать `sys.stdout` для корректной работы в контейнерах

```python
'console': {
    'class': 'logging.StreamHandler',
    'formatter': 'json',
    'stream': sys.stdout,  # Важно: stdout для контейнеров
}
```

#### 3.2 Local/Development (local.py, base.py)
- **ОСТАВИТЬ файловое логирование как есть**
- Логи продолжат писаться в `logs/django.log` и `logs/errors.log`
- Опционально подключить Sentry для тестирования

### 4. Настройка фильтрации

#### 4.1 Создание LogFilter
Создать `filters.py` в `core/logging/`:
```python
SENSITIVE_FIELDS = [
    'password', 'token', 'secret', 'api_key', 
    'session', 'cookie', 'authorization',
    'BRIGHTDATA_API_TOKEN', 'FIELD_ENCRYPTION_KEY',
    'SENTRY_DSN', 'DATABASE_URL'
]

def scrub_sensitive_data(record):
    """Удаляет чувствительные данные из логов"""
    # Реализация фильтрации
    pass
```

#### 4.2 Настройка Sentry
- Настроить игнорирование определённых типов ошибок (DisallowedHost, etc.)
- Настроить сэмплирование для высоконагруженных эндпоинтов

### 5. Дополнительные улучшения

#### 5.1 Контекст для Celery задач
- Интегрировать task_id в контекст логирования
- Настроить отправку ошибок Celery в Sentry

#### 5.2 Метрики и мониторинг
- Добавить performance monitoring для критичных операций
- Настроить алерты в Sentry для критических ошибок

### 6. Структура файлов

```
core/logging/
├── __init__.py (уже есть)
├── logger.py (уже есть)
├── formatters.py (уже есть)
├── setup.py (уже есть)
├── handlers.py (новый) - SentryHandler
└── filters.py (новый) - фильтры для логов
```

### 7. Изменения в settings файлах

#### base.py
- Оставить текущую конфигурацию LOGGING без изменений (с файловым логированием)

#### production.py
- Полностью переопределить LOGGING:
  - Убрать все файловые handlers
  - Оставить только console handler с JSON форматом
  - Добавить sentry handler для ERROR и выше

#### local.py
- Не трогать, наследует от base.py с файловым логированием

### 8. Переменные окружения

Добавить в `.env` для production:
```
# Выберите один из доступных DSN:
SENTRY_DSN=<выбранный DSN>
SENTRY_ENVIRONMENT=production
SENTRY_TRACES_SAMPLE_RATE=0.1
```

## Ожидаемые результаты

Этот план обеспечит:
- Отправку важных ошибок в Sentry в продакшне
- Вывод остальных логов в stdout для существующей системы в продакшне
- Сохранение файлового логирования для dev окружения
- Единую систему логирования
- Автоматическое добавление контекста
- Фильтрацию чувствительных данных
- Отслеживание ошибок в Celery задачах через CeleryIntegration

## Риски и митигация

| Риск | Вероятность | Влияние | Митигация |
|------|-------------|---------|-----------|
| Утечка PII данных | Средняя | Высокое | before_send фильтр, code review |
| Перегрузка Sentry | Низкая | Среднее | Rate limiting, sampling |
| Регрессии при миграции | Средняя | Среднее | Поэтапная миграция, тесты |
| Потеря логов | Низкая | Высокое | Параллельная работа систем |

## Тестирование

### Unit тесты
- Проверка отправки ошибок в Sentry
- Проверка фильтрации чувствительных данных
- Проверка добавления контекста

### Integration тесты
- Проверка на staging окружении
- Симуляция различных типов ошибок
- Валидация данных в Sentry UI

## Примерная оценка работ

1. Унификация системы логирования - 2 часа
2. Настройка Sentry интеграции - 1 час
3. Создание фильтров и handlers - 2 часа
4. Тестирование в разных окружениях - 1 час
5. Документация - 30 минут

**Итого: ~6.5 часов**