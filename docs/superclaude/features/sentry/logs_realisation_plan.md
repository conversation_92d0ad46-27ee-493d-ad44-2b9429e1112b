# План реализации интеграции логирования с Sentry

## 📋 Executive Summary
Создание детального плана поэтапной реализации интеграции системы логирования с Sentry для проекта SocialManager. План включает:
- Унификацию системы логирования через ContextLogger
- Настройку Sentry для production окружения
- Сохранение файлового логирования для dev окружения
- Создание фильтров для защиты чувствительных данных
- Интеграцию с Celery и Django middleware

## 🎯 Цели реализации
1. **Централизованный мониторинг ошибок** в production через Sentry
2. **Единообразная система логирования** через ContextLogger
3. **Автоматический контекст** (request_id, user_id, task_id) во всех логах
4. **Защита чувствительных данных** через фильтрацию
5. **Сохранение совместимости** с существующим кодом

## 📊 Архитектурный подход
- **Layered Architecture**: Разделение handlers, formatters, filters
- **Dependency Injection**: Конфигурация через settings
- **Strategy Pattern**: Разные стратегии логирования для разных окружений
- **Decorator Pattern**: Обогащение логов контекстом
- **Chain of Responsibility**: Последовательная обработка логов

## 🔄 Фазы реализации

### Фаза 1: Подготовка инфраструктуры (2 часа)
1. Создание новых модулей в core/logging/
2. Настройка базовых классов и интерфейсов
3. Подготовка тестового окружения

### Фаза 2: Унификация логирования (2 часа)
1. Миграция с logging.getLogger на ContextLogger
2. Активация LoggingContextMiddleware
3. Тестирование контекстного обогащения

### Фаза 3: Интеграция Sentry (2 часа)
1. Настройка Sentry SDK
2. Создание кастомных handlers
3. Конфигурация фильтров

### Фаза 4: Тестирование и валидация (1.5 часа)
1. Unit и integration тесты
2. Проверка в staging окружении
3. Валидация в Sentry UI

## 📁 Структура файлов и изменений

### Новые файлы:
```
core/logging/
├── handlers.py      # SentryHandler, консольные handlers
├── filters.py       # Фильтрация чувствительных данных
├── processors.py    # Обработчики для structlog
└── testing.py       # Утилиты для тестирования логов
```

### Изменяемые файлы:
```
settings/
├── base.py         # Без изменений (файловое логирование)
├── production.py   # Полная переработка LOGGING
└── local.py        # Минимальные изменения

core/
├── middleware/logging_middleware.py  # Активация
└── logging/logger.py                 # Улучшения ContextLogger
```

## 🔧 Детальная реализация

### 1. SentryHandler (core/logging/handlers.py)
```python
import sentry_sdk
from logging import Handler, LogRecord
from typing import Optional, Dict, Any
from core.logging.logger import get_context

class SentryHandler(Handler):
    """Handler для отправки логов в Sentry с контекстом"""
    
    def __init__(self, level=logging.ERROR):
        super().__init__(level)
        self.event_level = level
        
    def emit(self, record: LogRecord) -> None:
        # Добавляем контекст из ContextLogger
        with sentry_sdk.push_scope() as scope:
            # Контекст запроса
            context = get_context()
            if context:
                scope.set_context("request_context", {
                    "request_id": context.get("request_id"),
                    "user_id": context.get("user_id"),
                    "task_id": context.get("task_id"),
                })
            
            # Дополнительные данные из record
            if hasattr(record, 'extra_context'):
                scope.set_context("extra", record.extra_context)
            
            # Теги для фильтрации в Sentry
            scope.set_tag("logger", record.name)
            scope.set_tag("level", record.levelname)
            
            # Отправка в Sentry
            if record.levelno >= self.event_level:
                sentry_sdk.capture_message(
                    record.getMessage(),
                    level=self._get_sentry_level(record.levelno)
                )
    
    def _get_sentry_level(self, levelno: int) -> str:
        """Маппинг уровней Python logging на Sentry"""
        mapping = {
            logging.DEBUG: "debug",
            logging.INFO: "info",
            logging.WARNING: "warning",
            logging.ERROR: "error",
            logging.CRITICAL: "fatal",
        }
        return mapping.get(levelno, "error")
```

### 2. Фильтры для чувствительных данных (core/logging/filters.py)
```python
import re
import logging
from typing import List, Dict, Any
from dataclasses import dataclass

@dataclass
class SensitivePattern:
    """Паттерн для поиска чувствительных данных"""
    name: str
    pattern: re.Pattern
    replacement: str = "[REDACTED]"

class SensitiveDataFilter(logging.Filter):
    """Фильтр для удаления чувствительных данных из логов"""
    
    # Паттерны для чувствительных данных
    PATTERNS = [
        SensitivePattern(
            "api_key",
            re.compile(r'(api[_-]?key|apikey)["\']?\s*[:=]\s*["\']?([^"\'\s]+)', re.I)
        ),
        SensitivePattern(
            "password",
            re.compile(r'(password|passwd|pwd)["\']?\s*[:=]\s*["\']?([^"\'\s]+)', re.I)
        ),
        SensitivePattern(
            "token",
            re.compile(r'(token|auth|bearer)["\']?\s*[:=]\s*["\']?([^"\'\s]+)', re.I)
        ),
        SensitivePattern(
            "secret",
            re.compile(r'(secret|private[_-]?key)["\']?\s*[:=]\s*["\']?([^"\'\s]+)', re.I)
        ),
        SensitivePattern(
            "dsn",
            re.compile(r'https?://[a-f0-9]+@[^/]+/\d+', re.I),
            "[SENTRY_DSN]"
        ),
        SensitivePattern(
            "email",
            re.compile(r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'),
            "[EMAIL]"
        ),
    ]
    
    # Поля для проверки в extra данных
    SENSITIVE_FIELDS = {
        'password', 'token', 'secret', 'api_key', 'authorization',
        'cookie', 'session', 'csrf', 'csrftoken', 'sessionid',
        'BRIGHTDATA_API_TOKEN', 'FIELD_ENCRYPTION_KEY', 'SENTRY_DSN',
        'DATABASE_URL', 'REDIS_URL', 'API_HASH', 'API_ID'
    }
    
    def filter(self, record: LogRecord) -> bool:
        # Очистка сообщения
        record.msg = self._scrub_message(str(record.msg))
        
        # Очистка аргументов
        if record.args:
            record.args = self._scrub_args(record.args)
        
        # Очистка extra данных
        if hasattr(record, '__dict__'):
            for key, value in record.__dict__.items():
                if key.lower() in self.SENSITIVE_FIELDS:
                    setattr(record, key, "[REDACTED]")
                elif isinstance(value, (str, dict, list)):
                    setattr(record, key, self._scrub_value(value))
        
        return True
    
    def _scrub_message(self, message: str) -> str:
        """Очистка сообщения от чувствительных данных"""
        for pattern in self.PATTERNS:
            message = pattern.pattern.sub(
                lambda m: f"{m.group(1)}={pattern.replacement}",
                message
            )
        return message
    
    def _scrub_args(self, args):
        """Очистка аргументов логирования"""
        if isinstance(args, dict):
            return {k: self._scrub_value(v) for k, v in args.items()}
        elif isinstance(args, (list, tuple)):
            return type(args)(self._scrub_value(v) for v in args)
        else:
            return self._scrub_value(args)
    
    def _scrub_value(self, value):
        """Рекурсивная очистка значений"""
        if isinstance(value, str):
            return self._scrub_message(value)
        elif isinstance(value, dict):
            return {
                k: "[REDACTED]" if k.lower() in self.SENSITIVE_FIELDS 
                else self._scrub_value(v)
                for k, v in value.items()
            }
        elif isinstance(value, (list, tuple)):
            return type(value)(self._scrub_value(v) for v in value)
        return value

class RateLimitFilter(logging.Filter):
    """Фильтр для ограничения частоты одинаковых сообщений"""
    
    def __init__(self, rate_limit_seconds: int = 60):
        super().__init__()
        self.rate_limit_seconds = rate_limit_seconds
        self.seen_messages: Dict[str, float] = {}
    
    def filter(self, record: LogRecord) -> bool:
        import time
        
        # Создаем ключ из logger name + level + message
        key = f"{record.name}:{record.levelno}:{record.getMessage()}"
        current_time = time.time()
        
        # Проверяем, видели ли мы это сообщение недавно
        last_seen = self.seen_messages.get(key, 0)
        if current_time - last_seen < self.rate_limit_seconds:
            return False
        
        # Обновляем время последнего показа
        self.seen_messages[key] = current_time
        
        # Очистка старых записей
        self._cleanup_old_entries(current_time)
        
        return True
    
    def _cleanup_old_entries(self, current_time: float):
        """Удаление старых записей для экономии памяти"""
        if len(self.seen_messages) > 1000:
            cutoff_time = current_time - self.rate_limit_seconds * 2
            self.seen_messages = {
                k: v for k, v in self.seen_messages.items()
                if v > cutoff_time
            }
```

### 3. Обновление settings/production.py
```python
import sys
import sentry_sdk
from sentry_sdk.integrations.django import DjangoIntegration
from sentry_sdk.integrations.logging import LoggingIntegration
from sentry_sdk.integrations.redis import RedisIntegration
from sentry_sdk.integrations.celery import CeleryIntegration

# Sentry Configuration
SENTRY_DSN = env('SENTRY_DSN')
SENTRY_ENVIRONMENT = env('SENTRY_ENVIRONMENT', default='production')
SENTRY_TRACES_SAMPLE_RATE = env.float('SENTRY_TRACES_SAMPLE_RATE', default=0.1)

def before_send_filter(event, hint):
    """Фильтр событий перед отправкой в Sentry"""
    # Фильтрация известных некритичных ошибок
    if 'logger' in event:
        skip_loggers = [
            'django.security.DisallowedHost',
            'django.request.DisallowedHost',
            'django.core.exceptions.SuspiciousOperation',
        ]
        if event['logger'] in skip_loggers:
            return None
    
    # Удаление cookies из request данных
    if 'request' in event and 'cookies' in event['request']:
        event['request']['cookies'] = '[FILTERED]'
    
    # Удаление headers с токенами
    if 'request' in event and 'headers' in event['request']:
        sensitive_headers = ['authorization', 'x-api-key', 'cookie']
        for header in sensitive_headers:
            if header in event['request']['headers']:
                event['request']['headers'][header] = '[FILTERED]'
    
    return event

# Инициализация Sentry
sentry_sdk.init(
    dsn=SENTRY_DSN,
    integrations=[
        DjangoIntegration(
            transaction_style='function_name',
            middleware_spans=True,
        ),
        LoggingIntegration(
            level=logging.INFO,        # Capture info and above as breadcrumbs
            event_level=logging.ERROR  # Send errors as events
        ),
        RedisIntegration(),
        CeleryIntegration(
            monitor_beat_tasks=True,
            propagate_traces=True,
        ),
    ],
    traces_sample_rate=SENTRY_TRACES_SAMPLE_RATE,
    send_default_pii=False,
    environment=SENTRY_ENVIRONMENT,
    before_send=before_send_filter,
    attach_stacktrace=True,
    
    # Performance monitoring
    profiles_sample_rate=0.1,
    
    # Release tracking
    release=env('APP_VERSION', default='unknown'),
    
    # Дополнительные опции
    max_breadcrumbs=50,
    debug=False,
    
    # Игнорируемые ошибки
    ignore_errors=[
        # Django
        'django.security.DisallowedHost',
        'django.core.exceptions.ObjectDoesNotExist',
        'django.core.exceptions.PermissionDenied',
        
        # Python
        KeyboardInterrupt,
        SystemExit,
        
        # Celery
        'celery.exceptions.Ignore',
        'celery.exceptions.Retry',
    ],
)

# Полная переработка LOGGING для production
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    
    'filters': {
        'sensitive_data': {
            '()': 'core.logging.filters.SensitiveDataFilter',
        },
        'rate_limit': {
            '()': 'core.logging.filters.RateLimitFilter',
            'rate_limit_seconds': 60,
        },
        'require_debug_false': {
            '()': 'django.utils.log.RequireDebugFalse',
        },
    },
    
    'formatters': {
        'json': {
            '()': 'pythonjsonlogger.jsonlogger.JsonFormatter',
            'format': '%(asctime)s %(name)s %(levelname)s %(message)s',
            'datefmt': '%Y-%m-%d %H:%M:%S',
            'json_ensure_ascii': False,
        },
    },
    
    'handlers': {
        'console': {
            'level': 'INFO',
            'class': 'logging.StreamHandler',
            'formatter': 'json',
            'stream': sys.stdout,  # Важно для контейнеров
            'filters': ['sensitive_data', 'rate_limit'],
        },
        'sentry': {
            '()': 'core.logging.handlers.SentryHandler',
            'level': 'ERROR',
            'filters': ['sensitive_data'],
        },
    },
    
    'root': {
        'level': 'INFO',
        'handlers': ['console', 'sentry'],
    },
    
    'loggers': {
        'django': {
            'handlers': ['console', 'sentry'],
            'level': 'INFO',
            'propagate': False,
        },
        'django.request': {
            'handlers': ['console', 'sentry'],
            'level': 'WARNING',
            'propagate': False,
        },
        'django.security': {
            'handlers': ['console'],
            'level': 'WARNING',
            'propagate': False,
        },
        'django.db.backends': {
            'handlers': ['console'],
            'level': 'WARNING',  # Или DEBUG для SQL логов
            'propagate': False,
        },
        
        # App-specific логгеры
        'telegram_manager': {
            'handlers': ['console', 'sentry'],
            'level': 'INFO',
            'propagate': False,
        },
        'instagram_manager': {
            'handlers': ['console', 'sentry'],
            'level': 'INFO',
            'propagate': False,
        },
        'core': {
            'handlers': ['console', 'sentry'],
            'level': 'INFO',
            'propagate': False,
        },
        'mcp_server': {
            'handlers': ['console', 'sentry'],
            'level': 'INFO',
            'propagate': False,
        },
        
        # Celery логгеры
        'celery': {
            'handlers': ['console', 'sentry'],
            'level': 'INFO',
            'propagate': False,
        },
        'celery.task': {
            'handlers': ['console', 'sentry'],
            'level': 'INFO',
            'propagate': False,
        },
        
        # Внешние библиотеки (снижаем уровень шума)
        'urllib3': {
            'handlers': ['console'],
            'level': 'WARNING',
            'propagate': False,
        },
        'requests': {
            'handlers': ['console'],
            'level': 'WARNING',
            'propagate': False,
        },
    },
}

# Активация middleware
MIDDLEWARE = [
    'django.middleware.security.SecurityMiddleware',
    'core.middleware.logging_middleware.LoggingContextMiddleware',  # Добавлено
    'whitenoise.middleware.WhiteNoiseMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    # ... остальные middleware
]
```

### 4. Улучшенный ContextLogger (core/logging/logger.py)
```python
import logging
import contextvars
from typing import Dict, Any, Optional
import structlog

# Контекстная переменная для хранения данных
_context_var: contextvars.ContextVar[Dict[str, Any]] = contextvars.ContextVar(
    'logging_context',
    default={}
)

class ContextLogger:
    """Enhanced logger with automatic context injection"""
    
    def __init__(self, name: str):
        self.name = name
        self.logger = logging.getLogger(name)
        
        # Structlog процессоры
        self.processors = [
            structlog.stdlib.filter_by_level,
            structlog.stdlib.add_logger_name,
            structlog.stdlib.add_log_level,
            structlog.stdlib.PositionalArgumentsFormatter(),
            structlog.processors.TimeStamper(fmt="iso"),
            structlog.processors.StackInfoRenderer(),
            structlog.processors.format_exc_info,
            self._add_context,
            structlog.processors.UnicodeDecoder(),
        ]
        
        # Инициализация structlog
        self.struct_logger = structlog.wrap_logger(
            self.logger,
            processors=self.processors,
            context_class=dict,
            logger_factory=structlog.stdlib.LoggerFactory(),
            cache_logger_on_first_use=True,
        )
    
    def _add_context(self, logger, method_name, event_dict):
        """Добавляет контекст к каждому лог сообщению"""
        context = get_context()
        if context:
            event_dict.update(context)
        return event_dict
    
    def bind(self, **kwargs) -> 'ContextLogger':
        """Создает новый logger с дополнительным контекстом"""
        new_logger = ContextLogger(self.name)
        new_logger.struct_logger = self.struct_logger.bind(**kwargs)
        return new_logger
    
    def unbind(self, *keys) -> 'ContextLogger':
        """Удаляет ключи из контекста"""
        new_logger = ContextLogger(self.name)
        new_logger.struct_logger = self.struct_logger.unbind(*keys)
        return new_logger
    
    # Методы логирования
    def debug(self, msg, *args, **kwargs):
        self.struct_logger.debug(msg, *args, **kwargs)
    
    def info(self, msg, *args, **kwargs):
        self.struct_logger.info(msg, *args, **kwargs)
    
    def warning(self, msg, *args, **kwargs):
        self.struct_logger.warning(msg, *args, **kwargs)
    
    def error(self, msg, *args, **kwargs):
        self.struct_logger.error(msg, *args, **kwargs)
    
    def critical(self, msg, *args, **kwargs):
        self.struct_logger.critical(msg, *args, **kwargs)
    
    def exception(self, msg, *args, **kwargs):
        """Log exception with traceback"""
        kwargs['exc_info'] = kwargs.get('exc_info', True)
        self.struct_logger.error(msg, *args, **kwargs)

def get_context() -> Dict[str, Any]:
    """Получает текущий контекст логирования"""
    return _context_var.get()

def set_context(**kwargs):
    """Устанавливает контекст логирования"""
    current = _context_var.get()
    updated = {**current, **kwargs}
    _context_var.set(updated)

def clear_context():
    """Очищает контекст логирования"""
    _context_var.set({})
```

### 5. Миграционный скрипт
```python
#!/usr/bin/env python
"""
Скрипт для миграции с logging.getLogger на ContextLogger
Запуск: python scripts/migrate_to_context_logger.py
"""

import os
import re
from pathlib import Path
from typing import List, Tuple

def find_python_files(root_dir: str) -> List[Path]:
    """Находит все Python файлы в проекте"""
    exclude_dirs = {'.venv', '__pycache__', 'migrations', 'node_modules', '.git'}
    python_files = []
    
    for root, dirs, files in os.walk(root_dir):
        dirs[:] = [d for d in dirs if d not in exclude_dirs]
        for file in files:
            if file.endswith('.py'):
                python_files.append(Path(root) / file)
    
    return python_files

def migrate_file(file_path: Path) -> Tuple[bool, List[str]]:
    """Мигрирует один файл"""
    changes = []
    modified = False
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Паттерны для замены
    patterns = [
        # import logging -> from core.logging import ContextLogger
        (
            r'^import logging\s*$',
            'from core.logging import ContextLogger\n',
            'Replaced import'
        ),
        # from logging import getLogger -> from core.logging import ContextLogger
        (
            r'^from logging import .*getLogger.*$',
            'from core.logging import ContextLogger\n',
            'Replaced from import'
        ),
        # logger = logging.getLogger(__name__) -> logger = ContextLogger(__name__)
        (
            r'(\w+)\s*=\s*logging\.getLogger\((.*?)\)',
            r'\1 = ContextLogger(\2)',
            'Replaced logger creation'
        ),
        # getLogger(__name__) -> ContextLogger(__name__)
        (
            r'getLogger\((.*?)\)',
            r'ContextLogger(\1)',
            'Replaced getLogger call'
        ),
    ]
    
    new_content = content
    for pattern, replacement, description in patterns:
        new_content, count = re.subn(pattern, replacement, new_content, flags=re.MULTILINE)
        if count > 0:
            changes.append(f"{description}: {count} occurrences")
            modified = True
    
    if modified:
        # Backup original file
        backup_path = file_path.with_suffix('.py.bak')
        with open(backup_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        # Write new content
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(new_content)
    
    return modified, changes

def main():
    """Основная функция миграции"""
    root_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    python_files = find_python_files(root_dir)
    
    print(f"Found {len(python_files)} Python files")
    
    modified_files = []
    for file_path in python_files:
        try:
            modified, changes = migrate_file(file_path)
            if modified:
                modified_files.append((file_path, changes))
                print(f"✓ Modified: {file_path}")
                for change in changes:
                    print(f"  - {change}")
        except Exception as e:
            print(f"✗ Error processing {file_path}: {e}")
    
    print(f"\nSummary: Modified {len(modified_files)} files")
    
if __name__ == '__main__':
    main()
```

## 🧪 Стратегия тестирования

### Unit тесты (core/tests/test_logging.py)
```python
import pytest
import logging
from unittest.mock import patch, MagicMock
from core.logging.filters import SensitiveDataFilter, RateLimitFilter
from core.logging.handlers import SentryHandler
from core.logging.logger import ContextLogger, set_context, clear_context

class TestSensitiveDataFilter:
    """Тесты фильтрации чувствительных данных"""
    
    @pytest.fixture
    def filter(self):
        return SensitiveDataFilter()
    
    def test_filter_api_key(self, filter):
        record = logging.LogRecord(
            name="test", level=logging.INFO, pathname="", lineno=0,
            msg="API_KEY=secret123 in request", args=(), exc_info=None
        )
        filter.filter(record)
        assert "secret123" not in record.msg
        assert "[REDACTED]" in record.msg
    
    def test_filter_password(self, filter):
        record = logging.LogRecord(
            name="test", level=logging.INFO, pathname="", lineno=0,
            msg='User login with password="my_password"', args=(), exc_info=None
        )
        filter.filter(record)
        assert "my_password" not in record.msg
        assert "[REDACTED]" in record.msg
    
    def test_filter_sentry_dsn(self, filter):
        dsn = "https://<EMAIL>/789"
        record = logging.LogRecord(
            name="test", level=logging.INFO, pathname="", lineno=0,
            msg=f"Configured with DSN: {dsn}", args=(), exc_info=None
        )
        filter.filter(record)
        assert dsn not in record.msg
        assert "[SENTRY_DSN]" in record.msg
    
    def test_filter_extra_data(self, filter):
        record = logging.LogRecord(
            name="test", level=logging.INFO, pathname="", lineno=0,
            msg="Processing request", args=(), exc_info=None
        )
        record.password = "secret"
        record.user_data = {"token": "abc123", "name": "John"}
        
        filter.filter(record)
        assert record.password == "[REDACTED]"
        assert record.user_data["token"] == "[REDACTED]"
        assert record.user_data["name"] == "John"

class TestRateLimitFilter:
    """Тесты ограничения частоты логов"""
    
    def test_rate_limiting(self):
        filter = RateLimitFilter(rate_limit_seconds=1)
        
        # Первое сообщение проходит
        record1 = logging.LogRecord(
            name="test", level=logging.ERROR, pathname="", lineno=0,
            msg="Error occurred", args=(), exc_info=None
        )
        assert filter.filter(record1) is True
        
        # Повторное сообщение блокируется
        record2 = logging.LogRecord(
            name="test", level=logging.ERROR, pathname="", lineno=0,
            msg="Error occurred", args=(), exc_info=None
        )
        assert filter.filter(record2) is False
        
        # Другое сообщение проходит
        record3 = logging.LogRecord(
            name="test", level=logging.ERROR, pathname="", lineno=0,
            msg="Different error", args=(), exc_info=None
        )
        assert filter.filter(record3) is True

class TestContextLogger:
    """Тесты контекстного логгера"""
    
    def setup_method(self):
        clear_context()
    
    def teardown_method(self):
        clear_context()
    
    def test_context_injection(self):
        logger = ContextLogger("test")
        set_context(request_id="123", user_id=456)
        
        with patch.object(logger.struct_logger, 'info') as mock_info:
            logger.info("Test message")
            
            # Проверяем, что контекст был добавлен
            call_args = mock_info.call_args
            assert 'request_id' in str(call_args)
            assert 'user_id' in str(call_args)
    
    def test_bind_unbind(self):
        logger = ContextLogger("test")
        bound_logger = logger.bind(service="api", version="1.0")
        
        with patch.object(bound_logger.struct_logger, 'info') as mock_info:
            bound_logger.info("Bound message")
            
            # Проверяем, что bound контекст был добавлен
            call_args = mock_info.call_args
            assert 'service' in str(call_args)
            assert 'version' in str(call_args)

class TestSentryHandler:
    """Тесты Sentry handler"""
    
    @patch('sentry_sdk.capture_message')
    @patch('sentry_sdk.push_scope')
    def test_emit_with_context(self, mock_push_scope, mock_capture):
        handler = SentryHandler(level=logging.ERROR)
        set_context(request_id="req123", user_id=789)
        
        record = logging.LogRecord(
            name="test", level=logging.ERROR, pathname="", lineno=0,
            msg="Test error", args=(), exc_info=None
        )
        
        # Мокаем scope
        mock_scope = MagicMock()
        mock_push_scope.return_value.__enter__.return_value = mock_scope
        
        handler.emit(record)
        
        # Проверяем, что контекст был добавлен в scope
        mock_scope.set_context.assert_called()
        context_calls = mock_scope.set_context.call_args_list
        assert any("request_context" in str(call) for call in context_calls)
        
        # Проверяем, что сообщение было отправлено
        mock_capture.assert_called_once_with("Test error", level="error")
```

### Integration тесты
```python
@pytest.mark.django_db
class TestLoggingIntegration:
    """Интеграционные тесты системы логирования"""
    
    def test_middleware_context(self, client):
        """Тест добавления контекста через middleware"""
        with patch('core.logging.logger.ContextLogger.info') as mock_log:
            response = client.get('/api/test/')
            
            # Проверяем, что request_id был добавлен
            assert mock_log.called
            call_args = str(mock_log.call_args_list)
            assert 'request_id' in call_args
    
    @patch('sentry_sdk.capture_message')
    def test_error_to_sentry(self, mock_sentry, client):
        """Тест отправки ошибок в Sentry"""
        # Вызываем endpoint, который генерирует ошибку
        with pytest.raises(Exception):
            client.get('/api/error/')
        
        # Проверяем, что ошибка была отправлена в Sentry
        assert mock_sentry.called

class TestCeleryLogging:
    """Тесты логирования в Celery задачах"""
    
    @patch('core.logging.logger.ContextLogger.info')
    def test_task_context(self, mock_log):
        """Тест контекста в Celery задачах"""
        from instagram_manager.tasks import ImportInstagramProfileTask
        
        task = ImportInstagramProfileTask()
        task.task_id = "test-task-123"
        
        # Выполняем задачу
        with patch.object(task, '_import_profile'):
            task.execute(username="test")
        
        # Проверяем, что task_id был в контексте
        call_args = str(mock_log.call_args_list)
        assert 'task_id' in call_args
        assert 'test-task-123' in call_args
```

## 🚀 План развертывания

### 1. Pre-deployment checklist
- [ ] Выбор актуального Sentry DSN
- [ ] Создание тестового проекта в Sentry для staging
- [ ] Подготовка environment переменных
- [ ] Code review всех изменений
- [ ] Прогон всех тестов

### 2. Поэтапное развертывание
1. **Staging (День 1)**
   - Развертывание на staging окружении
   - Валидация логов в stdout
   - Проверка данных в Sentry UI
   - Симуляция различных типов ошибок

2. **Canary deployment (День 2)**
   - Развертывание на 10% production серверов
   - Мониторинг метрик и логов
   - Проверка performance impact

3. **Full rollout (День 3)**
   - Развертывание на все production сервера
   - Активный мониторинг первые 24 часа

### 3. Rollback план
```bash
# В случае проблем - быстрый откат
git revert <commit-hash>
kubectl rollout undo deployment/socialmanager
```

## 📊 Метрики успеха
1. **Снижение MTTR** (Mean Time To Resolution) на 30%
2. **100% ошибок** уровня ERROR попадают в Sentry
3. **0% утечек** чувствительных данных в логах
4. **<5% overhead** на performance
5. **>95% uptime** системы логирования

## ⚠️ Риски и митигация

| Риск | Вероятность | Влияние | Митигация |
|------|-------------|---------|-----------|
| Утечка чувствительных данных | Средняя | Критическое | Многоуровневая фильтрация, code review, тесты |
| Перегрузка Sentry | Низкая | Высокое | Rate limiting, sampling, квоты |
| Performance degradation | Низкая | Среднее | Бенчмарки, профилирование, оптимизация |
| Потеря логов при сбое | Очень низкая | Высокое | Буферизация, retry механизм |
| Неправильная конфигурация | Средняя | Среднее | Валидация конфигурации, тесты |

## 📚 Документация и обучение

### Для разработчиков
1. **Migration guide**: Как переходить на ContextLogger
2. **Best practices**: Что и как логировать
3. **Troubleshooting**: Частые проблемы и решения

### Для DevOps
1. **Deployment guide**: Пошаговая инструкция
2. **Monitoring setup**: Настройка алертов
3. **Performance tuning**: Оптимизация

## 🎯 Финальный чеклист
- [ ] Все тесты проходят
- [ ] Code review завершен
- [ ] Документация обновлена
- [ ] Staging тестирование успешно
- [ ] Rollback план готов
- [ ] Команда обучена
- [ ] Мониторинг настроен

Этот план обеспечит плавную и безопасную интеграцию системы логирования с Sentry, минимизируя риски и максимизируя ценность для команды разработки и поддержки.