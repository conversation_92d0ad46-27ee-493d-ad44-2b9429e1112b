# SuperClaude v3 для SocialManager

Комплексное руководство по использованию SuperClaude v3 для эффективной разработки в проекте SocialManager.

## 🚀 Что нового в v3

- **Префикс команд**: Все команды теперь используют `/sc:` вместо `/`
- **16 команд** вместо 18 (упрощение и объединение)
- **11 персон** включая новые `devops` и `scribe`
- **Playwright** вместо Puppeteer для браузерной автоматизации
- **Улучшенная интеграция** с MCP серверами

## 📁 Структура документации

```
docs/superclaude/
├── README.md                          # Этот файл (обновлен для v3)
├── migration_v2_to_v3.md             # ⭐ Руководство по миграции на v3
├── Super_Claude_Docs.md              # ⭐ Полная документация v3
├── django_workflows.md               # ⭐ Django-специфичные workflow
├── social_integration_guide.md       # ⭐ Интеграция с API соцсетей
├── quick_reference.md                # ⭐ Шпаргалка команд (скоро)
├── project_context.md                # Актуальный контекст проекта
├── templates/                        # Шаблоны документов
│   ├── feature_template.md          # Шаблон описания функционала
│   └── planning_result_template.md  # Шаблон результатов планирования
├── examples/                         # Примеры использования
│   └── moderation_planning.md       # Пример планирования модерации
├── features/                         # Описания функционалов
│   └── (ваши feature файлы)
└── planning/                         # Результаты планирования
    └── (результаты планирования)
```

## 🚀 Быстрый старт

### Для новых пользователей v3:
1. **Изучите миграцию**: [migration_v2_to_v3.md](migration_v2_to_v3.md) - переход на новый синтаксис
2. **Полная документация**: [Super_Claude_Docs.md](Super_Claude_Docs.md) - все команды и персоны
3. **Django workflow**: [django_workflows.md](django_workflows.md) - готовые рецепты для Django
4. **API интеграции**: [social_integration_guide.md](social_integration_guide.md) - работа с соцсетями

### Основные шаги:
1. **Обновите контекст проекта**: Убедитесь, что `project_context.md` актуален
2. **Используйте шаблоны**:
   - Для нового функционала: `templates/feature_template.md`
   - Для результатов: `templates/planning_result_template.md`
3. **Изучите пример**: `examples/moderation_planning.md` для v3 синтаксиса

## 📋 Основные команды SuperClaude v3 для планирования

```bash
# 1. Анализ задачи
/sc:analyze --think-hard --persona=architect,backend "Анализ архитектуры для новой функции..."

# 2. Проектирование
/sc:design --persona=architect,backend --think-hard --seq "Проектирование системы модерации..."

# 3. Анализ рисков  
/sc:analyze --focus security --persona=security,devops "Риски безопасности и инфраструктуры..."

# 4. Декомпозиция задач
/sc:task --detailed --persona=architect "Разбить на задачи 4-8 часов..."

# 5. Оценка времени
/sc:estimate --detailed --ultrathink "Оценка с учетом всех рисков..."

# 6. Документирование
/sc:document --type technical --persona=scribe "Создать техническую документацию..."

# 7. Реализация
/sc:implement --plan --with-tests "Реализовать с тестами..."
```

### 🎯 Примеры для Django проекта

```bash
# Создание нового Django app
/sc:implement django-app analytics --with-tests

# Проектирование API
/sc:design --api --ddd система модерации комментариев

# Анализ производительности
/sc:analyze --performance --seq медленные Celery задачи

# Создание Celery задачи
/sc:implement celery-task ImportTikTokVideos --with-progress

# Тестирование
/sc:test --coverage instagram_manager/
```

## 🎯 Когда использовать

### ✅ Обязательно для:
- Новых функций > 1 недели разработки
- Интеграций с внешними API
- Изменений архитектуры
- Сложных оптимизаций

### ❌ Можно пропустить для:
- Простых багфиксов
- Задач < 1 дня
- Косметических изменений

## 📊 Ключевые метрики эффективности

После использования SuperClaude наблюдаются:
- **-40-60%** архитектурных ошибок
- **+30-50%** точность оценок времени
- **-50%** количество переделок
- **+20%** скорость разработки

## 🔄 Процесс работы

1. **Product Owner** создает описание функционала в `/features/`
2. **Tech Lead** запускает SuperClaude планирование
3. **Команда** делает review результатов
4. **Задачи** импортируются в Jira/GitHub
5. **Результаты** сохраняются в `/planning/`

## 📝 Важные заметки

- **Обновляйте project_context.md** минимум раз в спринт
- **Сохраняйте промежуточные результаты** каждого шага
- **Используйте continuation_id** для связности команд
- **Не экономьте на глубине анализа** (--think-hard) для критических решений

## 🎭 Персоны для разных задач

- **backend**: Django модели, сервисы, репозитории
- **architect**: Системный дизайн, архитектурные решения
- **qa**: Тестирование, pytest, coverage
- **security**: Аудит безопасности, защита данных
- **performance**: Оптимизация запросов, профилирование
- **devops**: Docker, CI/CD, деплой [НОВАЯ]
- **scribe**: Документация, API docs [НОВАЯ]

## 🆘 Поддержка и ресурсы

### Документация SuperClaude v3:
- [Руководство по миграции](migration_v2_to_v3.md) - переход с v2 на v3
- [Полная документация](Super_Claude_Docs.md) - все возможности v3
- [Django workflows](django_workflows.md) - готовые решения
- [API интеграции](social_integration_guide.md) - работа с соцсетями

### Внешние ресурсы:
- SuperClaude v3: [github.com/NomenAK/SuperClaude](https://github.com/NomenAK/SuperClaude)
- Официальная документация: [SuperClaude Docs](https://github.com/NomenAK/SuperClaude/tree/master/Docs)
- Примеры использования: [Examples](https://github.com/NomenAK/SuperClaude/tree/master/examples)

### Проект SocialManager:
- Контекст проекта: [project_context.md](project_context.md)
- Шаблоны: [templates/](templates/)
- Примеры: [examples/](examples/)

---

*Последнее обновление: 2025-01-14 | SuperClaude v3.0*