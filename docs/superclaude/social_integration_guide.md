# SuperClaude v3 Guide: Интеграция с API социальных сетей

**Версия**: 1.0  
**Дата создания**: 2025-01-14  
**Проект**: SocialManager

## 🌐 Обзор

Комплексное руководство по использованию SuperClaude v3 для интеграции с API социальных сетей. Фокус на Instagram (BrightData), Telegram (Telethon) и планируемых интеграциях с TikTok, Facebook, Twitter.

---

## 📋 Содержание

1. [Общие принципы интеграции](#general-principles)
2. [Instagram & BrightData API](#instagram-brightdata)
3. [Telegram & Telethon](#telegram-telethon)
4. [Проектирование новых интеграций](#new-integrations)
5. [Обработка данных и валидация](#data-processing)
6. [Rate Limiting и устойчивость](#rate-limiting)
7. [Тестирование интеграций](#testing-integrations)
8. [Мониторинг и отладка](#monitoring-debugging)
9. [Безопасность и compliance](#security-compliance)
10. [Best Practices](#best-practices)

---

## 🎯 General Principles

### Архитектура интеграций в SocialManager

```yaml
Структура интеграции:
  models/: Модели Django для хранения данных
  repositories/: Доступ к данным через паттерн Repository
  services/: Бизнес-логика и оркестрация
  schemas/: Pydantic схемы для валидации API
  api_clients/: Клиенты для внешних API
  tasks/: Celery задачи для асинхронной обработки
```

### Workflow создания новой интеграции

```bash
# 1. Исследование API
/sc:analyze --c7 документация [SocialNetwork] API

# 2. Проектирование архитектуры
/sc:design --api --think-hard интеграция с [SocialNetwork]

# 3. Создание структуры
/sc:implement django-app [socialnetwork]_manager

# 4. Реализация клиента
/sc:implement api-client для [SocialNetwork] с retry

# 5. Pydantic схемы
/sc:implement pydantic схемы для [SocialNetwork] responses

# 6. Модели и миграции
/sc:implement модели для хранения [SocialNetwork] данных

# 7. Сервисы и задачи
/sc:implement сервисы и Celery задачи для импорта
```

---

## 📸 Instagram & BrightData API

### Структура интеграции

```python
instagram_manager/
├── instagram_api/
│   ├── client.py          # BrightDataClient с rate limiting
│   ├── handlers/          # Обработчики для разных типов данных
│   │   ├── profile.py     # ProfileDataHandler
│   │   ├── post.py        # PostDataHandler
│   │   ├── comment.py     # CommentDataHandler
│   │   └── media.py       # MediaDataHandler
│   └── exceptions.py      # Специфичные исключения
├── schemas/               # Pydantic валидация
│   ├── profile.py        # InstagramProfileSchema
│   ├── post.py           # InstagramPostSchema
│   └── comment.py        # InstagramCommentSchema
└── tasks/                # Celery задачи
    ├── import_profile.py
    ├── import_posts.py
    └── import_comments.py
```

### Работа с BrightData API

```bash
# 1. Анализ существующей интеграции
/sc:analyze instagram_api/client.py понять rate limits

# 2. Добавление нового endpoint
/sc:implement в BrightDataClient метод для stories

# 3. Создание handler для данных
/sc:implement StoryDataHandler с валидацией

# 4. Расширение схем
/sc:implement pydantic схема InstagramStorySchema

# 5. Celery задача для импорта
/sc:implement celery-task ImportStoriesTask
```

### Обработка медиафайлов

```bash
# 1. Анализ текущей реализации
/sc:analyze MediaService и GCS интеграция

# 2. Оптимизация загрузки
/sc:improve --performance batch загрузка медиа

# 3. Добавление preview генерации
/sc:implement генерация превью для видео

# 4. CDN интеграция
/sc:design CDN стратегия для медиафайлов
```

### Типовые задачи

```bash
# Импорт профиля с постами
/sc:implement workflow полный импорт профиля:
1. Получить данные профиля
2. Сохранить в БД
3. Запустить импорт постов
4. Для каждого поста импортировать комментарии
5. Загрузить все медиафайлы

# Обновление метрик
/sc:implement scheduled task обновление метрик постов

# Модерация комментариев
/sc:implement автомодерация новых комментариев
```

---

## 💬 Telegram & Telethon

### Структура интеграции

```python
telegram_manager/
├── telegram_api/
│   ├── client.py         # TelegramManager с Telethon
│   ├── handlers/         # Обработчики событий
│   │   ├── chat.py      # ChatHandler
│   │   ├── message.py   # MessageHandler
│   │   └── user.py      # UserHandler
│   └── session.py       # Управление сессиями
├── services/
│   ├── telegram_service.py
│   ├── message_import_service.py
│   └── chat_service.py
└── tasks/
    ├── import_chats.py
    ├── import_messages.py
    └── export_messages.py
```

### Работа с Telethon

```bash
# 1. Настройка сессии
/sc:implement безопасное хранение Telegram сессий

# 2. Обработка flood limits
/sc:implement адаптивные задержки для Telegram API

# 3. Импорт сообщений
/sc:improve оптимизация импорта больших чатов

# 4. Real-time мониторинг
/sc:implement webhook для новых сообщений

# 5. Экспорт данных
/sc:implement экспорт чата в различные форматы
```

### Обработка событий

```bash
# Event handlers
/sc:implement event handler для:
- Новых сообщений
- Редактирования сообщений
- Удаления сообщений
- Присоединения пользователей
- Изменения настроек чата

# Фильтрация сообщений
/sc:implement фильтры для сообщений:
- По ключевым словам
- По типу медиа
- По отправителю
- По дате
```

---

## 🆕 Проектирование новых интеграций

### TikTok интеграция (пример)

```bash
# 1. Исследование API
/sc:analyze --c7 TikTok API возможности и ограничения

# 2. Проектирование структуры
/sc:design --ddd --ultrathink архитектура TikTok интеграции

# 3. Создание app
/sc:implement django-app tiktok_manager с базовой структурой

# 4. API клиент
/sc:implement TikTokAPIClient с:
- OAuth2 аутентификация
- Rate limiting (100/min)
- Retry с exponential backoff
- Error handling

# 5. Модели данных
/sc:implement модели:
- TikTokProfile
- TikTokVideo
- TikTokComment
- TikTokHashtag
- TikTokSound

# 6. Pydantic схемы
/sc:implement схемы валидации для TikTok API

# 7. Сервисы
/sc:implement сервисы:
- ProfileService
- VideoService
- AnalyticsService

# 8. Celery задачи
/sc:implement задачи:
- ImportProfileTask
- ImportVideosTask
- UpdateMetricsTask
```

### Универсальный подход

```bash
# 1. Создание базового клиента
/sc:implement abstract BaseAPIClient с общей логикой

# 2. Стандартизация схем
/sc:design унифицированные схемы для соцсетей

# 3. Общие интерфейсы
/sc:implement интерфейсы:
- IProfileService
- IPostService
- ICommentService

# 4. Фабрика клиентов
/sc:implement SocialMediaClientFactory
```

---

## 📊 Обработка данных и валидация

### Pydantic схемы

```bash
# 1. Создание базовых схем
/sc:implement базовые Pydantic схемы:
- BaseProfileSchema
- BasePostSchema
- BaseCommentSchema

# 2. Расширение для конкретной соцсети
/sc:implement Instagram-специфичные поля

# 3. Валидация с бизнес-правилами
/sc:implement валидаторы:
- Username format
- URL validation
- Date ranges
- Content moderation

# 4. Трансформация данных
/sc:implement методы to_model() и from_api()
```

### Нормализация данных

```bash
# 1. Унификация форматов
/sc:implement нормализация:
- Дат и времени (UTC)
- Username форматов
- Hashtag обработка
- URL canonicalization

# 2. Дедупликация
/sc:implement логика дедупликации для импорта

# 3. Обогащение данных
/sc:implement обогащение данных:
- Геолокация
- Язык контента
- Sentiment analysis
```

---

## ⏱ Rate Limiting и устойчивость

### Стратегии rate limiting

```bash
# 1. Реализация rate limiter
/sc:implement RateLimiter с:
- Token bucket algorithm
- Sliding window
- Distributed locks (Redis)

# 2. Адаптивный backoff
/sc:implement exponential backoff с jitter

# 3. Приоритезация запросов
/sc:implement очередь с приоритетами для API

# 4. Кеширование ответов
/sc:implement кеширование частых запросов
```

### Circuit breaker pattern

```bash
# 1. Реализация circuit breaker
/sc:implement CircuitBreaker для API клиентов

# 2. Health checks
/sc:implement health checks для внешних API

# 3. Fallback механизмы
/sc:implement fallback стратегии при недоступности

# 4. Мониторинг состояния
/sc:implement метрики circuit breaker
```

---

## 🧪 Тестирование интеграций

### Unit тесты

```bash
# 1. Тесты API клиентов
/sc:test --unit все методы [API]Client

# 2. Тесты схем валидации
/sc:test Pydantic схемы с граничными случаями

# 3. Тесты handlers
/sc:test обработчики данных с моками

# 4. Тесты сервисов
/sc:test сервисы с мокированными клиентами
```

### Integration тесты

```bash
# 1. Тесты с моками API
/sc:test --integration полный flow импорта

# 2. Тесты retry логики
/sc:test поведение при ошибках API

# 3. Тесты rate limiting
/sc:test соблюдение rate limits

# 4. Тесты обработки больших данных
/sc:test импорт 10000+ записей
```

### E2E тесты

```bash
# 1. Тесты полного цикла
/sc:test --e2e от запроса до сохранения в БД

# 2. Тесты с реальным API (staging)
/sc:test --e2e --staging реальные запросы

# 3. Performance тесты
/sc:test --performance нагрузочное тестирование
```

---

## 🔍 Мониторинг и отладка

### Логирование

```bash
# 1. Структурированное логирование
/sc:implement логирование:
- Все API запросы/ответы
- Ошибки с контекстом
- Performance метрики
- Rate limit состояние

# 2. Correlation IDs
/sc:implement correlation ID для трейсинга

# 3. Sensitive data masking
/sc:implement маскирование токенов в логах

# 4. Log aggregation
/sc:implement отправка логов в ELK/Datadog
```

### Метрики и алерты

```bash
# 1. Prometheus метрики
/sc:implement метрики:
- API response time
- Error rates
- Rate limit usage
- Queue lengths

# 2. Grafana dashboards
/sc:implement дашборды для мониторинга

# 3. Алерты
/sc:implement алерты:
- API недоступен > 5 мин
- Error rate > 5%
- Rate limit > 80%
- Queue overflow

# 4. SLA мониторинг
/sc:implement SLA tracking для API
```

### Debugging tools

```bash
# 1. API request replay
/sc:implement возможность переиграть запросы

# 2. Sandbox mode
/sc:implement sandbox для тестирования

# 3. Debug endpoints
/sc:implement debug endpoints для проверки

# 4. Request/Response inspector
/sc:implement UI для просмотра API трафика
```

---

## 🔒 Security & Compliance

### Безопасность API ключей

```bash
# 1. Шифрование credentials
/sc:implement шифрование API ключей в БД

# 2. Rotation механизм
/sc:implement ротация API ключей

# 3. Vault интеграция
/sc:implement HashiCorp Vault для secrets

# 4. Audit logging
/sc:implement аудит использования ключей
```

### GDPR compliance

```bash
# 1. Data minimization
/sc:implement сбор только необходимых данных

# 2. Right to deletion
/sc:implement удаление данных пользователя

# 3. Data export
/sc:implement экспорт данных пользователя

# 4. Consent management
/sc:implement управление согласиями
```

### Security best practices

```bash
# 1. Input validation
/sc:implement валидация всех входных данных

# 2. Output encoding
/sc:implement безопасный вывод данных

# 3. Rate limiting per user
/sc:implement ограничения на пользователя

# 4. API abuse detection
/sc:implement обнаружение злоупотреблений
```

---

## 📚 Best Practices

### Команды для работы с API

```yaml
Исследование: /sc:analyze --c7 документация API
Проектирование: /sc:design --api --think-hard клиент
Реализация: /sc:implement с retry и rate limiting
Тестирование: /sc:test --integration с моками
Оптимизация: /sc:improve --performance батчинг
Мониторинг: /sc:implement метрики и алерты
```

### Персоны для API работы

```yaml
backend: Реализация клиентов и сервисов
architect: Проектирование интеграций
security: Аудит безопасности API
performance: Оптимизация запросов
qa: Тестирование интеграций
devops: Мониторинг и инфраструктура
```

### Типовые проблемы и решения

```yaml
Rate limits:
  Проблема: 429 Too Many Requests
  Решение: /sc:implement адаптивный rate limiter

Большие данные:
  Проблема: Timeout при импорте
  Решение: /sc:implement пагинация и батчинг

Нестабильное API:
  Проблема: Частые 5xx ошибки
  Решение: /sc:implement circuit breaker

Изменения в API:
  Проблема: Сломалась схема данных
  Решение: /sc:implement версионирование схем
```

### Чеклист для новой интеграции

- [ ] Изучить документацию API
- [ ] Определить rate limits
- [ ] Спроектировать модели данных
- [ ] Создать Pydantic схемы
- [ ] Реализовать API клиент
- [ ] Добавить retry логику
- [ ] Реализовать rate limiting
- [ ] Создать сервисы
- [ ] Написать Celery задачи
- [ ] Покрыть тестами
- [ ] Настроить мониторинг
- [ ] Документировать API

---

## 🚀 Quick Start примеры

### Быстрый старт с новым API

```bash
# 1. Scaffold структуры
/sc:implement --quick api-integration [name]

# 2. Базовый клиент
/sc:implement базовый API клиент с auth

# 3. Простой импорт
/sc:implement простой импорт данных

# 4. Тесты
/sc:test --quick базовая функциональность
```

### Отладка существующей интеграции

```bash
# 1. Анализ проблемы
/sc:troubleshoot API возвращает ошибки

# 2. Проверка логов
/sc:analyze логи последних запросов

# 3. Тестирование исправления
/sc:test конкретный сценарий

# 4. Мониторинг после фикса
/sc:implement временные метрики
```

---

*Последнее обновление: 2025-01-14 | SuperClaude v3.0 | Social Media API Integration Guide*