# Детальный анализ проекта SocialManager для Augment Agent

**Дата создания**: 2025-01-30  
**Версия**: 1.0  
**Автор**: Augment Agent

## Обзор проекта

SocialManager - это комплексное Django-приложение для управления интеграциями с социальными сетями (Telegram и Instagram). Проект построен на современных принципах архитектуры с использованием Repository Pattern, Service Layer и асинхронной обработки задач.

## Архитектурные принципы

### 1. Разделение ответственности
- **Models**: Только определение структуры данных, без бизнес-логики
- **Repositories**: Инкапсуляция доступа к данным
- **Services**: Бизнес-логика и оркестрация
- **Tasks**: Асинхронная обработка через Celery
- **Schemas**: Валидация внешних данных через Pydantic

### 2. Базовые классы (core/)
```python
# Модели
TimestampedModel - created_at, updated_at для всех моделей
SoftDeleteModel - мягкое удаление с is_deleted, deleted_at
SocialMediaUser - базовый класс для пользователей соцсетей
SocialMediaPost - базовый класс для постов
SocialMediaComment - базовый класс для комментариев
SocialMediaMedia - базовый класс для медиафайлов

# Репозитории
BaseRepository - CRUD операции, bulk операции, транзакции
BulkOperationsMixin - массовые операции
CachingMixin - кеширование запросов
SoftDeleteMixin - работа с мягким удалением

# Сервисы
BaseService - базовая бизнес-логика с валидацией
MediaService - работа с медиафайлами

# Задачи
BaseTask - базовый класс для Celery с прогрессом и retry
```

## Instagram Manager

### Модели данных
```python
InstagramProfile(SocialMediaUser, SoftDeleteModel):
    - profile_id: уникальный ID Instagram
    - is_business: бизнес-аккаунт
    - profile_pic_hd_url: аватар высокого разрешения
    - external_url: внешняя ссылка
    - category_name: категория бизнеса
    - last_scraped_at: последний импорт

InstagramPost(SocialMediaPost):
    - post_id: уникальный ID поста
    - post_type: photo/video/carousel/reel
    - caption: описание поста
    - hashtags: связь M2M с хештегами
    - location: геолокация
    - is_sponsored: реклама
    - accessibility_caption: описание для слабовидящих

InstagramMedia(SocialMediaMedia):
    - media_type: image/video
    - media_url: оригинальный URL
    - local_file: локальный файл
    - gcs_url: URL в Google Cloud Storage
    - width/height: размеры
    - duration: длительность видео

InstagramComment(SocialMediaComment):
    - comment_id: уникальный ID
    - parent_comment: иерархия ответов
    - is_pinned: закрепленный комментарий
    - like_count: количество лайков

InstagramHashtag:
    - name: название хештега
    - post_count: количество постов
    - is_trending: популярный хештег

InstagramFollower:
    - profile: профиль
    - follower: подписчик
    - followed_at: дата подписки
```

### API интеграция (BrightData)
```python
BrightDataClient:
    - Rate limiting: 100 req/min
    - Exponential backoff при ошибках
    - Кеширование результатов
    - Pydantic валидация ответов
    
Handlers:
    - ProfileDataHandler: обработка профилей
    - PostDataHandler: обработка постов
    - CommentDataHandler: обработка комментариев
    - MediaDataHandler: обработка медиафайлов
```

### Celery задачи
```python
ImportInstagramProfileTask - импорт профиля
ImportInstagramPostsTask - импорт постов профиля
ImportInstagramCommentsTask - импорт комментариев
ImportBatchPostsTask - массовый импорт постов
DownloadMediaTask - загрузка медиафайлов
ProcessMediaTask - обработка медиафайлов
```

## Telegram Manager

### Модели данных
```python
TelegramChat:
    - chat_id: уникальный ID чата (BigInteger)
    - title: название чата/канала
    - username: публичное имя
    - creator: создатель чата
    - left: покинул чат
    - broadcast: канал (не группа)
    - megagroup: супергруппа
    - participants_count: количество участников

TelegramUser:
    - user_id: уникальный ID пользователя (BigInteger)
    - username: имя пользователя
    - first_name/last_name: имя и фамилия
    - phone: номер телефона (зашифрован)
    - is_bot: бот
    - is_verified: верифицированный
    - is_premium: премиум аккаунт
    - last_seen_date: последняя активность

TelegramMessage:
    - message_id: ID сообщения в чате
    - chat: связь с чатом
    - from_user: отправитель
    - date: дата отправки
    - text: текст сообщения
    - media_type: тип медиа
    - is_forward: пересланное сообщение
    - reply_to: ответ на сообщение
    - views: количество просмотров
    - media_file: загруженный медиафайл
```

### API интеграция (Telethon)
```python
TelegramAPI:
    - Прямая интеграция с Telegram API
    - Аутентификация через API_ID, API_HASH
    - Управление сессиями
    - Обработка flood limits
    - Адаптивные задержки между запросами
```

### Celery задачи
```python
ImportTelegramChatsTask - импорт чатов
ImportTelegramUsersTask - импорт пользователей
ImportTelegramMessagesTask - импорт сообщений
```

## Система логирования

### Компоненты
```python
ContextLogger:
    - Контекстно-зависимое логирование
    - Автоматическое добавление request_id, user_id, task_id
    - Интеграция со structlog
    - Фабричные методы для разных контекстов

SentryHandler:
    - Отправка ошибок в Sentry
    - Обогащение контекста
    - Маппинг уровней логирования
    - Поддержка breadcrumbs

SensitiveDataFilter:
    - Фильтрация паролей, токенов, API ключей
    - 20+ встроенных паттернов
    - Рекурсивная очистка структур данных
    - Кеширование для производительности

RateLimitFilter:
    - Предотвращение дублирования сообщений
    - Thread-safe реализация
    - MD5 хеширование для дедупликации
    - Автоматическая очистка старых записей

LoggingContextMiddleware:
    - Автоматическая генерация request_id
    - Добавление контекста пользователя
    - Отслеживание времени выполнения
    - Очистка контекста после запроса
```

## Хранение медиафайлов

### Google Cloud Storage интеграция
```python
Структура папок:
    - images/ - фотографии
    - videos/ - видеофайлы
    - previews/ - превью видео

Возможности:
    - Автоматическая организация файлов
    - Валидация типов файлов
    - Fallback на локальное хранение
    - Асинхронная загрузка через Celery
```

## Валидация данных

### Pydantic схемы
```python
Instagram:
    - InstagramProfileSchema
    - InstagramPostSchema
    - InstagramCommentSchema
    - InstagramMediaSchema

Telegram:
    - TelegramChatSchema
    - TelegramUserSchema
    - TelegramMessageSchema

Валидация:
    - Все внешние данные проходят через Pydantic
    - Автоматическое преобразование типов
    - Детальные сообщения об ошибках
```

## Конфигурация и окружения

### Настройки Django
```python
base.py - общие настройки
local.py - локальная разработка
production.py - продакшн
test.py - тестирование
sentry.py - настройки Sentry по окружениям
```

### Переменные окружения
```bash
# Обязательные
SECRET_KEY, DEBUG, ALLOWED_HOSTS
DATABASE_URL
API_ID, API_HASH (Telegram)
BRIGHTDATA_API_KEY (Instagram)
FIELD_ENCRYPTION_KEY

# Опциональные
REDIS_URL, CELERY_BROKER_URL
GCS_BUCKET_NAME, GOOGLE_APPLICATION_CREDENTIALS
SENTRY_DSN, SENTRY_ENVIRONMENT
```

## Тестирование

### Структура тестов
```python
tests/
├── core/ - тесты базовых компонентов
├── instagram_manager/ - тесты Instagram функциональности
├── telegram_manager/ - тесты Telegram функциональности
├── integration/ - интеграционные тесты
├── performance/ - тесты производительности
└── e2e/ - end-to-end тесты

Инструменты:
    - pytest - основной фреймворк
    - factory-boy - фабрики тестовых данных
    - pytest-django - Django интеграция
    - pytest-cov - покрытие кода
    - freezegun - мокирование времени
```

## Мониторинг и метрики

### Ключевые метрики
- API response time < 200ms
- Celery task completion rate > 95%
- Database query time < 50ms
- Cache hit rate > 80%

### Sentry интеграция
- Автоматическая отправка ошибок
- Performance мониторинг
- Breadcrumbs для отладки
- Environment-specific настройки

## Известные ограничения

1. **BrightData API**: 100 req/min - требует очередь и батчинг
2. **Telegram flood limits**: адаптивные задержки между запросами
3. **Большие медиафайлы**: асинхронная загрузка через Celery
4. **PostgreSQL connections**: connection pooling обязателен
5. **Memory usage**: большие импорты требуют пагинации

## Celery интеграция

### Конфигурация
```python
# SocialManager/celery.py
- Автоматическое обнаружение задач
- Redis как брокер и result backend
- Настройки надежности (acks_late, reject_on_worker_lost)
- Интеграция с Django settings

# Настройки в local.py
CELERY_BROKER_URL = "redis://localhost:6379/0"
CELERY_RESULT_BACKEND = "redis://localhost:6379/1"
CELERY_TASK_ACKS_LATE = True
CELERY_TASK_REJECT_ON_WORKER_LOST = True
```

### BaseTask возможности
```python
BaseTask:
    - Отслеживание прогресса через update_progress()
    - Retry логика с экспоненциальным backoff
    - Сохранение результатов в TaskResult модель
    - WebSocket/SSE поддержка для real-time updates
    - Контекстное логирование с task_id
    - Мониторинг производительности
    - Кеширование результатов
```

### Instagram задачи
```python
ImportInstagramProfileTask:
    - Импорт профиля через BrightData API
    - Опциональный deep scan (посты + подписчики)
    - Обработка приватных профилей
    - Кеширование результатов (300 сек)

ImportInstagramPostsTask:
    - Импорт постов профиля
    - Пагинация для больших объемов
    - Фильтрация по типам постов
    - Автоматическая загрузка медиа

ImportBatchPostsTask:
    - Массовый импорт постов
    - Batch обработка для оптимизации
    - Параллельная обработка
    - Отчеты о прогрессе

DownloadMediaTask:
    - Асинхронная загрузка медиафайлов
    - Поддержка GCS и локального хранения
    - Валидация типов файлов
    - Retry при ошибках сети
```

### Telegram задачи
```python
ImportTelegramChatsTask:
    - Импорт чатов и каналов
    - Обработка flood limits
    - Сохранение метаданных чатов

ImportTelegramUsersTask:
    - Импорт пользователей из чатов
    - Шифрование чувствительных данных
    - Обновление существующих записей

ImportTelegramMessagesTask:
    - Импорт сообщений с медиа
    - Поддержка пересланных сообщений
    - Обработка ответов и цепочек
```

### Celery signal handlers
```python
setup_task_logging_context:
    - Автоматическая инъекция task_id в контекст
    - Добавление метаданных задачи
    - Логирование старта задачи

cleanup_task_logging_context:
    - Очистка контекста после выполнения
    - Логирование завершения задачи
    - Обработка ошибок и retry
```

## API интеграции

### BrightData API (Instagram)
```python
BrightDataClient:
    - Rate limiting: 100 req/min
    - Exponential backoff при ошибках
    - Кеширование ответов API
    - Pydantic валидация всех ответов
    - Специализированные клиенты (Profile, Post, Comment, Hashtag)

Обработка ошибок:
    - ProfileNotFoundError
    - PrivateProfileError
    - RateLimitError
    - SnapshotNotReadyError
    - ValidationError

Rate Limiter:
    - Отслеживание лимитов по endpoint
    - Автоматические задержки
    - Статистика использования API

Cache Manager:
    - Кеширование по типу данных
    - TTL для разных типов контента
    - Инвалидация кеша
```

### Telethon API (Telegram)
```python
TelegramAPIClient:
    - Прямая интеграция с Telegram API
    - Управление сессиями
    - Обработка flood limits
    - Адаптивные задержки между запросами
    - Поддержка 2FA аутентификации

Обработка ошибок:
    - FloodWaitError с автоматическими задержками
    - SessionPasswordNeededError для 2FA
    - AuthenticationError
    - NetworkError с retry логикой
```

### Pydantic валидация
```python
Instagram схемы:
    - InstagramProfileResponse
    - InstagramPostResponse
    - InstagramCommentResponse
    - InstagramMediaResponse
    - MediaItem

Telegram схемы:
    - TelegramChatSchema
    - TelegramUserSchema
    - TelegramMessageSchema

Валидаторы:
    - CommonValidators (timestamp, URL, text)
    - InstagramValidators (shortcode, username)
    - ValidationHelperMixin для общих операций
```

## Тестирование

### Структура тестов (690+ тестов)
```python
tests/
├── unit/ - модульные тесты (< 100ms)
├── integration/ - интеграционные тесты (< 1s)
├── e2e/ - end-to-end тесты с MCP клиентом
├── performance/ - тесты производительности
├── core/ - тесты базовых компонентов
├── instagram_manager/ - тесты Instagram функциональности
├── telegram_manager/ - тесты Telegram функциональности
└── mcp_server/ - тесты MCP сервера
```

### Инструменты тестирования
```python
pytest - основной фреймворк
factory-boy - фабрики тестовых данных
pytest-django - Django интеграция
pytest-asyncio - поддержка async тестов
pytest-cov - покрытие кода
pytest-xdist - параллельное выполнение
pytest-mock - мокирование
freezegun - мокирование времени
```

### Фикстуры и утилиты
```python
# Основные фикстуры
async_db - асинхронная база данных
mcp_client - MCP тестовый клиент
mock_brightdata - мок BrightData API
mock_celery - мок Celery задач
performance_monitor - мониторинг производительности

# Утилиты
MockDataGenerator - генерация тестовых данных
TestDataBuilder - построение сложных сценариев
assert_valid_response - проверка ответов API
```

### Покрытие тестами
- Общее покрытие: > 80%
- Unit тесты: > 90%
- Integration тесты: > 70%
- Критические пути: 100%
- Система логирования: 94.42%

## MCP Server интеграция

### Назначение
Model Context Protocol сервер для интеграции с AI ассистентами и внешними системами.

### Структура MCP сервера
```python
mcp_server/
├── main.py - основной сервер
├── tools/ - инструменты для AI
├── database/ - интеграция с БД
├── monitoring/ - мониторинг и метрики
├── validators/ - валидация запросов
└── utils/ - утилиты
```

### Доступные инструменты
```python
Instagram tools:
- instagram_get_profile - получение профиля
- instagram_get_posts - получение постов
- instagram_get_comments - получение комментариев
- instagram_import_profile - импорт профиля
- instagram_roast_profile - анализ профиля

Telegram tools:
- telegram_get_chats - получение чатов
- telegram_get_messages - получение сообщений
- telegram_import_chat - импорт чата

Task management:
- task_create_import - создание задачи импорта
- task_get_status - статус задачи
- task_get_results - результаты задачи
```

## Планы развития

### Краткосрочные
- Автоматическая модерация комментариев
- Webhook интеграции
- Улучшение производительности импорта

### Долгосрочные
- Поддержка TikTok
- ML для анализа контента
- Real-time уведомления
- Микросервисная архитектура
