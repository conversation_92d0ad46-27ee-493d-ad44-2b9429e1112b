# Пример: Планирование системы автоматической модерации комментариев (SuperClaude v3)

**Дата**: 2025-01-14 (обновлено для v3)  
**Автор**: SocialManager Team  
**Версия**: 2.0  
**Статус**: Example for SuperClaude v3

## Описание процесса

Это реальный пример использования SuperClaude для планирования функционала автоматической модерации Instagram комментариев. Показаны все шаги процесса с командами и результатами.

## Шаг 0: Подготовка и загрузка контекста

### Создание описания функционала

Сначала создали файл `/docs/superclaude/features/moderation_feature.md`:

```markdown
# Функционал: Автоматическая модерация комментариев Instagram

## Бизнес-требования
- Автоматически скрывать спам и негативные комментарии
- Настраиваемые правила модерации для каждого профиля  
- Уведомления о заблокированных комментариях
- Статистика модерации
- Human review для спорных случаев

## Технические требования
- ML-модель для sentiment analysis
- Проверка на спам-слова
- Real-time обработка через webhooks
- Batch обработка существующих
- GDPR compliance
```

### Загрузка контекста в v3

> **Примечание**: В SuperClaude v3 нет явной команды `/load`. Контекст передается через:
> - Включение файлов при вызове команды
> - Использование MCP сервера Context7 для документации
> - Прямое указание путей к файлам

**Подход v3**: Файлы будут переданы непосредственно в командах анализа.

## Шаг 1: Исследование и анализ

### Команда

```bash
/sc:analyze --think-hard --persona=architect,backend "Исследовать реализацию автоматической модерации Instagram комментариев для SocialManager: 
1) ML-модели для sentiment analysis на русском и английском языках
2) Интеграция с существующей архитектурой (InstagramComment, CommentRepository, CommentService)
3) BrightData API возможности для real-time получения комментариев
4) Оценка нагрузки - у нас ~10K комментариев в день
5) Хранение гибких правил модерации"

# Включаем нужные файлы для контекста
# files: docs/superclaude/project_context.md, docs/superclaude/features/moderation_feature.md
```

### Результат анализа

**ML-модели**:
- Рекомендована: `cardiffnlp/twitter-xlm-roberta-base-sentiment` - мультиязычная, включая русский
- Альтернатива: `blanchefort/rubert-base-cased-sentiment` - специально для русского
- Производительность: ~50ms на комментарий на CPU, ~10ms на GPU

**API возможности**:
- BrightData поддерживает webhooks для новых комментариев
- Endpoint: `POST /webhook/comments/new`
- Rate limit не применяется к входящим webhook
- Batch endpoint для исторических данных: 1000 комментариев за запрос

**Архитектурные находки**:
- Можем расширить существующий `InstagramComment` полем `moderation_status`
- `CommentRepository` уже поддерживает bulk операции
- Нужен отдельный сервис для изоляции ML-логики

**Оценка нагрузки**:
- 10K комментариев/день = ~7 комментариев/минуту
- Пиковая нагрузка: до 50 комментариев/минуту
- Необходим Redis для кеширования ML-результатов

## Шаг 2: Архитектурное проектирование

### Команда

```bash
/sc:design --persona=architect,backend --think-hard --seq "Спроектировать архитектуру модерации на основе анализа:
1) Модель ModerationRule с JSONB для гибких правил (spam keywords, sentiment thresholds, user blacklists)
2) ModerationAction для аудита всех действий
3) ModerationService координирующий ML и rule-based логику
4) MLAnalysisService изолирующий работу с ML моделями
5) Celery задачи: ModerateCommentsTask для batch, webhook handler для realtime
6) Кеширование в Redis с TTL 24 часа
7) Интеграция с существующими Comment компонентами"
```

### Результат проектирования

**Архитектура компонентов**:

```
┌─────────────────┐     ┌──────────────────┐     ┌─────────────────┐
│ Webhook Handler │────▶│ ModerationService │────▶│ MLAnalysisService│
└─────────────────┘     └──────────────────┘     └─────────────────┘
                               │                           │
                               ▼                           ▼
                        ┌──────────────┐           ┌─────────────┐
                        │ RuleEngine   │           │ Redis Cache │
                        └──────────────┘           └─────────────┘
                               │
                               ▼
                        ┌──────────────────┐
                        │ ModerationAction │
                        └──────────────────┘
```

**Новые модели**:

```python
class ModerationRule(TimestampedModel):
    profile = models.ForeignKey(InstagramProfile, on_delete=models.CASCADE)
    name = models.CharField(max_length=100)
    rule_type = models.CharField(max_length=50, choices=[
        ('spam_keywords', 'Spam Keywords'),
        ('sentiment_threshold', 'Sentiment Threshold'),
        ('user_blacklist', 'User Blacklist'),
        ('regex_pattern', 'Regex Pattern'),
    ])
    configuration = models.JSONField()  # Гибкая структура для разных типов
    is_active = models.BooleanField(default=True)
    priority = models.IntegerField(default=0)
    action = models.CharField(max_length=20, choices=[
        ('hide', 'Hide Comment'),
        ('delete', 'Delete Comment'),
        ('flag_review', 'Flag for Review'),
    ])

class ModerationAction(TimestampedModel):
    comment = models.ForeignKey(InstagramComment, on_delete=models.CASCADE)
    rule = models.ForeignKey(ModerationRule, null=True, on_delete=models.SET_NULL)
    action_type = models.CharField(max_length=20)
    ml_sentiment = models.JSONField(null=True)  # {"label": "negative", "score": 0.95}
    confidence_score = models.FloatField()
    reason = models.TextField()
    reviewed_by = models.ForeignKey(User, null=True, blank=True)
    review_decision = models.CharField(max_length=20, null=True)
```

**Сервисы**:

```python
class MLAnalysisService:
    def __init__(self):
        self.model = self._load_model()
        self.redis_client = get_redis_connection()
    
    def analyze_sentiment(self, text: str, language: str = 'auto') -> SentimentResult:
        # Кеширование по хешу текста
        cache_key = f"sentiment:{hashlib.md5(text.encode()).hexdigest()}"
        
    def detect_spam_probability(self, text: str) -> float:
        # Эвристики + ML

class ModerationService:
    def moderate_comment(self, comment: InstagramComment) -> Optional[ModerationAction]:
        # 1. Проверка ML sentiment
        # 2. Применение правил
        # 3. Принятие решения
        # 4. Логирование действия
```

## Шаг 3: Анализ рисков

### Команда

```bash
/sc:analyze --focus security --persona=security,devops --think-hard "Проанализировать риски автомодерации:
1) False positives - легитимные комментарии блокируются
2) GDPR - автоматические решения о пользователях
3) Производительность ML модели при пиковых нагрузках
4) Adversarial attacks - обход модерации специальными символами
5) Доступность ML сервиса
6) Целостность данных при сбоях"
```

### Результат анализа рисков

| Риск | Вероятность | Влияние | Митигация |
|------|-------------|---------|-----------|
| False positives | Высокая | Высокое | • Настраиваемый confidence threshold (default 0.8)<br>• Human review queue<br>• Whitelist доверенных пользователей<br>• A/B тестирование правил |
| GDPR нарушения | Средняя | Критичное | • Полный аудит лог<br>• Право на обжалование в течение 30 дней<br>• Объяснение решения<br>• Возможность отключить автомодерацию |
| ML производительность | Средняя | Среднее | • Горизонтальное масштабирование<br>• Кеширование результатов<br>• Fallback на rule-based<br>• Timeout 100ms |
| Обход модерации | Низкая | Среднее | • Нормализация текста<br>• Регулярные обновления модели<br>• Мониторинг аномалий<br>• Комбинирование подходов |

**План действий при инцидентах**:

1. **Всплеск false positives** (> 5%):
   - Автоматически повысить threshold до 0.9
   - Алерт модераторам
   - Включить human review для всех

2. **ML сервис недоступен**:
   - Переключение на rule-based only
   - Использование кеша последние 48ч
   - Постановка в очередь для позднейшей обработки

## Шаг 4: Декомпозиция задач

### Команда

```bash
/sc:task --detailed --persona=architect "Декомпозировать реализацию модерации на задачи:
- Размер задачи 4-8 часов
- Указать зависимости
- Определить критический путь
- Теги: backend, ml, devops, qa
- Группировка по фазам реализации"
```

### Результат декомпозиции

**Критический путь**: 
```
MOD-005 (ML выбор) → MOD-006 (ML сервис) → MOD-010 (ModerationService) → MOD-018 (Тесты)
```

**Задачи по фазам**:

#### Фаза 1: Инфраструктура (14 story points)
```
MOD-001: Создать модели ModerationRule и ModerationAction
  - Оценка: 6 часов
  - Теги: backend, database
  - Описание: Модели, миграции, индексы

MOD-002: Админка для управления правилами
  - Оценка: 4 часа  
  - Теги: backend, admin
  - Зависит от: MOD-001

MOD-003: ModerationRepository с CRUD и bulk операциями
  - Оценка: 6 часов
  - Теги: backend
  - Зависит от: MOD-001

MOD-004: Фабрики и fixtures для тестирования
  - Оценка: 4 часа
  - Теги: backend, qa
  - Зависит от: MOD-001
```

#### Фаза 2: ML интеграция (30 story points)
```
MOD-005: Research и выбор ML модели [КРИТИЧЕСКИЙ ПУТЬ]
  - Оценка: 8 часов
  - Теги: ml, research
  - Описание: Сравнить 3-4 модели, бенчмарки

MOD-006: Реализовать MLAnalysisService [КРИТИЧЕСКИЙ ПУТЬ]
  - Оценка: 8 часов
  - Теги: ml, backend
  - Зависит от: MOD-005

MOD-007: Redis кеширование для ML результатов
  - Оценка: 4 часа
  - Теги: backend, devops
  - Зависит от: MOD-006

MOD-008: Performance тесты ML модели
  - Оценка: 6 часов
  - Теги: ml, qa, performance
  - Зависит от: MOD-006

MOD-009: Fallback механизм при недоступности ML
  - Оценка: 4 часа
  - Теги: backend, reliability
  - Зависит от: MOD-006
```

## Шаг 5: Оценка времени

### Команда

```bash
/sc:estimate --detailed --ultrathink "Оценить время реализации модерации учитывая:
- Junior backend разработчик в команде
- Нет опыта работы с ML в production
- Возможны изменения требований от бизнеса
- Необходимость A/B тестирования перед полным запуском
- Зависимость от доступности ML эксперта (50% времени)"
```

### Результат оценки

**Сводка по сценариям**:

| Сценарий | Длительность | Вероятность | Обоснование |
|----------|--------------|-------------|-------------|
| Оптимистичный | 3 недели | 20% | Всё идет по плану, ML работает сразу |
| Реалистичный | 5 недель | 60% | Типичные проблемы с ML, небольшие правки |
| Пессимистичный | 8 недель | 20% | Серьезные проблемы с ML, изменение требований |

**Факторы риска для сроков**:

1. **ML модель** (+1-2 недели):
   - Может потребоваться fine-tuning
   - Проблемы с русским языком
   - Производительность на CPU

2. **Изменение требований** (+1-2 недели):
   - Вероятность 40%
   - Обычно после первого демо

3. **A/B тестирование** (+1 неделя):
   - Сбор метрик
   - Анализ результатов
   - Корректировки

**Ресурсное планирование**:

```
Неделя 1-2: Инфраструктура
  - Backend dev: 100%
  - ML expert: 20%

Неделя 2-4: ML интеграция  
  - Backend dev: 50%
  - ML expert: 80%
  - DevOps: 20%

Неделя 4-5: Бизнес-логика и API
  - Backend dev: 100%
  - QA: 30%

Неделя 5-6: Тестирование и оптимизация
  - Backend dev: 50%
  - QA: 100%
  - DevOps: 30%
```

## Финальная документация

### Команда

```bash
/sc:document --type technical --persona=scribe --think "Создать финальную документацию планирования модерации включая:
- Executive summary для менеджмента
- Технический план для команды
- Риски и план митигации
- Метрики успеха
- Timeline с milestones"
```

### Результат

Создан полный документ планирования (см. `/docs/planning/moderation_planning_v1.md`) включающий:

1. **Executive Summary**:
   - Снижение спама на 80%
   - ROI через 2 месяца
   - 5 недель разработки

2. **Технические решения**:
   - ML + rule-based подход
   - Асинхронная архитектура
   - Human-in-the-loop для качества

3. **Ключевые метрики**:
   - Accuracy > 95%
   - False positive < 2%
   - Response time < 100ms

4. **Milestones**:
   - Неделя 2: MVP с rule-based
   - Неделя 4: ML интеграция
   - Неделя 5: Beta тестирование
   - Неделя 6: Production

## Новые возможности v3 для планирования

### Дополнительные команды для проекта

```bash
# Реализация после планирования
/sc:implement --plan модели для модерации
/sc:implement celery-task ModerateCommentsTask
/sc:implement --with-tests ModerationService

# Тестирование
/sc:test --generate для всех компонентов модерации
/sc:test --integration с моками ML сервиса
/sc:test --e2e --playwright админка модерации

# Оптимизация
/sc:improve --performance ML кеширование
/sc:analyze --performance производительность модерации
```

### Использование MCP серверов

```bash
# Context7 для изучения ML библиотек
/sc:analyze --c7 "transformers библиотека для sentiment analysis"

# Sequential для сложного анализа
/sc:troubleshoot --seq "почему ML модель дает разные результаты"

# Playwright для UI тестов
/sc:test --playwright "тест модерации в админке"
```

## Уроки и выводы для v3

### Что улучшилось в v3

1. **Префикс /sc:** - четкое разделение команд SuperClaude
2. **11 персон** - добавлены `devops` и `scribe` для полноты
3. **MCP интеграция** - доступ к документации и инструментам
4. **Playwright** - современное E2E тестирование

### Рекомендации для v3

- Используйте **--seq** для сложного многошагового анализа
- Применяйте **--c7** для изучения документации библиотек
- Комбинируйте **персоны** в одной команде
- Используйте **--playwright** для тестирования UI
- Не забывайте про **--uc** для больших кодовых баз

### Workflow для Django проектов

1. **Анализ**: `/sc:analyze --think-hard --persona=architect,backend`
2. **Проектирование**: `/sc:design --seq --ddd`
3. **Риски**: `/sc:analyze --focus security --persona=security`
4. **Планирование**: `/sc:task --detailed`
5. **Оценка**: `/sc:estimate --ultrathink`
6. **Реализация**: `/sc:implement --with-tests`
7. **Документация**: `/sc:document --type technical --persona=scribe`

---

*Обновлено для SuperClaude v3. Используйте новый синтаксис и возможности для более эффективного планирования.*