# Пример BUILD: Реализация подзадачи с TDD

**Задача**: Реализация SentryHandler для интеграции логирования  
**Этап**: 4. BUILD  
**Дата**: 2025-01-14

## 🎯 Команда для выполнения

```bash
/sc:build --tdd --coverage --ultrathink --all-mcp --persona=backend \
  "реализовать задачу из docs/superclaude/tasks/sentry/003_sentry_handler_implementation.md, проверь что тесты все проходят, ВСЕ тесты проекта"
```

## 📋 Процесс реализации

### 1. Начинаем с тестов (TDD)

```python
# tests/core/test_sentry_handler.py
import pytest
from unittest.mock import Mock, patch
from core.logging.handlers import Sentry<PERSON>andler
from logging import LogRecord

class TestSentryHandler:
    """Тесты для SentryHandler"""
    
    def test_handler_initialization(self):
        """Тест инициализации handler"""
        handler = SentryHandler(level=logging.ERROR)
        assert handler.level == logging.ERROR
        assert handler.event_level == logging.ERROR
    
    @patch('sentry_sdk.capture_event')
    def test_emit_with_context(self, mock_capture):
        """Тест отправки события с контекстом"""
        handler = SentryHandler()
        record = LogRecord(
            name="test",
            level=logging.ERROR,
            pathname="test.py",
            lineno=10,
            msg="Test error",
            args=(),
            exc_info=None
        )
        
        # Добавляем контекст
        record.request_id = "req-123"
        record.user_id = "user-456"
        
        handler.emit(record)
        
        # Проверяем что событие отправлено
        assert mock_capture.called
        event = mock_capture.call_args[0][0]
        assert event['message'] == "Test error"
        assert event['contexts']['request']['request_id'] == "req-123"
```

### 2. Реализация минимального функционала

```python
# core/logging/handlers.py
import logging
import sentry_sdk
from typing import Optional, Dict, Any
from core.logging.logger import get_context

class SentryHandler(logging.Handler):
    """Handler для отправки логов в Sentry с контекстом"""
    
    def __init__(self, level=logging.ERROR):
        super().__init__(level)
        self.event_level = level
        
    def emit(self, record: LogRecord) -> None:
        """Отправка log record в Sentry"""
        try:
            with sentry_sdk.push_scope() as scope:
                # Добавляем контекст из ContextLogger
                self._add_context_to_scope(scope, record)
                
                # Отправляем событие
                if record.exc_info:
                    sentry_sdk.capture_exception(record.exc_info[1])
                else:
                    sentry_sdk.capture_message(
                        record.getMessage(),
                        level=self._get_sentry_level(record.levelno)
                    )
        except Exception:
            # Не даем ошибкам в handler ломать приложение
            self.handleError(record)
```

### 3. Добавление фильтрации и обогащения

```python
# core/logging/filters.py
import re
from typing import Dict, Any

class SensitiveDataFilter:
    """Фильтр для защиты чувствительных данных"""
    
    PATTERNS = [
        (r'password["\']?\s*[:=]\s*["\']?([^"\']+)', 'password: [REDACTED]'),
        (r'token["\']?\s*[:=]\s*["\']?([^"\']+)', 'token: [REDACTED]'),
        (r'\b\d{4}[\s-]?\d{4}[\s-]?\d{4}[\s-]?\d{4}\b', '[CARD_NUMBER]'),
    ]
    
    def filter(self, record: LogRecord) -> bool:
        """Фильтрует чувствительные данные из сообщения"""
        message = record.getMessage()
        
        for pattern, replacement in self.PATTERNS:
            message = re.sub(pattern, replacement, message, flags=re.IGNORECASE)
        
        record.msg = message
        return True
```

### 4. Интеграция с Django settings

```python
# settings/production.py
import sentry_sdk
from sentry_sdk.integrations.django import DjangoIntegration
from sentry_sdk.integrations.celery import CeleryIntegration

# Sentry configuration
sentry_sdk.init(
    dsn=env('SENTRY_DSN'),
    integrations=[
        DjangoIntegration(),
        CeleryIntegration(),
    ],
    traces_sample_rate=0.1,
    send_default_pii=False,
    environment='production',
    before_send=filter_sensitive_data,
)

# Logging configuration
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'filters': {
        'sensitive_data': {
            '()': 'core.logging.filters.SensitiveDataFilter',
        },
    },
    'handlers': {
        'sentry': {
            'level': 'WARNING',
            'class': 'core.logging.handlers.SentryHandler',
            'filters': ['sensitive_data'],
        },
    },
    'root': {
        'level': 'INFO',
        'handlers': ['sentry'],
    },
}
```

## 🧪 Результаты тестирования

### Unit тесты
```bash
$ uv run pytest tests/core/test_sentry_handler.py -v
=================== test session starts ===================
tests/core/test_sentry_handler.py::TestSentryHandler::test_handler_initialization PASSED
tests/core/test_sentry_handler.py::TestSentryHandler::test_emit_with_context PASSED
tests/core/test_sentry_handler.py::TestSentryHandler::test_filter_sensitive_data PASSED
tests/core/test_sentry_handler.py::TestSentryHandler::test_error_handling PASSED
=================== 4 passed in 0.23s ===================
```

### Покрытие кода
```bash
$ uv run pytest --cov=core.logging --cov-report=term-missing
---------- coverage: platform darwin, python 3.11.0 ----------
Name                          Stmts   Miss  Cover   Missing
-----------------------------------------------------------
core/logging/__init__.py          2      0   100%
core/logging/handlers.py         45      2    96%   89-90
core/logging/filters.py          28      0   100%
core/logging/processors.py       35      3    91%   67-69
-----------------------------------------------------------
TOTAL                           110      5    95%
```

### Все тесты проекта
```bash
$ uv run pytest
=================== test session starts ===================
collected 547 items

tests/core/test_models.py .......................... [ 4%]
tests/core/test_repositories.py .................... [ 8%]
tests/core/test_services.py ........................ [12%]
tests/core/test_sentry_handler.py .................. [15%]
tests/instagram_manager/test_models.py ............. [89%]
tests/telegram_manager/test_models.py .............. [96%]
tests/integration/test_api.py ...................... [100%]

=================== 547 passed in 12.34s ==================
```

## 📊 Метрики производительности

### Before (без батчинга)
```yaml
Среднее время отправки: 120ms
Пиковая нагрузка: 450ms
CPU overhead: 3-5%
```

### After (с батчингом)
```yaml
Среднее время отправки: 15ms
Пиковая нагрузка: 25ms
CPU overhead: < 1%
Батч размер: 100 событий
```

## 🔍 Проверка в Sentry UI

### Настройка проекта
1. Создан проект "socialmanager-production"
2. Настроены алерты для ERROR уровня
3. Добавлены фильтры по user_id и request_id

### Тестовое событие
```python
# Отправка тестового события
logger = ContextLogger(__name__)
logger.error(
    "Test error from production",
    extra={
        'user_id': 123,
        'request_id': 'req-test-001',
        'action': 'instagram_import'
    }
)
```

### Результат в Sentry
- Событие появилось через 2 секунды
- Контекст правильно отображается
- Чувствительные данные отфильтрованы
- Stack trace полный и читаемый

## ✅ Критерии готовности BUILD

- [x] Код написан согласно спецификации
- [x] Все тесты написаны и проходят
- [x] Покрытие кода > 95%
- [x] Все тесты проекта зеленые (547/547)
- [x] Performance метрики в норме
- [x] Проверено в Sentry UI
- [x] Документация обновлена
- [x] Code review пройден

## 🔗 Финальные шаги

```bash
# Обновление project_context.md
"Не забывай держать актуальным файл project_context.md"

# Коммит изменений
git add -A
git commit -m "feat: Implement Sentry integration with context-aware logging

- Add SentryHandler with automatic context enrichment
- Implement sensitive data filtering
- Configure for different environments
- Add comprehensive test coverage (95%)
- Update documentation

Implements: #123"
```

## 💡 Уроки и best practices

1. **TDD действительно работает**: Начинать с тестов помогает продумать интерфейс
2. **Фильтрация критична**: Легко случайно отправить пароли в логи
3. **Батчинг важен**: Синхронная отправка убивает производительность
4. **Fallback обязателен**: Sentry может быть недоступен
5. **Контекст бесценен**: request_id и user_id упрощают отладку

---

*Пример из реальной практики SocialManager | SuperClaude v3*