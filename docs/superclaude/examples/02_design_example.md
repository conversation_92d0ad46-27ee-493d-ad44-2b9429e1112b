# Пример DESIGN: Проектирование интеграции с Sentry

**Задача**: Проектирование системы логирования с Sentry  
**Этап**: 2. DESIGN  
**Дата**: 2025-01-14

## 🎯 Команда для выполнения

```bash
/sc:design --ultrathink --all-mcp --persona=Architect,System_Thinker \
  "спроектировать интеграцию Sentry на основе анализа из /docs/superclaude/features/sentry/logs_realisation_plan.md, код не трогаем только документы делаем"
```

## 📐 Архитектурное решение

### Общая архитектура
```mermaid
graph TB
    A[<PERSON><PERSON><PERSON> App] --> B[ContextLogger]
    B --> C[LoggingMiddleware]
    C --> D[Handlers]
    D --> E[FileHandler<br/>dev/staging]
    D --> F[SentryHandler<br/>production]
    F --> G[Filters]
    G --> H[Processors]
    H --> I[Sentry SDK]
    
    style F fill:#f9f,stroke:#333,stroke-width:4px
    style I fill:#bbf,stroke:#333,stroke-width:4px
```

### Компоненты системы

#### 1. SentryHandler
```python
# core/logging/handlers.py
class SentryHandler(logging.Handler):
    """
    Отправляет логи в Sentry с контекстом
    - Автоматическое обогащение request_id, user_id, task_id
    - Батчинг для производительности
    - Fallback на файловое логирование
    """
```

#### 2. SensitiveDataFilter
```python
# core/logging/filters.py
class SensitiveDataFilter:
    """
    Фильтрует чувствительные данные
    - Пароли, токены, ключи
    - Персональные данные пользователей
    - Финансовая информация
    """
```

#### 3. ContextProcessor
```python
# core/logging/processors.py
class ContextProcessor:
    """
    Обогащает логи контекстом
    - Request context (path, method, headers)
    - User context (id, username, roles)
    - Task context (task_id, progress, worker)
    """
```

## 🔧 Конфигурация по окружениям

### Production (settings/production.py)
```python
LOGGING = {
    'handlers': {
        'sentry': {
            'class': 'core.logging.handlers.SentryHandler',
            'level': 'WARNING',
            'filters': ['sensitive_data'],
            'processors': ['context_enrichment'],
        }
    },
    'root': {
        'handlers': ['sentry'],
        'level': 'INFO',
    }
}

# Sentry SDK настройки
SENTRY_DSN = env('SENTRY_DSN')
SENTRY_ENVIRONMENT = 'production'
SENTRY_TRACES_SAMPLE_RATE = 0.1
```

### Staging (settings/staging.py)
```python
# Гибридный подход: Sentry + файлы
LOGGING['handlers']['sentry']['level'] = 'ERROR'
LOGGING['handlers']['file']['level'] = 'DEBUG'
```

### Development (settings/local.py)
```python
# Только файловое логирование
# Sentry отключен для экономии квоты
SENTRY_DSN = None
```

## 📊 Поэтапная реализация

### Фаза 1: Инфраструктура (2 часа)
1. Создание базовых классов
2. Настройка тестового окружения
3. Подготовка конфигурации

### Фаза 2: Унификация (2 часа)
1. Миграция на ContextLogger
2. Активация middleware
3. Тестирование контекста

### Фаза 3: Интеграция (2 часа)
1. Настройка Sentry SDK
2. Реализация handlers
3. Настройка фильтров

### Фаза 4: Валидация (1.5 часа)
1. Unit и integration тесты
2. Проверка в staging
3. Мониторинг в Sentry UI

## 🛡️ Безопасность и производительность

### Защита данных
```yaml
Фильтруемые поля:
  - password, token, secret, key
  - credit_card, ssn, passport
  - email (частично маскируется)
  
Методы защиты:
  - Regex patterns для поиска
  - Белый список разрешенных полей
  - Хеширование для корреляции
```

### Оптимизация производительности
```yaml
Батчинг:
  - Буфер на 100 сообщений
  - Отправка каждые 5 секунд
  - Async worker для отправки
  
Сэмплирование:
  - 100% для ERROR и выше
  - 10% для WARNING
  - 1% для INFO в production
```

## 🧪 Стратегия тестирования

### Unit тесты
- SentryHandler отправка событий
- Фильтрация чувствительных данных
- Обогащение контекстом

### Integration тесты
- Работа с Django middleware
- Celery task контекст
- Fallback механизмы

### E2E тесты
- Проверка событий в Sentry UI
- Performance monitoring
- Алерты и уведомления

## ✅ Критерии готовности дизайна

- [x] Спроектирована архитектура компонентов
- [x] Определены классы и интерфейсы
- [x] Настроена конфигурация по окружениям
- [x] Разработан план поэтапной реализации
- [x] Учтены безопасность и производительность
- [x] Подготовлена стратегия тестирования

## 🔗 Следующий шаг

```bash
/sc:task --spawn --persona=Architect,ProjectManager,Planner --ultrathink \
  "разбить реализацию на подзадачи в docs/superclaude/tasks/sentry/ и документы пронумеруй для последовательности"
```

## 📁 Результаты проектирования

Создан план реализации:
- `/docs/superclaude/features/sentry/logs_realisation_plan.md`

Ключевые решения:
1. Использовать native Sentry SDK integration
2. Chain of Responsibility для обработки логов
3. Разные стратегии для разных окружений
4. Фокус на безопасности и производительности

---

*Пример из реальной практики SocialManager | SuperClaude v3*