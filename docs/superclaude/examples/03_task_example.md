# Пример TASK: Декомпозиция задачи на подзадачи

**Задача**: Разбиение интеграции Sentry на управляемые подзадачи  
**Этап**: 3. TASK  
**Дата**: 2025-01-14

## 🎯 Команда для выполнения

```bash
/sc:task --spawn --persona=Architect,ProjectManager,Planner --ultrathink \
  "разбить на подзадачи имплементацию из docs/superclaude/features/sentry/logs_realisation_plan.md, и сохранить в папку docs/superclaude/tasks/sentry/ и документы пронумеруй для последовательности"
```

## 📋 Результат декомпозиции

### Созданная структура
```
docs/superclaude/tasks/sentry/
├── 001_sentry_infrastructure_setup.md
├── 002_context_logger_migration.md
├── 003_sentry_handler_implementation.md
├── 004_filters_and_processors.md
├── 005_environment_configuration.md
├── 006_testing_and_validation.md
├── 007_staging_deployment.md
├── 008_production_rollout.md
└── 009_monitoring_setup.md
```

## 📄 Пример подзадачи: 001_sentry_infrastructure_setup.md

```markdown
# Задача 001: Настройка инфраструктуры Sentry

## Описание
Создание базовой инфраструктуры для интеграции Sentry в проект SocialManager.

## Зависимости
- [ ] Доступ к Sentry аккаунту
- [ ] Права на изменение settings
- [ ] Установлен sentry-sdk

## Шаги реализации

### 1. Установка зависимостей
\`\`\`bash
uv add sentry-sdk[django]
\`\`\`

### 2. Создание модулей
\`\`\`
core/logging/
├── handlers.py      # Новый файл
├── filters.py       # Новый файл
├── processors.py    # Новый файл
└── testing.py       # Новый файл
\`\`\`

### 3. Базовая конфигурация
- Добавить SENTRY_DSN в .env
- Настроить базовые параметры в settings/base.py
- Подготовить override для production.py

## Критерии готовности
- [ ] Установлен sentry-sdk
- [ ] Созданы все необходимые модули
- [ ] Базовая конфигурация добавлена
- [ ] Тесты для новых модулей написаны
- [ ] Документация обновлена

## Технические детали
- Версия sentry-sdk: latest stable
- Совместимость с Django 4.2+
- Поддержка async views

## Оценка времени
1-2 часа

## Риски
- Конфликт версий с существующими пакетами
- Увеличение размера зависимостей
```

## 📊 Матрица подзадач

| № | Задача | Критичность | Время | Зависимости |
|---|--------|-------------|-------|-------------|
| 001 | Infrastructure Setup | Высокая | 2ч | - |
| 002 | ContextLogger Migration | Высокая | 2ч | 001 |
| 003 | SentryHandler Implementation | Высокая | 3ч | 001 |
| 004 | Filters & Processors | Средняя | 2ч | 003 |
| 005 | Environment Configuration | Высокая | 1ч | 003 |
| 006 | Testing & Validation | Высокая | 3ч | 001-005 |
| 007 | Staging Deployment | Средняя | 1ч | 006 |
| 008 | Production Rollout | Критичная | 2ч | 007 |
| 009 | Monitoring Setup | Средняя | 1ч | 008 |

## 🔄 Последовательность выполнения

```mermaid
graph LR
    A[001 Infrastructure] --> B[002 Migration]
    A --> C[003 Handler]
    C --> D[004 Filters]
    C --> E[005 Config]
    B --> F[006 Testing]
    D --> F
    E --> F
    F --> G[007 Staging]
    G --> H[008 Production]
    H --> I[009 Monitoring]
    
    style A fill:#f9f,stroke:#333,stroke-width:4px
    style H fill:#f99,stroke:#333,stroke-width:4px
```

## 📝 Шаблон для каждой подзадачи

```yaml
Структура документа:
  - Описание: Что делаем и зачем
  - Зависимости: Что должно быть готово
  - Шаги: Конкретные действия
  - Критерии: Определение "готово"
  - Детали: Технические нюансы
  - Время: Реалистичная оценка
  - Риски: Что может пойти не так
```

## 🎯 Принципы декомпозиции

1. **Атомарность**: Каждая задача делает одно дело
2. **Независимость**: Минимум пересечений между задачами
3. **Тестируемость**: Результат можно проверить
4. **Временные рамки**: 1-4 часа на задачу
5. **Приоритеты**: Критичные задачи выделены

## ✅ Критерии готовности декомпозиции

- [x] Все этапы из плана покрыты задачами
- [x] Задачи пронумерованы для последовательности
- [x] Зависимости четко определены
- [x] Время оценено реалистично
- [x] Риски идентифицированы
- [x] Критерии готовности конкретны

## 🔗 Следующий шаг

```bash
/sc:build --tdd --coverage --ultrathink --all-mcp --persona=backend \
  "реализовать задачу из docs/superclaude/tasks/sentry/001_sentry_infrastructure_setup.md"
```

## 💡 Советы по декомпозиции

- **Нумерация с запасом**: 010, 020 для вставки задач
- **Критичные в конце**: 900+ для важных финальных шагов
- **Параллельные задачи**: Одинаковый префикс (021, 022)
- **Rollback задачи**: Всегда планировать откат (999)

---

*Пример из реальной практики SocialManager | SuperClaude v3*