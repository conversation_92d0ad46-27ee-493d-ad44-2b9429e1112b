# Полный пример 4-этапного workflow

**Задача**: Добавление сохранения медиафайлов в Google Cloud Storage  
**Проект**: SocialManager  
**Дата**: 2025-01-14

## 🎯 Обзор задачи

Необходимо реализовать сохранение медиафайлов из Instagram и Telegram в Google Cloud Storage вместо локального хранилища для улучшения масштабируемости и надежности.

---

## 📊 ЭТАП 1: ANALYZE

### Команда
```bash
/sc:analyze --ultrathink --persona=Researcher,Architect \
  "изучить текущую реализацию MediaHandler, существующие модели InstagramMedia, и документацию в /docs/superclaude/features/save_media_to_gcs.md"
```

### Результаты анализа
```yaml
Текущее состояние:
  - MediaHandler сохраняет файлы локально в MEDIA_ROOT
  - InstagramMedia и TelegramMedia хранят относительные пути
  - Нет разделения по типам медиа (фото/видео)
  - Отсутствует версионирование файлов
  
Требования GCS:
  - Структура: bucket/images/, bucket/videos/, bucket/previews/
  - Сохранение метаданных (размер, тип, дата)
  - Поддержка больших файлов (до 5GB для видео)
  - Публичные URL для отображения
  
Риски:
  - Совместимость с существующими данными
  - Увеличение времени загрузки
  - Стоимость хранения и трафика
```

### Консультация через Zen MCP
```bash
"проконсультироваться через zen mcp с Gemini Pro 2.5 о best practices для GCS и o3 о оптимальной архитектуре"
```

---

## 🏗️ ЭТАП 2: DESIGN

### Команда
```bash
/sc:design --ultrathink --all-mcp --persona=Architect,System_Thinker \
  "спроектировать интеграцию GCS с учетом обратной совместимости, код не трогаем только документы делаем"
```

### Архитектурное решение
```mermaid
graph TB
    A[MediaHandler] --> B{Storage Strategy}
    B -->|Local Dev| C[LocalStorage]
    B -->|Production| D[GCSStorage]
    D --> E[GCS Bucket]
    D --> F[CDN]
    
    G[InstagramMedia] --> A
    H[TelegramMedia] --> A
    
    style D fill:#f9f,stroke:#333,stroke-width:4px
```

### Ключевые компоненты
```python
# core/storage/backends.py
class GCSMediaStorage:
    """Google Cloud Storage backend для медиафайлов"""
    
# core/storage/handlers.py  
class MediaHandler:
    """Унифицированный handler с поддержкой разных backends"""
    
# instagram_manager/services/media_service.py
class MediaService:
    """Сервис для работы с медиа через MediaHandler"""
```

---

## 📋 ЭТАП 3: TASK

### Команда
```bash
/sc:task --spawn --persona=Architect,ProjectManager,Planner --ultrathink \
  "разбить на подзадачи реализацию GCS интеграции в /docs/superclaude/tasks/gcs_media/"
```

### Созданные подзадачи
```
docs/superclaude/tasks/gcs_media/
├── 001_gcs_infrastructure_setup.md
├── 002_storage_backend_implementation.md  
├── 003_mediahandler_refactoring.md
├── 004_migration_existing_media.md
├── 005_testing_and_validation.md
└── 006_deployment_and_monitoring.md
```

---

## 🔨 ЭТАП 4: BUILD (пример одной подзадачи)

### Команда
```bash
/sc:build --tdd --coverage --ultrathink --all-mcp --persona=backend \
  "реализовать 002_storage_backend_implementation.md, проверь что ВСЕ тесты проходят"
```

### Реализация GCSMediaStorage

#### Тесты первыми (TDD)
```python
# tests/core/test_gcs_storage.py
class TestGCSMediaStorage:
    @pytest.fixture
    def storage(self):
        return GCSMediaStorage(bucket_name='test-bucket')
    
    def test_save_image(self, storage, mock_gcs_client):
        """Тест сохранения изображения"""
        file_content = b"fake image data"
        filename = storage.save("test.jpg", ContentFile(file_content))
        
        assert filename.startswith("images/")
        assert mock_gcs_client.upload_blob.called
    
    def test_generate_public_url(self, storage):
        """Тест генерации публичного URL"""
        url = storage.url("images/2024/01/test.jpg")
        assert url.startswith("https://storage.googleapis.com/")
```

#### Реализация
```python
# core/storage/backends.py
from google.cloud import storage
from django.core.files.storage import Storage
from typing import Optional
import mimetypes

class GCSMediaStorage(Storage):
    """Google Cloud Storage backend для медиафайлов"""
    
    def __init__(self, bucket_name: Optional[str] = None):
        self.bucket_name = bucket_name or settings.GCS_BUCKET_NAME
        self._client = None
        self._bucket = None
    
    @property
    def client(self):
        if self._client is None:
            self._client = storage.Client()
        return self._client
    
    @property 
    def bucket(self):
        if self._bucket is None:
            self._bucket = self.client.bucket(self.bucket_name)
        return self._bucket
    
    def _get_folder_for_file(self, name: str) -> str:
        """Определяет папку на основе типа файла"""
        content_type, _ = mimetypes.guess_type(name)
        
        if content_type:
            if content_type.startswith('image/'):
                return 'images'
            elif content_type.startswith('video/'):
                return 'videos'
        
        return 'misc'
    
    def save(self, name, content, max_length=None):
        """Сохраняет файл в GCS"""
        # Определяем папку
        folder = self._get_folder_for_file(name)
        
        # Генерируем уникальное имя
        name = self.get_available_name(name, max_length)
        full_path = f"{folder}/{name}"
        
        # Загружаем в GCS
        blob = self.bucket.blob(full_path)
        blob.upload_from_file(content, content_type=content.content_type)
        
        # Делаем публичным для CDN
        blob.make_public()
        
        return full_path
    
    def url(self, name):
        """Возвращает публичный URL"""
        return f"https://storage.googleapis.com/{self.bucket_name}/{name}"
```

### Проверка всех тестов
```bash
$ uv run pytest
=================== test session starts ===================
collected 563 items

tests/core/test_gcs_storage.py .................... [ 3%]
tests/core/test_models.py .......................... [ 8%]
[... все остальные тесты ...]

=================== 563 passed in 14.21s ==================
```

---

## 📊 Итоговые метрики

### Время выполнения
- ANALYZE: 45 минут
- DESIGN: 1.5 часа  
- TASK: 30 минут
- BUILD: 6 часов (все подзадачи)
- **Итого**: 8 часов 15 минут

### Результаты
- ✅ Реализована интеграция с GCS
- ✅ Сохранена обратная совместимость
- ✅ Покрытие тестами 94%
- ✅ Все 563 теста проходят
- ✅ Документация обновлена

### Производительность
- Среднее время загрузки: 450ms → 380ms (CDN)
- Надежность: 99.9% → 99.99% (GCS SLA)
- Масштабируемость: Практически неограниченная

---

## 💡 Выводы и уроки

1. **4-этапный процесс работает**: Структурированный подход экономит время
2. **Консультации через Zen MCP полезны**: Помогли избежать типичных ошибок
3. **TDD оправдывает себя**: Нашли 3 edge case при написании тестов
4. **Документация первична**: План помог не отклониться от курса

---

## 🔗 Полезные команды из процесса

```bash
# Быстрая проверка что ничего не сломали
/sc:test --quick критические тесты

# Профилирование после изменений
/sc:analyze --performance загрузка медиа

# Обновление документации
"Не забывай держать актуальным файл project_context.md"
```

---

*Полный пример workflow | SuperClaude v3 | SocialManager*