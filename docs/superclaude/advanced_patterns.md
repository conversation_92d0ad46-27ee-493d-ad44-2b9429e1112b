# SuperClaude v3 Advanced Patterns - Продвинутые техники

**Версия**: 1.0  
**Дата**: 2025-01-14  
**Уровень**: Expert

## 🎯 Обзор

Документ описывает продвинутые техники и паттерны использования SuperClaude v3, основанные на реальном опыте работы с комплексными задачами в проекте SocialManager.

---

## 🧠 Режимы мышления (Thinking Modes)

### Стратегия выбора режима

```yaml
--think: "Простые задачи (2-5 файлов, ~4K токенов)"
  Когда использовать:
  - Анализ одного сервиса или модуля
  - Простой рефакторинг метода
  - Добавление нового поля в модель
  Примеры:
  - /sc:analyze --think CommentService
  - /sc:improve --think оптимизация метода

--think-hard: "Средние задачи (5-15 файлов, ~10K токенов)"
  Когда использовать:
  - Проектирование нового feature
  - Анализ целого Django app
  - Интеграция с внешним API
  Примеры:
  - /sc:design --think-hard система уведомлений
  - /sc:analyze --think-hard instagram_manager/

--ultrathink: "Сложные задачи (15+ файлов, ~32K токенов)"
  Когда использовать:
  - Архитектурные решения
  - Редизайн подсистем
  - Комплексные интеграции
  - Критические изменения
  Примеры:
  - /sc:design --ultrathink миграция на микросервисы
  - /sc:analyze --ultrathink переход на async Django
```

### Комбинирование с другими флагами

```bash
# Максимальная глубина анализа
/sc:analyze --ultrathink --seq --all-mcp \
  "архитектура для высоконагруженной системы"

# Безопасное проектирование
/sc:design --ultrathink --safe-mode --plan \
  "миграция критичных данных"

# Производительный анализ
/sc:analyze --ultrathink --profile --uc \
  "оптимизация всего проекта"
```

---

## 🎭 Множественные персоны

### Эффективные комбинации

```yaml
Исследование + Архитектура:
  --persona=Researcher,Architect
  Для: Глубокого анализа с проектированием
  Пример: "изучить существующие паттерны и предложить улучшения"

Архитектура + Системное мышление:
  --persona=Architect,System_Thinker  
  Для: Комплексных архитектурных решений
  Пример: "спроектировать масштабируемую систему"

Планирование комплексное:
  --persona=Architect,ProjectManager,Planner
  Для: Декомпозиции больших задач
  Пример: "разбить миграцию на управляемые этапы"

Backend + Performance:
  --persona=backend,performance
  Для: Оптимизации серверного кода
  Пример: "оптимизировать ORM запросы"

Security + Backend:
  --persona=security,backend
  Для: Безопасной реализации
  Пример: "реализовать защищенный API"
```

### Продвинутые комбинации для специальных задач

```bash
# Комплексный аудит системы
/sc:analyze --ultrathink \
  --persona=security,performance,architect \
  "полный аудит instagram_manager"

# Проектирование с учетом всех аспектов  
/sc:design --ultrathink \
  --persona=architect,devops,security,performance \
  "новая архитектура для production"

# Отладка сложных проблем
/sc:troubleshoot --seq \
  --persona=analyzer,backend,performance \
  "память утекает в Celery задачах"
```

---

## 🔌 MCP Серверы - продвинутое использование

### Context7 - работа с документацией

```bash
# Изучение best practices перед реализацией
/sc:analyze --c7 --ultrathink \
  "Django signals best practices для нашего use case"

# Сравнение подходов
/sc:analyze --c7 --persona=architect \
  "сравнить Celery vs Django-Q для нашей архитектуры"

# Поиск решений известных проблем
/sc:troubleshoot --c7 \
  "Django + Telethon event loop conflicts"
```

### Sequential - многошаговый анализ

```bash
# Глубокое исследование проблемы
/sc:troubleshoot --seq --ultrathink \
  "почему ImportPostsTask зависает после 1000 записей"

# Пошаговое проектирование
/sc:design --seq --ultrathink \
  "миграция с синхронного на async Django"

# Анализ цепочки вызовов
/sc:analyze --seq --performance \
  "trace полного flow импорта из Instagram"
```

### Комбинирование всех MCP серверов

```bash
# Максимальные возможности для критичных задач
/sc:design --ultrathink --all-mcp \
  --persona=architect,system_thinker \
  "редизайн архитектуры для 10x нагрузки"

# Комплексная отладка с всеми инструментами
/sc:troubleshoot --all-mcp --seq \
  "race condition в multi-tenant окружении"
```

### Консультации через Zen MCP

```bash
# Консультация с конкретными моделями
/sc:analyze --ultrathink \
  "проконсультироваться через zen mcp с моделями:
   - Gemini Pro 2.5 по архитектуре
   - o3 по алгоритмической сложности"

# Валидация решения разными моделями
/sc:design --ultrathink \
  "спроектировать систему и проверить через zen mcp 
   с разными моделями для консенсуса"
```

---

## 💾 Оптимизация токенов

### UltraCompressed Mode

```yaml
Автоматическая активация:
  - При >75% использования контекста
  - При анализе больших кодовых баз
  - При долгих сессиях

Ручная активация:
  --uc или --ultracompressed
  
Преимущества:
  - ~70% экономия токенов
  - Быстрые ответы
  - Больше кода в контексте
  
Когда использовать:
  - Анализ всего Django проекта
  - Долгие сессии рефакторинга
  - Работа с большими Celery задачами
```

### Эффективные паттерны

```bash
# Анализ большого проекта
/sc:analyze --uc --ultrathink \
  "архитектура всего проекта SocialManager"

# Долгая сессия рефакторинга
/sc:improve --refactor --uc \
  "рефакторинг всех сервисов в instagram_manager"

# Комплексная документация
/sc:document --uc --comprehensive \
  "полная документация проекта"
```

---

## 📋 Работа с документацией

### Принцип "Код не трогаем"

```bash
# При планировании - только документы
/sc:design --ultrathink \
  "план в /docs/superclaude/features/new_feature.md, 
   код не трогаем только документы делаем"

# При анализе для документации
/sc:analyze --docs-only \
  "изучить код и создать документацию, 
   сам код не меняем"
```

### Структурированное документирование

```bash
# Создание иерархии документов
/sc:task --spawn --numbered \
  "создать структуру документации:
   - /docs/superclaude/features/ - описания
   - /docs/superclaude/tasks/ - подзадачи  
   - /docs/superclaude/examples/ - примеры"

# Поддержка актуальности
/sc:build [любая задача] && \
  "обязательно обновить project_context.md"
```

---

## 🔄 Паттерны декомпозиции задач

### Иерархическая декомпозиция

```bash
# Уровень 1: Feature
/sc:design --ultrathink \
  "feature в /docs/superclaude/features/feature.md"

# Уровень 2: Подзадачи
/sc:task --spawn --numbered \
  "разбить на подзадачи в /tasks/feature/"

# Уровень 3: Конкретные шаги
/sc:task --detailed \
  "разбить каждую подзадачу на atomic steps"
```

### Нумерация для последовательности

```yaml
Паттерн нумерации:
  001_infrastructure_setup.md
  002_database_migration.md
  003_service_implementation.md
  004_api_endpoints.md
  005_tests_and_validation.md
  
Преимущества:
  - Четкий порядок выполнения
  - Легко отслеживать прогресс
  - Зависимости очевидны
```

---

## 🚀 Продвинутые workflow

### Параллельная разработка

```bash
# Анализ независимых компонентов параллельно
/sc:analyze --parallel \
  "instagram_manager & telegram_manager"

# Проектирование связанных систем
/sc:design --ultrathink --seq \
  "общая система модерации для всех соцсетей"

# Реализация независимых задач
/sc:build task_001.md & \
/sc:build task_002.md & \
/sc:build task_003.md
```

### Итеративное улучшение

```bash
# Цикл: Analyze → Improve → Test
while not optimal:
  /sc:analyze --performance текущая реализация
  /sc:improve --performance узкие места  
  /sc:test --performance --benchmark улучшения
```

### Безопасные изменения production

```bash
# Максимальная безопасность
/sc:implement --safe-mode --plan --dry-run \
  "критичное изменение"

# Поэтапный rollout
/sc:design --staged --safe \
  "план поэтапного внедрения"

# С откатом
/sc:implement --with-rollback \
  "изменение с планом отката"
```

---

## 📊 Метрики и мониторинг

### Отслеживание прогресса

```bash
# Для больших задач
/sc:task --progress --metrics \
  "отслеживать выполнение всех подзадач"

# Оценка сложности
/sc:estimate --detailed --complexity \
  "оценить трудозатраты на feature"
```

### Performance profiling

```bash
# Детальное профилирование
/sc:analyze --profile --detailed \
  "найти bottlenecks в системе"

# С визуализацией
/sc:analyze --profile --visualize \
  "создать flame graph для анализа"
```

---

## 🎯 Best Practices из опыта

### Всегда начинать с ultrathink для:
- Архитектурных решений
- Интеграций с внешними системами  
- Изменений в production
- Работы с sensitive данными

### Использовать множественные персоны для:
- Комплексного анализа
- Проектирования с разных точек зрения
- Планирования с учетом всех аспектов

### Документировать перед кодированием:
- План всегда в документах
- Код только после одобрения
- project_context.md всегда актуален

### Консультироваться через zen mcp для:
- Валидации архитектурных решений
- Получения альтернативных подходов
- Проверки best practices

---

## 🔧 Troubleshooting сложных проблем

### Систематический подход

```bash
# 1. Сбор контекста
/sc:analyze --comprehensive --logs \
  "все что связано с проблемой"

# 2. Формулировка гипотез
/sc:troubleshoot --seq --hypotheses \
  "возможные причины проблемы"

# 3. Проверка каждой гипотезы
/sc:investigate --systematic \
  "проверить каждую гипотезу"

# 4. Root cause analysis
/sc:analyze --root-cause --seq \
  "найти первопричину"
```

### Отладка race conditions

```bash
# Специальный режим для concurrency
/sc:troubleshoot --concurrency --trace \
  "race condition в Celery задачах"

# Анализ locks и транзакций
/sc:analyze --locks --transactions \
  "deadlock в Django ORM"
```

---

*Advanced Patterns v1.0 | SuperClaude v3 | Expert Level*