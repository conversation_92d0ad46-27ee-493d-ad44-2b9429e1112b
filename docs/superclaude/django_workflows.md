# SuperClaude v3 Django Workflows для SocialManager

**Версия**: 1.0  
**Дата создания**: 2025-01-14  
**Проект**: SocialManager

## 🎯 Обзор

Специализированные workflow для эффективной Django разработки с использованием SuperClaude v3. Каждый workflow оптимизирован под архитектуру SocialManager с Repository Pattern, Service Layer и асинхронными задачами.

---

## 📋 Содержание

1. [Django App Development](#django-app-development)
2. [Repository Pattern Implementation](#repository-pattern)
3. [Service Layer Development](#service-layer)
4. [Celery Task Creation](#celery-tasks)
5. [API Development](#api-development)
6. [Testing Workflows](#testing-workflows)
7. [Performance Optimization](#performance-optimization)
8. [Database Operations](#database-operations)
9. [Security Implementation](#security-implementation)
10. [Deployment Workflows](#deployment-workflows)

---

## 🚀 Django App Development

### Создание нового Django App (полный цикл с реальными командами)

```bash
# 1. ANALYZE - Глубокий анализ существующей архитектуры
/sc:analyze --think-hard --persona=Researcher,Architect \
  "существующие Django apps, их взаимосвязи и архитектурные паттерны"

# 2. DESIGN - Проектирование с учетом DDD и паттернов проекта
/sc:design --ultrathink --persona=Architect,System_Thinker \
  "новый app для [функционал] с Repository Pattern, Service Layer, учитывая project_context.md"

# 3. TASK - Разбиение на подзадачи для поэтапной реализации
/sc:task --spawn --persona=Architect,ProjectManager,Planner --ultrathink \
  "создать подзадачи для реализации [app_name] в /docs/superclaude/tasks/[app_name]/"

# 4. BUILD - Реализация структуры app
/sc:build --tdd --coverage --ultrathink --all-mcp --persona=backend \
  "django-app [app_name] с полной структурой: models, repositories, services, admin, tests"

# 5. Реализация моделей с наследованием от базовых
/sc:build --tdd --ultrathink --persona=backend \
  "модели для [app_name] наследуясь от TimestampedModel, SoftDeleteModel"

# 6. Автоматическая генерация репозиториев
/sc:build --tdd --persona=backend \
  "репозитории для всех моделей [app_name] по образцу BaseRepository"

# 7. Создание сервисного слоя с Pydantic валидацией
/sc:build --tdd --coverage --persona=backend \
  "service layer для [app_name] с Pydantic схемами и обработкой ошибок"

# 8. Настройка админки с кастомизацией
/sc:build --persona=frontend,backend \
  "django-admin для [app_name] с bulk actions, фильтрами, inline редактированием"

# 9. Комплексное тестирование
/sc:test --generate --coverage --persona=qa \
  "полное покрытие тестами [app_name]/ включая models, repositories, services"
```

### Реальный пример из практики

```bash
# Создание app для интеграции с Sentry
/sc:analyze --ultrathink --persona=Researcher,Architect \
  "изучить документы в /docs/superclaude/features/sentry/ и проконсультироваться через zen mcp"

/sc:design --ultrathink --all-mcp --persona=Architect,System_Thinker \
  "спроектировать sentry_integration app учитывая logs_realisation_plan.md"

/sc:task --spawn --persona=Architect,ProjectManager,Planner --ultrathink \
  "разбить на подзадачи имплементацию в docs/superclaude/tasks/sentry/"

/sc:build --tdd --coverage --ultrathink --all-mcp --persona=backend \
  "реализовать задачу 001_sentry_infrastructure_setup.md"
```

### Интеграция в существующий проект

```bash
# 1. Обновление settings.py
/sc:implement добавить [app_name] в INSTALLED_APPS

# 2. Создание URL routing
/sc:implement urls.py для [app_name]

# 3. Интеграция с основным urls.py
/sc:improve добавить [app_name] URLs в основной роутинг

# 4. Создание миграций
# В терминале: python manage.py makemigrations [app_name]

# 5. Документирование
/sc:document README для [app_name]
```

---

## 🗄 Repository Pattern Implementation

### Создание нового репозитория (полный workflow)

```bash
# 1. ANALYZE - Исследование модели и связей
/sc:analyze --think-hard --persona=Backend,Architect \
  "модель [ModelName], ее связи, частые запросы и оптимизации"

# 2. DESIGN - Проектирование интерфейса репозитория
/sc:design --ultrathink --persona=Backend \
  "репозиторий для [ModelName] с учетом специфики запросов в проекте"

# 3. BUILD - Реализация базовых методов
/sc:build --tdd --coverage --ultrathink --persona=backend \
  "repository для [ModelName] с методами:
  - get_by_id() с обработкой DoesNotExist
  - get_list() с пагинацией и фильтрацией
  - create() с валидацией через Pydantic
  - update() с partial updates
  - soft_delete() для SoftDeleteModel
  - bulk_create() с batch_size
  - get_or_create() атомарно"

# 4. Добавление оптимизированных методов
/sc:build --tdd --persona=backend,performance \
  "в [ModelName]Repository оптимизированные методы:
  - get_active() с индексом по is_active
  - get_by_user() с select_related('user')
  - get_with_stats() с annotate для агрегации
  - get_for_export() с только нужными полями"

# 5. Комплексное тестирование
/sc:test --coverage --persona=qa \
  "[ModelName]Repository все методы с edge cases и performance тестами"
```

### Реальный пример для Instagram интеграции

```bash
# Создание репозитория для InstagramPost
/sc:analyze --ultrathink --persona=Backend,Performance \
  "модель InstagramPost, связи с Media, Comments, оптимальные запросы для импорта"

/sc:design --ultrathink --seq --persona=Backend \
  "InstagramPostRepository с учетом batch операций и rate limits"

/sc:build --tdd --coverage --ultrathink --all-mcp --persona=backend \
  "InstagramPostRepository с методами:
  - get_posts_for_profile() с prefetch media и comments
  - bulk_create_with_media() для импорта
  - get_posts_for_moderation() с фильтрами
  - update_metrics() для batch обновления"
```

### Оптимизация существующих репозиториев

```bash
# 1. Анализ производительности
/sc:analyze --performance репозитории в [app_name]

# 2. Оптимизация N+1 запросов
/sc:improve --performance добавить select_related/prefetch_related

# 3. Добавление индексов
/sc:implement db индексы для частых запросов

# 4. Кеширование
/sc:implement Redis кеширование в репозитории
```

---

## 🛠 Service Layer Development

### Создание бизнес-сервиса (4-этапный процесс)

```bash
# 1. ANALYZE - Изучение бизнес-требований
/sc:analyze --ultrathink --persona=Backend,Architect \
  "бизнес-логика для [функционал], существующие сервисы и их взаимодействие"

# 2. DESIGN - Проектирование с DDD принципами
/sc:design --ultrathink --all-mcp --persona=Architect,System_Thinker \
  "сервис [ServiceName] с четким разделением ответственности, учитывая project_context.md"

# 3. BUILD - Реализация с полным функционалом
/sc:build --tdd --coverage --ultrathink --all-mcp --persona=backend \
  "service [ServiceName] включая:
  - Pydantic схемы для входных/выходных данных
  - Dependency injection для репозиториев
  - @transaction.atomic для критичных операций
  - Структурированное логирование с контекстом
  - Кастомные исключения с обработкой
  - Интеграцию с внешними API через клиенты"

# 4. Добавление бизнес-методов
/sc:build --tdd --ultrathink --persona=backend \
  "бизнес-методы в [ServiceName]:
  - process_[entity]() с полной валидацией
  - validate_[business_rule]() с детальными ошибками
  - calculate_[metric]() с кешированием результатов
  - bulk_process() для пакетной обработки"
```

### Реальный пример: Сервис модерации комментариев

```bash
# Исследование существующей логики
/sc:analyze --ultrathink --persona=Backend,Architect \
  "логика модерации в Instagram и Telegram, паттерны фильтрации"

# Проектирование универсального сервиса
/sc:design --ultrathink --all-mcp --persona=Architect,System_Thinker \
  "ModerationService для Instagram и Telegram с ML интеграцией"

# Реализация с полным покрытием
/sc:build --tdd --coverage --ultrathink --all-mcp --persona=backend \
  "ModerationService с методами:
  - moderate_comment() с ML scoring
  - bulk_moderate() для импорта
  - get_moderation_stats() с агрегацией
  - train_model() для обновления ML"
```

### Рефакторинг логики из views в services

```bash
# 1. Анализ текущей реализации
/sc:analyze views.py найти бизнес-логику

# 2. Выделение в сервис
/sc:improve --refactor вынести логику из [ViewName] в сервис

# 3. Обновление view
/sc:implement обновить [ViewName] использовать сервис

# 4. Тестирование
/sc:test интеграционные тесты для рефакторинга
```

---

## ⚡ Celery Task Creation

### Создание асинхронной задачи (полный workflow из практики)

```bash
# 1. ANALYZE - Исследование требований к асинхронности
/sc:analyze --ultrathink --persona=Backend,Performance \
  "требования к [операция]: объем данных, время выполнения, retry стратегия"

# 2. DESIGN - Проектирование с учетом масштабирования
/sc:design --ultrathink --seq --persona=Architect,Backend \
  "celery-task для [операция] с progress tracking и error recovery"

# 3. TASK - Разбиение на этапы реализации
/sc:task --spawn --persona=ProjectManager,Backend --ultrathink \
  "подзадачи для реализации [TaskName] в /docs/superclaude/tasks/celery/"

# 4. BUILD - Реализация с полным функционалом
/sc:build --tdd --coverage --ultrathink --all-mcp --persona=backend \
  "celery-task [TaskName] наследуя BaseTask с:
  - execute() с разбиением на chunks
  - update_progress() каждые N записей
  - handle_error() с отправкой в Sentry
  - retry с exponential backoff
  - результат в TaskResult модель"

# 5. Добавление мониторинга и management command
/sc:build --tdd --persona=backend,devops \
  "для [TaskName]:
  - Структурированные логи с task_id
  - Метрики в Prometheus формате
  - Management command с параметрами
  - Webhook уведомления о завершении"
```

### Реальный пример: Задача импорта из Sentry

```bash
# Анализ объемов и требований
/sc:analyze --ultrathink --persona=Backend,Performance \
  "импорт данных из Sentry API: rate limits, объемы, стратегия обработки"

# Проектирование с учетом ограничений API
/sc:design --ultrathink --all-mcp --persona=Architect,Backend \
  "ImportSentryDataTask с адаптивным rate limiting и checkpoint recovery"

# Реализация пошагово
/sc:build --tdd --coverage --ultrathink --all-mcp --persona=backend \
  "реализовать docs/superclaude/tasks/sentry/010_task_creation_management.md"

# Проверка и оптимизация
/sc:test --performance --persona=qa,performance \
  "ImportSentryDataTask с большими объемами данных"
```

### Оптимизация существующих задач

```bash
# 1. Анализ производительности
/sc:analyze --performance Celery задачи в [app_name]

# 2. Добавление батчинга
/sc:improve добавить batch обработку в [TaskName]

# 3. Оптимизация памяти
/sc:improve --memory оптимизировать [TaskName] для больших данных

# 4. Параллелизация
/sc:implement разделить [TaskName] на подзадачи
```

---

## 🌐 API Development

### Создание REST API endpoints

```bash
# 1. Проектирование API
/sc:design --api --openapi endpoints для [ресурс]

# 2. Создание serializers
/sc:implement DRF serializers для [Model]:
- Валидация полей
- Nested relationships
- Custom fields
- Different serializers для CRUD

# 3. Реализация ViewSets
/sc:implement DRF viewset для [Model]:
- Фильтрация
- Поиск
- Пагинация
- Permissions
- Throttling

# 4. Кастомные actions
/sc:implement в [Model]ViewSet:
- @action для bulk операций
- @action для статистики
- @action для экспорта

# 5. API документация
/sc:document --openapi спецификация для [app_name]
```

### Версионирование API

```bash
# 1. Создание новой версии
/sc:implement API v2 для [app_name]

# 2. Миграция endpoints
/sc:implement обратная совместимость v1 -> v2

# 3. Deprecation warnings
/sc:implement deprecation headers для v1
```

---

## 🧪 Testing Workflows

### Unit тестирование

```bash
# 1. Генерация тестов для моделей
/sc:test --generate модели в [app_name]

# 2. Тесты для репозиториев
/sc:test --unit все методы [Repository]

# 3. Тесты для сервисов
/sc:test --unit [Service] с моками зависимостей

# 4. Тесты для Celery задач
/sc:test --unit [Task] с CELERY_TASK_ALWAYS_EAGER=True
```

### Integration тестирование

```bash
# 1. API endpoint тесты
/sc:test --integration все endpoints в [app_name]

# 2. Тесты с внешними API
/sc:test --integration с моками BrightData API

# 3. Database transaction тесты
/sc:test транзакции в [Service]

# 4. Celery workflow тесты
/sc:test --integration полный цикл [Task]
```

### E2E тестирование

```bash
# 1. Тесты админки
/sc:test --e2e --playwright Django админка для [Model]

# 2. User flow тесты
/sc:test --e2e --playwright сценарий [пользовательский путь]

# 3. Performance тесты
/sc:test --performance endpoints под нагрузкой
```

---

## ⚡ Performance Optimization

### Оптимизация запросов

```bash
# 1. Профилирование
/sc:analyze --profile --performance медленные endpoints

# 2. Анализ SQL запросов
/sc:analyze SQL запросы в [view/service]

# 3. Оптимизация ORM
/sc:improve --performance:
- Добавить select_related
- Добавить prefetch_related
- Использовать only/defer
- Добавить db_index

# 4. Внедрение кеширования
/sc:implement кеширование:
- Redis для частых запросов
- Cachalot для ORM
- HTTP кеширование headers
```

### Оптимизация Celery

```bash
# 1. Анализ очередей
/sc:analyze Celery очереди и воркеры

# 2. Настройка приоритетов
/sc:implement priority queues для задач

# 3. Оптимизация batch размеров
/sc:improve оптимальный batch_size для импорта

# 4. Мониторинг
/sc:implement Celery мониторинг с Flower
```

---

## 🗃 Database Operations

### Работа с миграциями

```bash
# 1. Анализ изменений
/sc:analyze изменения моделей перед миграцией

# 2. Создание data migration
/sc:implement data migration для [изменение]

# 3. Оптимизация миграций
/sc:improve объединить миграции в [app_name]

# 4. Rollback план
/sc:design rollback стратегия для миграции
```

### Работа с индексами

```bash
# 1. Анализ slow queries
/sc:analyze --performance медленные запросы без индексов

# 2. Создание индексов
/sc:implement индексы:
- B-tree для exact lookups
- GIN для JSONB полей
- Составные индексы

# 3. Мониторинг использования
/sc:analyze использование индексов
```

---

## 🔒 Security Implementation

### Защита API

```bash
# 1. Аудит безопасности
/sc:analyze --focus security API endpoints

# 2. Реализация защиты
/sc:implement:
- JWT аутентификация
- Rate limiting per user
- Input validation
- CORS настройки

# 3. Защита от атак
/sc:implement защита от:
- SQL injection (параметризованные запросы)
- XSS (шаблоны экранирование)
- CSRF (токены)

# 4. Аудит логирование
/sc:implement security audit logs
```

### Шифрование данных

```bash
# 1. Анализ sensitive данных
/sc:analyze поля требующие шифрования

# 2. Реализация шифрования
/sc:implement field encryption для:
- Пароли Instagram аккаунтов
- API токены
- Персональные данные

# 3. Key rotation
/sc:design стратегия ротации ключей
```

---

## 🚀 Deployment Workflows

### Подготовка к production

```bash
# 1. Проверка конфигурации
/sc:analyze --production готовность к деплою

# 2. Оптимизация settings
/sc:implement production settings:
- DEBUG = False
- Secure cookies
- ALLOWED_HOSTS
- Static files

# 3. Создание Docker конфигурации
/sc:implement --devops Docker setup для проекта

# 4. CI/CD pipeline
/sc:implement --devops GitHub Actions для:
- Тестирование
- Линтинг
- Сборка Docker
- Деплой
```

### Мониторинг production

```bash
# 1. Настройка логирования
/sc:implement structured logging с Sentry

# 2. Health checks
/sc:implement health check endpoints

# 3. Метрики
/sc:implement Prometheus метрики

# 4. Алерты
/sc:implement алерты для критических ошибок
```

---

## 📊 Комбинированные Workflow

### Полный цикл новой функции

```bash
# 1. Планирование
/sc:design --ultrathink функция [название]

# 2. Создание моделей
/sc:implement модели с миграциями

# 3. Repository layer
/sc:implement репозитории для всех моделей

# 4. Service layer
/sc:implement сервисы с бизнес-логикой

# 5. API endpoints
/sc:implement REST API с документацией

# 6. Celery задачи
/sc:implement асинхронные задачи

# 7. Тестирование
/sc:test --all покрытие > 80%

# 8. Документация
/sc:document полная документация функции
```

### Рефакторинг legacy кода

```bash
# 1. Анализ
/sc:analyze --focus architecture legacy код

# 2. План рефакторинга
/sc:design --safe план миграции

# 3. Создание тестов
/sc:test --generate для legacy кода

# 4. Пошаговый рефакторинг
/sc:improve --refactor --safe по частям

# 5. Валидация
/sc:test регрессионное тестирование
```

---

## 🔄 4-Этапный Workflow из реальной практики

### Проверенный процесс для всех задач

```yaml
1. ANALYZE: "Глубокое исследование перед действием"
   Ключевые флаги:
   - --think-hard для средних задач
   - --ultrathink для архитектурных решений
   - --persona=Researcher,Architect для комплексного анализа
   Примеры:
   - "изучить документы в /docs/superclaude/features/"
   - "проконсультироваться через zen mcp с Gemini Pro 2.5 и o3"

2. DESIGN: "Детальное проектирование с учетом контекста"
   Ключевые флаги:
   - --ultrathink почти всегда
   - --all-mcp для доступа ко всем ресурсам
   - --persona=Architect,System_Thinker для системного подхода
   Примеры:
   - "план реализации с учетом project_context.md"
   - "код не трогаем только документы делаем"

3. TASK: "Декомпозиция на управляемые подзадачи"
   Ключевые флаги:
   - --spawn для автоматического создания файлов
   - --persona=Architect,ProjectManager,Planner
   - --ultrathink для сложной декомпозиции
   Примеры:
   - "разбить на подзадачи в docs/superclaude/tasks/[feature]/"
   - "документы пронумеруй для последовательности"

4. BUILD: "Пошаговая реализация с проверкой качества"
   Ключевые флаги:
   - --tdd --coverage всегда
   - --ultrathink --all-mcp для сложных задач
   - --persona=backend основная персона
   Примеры:
   - "реализовать задачу из docs/superclaude/tasks/*/001_*.md"
   - "проверь что ВСЕ тесты проекта проходят"
```

### Важные принципы из практики

```yaml
Последовательность: "Всегда начинать с ANALYZE"
  - Никогда не пропускать этап исследования
  - Даже для "простых" задач

Документация первична: "Код не трогаем только документы"
  - При планировании работаем только с документами
  - Код пишем только на этапе BUILD

Множественные персоны: "Комбинирование через запятую"
  - Researcher,Architect для анализа
  - Architect,System_Thinker для проектирования
  - Architect,ProjectManager,Planner для планирования

Консультации с моделями: "Через zen mcp"
  - Для сложных архитектурных решений
  - При необходимости второго мнения

Актуальность контекста: "Всегда обновлять project_context.md"
  - После каждой значимой реализации
  - "Не забывай держать актуальным"
```

### Пример полного цикла из практики

```bash
# Задача: Интеграция с новым API социальной сети

# 1. ANALYZE
/sc:analyze --ultrathink --persona=Researcher,Architect \
  "документация API и существующие интеграции, консультация через zen mcp"

# 2. DESIGN  
/sc:design --ultrathink --all-mcp --persona=Architect,System_Thinker \
  "план интеграции в /docs/superclaude/features/, код не трогаем"

# 3. TASK
/sc:task --spawn --persona=Architect,ProjectManager,Planner --ultrathink \
  "разбить на подзадачи в /docs/superclaude/tasks/api_integration/"

# 4. BUILD (для каждой подзадачи)
/sc:build --tdd --coverage --ultrathink --all-mcp --persona=backend \
  "реализовать 001_api_client_foundation.md"

# После каждого BUILD
"Проверь что все тесты проходят и обнови project_context.md"
```

---

## 🛡 Best Practices

### Использование персон

```yaml
backend: Для Django моделей, views, сервисов
architect: Для проектирования систем
qa: Для написания тестов
security: Для аудита безопасности
performance: Для оптимизации
devops: Для deployment и инфраструктуры
```

### Флаги производительности

```yaml
--think: Простые задачи (1-2 файла)
--think-hard: Средние задачи (целый app)
--ultrathink: Сложные архитектурные решения
--uc: При работе с большой кодовой базой
```

### Использование MCP

```yaml
--c7: Для изучения Django/DRF документации
--seq: Для сложного анализа и отладки
--magic: Для генерации Django форм и UI
--playwright: Для E2E тестирования
```

---

## 📚 Полезные команды

### Быстрые команды

```bash
# Создать модель с полным стеком
/sc:implement full-stack модель [Name]

# Быстрый security check
/sc:analyze --security --quick

# Генерация всех тестов
/sc:test --generate --all

# Оптимизация всего app
/sc:improve --performance --all [app_name]
```

### Отладка

```bash
# Найти источник ошибки
/sc:troubleshoot --seq ошибка в [место]

# Анализ памяти
/sc:analyze --memory утечки в Celery

# SQL debugging
/sc:analyze --sql медленные запросы
```

---

*Последнее обновление: 2025-01-14 | SuperClaude v3.0 | SocialManager Django Workflows*