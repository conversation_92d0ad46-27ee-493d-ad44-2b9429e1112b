import ast
from pathlib import Path


class DocstringGenerator:
    """Генератор документации из кода"""
    
    @staticmethod
    def generate_module_docs(module_path: str) -> str:
        """
        Генерация документации для модуля
        
        Args:
            module_path: Путь к Python модулю
            
        Returns:
            Строка с документацией в Markdown формате
        """
        try:
            with open(module_path, encoding="utf-8") as f:
                tree = ast.parse(f.read())
        except Exception as e:
            return f"Error parsing {module_path}: {e!s}"
        
        docs = []
        module_name = Path(module_path).stem
        
        # Извлекаем docstring модуля
        module_docstring = ast.get_docstring(tree)
        if module_docstring:
            docs.append(f"## Module: {module_name}\n\n{module_docstring}\n")
        
        # Обрабатываем классы и функции
        for node in ast.walk(tree):
            if isinstance(node, ast.ClassDef):
                class_doc = DocstringGenerator._process_class(node)
                if class_doc:
                    docs.append(class_doc)
            elif isinstance(node, ast.FunctionDef) and node.col_offset == 0:
                # Только функции верхнего уровня
                func_doc = DocstringGenerator._process_function(node)
                if func_doc:
                    docs.append(func_doc)
        
        return "\n\n".join(docs) if docs else f"No documentation found for {module_name}"
    
    @staticmethod
    def _process_class(node: ast.ClassDef) -> str:
        """Обработка класса"""
        if node.name.startswith("_"):
            return ""  # Пропускаем приватные классы
            
        docstring = ast.get_docstring(node) or "No description"
        methods = []
        properties = []
        
        for item in node.body:
            if isinstance(item, ast.FunctionDef):
                if item.name.startswith("__") and item.name.endswith("__"):
                    continue  # Пропускаем магические методы
                elif item.name.startswith("_"):
                    continue  # Пропускаем приватные методы
                
                # Проверяем, является ли это property
                is_property = any(
                    isinstance(decorator, ast.Name) and decorator.id == "property"
                    for decorator in item.decorator_list
                )
                
                method_doc = ast.get_docstring(item) or "No description"
                method_args = DocstringGenerator._get_function_args(item)
                
                if is_property:
                    properties.append(f"- `{item.name}`: {method_doc.split('.')[0]}")
                else:
                    methods.append(f"- `{item.name}({method_args})`: {method_doc.split('.')[0]}")
        
        result = f"### Class: {node.name}\n\n{docstring}\n"
        
        if properties:
            result += f"\n#### Properties:\n{chr(10).join(properties)}\n"
        
        if methods:
            result += f"\n#### Methods:\n{chr(10).join(methods)}\n"
        
        return result
    
    @staticmethod
    def _process_function(node: ast.FunctionDef) -> str:
        """Обработка функции"""
        if node.name.startswith("_"):
            return ""  # Пропускаем приватные функции
            
        docstring = ast.get_docstring(node) or "No description"
        args = DocstringGenerator._get_function_args(node)
        
        return f"### Function: {node.name}\n\n`{node.name}({args})`\n\n{docstring}"
    
    @staticmethod
    def _get_function_args(node: ast.FunctionDef) -> str:
        """Получение аргументов функции"""
        args = []
        
        # Обычные аргументы
        for i, arg in enumerate(node.args.args):
            if arg.arg == "self" or arg.arg == "cls":
                continue
                
            arg_str = arg.arg
            
            # Добавляем аннотацию типа если есть
            if arg.annotation:
                arg_str += f": {ast.unparse(arg.annotation)}"
            
            # Добавляем значение по умолчанию если есть
            defaults_start = len(node.args.args) - len(node.args.defaults)
            if i >= defaults_start:
                default_index = i - defaults_start
                default_value = ast.unparse(node.args.defaults[default_index])
                arg_str += f" = {default_value}"
            
            args.append(arg_str)
        
        # *args
        if node.args.vararg:
            arg_str = f"*{node.args.vararg.arg}"
            if node.args.vararg.annotation:
                arg_str += f": {ast.unparse(node.args.vararg.annotation)}"
            args.append(arg_str)
        
        # **kwargs
        if node.args.kwarg:
            arg_str = f"**{node.args.kwarg.arg}"
            if node.args.kwarg.annotation:
                arg_str += f": {ast.unparse(node.args.kwarg.annotation)}"
            args.append(arg_str)
        
        return ", ".join(args)


class ProjectDocumentationGenerator:
    """Генератор документации для всего проекта"""
    
    def __init__(self, project_root: str):
        self.project_root = Path(project_root)
        self.docs_dir = self.project_root / "docs"
        self.docs_dir.mkdir(exist_ok=True)
    
    def generate_app_documentation(self, app_name: str) -> str:
        """
        Генерация документации для Django приложения
        
        Args:
            app_name: Имя приложения
            
        Returns:
            Путь к созданному файлу документации
        """
        app_path = self.project_root / app_name
        if not app_path.exists():
            raise ValueError(f"Application {app_name} not found")
        
        docs_content = [f"# {app_name.title().replace('_', ' ')} Documentation\n"]
        
        # Категории файлов для документирования
        categories = {
            "Models": ["models.py"],
            "Services": ["services/*.py", "services.py"],
            "Repositories": ["repositories/*.py", "repositories.py"],
            "API": ["api/*.py", "api.py", "views.py"],
            "Utilities": ["utils/*.py", "utils.py"],
            "Managers": ["managers/*.py", "managers.py"],
        }
        
        for category, patterns in categories.items():
            category_docs = []
            
            for pattern in patterns:
                if "*" in pattern:
                    # Обработка паттернов с wildcard
                    base_dir, file_pattern = pattern.split("/")
                    dir_path = app_path / base_dir
                    if dir_path.exists() and dir_path.is_dir():
                        for py_file in dir_path.glob(file_pattern):
                            if py_file.name != "__init__.py":
                                module_docs = DocstringGenerator.generate_module_docs(str(py_file))
                                if module_docs and "No documentation found" not in module_docs:
                                    category_docs.append(module_docs)
                else:
                    # Обработка конкретных файлов
                    file_path = app_path / pattern
                    if file_path.exists():
                        module_docs = DocstringGenerator.generate_module_docs(str(file_path))
                        if module_docs and "No documentation found" not in module_docs:
                            category_docs.append(module_docs)
            
            if category_docs:
                docs_content.append(f"\n## {category}\n")
                docs_content.extend(category_docs)
        
        # Сохранение документации
        output_path = self.docs_dir / f"{app_name}.md"
        with open(output_path, "w", encoding="utf-8") as f:
            f.write("\n".join(docs_content))
        
        return str(output_path)
    
    def generate_project_overview(self) -> str:
        """Генерация общего обзора проекта"""
        overview = """# SocialManager Documentation

## Project Overview

SocialManager is a Django-based application for managing social media integrations, currently focused on Telegram and Instagram.

### Applications

- **core**: Base application with shared functionality
- **instagram_manager**: Instagram integration and management
- **telegram_manager**: Telegram integration and management

### Key Features

- Repository pattern for data access
- Service layer for business logic
- Pydantic integration for data validation
- BrightData API integration for Instagram data
- Telethon integration for Telegram
- Comprehensive admin interface
- Management commands for automation

### Architecture

The project follows a layered architecture:

1. **Models Layer**: Django ORM models
2. **Repository Layer**: Data access abstraction
3. **Service Layer**: Business logic
4. **API Layer**: REST endpoints and views
5. **Admin Layer**: Django admin customizations

For detailed documentation of each component, see the individual application documentation files.
"""
        
        output_path = self.docs_dir / "README.md"
        with open(output_path, "w", encoding="utf-8") as f:
            f.write(overview)
        
        return str(output_path)
    
    def generate_all_documentation(self) -> list[str]:
        """Генерация документации для всего проекта"""
        generated_files = []
        
        # Генерация обзора
        generated_files.append(self.generate_project_overview())
        
        # Генерация документации для каждого приложения
        apps = ["core", "instagram_manager", "telegram_manager"]
        
        for app in apps:
            try:
                doc_path = self.generate_app_documentation(app)
                generated_files.append(doc_path)
                print(f"✅ Generated documentation for {app}")
            except Exception as e:
                print(f"❌ Failed to generate documentation for {app}: {e!s}")
        
        return generated_files