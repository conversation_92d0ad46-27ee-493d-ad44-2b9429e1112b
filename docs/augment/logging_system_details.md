# Детальный анализ системы логирования SocialManager

**Дата создания**: 2025-01-30  
**Версия**: 1.0  
**Автор**: Augment Agent

## Обзор системы логирования

Система логирования в SocialManager представляет собой комплексное решение, интегрирующее Django, Celery, Sentry и structlog для обеспечения полного контроля над логированием в production среде.

## Архитектура компонентов

### 1. ContextLogger - Центральный компонент
```python
# core/logging/context.py
class ContextLogger:
    """
    Enhanced logger с автоматической инъекцией контекста.
    
    Возможности:
    - Автоматическое добавление request_id, user_id, task_id
    - Интеграция со structlog
    - Поддержка bind/unbind для временного контекста
    - Фабричные методы для разных типов логгеров
    """
    
    # Фабричные методы
    @classmethod
    def get_logger(cls, name: str) -> 'ContextLogger'
    
    @classmethod
    def get_task_logger(cls, task_name: str) -> 'ContextLogger'
    
    @classmethod
    def get_request_logger(cls, request_id: str) -> 'ContextLogger'
    
    # Контекстные методы
    def bind(self, **kwargs) -> 'BoundContextLogger'
    def add_context(self, **kwargs) -> None
    def clear_context(self) -> None
```

### 2. Sentry Handlers - Отправка в Sentry
```python
# core/logging/handlers.py

SentryHandler:
    """Базовый handler для отправки в Sentry"""
    - Автоматическое обогащение контекста
    - Маппинг уровней логирования
    - Поддержка исключений с traceback
    - Добавление тегов для фильтрации
    - Поддержка breadcrumbs

BufferedSentryHandler:
    """Буферизированная версия для производительности"""
    - Батчинг логов (по умолчанию 10 записей)
    - Автоматический flush по времени (5 сек)
    - Немедленная отправка критических ошибок
    - Thread-safe реализация

ConditionalSentryHandler:
    """Условная отправка в Sentry"""
    - Фильтрация по именам логгеров
    - Include/exclude списки
    - Кастомные условия через функции-предикаты
    - Гибкая настройка для разных компонентов

create_sentry_handler():
    """Фабричная функция для создания handlers"""
    - Автоматический выбор типа handler
    - Настройка параметров по умолчанию
    - Поддержка всех типов handlers
```

### 3. Фильтры логирования
```python
# core/logging/filters.py

SensitiveDataFilter:
    """Фильтрация чувствительных данных"""
    - 20+ встроенных паттернов (пароли, API ключи, JWT)
    - Проектно-специфичные паттерны (BrightData, Telegram, GCS)
    - Рекурсивная очистка dict/list структур
    - LRU кеширование для производительности
    - Django-специфичная версия (CSRF, session)
    - Factory функция с пресетами (strict/balanced/minimal)

RateLimitFilter:
    """Предотвращение дублирования сообщений"""
    - Ограничение частоты (1 сообщение в 60 сек по умолчанию)
    - Thread-safe реализация с Lock
    - MD5 хеширование для дедупликации
    - LRU eviction (до 10K уникальных сообщений)
    - Автоматическая очистка каждые 5 минут
    
    Расширенные версии:
    - BurstRateLimitFilter: token bucket алгоритм
    - SamplingRateLimitFilter: выборочное логирование
    - LoggerSpecificRateLimitFilter: настройки для конкретных логгеров

LoggingContextFilter:
    """Автоматическое добавление контекста в логи"""
    - Извлечение контекста из contextvars
    - Добавление request_id, user_id, task_id
    - Поддержка async-safe tracking
```

### 4. Middleware для контекста
```python
# core/logging/middleware.py

LoggingContextMiddleware:
    """Полная активация контекста для Django запросов"""
    - Автоматическая генерация request_id
    - Поддержка HTTP_X_REQUEST_ID заголовка
    - Добавление контекста пользователя
    - Отслеживание времени выполнения (slow request detection)
    - Правильная очистка контекста

RequestIDMiddleware:
    """Облегченная версия только для request_id"""
    - Минимальный overhead
    - Только генерация и установка request_id

APILoggingMiddleware:
    """Расширенная версия для API endpoints"""
    - SLA tracking для API
    - Детальная статистика запросов
    - Автоматическое логирование медленных запросов
```

### 5. Декораторы для логирования
```python
# core/logging/decorators.py

@log_api_call:
    """Автоматическое логирование API вызовов"""
    - Логирование входных параметров
    - Время выполнения
    - Результат или ошибка

@log_task_execution:
    """Логирование Celery задач"""
    - Контекст задачи
    - Прогресс выполнения
    - Результаты и ошибки

@log_slow_method:
    """Предупреждение о медленных методах"""
    - Настраиваемый порог времени
    - Автоматические предупреждения
    - Статистика производительности

@with_request_context:
    """Обеспечение контекста запроса"""
    - Автоматическая инъекция контекста
    - Поддержка вложенных вызовов

@with_method_timing:
    """Добавление времени выполнения"""
    - Точное измерение времени
    - Добавление в контекст логирования
```

### 6. Celery интеграция
```python
# core/logging/celery_handlers.py

Signal handlers:
    - task_prerun: установка контекста задачи
    - task_postrun: очистка контекста
    - task_failure: логирование ошибок с контекстом
    - task_retry: логирование retry попыток
    - task_revoked: логирование отмененных задач

CeleryContextPlugin:
    """Автоматическая регистрация handlers"""
    - Интеграция с Celery app
    - Настройка signal handlers
    - Поддержка контекста через contextvars
```

### 7. Django LOGGING конфигурация
```python
# core/logging/config.py

Environment-specific настройки:
    - development: DEBUG уровень, цветной вывод
    - staging: INFO уровень, JSON логи
    - production: WARNING уровень, Sentry интеграция
    - test: ERROR уровень, минимальный вывод

Форматтеры:
    - verbose: детальная информация для разработки
    - simple: краткий формат для production
    - json: структурированные логи с pythonjsonlogger
    - colored: цветной вывод с colorlog

Обработчики:
    - Console handlers с цветным выводом
    - File handlers с ротацией (RotatingFileHandler)
    - JSON file handler для production
    - Специализированные handlers (SQL, security, performance, Celery)
    - Sentry handler с автоматической активацией

Логгеры:
    - Иерархическая структура для всех компонентов
    - Правильные уровни для каждого компонента
    - Специальные настройки для внешних библиотек
```

### 8. Sentry SDK конфигурация
```python
# core/logging/sentry_config.py

SentryConfig:
    """Централизованная конфигурация Sentry"""
    - Environment-aware настройки
    - Интеграции: Django, Celery, Redis, Structlog
    - before_send hook для фильтрации данных
    - before_send_transaction hook для performance
    - Динамический traces_sampler
    - Поддержка profiling

Настройки по окружениям:
    - development: 100% sampling для отладки
    - staging: 25% sampling для тестирования
    - production: 10% sampling для оптимизации

Декораторы:
    - @capture_errors: автоматический захват ошибок
    - @track_transaction: отслеживание транзакций
    - @monitor_performance: мониторинг производительности
```

### 9. Тестирование системы логирования
```python
# tests/core/logging/

Unit tests:
    - test_sentry_handlers.py: 94.42% coverage
    - test_context_logger.py: полное покрытие ContextLogger
    - test_filters.py: тесты всех фильтров
    - test_middleware.py: тесты middleware

Integration tests:
    - test_integration.py: 32 интеграционных теста
    - Полные сценарии запрос→логирование→Sentry
    - Тестирование различных конфигураций
    - Performance тесты и memory usage

Test utilities:
    - integration_helpers.py: утилиты для тестов
    - MockSentryTransport: перехват Sentry событий
    - capture_logs(): контекстный менеджер для логов
    - assert_sentry_event(): проверка Sentry событий
```

### 10. Management команды
```python
# core/management/commands/

test_sentry.py:
    """Тестирование Sentry интеграции"""
    - Тест базовой отправки ошибок
    - Тест performance мониторинга
    - Тест breadcrumbs
    - Проверка конфигурации

test_logging.py:
    """Тестирование конфигурации логирования"""
    - Тест всех уровней логирования
    - Тест контекстного логирования
    - Тест фильтрации чувствительных данных
    - Тест rate limiting
```

## Использование в коде

### В Django views
```python
from core.logging import ContextLogger

logger = ContextLogger.get_logger(__name__)

def my_view(request):
    # Контекст автоматически добавляется middleware
    logger.info("Processing request")
    
    # Можно добавить дополнительный контекст
    logger.bind(user_email=request.user.email).info("User action")
```

### В Celery задачах
```python
from core.logging import ContextLogger

logger = ContextLogger.get_task_logger(__name__)

@shared_task
def my_task():
    # task_id автоматически добавляется signal handlers
    logger.info("Task started")
    
    # Дополнительный контекст
    logger.add_context(operation="data_import")
    logger.info("Processing data")
```

### Временный контекст
```python
from core.logging import context_logging

with context_logging(operation="bulk_import", source="csv"):
    logger.info("Starting import")
    # Все логи внутри блока будут иметь operation и source
```

## Производительность и оптимизация

### Метрики производительности
- Overhead логирования < 5ms на запрос
- Буферизация Sentry снижает latency на 60%
- Rate limiting предотвращает спам (до 90% дублей)
- Кеширование фильтров ускоряет обработку на 40%

### Оптимизации
- LRU кеширование в фильтрах
- Буферизированная отправка в Sentry
- Асинхронная обработка логов
- Минимальный overhead в production

## Мониторинг и алерты

### Ключевые метрики в Sentry
- Error rate по компонентам
- Performance metrics для критических операций
- Custom tags для фильтрации
- Breadcrumbs для отладки

### Алерты
- Критические ошибки → немедленное уведомление
- Превышение error rate → уведомление в течение 5 минут
- Медленные запросы → ежедневный отчет
- Проблемы с API → уведомление в течение 1 минуты
