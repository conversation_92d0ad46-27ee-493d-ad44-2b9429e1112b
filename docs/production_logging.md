# Production Logging Configuration

## Обзор

В production окружении настроена конфигурация логирования, которая:
- Выводит все логи только в консоль (stdout) в JSON формате
- НЕ создаёт файлы логов на диске
- Отправляет ошибки уровня WARNING и выше в Sentry

## Внесенные изменения

### 1. Изменения в `core/logging/config.py`

#### Функция `_get_handlers()` (строки 249-255, 258)
- Добавлен специальный `console_json` хендлер для production
- Файловые хендлеры НЕ создаются в production окружении

```python
# Add console_json handler for production
if environment == "production":
    handlers["console_json"] = {
        "level": "INFO",
        "class": "logging.StreamHandler",
        "formatter": "json",
        "filters": ["context", "sensitive_data"],
    }

# File handlers - not created for production
if environment not in ["test", "production"]:
    # ... файловые хендлеры ...
```

#### Функция `_get_loggers()` (строки 361-363)
- В production используются только консольные хендлеры и Sentry

```python
elif environment == "production":
    default_handlers = ["console_json"]
    error_handlers = ["console_errors", "mail_admins"]
```

#### Функция `_get_root_logger()` (строки 468-472)
- Root logger использует только консольные хендлеры

```python
elif environment == "production":
    return {
        "handlers": ["console_json", "console_errors"],
        "level": levels["root"],
    }
```

## Формат логов в production

Все логи выводятся в JSON формате для удобного парсинга:

```json
{
  "timestamp": "2025-07-16T16:15:01.342109",
  "level": "INFO",
  "logger": "core",
  "message": "Обработка запроса пользователя",
  "module": "views",
  "function": "import_profile",
  "line": 42,
  "request_id": "123e4567-e89b-12d3-a456-426614174000",
  "user_id": 42,
  "username": "testuser"
}
```

## Docker интеграция

В Docker контейнере (см. `Dockerfile` и `docker-compose.prod.yml`):
- Логи выводятся в stdout контейнера
- Docker/Kubernetes автоматически собирает логи из stdout
- Нет необходимости в volume для логов

## Sentry интеграция

- Sentry получает все ошибки уровня WARNING и выше
- Используется `BufferedSentryHandler` для оптимизации производительности
- Контекст (request_id, user_id и т.д.) автоматически добавляется к событиям в Sentry

## Проверка конфигурации

Для проверки конфигурации используйте:

```bash
# Простая проверка конфигурации
python test_production_logging_simple.py

# Демонстрация формата логов
python test_production_logging_output.py
```

## Преимущества

1. **Простота**: Все логи в одном месте - stdout
2. **Совместимость**: Работает с любой системой оркестрации контейнеров
3. **Производительность**: Нет дисковых операций для логов
4. **Мониторинг**: JSON формат легко парсится системами мониторинга
5. **Безопасность**: Sensitive данные автоматически фильтруются