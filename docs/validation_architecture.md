# Validation Architecture

## Overview

The validation system in SocialManager is designed to eliminate duplication between Django forms and Pydantic schemas by providing a centralized validation layer.

## Architecture

### 1. Core Validators (`core.schemas.validators`)

Centralized validation logic used by both Pydantic schemas and Django forms:

- **CommonValidators**: General purpose validators (dates, text, URLs, etc.)
- **InstagramValidators**: Instagram-specific validation rules
- **TelegramValidators**: Telegram-specific validation rules

### 2. Django Form Components (`core.forms`)

#### Validators (`core.forms.validators`)
Django-specific validators that wrap the core validation logic:
- `UsernameValidator`: Social media usernames
- `InstagramURLValidator`: Instagram URLs validation  
- `HashtagValidator`: Hashtag cleaning and validation
- `DateRangeValidator`: Date range validation
- `BulkUsernamesValidator`: Bulk username lists
- `BulkURLsValidator`: Bulk URL lists

#### Fields (`core.forms.fields`)
Reusable form fields with built-in validation:
- `SocialUsernameField`: Username input with platform validation
- `InstagramURLField`: Instagram URL input
- `HashtagField`: Hashtag input with auto-cleaning
- `BulkUsernamesField`: Multiple usernames textarea
- `BulkURLsField`: Multiple URLs textarea
- `PostTypesField`: Instagram post types selection

#### Mixins (`core.forms.mixins`)
Form mixins for common functionality:
- `CleanUsernameMixin`: Username cleaning logic
- `CleanHashtagMixin`: Hashtag cleaning logic
- `DateRangeMixin`: Date range validation
- `InstagramURLMixin`: Instagram URL validation
- `ExecutionModeMixin`: Sync/async execution mode
- `MediaDownloadOptionsMixin`: Media download options
- `PydanticFormMixin`: Integration with Pydantic schemas

## Usage Examples

### Using Custom Fields

```python
from core.forms import SocialUsernameField, InstagramURLField

class ProfileImportForm(forms.Form):
    username = SocialUsernameField(
        platform="instagram",
        required=True,
        help_text="Enter Instagram username"
    )
    
    profile_url = InstagramURLField(
        required=False,
        help_text="Optional profile URL"
    )
```

### Using Mixins

```python
from core.forms import CleanUsernameMixin, ExecutionModeMixin

class MyForm(CleanUsernameMixin, ExecutionModeMixin, forms.Form):
    username = forms.CharField(max_length=150)
    # clean_username() is automatically provided by mixin
```

### Using Validators Directly

```python
from core.forms.validators import instagram_username_validator

class SimpleForm(forms.Form):
    username = forms.CharField(
        validators=[instagram_username_validator]
    )
```

### Integration with Pydantic

```python
from core.forms import PydanticFormMixin
from myapp.schemas import MySchema

class MyForm(PydanticFormMixin, forms.Form):
    pydantic_schema = MySchema
    
    # Form fields...
    # Validation will use both Django and Pydantic
```

## Migration Guide

### Before (Duplicated Logic)

```python
# In forms.py
def clean_username(self):
    username = self.cleaned_data["username"]
    return username.lstrip("@")

# In schemas.py  
def validate_username(username: str) -> str:
    return username.lstrip("@")
```

### After (Centralized)

```python
# In forms.py
from core.forms import CleanUsernameMixin

class MyForm(CleanUsernameMixin, forms.Form):
    username = forms.CharField()
    # clean_username() automatically provided

# In schemas.py
from core.schemas.validators import CommonValidators

@field_validator("username")
def validate_username(cls, v):
    return CommonValidators.clean_username(v)
```

## Benefits

1. **No Duplication**: Single source of truth for validation logic
2. **Consistency**: Same validation rules in forms and schemas
3. **Maintainability**: Changes in one place affect all usage
4. **Reusability**: Common patterns extracted into mixins and fields
5. **Type Safety**: Validation logic can be type-checked
6. **Testing**: Centralized validators are easier to test

## Deprecated Functions

The following validation functions have been deprecated and moved to centralized validators:

### Instagram (`instagram_manager.schemas.validation`)
- `clean_text()` → `CommonValidators.clean_text()`
- `extract_hashtags()` → `CommonValidators.extract_hashtags()`
- `extract_mentions()` → `CommonValidators.extract_mentions()`
- `parse_brightdata_timestamp()` → `CommonValidators.parse_timestamp()`
- `normalize_media_type()` → `CommonValidators.normalize_media_type()`
- `parse_location()` → `CommonValidators.parse_location()`
- `validate_instagram_username()` → `InstagramValidators.validate_username()`
- `safe_int_conversion()` → `CommonValidators.safe_int_conversion()`

### Telegram (`telegram_manager.schemas.validation`)
- `clean_telegram_text()` → `CommonValidators.clean_text()`
- `extract_telegram_entities()` → `CommonValidators.extract_*` methods
- `normalize_telegram_phone()` → `TelegramValidators.normalize_phone()`
- `parse_telegram_timestamp()` → `CommonValidators.parse_timestamp()`
- `determine_chat_type()` → `TelegramValidators.determine_chat_type()`