### 2. Development Workflow

```bash
# Daily development workflow

# 1. Start services
docker-compose -f docker-compose.yml -f docker-compose.dev.yml up -d

# 2. Activate virtual environment
source .venv/bin/activate

# 3. Run Django with hot-reload
python manage.py runserver

# Or run with ASGI
uvicorn SocialManager.asgi:application --reload --host 0.0.0.0 --port 8000

# 4. Run tests
uv run pytest

# 5. Check code quality
uv run black .
uv run ruff check
uv run mypy .

# 6. Stop services when done
docker-compose down
```