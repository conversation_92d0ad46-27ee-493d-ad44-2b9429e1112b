# Development environment - Django runs locally, services in Docker
services:
  # MCP Server for development
  mcp-server:
    build:
      context: .
      dockerfile: Dockerfiles/Dockerfile.mcp
    ports:
      - "8001:8001"
    volumes:
      - ./mcp_server:/app/mcp_server
      - ./instagram_manager:/app/instagram_manager
      - ./telegram_manager:/app/telegram_manager
      - ./core:/app/core
      - ./SocialManager:/app/SocialManager
      - ./manage.py:/app/manage.py
      - ./logs:/app/logs
    environment:
      - DATABASE_URL=************************************/socialmanager
      - REDIS_URL=redis://redis:6379/0
      - DJANGO_SETTINGS_MODULE=SocialManager.settings.docker_dev
      - DEBUG=True
      - SECRET_KEY=django-insecure-dev-key
      - ALLOWED_HOSTS=localhost,127.0.0.1
    depends_on:
      db:
        condition: service_healthy
      redis:
        condition: service_healthy
    command: ["uv", "run", "uvicorn", "mcp_server.main:app", "--host", "0.0.0.0", "--port", "8001", "--reload"]
    healthcheck:
      test: ["CMD", "curl", "-f", "-H", "Accept: application/json", "http://localhost:8001/health"]
      interval: 300s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Development database with exposed port
  db:
    ports:
      - "5432:5432"

  # Development Redis with exposed port
  redis:
    ports:
      - "6379:6379"

  # Mount local code for Celery hot-reload
  celery:
    volumes:
      - .:/app
      - /app/.venv
    environment:
      - DJANGO_SETTINGS_MODULE=SocialManager.settings.local
      - DEBUG=True