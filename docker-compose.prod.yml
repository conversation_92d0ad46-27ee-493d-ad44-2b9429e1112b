# Production environment - Everything runs in Docker
services:
  # Django Application with ASGI
  django:
    build:
      context: .
      dockerfile: Dockerfiles/Dockerfile
    expose:
      - "8000"
    environment:
      - DJANGO_SETTINGS_MODULE=SocialManager.settings.production
      - DATABASE_URL=postgres://postgres:${DB_PASSWORD}@db:5432/socialmanager
      - REDIS_URL=redis://redis:6379/0
      - SECRET_KEY=${SECRET_KEY}
      - ALLOWED_HOSTS=${ALLOWED_HOSTS}
    volumes:
      - media_volume:/app/media
      - static_volume:/app/static
      - telegram_sessions:/app/telegram_sessions
    depends_on:
      db:
        condition: service_healthy
      redis:
        condition: service_healthy
    command: ["uv", "run", "uvicorn", "SocialManager.asgi:application", 
              "--host", "0.0.0.0", "--port", "8000", 
              "--workers", "4", "--loop", "asyncio"]

  # MCP Server
  mcp-server:
    build:
      context: .
      dockerfile: Dockerfiles/Dockerfile.mcp
    expose:
      - "8001"
    environment:
      - DATABASE_URL=postgres://postgres:${DB_PASSWORD}@db:5432/socialmanager
      - REDIS_URL=redis://redis:6379/0
      - DJANGO_SETTINGS_MODULE=SocialManager.settings.production
    depends_on:
      - django

  # Nginx Reverse Proxy
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/conf.d:/etc/nginx/conf.d:ro
      - static_volume:/app/static:ro
      - media_volume:/app/media:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
    depends_on:
      - django
      - mcp-server

  # Production Celery configuration
  celery:
    build:
      context: .
      dockerfile: Dockerfile.celery
    environment:
      - DJANGO_SETTINGS_MODULE=SocialManager.settings.production
      - DATABASE_URL=postgres://postgres:${DB_PASSWORD}@db:5432/socialmanager
      - REDIS_URL=redis://redis:6379/0
      - CELERY_BROKER_URL=redis://redis:6379/0
      - CELERY_RESULT_BACKEND=redis://redis:6379/1
      - CELERY_COMMAND=worker
    volumes:
      - media_volume:/app/media
      - telegram_sessions:/app/telegram_sessions
    depends_on:
      db:
        condition: service_healthy
      redis:
        condition: service_healthy
    deploy:
      replicas: 2
      resources:
        limits:
          cpus: '1'
          memory: 1G

  # Celery Beat Scheduler
  celery-beat:
    build:
      context: .
      dockerfile: Dockerfile.celery
    environment:
      - DJANGO_SETTINGS_MODULE=SocialManager.settings.production
      - DATABASE_URL=postgres://postgres:${DB_PASSWORD}@db:5432/socialmanager
      - REDIS_URL=redis://redis:6379/0
      - CELERY_BROKER_URL=redis://redis:6379/0
      - CELERY_RESULT_BACKEND=redis://redis:6379/1
      - CELERY_COMMAND=beat --scheduler django_celery_beat.schedulers:DatabaseScheduler
    volumes:
      - telegram_sessions:/app/telegram_sessions
    depends_on:
      db:
        condition: service_healthy
      redis:
        condition: service_healthy

  # Flower - Celery Monitoring
  flower:
    build:
      context: .
      dockerfile: Dockerfile.celery
    environment:
      - DJANGO_SETTINGS_MODULE=SocialManager.settings.production
      - DATABASE_URL=postgres://postgres:${DB_PASSWORD}@db:5432/socialmanager
      - REDIS_URL=redis://redis:6379/0
      - CELERY_BROKER_URL=redis://redis:6379/0
      - CELERY_RESULT_BACKEND=redis://redis:6379/1
      - CELERY_COMMAND=flower --port=5555
    expose:
      - "5555"
    depends_on:
      - celery

  # Secure PostgreSQL
  db:
    environment:
      - POSTGRES_PASSWORD=${DB_PASSWORD}
    ports: []  # Don't expose ports in production

  # Secure Redis
  redis:
    command: redis-server --requirepass ${REDIS_PASSWORD}
    ports: []  # Don't expose ports in production

volumes:
  static_volume:
  media_volume:
  postgres_data:
  redis_data:
  telegram_sessions: