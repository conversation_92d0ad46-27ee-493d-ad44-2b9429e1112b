from django import forms

from core.forms import ExecutionModeMixin, SafeRadioSelect


class ChatImportForm(ExecutionModeMixin, forms.Form):
    """Form for importing chats from Telegram."""
    CHAT_LIMIT_CHOICES = [
        ("all", "All chats"),
        ("custom", "Specific number"),
    ]
    
    chat_limit = forms.ChoiceField(
        choices=CHAT_LIMIT_CHOICES,
        initial="all",
        widget=SafeRadioSelect
    )
    custom_limit = forms.IntegerField(
        required=False,
        min_value=1,
        help_text="Enter number of chats to import"
    )
    include_private = forms.BooleanField(
        initial=True,
        required=False,
        label="Private messages (1-on-1 chats)"
    )
    include_groups = forms.BooleanField(
        initial=True,
        required=False,
        label="Regular Groups"
    )
    include_supergroups = forms.BooleanField(
        initial=True,
        required=False,
        label="Supergroups (large groups)"
    )
    include_channels = forms.BooleanField(
        initial=True,
        required=False,
        label="Channels (broadcast only)"
    )
    
    def clean(self):
        cleaned_data = super().clean()
        chat_limit = cleaned_data.get("chat_limit")
        custom_limit = cleaned_data.get("custom_limit")
        
        if chat_limit == "custom" and not custom_limit:
            raise forms.ValidationError("Please enter the number of chats to import")
            
        return cleaned_data


class MessageImportForm(ExecutionModeMixin, forms.Form):
    """Form for importing messages from Telegram."""
    CATEGORY_CHOICES = [
        ("all", "All Messages"),
        ("today", "Today"),
        ("week", "Last 7 Days"),
        ("month", "Last 30 Days"),
        ("custom", "Custom Date Range"),
    ]
    
    category = forms.ChoiceField(choices=CATEGORY_CHOICES, initial="all")
    date_from = forms.DateTimeField(
        required=False,
        widget=forms.DateTimeInput(attrs={"type": "datetime-local"}),
        help_text="Start date for custom range"
    )
    date_to = forms.DateTimeField(
        required=False,
        widget=forms.DateTimeInput(attrs={"type": "datetime-local"}),
        help_text="End date for custom range"
    )
    message_limit = forms.IntegerField(
        initial=1000,
        min_value=1,
        max_value=100000,
        help_text="Maximum number of messages to import per chat"
    )
    
    def clean(self):
        """Валидация диапазона дат для кастомного периода."""
        cleaned_data = super().clean()
        category = cleaned_data.get("category")
        date_from = cleaned_data.get("date_from")
        date_to = cleaned_data.get("date_to")
        
        if category == "custom":
            if not date_from:
                self.add_error("date_from", "Start date is required for custom range")
            if not date_to:
                self.add_error("date_to", "End date is required for custom range")
            
            if date_from and date_to:
                if date_from > date_to:
                    self.add_error(
                        "date_to", 
                        forms.ValidationError(
                            "End date must be after start date.", 
                            code="invalid_date_range"
                        )
                    )
        
        return cleaned_data