"""
Сигналы для Telegram моделей.

Обработчики сигналов Django для автоматической очистки данных
и управления связанными объектами.
"""

import logging
import os

from django.db.models import Q
from django.db.models.signals import post_delete, pre_delete
from django.dispatch import receiver

from telegram_manager.models import TelegramChat, TelegramMessage, TelegramUser

logger = logging.getLogger(__name__)


# Обработчики для TelegramChat

@receiver(pre_delete, sender=TelegramChat)
def delete_chat_photo(sender, instance, **kwargs):
    """
    Удаляет файл фото при удалении чата.
    
    Args:
        sender: Класс модели
        instance: Экземпляр удаляемого чата
        **kwargs: Дополнительные аргументы
    """
    if instance.photo and instance.photo.name:
        # Проверяем существование файла
        if os.path.isfile(instance.photo.path):
            try:
                os.remove(instance.photo.path)
                logger.info(f"Deleted photo file for chat {instance.chat_id}")
            except Exception as e:
                logger.exception(f"Failed to delete photo for chat {instance.chat_id}: {e}")


# Обработчики для TelegramMessage

@receiver(pre_delete, sender=TelegramMessage)
def delete_message_media(sender, instance, **kwargs):
    """
    Удаляет медиафайлы при удалении сообщения.
    
    Args:
        sender: Класс модели
        instance: Экземпляр удаляемого сообщения
        **kwargs: Дополнительные аргументы
    """
    if instance.media_file and instance.media_file.name:
        # Проверяем существование файла
        if os.path.isfile(instance.media_file.path):
            try:
                os.remove(instance.media_file.path)
                logger.info(f"Deleted media file for message {instance.message_id}")
            except Exception as e:
                logger.exception(f"Failed to delete media for message {instance.message_id}: {e}")


@receiver(pre_delete, sender=TelegramMessage)
def cache_related_users(sender, instance, **kwargs):
    """
    Кэширует ID пользователей перед удалением сообщения.
    
    Это необходимо для последующей проверки и удаления
    пользователей-сирот после удаления сообщения.
    
    Args:
        sender: Класс модели
        instance: Экземпляр удаляемого сообщения
        **kwargs: Дополнительные аргументы
    """
    # Кэшируем ID пользователей в атрибутах экземпляра
    instance._from_user_id = instance.from_user_id if instance.from_user else None
    instance._mentioned_user_ids = list(
        instance.mentioned_users.values_list("user_id", flat=True)
    )
    instance._action_user_ids = list(
        instance.action_users.values_list("user_id", flat=True)
    )
    instance._fwd_from_user_id = instance.fwd_from_user_id if instance.fwd_from_user else None
    instance._via_bot_id = instance.via_bot_id if instance.via_bot else None
    
    logger.debug(f"Cached user references for message {instance.message_id}")


@receiver(post_delete, sender=TelegramMessage)
def check_and_delete_orphaned_users(sender, instance, **kwargs):
    """
    Проверяет и удаляет пользователей-сирот после удаления сообщения.
    
    Args:
        sender: Класс модели
        instance: Экземпляр удаленного сообщения
        **kwargs: Дополнительные аргументы
    """
    # Собираем все ID пользователей из кэшированных данных
    user_ids: set[int] = set()
    
    # Добавляем отправителя
    if hasattr(instance, "_from_user_id") and instance._from_user_id:
        user_ids.add(instance._from_user_id)
    
    # Добавляем упомянутых пользователей
    if hasattr(instance, "_mentioned_user_ids"):
        user_ids.update(instance._mentioned_user_ids)
    
    # Добавляем пользователей из действий
    if hasattr(instance, "_action_user_ids"):
        user_ids.update(instance._action_user_ids)
    
    # Добавляем пользователя из пересылки
    if hasattr(instance, "_fwd_from_user_id") and instance._fwd_from_user_id:
        user_ids.add(instance._fwd_from_user_id)
    
    # Добавляем бота
    if hasattr(instance, "_via_bot_id") and instance._via_bot_id:
        user_ids.add(instance._via_bot_id)
    
    # Проверяем каждого пользователя
    for user_id in user_ids:
        _delete_user_if_orphaned(user_id)
    
    logger.debug(f"Checked {len(user_ids)} users for orphan status")


def _delete_user_if_orphaned(user_id: int) -> bool:
    """
    Удаляет пользователя если он больше не имеет ссылок.
    
    Args:
        user_id: ID пользователя для проверки
        
    Returns:
        True если пользователь был удален
    """
    try:
        user = TelegramUser.objects.get(user_id=user_id)
    except TelegramUser.DoesNotExist:
        return False
    
    # Проверяем наличие ссылок на пользователя
    has_references = TelegramMessage.objects.filter(
        Q(from_user_id=user_id) |
        Q(mentioned_users__user_id=user_id) |
        Q(action_users__user_id=user_id) |
        Q(fwd_from_user_id=user_id) |
        Q(via_bot_id=user_id)
    ).exists()
    
    # Если ссылок нет, удаляем пользователя
    if not has_references:
        user.delete()
        logger.info(f"Deleted orphaned user {user_id}")
        return True
    
    return False


def delete_all_orphaned_users():
    """
    Удаляет всех пользователей-сирот в базе данных.
    
    Эта функция может быть вызвана периодически
    для очистки базы данных от неиспользуемых пользователей.
    
    Returns:
        Количество удаленных пользователей
    """
    deleted_count = 0
    
    # Получаем всех пользователей
    for user in TelegramUser.objects.all():
        # Проверяем наличие ссылок
        has_references = TelegramMessage.objects.filter(
            Q(from_user_id=user.user_id) |
            Q(mentioned_users__user_id=user.user_id) |
            Q(action_users__user_id=user.user_id) |
            Q(fwd_from_user_id=user.user_id) |
            Q(via_bot_id=user.user_id)
        ).exists()
        
        # Удаляем если нет ссылок
        if not has_references:
            user.delete()
            deleted_count += 1
            logger.info(f"Deleted orphaned user {user.user_id}")
    
    logger.info(f"Total orphaned users deleted: {deleted_count}")
    return deleted_count