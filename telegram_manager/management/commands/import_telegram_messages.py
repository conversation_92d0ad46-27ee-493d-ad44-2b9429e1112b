"""
Import Telegram messages command with async support.
"""

from datetime import datetime

from core.management.commands.base_async_command import BaseAsyncCommand
from telegram_manager.tasks.import_tasks import ImportTelegramMessagesTask


class Command(BaseAsyncCommand):
    """Command to import Telegram messages with sync/async support."""
    
    help = "Import Telegram messages with support for both sync and async execution"
    
    def add_task_arguments(self, parser):
        """Add arguments specific to message import."""
        parser.add_argument(
            "--category",
            choices=["all", "today", "week", "month", "custom"],
            default="all",
            help="Time range for messages"
        )
        parser.add_argument(
            "--date-from",
            type=str,
            help="Start date for custom range (YYYY-MM-DD HH:MM:SS)"
        )
        parser.add_argument(
            "--date-to",
            type=str,
            help="End date for custom range (YYYY-MM-DD HH:MM:SS)"
        )
        parser.add_argument(
            "--message-limit",
            type=int,
            default=1000,
            help="Maximum messages per chat (default: 1000)"
        )
        parser.add_argument(
            "--skip-user-fetch",
            action="store_true",
            help="Skip fetching user data from API"
        )
    
    def get_task_class(self):
        """Return the task class for async execution."""
        return ImportTelegramMessagesTask
    
    def prepare_task_params(self, **options):
        """Prepare parameters for the task."""
        params = {
            "category": options.get("category", "all"),
            "message_limit": options.get("message_limit", 1000),
            "skip_user_fetch": options.get("skip_user_fetch", False),
        }
        
        # Handle date parameters for custom category
        if params["category"] == "custom":
            date_from = options.get("date_from")
            date_to = options.get("date_to")
            
            if not date_from or not date_to:
                raise ValueError("Both --date-from and --date-to are required for custom category")
            
            # Validate date format
            try:
                datetime.strptime(date_from, "%Y-%m-%d %H:%M:%S")
                datetime.strptime(date_to, "%Y-%m-%d %H:%M:%S")
            except ValueError:
                raise ValueError("Date format must be YYYY-MM-DD HH:MM:SS")
            
            params["date_from"] = date_from
            params["date_to"] = date_to
        
        return params
    
    def _display_result(self, result):
        """Display the import result."""
        if isinstance(result, dict):
            self.stdout.write("\nMessage Import Summary:")
            self.stdout.write(f"  Total messages: {result.get('total_messages', 0)}")
            self.stdout.write(f"  Created: {result.get('created', 0)}")
            self.stdout.write(f"  Updated: {result.get('updated', 0)}")
            
            users_created = result.get("users_created", 0)
            if users_created:
                self.stdout.write(f"  Users created: {users_created}")
            
            errors = result.get("errors", [])
            if errors:
                self.stdout.write(self.style.ERROR(f"\n  Errors: {len(errors)}"))
                for error in errors[:5]:
                    self.stdout.write(f"    - {error}")
                if len(errors) > 5:
                    self.stdout.write(f"    ... and {len(errors) - 5} more")
        else:
            super()._display_result(result)