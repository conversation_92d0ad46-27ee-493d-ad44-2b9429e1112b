import asyncio
import os
import sys

from asgiref.sync import sync_to_async
from decouple import config
from django.core.management.base import BaseCommand
from django.db.models import Q

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))))

import telegram_manager as tm_module
from telegram_manager.models import TelegramChat, TelegramMessage, TelegramUser

TelegramManager = tm_module.TelegramManager


class Command(BaseCommand):
    help = "Fix Telegram users - import users from messages that already exist in the database"

    def add_arguments(self, parser):
        parser.add_argument("--chat-ids", nargs="+", type=int, 
                            help="Chat IDs для обработки (по умолчанию все чаты)")
        parser.add_argument("--limit", type=int, default=1000,
                            help="Максимальное количество сообщений или пользователей для обработки из каждого чата")
        parser.add_argument("--fetch-api", action="store_true",
                            help="Получать дополнительные данные о пользователях из API Telegram")
        parser.add_argument("--process-mentions", action="store_true",
                            help="Обрабатывать упоминания пользователей в сообщениях")
        parser.add_argument("--process-actions", action="store_true",
                            help="Обрабатывать пользователей из сервисных сообщений")
        parser.add_argument("--cleanup-orphans", action="store_true",
                            help="Удалить пользователей, на которых нет ссылок из сообщений")
        parser.add_argument("--verbose", action="store_true", 
                            help="Показывать детальную информацию об обработке")
        
    def handle(self, *args, **options):
        chat_ids = options.get("chat_ids")
        limit = options.get("limit", 1000)
        fetch_api = options.get("fetch_api", False)
        process_mentions = options.get("process_mentions", False)
        process_actions = options.get("process_actions", False)
        cleanup_orphans = options.get("cleanup_orphans", False)  # New option
        verbose = options.get("verbose", False)
        
        self.stdout.write(self.style.WARNING(
            f"Восстановление пользователей с параметрами: "
            f"chat_ids={chat_ids}, limit={limit}, fetch_api={fetch_api}, "
            f"process_mentions={process_mentions}, process_actions={process_actions}, "
            f"cleanup_orphans={cleanup_orphans}, "
            f"verbose={verbose}"
        ))
        
        try:
            # First, count how many messages and users we have
            message_count = TelegramMessage.objects.count()
            user_count = TelegramUser.objects.count()
            self.stdout.write(self.style.WARNING(f"Текущее количество: {message_count} сообщений, {user_count} пользователей"))
            
            # If cleanup_orphans option is enabled, process it
            if cleanup_orphans:
                self.stdout.write(self.style.WARNING(
                    "\n\nStarting cleanup of orphaned users..."
                ))
                
                # Track stats for reporting
                removed_users = 0
                
                # Find all users
                for user in TelegramUser.objects.all():
                    # Check if this user is referenced by any messages
                    referenced = TelegramMessage.objects.filter(
                        Q(from_user_id=user.user_id) |
                        Q(mentioned_users__user_id=user.user_id) |
                        Q(action_users__user_id=user.user_id) |
                        Q(fwd_from_user_id=user.user_id) |
                        Q(via_bot_id=user.user_id)
                    ).exists()
                    
                    # If not referenced, delete the user
                    if not referenced:
                        if verbose:
                            self.stdout.write(f"  Deleting orphaned user: {user}")
                        user.delete()
                        removed_users += 1
                    
                # Report results
                remaining_users = TelegramUser.objects.count()
                self.stdout.write(self.style.SUCCESS(
                    f"Cleanup complete. Removed {removed_users} orphaned users. "
                    f"Remaining users: {remaining_users}\n\n"
                ))
            
            # API credentials
            api_id = config("API_ID", cast=int)
            api_hash = config("API_HASH")
            session_name = config("SESSION_NAME")
            
            # Use absolute path for session file in the sessions directory
            sessions_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))), "sessions")
            
            # Make sure sessions directory exists
            if not os.path.exists(sessions_dir):
                os.makedirs(sessions_dir, exist_ok=True)
                
            session_path = os.path.join(sessions_dir, session_name)
            
            # Run the async operation
            fixed_users, processed_messages = asyncio.run(
                self._fix_users_async(
                    api_id, api_hash, session_path, 
                    chat_ids, limit, fetch_api, process_mentions, process_actions, verbose
                )
            )
            
            # Count users after import
            new_user_count = TelegramUser.objects.count()
            increase = new_user_count - user_count
            
            self.stdout.write(self.style.SUCCESS(
                f"Успешно обработано {processed_messages} сообщений, восстановлено/обновлено {fixed_users} пользователей. "
                f"Новое количество пользователей: {new_user_count} (добавлено {increase})"
            ))
        
        except Exception as e:
            import traceback
            self.stdout.write(self.style.ERROR(f"Ошибка восстановления пользователей: {e!s}\n{traceback.format_exc()}"))

    async def _fix_users_async(self, api_id, api_hash, session_path, 
                           chat_ids, limit, fetch_api, process_mentions, process_actions, verbose):
        """Асинхронная функция для восстановления пользователей из сообщений"""
        manager = None
        fixed_users = 0
        processed_messages = 0
        
        try:
            # Connect to Telegram API if we need to fetch additional user info
            if fetch_api:
                manager = TelegramManager(api_id, api_hash, session_path)
                # Ensure we're properly connected and authenticated
                await manager.connect()
                
                # Make sure the connection is properly initialized for specialized managers
                if not manager.user_manager.client.is_connected():
                    await manager.user_manager.connect()
                    
                self.stdout.write("Подключено к Telegram API")
            
            # Get chats to process
            chats: list[TelegramChat]
            if chat_ids:
                chat_qs = TelegramChat.objects.filter(chat_id__in=chat_ids)
                chats = await sync_to_async(lambda: list(chat_qs))()
            else:
                chat_qs = TelegramChat.objects.all()
                chats = await sync_to_async(lambda: list(chat_qs))()
                
            self.stdout.write(f"Найдено {len(chats)} чатов для обработки")
            
            # Process each chat
            for chat in chats:
                chat_fixed_users = 0
                chat_processed = 0
                
                self.stdout.write(f"Обработка чата {chat.title} (ID: {chat.chat_id})")
                
                # Process messages without authors first
                try:
                    # Get messages without authors or with from_id but no linked user
                    msg_qs = TelegramMessage.objects.filter(
                        Q(chat=chat) & (Q(from_user__isnull=True) | Q(from_id__isnull=False, from_user__isnull=True))
                    ).order_by("-date")[:limit]
                    messages: list[TelegramMessage] = await sync_to_async(lambda: list(msg_qs))()
                    
                    self.stdout.write(f"Найдено {len(messages)} сообщений без авторов в чате {chat.title}")
                    
                    # Process each message
                    for message in messages:
                        chat_processed += 1
                        processed_messages += 1
                        
                        # Случай 1: Сообщение из канала (broadcast=True) без from_id
                        if chat.broadcast and (not message.from_id or message.from_id == 0):
                            # Создаем пользователя из самого канала
                            try:
                                user, created = await sync_to_async(TelegramUser.objects.update_or_create)(
                                    user_id=chat.chat_id,
                                    defaults={
                                        "username": chat.username or "",
                                        "first_name": chat.title or "",
                                        "last_name": "",
                                        "is_bot": False,
                                        "is_verified": chat.verified,
                                        "is_restricted": chat.restricted,
                                        "is_deleted": False,
                                        "is_premium": False,
                                    }
                                )
                                
                                # Связываем пользователя с сообщением
                                @sync_to_async
                                def update_message_user(msg, usr):
                                    msg.from_user = usr
                                    msg.save(update_fields=["from_user"])
                                    
                                await update_message_user(message, user)
                                
                                if created:
                                    chat_fixed_users += 1
                                    fixed_users += 1
                                    if verbose:
                                        self.stdout.write(f"Создан пользователь для канала: {chat.title}")
                                else:
                                    if verbose:
                                        self.stdout.write(f"Обновлен пользователь для канала: {chat.title}")
                                
                            except Exception as e:
                                self.stdout.write(self.style.ERROR(f"Ошибка при создании пользователя для канала {chat.title}: {e}"))
                        
                        # Случай 2: Сообщение с post_author (подписанное сообщение канала)
                        elif chat.broadcast and message.post_author:
                            try:
                                # Создаем пользователя на основе post_author
                                author_name = message.post_author
                                import hashlib
                                author_hash = int(hashlib.md5(author_name.encode()).hexdigest(), 16) % (10 ** 10)
                                author_id = -1 * author_hash
                                
                                user, created = await sync_to_async(TelegramUser.objects.update_or_create)(
                                    user_id=author_id,
                                    defaults={
                                        "username": "",
                                        "first_name": author_name,
                                        "last_name": "(Channel Author)",
                                        "is_bot": False,
                                        "is_verified": False,
                                        "is_restricted": False,
                                        "is_deleted": False,
                                        "is_premium": False,
                                    }
                                )
                                
                                # Связываем пользователя с сообщением
                                @sync_to_async
                                def update_message_user(msg, usr):
                                    msg.from_user = usr
                                    msg.save(update_fields=["from_user"])
                                    
                                await update_message_user(message, user)
                                
                                if created:
                                    chat_fixed_users += 1
                                    fixed_users += 1
                                    if verbose:
                                        self.stdout.write(f"Создан пользователь для автора канала: {author_name}")
                                else:
                                    if verbose:
                                        self.stdout.write(f"Обновлен пользователь для автора канала: {author_name}")
                                    
                            except Exception as e:
                                self.stdout.write(self.style.ERROR(f"Ошибка при создании пользователя для автора канала {message.post_author}: {e}"))
                        
                        # Случай 3: Сообщение с from_id без привязанного пользователя
                        elif message.from_id and message.from_id != 0:
                            try:
                                user_data = None
                                user_id = message.from_id
                                
                                # Получаем данные пользователя из API, если включено
                                if fetch_api and manager:
                                    user_data = await manager.get_user_by_id(user_id)
                                
                                if user_data:
                                    # Создаем/обновляем с полными данными
                                    user, created = await sync_to_async(TelegramUser.objects.update_or_create)(
                                        user_id=user_data["user_id"],
                                        defaults={
                                            "username": user_data.get("username", ""),
                                            "first_name": user_data.get("first_name", ""),
                                            "last_name": user_data.get("last_name", ""),
                                            "phone": user_data.get("phone", ""),
                                            "is_bot": user_data.get("is_bot", False),
                                            "is_verified": user_data.get("is_verified", False),
                                            "is_restricted": user_data.get("is_restricted", False),
                                            "is_deleted": user_data.get("is_deleted", False),
                                            "is_premium": user_data.get("is_premium", False),
                                            "profile_photo_id": user_data.get("profile_photo_id"),
                                            "lang_code": user_data.get("lang_code"),
                                            "last_seen_date": user_data.get("last_seen_date"),
                                        }
                                    )
                                else:
                                    # Создаем/обновляем с минимальными данными
                                    user, created = await sync_to_async(TelegramUser.objects.update_or_create)(
                                        user_id=user_id,
                                        defaults={
                                            "username": "",
                                            "first_name": f"User {user_id}",
                                            "last_name": "",
                                        }
                                    )
                                
                                # Связываем пользователя с сообщением
                                @sync_to_async
                                def update_message_user(msg, usr):
                                    msg.from_user = usr
                                    msg.save(update_fields=["from_user"])
                                    
                                await update_message_user(message, user)
                                
                                if created:
                                    chat_fixed_users += 1
                                    fixed_users += 1
                                    if verbose:
                                        self.stdout.write(f"Создан пользователь для ID {user_id}")
                                else:
                                    if verbose:
                                        self.stdout.write(f"Обновлен пользователь для ID {user_id}")
                                
                            except Exception as e:
                                self.stdout.write(self.style.ERROR(f"Ошибка при создании пользователя для ID {message.from_id}: {e}"))
                                
                        # Обработка упоминаний, если включено
                        if process_mentions and message.entities:
                            await self._process_mentions(manager, message, fetch_api, verbose)
                        
                        # Обработка сервисных действий, если включено
                        if process_actions and message.action:
                            await self._process_action_users(manager, message, fetch_api, verbose)
                    
                    self.stdout.write(self.style.SUCCESS(
                        f"Обработано {chat_processed} сообщений, восстановлено {chat_fixed_users} пользователей в чате {chat.title}"
                    ))
                    
                except Exception as e:
                    self.stdout.write(self.style.ERROR(f"Ошибка при обработке чата {chat.title}: {e}"))
            
            return fixed_users, processed_messages
        
        finally:
            # Отключаемся от API Telegram
            if manager and hasattr(manager, "client") and manager.client.is_connected():
                await manager.client.disconnect()
    
    async def _process_mentions(self, manager, message, fetch_api, verbose):
        """Обработка упоминаний пользователей в сообщении"""
        if not message.entities:
            return
            
        mentioned_count = 0
        entities = message.entities
        
        for entity in entities:
            # Обрабатываем только упоминания пользователей
            if entity.get("type") in ["MessageEntityMention", "MessageEntityTextMention"]:
                mentioned_user_id = entity.get("user_id")
                
                # MessageEntityTextMention с прямым ID пользователя
                if mentioned_user_id:
                    try:
                        user_data = None
                        if fetch_api and manager:
                            user_data = await manager.get_user_by_id(mentioned_user_id)
                            
                        if user_data:
                            mentioned_user, created = await sync_to_async(TelegramUser.objects.update_or_create)(
                                user_id=mentioned_user_id,
                                defaults={
                                    "username": user_data.get("username", ""),
                                    "first_name": user_data.get("first_name", ""),
                                    "last_name": user_data.get("last_name", ""),
                                    "is_bot": user_data.get("is_bot", False),
                                    "is_verified": user_data.get("is_verified", False),
                                    "is_restricted": user_data.get("is_restricted", False),
                                    "is_deleted": user_data.get("is_deleted", False),
                                    "is_premium": user_data.get("is_premium", False),
                                }
                            )
                        else:
                            mentioned_user, created = await sync_to_async(TelegramUser.objects.update_or_create)(
                                user_id=mentioned_user_id,
                                defaults={
                                    "username": "",
                                    "first_name": "Mentioned User",
                                    "last_name": f"#{mentioned_user_id}",
                                }
                            )
                        
                        # Добавляем связь с сообщением
                        @sync_to_async
                        def add_mentioned_user(msg, user):
                            msg.mentioned_users.add(user)
                            
                        await add_mentioned_user(message, mentioned_user)
                        mentioned_count += 1
                        
                        if created and verbose:
                            self.stdout.write(f"Создан упомянутый пользователь ID {mentioned_user_id}")
                        
                    except Exception as e:
                        self.stdout.write(self.style.ERROR(f"Ошибка при обработке упомянутого пользователя {mentioned_user_id}: {e}"))
                
                # MessageEntityMention с @username
                elif entity.get("type") == "MessageEntityMention" and message.text:
                    offset = entity.get("offset", 0)
                    length = entity.get("length", 0)
                    
                    if offset is not None and length is not None and offset + length <= len(message.text):
                        username = message.text[offset+1:offset+length]
                        
                        try:
                            # Пытаемся получить пользователя по username через API
                            if fetch_api and manager:
                                try:
                                    mentioned_entity = await manager.client.get_entity(username)
                                    if mentioned_entity and hasattr(mentioned_entity, "id"):
                                        mentioned_user_id = mentioned_entity.id
                                        
                                        mentioned_user, created = await sync_to_async(TelegramUser.objects.update_or_create)(
                                            user_id=mentioned_user_id,
                                            defaults={
                                                "username": getattr(mentioned_entity, "username", username),
                                                "first_name": getattr(mentioned_entity, "first_name", ""),
                                                "last_name": getattr(mentioned_entity, "last_name", ""),
                                                "is_bot": getattr(mentioned_entity, "bot", False),
                                            }
                                        )
                                        
                                        # Добавляем связь с сообщением
                                        @sync_to_async
                                        def add_mentioned_user(msg, user):
                                            msg.mentioned_users.add(user)
                                            
                                        await add_mentioned_user(message, mentioned_user)
                                        mentioned_count += 1
                                        
                                        if created and verbose:
                                            self.stdout.write(f"Создан упомянутый пользователь @{username}")
                                            
                                        continue
                                        
                                except Exception as e:
                                    if verbose:
                                        self.stdout.write(self.style.WARNING(f"Ошибка при получении данных пользователя @{username}: {e}"))
                            
                            # Создаем пользователя на основе username
                            import hashlib
                            username_hash = int(hashlib.md5(username.encode()).hexdigest(), 16) % (10 ** 10)
                            username_user_id = -2 * username_hash
                            
                            mentioned_user, created = await sync_to_async(TelegramUser.objects.update_or_create)(
                                user_id=username_user_id,
                                defaults={
                                    "username": username,
                                    "first_name": "@" + username,
                                    "last_name": "(Username Mention)",
                                    "is_bot": False,
                                }
                            )
                            
                            # Добавляем связь с сообщением
                            @sync_to_async
                            def add_mentioned_user(msg, user):
                                msg.mentioned_users.add(user)
                                
                            await add_mentioned_user(message, mentioned_user)
                            mentioned_count += 1
                            
                            if created and verbose:
                                self.stdout.write(f"Создан пользователь на основе имени @{username}")
                            
                        except Exception as e:
                            self.stdout.write(self.style.ERROR(f"Ошибка при обработке упомянутого пользователя @{username}: {e}"))
        
        if mentioned_count > 0 and verbose:
            self.stdout.write(f"Добавлено {mentioned_count} упомянутых пользователей к сообщению {message.message_id}")
    
    async def _process_action_users(self, manager, message, fetch_api, verbose):
        """Обработка пользователей из сервисных сообщений (действий)"""
        if not message.action:
            return
            
        action_count = 0
        action_data = message.action
        action_type = action_data.get("type", "")
        
        # Обрабатываем только действия с пользователями
        if action_type in ["MessageActionChatAddUser", "MessageActionChatJoinedByLink", "MessageActionChatDeleteUser"]:
            # Извлекаем ID пользователей
            user_ids = []
            
            if action_data.get("user_id"):
                user_ids.append(action_data["user_id"])
                
            if action_data.get("users"):
                user_ids.extend(action_data["users"])
            
            # Обрабатываем каждого пользователя
            for action_user_id in user_ids:
                try:
                    user_data = None
                    if fetch_api and manager:
                        user_data = await manager.get_user_by_id(action_user_id)
                        
                    if user_data:
                        action_user, created = await sync_to_async(TelegramUser.objects.update_or_create)(
                            user_id=action_user_id,
                            defaults={
                                "username": user_data.get("username", ""),
                                "first_name": user_data.get("first_name", ""),
                                "last_name": user_data.get("last_name", ""),
                                "is_bot": user_data.get("is_bot", False),
                                "is_verified": user_data.get("is_verified", False),
                                "is_restricted": user_data.get("is_restricted", False),
                                "is_deleted": user_data.get("is_deleted", False),
                                "is_premium": user_data.get("is_premium", False),
                            }
                        )
                    else:
                        action_user, created = await sync_to_async(TelegramUser.objects.update_or_create)(
                            user_id=action_user_id,
                            defaults={
                                "username": "",
                                "first_name": "Action User",
                                "last_name": f"#{action_user_id}",
                            }
                        )
                    
                    # Добавляем связь с сообщением
                    @sync_to_async
                    def add_action_user(msg, user):
                        msg.action_users.add(user)
                        
                    await add_action_user(message, action_user)
                    action_count += 1
                    
                    if created and verbose:
                        self.stdout.write(f"Создан пользователь действия {action_user_id}")
                    
                except Exception as e:
                    self.stdout.write(self.style.ERROR(f"Ошибка при обработке пользователя действия {action_user_id}: {e}"))
        
        if action_count > 0 and verbose:
            self.stdout.write(f"Добавлено {action_count} пользователей действия к сообщению {message.message_id}")