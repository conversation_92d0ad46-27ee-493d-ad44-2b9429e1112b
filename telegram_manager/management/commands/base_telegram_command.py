"""
Новая базовая команда для Telegram операций с использованием базовой архитектуры.
"""

import asyncio
import os
from typing import Any

from decouple import config
from django.conf import settings

from core.exceptions import ConfigurationError
from core.management.commands.base import BaseSocialMediaCommand
from telegram_manager.models import TelegramChat
from telegram_manager.services import TelegramChatService, TelegramMessageService, TelegramUserService


class BaseTelegramCommand(BaseSocialMediaCommand):
    """
    Базовая команда для операций с Telegram API.
    Наследует функциональность из BaseSocialMediaCommand.
    """
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        
        # Инициализируем сервисы
        self.chat_service = TelegramChatService()
        self.user_service = TelegramUserService()
        self.message_service = TelegramMessageService()
        
        # Telegram API manager будет инициализирован при необходимости
        self._telegram_manager = None
    
    def get_credentials(self) -> dict[str, Any]:
        """
        Получает учетные данные для Telegram API.
        
        Returns:
            Словарь с учетными данными
            
        Raises:
            ConfigurationError: Если учетные данные не настроены
        """
        try:
            # Try to get from Django settings first (for tests), then from environment
            api_id = getattr(settings, "TELEGRAM_API_ID", None)
            if api_id is None:
                api_id = config("API_ID", cast=int)
                
            api_hash = getattr(settings, "TELEGRAM_API_HASH", None)
            if api_hash is None:
                api_hash = config("API_HASH")
                
            session_name = getattr(settings, "TELEGRAM_SESSION_NAME", None)
            if session_name is None:
                session_name = config("SESSION_NAME", default="telegram_session")
        except Exception as e:
            raise ConfigurationError(
                f"Failed to get Telegram credentials: {e!s}. "
                "Make sure API_ID, API_HASH are set in environment"
            )
        
        # Определяем путь к сессии
        session_path = self._get_session_path(session_name)
        
        return {
            "api_id": api_id,
            "api_hash": api_hash,
            "session_path": session_path,
            "session_name": session_name
        }
    
    def _get_session_path(self, session_name: str) -> str:
        """
        Определяет путь к файлу сессии.
        
        Args:
            session_name: Имя сессии
            
        Returns:
            Полный путь к файлу сессии
        """
        # Определяем корневую директорию проекта
        project_root = os.path.dirname(
            os.path.dirname(
                os.path.dirname(
                    os.path.dirname(
                        os.path.abspath(__file__)
                    )
                )
            )
        )
        
        # Директория для сессий
        sessions_dir = os.path.join(project_root, "telegram_manager", "sessions")
        
        # Создаем директорию если не существует
        os.makedirs(sessions_dir, exist_ok=True)
        
        return os.path.join(sessions_dir, session_name)
    
    def validate_credentials(self, credentials: dict[str, Any]) -> bool:
        """
        Валидирует учетные данные Telegram.
        
        Args:
            credentials: Учетные данные
            
        Returns:
            True если валидны
        """
        required_keys = ["api_id", "api_hash", "session_path"]
        
        for key in required_keys:
            if key not in credentials or not credentials[key]:
                self.log_progress(f"Missing required credential: {key}", "error")
                return False
        
        # Проверяем, что api_id это число
        if not isinstance(credentials["api_id"], int):
            self.log_progress("api_id must be an integer", "error")
            return False
        
        return True
    
    def get_telegram_manager(self, manager_class: type):
        """
        Получает или создает экземпляр Telegram manager.
        
        Args:
            manager_class: Класс менеджера (например, TelegramChatManager)
            
        Returns:
            Экземпляр менеджера
            
        Raises:
            ConfigurationError: При ошибке конфигурации
        """
        if self._telegram_manager is None:
            credentials = self.get_credentials()
            
            if not self.validate_credentials(credentials):
                raise ConfigurationError("Invalid Telegram credentials")
            
            self._telegram_manager = manager_class(
                credentials["api_id"],
                credentials["api_hash"],
                credentials["session_path"]
            )
        
        return self._telegram_manager
    
    def execute_async(self, async_func, *args, **kwargs):
        """
        Выполняет асинхронную функцию с обработкой ошибок.
        
        Args:
            async_func: Асинхронная функция
            *args: Позиционные аргументы
            **kwargs: Именованные аргументы
            
        Returns:
            Результат функции или None при ошибке
        """
        try:
            # Создаем новый event loop для выполнения
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            try:
                return loop.run_until_complete(async_func(*args, **kwargs))
            finally:
                loop.close()
                
        except Exception as e:
            self.handle_exception(f"executing {async_func.__name__}", e)
            return None
    
    def add_custom_arguments(self, parser):
        """
        Добавляет специфичные для Telegram аргументы.
        
        Args:
            parser: ArgumentParser
        """
        parser.add_argument(
            "--limit",
            type=int,
            default=None,
            help="Максимальное количество элементов для обработки"
        )
        
        parser.add_argument(
            "--batch-size",
            type=int,
            default=100,
            help="Размер пакета для обработки (по умолчанию: 100)"
        )
        
        parser.add_argument(
            "--chat-id",
            type=int,
            help="ID конкретного чата для обработки"
        )
        
        parser.add_argument(
            "--username",
            type=str,
            help="Username пользователя или чата"
        )
    
    def log_import_stats(self, entity_type: str, total: int, created: int, updated: int = 0):
        """
        Логирует статистику импорта.
        
        Args:
            entity_type: Тип сущности (users, chats, messages)
            total: Всего обработано
            created: Создано новых
            updated: Обновлено существующих
        """
        self.log_progress(
            f"\n{entity_type.capitalize()} import complete:",
            "success"
        )
        self.log_progress(f"  Total processed: {total}", "info")
        self.log_progress(f"  Created: {created}", "info")
        
        if updated:
            self.log_progress(f"  Updated: {updated}", "info")
    
    def cleanup_on_exit(self):
        """
        Очистка ресурсов при завершении команды.
        """
        if self._telegram_manager:
            try:
                # Закрываем соединение с Telegram
                self.execute_async(self._telegram_manager.disconnect)
            except Exception as e:
                self.log_progress(f"Error disconnecting: {e}", "warning")
    
    def format_chat_info(self, chat: TelegramChat) -> str:
        """
        Форматирует информацию о чате для отображения.
        
        Args:
            chat: Объект TelegramChat
            
        Returns:
            Отформатированная строка с информацией о чате
        """
        if hasattr(chat, "username") and chat.username:
            return f"{chat.title} (@{chat.username})"
        return str(chat.title) if hasattr(chat, "title") else str(chat)