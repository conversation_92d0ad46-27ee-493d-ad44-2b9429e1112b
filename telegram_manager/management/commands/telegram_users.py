"""
Рефакторинг команды импорта пользователей из Telegram.
Использует новую базовую архитектуру.
"""

from datetime import datetime
from typing import Any

from core.exceptions import DataValidationError
from telegram_manager.management.commands.base_telegram_command import BaseTelegramCommand
from telegram_manager.models import TelegramChat, TelegramMessage
from telegram_manager.services import TelegramChatService, TelegramMessageService, TelegramUserService
from telegram_manager.telegram_api.managers import TelegramManager


class Command(BaseTelegramCommand):
    """
    Команда для импорта пользователей из Telegram API.
    """
    help = "Import users from Telegram API into the database"
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.user_service = TelegramUserService()
        self.chat_service = TelegramChatService()
        self.message_service = TelegramMessageService()
    
    def add_custom_arguments(self, parser):
        """Добавляет аргументы специфичные для импорта пользователей."""
        super().add_custom_arguments(parser)
        
        parser.add_argument(
            "--extract-from-messages",
            action="store_true",
            help="Extract users from existing messages in the database"
        )
        
        parser.add_argument(
            "--from-chat",
            type=int,
            help="Import users from a specific chat ID"
        )
        
        parser.add_argument(
            "--update-existing",
            action="store_true",
            help="Update existing users with new data"
        )
    
    def execute_command(self, credentials: dict[str, Any], 
                       date_range: tuple[datetime, datetime] | None, 
                       **options) -> Any:
        """
        Выполняет основную логику команды.
        
        Args:
            credentials: Учетные данные Telegram
            date_range: Диапазон дат для фильтрации
            **options: Опции команды
            
        Returns:
            Результат выполнения
        """
        extract_from_messages = options.get("extract_from_messages", False)
        from_chat = options.get("from_chat")
        limit = options.get("limit")
        
        if extract_from_messages:
            # Извлекаем пользователей из существующих сообщений
            result = self._extract_users_from_messages(limit)
        elif from_chat:
            # Импортируем пользователей из конкретного чата
            result = self.execute_async(
                self._import_users_from_chat_async,
                credentials,
                from_chat,
                options
            )
        else:
            # Импортируем пользователей из всех доступных чатов
            result = self.execute_async(
                self._import_all_users_async,
                credentials,
                options
            )
        
        if result:
            total_processed, total_created, total_updated = result
            self.log_import_stats("users", total_processed, total_created, total_updated)
        
        return result
    
    def _extract_users_from_messages(self, limit: int | None = None) -> tuple[int, int, int]:
        """
        Извлекает пользователей из существующих сообщений в БД.
        
        Args:
            limit: Максимальное количество сообщений для обработки
            
        Returns:
            Кортеж (total_processed, created, updated)
        """
        self.log_progress("Extracting users from existing messages", "info")
        
        # Получаем сообщения с from_user_id
        messages_query = TelegramMessage.objects.exclude(from_user_id__isnull=True)
        
        if limit:
            messages_query = messages_query[:limit]
        
        user_ids = set()
        for message in messages_query:
            if message.from_user_id:
                user_ids.add(message.from_user_id)
            
            # Также собираем mentioned users
            mentioned_users = message.mentioned_users.all()
            for user in mentioned_users:
                user_ids.add(user.user_id)
        
        self.log_progress(f"Found {len(user_ids)} unique user IDs", "info")
        
        # Создаем пользователей
        created = 0
        updated = 0
        
        for user_id in user_ids:
            result = self.user_service.get_or_create_user_by_id(user_id)
            if result:
                user, is_created = result
                if is_created:
                    created += 1
                else:
                    updated += 1
        
        return len(user_ids), created, updated
    
    async def _import_users_from_chat_async(self, credentials: dict[str, Any],
                                          chat_id: int,
                                          options: dict[str, Any]) -> tuple[int, int, int]:
        """
        Асинхронно импортирует пользователей из конкретного чата.
        
        Args:
            credentials: Учетные данные
            chat_id: ID чата
            options: Опции команды
            
        Returns:
            Кортеж (total_processed, created, updated)
        """
        # Получаем менеджер
        manager = self.get_telegram_manager(TelegramManager)
        
        try:
            # Подключаемся к Telegram
            await manager.connect()
            
            # Получаем участников чата
            self.log_progress(f"Getting participants from chat {chat_id}", "info")
            participants = await manager.get_chat_participants(chat_id, limit=options.get("limit"))
            
            # Обрабатываем участников
            created = 0
            updated = 0
            
            for participant in participants:
                user_data = self._extract_user_data(participant)
                user, is_created = await self._save_user(user_data, options.get("update_existing", False))
                
                if is_created:
                    created += 1
                else:
                    updated += 1
            
            return len(participants), created, updated
            
        finally:
            await manager.disconnect()
    
    async def _import_all_users_async(self, credentials: dict[str, Any],
                                    options: dict[str, Any]) -> tuple[int, int, int]:
        """
        Асинхронно импортирует пользователей из всех доступных чатов.
        
        Args:
            credentials: Учетные данные
            options: Опции команды
            
        Returns:
            Кортеж (total_processed, created, updated)
        """
        # Получаем менеджер
        manager = self.get_telegram_manager(TelegramManager)
        
        try:
            # Подключаемся к Telegram
            await manager.connect()
            
            # Получаем все чаты
            chats = TelegramChat.objects.all()
            
            if not chats.exists():
                self.log_progress(
                    "No chats found. Please import chats first using telegram_chats command.",
                    "warning"
                )
                return 0, 0, 0
            
            total_processed = 0
            total_created = 0
            total_updated = 0
            
            for chat in chats:
                self.log_progress(f"Processing chat: {chat.title}", "info")
                
                try:
                    participants = await manager.get_chat_participants(
                        chat.chat_id, 
                        limit=options.get("limit")
                    )
                    
                    for participant in participants:
                        user_data = self._extract_user_data(participant)
                        user, is_created = await self._save_user(
                            user_data, 
                            options.get("update_existing", False)
                        )
                        
                        if is_created:
                            total_created += 1
                        else:
                            total_updated += 1
                        
                        total_processed += 1
                        
                except Exception as e:
                    self.log_progress(
                        f"Error processing chat {chat.title}: {e}",
                        "error"
                    )
                    continue
            
            return total_processed, total_created, total_updated
            
        finally:
            await manager.disconnect()
    
    def _extract_user_data(self, participant) -> dict[str, Any]:
        """
        Извлекает данные пользователя из участника.
        
        Args:
            participant: Объект участника
            
        Returns:
            Словарь с данными пользователя
        """
        # Обработка тестовых данных (словари)
        if isinstance(participant, dict):
            return {
                "user_id": participant.get("id"),
                "username": participant.get("username"),
                "first_name": participant.get("first_name"),
                "last_name": participant.get("last_name"),
                "is_bot": participant.get("bot", False),
                "is_verified": participant.get("verified", False),
                "is_restricted": participant.get("restricted", False),
                "is_scam": participant.get("scam", False),
                "is_fake": participant.get("fake", False),
                "is_premium": participant.get("premium", False),
            }
        
        # Обработка реальных объектов Telegram
        user = participant if not hasattr(participant, "user") else participant.user
        
        return {
            "user_id": getattr(user, "id", None),
            "username": getattr(user, "username", None),
            "first_name": getattr(user, "first_name", None),
            "last_name": getattr(user, "last_name", None),
            "is_bot": getattr(user, "bot", False),
            "is_verified": getattr(user, "verified", False),
            "is_restricted": getattr(user, "restricted", False),
            "is_scam": getattr(user, "scam", False),
            "is_fake": getattr(user, "fake", False),
            "is_premium": getattr(user, "premium", False),
        }
    
    async def _save_user(self, user_data: dict[str, Any], update_existing: bool) -> tuple[Any, bool]:
        """
        Сохраняет или обновляет пользователя.
        
        Args:
            user_data: Данные пользователя
            update_existing: Обновлять существующих пользователей
            
        Returns:
            Кортеж (user, is_created)
        """
        user_id = user_data.get("user_id")
        
        if not user_id:
            raise DataValidationError("User ID is required")
        
        # Используем сервис для создания или обновления
        if update_existing:
            user, is_created = self.user_service.update_or_create(
                user_id=user_id,
                defaults=user_data
            )
        else:
            user, is_created = self.user_service.get_or_create(
                user_id=user_id,
                defaults=user_data
            )
        
        return user, is_created