"""
Рефакторинг команды импорта чатов из Telegram.
Использует новую базовую архитектуру.
"""

from io import BytesIO
from typing import Any

from django.core.files.base import ContentFile
from telethon.tl.types import Channel, ChatPhotoEmpty

from core.exceptions import APIError
from telegram_manager.management.commands.base_telegram_command import BaseTelegramCommand
from telegram_manager.services import TelegramChatService
from telegram_manager.telegram_api.managers import TelegramManager


class Command(BaseTelegramCommand):
    """
    Команда для импорта чатов из Telegram API.
    """
    help = "Import chats from Telegram API into the database"
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.chat_service = TelegramChatService()
    
    def add_custom_arguments(self, parser):
        """Добавляет аргументы специфичные для импорта чатов."""
        super().add_custom_arguments(parser)
        
        parser.add_argument(
            "--include-private",
            action="store_true",
            help="Include private chats"
        )
        parser.add_argument(
            "--include-groups",
            action="store_true",
            help="Include groups"
        )
        parser.add_argument(
            "--include-channels",
            action="store_true",
            help="Include channels"
        )
        parser.add_argument(
            "--include-supergroups",
            action="store_true",
            help="Include supergroups"
        )
        parser.add_argument(
            "--download-photos",
            action="store_true",
            default=True,
            help="Download chat profile photos (default: True)"
        )
    
    def execute_command(self, credentials: dict[str, Any], 
                       date_range: tuple | None, **options) -> Any:
        """
        Выполняет основную логику команды.
        
        Args:
            credentials: Учетные данные Telegram
            date_range: Диапазон дат (не используется для чатов)
            **options: Опции команды
            
        Returns:
            Результат выполнения
        """
        # Определяем типы чатов для импорта
        include_types = self._determine_chat_types(options)
        
        # Логируем параметры
        self.log_progress(
            f"Importing chats with parameters: "
            f"limit={options.get('limit', 'all')}, "
            f"types={include_types}",
            "info"
        )
        
        # Выполняем асинхронный импорт
        result = self.execute_async(
            self._import_chats_async,
            credentials,
            include_types,
            options
        )
        
        if result:
            created, updated = result
            self.log_import_stats("chats", created + updated, created, updated)
        
        return result
    
    def _determine_chat_types(self, options: dict[str, Any]) -> dict[str, bool]:
        """
        Определяет какие типы чатов нужно импортировать.
        
        Args:
            options: Опции команды
            
        Returns:
            Словарь с типами чатов
        """
        include_private = options.get("include_private", False)
        include_groups = options.get("include_groups", False)
        include_channels = options.get("include_channels", False)
        include_supergroups = options.get("include_supergroups", False)
        
        # Если ничего не указано, включаем все типы кроме приватных чатов
        if not any([include_private, include_groups, include_channels, include_supergroups]):
            # По умолчанию не включаем приватные чаты для безопасности
            include_private = False
            include_groups = include_channels = include_supergroups = True
        
        return {
            "include_private": include_private,
            "include_groups": include_groups,
            "include_channels": include_channels,
            "include_supergroups": include_supergroups
        }
    
    async def _import_chats_async(self, credentials: dict[str, Any], 
                                 include_types: dict[str, bool], 
                                 options: dict[str, Any]) -> tuple[int, int]:
        """
        Асинхронный импорт чатов.
        
        Args:
            credentials: Учетные данные
            include_types: Типы чатов для импорта
            options: Опции команды
            
        Returns:
            Кортеж (создано, обновлено)
        """
        # Создаем менеджер Telegram
        manager = self.get_telegram_manager(TelegramManager)
        
        created_count = 0
        updated_count = 0
        
        try:
            # Подключаемся к Telegram
            await manager.connect()
            
            # Получаем диалоги
            dialogs = await manager.get_dialogs(
                limit=options.get("limit"),
                **include_types
            )
            
            self.log_progress(f"Found {len(dialogs)} chats to process from selected types", "info")
            
            # Обрабатываем чаты пакетами
            batch_size = options.get("batch_size", 10)  # Меньший размер для чатов
            
            for i in range(0, len(dialogs), batch_size):
                batch = dialogs[i:i + batch_size]
                batch_created, batch_updated = await self._process_chat_batch(
                    batch, 
                    manager,
                    options.get("download_photos", True)
                )
                
                created_count += batch_created
                updated_count += batch_updated
                
                self.log_progress(
                    f"Processed batch {i//batch_size + 1}: "
                    f"{batch_created} new, {batch_updated} updated",
                    "info"
                )
            
            return created_count, updated_count
            
        except Exception as e:
            raise APIError(f"Failed to import chats: {e!s}") from e
        finally:
            # Отключаемся от Telegram
            await manager.disconnect()
    
    async def _process_chat_batch(self, dialogs: list, 
                                 manager: TelegramManager,
                                 download_photos: bool) -> tuple[int, int]:
        """
        Обрабатывает пакет чатов.
        
        Args:
            dialogs: Список диалогов
            manager: Telegram manager
            download_photos: Загружать ли фото профилей
            
        Returns:
            Кортеж (создано, обновлено)
        """
        created = 0
        updated = 0
        
        for dialog in dialogs:
            try:
                # Подготавливаем данные чата
                chat_data = self._extract_chat_data(dialog)
                
                # Логируем информацию о чате
                chat_name = chat_data.get("title") or chat_data.get("username") or f"ID:{chat_data.get('chat_id')}"
                is_megagroup = dialog.get("entity", {}).get("megagroup", False) if isinstance(dialog, dict) else False
                is_broadcast = dialog.get("entity", {}).get("broadcast", False) if isinstance(dialog, dict) else False
                
                if self.verbosity >= 2:  # Only log in verbose mode
                    self.log_progress(
                        f"Processing chat: {chat_name} "
                        f"(megagroup={is_megagroup}, broadcast={is_broadcast})",
                        "info"
                    )
                
                # Загружаем фото если нужно
                if download_photos:
                    # Получаем entity в зависимости от типа dialog
                    if isinstance(dialog, dict):
                        # Сначала проверяем наличие _raw_entity (оригинальный Telethon объект)
                        entity = dialog.get("_raw_entity")
                        if not entity:
                            # Фолбэк на словарь
                            entity = dialog.get("entity", {})
                            if self.verbosity >= 2:
                                self.log_progress(
                                    f"No _raw_entity found for {chat_name}, using dict entity",
                                    "warning"
                                )
                    else:
                        entity = dialog.entity
                    
                    if self.verbosity >= 2:
                        self.log_progress(
                            f"Photo download enabled for {chat_name}, entity type: {type(entity).__name__}",
                            "debug"
                        )
                    
                    photo_data = await self._download_chat_photo(
                        entity, 
                        manager
                    )
                    
                    if photo_data:
                        self.log_progress(
                            f"Photo data retrieved for {chat_name} ({len(photo_data)} bytes)",
                            "info"
                        )
                else:
                    photo_data = None
                    if self.verbosity >= 2:
                        self.log_progress(
                            f"Photo download disabled for {chat_name}",
                            "debug"
                        )
                
                # Получаем ID диалога
                if isinstance(dialog, dict):
                    entity = dialog.get("entity", {})
                    dialog_id = dialog.get("id", entity.get("id") if isinstance(entity, dict) else None)
                else:
                    dialog_id = dialog.id
                
                # Создаем или обновляем чат
                chat, is_created = await self._save_chat(
                    dialog_id,
                    chat_data,
                    photo_data
                )
                
                if is_created:
                    created += 1
                else:
                    updated += 1
                    
            except Exception as e:
                # Получаем имя чата для логирования
                if isinstance(dialog, dict):
                    entity = dialog.get("entity", {})
                    chat_name = dialog.get("name", entity.get("title", "Unknown") if isinstance(entity, dict) else "Unknown")
                else:
                    chat_name = getattr(dialog, "name", "Unknown")
                
                self.log_progress(
                    f"Error processing chat {chat_name}: {e}",
                    "error"
                )
                continue
        
        return created, updated
    
    def _extract_chat_data(self, dialog) -> dict[str, Any]:
        """
        Извлекает данные чата из диалога Telegram.
        
        Args:
            dialog: Объект диалога или словарь (для тестов)
            
        Returns:
            Словарь с данными чата
        """
        # Обработка тестовых данных (словари)
        if isinstance(dialog, dict):
            entity = dialog.get("entity", {})
            dialog_id = dialog.get("id", entity.get("id") if isinstance(entity, dict) else None)
            dialog_name = dialog.get("name", entity.get("title", "") if isinstance(entity, dict) else "")
        else:
            # Обработка реальных объектов Telegram
            entity = dialog.entity
            dialog_id = dialog.id
            dialog_name = dialog.name
        
        # Базовые данные
        data = {
            "title": dialog_name or f"Chat {dialog_id}",
            "chat_id": dialog_id
        }
        
        # Специфичные поля для каналов/групп
        if isinstance(entity, dict):
            # Обработка тестовых данных
            data.update({
                "username": entity.get("username"),
                "creator": entity.get("creator", False),
                "left": entity.get("left", False),
                "broadcast": entity.get("broadcast", False),
                "verified": entity.get("verified", False),
                "megagroup": entity.get("megagroup", False),
                "restricted": entity.get("restricted", False),
                "min": entity.get("min", False),
                "scam": entity.get("scam", False),
                "has_link": entity.get("has_link", False),
                "has_geo": entity.get("has_geo", False),
                "participants_count": entity.get("participants_count"),
                "date": entity.get("date"),
            })
        elif isinstance(entity, Channel):
            # Обработка реальных объектов Channel
            data.update({
                "username": getattr(entity, "username", None),
                "creator": getattr(entity, "creator", False),
                "left": getattr(entity, "left", False),
                "broadcast": getattr(entity, "broadcast", False),
                "verified": getattr(entity, "verified", False),
                "megagroup": getattr(entity, "megagroup", False),
                "restricted": getattr(entity, "restricted", False),
                "min": getattr(entity, "min", False),
                "scam": getattr(entity, "scam", False),
                "has_link": getattr(entity, "has_link", False),
                "has_geo": getattr(entity, "has_geo", False),
                "participants_count": getattr(entity, "participants_count", None),
                "date": getattr(entity, "date", None),
            })
            
            # ID фото профиля
            if hasattr(entity, "photo") and entity.photo and not isinstance(entity.photo, ChatPhotoEmpty):
                if hasattr(entity.photo, "photo_big"):
                    data["photo_id"] = entity.photo.photo_big.id
        
        return data
    
    async def _download_chat_photo(self, entity, manager: TelegramManager) -> bytes | None:
        """
        Загружает фото профиля чата.
        
        Args:
            entity: Сущность чата
            manager: Telegram manager
            
        Returns:
            Байты фото или None
        """
        # Если entity - это словарь, не можем скачать фото
        if isinstance(entity, dict):
            self.log_progress(
                f"Cannot download photo from dict entity: {entity.get('title', entity.get('id', 'unknown'))}",
                "warning"
            )
            return None
        
        # Log entity type and ID for debugging
        entity_info = f"Entity type: {type(entity).__name__}, ID: {getattr(entity, 'id', 'unknown')}"
        if self.verbosity >= 2:
            self.log_progress(f"Checking photo for {entity_info}", "debug")
        
        if not hasattr(entity, "photo") or not entity.photo:
            if self.verbosity >= 2:
                self.log_progress(
                    f"Entity has no photo attribute or photo is None for {entity_info}",
                    "debug"
                )
            return None
            
        if isinstance(entity.photo, ChatPhotoEmpty):
            if self.verbosity >= 2:
                self.log_progress(
                    f"Entity has empty photo (ChatPhotoEmpty) for {entity_info}",
                    "debug"
                )
            return None
        
        # Log photo details
        if self.verbosity >= 2:
            photo_type = type(entity.photo).__name__
            self.log_progress(
                f"Photo found for {entity_info}, photo type: {photo_type}",
                "debug"
            )
        
        try:
            self.log_progress(
                f"Attempting to download photo for {getattr(entity, 'title', getattr(entity, 'username', entity_info))}",
                "info"
            )
            
            photo_io = BytesIO()
            # Используем Telethon client напрямую
            # Проверяем, что у нас есть доступ к client
            if not hasattr(manager, "client") or not hasattr(manager.client, "client"):
                self.log_progress(
                    f"Manager structure issue: manager has client: {hasattr(manager, 'client')}, "
                    f"client has client: {hasattr(manager.client, 'client') if hasattr(manager, 'client') else 'N/A'}",
                    "error"
                )
                return None
            
            telethon_client = manager.client.client
            
            # Согласно документации Telethon, download_profile_photo работает лучше с ID
            # Попробуем разные способы скачивания
            photo_path = None
            
            # Способ 1: Через entity напрямую
            try:
                photo_path = await telethon_client.download_profile_photo(
                    entity, 
                    file=photo_io
                )
                if photo_path:
                    self.log_progress("Downloaded photo using entity directly", "debug")
            except Exception as e:
                self.log_progress(
                    f"Direct entity download failed: {type(e).__name__}: {e}",
                    "debug"
                )
                
            # Способ 2: Если не получилось, пробуем через ID
            if not photo_path and hasattr(entity, "id"):
                try:
                    photo_io = BytesIO()  # Создаем новый BytesIO
                    photo_path = await telethon_client.download_profile_photo(
                        entity.id, 
                        file=photo_io
                    )
                    if photo_path:
                        self.log_progress("Downloaded photo using entity ID", "debug")
                except Exception as e:
                    self.log_progress(
                        f"ID-based download failed: {type(e).__name__}: {e}",
                        "debug"
                    )
            
            # Способ 3: Если есть username, попробуем через него
            if not photo_path and hasattr(entity, "username") and entity.username:
                try:
                    photo_io = BytesIO()  # Создаем новый BytesIO
                    photo_path = await telethon_client.download_profile_photo(
                        entity.username, 
                        file=photo_io
                    )
                    if photo_path:
                        self.log_progress("Downloaded photo using username", "debug")
                except Exception as e:
                    self.log_progress(
                        f"Username-based download failed: {type(e).__name__}: {e}",
                        "debug"
                    )
            
            if photo_path:
                photo_io.seek(0)
                photo_bytes = photo_io.read()
                self.log_progress(
                    f"Successfully downloaded photo: {len(photo_bytes)} bytes for {getattr(entity, 'title', 'chat')}",
                    "info"
                )
                return photo_bytes
            else:
                self.log_progress(
                    f"download_profile_photo returned None for {entity_info}",
                    "warning"
                )
                
        except Exception as e:
            self.log_progress(
                f"Failed to download photo for {entity_info}: {type(e).__name__}: {e}",
                "error"
            )
        
        return None
    
    async def _save_chat(self, chat_id: int, 
                        chat_data: dict[str, Any], 
                        photo_data: bytes | None) -> tuple[Any, bool]:
        """
        Сохраняет чат в базу данных.
        
        Args:
            chat_id: ID чата
            chat_data: Данные чата
            photo_data: Байты фото
            
        Returns:
            Кортеж (чат, создан_новый)
        """
        from asgiref.sync import sync_to_async
        
        @sync_to_async
        def save_chat_sync():
            # Проверяем, существует ли чат
            existing_chat = self.chat_service.repository.get_by_chat_id(chat_id)
            is_created = existing_chat is None
            
            # Создаем объект TelegramChatData для pydantic валидации
            from telegram_manager.schemas.telegram import TelegramChatData
            
            # Преобразуем данные в формат pydantic
            chat_data_obj = TelegramChatData(**chat_data)
            
            # Используем сервис для создания/обновления
            chat = self.chat_service.update_chat_from_telegram(chat_data_obj)
            
            # Сохраняем фото если есть
            if photo_data:
                self.log_progress(
                    f"Saving photo for chat {chat.title} ({len(photo_data)} bytes)",
                    "info"
                )
                try:
                    # Ensure we have a valid filename
                    filename = f"chat_{chat_id}.jpg"
                    
                    # Save the photo
                    chat.photo.save(
                        filename,
                        ContentFile(photo_data),
                        save=True
                    )
                    
                    # Verify the photo was saved
                    if chat.photo:
                        self.log_progress(
                            f"Photo saved successfully for chat {chat.title} at {chat.photo.name}",
                            "info"
                        )
                    else:
                        self.log_progress(
                            f"Photo save appeared to succeed but field is empty for chat {chat.title}",
                            "warning"
                        )
                except Exception as e:
                    self.log_progress(
                        f"Failed to save photo for chat {chat.title}: {type(e).__name__}: {e}",
                        "error"
                    )
                    # Re-raise to see the full traceback in debug mode
                    if self.verbosity >= 2:
                        import traceback
                        self.log_progress(
                            f"Full traceback:\n{traceback.format_exc()}",
                            "error"
                        )
            
            return chat, is_created
        
        return await save_chat_sync()  # type: ignore[no-any-return]
    
    def handle_result(self, result: Any):
        """
        Обрабатывает результат выполнения команды.
        
        Args:
            result: Результат выполнения
        """
        if result:
            created, updated = result
            total = created + updated
            
            if total == 0:
                self.log_progress(
                    "No chats found matching the specified criteria",
                    "warning"
                )
            else:
                super().handle_result(result)