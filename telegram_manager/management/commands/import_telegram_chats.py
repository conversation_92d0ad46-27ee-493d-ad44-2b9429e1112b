"""
Import Telegram chats command with async support.
"""

from core.management.commands.base_async_command import BaseAsyncCommand
from telegram_manager.tasks.import_tasks import ImportTelegramChatsTask


class Command(BaseAsyncCommand):
    """Command to import Telegram chats with sync/async support."""
    
    help = "Import Telegram chats with support for both sync and async execution"
    
    def add_task_arguments(self, parser):
        """Add arguments specific to chat import."""
        parser.add_argument(
            "--chat-limit",
            choices=["all", "custom"],
            default="all",
            help="Number of chats to import"
        )
        parser.add_argument(
            "--custom-limit",
            type=int,
            help="Specific number of chats (when chat-limit is 'custom')"
        )
        parser.add_argument(
            "--include-private",
            action="store_true",
            default=True,
            help="Include private chats"
        )
        parser.add_argument(
            "--include-groups",
            action="store_true",
            default=True,
            help="Include groups"
        )
        parser.add_argument(
            "--include-supergroups",
            action="store_true",
            default=True,
            help="Include supergroups"
        )
        parser.add_argument(
            "--include-channels",
            action="store_true",
            default=True,
            help="Include channels"
        )
    
    def get_task_class(self):
        """Return the task class for async execution."""
        return ImportTelegramChatsTask
    
    def prepare_task_params(self, **options):
        """Prepare parameters for the task."""
        return {
            "chat_limit": options.get("chat_limit", "all"),
            "custom_limit": options.get("custom_limit"),
            "include_private": options.get("include_private", True),
            "include_groups": options.get("include_groups", True),
            "include_supergroups": options.get("include_supergroups", True),
            "include_channels": options.get("include_channels", True),
        }
    
    def _display_result(self, result):
        """Display the import result."""
        if isinstance(result, dict):
            self.stdout.write("\nChat Import Summary:")
            self.stdout.write(f"  Total chats: {result.get('total_chats', 0)}")
            self.stdout.write(f"  Created: {result.get('created', 0)}")
            self.stdout.write(f"  Updated: {result.get('updated', 0)}")
            
            errors = result.get("errors", [])
            if errors:
                self.stdout.write(self.style.ERROR(f"\n  Errors: {len(errors)}"))
                for error in errors[:5]:
                    self.stdout.write(f"    - {error}")
                if len(errors) > 5:
                    self.stdout.write(f"    ... and {len(errors) - 5} more")
        else:
            super()._display_result(result)