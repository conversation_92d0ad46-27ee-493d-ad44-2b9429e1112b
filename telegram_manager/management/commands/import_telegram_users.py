"""
Import Telegram users command with async support.
"""

from core.management.commands.base_async_command import BaseAsyncCommand
from telegram_manager.tasks.import_tasks import ImportTelegramUsersTask


class Command(BaseAsyncCommand):
    """Command to import Telegram users with sync/async support."""
    
    help = "Import Telegram users with support for both sync and async execution"
    
    def add_task_arguments(self, parser):
        """Add arguments specific to user import."""
        parser.add_argument(
            "--extract-from-messages",
            action="store_true",
            help="Extract users from existing messages in database"
        )
        parser.add_argument(
            "--from-chat",
            type=int,
            help="Import users from a specific chat ID"
        )
        parser.add_argument(
            "--update-existing",
            action="store_true",
            help="Update existing users with new data"
        )
        parser.add_argument(
            "--limit",
            type=int,
            help="Maximum number of users to import"
        )
    
    def get_task_class(self):
        """Return the task class for async execution."""
        return ImportTelegramUsersTask
    
    def prepare_task_params(self, **options):
        """Prepare parameters for the task."""
        params = {
            "extract_from_messages": options.get("extract_from_messages", False),
            "update_existing": options.get("update_existing", False),
        }
        
        # Add optional parameters
        if options.get("from_chat"):
            params["from_chat"] = options["from_chat"]
        
        if options.get("limit"):
            params["limit"] = options["limit"]
        
        return params
    
    def _display_result(self, result):
        """Display the import result."""
        if isinstance(result, dict):
            self.stdout.write("\nUser Import Summary:")
            self.stdout.write(f"  Total users: {result.get('total_users', 0)}")
            self.stdout.write(f"  Created: {result.get('created', 0)}")
            self.stdout.write(f"  Updated: {result.get('updated', 0)}")
            
            errors = result.get("errors", [])
            if errors:
                self.stdout.write(self.style.ERROR(f"\n  Errors: {len(errors)}"))
                for error in errors[:5]:
                    self.stdout.write(f"    - {error}")
                if len(errors) > 5:
                    self.stdout.write(f"    ... and {len(errors) - 5} more")
        else:
            super()._display_result(result)