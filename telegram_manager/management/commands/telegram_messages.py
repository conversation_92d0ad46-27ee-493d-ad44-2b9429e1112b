"""
Рефакторинг команды импорта сообщений из Telegram.
Использует новую базовую архитектуру.
"""

from datetime import datetime
from typing import Any

from core.exceptions import DataValidationError, NotFoundError
from telegram_manager.management.commands.base_telegram_command import BaseTelegramCommand
from telegram_manager.models import TelegramChat
from telegram_manager.services import TelegramChatService, TelegramMessageService
from telegram_manager.telegram_api.managers import TelegramManager


class Command(BaseTelegramCommand):
    """
    Команда для импорта сообщений из Telegram API.
    """
    help = "Import messages from Telegram chats into the database"
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.chat_service = TelegramChatService()
        self.message_service = TelegramMessageService()
    
    def add_custom_arguments(self, parser):
        """Добавляет аргументы специфичные для импорта сообщений."""
        super().add_custom_arguments(parser)
        
        # Позиционный аргумент для совместимости с тестами
        parser.add_argument(
            "chat_id_positional",
            nargs="?",
            type=int,
            help="Chat ID to import messages from (positional argument)"
        )
        
        # Переопределяем chat-id для множественного выбора
        parser.add_argument(
            "--chat-ids",
            nargs="+",
            type=int,
            help="Chat IDs to import messages from (space-separated)"
        )
        
        parser.add_argument(
            "--all-chats",
            action="store_true",
            help="Import messages from all chats"
        )
        
        parser.add_argument(
            "--message-limit",
            type=int,
            default=1000,
            help="Maximum number of messages to import per chat (default: 1000)"
        )
        
        parser.add_argument(
            "--skip-media",
            action="store_true",
            help="Skip downloading media files"
        )
        
        parser.add_argument(
            "--only-media",
            action="store_true",
            help="Import only messages with media"
        )
        
        parser.add_argument(
            "--media-types",
            nargs="+",
            choices=["photo", "video", "document", "audio", "voice", "sticker"],
            help="Specific media types to import"
        )
        
        parser.add_argument(
            "--skip-user-fetch",
            action="store_true",
            help="Skip fetching full user data from API (faster but less complete)"
        )
    
    def validate_options(self, options: dict[str, Any]):
        """
        Валидирует опции команды.
        
        Args:
            options: Опции команды
            
        Raises:
            CommandError: При невалидных опциях
        """
        # Проверяем наличие chat_ids или chat_id
        chat_ids = options.get("chat_ids") or []
        single_chat_id = options.get("chat_id")
        positional_chat_id = options.get("chat_id_positional")
        all_chats = options.get("all_chats", False)
        
        # Если указан --all-chats, берем все чаты
        if all_chats:
            all_chat_objs = TelegramChat.objects.all()
            chat_ids = list(all_chat_objs.values_list("chat_id", flat=True))
        else:
            # Объединяем все источники chat_ids
            if positional_chat_id and positional_chat_id not in chat_ids:
                chat_ids.append(positional_chat_id)
            if single_chat_id and single_chat_id not in chat_ids:
                chat_ids.append(single_chat_id)
            
            if not chat_ids:
                raise DataValidationError(
                    "Please specify at least one chat ID using positional argument, --chat-ids, --chat-id, or use --all-chats"
                )
        
        options["chat_ids"] = chat_ids
    
    def execute_command(self, credentials: dict[str, Any], 
                       date_range: tuple[datetime, datetime] | None, 
                       **options) -> Any:
        """
        Выполняет основную логику команды.
        
        Args:
            credentials: Учетные данные Telegram
            date_range: Диапазон дат для фильтрации
            **options: Опции команды
            
        Returns:
            Результат выполнения
        """
        # Валидируем опции
        self.validate_options(options)
        
        chat_ids = options["chat_ids"]
        
        # Проверяем существование чатов
        existing_chats = self._validate_chats(chat_ids)
        
        if not existing_chats:
            self.log_progress(
                "No valid chats found. Please import chats first using telegram_chats command.",
                "error"
            )
            return None
        
        # Логируем параметры
        self.log_progress(
            f"Importing messages from {len(existing_chats)} chats",
            "info"
        )
        
        if date_range:
            self.log_progress(
                f"Date range: {date_range[0]} to {date_range[1]}",
                "info"
            )
        
        # Выполняем асинхронный импорт
        result = self.execute_async(
            self._import_messages_async,
            credentials,
            existing_chats,
            date_range,
            options
        )
        
        if result:
            total_processed, total_created = result
            updated = total_processed - total_created
            self.log_import_stats("messages", total_processed, total_created, updated)
        
        return result
    
    def _validate_chats(self, chat_ids: list[int]) -> list[TelegramChat]:
        """
        Проверяет существование чатов в БД.
        
        Args:
            chat_ids: Список ID чатов
            
        Returns:
            Список существующих чатов
        """
        existing_chats = []
        
        for chat_id in chat_ids:
            try:
                chat = self.chat_service.get_by_id(chat_id)
                existing_chats.append(chat)
            except NotFoundError:
                self.log_progress(
                    f"Chat {chat_id} not found in database. Skipping...",
                    "warning"
                )
        
        return existing_chats
    
    async def _import_messages_async(self, credentials: dict[str, Any],
                                    chats: list[TelegramChat],
                                    date_range: tuple[datetime, datetime] | None,
                                    options: dict[str, Any]) -> tuple[int, int]:
        """
        Асинхронный импорт сообщений.
        
        Args:
            credentials: Учетные данные
            chats: Список чатов
            date_range: Диапазон дат
            options: Опции команды
            
        Returns:
            Кортеж (всего обработано, создано новых)
        """
        # Создаем менеджер Telegram
        manager = self.get_telegram_manager(TelegramManager)
        
        total_processed = 0
        total_created = 0
        
        try:
            # Подключаемся к Telegram
            await manager.connect()
            
            # Обрабатываем каждый чат
            for chat in chats:
                try:
                    chat_processed, chat_created = await self._import_chat_messages(
                        chat,
                        manager,
                        date_range,
                        options
                    )
                    
                    total_processed += chat_processed
                    total_created += chat_created
                    
                except Exception as e:
                    self.log_progress(
                        f"Error importing messages from chat {chat.title}: {e}",
                        "error"
                    )
                    continue
            
            return total_processed, total_created
            
        finally:
            # Отключаемся от Telegram
            await manager.disconnect()
    
    async def _import_chat_messages(self, chat: TelegramChat,
                                   manager: TelegramManager,
                                   date_range: tuple[datetime, datetime] | None,
                                   options: dict[str, Any]) -> tuple[int, int]:
        """
        Импортирует сообщения из одного чата.
        
        Args:
            chat: Объект чата
            manager: Telegram manager
            date_range: Диапазон дат
            options: Опции команды
            
        Returns:
            Кортеж (обработано, создано)
        """
        message_limit = options.get("message_limit", 1000)
        
        self.log_progress(
            f"Importing messages from '{chat.title}' (ID: {chat.chat_id})",
            "info"
        )
        
        # Получаем сообщения из Telegram
        messages_data = await self._fetch_messages(
            manager,
            chat.chat_id,
            message_limit,
            date_range,
            options
        )
        
        if not messages_data:
            self.log_progress(
                f"No messages found in chat '{chat.title}'",
                "warning"
            )
            return 0, 0
        
        self.log_progress(
            f"Retrieved {len(messages_data)} messages from Telegram",
            "info"
        )
        
        # Фильтруем сообщения если нужно
        if options.get("only_media"):
            messages_data = self._filter_media_messages(
                messages_data,
                options.get("media_types")
            )
            
            self.log_progress(
                f"Filtered to {len(messages_data)} messages with media",
                "info"
            )
        
        # Обрабатываем и сохраняем сообщения
        from asgiref.sync import sync_to_async

        from telegram_manager.models import TelegramMessage, TelegramUser
        
        # Проверяем, нужно ли загружать данные пользователей
        skip_user_fetch = options.get("skip_user_fetch", False)
        
        @sync_to_async
        def process_messages_sync():
            created_count = 0
            processed_count = 0
            
            # Сначала импортируем пользователей из сообщений
            users_cache = {}
            if options.get("import_users", True):
                # Собираем уникальные user_ids
                user_ids = set()
                for msg_data in messages_data:
                    if msg_data.get("from_id"):
                        user_ids.add(msg_data["from_id"])
                
                # Загружаем существующих пользователей
                existing_users = TelegramUser.objects.filter(user_id__in=user_ids)
                for user in existing_users:
                    users_cache[user.user_id] = user
                
                # Создаем отсутствующих пользователей
                missing_user_ids = user_ids - set(users_cache.keys())
                for user_id in missing_user_ids:
                    try:
                        # Создаем базового пользователя - будем обновлять его данные позже
                        user = TelegramUser.objects.create(
                            user_id=user_id,
                            username="",  # Пустой username вместо временного
                            first_name=f"User {user_id}",
                            is_bot=False
                        )
                        users_cache[user_id] = user
                    except Exception as e:
                        self.stdout.write(f"Failed to create user {user_id}: {e}")
            for msg_data in messages_data:
                try:
                    message_id = msg_data.get("id")
                    
                    # Проверяем, существует ли сообщение
                    existing_message = TelegramMessage.objects.filter(
                        chat=chat,
                        message_id=message_id
                    ).first()
                    
                    if existing_message:
                        # Обновляем существующее сообщение
                        existing_message.text = msg_data.get("text", msg_data.get("message", ""))
                        existing_message.views = msg_data.get("views")
                        existing_message.forwards = msg_data.get("forwards")
                        existing_message.save()
                        processed_count += 1
                    else:
                        # Создаем новое сообщение
                        message_data = {
                            "chat": chat,
                            "message_id": message_id,
                            "text": msg_data.get("text", msg_data.get("message", "")),
                            "date": msg_data.get("date"),
                            "views": msg_data.get("views"),
                            "forwards": msg_data.get("forwards"),
                            "is_reply": msg_data.get("is_reply", False),
                            "is_forward": msg_data.get("is_forward", False),
                            "is_edited": msg_data.get("is_edited", False),
                            "edit_date": msg_data.get("edit_date"),
                            "mentioned": msg_data.get("mentioned", False),
                            "pinned": msg_data.get("pinned", False),
                        }
                        
                        # Добавляем отправителя
                        if msg_data.get("from_id") and msg_data["from_id"] in users_cache:
                            message_data["from_user"] = users_cache[msg_data["from_id"]]
                            message_data["from_id"] = msg_data["from_id"]
                        
                        # Создаем сообщение
                        TelegramMessage.objects.create(**message_data)
                        processed_count += 1
                        created_count += 1
                        
                except Exception as e:
                    self.stdout.write(f"Error processing message {msg_data.get('id')}: {e}")
                    continue
            
            return processed_count, created_count
        
        # Сначала обрабатываем синхронную часть и получаем users_cache
        result_data = await process_messages_sync()
        processed = result_data[0]
        created = result_data[1]
        
        # Извлекаем users_cache из функции
        @sync_to_async
        def get_users_cache():
            cache = {}
            # Собираем всех пользователей, созданных или загруженных
            user_ids = set()
            for msg_data in messages_data:
                if msg_data.get("from_id"):
                    user_ids.add(msg_data["from_id"])
            
            users = TelegramUser.objects.filter(user_id__in=user_ids)
            for user in users:
                cache[user.user_id] = user
            return cache
        
        users_cache = await get_users_cache()
        
        # После обработки сообщений, если не пропускаем загрузку, обновляем данные пользователей из API
        if not skip_user_fetch and users_cache:
            self.log_progress(f"Fetching user data from Telegram API for {len(users_cache)} users...")
            for user_id, user in users_cache.items():
                # Обновляем только тех пользователей, у которых нет username
                if not user.username or user.first_name == f"User {user_id}":
                    try:
                        user_data = await manager.get_user_by_id(user_id)
                        if user_data:
                            # Обновляем данные пользователя
                            await sync_to_async(TelegramUser.objects.filter(user_id=user_id).update)(
                                username=user_data.get("username", ""),
                                first_name=user_data.get("first_name", user.first_name),
                                last_name=user_data.get("last_name", ""),
                                phone=user_data.get("phone", ""),
                                is_bot=user_data.get("is_bot", False),
                                is_verified=user_data.get("is_verified", False),
                                is_restricted=user_data.get("is_restricted", False),
                                is_deleted=user_data.get("is_deleted", False),
                                is_premium=user_data.get("is_premium", False),
                            )
                            self.stdout.write(f"Updated user data from API: @{user_data.get('username', 'no_username')} ({user_data.get('first_name', '')} {user_data.get('last_name', '')})")
                    except Exception as e:
                        self.stdout.write(f"Failed to update user {user_id} from API: {e}")
        
        return processed, created
    
    async def _fetch_messages(self, manager: TelegramManager,
                             chat_id: int,
                             limit: int,
                             date_range: tuple[datetime, datetime] | None,
                             options: dict[str, Any]) -> list[dict[str, Any]]:
        """
        Получает сообщения из Telegram API.
        
        Args:
            manager: Telegram manager
            chat_id: ID чата
            limit: Лимит сообщений
            date_range: Диапазон дат
            options: Опции команды
            
        Returns:
            Список данных сообщений
        """
        if date_range:
            # Получаем сообщения по датам
            messages = await manager.get_messages_by_date(
                chat_id,
                limit=limit,
                date_from=date_range[0],
                date_to=date_range[1]
            )
        else:
            # Получаем последние сообщения
            messages = await manager.get_messages(
                chat_id,
                limit=limit
            )
        
        return messages
    
    def _filter_media_messages(self, messages: list[dict[str, Any]], 
                              media_types: list[str] | None = None) -> list[dict[str, Any]]:
        """
        Фильтрует сообщения с медиа.
        
        Args:
            messages: Список сообщений
            media_types: Типы медиа для фильтрации
            
        Returns:
            Отфильтрованный список
        """
        filtered = []
        
        for msg in messages:
            # Проверяем наличие медиа
            if not msg.get("media"):
                continue
            
            # Если указаны конкретные типы
            if media_types:
                media_type = msg["media"].get("type", "").lower()
                if media_type not in media_types:
                    continue
            
            filtered.append(msg)
        
        return filtered
    
    def handle_result(self, result: Any):
        """
        Обрабатывает результат выполнения команды.
        
        Args:
            result: Результат выполнения
        """
        if result:
            total_processed, total_created = result
            
            if total_processed == 0:
                self.log_progress(
                    "No messages were imported. Check your filters and date range.",
                    "warning"
                )
            else:
                # Показываем статистику обновленных сообщений
                updated = total_processed - total_created
                if updated > 0:
                    self.log_progress(
                        f"Updated {updated} existing messages",
                        "info"
                    )
                
                super().handle_result(result)