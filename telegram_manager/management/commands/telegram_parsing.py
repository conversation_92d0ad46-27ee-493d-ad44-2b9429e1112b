import os
import sys

from django.core.management.base import BaseCommand

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))))

# Import commands from respective modules
from .telegram_chats import Command as ChatsCommand
from .telegram_messages import Command as MessagesCommand
from .telegram_users import Command as UsersCommand


class Command(BaseCommand):
    help = "Import data from Telegram API"

    def add_arguments(self, parser):
        subparsers = parser.add_subparsers(dest="command", help="Commands")
        
        # Import chats command
        import_chats_parser = subparsers.add_parser("import_chats", help="Import chats from Telegram")
        import_chats_parser.add_argument("--limit", type=int, help="Maximum number of chats to import")
        import_chats_parser.add_argument("--include-private", action="store_true", help="Include private chats")
        import_chats_parser.add_argument("--include-groups", action="store_true", help="Include groups")
        import_chats_parser.add_argument("--include-channels", action="store_true", help="Include channels")
        import_chats_parser.add_argument("--include-supergroups", action="store_true", help="Include supergroups")
        
        # Import messages command
        import_msgs_parser = subparsers.add_parser("import_messages", help="Import messages from Telegram")
        import_msgs_parser.add_argument("--chat-ids", nargs="+", type=int, required=True, 
                                        help="Chat IDs to import messages from")
        import_msgs_parser.add_argument("--category", choices=["all", "today", "week", "month", "custom"], 
                                        default="all", help="Date category")
        import_msgs_parser.add_argument("--date-from", type=str, help="Start date for custom range (YYYY-MM-DD)")
        import_msgs_parser.add_argument("--date-to", type=str, help="End date for custom range (YYYY-MM-DD)")
        import_msgs_parser.add_argument("--message-limit", type=int, default=1000, 
                                        help="Maximum number of messages to import per chat")
                                        
        # Import members command
        import_members_parser = subparsers.add_parser("import_members", help="Import members from Telegram groups/channels")
        import_members_parser.add_argument("--chat-ids", nargs="+", type=int, required=True, 
                                          help="Chat IDs to import members from")
        import_members_parser.add_argument("--limit", type=int, help="Maximum number of members to import per chat")

    def handle(self, *args, **options):
        command = options.get("command")
        
        if command == "import_chats":
            # Extract arguments for the chats command
            chats_cmd = ChatsCommand()
            # Copy stdout/stderr from parent command
            chats_cmd.stdout = self.stdout
            chats_cmd.stderr = self.stderr
            chats_cmd.style = self.style
            chats_cmd.handle(
                limit=options.get("limit"),
                include_private=options.get("include_private", False),
                include_groups=options.get("include_groups", False),
                include_channels=options.get("include_channels", False),
                include_supergroups=options.get("include_supergroups", False)
            )
        elif command == "import_messages":
            # Extract arguments for the messages command
            messages_cmd = MessagesCommand()
            # Copy stdout/stderr from parent command
            messages_cmd.stdout = self.stdout
            messages_cmd.stderr = self.stderr
            messages_cmd.style = self.style
            messages_cmd.handle(
                chat_ids=options.get("chat_ids", []),
                category=options.get("category", "all"),
                date_from=options.get("date_from"),
                date_to=options.get("date_to"),
                message_limit=options.get("message_limit", 1000)
            )
        elif command == "import_members":
            # Extract arguments for the members command
            users_cmd = UsersCommand()
            # Copy stdout/stderr from parent command
            users_cmd.stdout = self.stdout
            users_cmd.stderr = self.stderr
            users_cmd.style = self.style
            users_cmd.handle(
                chat_ids=options.get("chat_ids", []),
                limit=options.get("limit")
            )
        else:
            self.stdout.write(self.style.ERROR('No valid command specified. Use "import_chats", "import_messages", or "import_members".'))