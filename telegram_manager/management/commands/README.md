# Telegram Parsing Management Command

This Django management command allows you to import Telegram chats and messages from the command line.

## Usage

### Import Chats

Imports chat/channel/group information from Telegram:

```bash
python manage.py telegram_parsing import_chats [options]
```

Options:
- `--limit INTEGER`: Maximum number of chats to import
- `--include-private`: Include private chats
- `--include-groups`: Include group chats
- `--include-channels`: Include channels

If none of the include options are specified, all types will be included.

Example:
```bash
# Import up to 100 channels
python manage.py telegram_parsing import_chats --limit 100 --include-channels

# Import all types of chats
python manage.py telegram_parsing import_chats
```

### Import Messages

Imports messages from specified Telegram chats:

```bash
python manage.py telegram_parsing import_messages --chat-ids CHAT_IDS [options]
```

Options:
- `--chat-ids`: List of chat IDs to import messages from (required)
- `--category {all,today,week,month,custom}`: Date range category (default: all)
- `--date-from DATE_FROM`: Start date for custom range (YYYY-MM-DD)
- `--date-to DATE_TO`: End date for custom range (YYYY-MM-DD)
- `--message-limit INTEGER`: Maximum number of messages per chat (default: 1000)

Example:
```bash
# Import all messages from chat with ID -1001234567890
python manage.py telegram_parsing import_messages --chat-ids -1001234567890

# Import messages from the last 7 days
python manage.py telegram_parsing import_messages --chat-ids -1001234567890 --category week

# Import messages from a specific date range
python manage.py telegram_parsing import_messages --chat-ids -1001234567890 --category custom --date-from 2023-01-01 --date-to 2023-01-31
```

## Authentication

The command uses the same authentication mechanism as the Django admin interface. It reads the following environment variables:

- `API_ID`: Telegram API ID
- `API_HASH`: Telegram API Hash
- `SESSION_NAME`: Telegram session name

## Notes

- The command will prompt for phone number and verification code if not already authenticated
- For large imports, consider increasing the message limit appropriately