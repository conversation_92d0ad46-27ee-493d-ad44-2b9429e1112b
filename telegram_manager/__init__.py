"""
Telegram Manager Django App

This package provides integration with Telegram API for Django projects.
It includes models for storing chat, user, and message data from Telegram,
as well as management commands for importing data via Telegram's API.
"""

# Модули импортируются отдельно для избежания циклических зависимостей при старте Django
# Используйте: 
# from telegram_manager.models import TelegramChat, TelegramUser, TelegramMessage
# from telegram_manager.services import ChatService, UserService, MessageService
# from telegram_manager.repositories import ChatRepository, UserRepository, MessageRepository

default_app_config = "telegram_manager.apps.TelegrammManagerConfig"