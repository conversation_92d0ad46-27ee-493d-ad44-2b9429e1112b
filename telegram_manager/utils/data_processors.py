"""
Data Processing Utilities

This module provides utility functions for processing and standardizing
data from the Telegram API.
"""

import hashlib
from datetime import datetime


def generate_hash_id(text, multiplier=-1, suffix=0):
    """
    Generate a hash-based ID from text.
    
    Args:
        text: String to hash
        multiplier: Multiplier for the hash value (default: -1)
        suffix: Value to add to the hash (default: 0)
        
    Returns:
        int: Generated hash ID
    """
    if not text:
        return 0
        
    hash_value = int(hashlib.md5(text.encode()).hexdigest(), 16) % (10 ** 10)
    return multiplier * (hash_value + suffix)


def extract_entity_data(entity, entity_type="unknown"):
    """
    Extract common entity data with proper attribute checking.
    
    Args:
        entity: Telethon entity object
        entity_type: Type of entity (default: "unknown")
        
    Returns:
        dict: Standardized entity data
    """
    if not entity:
        return {}
        
    data = {
        "id": getattr(entity, "id", None),
        "type": entity_type,
    }
    
    # Add common attributes with safe getattr
    common_attributes = [
        "access_hash", "username", "first_name", "last_name", 
        "verified", "restricted", "scam", "fake"
    ]
    
    for attr in common_attributes:
        if hasattr(entity, attr):
            data[attr] = getattr(entity, attr)
            
    return data


def extract_date(date_value, default=None):
    """
    Extract a date value, ensuring it's in ISO format.
    
    Args:
        date_value: Datetime object or string to convert
        default: Default value if date_value is None
        
    Returns:
        str: ISO formatted date string or default value
    """
    if date_value is None:
        return default
        
    if isinstance(date_value, datetime):
        return date_value.isoformat()
    
    try:
        return str(date_value)
    except Exception:
        return default
    

def safe_dict_get(data, key, default=None):
    """
    Safely get a value from a dictionary.
    
    Args:
        data: Dictionary to get value from
        key: Key to get
        default: Default value if key doesn't exist (default: None)
        
    Returns:
        The value or default
    """
    if not isinstance(data, dict):
        return default
        
    return data.get(key, default)


def clean_none_values(data):
    """
    Remove None values from a dictionary.
    
    Args:
        data: Dictionary to clean
        
    Returns:
        dict: Dictionary without None values
    """
    if not isinstance(data, dict):
        return data
        
    return {k: v for k, v in data.items() if v is not None}


def extract_nested_attribute(obj, attribute_path, default=None):
    """
    Extract a nested attribute from an object using a dot-separated path.
    
    Args:
        obj: Object to extract attribute from
        attribute_path: Dot-separated path to the attribute (e.g., "photo.photo_id")
        default: Default value if attribute doesn't exist
        
    Returns:
        The attribute value or default
    """
    if not obj or not attribute_path:
        return default
        
    parts = attribute_path.split(".")
    current = obj
    
    for part in parts:
        if hasattr(current, part):
            current = getattr(current, part)
        else:
            return default
            
    return current