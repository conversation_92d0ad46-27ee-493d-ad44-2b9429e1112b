"""
Celery tasks for Telegram manager.
"""

from celery import shared_task

from telegram_manager.tasks.import_tasks import (
    ImportTelegramChatsTask,
    ImportTelegramMessagesTask,
    ImportTelegramUsersTask,
)


@shared_task(bind=True, name="telegram_manager.import_chats")
def import_chats_async(self, **kwargs):
    """Import Telegram chats asynchronously."""
    task = ImportTelegramChatsTask()
    
    # Set Celery task ID for tracking
    if hasattr(self, "request") and self.request.id:
        task._celery_task_id = self.request.id
    
    return task.execute_task(**kwargs)


@shared_task(bind=True, name="telegram_manager.import_messages")
def import_messages_async(self, **kwargs):
    """Import Telegram messages asynchronously."""
    task = ImportTelegramMessagesTask()
    
    # Set Celery task ID for tracking
    if hasattr(self, "request") and self.request.id:
        task._celery_task_id = self.request.id
    
    return task.execute_task(**kwargs)


@shared_task(bind=True, name="telegram_manager.import_users")
def import_users_async(self, **kwargs):
    """Import Telegram users asynchronously."""
    task = ImportTelegramUsersTask()
    
    # Set Celery task ID for tracking
    if hasattr(self, "request") and self.request.id:
        task._celery_task_id = self.request.id
    
    return task.execute_task(**kwargs)