"""
Telegram Services

This package provides service classes for processing and saving Telegram data.
"""

# Export pydantic-based versions
from .chat_service import TelegramChatService as ChatService
from .import_service import ImportService
from .message_service import TelegramMessageService as MessageService
from .user_service import TelegramUserService as UserService

__all__ = [
    # Короткие имена
    "ChatService",
    "UserService", 
    "MessageService",
    "ImportService",
    # Полные имена для обратной совместимости
    "TelegramChatService",
    "TelegramUserService",
    "TelegramMessageService",
]

# Для обратной совместимости
TelegramChatService = ChatService
TelegramUserService = UserService
TelegramMessageService = MessageService
