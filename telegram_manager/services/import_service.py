"""
Unified import service for Telegram.

Provides a single interface for importing Telegram data either synchronously
or asynchronously via Celery.
"""

from django.conf import settings


class ImportService:
    """Service for unified sync/async Telegram imports."""
    
    @classmethod
    def _should_run_async(cls, force_sync: bool | None = None) -> bool:
        """Determine if import should run asynchronously."""
        if force_sync is not None:
            return not force_sync
        
        # Check global setting - default to async (False means async)
        return not getattr(settings, "TELEGRAM_IMPORTS_FORCE_SYNC", False)
    
    @classmethod
    def import_chats(cls, force_sync: bool | None = None, **kwargs):
        """
        Import Telegram chats.
        
        Args:
            force_sync: Force synchronous execution (overrides global setting)
            **kwargs: Parameters for the import task
            
        Returns:
            Celery task result (async) or task result dict (sync)
        """
        if cls._should_run_async(force_sync):
            # Run asynchronously via Celery
            from telegram_manager.tasks import import_chats_async
            return import_chats_async.delay(**kwargs)
        else:
            # Run synchronously
            from telegram_manager.tasks.import_tasks import ImportTelegramChatsTask
            task = ImportTelegramChatsTask()
            return task.execute_task(**kwargs)
    
    @classmethod
    def import_messages(cls, force_sync: bool | None = None, **kwargs):
        """
        Import Telegram messages.
        
        Args:
            force_sync: Force synchronous execution (overrides global setting)
            **kwargs: Parameters for the import task
            
        Returns:
            Celery task result (async) or task result dict (sync)
        """
        if cls._should_run_async(force_sync):
            # Run asynchronously via Celery
            from telegram_manager.tasks import import_messages_async
            return import_messages_async.delay(**kwargs)
        else:
            # Run synchronously
            from telegram_manager.tasks.import_tasks import ImportTelegramMessagesTask
            task = ImportTelegramMessagesTask()
            return task.execute_task(**kwargs)
    
    @classmethod
    def import_users(cls, force_sync: bool | None = None, **kwargs):
        """
        Import Telegram users.
        
        Args:
            force_sync: Force synchronous execution (overrides global setting)
            **kwargs: Parameters for the import task
            
        Returns:
            Celery task result (async) or task result dict (sync)
        """
        if cls._should_run_async(force_sync):
            # Run asynchronously via Celery
            from telegram_manager.tasks import import_users_async
            return import_users_async.delay(**kwargs)
        else:
            # Run synchronously
            from telegram_manager.tasks.import_tasks import ImportTelegramUsersTask
            task = ImportTelegramUsersTask()
            return task.execute_task(**kwargs)
    
    @classmethod
    def set_global_sync_mode(cls, force_sync: bool):
        """
        Set global sync mode for all Telegram imports.
        
        Args:
            force_sync: True to force all imports to run synchronously
        """
        from django.conf import settings
        settings.TELEGRAM_IMPORTS_FORCE_SYNC = force_sync