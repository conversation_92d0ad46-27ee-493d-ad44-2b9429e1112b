"""Enhanced Telegram message service with Pydantic validation."""

from datetime import datetime
from typing import Any

from django.db import transaction

from core.exceptions import DataValidationError
from core.services.base import BaseService
from telegram_manager.models import Telegram<PERSON>hat, TelegramMessage, TelegramUser
from telegram_manager.repositories import TelegramMessageRepository
from telegram_manager.schemas.telegram import TelegramMessageData
from telegram_manager.telegram_api.data_handlers.message_handler import MessageHandler
from telegram_manager.telegram_api.data_handlers.user_handler import UserHandler
from telegram_manager.telegram_api.telegram_client import TelegramAPIClient


class TelegramMessageService(BaseService[TelegramMessage, TelegramMessageRepository]):
    """
    Enhanced service for working with Telegram messages using Pydantic validation.
    """
    
    def __init__(self):
        repository = TelegramMessageRepository()
        super().__init__(repository)
    
    @property
    def entity_name(self):
        return "TelegramMessage"
    
    def import_messages(
        self,
        client: TelegramAPIClient,
        chat: TelegramChat,
        limit: int = 100,
        date_from: datetime | None = None,
        date_to: datetime | None = None,
        import_users: bool = True
    ) -> list[TelegramMessage]:
        """
        Import messages from a chat using pydantic validation.
        
        Args:
            client: Enhanced Telegram API client
            chat: TelegramChat instance
            limit: Maximum number of messages
            date_from: Start date
            date_to: End date
            import_users: Whether to import users from messages
            
        Returns:
            List of imported TelegramMessage instances
        """
        try:
            # Get messages from API (already validated by pydantic)
            messages_data = client.get_posts(
                chat.chat_id,
                limit=limit,
                date_from=date_from,
                date_to=date_to
            )
            
            if not messages_data:
                self.logger.info(f"No messages found for chat {chat.chat_id}")
                return []
            
            users_cache = {}
            
            # Import users if requested
            if import_users:
                users_cache = self._import_users_from_messages(client, messages_data)
            
            # Process messages using handler
            with transaction.atomic():
                self.logger.debug(
                    f"[TRANSACTION START] Starting atomic transaction for batch processing "
                    f"{len(messages_data)} messages in chat {chat.chat_id}"
                )
                
                messages = MessageHandler.batch_process_messages(
                    messages_data,
                    chat,
                    users_cache
                )
                
                self.logger.debug(
                    f"[TRANSACTION END] Successfully processed {len(messages)} messages "
                    f"in transaction for chat {chat.chat_id}"
                )
            
            # Update chat statistics
            from telegram_manager.telegram_api.data_handlers.chat_handler import ChatHandler
            ChatHandler.update_chat_stats(chat, len(messages))
            
            self.logger.info(
                f"Imported {len(messages)} messages for chat {chat.chat_id}"
            )
            return messages
            
        except Exception as e:
            self.logger.exception(f"Error importing messages for chat {chat.chat_id}: {e}")
            raise
    
    def _import_users_from_messages(
        self,
        client: TelegramAPIClient,
        messages_data: list[TelegramMessageData]
    ) -> dict[int, TelegramUser]:
        """
        Import users mentioned in messages.
        
        Args:
            client: Enhanced Telegram API client
            messages_data: List of validated message data
            
        Returns:
            Dictionary mapping user_id to TelegramUser
        """
        # Collect unique user IDs
        user_ids: set[int] = set()
        
        for msg in messages_data:
            # Add sender
            if msg.from_id:
                user_ids.add(msg.from_id)
            
            # Add mentioned users
            for entity in msg.entities:
                if entity.user_id:
                    user_ids.add(entity.user_id)
            
            # Add action users
            if msg.action and msg.action.users:
                user_ids.update(msg.action.users)
            
            # Add forward from user
            if msg.forward and msg.forward.from_id:
                user_ids.add(msg.forward.from_id)
        
        # Load existing users
        existing_users = {
            user.user_id: user
            for user in TelegramUser.objects.filter(user_id__in=user_ids)
        }
        
        # Import missing users
        missing_user_ids = user_ids - set(existing_users.keys())
        
        if missing_user_ids:
            self.logger.info(f"Importing {len(missing_user_ids)} new users")
            
            for user_id in missing_user_ids:
                try:
                    # Get user data from API
                    user_data = client.get_user(user_id)
                    # Process using handler
                    user = UserHandler.process_user_data(user_data)
                    existing_users[user_id] = user
                except Exception as e:
                    self.logger.warning(f"Failed to import user {user_id}: {e}")
        
        return existing_users
    
    def process_message_from_telegram(
        self,
        message_data: TelegramMessageData,
        chat: TelegramChat,
        users_cache: dict[int, TelegramUser] | None = None
    ) -> TelegramMessage:
        """
        Process a single message from validated Telegram data.
        
        Args:
            message_data: Validated TelegramMessageData
            chat: Associated TelegramChat
            users_cache: Optional users cache
            
        Returns:
            TelegramMessage instance
        """
        return MessageHandler.process_message_data(message_data, chat, users_cache)
    
    def get_chat_messages(
        self,
        chat_id: int,
        limit: int | None = None,
        offset: int = 0
    ) -> list[TelegramMessage]:
        """
        Get messages for a chat.
        
        Args:
            chat_id: Chat ID
            limit: Maximum number of messages
            offset: Offset for pagination
            
        Returns:
            List of messages
        """
        return list(
            self.repository.filter_by_chat(chat_id)
            .order_by("-date")[offset:offset + limit if limit else None]
        )
    
    def search_messages(
        self,
        query: str,
        chat_id: int | None = None,
        user_id: int | None = None
    ) -> list[TelegramMessage]:
        """
        Search messages by text.
        
        Args:
            query: Search query
            chat_id: Filter by chat
            user_id: Filter by sender
            
        Returns:
            List of found messages
        """
        if len(query) < 2:
            raise DataValidationError("Search query must be at least 2 characters long")
        
        return list(self.repository.search_text(query, chat_id))
    
    def get_user_messages(self, user_id: int) -> list[TelegramMessage]:
        """
        Get all messages from a user.
        
        Args:
            user_id: User ID
            
        Returns:
            List of messages
        """
        return list(self.repository.filter_by_user(user_id))
    
    def get_media_messages(
        self,
        chat_id: int | None = None,
        media_type: str | None = None
    ) -> list[TelegramMessage]:
        """
        Get messages with media.
        
        Args:
            chat_id: Filter by chat
            media_type: Filter by media type
            
        Returns:
            List of messages with media
        """
        queryset = self.repository.filter_media_messages()
        
        if chat_id:
            queryset = queryset.filter(chat_id=chat_id)
        
        if media_type:
            queryset = queryset.filter(media_type=media_type)
        
        return list(queryset)
    
    def get_forwarded_messages(self, chat_id: int | None = None) -> list[TelegramMessage]:
        """
        Get forwarded messages.
        
        Args:
            chat_id: Filter by chat
            
        Returns:
            List of forwarded messages
        """
        queryset = self.repository.filter_forwards()
        if chat_id:
            queryset = queryset.filter(chat_id=chat_id)
        return list(queryset)
    
    def validate_create_data(self, data: dict[str, Any]):
        """Validate data for message creation."""
        required_fields = ["message_id", "chat", "date"]
        for field in required_fields:
            if field not in data:
                raise ValueError(f"{field} is required")
    
    def process_create_data(self, data: dict[str, Any]) -> dict[str, Any]:
        """Process data before creation."""
        # No need for manual type conversion - pydantic handles it
        return data