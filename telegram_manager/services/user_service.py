"""Enhanced Telegram user service with Pydantic validation."""

from typing import Any, cast

from core.exceptions import DataValidationError
from core.services.base import BaseService
from telegram_manager.models import TelegramUser
from telegram_manager.repositories import TelegramUserRepository
from telegram_manager.schemas.telegram import TelegramUserData
from telegram_manager.telegram_api.data_handlers.user_handler import UserHandler
from telegram_manager.telegram_api.telegram_client import TelegramAPIClient


class TelegramUserService(BaseService[TelegramUser, TelegramUserRepository]):
    """
    Enhanced service for working with Telegram users using Pydantic validation.
    """
    
    def __init__(self):
        repository = TelegramUserRepository()
        super().__init__(repository)
    
    @property
    def entity_name(self):
        return "TelegramUser"
    
    def import_user(
        self,
        client: TelegramAPIClient,
        user_id: str | int
    ) -> TelegramUser:
        """
        Import a user from Telegram using pydantic validation.
        
        Args:
            client: Enhanced Telegram API client
            user_id: User ID or username
            
        Returns:
            TelegramUser model instance
        """
        try:
            # Get user data from API (already validated by pydantic)
            user_data = client.get_user(user_id)
            
            # Process and save using handler
            user = UserHandler.process_user_data(user_data)
            
            return user
            
        except Exception as e:
            self.logger.exception(f"Error importing user {user_id}: {e}")
            raise
    
    def import_chat_participants(
        self,
        client: TelegramAPIClient,
        chat_id: str | int,
        limit: int | None = None
    ) -> list[TelegramUser]:
        """
        Import participants from a chat using pydantic validation.
        
        Args:
            client: Enhanced Telegram API client
            chat_id: Chat ID
            limit: Maximum number of participants
            
        Returns:
            List of TelegramUser instances
        """
        try:
            # Get participants from API (already validated by pydantic)
            users_data = client.get_chat_participants(chat_id, limit)
            
            # Process using handler
            users = UserHandler.batch_process_users(users_data)
            
            self.logger.info(
                f"Imported {len(users)} participants from chat {chat_id}"
            )
            return users
            
        except Exception as e:
            self.logger.exception(f"Error importing participants from chat {chat_id}: {e}")
            raise
    
    def update_user_from_telegram(self, user_data: TelegramUserData) -> TelegramUser:
        """
        Update user from validated Telegram data.
        
        Args:
            user_data: Validated TelegramUserData
            
        Returns:
            Updated TelegramUser instance
        """
        user = UserHandler.process_user_data(user_data)
        return user
    
    def get_active_users(self) -> list[TelegramUser]:
        """Get list of active (non-deleted) users."""
        return list(self.repository.filter_active_users())
    
    def get_bots(self) -> list[TelegramUser]:
        """Get list of bot users."""
        return list(self.repository.filter_bots())
    
    def get_verified_users(self) -> list[TelegramUser]:
        """Get list of verified users."""
        return list(self.repository.filter(is_verified=True))
    
    def get_premium_users(self) -> list[TelegramUser]:
        """Get list of premium users."""
        return list(self.repository.filter(is_premium=True))
    
    def search_users(self, query: str) -> list[TelegramUser]:
        """
        Search users by username, first name, or last name.
        
        Args:
            query: Search query
            
        Returns:
            List of found users
        """
        if len(query) < 2:
            raise DataValidationError("Search query must be at least 2 characters long")
        
        return list(self.repository.search_users(query))
    
    def get_user_statistics(self, user_id: int) -> dict[str, Any]:
        """
        Get user statistics.
        
        Args:
            user_id: User ID
            
        Returns:
            Dictionary with statistics
        """
        # Check user existence
        user = self.get_by_id(user_id)
        
        # Get statistics from message repository
        from telegram_manager.repositories import TelegramMessageRepository
        message_repo = TelegramMessageRepository()
        
        stats = {
            "user_info": {
                "username": user.username,
                "full_name": user.full_name,
                "is_bot": user.is_bot,
                "is_verified": user.is_verified,
                "is_premium": user.is_premium,
            },
            "messages_sent": message_repo.filter_by_user(user_id).count(),
            "mentioned_in": user.mentioned_in.count() if hasattr(user, "mentioned_in") else 0,
        }
        
        return stats
    
    def mark_user_as_deleted(self, user_id: int) -> TelegramUser:
        """
        Mark user as deleted.
        
        Args:
            user_id: User ID
            
        Returns:
            Updated user
        """
        user = self.update(user_id, is_deleted=True)
        self.logger.info(f"Marked user {user} as deleted")
        return cast(TelegramUser, user)
    
    def validate_create_data(self, data: dict[str, Any]):
        """Validate data for user creation."""
        if "user_id" not in data:
            raise ValueError("user_id is required")
        
        # Check if user already exists
        if self.repository.exists(user_id=data["user_id"]):
            raise ValueError(f"User with id {data['user_id']} already exists")
    
    def process_create_data(self, data: dict[str, Any]) -> dict[str, Any]:
        """Process data before creation."""
        # Set default values
        data.setdefault("first_name", "")
        data.setdefault("last_name", "")
        data.setdefault("is_bot", False)
        
        # No need for manual type conversion - pydantic handles it
        return data
    
    def get_users_by_chat(self, chat_id: int) -> list[TelegramUser]:
        """
        Get all users who sent messages in a chat.
        
        Args:
            chat_id: Chat ID
            
        Returns:
            List of users
        """
        from telegram_manager.models import TelegramMessage
        
        user_ids = TelegramMessage.objects.filter(
            chat_id=chat_id
        ).values_list("sender_id", flat=True).distinct()
        
        return list(self.repository.filter(user_id__in=user_ids))