"""Enhanced Telegram chat service with Pydantic validation."""

from datetime import datetime
from typing import Any, cast

from core.exceptions import DataValidationError
from core.services.base import BaseService
from telegram_manager.models import TelegramChat
from telegram_manager.repositories import TelegramChatRepository
from telegram_manager.schemas.telegram import TelegramChatData
from telegram_manager.telegram_api.data_handlers.chat_handler import Chat<PERSON>andler
from telegram_manager.telegram_api.telegram_client import TelegramAPIClient


class TelegramChatService(BaseService[TelegramChat, TelegramChatRepository]):
    """
    Enhanced service for working with Telegram chats using Pydantic validation.
    """
    
    def __init__(self):
        repository = TelegramChatRepository()
        super().__init__(repository)
    
    @property
    def entity_name(self):
        return "TelegramChat"
    
    def import_chat(self, client: TelegramAPIClient, chat_id: str | int) -> TelegramChat:
        """
        Import a chat from Telegram using pydantic validation.
        
        Args:
            client: Enhanced Telegram API client
            chat_id: Chat ID or username
            
        Returns:
            TelegramChat model instance
        """
        try:
            # Get chat data from API (already validated by pydantic)
            chat_data = client.get_chat(chat_id)
            
            # Process and save using handler
            chat = ChatHandler.process_chat_data(chat_data)
            
            return chat
            
        except Exception as e:
            self.logger.exception(f"Error importing chat {chat_id}: {e}")
            raise
    
    def import_dialogs(
        self, 
        client: TelegramAPIClient,
        limit: int = 100,
        include_channels: bool = True,
        include_groups: bool = True
    ) -> list[TelegramChat]:
        """
        Import dialogs from Telegram using pydantic validation.
        
        Args:
            client: Enhanced Telegram API client
            limit: Maximum number of dialogs
            include_channels: Include channels
            include_groups: Include groups
            
        Returns:
            List of TelegramChat instances
        """
        try:
            # Get dialogs from API (already validated by pydantic)
            chats_data = client.get_dialogs(
                limit=limit,
                include_private=False,  # Don't import private chats
                include_groups=include_groups,
                include_channels=include_channels
            )
            
            # Process using handler
            chats = ChatHandler.batch_process_chats(chats_data)
            
            self.logger.info(f"Imported {len(chats)} chats from Telegram")
            return chats
            
        except Exception as e:
            self.logger.exception(f"Error importing dialogs: {e}")
            raise
    
    def update_chat_from_telegram(self, chat_data: TelegramChatData) -> TelegramChat:
        """
        Update chat from validated Telegram data.
        
        Args:
            chat_data: Validated TelegramChatData
            
        Returns:
            Updated TelegramChat instance
        """
        chat = ChatHandler.process_chat_data(chat_data)
        return chat
    
    def get_active_chats(self) -> list[TelegramChat]:
        """Get list of active chats."""
        return list(self.repository.filter_active_chats())
    
    def get_channels(self) -> list[TelegramChat]:
        """Get list of channels."""
        return list(self.repository.filter_channels())
    
    def get_groups(self) -> list[TelegramChat]:
        """Get list of groups."""
        return list(self.repository.filter_groups())
    
    def search_chats(self, query: str) -> list[TelegramChat]:
        """
        Search chats by title or username.
        
        Args:
            query: Search query
            
        Returns:
            List of found chats
        """
        if len(query) < 2:
            raise DataValidationError("Search query must be at least 2 characters long")
        
        return list(self.repository.search_chats(query))
    
    def get_chat_statistics(self, chat_id: int) -> dict[str, Any]:
        """
        Get chat statistics.
        
        Args:
            chat_id: Chat ID
            
        Returns:
            Dictionary with statistics
        """
        # Check chat existence
        chat = self.get_by_id(chat_id)
        
        # Get statistics from message repository
        from telegram_manager.repositories import TelegramMessageRepository
        message_repo = TelegramMessageRepository()
        
        stats = message_repo.get_chat_statistics(chat_id)
        
        # Add chat info
        stats["chat_info"] = {
            "title": chat.title,
            "username": chat.username,
            "type": "channel" if chat.broadcast else ("supergroup" if chat.megagroup else "group"),
            "participants": chat.participants_count,
            "verified": chat.verified,
        }
        
        return stats
    
    def update_participants_count(self, chat_id: int, count: int) -> TelegramChat:
        """
        Update participants count in chat.
        
        Args:
            chat_id: Chat ID
            count: New participants count
            
        Returns:
            Updated chat
        """
        if count < 0:
            raise DataValidationError("Participants count cannot be negative")
        
        chat = self.update(chat_id, participants_count=count)
        self.logger.info(f"Updated participants count for {chat}: {count}")
        return cast(TelegramChat, chat)
    
    def get_verified_chats(self) -> list[TelegramChat]:
        """Get verified chats."""
        return list(self.repository.filter_verified_chats())
    
    def validate_create_data(self, data: dict[str, Any]):
        """Validate data for chat creation."""
        if "chat_id" not in data:
            raise ValueError("chat_id is required")
        
        # Check if chat already exists
        if self.repository.exists(chat_id=data["chat_id"]):
            raise ValueError(f"Chat with id {data['chat_id']} already exists")
    
    def process_create_data(self, data: dict[str, Any]) -> dict[str, Any]:
        """Process data before creation."""
        # Set default values
        data.setdefault("title", f"Chat {data['chat_id']}")
        data.setdefault("date", datetime.now())
        
        # No need for manual type conversion - pydantic handles it
        return data
    
    def validate_delete(self, entity: TelegramChat):
        """Validate before deleting chat."""
        # Check if chat has messages
        if entity.messages.exists():
            raise DataValidationError(
                f"Cannot delete chat {entity} with existing messages. "
                "Delete messages first or use cascade delete."
            )