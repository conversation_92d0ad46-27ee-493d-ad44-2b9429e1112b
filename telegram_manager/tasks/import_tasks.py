"""
Telegram import tasks for Celery.
"""

from datetime import datetime
from typing import Any

from django.utils import timezone

from core.tasks.base import BaseTask
from telegram_manager.models import TelegramChat, TelegramMessage
from telegram_manager.services import TelegramChatService, TelegramMessageService, TelegramUserService
from telegram_manager.telegram_api.telegram_client import TelegramClient


class ImportTelegramChatsTask(BaseTask):
    """Task for importing Telegram chats."""
    
    task_type = "telegram_chat_import"
    description = "Import Telegram chats"
    max_retries = 3
    
    def validate_params(self, params: dict[str, Any]) -> dict[str, Any]:
        """Validate and prepare parameters."""
        validated = {
            "chat_limit": params.get("chat_limit", "all"),
            "custom_limit": params.get("custom_limit"),
            "include_private": params.get("include_private", True),
            "include_groups": params.get("include_groups", True),
            "include_supergroups": params.get("include_supergroups", True),
            "include_channels": params.get("include_channels", True),
        }
        
        # Validate custom limit
        if validated["chat_limit"] == "custom":
            if not validated["custom_limit"] or validated["custom_limit"] < 1:
                raise ValueError("Custom limit must be a positive integer")
        
        return validated
    
    def execute_task(self, **kwargs) -> dict[str, Any]:
        """Execute the chat import task."""
        params = self.validate_params(kwargs)
        
        # Initialize services
        chat_service = TelegramChatService()
        client = TelegramClient()
        
        self.update_progress(0, "Connecting to Telegram...")
        
        try:
            # Connect to Telegram
            client.connect()
            
            # Determine limit
            limit = None
            if params["chat_limit"] == "custom":
                limit = params["custom_limit"]
            
            self.update_progress(10, "Fetching dialogs from Telegram...")
            
            # Get dialogs
            dialogs = client.get_dialogs(limit=limit)
            
            if not dialogs:
                self.update_progress(100, "No dialogs found")
                return {
                    "total_chats": 0,
                    "created": 0,
                    "updated": 0,
                    "errors": []
                }
            
            # Filter dialogs based on type preferences
            filtered_dialogs = []
            for dialog in dialogs:
                chat_type = dialog.get("type", "").lower()
                
                if chat_type == "private" and params["include_private"]:
                    filtered_dialogs.append(dialog)
                elif chat_type == "group" and params["include_groups"]:
                    filtered_dialogs.append(dialog)
                elif chat_type == "supergroup" and params["include_supergroups"]:
                    filtered_dialogs.append(dialog)
                elif chat_type == "channel" and params["include_channels"]:
                    filtered_dialogs.append(dialog)
            
            total_dialogs = len(filtered_dialogs)
            self.set_total_items(total_dialogs)
            
            created = 0
            updated = 0
            errors = []
            
            for idx, dialog in enumerate(filtered_dialogs):
                try:
                    progress = 10 + int((idx / total_dialogs) * 80)
                    self.update_progress(
                        progress,
                        f"Processing chat {idx + 1}/{total_dialogs}: {dialog.get('title', 'Unknown')}"
                    )
                    
                    # Process chat using service
                    chat, is_created = chat_service.import_chat_from_api(dialog)
                    
                    if is_created:
                        created += 1
                    else:
                        updated += 1
                    
                    self.increment_processed_items()
                    
                except Exception as e:
                    error_msg = f"Error processing chat {dialog.get('title', 'Unknown')}: {e!s}"
                    errors.append(error_msg)
                    self.log_error(error_msg)
            
            # Disconnect from Telegram
            client.disconnect()
            
            self.update_progress(100, "Import completed")
            
            return {
                "total_chats": total_dialogs,
                "created": created,
                "updated": updated,
                "errors": errors
            }
            
        except Exception as e:
            self.log_error(f"Fatal error during chat import: {e!s}")
            raise
        finally:
            # Ensure disconnection
            try:
                client.disconnect()
            except Exception:
                pass


class ImportTelegramMessagesTask(BaseTask):
    """Task for importing Telegram messages."""
    
    task_type = "telegram_message_import"
    description = "Import Telegram messages"
    max_retries = 3
    
    def validate_params(self, params: dict[str, Any]) -> dict[str, Any]:
        """Validate and prepare parameters."""
        validated = {
            "category": params.get("category", "all"),
            "date_from": params.get("date_from"),
            "date_to": params.get("date_to"),
            "message_limit": params.get("message_limit", 1000),
            "skip_user_fetch": params.get("skip_user_fetch", False),
        }
        
        # Validate dates for custom range
        if validated["category"] == "custom":
            if not validated["date_from"] or not validated["date_to"]:
                raise ValueError("Date range is required for custom category")
        
        # Validate message limit
        if validated["message_limit"] < 1 or validated["message_limit"] > 100000:
            raise ValueError("Message limit must be between 1 and 100000")
        
        return validated
    
    def execute_task(self, **kwargs) -> dict[str, Any]:
        """Execute the message import task."""
        params = self.validate_params(kwargs)
        
        # Initialize services
        message_service = TelegramMessageService()
        user_service = TelegramUserService()
        client = TelegramClient()
        
        self.update_progress(0, "Connecting to Telegram...")
        
        try:
            # Connect to Telegram
            client.connect()
            
            # Get all chats from database
            chats = TelegramChat.objects.all()
            if not chats.exists():
                self.update_progress(100, "No chats found. Import chats first.")
                return {
                    "total_messages": 0,
                    "created": 0,
                    "updated": 0,
                    "users_created": 0,
                    "errors": []
                }
            
            # Calculate date range
            date_range = self._calculate_date_range(params["category"], params["date_from"], params["date_to"])
            
            total_chats = chats.count()
            total_messages = 0
            created = 0
            updated = 0
            users_created = 0
            errors = []
            
            for idx, chat in enumerate(chats):
                try:
                    progress = int((idx / total_chats) * 90)
                    self.update_progress(
                        progress,
                        f"Processing chat {idx + 1}/{total_chats}: {chat.title}"
                    )
                    
                    # Fetch messages for this chat
                    messages = client.get_messages(
                        chat_id=chat.chat_id,
                        limit=params["message_limit"],
                        min_date=date_range[0] if date_range else None,
                        max_date=date_range[1] if date_range else None
                    )
                    
                    if not messages:
                        continue
                    
                    # Track unique user IDs
                    user_ids = set()
                    
                    for message in messages:
                        try:
                            # Collect user IDs for later fetching
                            if message.get("from_user_id"):
                                user_ids.add(message["from_user_id"])
                            
                            # Process message
                            msg, is_created = message_service.import_message_from_api(
                                message, 
                                chat_id=chat.chat_id
                            )
                            
                            if is_created:
                                created += 1
                            else:
                                updated += 1
                            
                            total_messages += 1
                            
                        except Exception as e:
                            error_msg = f"Error processing message in chat {chat.title}: {e!s}"
                            errors.append(error_msg)
                            self.log_error(error_msg)
                    
                    # Fetch user data if not skipped
                    if not params["skip_user_fetch"] and user_ids:
                        for user_id in user_ids:
                            try:
                                user_data = client.get_user(user_id)
                                if user_data:
                                    user, is_created = user_service.update_or_create(
                                        user_id=user_id,
                                        defaults=user_data
                                    )
                                    if is_created:
                                        users_created += 1
                            except Exception as e:
                                self.log_error(f"Error fetching user {user_id}: {e!s}")
                    
                except Exception as e:
                    error_msg = f"Error processing chat {chat.title}: {e!s}"
                    errors.append(error_msg)
                    self.log_error(error_msg)
            
            # Disconnect from Telegram
            client.disconnect()
            
            self.update_progress(100, "Import completed")
            
            return {
                "total_messages": total_messages,
                "created": created,
                "updated": updated,
                "users_created": users_created,
                "errors": errors
            }
            
        except Exception as e:
            self.log_error(f"Fatal error during message import: {e!s}")
            raise
        finally:
            # Ensure disconnection
            try:
                client.disconnect()
            except Exception:
                pass
    
    def _calculate_date_range(self, category: str, date_from: str | None, date_to: str | None) -> tuple[datetime, datetime] | None:
        """Calculate date range based on category."""
        if category == "all":
            return None
        
        now = timezone.now()
        
        if category == "today":
            start = now.replace(hour=0, minute=0, second=0, microsecond=0)
            end = now
        elif category == "week":
            start = now - timezone.timedelta(days=7)
            end = now
        elif category == "month":
            start = now - timezone.timedelta(days=30)
            end = now
        elif category == "custom":
            # Parse date strings
            start = datetime.fromisoformat(date_from)
            end = datetime.fromisoformat(date_to)
            
            # Make timezone aware if needed
            if timezone.is_naive(start):
                start = timezone.make_aware(start)
            if timezone.is_naive(end):
                end = timezone.make_aware(end)
        else:
            return None
        
        return (start, end)


class ImportTelegramUsersTask(BaseTask):
    """Task for importing Telegram users."""
    
    task_type = "telegram_user_import"
    description = "Import Telegram users"
    max_retries = 3
    
    def validate_params(self, params: dict[str, Any]) -> dict[str, Any]:
        """Validate and prepare parameters."""
        validated = {
            "extract_from_messages": params.get("extract_from_messages", False),
            "from_chat": params.get("from_chat"),
            "update_existing": params.get("update_existing", False),
            "limit": params.get("limit"),
        }
        
        # Validate chat ID if provided
        if validated["from_chat"] is not None:
            try:
                validated["from_chat"] = int(validated["from_chat"])
            except (ValueError, TypeError):
                raise ValueError("Invalid chat ID")
        
        return validated
    
    def execute_task(self, **kwargs) -> dict[str, Any]:
        """Execute the user import task."""
        params = self.validate_params(kwargs)
        
        # Initialize services
        user_service = TelegramUserService()
        
        if params["extract_from_messages"]:
            # Extract users from existing messages
            return self._extract_users_from_messages(
                user_service,
                params["limit"]
            )
        elif params["from_chat"]:
            # Import users from specific chat
            return self._import_users_from_chat(
                user_service,
                params["from_chat"],
                params["update_existing"],
                params["limit"]
            )
        else:
            # Import users from all chats
            return self._import_all_users(
                user_service,
                params["update_existing"],
                params["limit"]
            )
    
    def _extract_users_from_messages(self, user_service: TelegramUserService, limit: int | None) -> dict[str, Any]:
        """Extract users from existing messages in database."""
        self.update_progress(0, "Extracting users from messages...")
        
        # Get messages with user IDs
        messages_query = TelegramMessage.objects.exclude(from_user_id__isnull=True)
        
        if limit:
            messages_query = messages_query[:limit]
        
        # Collect unique user IDs
        user_ids = set()
        for message in messages_query:
            if message.from_user_id:
                user_ids.add(message.from_user_id)
            
            # Also get mentioned users
            for user in message.mentioned_users.all():
                user_ids.add(user.user_id)
        
        total_users = len(user_ids)
        self.set_total_items(total_users)
        
        created = 0
        updated = 0
        errors = []
        
        for idx, user_id in enumerate(user_ids):
            try:
                progress = int((idx / total_users) * 100)
                self.update_progress(progress, f"Processing user {idx + 1}/{total_users}")
                
                result = user_service.get_or_create_user_by_id(user_id)
                if result:
                    user, is_created = result
                    if is_created:
                        created += 1
                    else:
                        updated += 1
                
                self.increment_processed_items()
                
            except Exception as e:
                error_msg = f"Error processing user {user_id}: {e!s}"
                errors.append(error_msg)
                self.log_error(error_msg)
        
        self.update_progress(100, "Extraction completed")
        
        return {
            "total_users": total_users,
            "created": created,
            "updated": updated,
            "errors": errors
        }
    
    def _import_users_from_chat(self, user_service: TelegramUserService, chat_id: int, 
                               update_existing: bool, limit: int | None) -> dict[str, Any]:
        """Import users from specific chat."""
        client = TelegramClient()
        
        self.update_progress(0, "Connecting to Telegram...")
        
        try:
            # Connect to Telegram
            client.connect()
            
            # Get chat participants
            self.update_progress(10, f"Fetching participants from chat {chat_id}...")
            
            # Get participants using the existing client
            participants = client.get_chat_participants(chat_id, limit=limit)
            
            if not participants:
                self.update_progress(100, "No participants found")
                return {
                    "total_users": 0,
                    "created": 0,
                    "updated": 0,
                    "errors": []
                }
            
            total_users = len(participants)
            self.set_total_items(total_users)
            
            created = 0
            updated = 0
            errors = []
            
            for idx, participant in enumerate(participants):
                try:
                    progress = 10 + int((idx / total_users) * 80)
                    self.update_progress(
                        progress,
                        f"Processing user {idx + 1}/{total_users}"
                    )
                    
                    # Extract user data
                    user_data = self._extract_user_data(participant)
                    
                    # Save user
                    if update_existing:
                        user, is_created = user_service.update_or_create(
                            user_id=user_data["user_id"],
                            defaults=user_data
                        )
                    else:
                        user, is_created = user_service.get_or_create(
                            user_id=user_data["user_id"],
                            defaults=user_data
                        )
                    
                    if is_created:
                        created += 1
                    else:
                        updated += 1
                    
                    self.increment_processed_items()
                    
                except Exception as e:
                    error_msg = f"Error processing participant: {e!s}"
                    errors.append(error_msg)
                    self.log_error(error_msg)
            
            client.disconnect()
            
            self.update_progress(100, "Import completed")
            
            return {
                "total_users": total_users,
                "created": created,
                "updated": updated,
                "errors": errors
            }
            
        except Exception as e:
            self.log_error(f"Fatal error during user import: {e!s}")
            raise
        finally:
            # Ensure disconnection
            try:
                client.disconnect()
            except Exception:
                pass
    
    def _import_all_users(self, user_service: TelegramUserService, 
                         update_existing: bool, limit: int | None) -> dict[str, Any]:
        """Import users from all chats."""
        # Get all chats
        chats = TelegramChat.objects.all()
        
        if not chats.exists():
            self.update_progress(100, "No chats found. Import chats first.")
            return {
                "total_users": 0,
                "created": 0,
                "updated": 0,
                "errors": []
            }
        
        total_created = 0
        total_updated = 0
        total_users = 0
        all_errors = []
        
        for chat in chats:
            self.update_progress(
                int((total_users / 1000) * 100),  # Rough estimate
                f"Processing chat: {chat.title}"
            )
            
            result = self._import_users_from_chat(
                user_service,
                chat.chat_id,
                update_existing,
                limit
            )
            
            total_users += result["total_users"]
            total_created += result["created"]
            total_updated += result["updated"]
            all_errors.extend(result["errors"])
        
        return {
            "total_users": total_users,
            "created": total_created,
            "updated": total_updated,
            "errors": all_errors
        }
    
    def _extract_user_data(self, participant) -> dict[str, Any]:
        """Extract user data from participant object."""
        # Handle test data (dictionaries)
        if isinstance(participant, dict):
            return {
                "user_id": participant.get("id"),
                "username": participant.get("username"),
                "first_name": participant.get("first_name"),
                "last_name": participant.get("last_name"),
                "is_bot": participant.get("bot", False),
                "is_verified": participant.get("verified", False),
                "is_restricted": participant.get("restricted", False),
                "is_scam": participant.get("scam", False),
                "is_fake": participant.get("fake", False),
                "is_premium": participant.get("premium", False),
            }
        
        # Handle real Telegram objects
        user = participant if not hasattr(participant, "user") else participant.user
        
        return {
            "user_id": getattr(user, "id", None),
            "username": getattr(user, "username", None),
            "first_name": getattr(user, "first_name", None),
            "last_name": getattr(user, "last_name", None),
            "is_bot": getattr(user, "bot", False),
            "is_verified": getattr(user, "verified", False),
            "is_restricted": getattr(user, "restricted", False),
            "is_scam": getattr(user, "scam", False),
            "is_fake": getattr(user, "fake", False),
            "is_premium": getattr(user, "premium", False),
        }