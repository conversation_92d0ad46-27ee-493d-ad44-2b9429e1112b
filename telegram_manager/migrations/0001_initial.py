# Generated by Django 5.2.1 on 2025-06-25 18:48

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name="TelegramChat",
            fields=[
                ("created_at", models.DateTimeField(auto_now_add=True, db_index=True)),
                ("updated_at", models.DateTimeField(auto_now=True, db_index=True)),
                (
                    "chat_id",
                    models.BigIntegerField(
                        help_text="Unique identifier for the chat",
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "title",
                    models.CharField(
                        blank=True,
                        help_text="Title of the chat or channel",
                        max_length=255,
                    ),
                ),
                (
                    "username",
                    models.CharField(
                        blank=True,
                        help_text="Public username",
                        max_length=32,
                        null=True,
                    ),
                ),
                (
                    "creator",
                    models.BooleanField(
                        default=False, help_text="Whether you created it"
                    ),
                ),
                (
                    "left",
                    models.<PERSON><PERSON><PERSON><PERSON>ield(default=False, help_text="Whether you left"),
                ),
                (
                    "broadcast",
                    models.<PERSON><PERSON>anField(
                        default=False, help_text="Whether it's a channel (not group)"
                    ),
                ),
                (
                    "verified",
                    models.BooleanField(default=False, help_text="Whether verified"),
                ),
                (
                    "megagroup",
                    models.BooleanField(
                        default=False, help_text="Whether it's a supergroup"
                    ),
                ),
                (
                    "restricted",
                    models.BooleanField(default=False, help_text="Whether restricted"),
                ),
                (
                    "min",
                    models.BooleanField(default=False, help_text="Minimal info flag"),
                ),
                (
                    "scam",
                    models.BooleanField(
                        default=False, help_text="Whether marked as scam"
                    ),
                ),
                (
                    "has_link",
                    models.BooleanField(
                        default=False, help_text="Has discussion group link"
                    ),
                ),
                (
                    "has_geo",
                    models.BooleanField(default=False, help_text="Has geolocation"),
                ),
                (
                    "participants_count",
                    models.IntegerField(
                        blank=True, help_text="Approx participant count", null=True
                    ),
                ),
                (
                    "photo_id",
                    models.BigIntegerField(
                        blank=True, help_text="Channel photo ID", null=True
                    ),
                ),
                (
                    "photo",
                    models.ImageField(
                        blank=True,
                        help_text="Chat profile photo",
                        null=True,
                        upload_to="telegram_chat_photos/",
                    ),
                ),
            ],
            options={
                "verbose_name": "Telegram Chat",
                "verbose_name_plural": "Telegram Chats",
                "indexes": [
                    models.Index(
                        fields=["username"], name="telegram_ma_usernam_1a19ac_idx"
                    ),
                    models.Index(
                        fields=["created_at", "-updated_at"],
                        name="telegram_ma_created_721254_idx",
                    ),
                ],
            },
        ),
        migrations.CreateModel(
            name="TelegramUser",
            fields=[
                ("created_at", models.DateTimeField(auto_now_add=True, db_index=True)),
                ("updated_at", models.DateTimeField(auto_now=True, db_index=True)),
                (
                    "user_id",
                    models.BigIntegerField(
                        help_text="Unique identifier for the user",
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "username",
                    models.CharField(
                        blank=True,
                        help_text="Username of the user",
                        max_length=32,
                        null=True,
                    ),
                ),
                (
                    "first_name",
                    models.CharField(
                        blank=True, help_text="First name of the user", max_length=64
                    ),
                ),
                (
                    "last_name",
                    models.CharField(
                        blank=True,
                        help_text="Last name of the user",
                        max_length=64,
                        null=True,
                    ),
                ),
                (
                    "phone",
                    models.CharField(
                        blank=True,
                        help_text="Phone number of the user",
                        max_length=20,
                        null=True,
                    ),
                ),
                (
                    "is_bot",
                    models.BooleanField(
                        default=False, help_text="Whether the user is a bot"
                    ),
                ),
                (
                    "is_verified",
                    models.BooleanField(
                        default=False, help_text="Whether the user is verified"
                    ),
                ),
                (
                    "is_restricted",
                    models.BooleanField(
                        default=False, help_text="Whether the user is restricted"
                    ),
                ),
                (
                    "is_deleted",
                    models.BooleanField(
                        default=False, help_text="Whether the user is deleted"
                    ),
                ),
                (
                    "is_premium",
                    models.BooleanField(
                        default=False, help_text="Whether the user has Telegram Premium"
                    ),
                ),
                (
                    "profile_photo_id",
                    models.BigIntegerField(
                        blank=True,
                        help_text="ID of the user's profile photo",
                        null=True,
                    ),
                ),
                (
                    "lang_code",
                    models.CharField(
                        blank=True,
                        help_text="User's language code",
                        max_length=10,
                        null=True,
                    ),
                ),
                (
                    "last_seen_date",
                    models.DateTimeField(
                        blank=True,
                        help_text="Last time the user was seen online",
                        null=True,
                    ),
                ),
            ],
            options={
                "verbose_name": "Telegram User",
                "verbose_name_plural": "Telegram Users",
                "indexes": [
                    models.Index(
                        fields=["username"], name="telegram_ma_usernam_17dd88_idx"
                    ),
                    models.Index(
                        fields=["is_deleted", "created_at"],
                        name="telegram_ma_is_dele_988cdf_idx",
                    ),
                ],
            },
        ),
        migrations.CreateModel(
            name="TelegramMessage",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True, db_index=True)),
                ("updated_at", models.DateTimeField(auto_now=True, db_index=True)),
                (
                    "message_id",
                    models.BigIntegerField(
                        help_text="Unique identifier for the message"
                    ),
                ),
                (
                    "from_id",
                    models.BigIntegerField(
                        blank=True,
                        help_text="Original user ID from Telegram",
                        null=True,
                    ),
                ),
                (
                    "date",
                    models.DateTimeField(
                        help_text="Date and time when the message was sent"
                    ),
                ),
                (
                    "text",
                    models.TextField(
                        blank=True,
                        help_text="Text content of the message",
                        max_length=4096,
                    ),
                ),
                (
                    "fwd_date",
                    models.DateTimeField(
                        blank=True,
                        help_text="Original message date if forwarded",
                        null=True,
                    ),
                ),
                (
                    "fwd_from_msg_id",
                    models.BigIntegerField(
                        blank=True,
                        help_text="Original message ID if forwarded",
                        null=True,
                    ),
                ),
                (
                    "photo",
                    models.JSONField(
                        blank=True, help_text="Photo attachment data", null=True
                    ),
                ),
                (
                    "video",
                    models.JSONField(
                        blank=True, help_text="Video attachment data", null=True
                    ),
                ),
                (
                    "document",
                    models.JSONField(
                        blank=True, help_text="Document attachment data", null=True
                    ),
                ),
                (
                    "audio",
                    models.JSONField(
                        blank=True, help_text="Audio attachment data", null=True
                    ),
                ),
                (
                    "voice",
                    models.JSONField(
                        blank=True, help_text="Voice message data", null=True
                    ),
                ),
                (
                    "sticker",
                    models.JSONField(blank=True, help_text="Sticker data", null=True),
                ),
                (
                    "animation",
                    models.JSONField(
                        blank=True, help_text="Animation/GIF data", null=True
                    ),
                ),
                (
                    "poll",
                    models.JSONField(blank=True, help_text="Poll data", null=True),
                ),
                (
                    "location",
                    models.JSONField(blank=True, help_text="Location data", null=True),
                ),
                (
                    "venue",
                    models.JSONField(blank=True, help_text="Venue data", null=True),
                ),
                (
                    "contact",
                    models.JSONField(blank=True, help_text="Contact data", null=True),
                ),
                (
                    "media_type",
                    models.CharField(
                        blank=True, help_text="Type of media attachment", max_length=50
                    ),
                ),
                (
                    "is_private",
                    models.BooleanField(
                        default=False, help_text="Whether message is in private chat"
                    ),
                ),
                (
                    "is_group",
                    models.BooleanField(
                        default=False, help_text="Whether message is in group"
                    ),
                ),
                (
                    "is_channel",
                    models.BooleanField(
                        default=False, help_text="Whether message is in channel"
                    ),
                ),
                (
                    "is_reply",
                    models.BooleanField(
                        default=False, help_text="Whether this is a reply"
                    ),
                ),
                (
                    "is_forward",
                    models.BooleanField(
                        default=False, help_text="Whether this is forwarded"
                    ),
                ),
                (
                    "is_edited",
                    models.BooleanField(
                        default=False, help_text="Whether message was edited"
                    ),
                ),
                (
                    "action",
                    models.JSONField(
                        blank=True, help_text="Service message action data", null=True
                    ),
                ),
                (
                    "action_type",
                    models.CharField(
                        blank=True, help_text="Type of service action", max_length=100
                    ),
                ),
                (
                    "edit_date",
                    models.DateTimeField(
                        blank=True, help_text="Date of last edit", null=True
                    ),
                ),
                (
                    "views",
                    models.IntegerField(
                        blank=True,
                        help_text="View count for channel messages",
                        null=True,
                    ),
                ),
                (
                    "forwards",
                    models.IntegerField(
                        blank=True, help_text="Forward count", null=True
                    ),
                ),
                (
                    "inline_query",
                    models.CharField(
                        blank=True,
                        help_text="Inline query if from inline bot",
                        max_length=255,
                    ),
                ),
                (
                    "entities",
                    models.JSONField(
                        blank=True,
                        help_text="Message entities (URLs, mentions, etc)",
                        null=True,
                    ),
                ),
                (
                    "buttons",
                    models.JSONField(
                        blank=True, help_text="Inline keyboard buttons", null=True
                    ),
                ),
                (
                    "web_preview",
                    models.JSONField(
                        blank=True, help_text="Web page preview data", null=True
                    ),
                ),
                (
                    "grouped_id",
                    models.BigIntegerField(
                        blank=True, help_text="ID for grouped messages", null=True
                    ),
                ),
                (
                    "signature",
                    models.CharField(
                        blank=True,
                        help_text="Signature for channel posts",
                        max_length=255,
                        null=True,
                    ),
                ),
                (
                    "post_author",
                    models.CharField(
                        blank=True,
                        help_text="Author signature for channel posts",
                        max_length=255,
                        null=True,
                    ),
                ),
                (
                    "mentioned",
                    models.BooleanField(
                        default=False, help_text="Whether current user was mentioned"
                    ),
                ),
                (
                    "scheduled",
                    models.BooleanField(
                        default=False, help_text="Whether this is a scheduled message"
                    ),
                ),
                (
                    "ttl_period",
                    models.IntegerField(
                        blank=True,
                        help_text="TTL for self-destructing messages",
                        null=True,
                    ),
                ),
                (
                    "pinned",
                    models.BooleanField(
                        default=False, help_text="Whether message is pinned"
                    ),
                ),
                (
                    "media_file",
                    models.FileField(
                        blank=True,
                        help_text="Downloaded media file",
                        null=True,
                        upload_to="telegram_media/",
                    ),
                ),
                (
                    "chat",
                    models.ForeignKey(
                        help_text="Chat where the message was sent",
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="messages",
                        to="telegram_manager.telegramchat",
                    ),
                ),
                (
                    "fwd_from_chat",
                    models.ForeignKey(
                        blank=True,
                        help_text="Original chat if forwarded",
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="forwarded_from_messages",
                        to="telegram_manager.telegramchat",
                    ),
                ),
                (
                    "reply_to",
                    models.ForeignKey(
                        blank=True,
                        help_text="Message this message is replying to",
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="replies",
                        to="telegram_manager.telegrammessage",
                    ),
                ),
                (
                    "action_users",
                    models.ManyToManyField(
                        blank=True,
                        help_text="Users involved in service actions (join, leave, etc.)",
                        related_name="involved_in_actions",
                        to="telegram_manager.telegramuser",
                    ),
                ),
                (
                    "from_user",
                    models.ForeignKey(
                        blank=True,
                        help_text="User who sent the message",
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="sent_messages",
                        to="telegram_manager.telegramuser",
                    ),
                ),
                (
                    "fwd_from_user",
                    models.ForeignKey(
                        blank=True,
                        help_text="Original sender if forwarded",
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="forwarded_messages",
                        to="telegram_manager.telegramuser",
                    ),
                ),
                (
                    "mentioned_users",
                    models.ManyToManyField(
                        blank=True,
                        help_text="Users mentioned in this message",
                        related_name="mentioned_in",
                        to="telegram_manager.telegramuser",
                    ),
                ),
                (
                    "via_bot",
                    models.ForeignKey(
                        blank=True,
                        help_text="Bot that sent this message",
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="bot_messages",
                        to="telegram_manager.telegramuser",
                    ),
                ),
            ],
            options={
                "verbose_name": "Telegram Message",
                "verbose_name_plural": "Telegram Messages",
                "indexes": [
                    models.Index(fields=["date"], name="telegram_ma_date_4bdb4c_idx"),
                    models.Index(
                        fields=["from_user"], name="telegram_ma_from_us_1da429_idx"
                    ),
                    models.Index(
                        fields=["reply_to"], name="telegram_ma_reply_t_aa2d90_idx"
                    ),
                    models.Index(
                        fields=["is_forward"], name="telegram_ma_is_forw_401b33_idx"
                    ),
                    models.Index(
                        fields=["media_type"], name="telegram_ma_media_t_fda2b3_idx"
                    ),
                ],
                "unique_together": {("message_id", "chat")},
            },
        ),
    ]
