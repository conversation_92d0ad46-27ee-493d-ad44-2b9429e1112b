from datetime import datetime
from typing import Any

from django.db.models import Count, QuerySet

from core.repositories.base import BaseRepository
from core.repositories.mixins import (
    BulkOperationsMixin,
    DateFilterMixin,
    SearchMixin,
    StatisticsMixin,
)
from telegram_manager.models import TelegramChat, TelegramMessage, TelegramUser


class TelegramMessageRepository(
    BaseRepository[TelegramMessage],
    BulkOperationsMixin,
    DateFilterMixin,
    SearchMixin,
    StatisticsMixin,
):
    """
    Репозиторий для работы с Telegram сообщениями.
    """
    
    # Оптимизация запросов
    select_related_fields = ["chat", "from_user", "reply_to", "fwd_from_user"]
    prefetch_related_fields = []
    
    @property
    def model(self):
        return TelegramMessage
    
    def get_by_message_and_chat_id(self, message_id: int, chat_id: int) -> TelegramMessage | None:
        """
        Получает сообщение по message_id и chat_id.
        
        Args:
            message_id: ID сообщения в Telegram
            chat_id: ID чата в Telegram
            
        Returns:
            Объект TelegramMessage или None
        """
        return self.get_or_none(message_id=message_id, chat_id=chat_id)
    
    def filter_by_chat(self, chat_id: int) -> QuerySet[TelegramMessage]:
        """
        Фильтрует сообщения по чату.
        
        Args:
            chat_id: ID чата
            
        Returns:
            QuerySet сообщений в чате
        """
        queryset = self.filter(chat_id=chat_id)
        queryset = self._apply_optimizations(queryset)
        return queryset.order_by("-date")
    
    def filter_by_user(self, user_id: int) -> QuerySet[TelegramMessage]:
        """
        Фильтрует сообщения по отправителю.
        
        Args:
            user_id: ID пользователя
            
        Returns:
            QuerySet сообщений пользователя
        """
        queryset = self.filter(from_user_id=user_id)
        queryset = self._apply_optimizations(queryset)
        return queryset.order_by("-date")
    
    def filter_by_media_type(self, media_type: str) -> QuerySet[TelegramMessage]:
        """
        Фильтрует сообщения по типу медиа.
        
        Args:
            media_type: Тип медиа (photo, video, document, etc.)
            
        Returns:
            QuerySet сообщений с указанным типом медиа
        """
        return self.filter(media_type=media_type)
    
    def filter_text_messages(self) -> QuerySet[TelegramMessage]:
        """
        Возвращает только текстовые сообщения (без медиа).
        
        Returns:
            QuerySet текстовых сообщений
        """
        return self.filter(media_type="").exclude(text="")
    
    def filter_media_messages(self) -> QuerySet[TelegramMessage]:
        """
        Возвращает сообщения с медиа вложениями.
        
        Returns:
            QuerySet сообщений с медиа
        """
        return self.filter().exclude(media_type="")
    
    def filter_replies(self) -> QuerySet[TelegramMessage]:
        """
        Возвращает сообщения-ответы.
        
        Returns:
            QuerySet ответов
        """
        return self.filter(is_reply=True)
    
    def filter_forwards(self) -> QuerySet[TelegramMessage]:
        """
        Возвращает пересланные сообщения.
        
        Returns:
            QuerySet пересланных сообщений
        """
        return self.filter(is_forward=True)
    
    def filter_edited(self) -> QuerySet[TelegramMessage]:
        """
        Возвращает отредактированные сообщения.
        
        Returns:
            QuerySet отредактированных сообщений
        """
        return self.filter(is_edited=True)
    
    def filter_by_date_range(self, date_from: datetime = None, 
                            date_to: datetime = None) -> QuerySet[TelegramMessage]:
        """
        Фильтрует сообщения по диапазону дат.
        
        Args:
            date_from: Начальная дата
            date_to: Конечная дата
            
        Returns:
            QuerySet отфильтрованных сообщений
        """
        queryset = self.all()
        
        if date_from:
            queryset = queryset.filter(date__gte=date_from)
        
        if date_to:
            queryset = queryset.filter(date__lte=date_to)
        
        return queryset.order_by("-date")
    
    def search_text(self, query: str, chat_id: int | None = None) -> QuerySet[TelegramMessage]:
        """
        Поиск по тексту сообщений.
        
        Args:
            query: Поисковый запрос
            chat_id: ID чата для ограничения поиска
            
        Returns:
            QuerySet найденных сообщений
        """
        queryset = self.filter(text__icontains=query)
        
        if chat_id:
            queryset = queryset.filter(chat_id=chat_id)
        
        queryset = self._apply_optimizations(queryset)
        return queryset.order_by("-date")
    
    def get_replies_to_message(self, message_id: int, chat_id: int) -> QuerySet[TelegramMessage]:
        """
        Получает ответы на конкретное сообщение.
        
        Args:
            message_id: ID исходного сообщения
            chat_id: ID чата
            
        Returns:
            QuerySet ответов
        """
        queryset = self.filter(
            reply_to__message_id=message_id,
            reply_to__chat_id=chat_id
        )
        queryset = self._apply_optimizations(queryset)
        return queryset.order_by("date")
    
    def get_thread_messages(self, message: TelegramMessage) -> QuerySet[TelegramMessage]:
        """
        Получает все сообщения в цепочке ответов.
        
        Args:
            message: Сообщение из цепочки
            
        Returns:
            QuerySet сообщений в цепочке
        """
        # Находим корневое сообщение
        root = message
        while root.reply_to:
            root = root.reply_to
        
        # Получаем все сообщения в цепочке
        thread_ids = [root.id]
        to_check = [root]
        
        while to_check:
            current = to_check.pop(0)
            replies = self.filter(reply_to=current)
            for reply in replies:
                if reply.id not in thread_ids:
                    thread_ids.append(reply.id)
                    to_check.append(reply)
        
        return self.filter(id__in=thread_ids).order_by("date")
    
    def get_mentioned_messages(self, user_id: int) -> QuerySet[TelegramMessage]:
        """
        Получает сообщения, где упомянут пользователь.
        
        Args:
            user_id: ID пользователя
            
        Returns:
            QuerySet сообщений с упоминанием
        """
        queryset = self.filter(mentioned_users__user_id=user_id)
        queryset = self._apply_optimizations(queryset)
        return queryset.order_by("-date")
    
    def get_messages_with_actions(self) -> QuerySet[TelegramMessage]:
        """
        Получает сообщения с действиями (присоединение, выход и т.д.).
        
        Returns:
            QuerySet сообщений с действиями
        """
        queryset = self.filter().exclude(action_type="")
        queryset = self._apply_optimizations(queryset)
        return queryset.order_by("-date")
    
    def get_chat_statistics(self, chat_id: int) -> dict[str, Any]:
        """
        Получает статистику по чату.
        
        Args:
            chat_id: ID чата
            
        Returns:
            Словарь со статистикой
        """
        messages = self.filter_by_chat(chat_id)
        
        stats: dict[str, Any] = {
            "total_messages": messages.count(),
            "text_messages": messages.filter(media_type="").exclude(text="").count(),
            "media_messages": messages.exclude(media_type="").count(),
            "forwards": messages.filter(is_forward=True).count(),
            "replies": messages.filter(is_reply=True).count(),
            "edited": messages.filter(is_edited=True).count(),
        }
        
        # Статистика по типам медиа
        media_stats = messages.exclude(media_type="").values("media_type").annotate(
            count=Count("id")
        ).order_by("-count")
        
        stats["media_by_type"] = {item["media_type"]: item["count"] for item in media_stats}
        
        # Топ авторов
        top_authors = messages.values("from_user__username", "from_user__first_name").annotate(
            message_count=Count("id")
        ).order_by("-message_count")[:10]
        
        stats["top_authors"] = list(top_authors)
        
        return stats
    
    def create_from_telegram_data(self, message_data: dict[str, Any], 
                                 chat: TelegramChat) -> TelegramMessage:
        """
        Создает сообщение из данных Telegram API.
        
        Args:
            message_data: Данные сообщения от Telegram API
            chat: Объект чата
            
        Returns:
            Созданное сообщение
        """
        # Базовые поля
        message_dict = {
            "message_id": message_data["id"],
            "chat": chat,
            "date": message_data["date"],
            "text": message_data.get("message", ""),
            "from_id": message_data.get("from_id"),
            "is_reply": bool(message_data.get("reply_to")),
            "is_forward": bool(message_data.get("fwd_from")),
            "is_edited": bool(message_data.get("edit_date")),
            "edit_date": message_data.get("edit_date"),
            "views": message_data.get("views"),
            "forwards": message_data.get("forwards"),
            "pinned": message_data.get("pinned", False),
            "mentioned": message_data.get("mentioned", False),
        }
        
        # Определяем тип чата
        message_dict["is_channel"] = chat.broadcast
        message_dict["is_group"] = not chat.broadcast and not message_data.get("is_private")
        message_dict["is_private"] = message_data.get("is_private", False)
        
        # Обрабатываем медиа
        media_type, media_data = self._extract_media_info(message_data)
        if media_type:
            message_dict["media_type"] = media_type
            message_dict[media_type] = media_data
        
        # Обрабатываем entities
        if message_data.get("entities"):
            message_dict["entities"] = message_data["entities"]
        
        # Обрабатываем действия
        if message_data.get("action"):
            message_dict["action"] = message_data["action"]
            message_dict["action_type"] = message_data["action"].get("_", "")
        
        # Создаем сообщение
        message = self.create(**message_dict)
        
        # Обрабатываем связи после создания
        self._process_message_relations(message, message_data)
        
        return message
    
    def _extract_media_info(self, message_data: dict[str, Any]) -> tuple[str | None, dict | None]:
        """
        Извлекает информацию о медиа из данных сообщения.
        
        Args:
            message_data: Данные сообщения
            
        Returns:
            Кортеж (media_type, media_data)
        """
        media_types = ["photo", "video", "document", "audio", "voice", 
                      "sticker", "animation", "poll", "location", "venue", "contact"]
        
        for media_type in media_types:
            if message_data.get(media_type):
                return media_type, message_data[media_type]
        
        return None, None
    
    def _process_message_relations(self, message: TelegramMessage, message_data: dict[str, Any]):
        """
        Обрабатывает связи сообщения (пользователи, ответы и т.д.).
        
        Args:
            message: Созданное сообщение
            message_data: Данные от Telegram API
        """
        # Обработка отправителя
        if message_data.get("from_id"):
            try:
                from_user = TelegramUser.objects.get(pk=message_data["from_id"])
                message.from_user = from_user
                message.save(update_fields=["from_user"])
            except TelegramUser.DoesNotExist:
                pass
        
        # Обработка пересылки
        if message_data.get("fwd_from"):
            fwd_data = message_data["fwd_from"]
            message.fwd_date = fwd_data.get("date")
            message.fwd_from_msg_id = fwd_data.get("channel_post")
            
            if fwd_data.get("from_id"):
                try:
                    fwd_user = TelegramUser.objects.get(pk=fwd_data["from_id"])
                    message.fwd_from_user = fwd_user
                except TelegramUser.DoesNotExist:
                    pass
            
            message.save(update_fields=["fwd_date", "fwd_from_msg_id", "fwd_from_user"])
        
        # Обработка ответа
        if message_data.get("reply_to") and message_data["reply_to"].get("reply_to_msg_id"):
            reply_id = message_data["reply_to"]["reply_to_msg_id"]
            try:
                reply_to = self.get_by_message_and_chat_id(reply_id, message.chat_id)
                message.reply_to = reply_to
                message.save(update_fields=["reply_to"])
            except Exception:
                pass
    
    def bulk_create_from_telegram_data(self, messages_data: list[dict[str, Any]], 
                                      chat: TelegramChat) -> list[TelegramMessage]:
        """
        Массовое создание сообщений из данных Telegram API.
        
        Args:
            messages_data: Список данных сообщений
            chat: Объект чата
            
        Returns:
            Список созданных сообщений
        """
        messages = []
        
        for msg_data in messages_data:
            try:
                # Проверяем, не существует ли уже сообщение
                exists = self.exists(
                    message_id=msg_data["id"],
                    chat_id=chat.chat_id
                )
                
                if not exists:
                    message = self.create_from_telegram_data(msg_data, chat)
                    messages.append(message)
                    
            except Exception as e:
                # Логируем ошибку и продолжаем
                print(f"Error creating message {msg_data.get('id')}: {e}")
                continue
        
        return messages
    
    def bulk_create_or_update(self, messages_data: list[dict[str, Any]]) -> tuple[int, int]:
        """
        Массовое создание или обновление сообщений.
        
        Args:
            messages_data: Список данных сообщений
            
        Returns:
            Кортеж (количество созданных, количество обновленных)
        """
        created_count = 0
        updated_count = 0
        
        for msg_data in messages_data:
            message_id = msg_data.get("message_id")
            chat_id = msg_data.get("chat_id")
            
            if not message_id or not chat_id:
                continue
                
            # Извлекаем chat FK
            chat_data = msg_data.pop("chat", None)
            if chat_data:
                try:
                    chat = TelegramChat.objects.get(chat_id=chat_id)
                    msg_data["chat"] = chat
                except Exception:
                    continue
                    
            message, created = TelegramMessage.objects.update_or_create(
                message_id=message_id,
                chat_id=chat_id,
                defaults=msg_data
            )
            
            if created:
                created_count += 1
            else:
                updated_count += 1
                
        return created_count, updated_count
    
    def count_user_messages(self, user_id: int) -> int:
        """
        Подсчитывает количество сообщений пользователя.
        
        Args:
            user_id: ID пользователя
            
        Returns:
            Количество сообщений
        """
        return self.filter(from_user_id=user_id).count()
    
    def get_latest_message_date(self, chat_id: int) -> datetime | None:
        """
        Получает дату последнего сообщения в чате.
        
        Args:
            chat_id: ID чата
            
        Returns:
            Дата последнего сообщения или None
        """
        latest = self.filter(chat_id=chat_id).order_by("-date").first()
        return latest.date if latest else None
    
    # Light методы для оптимизации производительности
    
    def get_messages_list_light(self, chat_id: int | None = None) -> QuerySet[TelegramMessage]:
        """
        Получить легкий список сообщений без медиа полей.
        
        Загружает только: id, message_id, chat_id, from_user_id, 
        date, text, is_forward, is_reply, media_type.
        
        Args:
            chat_id: ID чата для фильтрации (опционально)
            
        Returns:
            QuerySet с минимальными полями
        """
        only_fields = [
            "id", "message_id", "chat_id", "from_user_id",
            "date", "text", "is_forward", "is_reply", 
            "media_type", "views", "forwards"
        ]
        
        queryset = self.filter(only_fields=only_fields).select_related("chat", "from_user")
        
        if chat_id:
            queryset = queryset.filter(chat_id=chat_id)
            
        return queryset.order_by("-date")
    
    def get_messages_text_only(self, chat_id: int) -> QuerySet[TelegramMessage]:
        """
        Получить только текст сообщений для экспорта.
        
        Args:
            chat_id: ID чата
            
        Returns:
            QuerySet только с текстовыми полями
        """
        only_fields = [
            "id", "message_id", "from_user_id", "date", "text",
            "from_user__username", "from_user__first_name"
        ]
        
        return self.filter(
            chat_id=chat_id,
            only_fields=only_fields
        ).select_related("from_user").order_by("date")
    
    def get_messages_for_export(self, chat_id: int) -> QuerySet[TelegramMessage]:
        """
        Получить сообщения для экспорта без служебных полей.
        
        Args:
            chat_id: ID чата
            
        Returns:
            QuerySet для экспорта
        """
        defer_fields = [
            "photo", "video", "document", "audio", "voice",
            "sticker", "animation", "poll", "location", "venue",
            "contact", "entities", "buttons", "web_preview",
            "action", "media_file"
        ]
        
        return self.filter(
            chat_id=chat_id,
            defer_fields=defer_fields
        ).select_related("from_user", "reply_to").order_by("date")
    
    def get_media_messages_light(self, chat_id: int | None = None) -> QuerySet[TelegramMessage]:
        """
        Получить легкий список медиа сообщений.
        
        Args:
            chat_id: ID чата для фильтрации (опционально)
            
        Returns:
            QuerySet с медиа сообщениями
        """
        only_fields = [
            "id", "message_id", "chat_id", "from_user_id",
            "date", "media_type", "text"
        ]
        
        queryset = self.filter(
            only_fields=only_fields
        ).exclude(media_type="")
        
        if chat_id:
            queryset = queryset.filter(chat_id=chat_id)
            
        return queryset.select_related("from_user").order_by("-date")
    
    def get_messages_for_search(self) -> QuerySet[TelegramMessage]:
        """
        Получить сообщения для поиска с минимальными полями.
        
        Returns:
            QuerySet для поиска
        """
        only_fields = [
            "id", "message_id", "chat_id", "text", "date"
        ]
        
        return self.filter(only_fields=only_fields).exclude(text="")