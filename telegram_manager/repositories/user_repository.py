from datetime import datetime
from typing import Any, cast

from django.db.models import Count, Exists, OuterRef, Q, QuerySet

from core.repositories.base import BaseRepository
from core.repositories.mixins import (
    BulkOperationsMixin,
    DateFilterMixin,
    SearchMixin,
    SoftDeleteMixin,
    StatisticsMixin,
)
from telegram_manager.models import TelegramMessage, TelegramUser


class TelegramUserRepository(
    BaseRepository[TelegramUser],
    BulkOperationsMixin,
    DateFilterMixin,
    SearchMixin,
    SoftDeleteMixin,
    StatisticsMixin,
):
    """
    Репозиторий для работы с Telegram пользователями.
    """
    
    @property
    def model(self):
        return TelegramUser
    
    def get_by_user_id(self, user_id: int) -> TelegramUser | None:
        """
        Получает пользователя по Telegram user_id.
        
        Args:
            user_id: ID пользователя в Telegram
            
        Returns:
            Объект TelegramUser или None
        """
        try:
            return self.get_by_id(user_id)
        except Exception:
            return None
    
    def get_by_username(self, username: str) -> TelegramUser | None:
        """
        Получает пользователя по username.
        
        Args:
            username: Username пользователя (без @)
            
        Returns:
            Объект TelegramUser или None
        """
        # Убираем @ если есть
        username = username.lstrip("@")
        return self.get_or_none(username=username)
    
    def filter_active_users(self) -> QuerySet[TelegramUser]:
        """
        Возвращает активных (не удаленных) пользователей.
        
        Returns:
            QuerySet активных пользователей
        """
        return self.filter(is_deleted=False)
    
    def filter_bots(self) -> QuerySet[TelegramUser]:
        """
        Возвращает только ботов.
        
        Returns:
            QuerySet ботов
        """
        return self.filter(is_bot=True)
    
    def filter_humans(self) -> QuerySet[TelegramUser]:
        """
        Возвращает только людей (не ботов).
        
        Returns:
            QuerySet людей
        """
        return self.filter(is_bot=False)
    
    def filter_premium_users(self) -> QuerySet[TelegramUser]:
        """
        Возвращает пользователей с Telegram Premium.
        
        Returns:
            QuerySet premium пользователей
        """
        return self.filter(is_premium=True)
    
    def filter_verified_users(self) -> QuerySet[TelegramUser]:
        """
        Возвращает верифицированных пользователей.
        
        Returns:
            QuerySet верифицированных пользователей
        """
        return self.filter(is_verified=True)
    
    def search_users(self, query: str) -> QuerySet[TelegramUser]:
        """
        Поиск пользователей по имени или username.
        
        Args:
            query: Поисковый запрос
            
        Returns:
            QuerySet найденных пользователей
        """
        return cast(QuerySet[TelegramUser], self.model.objects.filter(
            Q(username__icontains=query) |
            Q(first_name__icontains=query) |
            Q(last_name__icontains=query)
        ))
    
    def get_users_with_message_count(self) -> QuerySet[TelegramUser]:
        """
        Возвращает пользователей с количеством отправленных сообщений.
        
        Returns:
            QuerySet с аннотированным количеством сообщений
        """
        return self.all().annotate(
            sent_message_count=Count("sent_messages")
        ).order_by("-sent_message_count")
    
    def filter_by_last_seen(self, date_from: datetime = None, 
                           date_to: datetime = None) -> QuerySet[TelegramUser]:
        """
        Фильтрует пользователей по дате последнего онлайна.
        
        Args:
            date_from: Начальная дата
            date_to: Конечная дата
            
        Returns:
            QuerySet отфильтрованных пользователей
        """
        queryset = self.all()
        
        if date_from:
            queryset = queryset.filter(last_seen_date__gte=date_from)
        
        if date_to:
            queryset = queryset.filter(last_seen_date__lte=date_to)
        
        return queryset
    
    def get_or_create_from_telegram_data(self, user_data: dict[str, Any]) -> tuple[TelegramUser, bool]:
        """
        Создает или обновляет пользователя из данных Telegram API.
        
        Args:
            user_data: Данные пользователя от Telegram API
            
        Returns:
            Кортеж (user, created)
        """
        user_id = user_data.get("id")
        
        defaults = {
            "username": user_data.get("username"),
            "first_name": user_data.get("first_name", ""),
            "last_name": user_data.get("last_name"),
            "phone": user_data.get("phone"),
            "is_bot": user_data.get("bot", False),
            "is_verified": user_data.get("verified", False),
            "is_restricted": user_data.get("restricted", False),
            "is_deleted": user_data.get("deleted", False),
            "is_premium": user_data.get("premium", False),
            "lang_code": user_data.get("lang_code"),
        }
        
        # Убираем None значения
        defaults = {k: v for k, v in defaults.items() if v is not None}
        
        return self.update_or_create(
            user_id=user_id,
            defaults=defaults
        )
    
    def get_orphaned_users(self) -> QuerySet[TelegramUser]:
        """
        Возвращает пользователей без ссылок из сообщений.
        
        Returns:
            QuerySet пользователей-сирот
        """
        # Подзапрос для проверки существования ссылок
        has_sent_messages = Exists(
            TelegramMessage.objects.filter(from_user_id=OuterRef("user_id"))
        )
        has_mentions = Exists(
            TelegramMessage.objects.filter(mentioned_users__user_id=OuterRef("user_id"))
        )
        has_actions = Exists(
            TelegramMessage.objects.filter(action_users__user_id=OuterRef("user_id"))
        )
        has_forwards = Exists(
            TelegramMessage.objects.filter(fwd_from_user_id=OuterRef("user_id"))
        )
        has_bot_messages = Exists(
            TelegramMessage.objects.filter(via_bot_id=OuterRef("user_id"))
        )
        
        # Возвращаем пользователей без ссылок
        return self.all().annotate(
            has_sent=has_sent_messages,
            has_mentions=has_mentions,
            has_actions=has_actions,
            has_forwards=has_forwards,
            has_bot_msgs=has_bot_messages
        ).filter(
            has_sent=False,
            has_mentions=False,
            has_actions=False,
            has_forwards=False,
            has_bot_msgs=False
        )
    
    def delete_orphaned_users(self) -> int:
        """
        Удаляет пользователей без ссылок из сообщений.
        
        Returns:
            Количество удаленных пользователей
        """
        orphaned = self.get_orphaned_users()
        count = orphaned.count()
        orphaned.delete()
        return count
    
    def check_user_is_orphaned(self, user_id: int) -> bool:
        """
        Проверяет, является ли пользователь сиротой.
        
        Args:
            user_id: ID пользователя
            
        Returns:
            True если пользователь не имеет ссылок
        """
        has_references = TelegramMessage.objects.filter(
            Q(from_user_id=user_id) |
            Q(mentioned_users__user_id=user_id) |
            Q(action_users__user_id=user_id) |
            Q(fwd_from_user_id=user_id) |
            Q(via_bot_id=user_id)
        ).exists()
        
        return not has_references
    
    def bulk_update_from_telegram_data(self, users_data: list[dict[str, Any]]) -> int:
        """
        Массовое обновление пользователей из данных Telegram API.
        
        Args:
            users_data: Список данных пользователей от Telegram API
            
        Returns:
            Количество обновленных пользователей
        """
        users_to_update = []
        
        for user_data in users_data:
            user_id = user_data.get("id")
            if not user_id:
                continue
            
            try:
                user = self.get_by_id(user_id)
                
                # Обновляем поля
                user.username = user_data.get("username", user.username)
                user.first_name = user_data.get("first_name", user.first_name)
                user.last_name = user_data.get("last_name", user.last_name)
                user.is_bot = user_data.get("bot", user.is_bot)
                user.is_verified = user_data.get("verified", user.is_verified)
                user.is_premium = user_data.get("premium", user.is_premium)
                
                users_to_update.append(user)
                
            except Exception:
                # Если пользователь не существует, пропускаем
                continue
        
        if users_to_update:
            return self.bulk_update(
                users_to_update,
                ["username", "first_name", "last_name", "is_bot", "is_verified", "is_premium"]
            )
        
        return 0
    
    def get_mentioned_users_in_chat(self, chat_id: int) -> QuerySet[TelegramUser]:
        """
        Возвращает пользователей, упомянутых в конкретном чате.
        
        Args:
            chat_id: ID чата
            
        Returns:
            QuerySet упомянутых пользователей
        """
        return self.filter(
            mentioned_in__chat_id=chat_id
        ).distinct()
    
    def get_active_users_in_chat(self, chat_id: int) -> QuerySet[TelegramUser]:
        """
        Возвращает пользователей, отправлявших сообщения в чат.
        
        Args:
            chat_id: ID чата
            
        Returns:
            QuerySet активных пользователей
        """
        return self.filter(
            sent_messages__chat_id=chat_id
        ).distinct()
    
    def bulk_create_or_update(self, users_data: list[dict[str, Any]]) -> tuple[int, int]:
        """
        Массовое создание или обновление пользователей.
        
        Args:
            users_data: Список данных пользователей
            
        Returns:
            Кортеж (количество созданных, количество обновленных)
        """
        created_count = 0
        updated_count = 0
        
        for user_data in users_data:
            user_id = user_data.get("user_id")
            if not user_id:
                continue
                
            user, created = TelegramUser.objects.update_or_create(
                user_id=user_id,
                defaults=user_data
            )
            
            if created:
                created_count += 1
            else:
                updated_count += 1
                
        return created_count, updated_count