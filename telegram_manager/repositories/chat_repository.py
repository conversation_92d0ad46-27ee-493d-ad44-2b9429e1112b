from datetime import datetime
from typing import Any, cast

from django.db.models import Count, Q, QuerySet

from core.repositories.base import BaseRepository
from core.repositories.mixins import (
    BulkOperationsMixin,
    DateFilterMixin,
    SearchMixin,
    StatisticsMixin,
)
from telegram_manager.models import TelegramChat


class TelegramChatRepository(
    BaseRepository[TelegramChat],
    BulkOperationsMixin,
    DateFilterMixin,
    SearchMixin,
    StatisticsMixin,
):
    """
    Репозиторий для работы с Telegram чатами.
    """
    
    # Оптимизация запросов
    select_related_fields = []
    prefetch_related_fields = ["messages", "messages__from_user"]
    
    @property
    def model(self):
        return TelegramChat
    
    def get_by_chat_id(self, chat_id: int) -> TelegramChat | None:
        """
        Получает чат по Telegram chat_id.
        
        Args:
            chat_id: ID чата в Telegram
            
        Returns:
            Объект TelegramChat или None
        """
        try:
            return self.get_by_id(chat_id)
        except Exception:
            return None
    
    def get_by_username(self, username: str) -> TelegramChat | None:
        """
        Получает чат по username.
        
        Args:
            username: Username чата (без @)
            
        Returns:
            Объект TelegramChat или None
        """
        # Убираем @ если есть
        username = username.lstrip("@")
        return self.get_or_none(username=username)
    
    def filter_active_chats(self) -> QuerySet[TelegramChat]:
        """
        Возвращает активные чаты (где пользователь не покинул).
        
        Returns:
            QuerySet активных чатов
        """
        queryset = self.filter(left=False)
        return self._apply_optimizations(queryset)
    
    def filter_channels(self) -> QuerySet[TelegramChat]:
        """
        Возвращает только каналы.
        
        Returns:
            QuerySet каналов
        """
        queryset = self.filter(broadcast=True)
        return self._apply_optimizations(queryset)
    
    def filter_groups(self) -> QuerySet[TelegramChat]:
        """
        Возвращает только группы.
        
        Returns:
            QuerySet групп
        """
        queryset = self.filter(broadcast=False)
        return self._apply_optimizations(queryset)
    
    def filter_megagroups(self) -> QuerySet[TelegramChat]:
        """
        Возвращает только супергруппы.
        
        Returns:
            QuerySet супергрупп
        """
        queryset = self.filter(megagroup=True)
        return self._apply_optimizations(queryset)
    
    def filter_owned_chats(self) -> QuerySet[TelegramChat]:
        """
        Возвращает чаты, созданные пользователем.
        
        Returns:
            QuerySet собственных чатов
        """
        return self.filter(creator=True)
    
    def search_chats(self, query: str) -> QuerySet[TelegramChat]:
        """
        Поиск чатов по названию или username.
        
        Args:
            query: Поисковый запрос
            
        Returns:
            QuerySet найденных чатов
        """
        return cast(QuerySet[TelegramChat], self.model.objects.filter(
            Q(title__icontains=query) | 
            Q(username__icontains=query)
        ))
    
    def get_chats_with_message_count(self) -> QuerySet[TelegramChat]:
        """
        Возвращает чаты с количеством сообщений.
        
        Returns:
            QuerySet с аннотированным количеством сообщений
        """
        queryset = self.all().annotate(
            message_count=Count("messages")
        )
        # Для агрегаций prefetch не нужен
        return queryset.order_by("-message_count")
    
    def filter_by_participant_count(self, min_count: int = None, 
                                   max_count: int = None) -> QuerySet[TelegramChat]:
        """
        Фильтрует чаты по количеству участников.
        
        Args:
            min_count: Минимальное количество участников
            max_count: Максимальное количество участников
            
        Returns:
            QuerySet отфильтрованных чатов
        """
        queryset = self.all()
        
        if min_count is not None:
            queryset = queryset.filter(participants_count__gte=min_count)
        
        if max_count is not None:
            queryset = queryset.filter(participants_count__lte=max_count)
        
        return queryset
    
    def filter_verified_chats(self) -> QuerySet[TelegramChat]:
        """
        Возвращает только верифицированные чаты.
        
        Returns:
            QuerySet верифицированных чатов
        """
        queryset = self.filter(verified=True)
        return self._apply_optimizations(queryset)
    
    def filter_by_creation_date(self, date_from: datetime = None, 
                               date_to: datetime = None) -> QuerySet[TelegramChat]:
        """
        Фильтрует чаты по дате создания.
        
        Args:
            date_from: Начальная дата
            date_to: Конечная дата
            
        Returns:
            QuerySet отфильтрованных чатов
        """
        queryset = self.all()
        
        if date_from:
            queryset = queryset.filter(date__gte=date_from)
        
        if date_to:
            queryset = queryset.filter(date__lte=date_to)
        
        return queryset
    
    def get_or_create_from_telegram_data(self, chat_data: dict[str, Any]) -> tuple[TelegramChat, bool]:
        """
        Создает или обновляет чат из данных Telegram API.
        
        Args:
            chat_data: Данные чата от Telegram API
            
        Returns:
            Кортеж (chat, created)
        """
        chat_id = chat_data.get("id")
        
        defaults = {
            "title": chat_data.get("title", ""),
            "username": chat_data.get("username"),
            "broadcast": chat_data.get("broadcast", False),
            "megagroup": chat_data.get("megagroup", False),
            "verified": chat_data.get("verified", False),
            "restricted": chat_data.get("restricted", False),
            "scam": chat_data.get("scam", False),
            "participants_count": chat_data.get("participants_count"),
            "date": chat_data.get("date"),
        }
        
        # Убираем None значения
        defaults = {k: v for k, v in defaults.items() if v is not None}
        
        return self.update_or_create(
            chat_id=chat_id,
            defaults=defaults
        )
    
    def bulk_update_from_telegram_data(self, chats_data: list[dict[str, Any]]) -> int:
        """
        Массовое обновление чатов из данных Telegram API.
        
        Args:
            chats_data: Список данных чатов от Telegram API
            
        Returns:
            Количество обновленных чатов
        """
        chats_to_update = []
        
        for chat_data in chats_data:
            chat_id = chat_data.get("id")
            if not chat_id:
                continue
            
            try:
                chat = self.get_by_id(chat_id)
                
                # Обновляем поля
                chat.title = chat_data.get("title", chat.title)
                chat.username = chat_data.get("username", chat.username)
                chat.broadcast = chat_data.get("broadcast", chat.broadcast)
                chat.megagroup = chat_data.get("megagroup", chat.megagroup)
                chat.verified = chat_data.get("verified", chat.verified)
                chat.participants_count = chat_data.get("participants_count", chat.participants_count)
                
                chats_to_update.append(chat)
                
            except Exception:
                # Если чат не существует, пропускаем
                continue
        
        if chats_to_update:
            return self.bulk_update(
                chats_to_update,
                ["title", "username", "broadcast", "megagroup", "verified", "participants_count"]
            )
        
        return 0
    
    def filter_by_type(self, channels: bool = False, groups: bool = False, 
                      supergroups: bool = False) -> QuerySet[TelegramChat]:
        """
        Фильтрует чаты по типу.
        
        Args:
            channels: Включать каналы
            groups: Включать обычные группы
            supergroups: Включать супергруппы
            
        Returns:
            QuerySet отфильтрованных чатов
        """
        q = Q()
        
        if channels:
            q |= Q(broadcast=True)
            
        if groups:
            q |= Q(broadcast=False, megagroup=False)
            
        if supergroups:
            q |= Q(megagroup=True)
            
        if not q:
            # Если ничего не выбрано, возвращаем пустой queryset
            return cast(QuerySet[TelegramChat], self.model.objects.none())
            
        return cast(QuerySet[TelegramChat], self.model.objects.filter(q))
    
    def bulk_create_or_update(self, chats_data: list[dict[str, Any]]) -> tuple[int, int]:
        """
        Массовое создание или обновление чатов.
        
        Args:
            chats_data: Список данных чатов
            
        Returns:
            Кортеж (количество созданных, количество обновленных)
        """
        created_count = 0
        updated_count = 0
        
        for chat_data in chats_data:
            chat_id = chat_data.get("chat_id")
            if not chat_id:
                continue
                
            chat, created = TelegramChat.objects.update_or_create(
                chat_id=chat_id,
                defaults=chat_data
            )
            
            if created:
                created_count += 1
            else:
                updated_count += 1
                
        return created_count, updated_count
    
    # Light методы для оптимизации производительности
    
    def get_chats_list_light(self) -> QuerySet[TelegramChat]:
        """
        Получить легкий список чатов для отображения.
        
        Загружает только: chat_id, title, username, broadcast, 
        megagroup, participants_count, left.
        
        Returns:
            QuerySet с минимальными полями
        """
        only_fields = [
            "chat_id", "title", "username", "broadcast",
            "megagroup", "participants_count", "left",
            "verified", "creator"
        ]
        
        return self.filter(only_fields=only_fields).order_by("-participants_count")
    
    def get_chats_for_dropdown(self) -> QuerySet[TelegramChat]:
        """
        Получить минимальный набор полей для выпадающих списков.
        
        Returns:
            QuerySet только с chat_id, title, username
        """
        only_fields = ["chat_id", "title", "username"]
        
        return self.filter(
            left=False,
            only_fields=only_fields
        ).order_by("title")
    
    def get_chats_for_export(self) -> QuerySet[TelegramChat]:
        """
        Получить чаты для экспорта без служебных полей.
        
        Returns:
            QuerySet для экспорта
        """
        defer_fields = ["photo", "photo_id", "scam", "min"]
        
        return self.filter(defer_fields=defer_fields).order_by("title")
    
    def get_active_chats_light(self) -> QuerySet[TelegramChat]:
        """
        Получить активные чаты с минимальными данными.
        
        Returns:
            QuerySet активных чатов
        """
        only_fields = [
            "chat_id", "title", "username", "participants_count"
        ]
        
        return self.filter(
            left=False,
            only_fields=only_fields
        ).order_by("-participants_count")