from django.db import models
from django.utils.translation import gettext_lazy as _

from core.models import TimestampedModel
from core.utils.constants import TelegramLimits


# Create your models here.
class TelegramChat(TimestampedModel):
    """Model representing a Telegram chat or channel."""
    chat_id = models.BigIntegerField(primary_key=True, help_text=_("Unique identifier for the chat"))
    title = models.CharField(max_length=TelegramLimits.CHAT_TITLE_MAX_LENGTH, blank=True, help_text=_("Title of the chat or channel"))
    username = models.CharField(max_length=TelegramLimits.USERNAME_MAX_LENGTH, blank=True, null=True, help_text=_("Public username"))
    creator = models.BooleanField(default=False, help_text=_("Whether you created it"))
    left = models.BooleanField(default=False, help_text=_("Whether you left"))
    broadcast = models.BooleanField(default=False, help_text=_("Whether it's a channel (not group)"))
    verified = models.BooleanField(default=False, help_text=_("Whether verified"))
    megagroup = models.BooleanField(default=False, help_text=_("Whether it's a supergroup"))
    restricted = models.BooleanField(default=False, help_text=_("Whether restricted"))
    min = models.BooleanField(default=False, help_text=_("Minimal info flag"))
    scam = models.BooleanField(default=False, help_text=_("Whether marked as scam"))
    has_link = models.BooleanField(default=False, help_text=_("Has discussion group link"))
    has_geo = models.BooleanField(default=False, help_text=_("Has geolocation"))
    participants_count = models.IntegerField(null=True, blank=True, help_text=_("Approx participant count"))
    photo_id = models.BigIntegerField(null=True, blank=True, help_text=_("Channel photo ID"))
    photo = models.ImageField(upload_to="telegram_chat_photos/", null=True, blank=True, help_text=_("Chat profile photo"))

    class Meta:
        verbose_name = _("Telegram Chat")
        verbose_name_plural = _("Telegram Chats")
        indexes = [
            models.Index(fields=["username"]),
            models.Index(fields=["created_at", "-updated_at"]),
        ]

    def __str__(self):
        return self.title or f"Chat {self.chat_id}"


class TelegramUser(TimestampedModel):
    """Model representing a Telegram user."""
    user_id = models.BigIntegerField(primary_key=True, help_text=_("Unique identifier for the user"))
    username = models.CharField(max_length=TelegramLimits.USERNAME_MAX_LENGTH, blank=True, null=True, help_text=_("Username of the user"))
    first_name = models.CharField(max_length=TelegramLimits.FIRST_NAME_MAX_LENGTH, blank=True, help_text=_("First name of the user"))
    last_name = models.CharField(max_length=TelegramLimits.LAST_NAME_MAX_LENGTH, blank=True, null=True, help_text=_("Last name of the user"))
    phone = models.CharField(max_length=20, blank=True, null=True, help_text=_("Phone number of the user"))
    is_bot = models.BooleanField(default=False, help_text=_("Whether the user is a bot"))
    is_verified = models.BooleanField(default=False, help_text=_("Whether the user is verified"))
    is_restricted = models.BooleanField(default=False, help_text=_("Whether the user is restricted"))
    is_deleted = models.BooleanField(default=False, help_text=_("Whether the user is deleted"))
    is_premium = models.BooleanField(default=False, help_text=_("Whether the user has Telegram Premium"))
    profile_photo_id = models.BigIntegerField(null=True, blank=True, help_text=_("ID of the user's profile photo"))
    lang_code = models.CharField(max_length=10, blank=True, null=True, help_text=_("User's language code"))
    last_seen_date = models.DateTimeField(null=True, blank=True, help_text=_("Last time the user was seen online"))

    class Meta:
        verbose_name = _("Telegram User")
        verbose_name_plural = _("Telegram Users")
        indexes = [
            models.Index(fields=["username"]),
            models.Index(fields=["is_deleted", "created_at"]),
        ]

    @property
    def external_id(self):
        """Compatibility property for external_id."""
        return str(self.user_id)
    
    @property
    def full_name(self):
        """Get user's full name combining first and last names."""
        parts = []
        if self.first_name:
            parts.append(self.first_name)
        if self.last_name:
            parts.append(self.last_name)
        return " ".join(parts) if parts else f"User {self.user_id}"
    
    @full_name.setter
    def full_name(self, value):
        """Set user's full name by splitting into first and last names."""
        if not value:
            return
        
        parts = value.strip().split(" ", 1)
        self.first_name = parts[0] if parts else ""
        self.last_name = parts[1] if len(parts) > 1 else ""

    def __str__(self):
        if self.username:
            return f"@{self.username}"
        elif self.first_name and self.first_name != f"User {self.user_id}":
            return self.full_name
        else:
            return f"User #{self.user_id}"


class TelegramMessage(TimestampedModel):
    """Model representing a Telegram message."""
    # Core fields
    message_id = models.BigIntegerField(help_text=_("Unique identifier for the message"))
    chat = models.ForeignKey(TelegramChat, on_delete=models.CASCADE, related_name="messages", 
                            help_text=_("Chat where the message was sent"))
    from_user = models.ForeignKey(TelegramUser, on_delete=models.SET_NULL, null=True, blank=True, related_name="sent_messages",
                                help_text=_("User who sent the message"))
    from_id = models.BigIntegerField(null=True, blank=True, help_text=_("Original user ID from Telegram"))
    date = models.DateTimeField(help_text=_("Date and time when the message was sent"))
    text = models.TextField(max_length=TelegramLimits.MESSAGE_MAX_LENGTH, blank=True, help_text=_("Text content of the message"))
    
    # User relationship fields
    mentioned_users = models.ManyToManyField(TelegramUser, related_name="mentioned_in", blank=True, 
                                          help_text=_("Users mentioned in this message"))
    action_users = models.ManyToManyField(TelegramUser, related_name="involved_in_actions", blank=True,
                                       help_text=_("Users involved in service actions (join, leave, etc.)"))
    
    # Reply/Forward fields
    reply_to = models.ForeignKey("self", on_delete=models.SET_NULL, null=True, blank=True, related_name="replies",
                                help_text=_("Message this message is replying to"))
    fwd_from_user = models.ForeignKey(TelegramUser, on_delete=models.SET_NULL, null=True, blank=True, 
                                     related_name="forwarded_messages", help_text=_("Original sender if forwarded"))
    fwd_from_chat = models.ForeignKey(TelegramChat, on_delete=models.SET_NULL, null=True, blank=True,
                                     related_name="forwarded_from_messages", help_text=_("Original chat if forwarded"))
    fwd_date = models.DateTimeField(null=True, blank=True, help_text=_("Original message date if forwarded"))
    fwd_from_msg_id = models.BigIntegerField(null=True, blank=True, help_text=_("Original message ID if forwarded"))
    
    # Media fields
    photo = models.JSONField(null=True, blank=True, help_text=_("Photo attachment data"))
    video = models.JSONField(null=True, blank=True, help_text=_("Video attachment data"))
    document = models.JSONField(null=True, blank=True, help_text=_("Document attachment data"))
    audio = models.JSONField(null=True, blank=True, help_text=_("Audio attachment data"))
    voice = models.JSONField(null=True, blank=True, help_text=_("Voice message data"))
    sticker = models.JSONField(null=True, blank=True, help_text=_("Sticker data"))
    animation = models.JSONField(null=True, blank=True, help_text=_("Animation/GIF data"))
    poll = models.JSONField(null=True, blank=True, help_text=_("Poll data"))
    location = models.JSONField(null=True, blank=True, help_text=_("Location data"))
    venue = models.JSONField(null=True, blank=True, help_text=_("Venue data"))
    contact = models.JSONField(null=True, blank=True, help_text=_("Contact data"))
    media_type = models.CharField(max_length=50, blank=True, help_text=_("Type of media attachment"))
    
    # Message properties
    is_private = models.BooleanField(default=False, help_text=_("Whether message is in private chat"))
    is_group = models.BooleanField(default=False, help_text=_("Whether message is in group"))
    is_channel = models.BooleanField(default=False, help_text=_("Whether message is in channel"))
    is_reply = models.BooleanField(default=False, help_text=_("Whether this is a reply"))
    is_forward = models.BooleanField(default=False, help_text=_("Whether this is forwarded"))
    is_edited = models.BooleanField(default=False, help_text=_("Whether message was edited"))
    
    # Service/Action fields
    action = models.JSONField(null=True, blank=True, help_text=_("Service message action data"))
    action_type = models.CharField(max_length=100, blank=True, help_text=_("Type of service action"))
    
    # Edit/View fields
    edit_date = models.DateTimeField(null=True, blank=True, help_text=_("Date of last edit"))
    views = models.IntegerField(null=True, blank=True, help_text=_("View count for channel messages"))
    forwards = models.IntegerField(null=True, blank=True, help_text=_("Forward count"))
    
    # Bot-specific fields
    via_bot = models.ForeignKey(TelegramUser, on_delete=models.SET_NULL, null=True, blank=True,
                               related_name="bot_messages", help_text=_("Bot that sent this message"))
    inline_query = models.CharField(max_length=255, blank=True, help_text=_("Inline query if from inline bot"))
    
    # Other fields
    entities = models.JSONField(null=True, blank=True, help_text=_("Message entities (URLs, mentions, etc)"))
    buttons = models.JSONField(null=True, blank=True, help_text=_("Inline keyboard buttons"))
    web_preview = models.JSONField(null=True, blank=True, help_text=_("Web page preview data"))
    grouped_id = models.BigIntegerField(null=True, blank=True, help_text=_("ID for grouped messages"))
    signature = models.CharField(max_length=255, blank=True, null=True, help_text=_("Signature for channel posts"))
    post_author = models.CharField(max_length=255, blank=True, null=True, help_text=_("Author signature for channel posts"))
    mentioned = models.BooleanField(default=False, help_text=_("Whether current user was mentioned"))
    scheduled = models.BooleanField(default=False, help_text=_("Whether this is a scheduled message"))
    ttl_period = models.IntegerField(null=True, blank=True, help_text=_("TTL for self-destructing messages"))
    pinned = models.BooleanField(default=False, help_text=_("Whether message is pinned"))
    
    # Media file references
    media_file = models.FileField(upload_to="telegram_media/", null=True, blank=True, 
                                 help_text=_("Downloaded media file"))

    class Meta:
        verbose_name = _("Telegram Message")
        verbose_name_plural = _("Telegram Messages")
        unique_together = ("message_id", "chat")
        indexes = [
            models.Index(fields=["date"]),
            models.Index(fields=["from_user"]),
            models.Index(fields=["reply_to"]),
            models.Index(fields=["is_forward"]),
            models.Index(fields=["media_type"]),
        ]

    def __str__(self):
        return f"Message {self.message_id} from {self.from_user} in {self.chat}"
