"""
Базовые классы и миксины для Telegram админки.
"""

from django.contrib import admin
from django.utils import timezone
from django.utils.html import format_html


class OptimizedQuerysetMixin:
    """Миксин для оптимизации запросов с автоматическим select_related/prefetch_related"""
    
    # Поля для select_related (ForeignKey, OneToOne)
    list_select_related: list[str] = []
    
    # Поля для prefetch_related (ManyToMany, reverse ForeignKey)
    list_prefetch_related: list[str] = []
    
    def get_queryset(self, request):
        """Оптимизированный queryset с автоматическим prefetch"""
        qs = super().get_queryset(request)
        
        # Применяем select_related если указано
        if self.list_select_related:
            qs = qs.select_related(*self.list_select_related)
        
        # Применяем prefetch_related если указано
        if self.list_prefetch_related:
            qs = qs.prefetch_related(*self.list_prefetch_related)
        
        # Автоматически определяем foreign key поля в list_display
        if hasattr(self, "list_display"):
            for field_name in self.list_display:
                if "__" in field_name:  # Поле с lookup (например, 'user__username')
                    base_field = field_name.split("__")[0]
                    # Добавляем в select_related если еще не добавлено
                    if base_field not in self.list_select_related:
                        qs = qs.select_related(base_field)
        
        return qs


class DateDisplayMixin:
    """Миксин для отображения дат в удобном формате"""
    
    def format_date(self, date):
        """Форматирует дату с относительным временем"""
        if not date:
            return "-"
            
        now = timezone.now()
        diff = now - date
        
        if diff.days == 0:
            if diff.seconds < 3600:
                minutes = diff.seconds // 60
                return f"{minutes} минут назад"
            else:
                hours = diff.seconds // 3600
                return f"{hours} часов назад"
        elif diff.days == 1:
            return "Вчера"
        elif diff.days < 7:
            return f"{diff.days} дней назад"
        else:
            return date.strftime("%d.%m.%Y %H:%M")


class MediaPreviewMixin:
    """Миксин для предпросмотра медиа файлов"""
    
    def photo_preview(self, obj, photo_field="photo", size=50):
        """Показывает превью фотографии"""
        photo = getattr(obj, photo_field, None)
        if photo:
            return format_html(
                '<img src="{}" width="{}" height="{}" style="border-radius: 50%;" />', 
                photo.url, size, size
            )
        return "-"
    
    def media_type_icon(self, media_type):
        """Возвращает иконку для типа медиа"""
        icons = {
            "photo": "📷",
            "video": "📹",
            "audio": "🎵",
            "voice": "🎤",
            "document": "📄",
            "sticker": "🎨",
            "animation": "🎬",
            "poll": "📊",
            "location": "📍",
        }
        return icons.get(media_type, "📎")


class CounterMixin:
    """Миксин для отображения счетчиков"""
    
    def format_count(self, count):
        """Форматирует большие числа (1K, 1M)"""
        if count is None:
            return "0"
        if count < 1000:
            return str(count)
        elif count < 1_000_000:
            return f"{count / 1000:.1f}K"
        else:
            return f"{count / 1_000_000:.1f}M"


class BulkActionsMixin:
    """Миксин для массовых операций"""
    
    def bulk_update_field(self, request, queryset, field_name, value):
        """Массовое обновление поля"""
        count = queryset.update(**{field_name: value})
        self.message_user(request, f"Обновлено {count} записей")
    
    def bulk_delete_with_confirmation(self, request, queryset):
        """Массовое удаление с подтверждением"""
        count = queryset.count()
        queryset.delete()
        self.message_user(request, f"Удалено {count} записей")


class BaseTelegramAdmin(
    OptimizedQuerysetMixin,
    DateDisplayMixin,
    MediaPreviewMixin,
    CounterMixin,
    admin.ModelAdmin
):
    """Базовый класс для всех Telegram админок"""
    
    # Общие настройки
    save_on_top = True
    save_as = True
    
    class Media:
        css = {
            "all": ("admin/css/telegram_admin.css",)
        }