"""
Admin для модели TelegramChat с оптимизациями.
"""

from django.contrib import admin
from django.urls import reverse
from django.utils.html import format_html

from telegram_manager.models import TelegramChat

from .base import BaseTelegramAdmin


@admin.register(TelegramChat)
class TelegramChatAdmin(BaseTelegramAdmin):
    """Админка для Telegram чатов с оптимизациями"""
    
    # Отображение в списке
    list_display = (
        "photo_tag", "chat_id", "title", "username", "type_icon",
        "participants_display", "verified_icon", "created_display"
    )
    
    # Поиск
    search_fields = ("chat_id", "title", "username")
    
    # Фильтры
    list_filter = (
        "broadcast", "megagroup", "verified", "creator", 
        "left", "restricted", "scam", "created_at"
    )
    
    # Оптимизация запросов - указываем явно что нужно prefetch
    list_prefetch_related = ["messages"]  # Для подсчета сообщений если нужно
    
    # Поля только для чтения
    readonly_fields = (
        "chat_id", "created_at", "updated_at", "photo_tag_large",
        "statistics_display", "restrictions_display"
    )
    
    # Группировка полей
    fieldsets = (
        ("Основная информация", {
            "fields": ("chat_id", "title", "username", "photo", "photo_tag_large")
        }),
        ("Тип чата", {
            "fields": ("broadcast", "megagroup")
        }),
        ("Статус", {
            "fields": (
                "verified", "scam", "creator", "left", 
                "restricted"
            )
        }),
        ("Статистика", {
            "fields": ("participants_count", "statistics_display")
        }),
        ("Ограничения", {
            "fields": ("restrictions_display",),
            "classes": ("collapse",)
        }),
        ("Временные метки", {
            "fields": ("created_at", "updated_at"),
            "classes": ("collapse",)
        }),
    )
    
    # Действия
    actions = ["mark_as_verified", "mark_as_scam", "update_chat_info"]
    
    def photo_tag(self, obj):
        """Миниатюра фото в списке"""
        return self.photo_preview(obj, size=40)
    photo_tag.short_description = "Фото"
    
    def photo_tag_large(self, obj):
        """Большое фото в детальном просмотре"""
        return self.photo_preview(obj, size=100)
    photo_tag_large.short_description = "Фото чата"
    
    def type_icon(self, obj):
        """Иконка типа чата"""
        if obj.broadcast:
            return "📢 Канал"
        elif obj.megagroup:
            return "👥 Супергруппа"
        else:
            return "💬 Группа"
    type_icon.short_description = "Тип"
    
    def participants_display(self, obj):
        """Отображение количества участников"""
        return self.format_count(obj.participants_count)
    participants_display.short_description = "Участники"
    participants_display.admin_order_field = "participants_count"
    
    def verified_icon(self, obj):
        """Иконка верификации"""
        if obj.verified:
            return format_html('<span style="color: #1DA1F2;">✓</span>')
        elif obj.scam:
            return format_html('<span style="color: #E74C3C;">⚠️</span>')
        return ""
    verified_icon.short_description = "Статус"
    
    def created_display(self, obj):
        """Отображение даты создания"""
        return self.format_date(obj.created_at)
    created_display.short_description = "Создан"
    created_display.admin_order_field = "created_at"
    
    def statistics_display(self, obj):
        """Отображение статистики чата"""
        stats = []
        if obj.participants_count:
            stats.append(f"👥 {self.format_count(obj.participants_count)} участников")
        if hasattr(obj, "messages") and obj.messages.exists():
            msg_count = obj.messages.count()
            stats.append(f"💬 {self.format_count(msg_count)} сообщений")
        return format_html("<br>".join(stats)) if stats else "-"
    statistics_display.short_description = "Статистика"
    
    def restrictions_display(self, obj):
        """Отображение ограничений чата"""
        restrictions = []
        if obj.restricted:
            restrictions.append("⛔ Ограничен")
        return ", ".join(restrictions) if restrictions else "Нет ограничений"
    restrictions_display.short_description = "Ограничения"
    
    def changelist_view(self, request, extra_context=None):
        """Добавляем кнопку импорта"""
        extra_context = extra_context or {}
        extra_context["import_button_url"] = reverse(
            "telegram_manager:telegram_manager_telegramchat_import-from-telegram"
        )
        return super().changelist_view(request, extra_context=extra_context)
    
    # Действия
    def mark_as_verified(self, request, queryset):
        """Отметить как верифицированные"""
        updated = queryset.update(verified=True, scam=False, fake=False)
        self.message_user(request, f"Отмечено как верифицированные: {updated} чатов")
    mark_as_verified.short_description = "Отметить как верифицированные"
    
    def mark_as_scam(self, request, queryset):
        """Отметить как мошеннические"""
        updated = queryset.update(scam=True, verified=False)
        self.message_user(request, f"Отмечено как мошеннические: {updated} чатов")
    mark_as_scam.short_description = "Отметить как мошеннические"
    
    def update_chat_info(self, request, queryset):
        """Обновить информацию о чатах"""
        from core.models import TaskResult
        from telegram_manager.tasks.import_tasks import ImportTelegramChatsTask
        
        # Create task for updating chat info
        task = ImportTelegramChatsTask()
        task_result = TaskResult.objects.create(
            task_type="telegram_chat_update",
            status="pending",
            total_items=queryset.count()
        )
        
        # Queue the task
        task.apply_async(
            kwargs={"chat_ids": list(queryset.values_list("chat_id", flat=True))},
            task_id=str(task_result.task_id)
        )
        
        self.message_user(
            request, 
            f"Запущено обновление {queryset.count()} чатов. ID задачи: {task_result.task_id}"
        )
    update_chat_info.short_description = "Обновить информацию"