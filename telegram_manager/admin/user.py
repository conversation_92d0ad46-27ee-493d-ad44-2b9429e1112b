"""
Admin для модели TelegramUser с оптимизациями.
"""

from django.contrib import admin
from django.utils.html import format_html

from telegram_manager.models import TelegramUser

from .base import BaseTelegramAdmin


@admin.register(TelegramUser)
class TelegramUserAdmin(BaseTelegramAdmin):
    """Админка для Telegram пользователей с оптимизациями"""
    
    # Отображение в списке
    list_display = (
        "user_id", "username_display", "full_name", 
        "status_icons", "last_seen_display", "updated_display"
    )
    
    # Поиск
    search_fields = ("user_id", "username", "first_name", "last_name", "phone")
    
    # Фильтры
    list_filter = (
        ("username", admin.EmptyFieldListFilter),
        "is_bot", "is_verified", "is_premium",
        "is_restricted", "is_deleted",
        "last_seen_date"
    )
    
    # Оптимизация запросов
    list_prefetch_related = ["sent_messages", "chats"]  # Если нужно считать сообщения
    
    # Поля только для чтения
    readonly_fields = (
        "user_id", "created_at", "updated_at",
        "statistics_display", "status_display"
    )
    
    # Группировка полей
    fieldsets = (
        ("Основная информация", {
            "fields": ("user_id", "username", "first_name", "last_name", "phone")
        }),
        ("Статус пользователя", {
            "fields": (
                "is_bot", "is_verified", "is_premium", "is_restricted", 
                "is_deleted", "status_display"
            )
        }),
        ("Дополнительная информация", {
            "fields": ("profile_photo_id", "lang_code", "last_seen_date"),
            "classes": ("collapse",)
        }),
        ("Статистика", {
            "fields": ("statistics_display",),
            "classes": ("collapse",)
        }),
        ("Временные метки", {
            "fields": ("created_at", "updated_at"),
            "classes": ("collapse",)
        }),
    )
    
    # Действия
    actions = ["mark_as_bot", "mark_as_verified", "mark_as_deleted", "update_user_info"]
    
    def username_display(self, obj):
        """Отображение username с проверкой"""
        if obj.username:
            return f"@{obj.username}"
        return format_html('<span style="color: #999;">—</span>')
    username_display.short_description = "Username"
    username_display.admin_order_field = "username"
    
    def full_name(self, obj):
        """Полное имя пользователя"""
        parts = []
        if obj.first_name:
            parts.append(obj.first_name)
        if obj.last_name:
            parts.append(obj.last_name)
        
        if not parts:
            return format_html('<span style="color: #999;">Без имени</span>')
        
        full_name = " ".join(parts)
        # Обрезаем если слишком длинное
        if len(full_name) > 30:
            full_name = full_name[:27] + "..."
        return full_name
    full_name.short_description = "Имя"
    
    def status_icons(self, obj):
        """Иконки статуса пользователя"""
        icons = []
        
        if obj.is_bot:
            icons.append(format_html('<span title="Бот">🤖</span>'))
        if obj.is_verified:
            icons.append(format_html('<span style="color: #1DA1F2;" title="Верифицирован">✓</span>'))
        if obj.is_premium:
            icons.append(format_html('<span style="color: #FFD700;" title="Premium">⭐</span>'))
        if obj.is_scam:
            icons.append(format_html('<span style="color: #E74C3C;" title="Мошенник">⚠️</span>'))
        if obj.is_fake:
            icons.append(format_html('<span style="color: #F39C12;" title="Фейк">⚠️</span>'))
        if obj.is_restricted:
            icons.append(format_html('<span style="color: #E67E22;" title="Ограничен">🚫</span>'))
        if obj.is_deleted:
            icons.append(format_html('<span style="color: #95A5A6;" title="Удален">🗑️</span>'))
        if obj.is_support:
            icons.append(format_html('<span style="color: #3498DB;" title="Поддержка">💬</span>'))
        
        return format_html(" ".join(icons)) if icons else "-"
    status_icons.short_description = "Статус"
    
    def last_seen_display(self, obj):
        """Отображение последнего визита"""
        if obj.last_seen_date:
            return self.format_date(obj.last_seen_date)
        return format_html('<span style="color: #999;">Неизвестно</span>')
    last_seen_display.short_description = "Был в сети"
    last_seen_display.admin_order_field = "last_seen_date"
    
    def updated_display(self, obj):
        """Отображение даты обновления"""
        return self.format_date(obj.updated_at)
    updated_display.short_description = "Обновлен"
    updated_display.admin_order_field = "updated_at"
    
    def status_display(self, obj):
        """Детальное отображение статуса"""
        statuses = []
        
        if obj.is_bot:
            statuses.append("🤖 Бот")
        if obj.is_verified:
            statuses.append("✓ Верифицирован")
        if obj.is_premium:
            statuses.append("⭐ Premium подписка")
        if obj.is_scam:
            statuses.append("⚠️ Помечен как мошенник")
        if obj.is_fake:
            statuses.append("⚠️ Помечен как фейк")
        if obj.is_restricted:
            statuses.append("🚫 Ограниченный аккаунт")
        if obj.is_deleted:
            statuses.append("🗑️ Удаленный аккаунт")
        if obj.is_support:
            statuses.append("💬 Официальная поддержка")
        
        return format_html("<br>".join(statuses)) if statuses else "Обычный пользователь"
    status_display.short_description = "Детали статуса"
    
    def statistics_display(self, obj):
        """Отображение статистики пользователя"""
        stats = []
        
        # Подсчет сообщений (если prefetch сделан)
        if hasattr(obj, "sent_messages"):
            msg_count = obj.sent_messages.count()
            stats.append(f"💬 {self.format_count(msg_count)} сообщений")
        
        # Язык
        if obj.lang_code:
            stats.append(f"🌐 Язык: {obj.lang_code}")
        
        # Телефон (частично скрыт)
        if obj.phone:
            masked_phone = obj.phone[:4] + "****" + obj.phone[-2:]
            stats.append(f"📱 {masked_phone}")
        
        return format_html("<br>".join(stats)) if stats else "-"
    statistics_display.short_description = "Статистика"
    
    # Действия
    def mark_as_bot(self, request, queryset):
        """Отметить как ботов"""
        updated = queryset.update(is_bot=True)
        self.message_user(request, f"Отмечено как боты: {updated} пользователей")
    mark_as_bot.short_description = "Отметить как ботов"
    
    def mark_as_verified(self, request, queryset):
        """Отметить как верифицированных"""
        updated = queryset.update(is_verified=True, is_scam=False, is_fake=False)
        self.message_user(request, f"Отмечено как верифицированные: {updated} пользователей")
    mark_as_verified.short_description = "Отметить как верифицированных"
    
    def mark_as_deleted(self, request, queryset):
        """Отметить как удаленных"""
        updated = queryset.update(is_deleted=True)
        self.message_user(request, f"Отмечено как удаленные: {updated} пользователей")
    mark_as_deleted.short_description = "Отметить как удаленных"
    
    def update_user_info(self, request, queryset):
        """Обновить информацию о пользователях"""
        from core.models import TaskResult
        from telegram_manager.tasks.import_tasks import ImportTelegramUsersTask
        
        # Create task for updating user info
        task = ImportTelegramUsersTask()
        task_result = TaskResult.objects.create(
            task_type="telegram_user_update",
            status="pending",
            total_items=queryset.count()
        )
        
        # Queue the task
        task.apply_async(
            kwargs={
                "user_ids": list(queryset.values_list("user_id", flat=True)),
                "update_existing": True
            },
            task_id=str(task_result.task_id)
        )
        
        self.message_user(
            request,
            f"Запущено обновление {queryset.count()} пользователей. ID задачи: {task_result.task_id}"
        )
    update_user_info.short_description = "Обновить информацию"