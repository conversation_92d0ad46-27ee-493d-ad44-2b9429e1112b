"""
Telegram Manager Admin - модульная структура админки.

Разделено на отдельные модули для лучшей организации и производительности:
- base.py - базовые классы и миксины
- chat.py - админка для TelegramChat
- user.py - админка для TelegramUser  
- message.py - админка для TelegramMessage
"""

# Импортируем все админ-классы для автоматической регистрации
from .chat import TelegramChatAdmin
from .message import TelegramMessageAdmin
from .user import TelegramUserAdmin

__all__ = [
    "TelegramChatAdmin",
    "TelegramMessageAdmin",
    "TelegramUserAdmin",
]