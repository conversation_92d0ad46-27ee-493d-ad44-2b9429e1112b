"""
Admin для модели TelegramMessage с оптимизациями.
"""

from django.contrib import admin
from django.urls import reverse
from django.utils.html import format_html

from telegram_manager.models import TelegramMessage

from .base import BaseTelegramAdmin


@admin.register(TelegramMessage)
class TelegramMessageAdmin(BaseTelegramAdmin):
    """Админка для Telegram сообщений с оптимизациями"""
    
    # Отображение в списке
    list_display = (
        "message_id", "chat_link", "from_user_link", "date_display",
        "media_icon", "text_preview", "stats_display", "flags_display"
    )
    
    # Фильтры
    list_filter = (
        "date", "media_type", "is_forward", "is_reply", 
        "is_channel", "is_group", "is_private", "mentioned",
        "pinned", "is_edited"
    )
    
    # Поиск
    search_fields = (
        "message_id", "text", "from_user__username", 
        "from_user__first_name", "chat__title"
    )
    
    # Иерархия по датам
    date_hierarchy = "date"
    
    # Оптимизация запросов - важно для производительности!
    list_select_related = ["from_user", "chat", "reply_to", "fwd_from_user", "fwd_from_chat", "via_bot"]
    
    # Raw ID поля для больших связей
    raw_id_fields = ("from_user", "chat", "reply_to", "fwd_from_user", "fwd_from_chat", "via_bot")
    
    # Поля только для чтения
    readonly_fields = (
        "message_id", "date", "created_at", "updated_at",
        "message_details", "forward_details", "media_details"
    )
    
    # Группировка полей
    fieldsets = (
        ("Основная информация", {
            "fields": ("message_id", "chat", "from_user", "date", "text")
        }),
        ("Медиа", {
            "fields": ("media_type", "media_details"),
            "classes": ("collapse",)
        }),
        ("Связи", {
            "fields": ("reply_to", "via_bot"),
            "classes": ("collapse",)
        }),
        ("Пересылка", {
            "fields": (
                "is_forward", "fwd_from_user", "fwd_from_chat",
                "fwd_from_msg_id", "fwd_date", "forward_details"
            ),
            "classes": ("collapse",)
        }),
        ("Флаги", {
            "fields": (
                "is_channel", "is_group", "is_private", "mentioned",
                "pinned", "is_edited", "edit_date"
            ),
            "classes": ("collapse",)
        }),
        ("Статистика", {
            "fields": ("views", "forwards", "message_details"),
            "classes": ("collapse",)
        }),
        ("Временные метки", {
            "fields": ("created_at", "updated_at"),
            "classes": ("collapse",)
        }),
    )
    
    # Действия
    actions = ["export_messages", "mark_as_important", "analyze_media"]
    
    def chat_link(self, obj):
        """Ссылка на чат"""
        if obj.chat:
            url = reverse("admin:telegram_manager_telegramchat_change", args=[obj.chat.pk])
            return format_html(
                '<a href="{}">{}</a>',
                url,
                obj.chat.title[:30] + "..." if len(obj.chat.title) > 30 else obj.chat.title
            )
        return "-"
    chat_link.short_description = "Чат"
    chat_link.admin_order_field = "chat__title"
    
    def from_user_link(self, obj):
        """Ссылка на пользователя"""
        if obj.from_user:
            url = reverse("admin:telegram_manager_telegramuser_change", args=[obj.from_user.pk])
            name = obj.from_user.first_name or obj.from_user.username or f"ID:{obj.from_user.user_id}"
            return format_html('<a href="{}">{}</a>', url, name[:20])
        return "-"
    from_user_link.short_description = "От"
    from_user_link.admin_order_field = "from_user__first_name"
    
    def date_display(self, obj):
        """Отображение даты"""
        return self.format_date(obj.date)
    date_display.short_description = "Дата"
    date_display.admin_order_field = "date"
    
    def media_icon(self, obj):
        """Иконка типа медиа"""
        if obj.media_type:
            icon = self.media_type_icon(obj.media_type)
            return format_html(
                '<span title="{}">{}</span>',
                obj.media_type, icon
            )
        return ""
    media_icon.short_description = "Медиа"
    
    def text_preview(self, obj):
        """Превью текста сообщения"""
        if obj.text:
            preview = obj.text[:50]
            if len(obj.text) > 50:
                preview += "..."
            # Экранируем HTML
            return format_html('<span title="{}">{}</span>', obj.text[:200], preview)
        elif obj.media_type:
            return format_html('<i style="color: #999;">[{}]</i>', obj.media_type)
        return "-"
    text_preview.short_description = "Текст"
    
    def stats_display(self, obj):
        """Отображение статистики"""
        stats = []
        if obj.views:
            stats.append(f"👁 {self.format_count(obj.views)}")
        if obj.forwards:
            stats.append(f"↗️ {self.format_count(obj.forwards)}")
        return " ".join(stats) if stats else ""
    stats_display.short_description = "Стат."
    
    def flags_display(self, obj):
        """Отображение флагов сообщения"""
        flags = []
        if obj.is_forward:
            flags.append("↗️")
        if obj.is_reply:
            flags.append("↩️")
        if obj.pinned:
            flags.append("📌")
        if obj.is_edited:
            flags.append("✏️")
        if obj.mentioned:
            flags.append("@")
        return " ".join(flags) if flags else ""
    flags_display.short_description = "Флаги"
    
    def message_details(self, obj):
        """Детальная информация о сообщении"""
        details = [
            f"ID: {obj.message_id}",
            f"Дата: {obj.date.strftime('%d.%m.%Y %H:%M:%S')}",
        ]
        
        if obj.is_channel:
            details.append("📢 Сообщение канала")
        elif obj.is_group:
            details.append("👥 Сообщение группы")
        elif obj.is_private:
            details.append("👤 Личное сообщение")
        
        if obj.views:
            details.append(f"👁 Просмотров: {self.format_count(obj.views)}")
        if obj.forwards:
            details.append(f"↗️ Пересылок: {self.format_count(obj.forwards)}")
        if obj.replies_count:
            details.append(f"💬 Ответов: {self.format_count(obj.replies_count)}")
        
        return format_html("<br>".join(details))
    message_details.short_description = "Детали сообщения"
    
    def forward_details(self, obj):
        """Детали пересылки"""
        if not obj.is_forward:
            return "Не является пересылкой"
        
        details = ["📤 Переслано"]
        if obj.fwd_from_user:
            details.append(f"От пользователя: {obj.fwd_from_user}")
        if obj.fwd_from_chat:
            details.append(f"Из чата: {obj.fwd_from_chat}")
        if obj.fwd_from_message_id:
            details.append(f"ID сообщения: {obj.fwd_from_message_id}")
        if obj.fwd_from_date:
            details.append(f"Дата: {obj.fwd_from_date.strftime('%d.%m.%Y %H:%M')}")
        
        return format_html("<br>".join(details))
    forward_details.short_description = "Детали пересылки"
    
    def media_details(self, obj):
        """Детали медиа"""
        if not obj.media_type:
            return "Нет медиа"
        
        icon = self.media_type_icon(obj.media_type)
        details = [f"{icon} {obj.media_type}"]
        if obj.media_id:
            details.append(f"ID: {obj.media_id}")
        
        return format_html("<br>".join(details))
    media_details.short_description = "Детали медиа"
    
    def changelist_view(self, request, extra_context=None):
        """Добавляем кнопку импорта"""
        extra_context = extra_context or {}
        extra_context["import_button_url"] = reverse(
            "telegram_manager:telegram_manager_telegrammessage_import-messages"
        )
        return super().changelist_view(request, extra_context=extra_context)
    
    # Действия
    def export_messages(self, request, queryset):
        """Экспорт выбранных сообщений"""
        import json

        from django.core.serializers.json import DjangoJSONEncoder
        from django.http import HttpResponse
        
        # Prepare data for export
        messages_data = []
        for message in queryset.select_related("chat", "sender"):
            messages_data.append({
                "message_id": message.message_id,
                "chat": message.chat.title if message.chat else None,
                "sender": message.sender.username if message.sender else None,
                "date": message.date,
                "text": message.text,
                "media_type": message.media_type,
                "views": message.views,
                "forwards": message.forwards,
            })
        
        # Create JSON response
        response = HttpResponse(
            json.dumps(messages_data, cls=DjangoJSONEncoder, ensure_ascii=False, indent=2),
            content_type="application/json"
        )
        response["Content-Disposition"] = f'attachment; filename="telegram_messages_{len(messages_data)}.json"'
        return response
    export_messages.short_description = "Экспортировать сообщения"
    
    def mark_as_important(self, request, queryset):
        """Отметить как важные"""
        # Note: Important messages can be tracked via tags or custom metadata
        # For now, we'll add them to a session variable for tracking
        important_ids = request.session.get("important_message_ids", [])
        new_ids = list(queryset.values_list("id", flat=True))
        important_ids.extend(new_ids)
        request.session["important_message_ids"] = list(set(important_ids))
        
        count = queryset.count()
        self.message_user(
            request, 
            f"Отмечено как важные: {count} сообщений. "
            f"Всего важных: {len(request.session['important_message_ids'])}"
        )
    mark_as_important.short_description = "Отметить как важные"
    
    def analyze_media(self, request, queryset):
        """Анализировать медиа контент"""
        media_messages = queryset.exclude(media_type__isnull=True)
        count = media_messages.count()
        self.message_user(request, f"Найдено медиа сообщений: {count}")
    analyze_media.short_description = "Анализировать медиа"