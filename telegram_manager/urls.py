from django.contrib import admin
from django.urls import path

from .admin import TelegramChatAdmin, TelegramMessageAdmin
from .models import TelegramChat, TelegramMessage
from .views import (
    admin_telegram_chat_do_import,
    admin_telegram_chat_import_form,
    admin_telegram_message_do_import,
    admin_telegram_message_import_form,
)

app_name = "telegram_manager"

# Get admin site instance
admin_site = admin.site

# Get model admin instances
telegram_chat_admin = TelegramChatAdmin(TelegramChat, admin_site)
telegram_message_admin = TelegramMessageAdmin(TelegramMessage, admin_site)

urlpatterns = [
    # TelegramChat URLs
    path("telegram-chat/import-from-telegram/", 
         lambda request: admin_telegram_chat_import_form(request, admin_site, telegram_chat_admin), 
         name="telegram_manager_telegramchat_import-from-telegram"),
    path("telegram-chat/do-import/", 
         lambda request: admin_telegram_chat_do_import(request, admin_site, telegram_chat_admin), 
         name="telegram_manager_telegramchat_do-import"),

    # TelegramMessage URLs
    path("telegram-message/import-messages/", 
         lambda request: admin_telegram_message_import_form(request, admin_site, telegram_message_admin), 
         name="telegram_manager_telegrammessage_import-messages"),
    path("telegram-message/do-import/", 
         lambda request: admin_telegram_message_do_import(request, admin_site, telegram_message_admin), 
         name="telegram_manager_telegrammessage_do-import"),
]
