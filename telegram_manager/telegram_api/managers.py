"""
Новые специализированные менеджеры для работы с Telegram API.
Используют новый TelegramAPIClient.
"""

from datetime import datetime
from typing import Any, cast

from core.exceptions import APIError, NotFoundError
from telegram_manager.telegram_api.telegram_client import TelegramAPIClient


class TelegramUserManager:
    """Менеджер для работы с пользователями Telegram."""
    
    def __init__(self, client: TelegramAPIClient):
        self.client = client
    
    async def get_user_by_id(self, user_id: str | int) -> dict[str, Any]:
        """
        Получает пользователя по ID.
        
        Args:
            user_id: ID или username пользователя
            
        Returns:
            Данные пользователя
        """
        result = await self.client.get_user(user_id)
        return cast(dict[str, Any], result)
    
    async def get_chat_participants(self, chat_id: str | int, 
                                   limit: int | None = None) -> list[dict[str, Any]]:
        """
        Получает участников чата.
        
        Args:
            chat_id: ID чата
            limit: Максимальное количество
            
        Returns:
            Список участников
        """
        result = await self.client.get_chat_participants(chat_id, limit)
        return cast(list[dict[str, Any]], result)
    
    async def search_users(self, query: str, limit: int = 50) -> list[dict[str, Any]]:
        """
        Поиск пользователей по запросу.
        
        Args:
            query: Поисковый запрос
            limit: Максимальное количество результатов
            
        Returns:
            Список найденных пользователей
        """
        # В Telegram нет прямого поиска пользователей
        # Можно искать только в контактах или через username
        raise NotImplementedError(
            "User search is limited in Telegram API"
        )


class TelegramChatManager:
    """Менеджер для работы с чатами Telegram."""
    
    def __init__(self, client: TelegramAPIClient):
        self.client = client
    
    async def get_dialogs(self, limit: int = 100,
                         include_private: bool = True,
                         include_groups: bool = True,
                         include_channels: bool = True,
                         include_supergroups: bool = True) -> list[dict[str, Any]]:
        """
        Получает список диалогов.
        
        Args:
            limit: Максимальное количество
            include_private: Включать приватные чаты
            include_groups: Включать группы
            include_channels: Включать каналы
            include_supergroups: Включать супергруппы
            
        Returns:
            Список диалогов
        """
        # Супергруппы являются разновидностью каналов в Telegram API
        # Объединяем флаги
        include_channels_all = include_channels or include_supergroups
        
        dialogs = await self.client.get_dialogs(
            limit=limit,
            include_private=include_private,
            include_groups=include_groups,
            include_channels=include_channels_all
        )
        
        # Фильтруем супергруппы если нужно
        if include_channels and not include_supergroups:
            # Логируем для отладки
            total_before = len(dialogs)
            dialogs = [d for d in dialogs if not d.get("entity", {}).get("megagroup", False)]
            filtered_out = total_before - len(dialogs)
            if filtered_out > 0:
                import logging
                logger = logging.getLogger(__name__)
                logger.info(f"Filtered out {filtered_out} supergroups (megagroups) from channels")
        elif include_supergroups and not include_channels:
            dialogs = [d for d in dialogs if d.get("entity", {}).get("megagroup", False)]
        
        return cast(list[dict[str, Any]], dialogs)
    
    async def get_chat_by_id(self, chat_id: str | int) -> dict[str, Any]:
        """
        Получает информацию о чате.
        
        Args:
            chat_id: ID или username чата
            
        Returns:
            Данные чата
        """
        # Используем get_user, так как в Telegram API чаты тоже являются сущностями
        try:
            entity = await self.client.client.get_entity(chat_id)
            
            # Преобразуем в словарь
            return {
                "id": entity.id,
                "title": getattr(entity, "title", None),
                "username": getattr(entity, "username", None),
                "broadcast": getattr(entity, "broadcast", False),
                "megagroup": getattr(entity, "megagroup", False),
                "verified": getattr(entity, "verified", False),
                "restricted": getattr(entity, "restricted", False),
                "participants_count": getattr(entity, "participants_count", None),
                "date": getattr(entity, "date", None),
            }
        except Exception as e:
            raise NotFoundError(entity="TelegramChat", identifier=chat_id) from e


class TelegramMessageManager:
    """Менеджер для работы с сообщениями Telegram."""
    
    def __init__(self, client: TelegramAPIClient):
        self.client = client
    
    async def get_messages(self, chat_id: str | int, 
                          limit: int = 100) -> list[dict[str, Any]]:
        """
        Получает сообщения из чата.
        
        Args:
            chat_id: ID чата
            limit: Максимальное количество
            
        Returns:
            Список сообщений
        """
        result = await self.client.get_posts(chat_id, limit)
        return cast(list[dict[str, Any]], result)
    
    async def get_messages_by_date(self, chat_id: str | int,
                                  limit: int = 100,
                                  date_from: datetime | None = None,
                                  date_to: datetime | None = None) -> list[dict[str, Any]]:
        """
        Получает сообщения за период.
        
        Args:
            chat_id: ID чата
            limit: Максимальное количество
            date_from: Начальная дата
            date_to: Конечная дата
            
        Returns:
            Список сообщений
        """
        result = await self.client.get_posts(
            chat_id, 
            limit, 
            date_from, 
            date_to
        )
        return cast(list[dict[str, Any]], result)
    
    async def search_messages(self, chat_id: str | int,
                             query: str,
                             limit: int = 100) -> list[dict[str, Any]]:
        """
        Поиск сообщений в чате.
        
        Args:
            chat_id: ID чата
            query: Поисковый запрос
            limit: Максимальное количество
            
        Returns:
            Список найденных сообщений
        """
        # Используем search через Telegram API
        messages = []
        
        try:
            async for message in self.client.client.iter_messages(
                chat_id,
                search=query,
                limit=limit
            ):
                messages.append(self.client._message_to_dict(message))
            
            return messages
        except Exception as e:
            raise APIError(f"Failed to search messages: {e!s}") from e
    
    async def download_message_media(self, message_data: dict[str, Any],
                                   download_path: str | None = None) -> str | None:
        """
        Загружает медиа из сообщения.
        
        Args:
            message_data: Данные сообщения
            download_path: Путь для сохранения
            
        Returns:
            Путь к сохраненному файлу или None
        """
        # Для загрузки медиа нужен оригинальный объект сообщения
        # В реальной реализации нужно будет получить его заново
        raise NotImplementedError(
            "Media download requires original message object"
        )


class TelegramManager:
    """
    Главный менеджер для работы с Telegram API.
    Объединяет функциональность всех специализированных менеджеров.
    """
    
    def __init__(self, api_id: int, api_hash: str, session_path: str):
        """
        Инициализирует Telegram менеджер.
        
        Args:
            api_id: ID приложения
            api_hash: Hash приложения
            session_path: Путь к файлу сессии
        """
        self.client = TelegramAPIClient(api_id, api_hash, session_path)
        
        # Создаем специализированные менеджеры
        self.user_manager = TelegramUserManager(self.client)
        self.chat_manager = TelegramChatManager(self.client)
        self.message_manager = TelegramMessageManager(self.client)
    
    async def connect(self):
        """Подключается к Telegram."""
        # Попытка автоматической аутентификации
        await self.client.authenticate({})
    
    async def disconnect(self):
        """Отключается от Telegram."""
        await self.client.close()
    
    # Прокси методы для обратной совместимости
    
    async def get_user_by_id(self, user_id: str | int) -> dict[str, Any]:
        """Получает пользователя по ID."""
        return await self.user_manager.get_user_by_id(user_id)
    
    async def get_dialogs(self, **kwargs) -> list[dict[str, Any]]:
        """Получает диалоги."""
        return await self.chat_manager.get_dialogs(**kwargs)
    
    async def get_messages_by_date(self, chat_id: str | int, **kwargs) -> list[dict[str, Any]]:
        """Получает сообщения по датам."""
        return await self.message_manager.get_messages_by_date(chat_id, **kwargs)
    
    async def get_messages(self, chat_id: str | int, limit: int = 100) -> list[dict[str, Any]]:
        """Получает сообщения."""
        return await self.message_manager.get_messages(chat_id, limit)
    
    async def get_chat_participants(self, chat_id: str | int, 
                                   limit: int | None = None) -> list[dict[str, Any]]:
        """Получает участников чата."""
        return await self.user_manager.get_chat_participants(chat_id, limit)