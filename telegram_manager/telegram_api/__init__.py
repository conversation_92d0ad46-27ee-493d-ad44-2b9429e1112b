"""
Telegram API Module

This package provides functionality for interacting with the Telegram API
through specialized classes for managing chats, messages, and users.
"""

# Export pydantic-based versions
from .data_handlers.chat_handler import Chat<PERSON>andler
from .data_handlers.message_handler import MessageHandler
from .data_handlers.user_handler import UserHandler
from .managers import TelegramChatManager, TelegramManager, TelegramMessageManager, TelegramUserManager

# Для обратной совместимости экспортируем старые имена
from .managers import TelegramManager as TelegramManagerOld

# Keep imports for backward compatibility
from .telegram_client import TelegramAPIClient
from .telegram_client import TelegramAPIClient as TelegramClient

__all__ = [
    # Pydantic-based exports
    "TelegramClient",
    "UserHandler",
    "ChatHandler",
    "MessageHandler",
    # Exports for compatibility
    "TelegramAPIClient",
    "TelegramManager",
    "TelegramUserManager",
    "TelegramChatManager",
    "TelegramMessageManager",
    "TelegramManagerOld",
]