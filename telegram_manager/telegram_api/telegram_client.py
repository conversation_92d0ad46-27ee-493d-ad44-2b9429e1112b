"""
Новый Telegram API клиент с использованием базовой архитектуры.
"""

import logging
from datetime import datetime
from pathlib import Path
from typing import Any

from telethon import TelegramClient
from telethon.errors import FloodWaitError, SessionPasswordNeededError
from telethon.tl.types import Dialog, Message, User

from core.api.base import BaseSocialMediaAPI
from core.exceptions import (
    APIError,
    AuthenticationError,
    NetworkError,
    RateLimitError,
)
from core.utils.error_handler import ErrorHandler


class TelegramAPIClient(BaseSocialMediaAPI):
    """
    Telegram API клиент с использованием базовой архитектуры.
    """

    def __init__(self, api_id: int, api_hash: str, session_path: str):
        """
                Инициализирует Telegram клиент.

                Args:
                    api_id: ID приложения Telegram
        api_hash: Hash приложения Telegram
                    session_path: Путь к файлу сессии
        """
        super().__init__("Telegram")

        self.api_id = api_id
        self.api_hash = api_hash
        self.session_path = Path(session_path)

        # Создаем директорию для сессии если нужно
        self.session_path.parent.mkdir(parents=True, exist_ok=True)

        # Telethon клиент
        self.client: TelegramClient | None = None
        self.logger = logging.getLogger(__name__)

    async def authenticate(self, credentials: dict[str, Any]) -> bool:
        """
        Аутентифицирует клиента в Telegram.

        Args:
            credentials: Дополнительные учетные данные (phone, code, password)

        Returns:
            True если аутентификация успешна

        Raises:
            AuthenticationError: При ошибке аутентификации
        """
        try:
            # Создаем клиент если еще не создан
            if not self.client:
                self.client = TelegramClient(
                    str(self.session_path), self.api_id, self.api_hash
                )

            # Подключаемся
            await self.client.connect()

            # Проверяем авторизацию
            if await self.client.is_user_authorized():
                self._is_authenticated = True
                self.logger.info("Already authenticated to Telegram")
                return True

            # Если не авторизованы, нужны дополнительные данные
            phone = credentials.get("phone")
            if not phone:
                raise AuthenticationError("Phone number required for authentication")

            # Отправляем код
            await self.client.send_code_request(phone)

            # Получаем код
            code = credentials.get("code")
            if not code:
                raise AuthenticationError(
                    "Verification code required. Check your Telegram app."
                )

            try:
                # Пробуем войти с кодом
                await self.client.sign_in(phone, code)
                self._is_authenticated = True
                return True

            except SessionPasswordNeededError:
                # Требуется пароль 2FA
                password = credentials.get("password")
                if not password:
                    raise AuthenticationError(
                        "Two-factor authentication password required"
                    )

                await self.client.sign_in(password=password)
                self._is_authenticated = True
                return True

        except Exception as e:
            raise AuthenticationError(f"Failed to authenticate: {e!s}") from e

    @ErrorHandler.convert_api_error("Telegram")
    async def get_user(self, user_id: str | int) -> dict[str, Any]:
        """
        Получает информацию о пользователе.

        Args:
            user_id: ID или username пользователя

        Returns:
            Словарь с данными пользователя
        """
        self.check_authentication()

        try:
            entity = await self.client.get_entity(user_id)

            if isinstance(entity, User):
                return self._user_to_dict(entity)
            else:
                # Если это не пользователь, а чат/канал
                raise APIError(f"Entity {user_id} is not a user")

        except FloodWaitError as e:
            raise RateLimitError(
                f"Telegram rate limit: wait {e.seconds} seconds",
                retry_after=e.seconds,
            ) from e
        except Exception as e:
            raise NetworkError(api_name="Telegram", error_message=f"Failed to get user: {e!s}") from e

    @ErrorHandler.convert_api_error("Telegram")
    async def get_posts(
        self,
        user_id: str | int,
        limit: int = 50,
        date_from: datetime | None = None,
        date_to: datetime | None = None,
    ) -> list[dict[str, Any]]:
        """
        Получает сообщения (посты) из чата/канала.

        Args:
            user_id: ID чата/канала
            limit: Максимальное количество сообщений
            date_from: Начальная дата
            date_to: Конечная дата

        Returns:
            Список сообщений
        """
        self.check_authentication()

        try:
            messages = []

            # Логируем параметры
            self.logger.debug(f"Getting messages from {user_id}, limit={limit}, date_from={date_from}, date_to={date_to}")

            # Получаем сообщения
            # Безопасно обрабатываем параметры
            iter_params: dict[str, Any] = {
                "entity": user_id,
                "limit": limit,
            }
            if date_to:
                iter_params["offset_date"] = date_to
            if date_from:
                iter_params["min_id"] = 0
                
            async for message in self.client.iter_messages(**iter_params):
                # Проверяем тип и наличие даты
                if not hasattr(message, "date"):
                    self.logger.warning(f"Message {getattr(message, 'id', 'unknown')} has no date attribute")
                    continue
                    
                # Фильтруем по датам если нужно
                if date_from and message.date and message.date < date_from:
                    break

                # Пропускаем сообщения без даты
                if message.date:
                    messages.append(self._message_to_dict(message))

            return messages

        except FloodWaitError as e:
            raise RateLimitError(
                f"Telegram rate limit: wait {e.seconds} seconds",
                retry_after=e.seconds,
            ) from e
        except Exception as e:
            self.logger.exception(f"Error in get_posts: {type(e).__name__}: {e!s}")
            self.logger.error("Traceback: ", exc_info=True)
            raise NetworkError(api_name="Telegram", error_message=f"Failed to get messages: {e!s}") from e

    async def get_post_comments(
        self, post_id: str | int, limit: int = 100
    ) -> list[dict[str, Any]]:
        """
        Получает комментарии к посту (для каналов с комментариями).

        Args:
            post_id: ID поста
            limit: Максимальное количество комментариев

        Returns:
            Список комментариев
        """
        # В Telegram комментарии реализованы через связанные чаты
        # Эта функциональность требует дополнительной логики
        raise NotImplementedError("Comments in Telegram require linked discussion chat")

    @ErrorHandler.convert_api_error("Telegram")
    async def get_dialogs(
        self,
        limit: int = 100,
        include_private: bool = True,
        include_groups: bool = True,
        include_channels: bool = True,
    ) -> list[dict[str, Any]]:
        """
        Получает список диалогов (чатов).

        Args:
            limit: Максимальное количество
            include_private: Включать приватные чаты
            include_groups: Включать группы
            include_channels: Включать каналы

        Returns:
            Список диалогов
        """
        self.check_authentication()

        try:
            dialogs = []
            checked_count = 0
            max_iterations = (
                limit * 10 if limit else 1000
            )  # Защита от бесконечного цикла

            self.logger.debug(
                f"Getting dialogs with limit={limit}, filters: private={include_private}, groups={include_groups}, channels={include_channels}"
            )

            async for dialog in self.client.iter_dialogs(limit=None):
                checked_count += 1

                # Проверяем, не превысили ли мы максимум итераций
                if checked_count > max_iterations:
                    break

                # Фильтруем по типам
                if dialog.is_user and not include_private:
                    self.logger.debug(
                        f"Skipping private chat: {dialog.name or dialog.id}"
                    )
                    continue
                if dialog.is_group and not include_groups:
                    self.logger.debug(f"Skipping group: {dialog.name or dialog.id}")
                    continue
                if dialog.is_channel and not include_channels:
                    self.logger.debug(
                        f"Skipping channel: {dialog.name or dialog.id}"
                    )
                    continue

                dialogs.append(self._dialog_to_dict(dialog))

                # Если набрали нужное количество, выходим
                if limit and len(dialogs) >= limit:
                    break

            return dialogs

        except FloodWaitError as e:
            raise RateLimitError(
                f"Telegram rate limit: wait {e.seconds} seconds",
                retry_after=e.seconds,
            ) from e
        except Exception as e:
            raise NetworkError(api_name="Telegram", error_message=f"Failed to get dialogs: {e!s}") from e

    @ErrorHandler.convert_api_error("Telegram")
    async def get_chat_participants(
        self, chat_id: str | int, limit: int | None = None
    ) -> list[dict[str, Any]]:
        """
        Получает участников чата.

        Args:
            chat_id: ID чата
            limit: Максимальное количество

        Returns:
            Список участников
        """
        self.check_authentication()

        try:
            participants = []

            async for user in self.client.iter_participants(chat_id, limit=limit):
                participants.append(self._user_to_dict(user))

            return participants

        except FloodWaitError as e:
            raise RateLimitError(
                f"Telegram rate limit: wait {e.seconds} seconds",
                retry_after=e.seconds,
            ) from e
        except Exception as e:
            raise NetworkError(api_name="Telegram", error_message=f"Failed to get participants: {e!s}") from e

    async def download_media(
        self, message: Message, file_path: str | None = None
    ) -> str | None:
        """
        Загружает медиа из сообщения.

        Args:
            message: Объект сообщения
            file_path: Путь для сохранения

        Returns:
            Путь к сохраненному файлу или None
        """
        self.check_authentication()

        try:
            if message.media:
                path = await self.client.download_media(message.media, file=file_path)
                return str(path) if path else None
        except Exception as e:
            self.logger.exception(f"Failed to download media: {e}")

        return None

    # Вспомогательные методы для преобразования объектов Telethon

    def _user_to_dict(self, user: User) -> dict[str, Any]:
        """Преобразует объект User в словарь."""
        return {
            "id": user.id,
            "user_id": user.id,
            "username": user.username,
            "first_name": user.first_name,
            "last_name": user.last_name,
            "phone": user.phone,
            "is_bot": user.bot,
            "is_verified": user.verified,
            "is_restricted": user.restricted,
            "is_deleted": user.deleted,
            "is_premium": getattr(user, "premium", False),
            "profile_photo_id": user.photo.photo_big.id if user.photo and hasattr(user.photo, "photo_big") else None,
            "lang_code": user.lang_code,
            "last_seen_date": (
                user.status.was_online if hasattr(user.status, "was_online") else None
            ),
        }

    def _message_to_dict(self, message: Message) -> dict[str, Any]:
        """Преобразует объект Message в словарь."""
        msg_dict = {
            "id": message.id,
            "date": message.date,
            "text": message.text or message.message or "",
            "message": message.text
            or message.message
            or "",  # Для совместимости с тестами
            "from_id": message.from_id.user_id if message.from_id else None,
            "is_reply": bool(message.reply_to),
            "is_forward": bool(message.fwd_from),
            "is_edited": bool(message.edit_date),
            "edit_date": message.edit_date,
            "views": message.views,
            "forwards": message.forwards,
            "mentioned": message.mentioned,
            "pinned": message.pinned,
        }

        # Обработка медиа
        if message.media:
            media_type = message.media.__class__.__name__.lower()
            msg_dict["media"] = {
                "type": media_type,
                "_": media_type,
            }

        # Обработка ответа
        if message.reply_to:
            msg_dict["reply_to_msg_id"] = message.reply_to.reply_to_msg_id

        # Обработка пересылки
        if message.fwd_from:
            msg_dict["forward"] = {
                "date": message.fwd_from.date,
                "from_id": (
                    message.fwd_from.from_id.user_id
                    if message.fwd_from.from_id
                    else None
                ),
                "from_name": message.fwd_from.from_name,
                "channel_post": message.fwd_from.channel_post,
            }

        # Entities (упоминания, ссылки и т.д.)
        if message.entities:
            msg_dict["entities"] = {
                "mentions": [
                    {"user_id": e.user_id}
                    for e in message.entities
                    if hasattr(e, "user_id")
                ]
            }

        # Действия (присоединение, выход и т.д.)
        if message.action:
            action_type = message.action.__class__.__name__
            msg_dict["action"] = {
                "_": action_type,
                "users": getattr(message.action, "users", []),
            }
            msg_dict["action_type"] = action_type

        return msg_dict

    def _dialog_to_dict(self, dialog: Dialog) -> dict[str, Any]:
        """Преобразует объект Dialog в словарь."""
        entity = dialog.entity
        
        # Преобразуем entity в словарь с нужными полями
        entity_dict = {}
        if entity:
            entity_dict = {
                "id": entity.id,
                "megagroup": getattr(entity, "megagroup", False),
                "broadcast": getattr(entity, "broadcast", False),
                "username": getattr(entity, "username", None),
                "title": getattr(entity, "title", None),
            }

        return {
            "id": dialog.id,
            "name": dialog.name or dialog.title,
            "entity": entity_dict,
            "_raw_entity": entity,  # Store original entity for photo downloads
            "is_user": dialog.is_user,
            "is_group": dialog.is_group,
            "is_channel": dialog.is_channel,
            "unread_count": dialog.unread_count,
            "unread_mentions_count": dialog.unread_mentions_count,
        }

    async def close(self):
        """Закрывает соединение с Telegram."""
        await super().close()

        if self.client and self.client.is_connected():
            await self.client.disconnect()
            self.logger.info("Disconnected from Telegram")
