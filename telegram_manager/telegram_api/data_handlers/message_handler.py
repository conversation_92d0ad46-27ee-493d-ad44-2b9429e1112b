"""Message data handler with Pydantic validation."""

import logging
from typing import Any

from django.db import transaction

from telegram_manager.models import TelegramChat, TelegramMessage, TelegramUser
from telegram_manager.schemas.telegram import MessageMediaData, TelegramMessageData

logger = logging.getLogger(__name__)


class MessageHandler:
    """Handler for processing Telegram message data with Pydantic validation."""
    
    @classmethod
    def process_message_data(
        cls, 
        message_data: TelegramMessageData,
        chat: TelegramChat,
        users_cache: dict[int, TelegramUser] | None = None
    ) -> TelegramMessage:
        """
        Process and save validated message data.
        
        Args:
            message_data: Validated TelegramMessageData from API
            chat: Associated TelegramChat instance
            users_cache: Optional cache of user_id -> TelegramUser mapping
            
        Returns:
            TelegramMessage model instance
        """
        try:
            with transaction.atomic():
                # Use model_dump to get dict with correct field names
                data_dict = message_data.model_dump()
                
                # Extract message_id for lookup
                message_id = data_dict.pop("message_id")
                
                logger.debug(
                    f"[TRANSACTION START] Starting atomic transaction for saving message {message_id} "
                    f"in chat {chat.chat_id}"
                )
                
                # Prepare model data
                model_data = {
                    "chat": chat,
                    "text": data_dict.get("text", ""),
                    "date": data_dict.get("date"),
                    "is_edited": data_dict.get("is_edited", False),
                    "edit_date": data_dict.get("edit_date"),
                    "views": data_dict.get("views"),
                    "forwards": data_dict.get("forwards"),
                    "is_mentioned": data_dict.get("is_mentioned", False),
                    "is_silent": data_dict.get("is_silent", False),
                    "is_post": data_dict.get("is_post", False),
                    "is_pinned": data_dict.get("is_pinned", False),
                    "via_bot_id": data_dict.get("via_bot_id"),
                }
                
                # Handle sender
                if data_dict.get("from_id"):
                    if users_cache and data_dict["from_id"] in users_cache:
                        model_data["sender"] = users_cache[data_dict["from_id"]]
                    else:
                        # Try to get user from database
                        try:
                            model_data["sender"] = TelegramUser.objects.get(
                                user_id=data_dict["from_id"]
                            )
                        except TelegramUser.DoesNotExist:
                            logger.warning(f"Sender {data_dict['from_id']} not found for message {message_id}")
                
                # Handle reply
                if data_dict.get("reply_to_msg_id"):
                    model_data["reply_to_msg_id"] = data_dict["reply_to_msg_id"]
                    # Try to find the replied message
                    try:
                        model_data["reply_to"] = TelegramMessage.objects.get(
                            chat=chat,
                            message_id=data_dict["reply_to_msg_id"]
                        )
                    except TelegramMessage.DoesNotExist:
                        pass
                
                # Handle forward
                forward_data = data_dict.get("forward")
                if forward_data:
                    model_data["is_forwarded"] = True
                    model_data["forward_date"] = forward_data.get("date")
                    model_data["forward_from_name"] = forward_data.get("from_name")
                    model_data["forward_from_message_id"] = forward_data.get("channel_post")
                    model_data["post_author"] = forward_data.get("post_author")
                    
                    # Handle forward from user
                    if forward_data.get("from_id"):
                        try:
                            model_data["forward_from"] = TelegramUser.objects.get(
                                user_id=forward_data["from_id"]
                            )
                        except TelegramUser.DoesNotExist:
                            pass
                
                # Handle media
                media_data = data_dict.get("media")
                if media_data:
                    model_data["has_media"] = True
                    model_data["media_type"] = media_data.get("type", "unknown")
                    model_data["media_data"] = media_data  # Store full media data as JSON
                
                # Handle action
                action_data = data_dict.get("action")
                if action_data:
                    model_data["action_type"] = action_data.get("type", "unknown")
                    model_data["action"] = action_data  # Store full action data as JSON
                
                # Update or create message
                message, created = TelegramMessage.objects.update_or_create(
                    message_id=message_id,
                    chat=chat,
                    defaults=model_data
                )
                
                # Handle entities (mentions, hashtags, urls)
                cls._process_entities(message, data_dict, users_cache)
                
                if created:
                    logger.info(f"Created new message: {message_id} in chat {chat.chat_id}")
                else:
                    logger.debug(f"Updated message: {message_id} in chat {chat.chat_id}")
                
                logger.debug(
                    f"[TRANSACTION END] Successfully saved message {message_id} in transaction "
                    f"(action: {'created' if created else 'updated'})"
                )
                
                return message
                
        except Exception as e:
            logger.exception(f"Error processing message data: {e}")
            raise
    
    @classmethod
    def _process_entities(
        cls, 
        message: TelegramMessage, 
        data_dict: dict[str, Any],
        users_cache: dict[int, TelegramUser] | None = None
    ) -> None:
        """Process message entities (mentions, hashtags, etc)."""
        # Clear existing relationships
        message.mentioned_users.clear()
        
        # Extract hashtags
        hashtags = data_dict.get("hashtags", [])
        if hashtags:
            # Store hashtags in message (if we have a field for it)
            # For now, we can store in the text or a custom field
            pass
        
        # Extract entities for processing mentions
        entities = data_dict.get("entities", [])
        
        mentioned_user_ids = set()
        
        # Process entities to find user mentions
        for entity in entities:
            if entity.get("type") == "mention" and entity.get("user_id"):
                mentioned_user_ids.add(entity["user_id"])
        
        # Add mentioned users
        for user_id in mentioned_user_ids:
            if users_cache and user_id in users_cache:
                message.mentioned_users.add(users_cache[user_id])
            else:
                try:
                    user = TelegramUser.objects.get(user_id=user_id)
                    message.mentioned_users.add(user)
                except TelegramUser.DoesNotExist:
                    logger.warning(f"Mentioned user {user_id} not found")
        
        # Process action users
        action_data = data_dict.get("action")
        if action_data and action_data.get("users"):
            for user_id in action_data["users"]:
                if users_cache and user_id in users_cache:
                    message.action_users.add(users_cache[user_id])
                else:
                    try:
                        user = TelegramUser.objects.get(user_id=user_id)
                        message.action_users.add(user)
                    except TelegramUser.DoesNotExist:
                        logger.warning(f"Action user {user_id} not found")
    
    @classmethod
    def batch_process_messages(
        cls, 
        messages_data: list[TelegramMessageData],
        chat: TelegramChat,
        users_cache: dict[int, TelegramUser] | None = None
    ) -> list[TelegramMessage]:
        """
        Process multiple messages efficiently.
        
        Args:
            messages_data: List of validated TelegramMessageData
            chat: Associated TelegramChat instance
            users_cache: Optional cache of user_id -> TelegramUser mapping
            
        Returns:
            List of TelegramMessage instances
        """
        processed_messages = []
        
        # If no users cache provided, build one from existing users
        if not users_cache:
            # Collect all user IDs from messages
            user_ids: set[int] = set()
            for msg_data in messages_data:
                if msg_data.from_id:
                    user_ids.add(msg_data.from_id)
                
                # Check entities for user mentions
                for entity in msg_data.entities:
                    if entity.user_id:
                        user_ids.add(entity.user_id)
                
                # Check action users
                if msg_data.action and msg_data.action.users:
                    user_ids.update(msg_data.action.users)
                
                # Check forward from
                if msg_data.forward and msg_data.forward.from_id:
                    user_ids.add(msg_data.forward.from_id)
            
            # Load users from database
            users_cache = {
                user.user_id: user 
                for user in TelegramUser.objects.filter(user_id__in=user_ids)
            }
        
        with transaction.atomic():
            logger.debug(
                f"[TRANSACTION START] Starting atomic transaction for batch processing "
                f"{len(messages_data)} messages in chat {chat.chat_id}"
            )
            
            for message_data in messages_data:
                try:
                    message = cls.process_message_data(message_data, chat, users_cache)
                    processed_messages.append(message)
                except Exception as e:
                    logger.exception(f"Error processing message {message_data.message_id}: {e}")
                    continue
            
            logger.debug(
                f"[TRANSACTION END] Successfully processed {len(processed_messages)} messages "
                f"out of {len(messages_data)} in transaction"
            )
        
        logger.info(f"Processed {len(processed_messages)} messages for chat {chat.chat_id}")
        return processed_messages
    
    @classmethod
    def extract_media_info(cls, media: MessageMediaData) -> dict[str, Any]:
        """
        Extract relevant media information.
        
        Args:
            media: Validated MessageMediaData
            
        Returns:
            Dict with media information
        """
        media_info = {
            "type": media.type,
            "mime_type": media.mime_type,
            "size": media.size,
        }
        
        if media.type == "photo":
            media_info.update({
                "width": media.width,
                "height": media.height,
            })
        elif media.type in ["video", "audio"]:
            media_info.update({
                "duration": media.duration,
            })
        elif media.type == "document":
            media_info.update({
                "file_name": media.file_name,
            })
        
        return media_info