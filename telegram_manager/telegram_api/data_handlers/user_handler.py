"""User data handler with Pydantic validation."""

import logging
from typing import Any

from django.db import transaction

from telegram_manager.models import TelegramUser
from telegram_manager.schemas.telegram import TelegramUserData

logger = logging.getLogger(__name__)


class UserHandler:
    """Handler for processing Telegram user data with Pydantic validation."""
    
    @classmethod
    def process_user_data(cls, user_data: TelegramUserData) -> TelegramUser:
        """
        Process and save validated user data.
        
        Args:
            user_data: Validated TelegramUserData from API
            
        Returns:
            TelegramUser model instance
        """
        try:
            with transaction.atomic():
                # Use model_dump to get dict with correct field names
                data_dict = user_data.model_dump()
                
                # Extract user_id for lookup
                user_id = data_dict.pop("user_id")
                
                logger.debug(
                    f"[TRANSACTION START] Starting atomic transaction for saving user {user_id}"
                )
                
                # Remove external_id as it's a read-only property
                data_dict.pop("external_id", None)
                
                # Remove None values for created_at and updated_at
                # These fields have auto_now_add and auto_now in the model
                data_dict.pop("created_at", None)
                data_dict.pop("updated_at", None)
                
                # Update or create user
                user, created = TelegramUser.objects.update_or_create(
                    user_id=user_id,
                    defaults=data_dict
                )
                
                if created:
                    logger.info(f"Created new user: {user.username or user.user_id}")
                else:
                    logger.debug(f"Updated user: {user.username or user.user_id}")
                
                logger.debug(
                    f"[TRANSACTION END] Successfully saved user {user_id} in transaction "
                    f"(action: {'created' if created else 'updated'})"
                )
                
                return user
                
        except Exception as e:
            logger.exception(f"Error processing user data: {e}")
            raise
    
    @classmethod
    def batch_process_users(cls, users_data: list[TelegramUserData]) -> list[TelegramUser]:
        """
        Process multiple users efficiently.
        
        Args:
            users_data: List of validated TelegramUserData
            
        Returns:
            List of TelegramUser instances
        """
        processed_users = []
        
        with transaction.atomic():
            logger.debug(
                f"[TRANSACTION START] Starting atomic transaction for batch processing "
                f"{len(users_data)} users"
            )
            
            for user_data in users_data:
                try:
                    user = cls.process_user_data(user_data)
                    processed_users.append(user)
                except Exception as e:
                    logger.exception(f"Error processing user {user_data.user_id}: {e}")
                    continue
            
            logger.debug(
                f"[TRANSACTION END] Successfully processed {len(processed_users)} users "
                f"out of {len(users_data)} in transaction"
            )
        
        logger.info(f"Processed {len(processed_users)} users")
        return processed_users
    
    @classmethod
    def extract_users_from_messages(cls, messages: list[Any]) -> dict[int, TelegramUserData]:
        """
        Extract unique users from messages.
        
        Args:
            messages: List of TelegramMessageData
            
        Returns:
            Dict mapping user_id to TelegramUserData
        """
        users: dict[int, Any] = {}
        
        for message in messages:
            # Extract sender
            if message.from_id and message.from_id not in users:
                # This would need additional API calls to get full user data
                # For now, we'll just track the IDs
                pass
            
            # Extract mentioned users
            for entity in message.entities:
                if entity.type == "mention" and entity.user_id:
                    if entity.user_id not in users:
                        # Track mentioned user ID
                        pass
            
            # Extract action users
            if message.action and message.action.users:
                for user_id in message.action.users:
                    if user_id not in users:
                        # Track action user ID
                        pass
        
        return users