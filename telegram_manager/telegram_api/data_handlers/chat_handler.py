"""Chat data handler with Pydantic validation."""

import logging

from django.db import transaction

from telegram_manager.models import TelegramChat
from telegram_manager.schemas.telegram import TelegramChatData

logger = logging.getLogger(__name__)


class ChatHandler:
    """Handler for processing Telegram chat data with Pydantic validation."""
    
    @classmethod
    def process_chat_data(cls, chat_data: TelegramChatData) -> TelegramChat:
        """
        Process and save validated chat data.
        
        Args:
            chat_data: Validated TelegramChatData from API
            
        Returns:
            TelegramChat model instance
        """
        try:
            with transaction.atomic():
                # Use model_dump to get dict with correct field names
                data_dict = chat_data.model_dump()
                
                # Extract chat_id for lookup
                chat_id = data_dict.pop("chat_id")
                
                logger.debug(
                    f"[TRANSACTION START] Starting atomic transaction for saving chat {chat_id}"
                )
                
                # Map pydantic fields to model fields
                model_data = {
                    "chat_id": chat_id,
                    "title": data_dict.get("title"),
                    "username": data_dict.get("username"),
                    "verified": data_dict.get("is_verified", False),
                    "restricted": data_dict.get("is_restricted", False),
                    "scam": data_dict.get("is_scam", False),
                    "participants_count": data_dict.get("participants_count"),
                    "broadcast": data_dict.get("is_broadcast", False),
                    "megagroup": data_dict.get("is_megagroup", False),
                    "photo_id": data_dict.get("photo_id"),
                    # Only include fields that exist in the model
                }
                
                # Update or create chat
                chat, created = TelegramChat.objects.update_or_create(
                    chat_id=chat_id,
                    defaults=model_data
                )
                
                if created:
                    logger.info(f"Created new chat: {chat.title} ({chat.chat_id})")
                else:
                    logger.debug(f"Updated chat: {chat.title} ({chat.chat_id})")
                
                logger.debug(
                    f"[TRANSACTION END] Successfully saved chat {chat_id} in transaction "
                    f"(action: {'created' if created else 'updated'})"
                )
                
                return chat
                
        except Exception as e:
            logger.error(
                f"[TRANSACTION ERROR] Failed to save chat {chat_data.chat_id} in transaction: {e}"
            )
            logger.exception(f"Error processing chat data: {e}")
            raise
    
    @classmethod
    def batch_process_chats(cls, chats_data: list[TelegramChatData]) -> list[TelegramChat]:
        """
        Process multiple chats efficiently.
        
        Args:
            chats_data: List of validated TelegramChatData
            
        Returns:
            List of TelegramChat instances
        """
        processed_chats = []
        
        with transaction.atomic():
            logger.debug(
                f"[TRANSACTION START] Starting atomic transaction for batch processing "
                f"{len(chats_data)} chats"
            )
            
            for chat_data in chats_data:
                try:
                    chat = cls.process_chat_data(chat_data)
                    processed_chats.append(chat)
                except Exception as e:
                    logger.exception(f"Error processing chat {chat_data.chat_id}: {e}")
                    continue
            
            logger.debug(
                f"[TRANSACTION END] Successfully processed {len(processed_chats)} chats "
                f"out of {len(chats_data)} in transaction"
            )
        
        logger.info(f"Processed {len(processed_chats)} chats")
        return processed_chats
    
    @classmethod
    def update_chat_stats(cls, chat: TelegramChat, messages_count: int = 0) -> None:
        """
        Update chat statistics.
        
        Args:
            chat: TelegramChat instance
            messages_count: Number of messages in chat
        """
        try:
            # Currently we don't store messages_count in the model
            # This is a placeholder for future statistics tracking
            logger.debug(f"Chat {chat.chat_id} has {messages_count} messages")
                
        except Exception as e:
            logger.exception(f"Error updating chat stats: {e}")