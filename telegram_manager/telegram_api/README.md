# Telegram API Module

This directory contains the refactored implementation of the Telegram API integration for the SocialManager project.

## Architecture

The implementation follows a modular, layered architecture:

### Base Components

- `base_manager.py`: Contains the `BaseTelegramManager` class that provides common functionality for all managers, including authentication, session management, and connection handling.

### Specialized Managers

- `telegram_manager.py`: Main coordinator that initializes and delegates to specialized managers.
- `telegram_users.py`: Manages Telegram user operations via `TelegramUserManager`.
- `telegram_messages.py`: Manages Telegram message operations via `TelegramMessageManager`.
- `telegram_chats.py`: Manages Telegram chat operations via `TelegramChatManager`.

### Message Handlers

Specialized handlers for different aspects of Telegram messages, located in the `message_handlers` directory:

- `forward_handler.py`: Handles forwarded message processing.
- `media_handler.py`: Handles media content (photos, videos, documents, etc.).
- `entity_handler.py`: Handles message entities (mentions, hashtags, URLs, etc.).

## Usage

### Basic Usage

```python
import asyncio
from telegram_manager.telegram_api.telegram_manager import TelegramManager

async def main():
    # Create a manager
    manager = TelegramManager()
    
    # Connect to Telegram
    await manager.connect()
    
    try:
        # Get user information
        user_data = await manager.get_user_by_id(user_id)
        
        # Get chat information
        chat_data = await manager.get_chat_by_id(chat_id)
        
        # Get messages
        messages = await manager.get_messages_by_date(
            chat_id, 
            limit=100, 
            date_from=start_date, 
            date_to=end_date
        )
    finally:
        # Disconnect when done
        await manager.disconnect()

# Run the async function
asyncio.run(main())
```

### Dependency Injection

The architecture supports dependency injection for the Telegram client:

```python
# Create a shared client
client = TelegramClient(session_path, api_id, api_hash)
await client.start()

# Create specialized managers with the shared client
user_manager = TelegramUserManager(client=client)
message_manager = TelegramMessageManager(client=client)
chat_manager = TelegramChatManager(client=client)

# Use the managers
user_data = await user_manager.get_user_by_id(user_id)
```

## Utilities

The implementation uses utility functions from the `telegram_manager.utils` package:

- `data_processors.py`: Contains functions for standardizing and processing data from Telegram API.

## Service Layer

The service layer in `telegram_manager.services` handles the integration with Django models:

- `user_service.py`: Service for processing and saving Telegram users.
- `message_service.py`: Service for processing and saving Telegram messages.

## Django Management Commands

The refactored implementation is used by Django management commands in `telegram_manager.management.commands`:

- `base_telegram_command.py`: Base command class for Telegram operations.
- `telegram_messages.py`: Command for importing messages from Telegram.
- `telegram_users.py`: Command for importing users from Telegram chats.
- `telegram_chats.py`: Command for importing chats from Telegram.

## Error Handling

The implementation includes comprehensive error handling at all levels:

- Each operation has proper try/except blocks.
- Errors are logged using Python's logging module.
- User-friendly error messages are provided.

## Asynchronous Operations

The implementation uses async/await for better performance and responsiveness:

- All Telegram API operations are asynchronous.
- Operations can be batched for better performance.
- Django's `sync_to_async` is used for database operations from async code.