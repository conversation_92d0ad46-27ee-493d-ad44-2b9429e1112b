
from django.contrib import messages
from django.shortcuts import redirect, render

from .forms import ChatImportForm, MessageImportForm
from .models import TelegramChat


def admin_telegram_chat_import_form(request, admin_site, model_admin):
    """
    View for displaying the Telegram chat import form in admin.
    This is a view function despite being used in admin because it renders a template.
    """
    form = ChatImportForm(request.POST or None)

    context = {
        "title": "Import Chats from Telegram",
        "subtitle": "Import chats, users and messages from Telegram",
        "site_title": admin_site.site_title,
        "site_header": admin_site.site_header,
        "has_permission": model_admin.has_change_permission(request),
        "app_label": model_admin.model._meta.app_label,
        "opts": model_admin.model._meta,
        "form": form,
    }
    return render(request, "admin/telegram_manager/telegramchat/import_form.html", context)


def admin_telegram_chat_do_import(request, admin_site, model_admin):
    """
    View function for processing the chat import form submission and running the import.
    """
    if request.method != "POST":
        return redirect("telegram_manager:telegram_manager_telegramchat_import-from-telegram")

    try:
        form = ChatImportForm(request.POST)

        if not form.is_valid():
            messages.error(request, "Invalid form data")
            return redirect("telegram_manager:telegram_manager_telegramchat_import-from-telegram")

        # Get the limit from form data
        chat_limit = form.cleaned_data["chat_limit"]
        if chat_limit == "all":
            limit = None
        else:
            limit = form.cleaned_data["custom_limit"]

        # Get the chat type filters from form data
        include_private = form.cleaned_data["include_private"]
        include_groups = form.cleaned_data["include_groups"]
        include_supergroups = form.cleaned_data["include_supergroups"]
        include_channels = form.cleaned_data["include_channels"]
        
        # Get execution mode
        execution_mode = form.cleaned_data.get("execution_mode", "async")
        force_sync = execution_mode == "sync"
        
        # Use ImportService for unified sync/async execution
        from telegram_manager.services import ImportService
        
        # Prepare parameters
        import_params = {
            "chat_limit": chat_limit,
            "custom_limit": limit,
            "include_private": include_private,
            "include_groups": include_groups,
            "include_supergroups": include_supergroups,
            "include_channels": include_channels,
        }
        
        # Execute import
        result = ImportService.import_chats(force_sync=force_sync, **import_params)
        
        # Handle result based on sync/async mode
        if force_sync:
            # Synchronous result - show immediate feedback
            output_str = "Chat import completed:\n"
            output_str += f"Total chats: {result.get('total_chats', 0)}\n"
            output_str += f"Created: {result.get('created', 0)}\n"
            output_str += f"Updated: {result.get('updated', 0)}"
            
            if result.get("errors"):
                output_str += f"\nErrors: {len(result['errors'])}"
                
            messages.success(request, output_str)
        else:
            # Asynchronous result - show task info
            messages.success(
                request,
                f"Chat import started asynchronously. Task ID: {result.id}. "
                f"Check Flower at http://localhost:5555 or monitoring page for progress."
            )

    except Exception as e:
        import traceback
        messages.error(request, f"Error importing chats from Telegram: {e!s}\n{traceback.format_exc()}")

    # Redirect back to the changelist
    return redirect("admin:telegram_manager_telegramchat_changelist")


def admin_telegram_message_import_form(request, admin_site, model_admin):
    """
    View for displaying the Telegram message import form in admin.
    """
    # Get all chats to allow selection
    chats = TelegramChat.objects.all()
    form = MessageImportForm(request.POST or None)

    context = {
        "title": "Import Messages from Telegram",
        "subtitle": "Import messages from selected Telegram chats",
        "site_title": admin_site.site_title,
        "site_header": admin_site.site_header,
        "has_permission": model_admin.has_change_permission(request),
        "app_label": model_admin.model._meta.app_label,
        "opts": model_admin.model._meta,
        "form": form,
        "chats": chats,
    }
    return render(request, "admin/telegram_manager/telegrammessage/import_messages_form.html", context)


def admin_telegram_message_do_import(request, admin_site, model_admin):
    """
    View function for processing the message import form submission and running the import.
    """
    if request.method != "POST":
        return redirect("admin:telegram_manager_telegrammessage_changelist")

    try:
        # Get form data
        form = MessageImportForm(request.POST)

        if not form.is_valid():
            messages.error(request, "Invalid form data")
            return redirect("admin:telegram_manager_telegrammessage_changelist")

        # Get selected chat IDs
        chat_ids = request.POST.getlist("chat_ids")

        if not chat_ids:
            messages.error(request, "Please select at least one chat to import messages from")
            return redirect("telegram_manager:telegram_manager_telegrammessage_import-messages")

        # Get form data
        category = form.cleaned_data["category"]
        date_from = form.cleaned_data.get("date_from")
        date_to = form.cleaned_data.get("date_to")
        message_limit = form.cleaned_data["message_limit"]
        
        # Get execution mode
        execution_mode = form.cleaned_data.get("execution_mode", "async")
        force_sync = execution_mode == "sync"
        
        # Use ImportService for unified sync/async execution
        from telegram_manager.services import ImportService
        
        # Prepare parameters
        import_params = {
            "category": category,
            "message_limit": message_limit,
            "skip_user_fetch": False,  # Default to fetching user data
        }
        
        # Add date parameters for custom category
        if category == "custom" and date_from and date_to:
            import_params["date_from"] = date_from.isoformat()
            import_params["date_to"] = date_to.isoformat()
        
        # Note: The current implementation imports from all chats
        # We might need to update the task to support specific chat IDs
        # For now, we'll import from all chats
        
        # Execute import
        result = ImportService.import_messages(force_sync=force_sync, **import_params)
        
        # Handle result based on sync/async mode
        if force_sync:
            # Synchronous result - show immediate feedback
            output_str = "Message import completed:\n"
            output_str += f"Total messages: {result.get('total_messages', 0)}\n"
            output_str += f"Created: {result.get('created', 0)}\n"
            output_str += f"Updated: {result.get('updated', 0)}\n"
            output_str += f"Users created: {result.get('users_created', 0)}"
            
            if result.get("errors"):
                output_str += f"\nErrors: {len(result['errors'])}"
                
            messages.success(request, output_str)
        else:
            # Asynchronous result - show task info
            messages.success(
                request,
                f"Message import started asynchronously. Task ID: {result.id}. "
                f"Check Flower at http://localhost:5555 or monitoring page for progress."
            )

    except Exception as e:
        import traceback
        messages.error(request, f"Error importing messages: {e!s}\n{traceback.format_exc()}")

    return redirect("admin:telegram_manager_telegrammessage_changelist")
