"""Pydantic models for Telegram API data."""

from datetime import datetime
from typing import Any

from pydantic import ConfigDict, Field, field_validator, model_validator

# Импорт новых базовых классов и миксинов
from core.schemas.base import BaseSchema, ContentSchema, MediaSchema, SocialMediaSchema
from core.schemas.mixins import ValidationHelperMixin
from core.schemas.validators import CommonValidators, TelegramValidators


class TelegramUserData(SocialMediaSchema, ValidationHelperMixin):
    """Model for Telegram user data."""
    
    model_config = ConfigDict(
        populate_by_name=True,
        extra="allow"  # Allow extra fields for backward compatibility
    )
    
    # Основные поля (наследуются от SocialMediaSchema)
    external_id: str = Field(..., alias="id")  # user_id as string for base class compatibility
    user_id: int = Field(..., alias="id")  # Дублируем для обратной совместимости
    first_name: str | None = Field(default="")
    last_name: str | None = Field(default="")
    
    # Telegram специфичные поля
    phone: str | None = None
    is_bot: bool = Field(default=False, alias="bot")
    is_verified: bool = Field(default=False, alias="verified")
    is_restricted: bool = Field(default=False, alias="restricted")
    is_scam: bool = Field(default=False, alias="scam")
    is_fake: bool = Field(default=False, alias="fake")
    is_deleted: bool = Field(default=False, alias="deleted")
    is_premium: bool = Field(default=False, alias="premium")
    
    @field_validator("first_name", "last_name", mode="before")
    @classmethod
    def clean_name(cls, v: Any) -> str:
        """Clean and normalize name fields."""
        if v is None:
            return ""
        return CommonValidators.clean_text(str(v))
    
    @field_validator("external_id")
    @classmethod
    def validate_external_id(cls, v: str) -> str:
        """Validate Telegram user ID as string."""
        # Convert to int for validation, then back to string
        user_id = int(v)
        validated_id = TelegramValidators.validate_user_id(user_id)
        return str(validated_id)
    
    @field_validator("phone", mode="before")
    @classmethod
    def normalize_phone(cls, v: Any) -> str | None:
        """Normalize phone number."""
        if v:
            return TelegramValidators.normalize_phone(str(v))
        return None
    
    @model_validator(mode="before")
    @classmethod
    def handle_field_mappings(cls, data: dict[str, Any]) -> dict[str, Any]:
        """Handle different field name mappings."""
        if isinstance(data, dict):
            # Map user_id -> id (for alias)
            if "user_id" in data and "id" not in data:
                data["id"] = data["user_id"]
                
            # If user is deleted, set default values
            if data.get("deleted") or data.get("is_deleted"):
                data["first_name"] = "Deleted"
                data["last_name"] = "Account"
                data["username"] = None
                data["phone"] = None
        
        return data


class TelegramChatData(BaseSchema, ValidationHelperMixin):
    """Model for Telegram chat data."""
    
    model_config = ConfigDict(populate_by_name=True)
    
    # Основные поля
    chat_id: int = Field(..., alias="id")
    title: str
    username: str | None = None
    chat_type: str = Field(default="chat", alias="type")
    description: str | None = None
    photo: str | None = None
    
    # Telegram специфичные поля
    is_verified: bool = Field(default=False, alias="verified")
    is_restricted: bool = Field(default=False, alias="restricted")
    is_scam: bool = Field(default=False, alias="scam")
    is_fake: bool = Field(default=False, alias="fake")
    participants_count: int | None = Field(default=None)
    is_broadcast: bool = Field(default=False, alias="broadcast")
    is_megagroup: bool = Field(default=False, alias="megagroup")
    is_gigagroup: bool = Field(default=False, alias="gigagroup")
    
    @field_validator("chat_id")
    @classmethod
    def validate_chat_id(cls, v: int) -> int:
        """Validate Telegram chat ID."""
        return TelegramValidators.validate_chat_id(v)
    
    @field_validator("chat_type", mode="before")
    @classmethod
    def normalize_chat_type(cls, v: Any) -> str:
        """Normalize chat type."""
        if not v:
            return "chat"
        
        type_str = str(v).lower()
        
        # Map various types to standard values
        type_map = {
            "channel": "channel",
            "megagroup": "supergroup",
            "gigagroup": "supergroup",
            "chat": "chat",
            "user": "private",
            "private": "private",
        }
        
        return type_map.get(type_str, type_str)
    
    @field_validator("title", "description", mode="before")
    @classmethod
    def clean_text_fields(cls, v: Any) -> str:
        """Clean text fields."""
        if v:
            return CommonValidators.clean_text(str(v))
        return ""


class MessageMediaData(BaseSchema):
    """Model for message media data."""
    
    model_config = ConfigDict(populate_by_name=True)
    
    type: str
    id: int | None = None
    access_hash: int | None = None
    file_reference: str | bytes | None = None
    mime_type: str | None = None
    size: int | None = None
    attributes: dict[str, Any] = Field(default_factory=dict)
    caption: str | None = None
    
    # Photo specific
    width: int | None = None
    height: int | None = None
    
    # Video/Audio specific
    duration: int | None = None
    
    # Document specific
    file_name: str | None = None
    
    @field_validator("file_reference", mode="before")
    @classmethod
    def handle_file_reference(cls, v: Any) -> str | None:
        """Convert file reference to string if bytes."""
        if isinstance(v, bytes):
            return v.hex()
        return str(v) if v is not None else None
    
    @field_validator("duration", mode="before")
    @classmethod
    def parse_duration(cls, v: Any) -> int | None:
        """Parse duration to seconds."""
        return CommonValidators.safe_int_conversion(v) if v is not None else None


class MessageEntityData(BaseSchema):
    """Model for message entity data (mentions, hashtags, links, etc)."""
    
    model_config = ConfigDict(populate_by_name=True)
    
    type: str
    offset: int
    length: int
    url: str | None = None
    user_id: int | None = None
    language: str | None = None
    
    @field_validator("type", mode="before")
    @classmethod
    def normalize_entity_type(cls, v: Any) -> str:
        """Normalize entity type."""
        if not v:
            return "unknown"
        
        type_str = str(v).lower()
        
        # Remove 'messageentity' prefix if present
        if type_str.startswith("messageentity"):
            type_str = type_str[13:]
        
        return type_str


class MessageActionData(BaseSchema):
    """Model for message action data (user joined, left, etc)."""
    
    model_config = ConfigDict(populate_by_name=True)
    
    type: str
    users: list[int] = Field(default_factory=list)
    title: str | None = None
    photo: dict[str, Any] | None = None
    ttl_seconds: int | None = None
    
    @field_validator("type", mode="before")
    @classmethod
    def normalize_action_type(cls, v: Any) -> str:
        """Normalize action type."""
        if not v:
            return "unknown"
        
        type_str = str(v).lower()
        
        # Remove 'messageaction' prefix if present
        if type_str.startswith("messageaction"):
            type_str = type_str[13:]
        
        return type_str


class MessageForwardData(BaseSchema):
    """Model for forwarded message data."""
    
    model_config = ConfigDict(populate_by_name=True)
    
    from_id: int | None = None
    from_name: str | None = None
    channel_post: int | None = None
    post_author: str | None = None
    date: datetime | None = None
    
    @field_validator("date", mode="before")
    @classmethod
    def parse_timestamp(cls, v: Any) -> datetime | None:
        """Parse timestamp."""
        return CommonValidators.parse_timestamp(v)


class TelegramMessageData(ContentSchema, ValidationHelperMixin):
    """Model for Telegram message data."""
    
    model_config = ConfigDict(
        populate_by_name=True,
        extra="allow"  # Allow extra fields for backward compatibility
    )
    
    # Основные поля (наследуются от ContentSchema)
    content_id: str = Field(..., alias="id")  # message_id as string for base class compatibility
    external_id: str = Field(..., alias="id")  # Дублируем для обратной совместимости
    message_id: int = Field(..., alias="id")  # Keep original int field for backward compatibility
    text: str = Field(default="", alias="message")
    date_posted: datetime = Field(..., alias="date")
    
    # Override media field from base class to exclude it
    media: list[MediaSchema] = Field(default_factory=list, exclude=True)
    
    # Telegram специфичные поля
    from_id: int | None = None
    telegram_media: MessageMediaData | None = None  # Rename to avoid conflict with base class
    is_reply: bool = Field(default=False)
    reply_to_msg_id: int | None = None
    is_forward: bool = Field(default=False)
    forward: MessageForwardData | None = None
    is_edited: bool = Field(default=False, alias="edit_hide")
    edit_date: datetime | None = None
    views_count: int | None = Field(default=None, alias="views")
    forwards: int | None = None
    is_mentioned: bool = Field(default=False, alias="mentioned")
    is_silent: bool = Field(default=False, alias="silent")
    is_post: bool = Field(default=False, alias="post")
    is_pinned: bool = Field(default=False, alias="pinned")
    
    # Additional message components
    entities: list[MessageEntityData] = Field(default_factory=list)
    action: MessageActionData | None = None
    via_bot_id: int | None = None
    
    @field_validator("content_id", "external_id")
    @classmethod
    def validate_content_external_id(cls, v: str) -> str:
        """Validate content/external ID as string."""
        # Convert to int for validation, then back to string
        msg_id = int(v)
        if msg_id <= 0:
            raise ValueError(f"Invalid message ID: {msg_id}")
        return str(msg_id)
    
    @field_validator("from_id")
    @classmethod
    def validate_from_id(cls, v: int | None) -> int | None:
        """Validate from_id."""
        if v is not None:
            return TelegramValidators.validate_user_id(v)
        return None
    
    @field_validator("via_bot_id")
    @classmethod
    def validate_via_bot_id(cls, v: int | None) -> int | None:
        """Validate via_bot_id."""
        if v is not None:
            return TelegramValidators.validate_user_id(v)
        return None
    
    @field_validator("entities", mode="before")
    @classmethod
    def parse_entities(cls, v: Any) -> list[dict[str, Any]]:
        """Parse entities from various formats."""
        if not v:
            return []
        
        # If it's already a list, return it
        if isinstance(v, list):
            return v
        
        # If it's a dict with specific entity types
        if isinstance(v, dict):
            entities_list = []
            
            # Extract mentions
            if "mentions" in v and isinstance(v["mentions"], list):
                entities_list.extend(v["mentions"])
            
            # Extract hashtags
            if "hashtags" in v and isinstance(v["hashtags"], list):
                entities_list.extend(v["hashtags"])
            
            # Extract URLs
            if "urls" in v and isinstance(v["urls"], list):
                entities_list.extend(v["urls"])
            
            return entities_list
        
        return []
    
    @model_validator(mode="before")
    @classmethod
    def handle_message_field_mappings(cls, data: dict[str, Any]) -> dict[str, Any]:
        """Handle different field name mappings for messages."""
        if isinstance(data, dict):
            # Map external_id -> id (for alias) if not present
            if "external_id" in data and "id" not in data:
                data["id"] = data["external_id"]
                
        return data
    
    @model_validator(mode="after")
    def extract_text_entities(self) -> "TelegramMessageData":
        """Extract hashtags, mentions, and URLs from entities."""
        for entity in self.entities:
            if entity.type == "hashtag" and self.text:
                # Extract hashtag text
                start = entity.offset
                end = entity.offset + entity.length
                hashtag = self.text[start:end].lstrip("#")
                if hashtag and hashtag not in self.hashtags:
                    self.hashtags.append(hashtag)
            
            elif entity.type == "mention" and self.text:
                # Extract mention text
                start = entity.offset
                end = entity.offset + entity.length
                mention = self.text[start:end].lstrip("@")
                if mention and mention not in self.mentions:
                    self.mentions.append(mention)
            
            elif entity.type in ["url", "text_link"]:
                # Add URL
                url = entity.url or ""
                if not url and self.text:
                    # Extract URL from text
                    start = entity.offset
                    end = entity.offset + entity.length
                    url = self.text[start:end]
                
                if url and url not in self.urls:
                    self.urls.append(url)
        
        return self
    
    def get_media(self) -> list[MediaSchema]:
        """Convert telegram_media to list of MediaSchema for base class compatibility."""
        if not self.telegram_media:
            return []
        # For now, return empty list as Telegram media format is different
        # This maintains base class compatibility
        return []
    
    @property
    def date(self) -> datetime:
        """Backward compatibility property."""
        return self.date_posted