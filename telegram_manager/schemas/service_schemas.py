"""
Pydantic схемы для валидации параметров сервисов Telegram
"""

from datetime import datetime

from pydantic import BaseModel, ConfigDict, field_validator


class ChatImportSchema(BaseModel):
    """Схема для импорта чата"""
    model_config = ConfigDict(str_strip_whitespace=True)
    
    chat_id: int | None = None
    username: str | None = None
    
    @field_validator("username")
    @classmethod
    def clean_username(cls, v: str | None) -> str | None:
        if v:
            return v.lstrip("@").strip()
        return v
    
    @field_validator("chat_id", "username")
    @classmethod
    def validate_at_least_one(cls, v, values):
        if not v and not values.get("username") and not values.get("chat_id"):
            raise ValueError("Either chat_id or username must be provided")
        return v


class MessageFilterSchema(BaseModel):
    """Схема для фильтрации сообщений"""
    
    chat_id: int | None = None
    user_id: int | None = None
    date_from: datetime | None = None
    date_to: datetime | None = None
    text_contains: str | None = None
    has_media: bool | None = None
    limit: int | None = 100
    
    @field_validator("limit")
    @classmethod
    def validate_limit(cls, v: int | None) -> int | None:
        if v is not None:
            if v <= 0:
                raise ValueError("Limit must be positive")
            if v > 10000:
                raise ValueError("Limit cannot exceed 10000")
        return v


class MessageImportSchema(BaseModel):
    """Схема для импорта сообщений"""
    model_config = ConfigDict(str_strip_whitespace=True)
    
    limit: int | None = None
    min_id: int | None = None
    max_id: int | None = None
    offset_date: datetime | None = None
    skip_user_fetch: bool = False
    
    @field_validator("limit")
    @classmethod
    def validate_limit(cls, v: int | None) -> int | None:
        if v is not None and v <= 0:
            raise ValueError("Limit must be positive")
        return v


class UserImportSchema(BaseModel):
    """Схема для импорта пользователей"""
    model_config = ConfigDict(str_strip_whitespace=True)
    
    user_ids: list[int] | None = None
    usernames: list[str] | None = None
    fetch_from_api: bool = True
    
    @field_validator("usernames")
    @classmethod
    def clean_usernames(cls, v: list[str] | None) -> list[str] | None:
        if v:
            return [u.lstrip("@").strip() for u in v]
        return v