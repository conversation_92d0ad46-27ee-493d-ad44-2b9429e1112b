"""Validation utilities for Telegram schemas.

This module contains Telegram-specific validation helpers.
Common validators have been moved to core.schemas.validators.
"""

import logging
from typing import Any, cast

from pydantic import BaseModel, ValidationError

# Импорт централизованных валидаторов для удобства использования
from core.schemas.validators import CommonValidators

# Pre-import for validation helpers
from telegram_manager.schemas.telegram import TelegramMessageData


def parse_telegram_user_id(user_data: Any) -> int | None:
    """Parse user ID from various Telegram formats."""
    if user_data is None:
        return None
    
    # Direct integer ID
    if isinstance(user_data, int):
        return user_data
    
    # Telethon PeerUser or similar
    if hasattr(user_data, "user_id"):
        return int(user_data.user_id) if user_data.user_id is not None else None
    
    # Dictionary format
    if isinstance(user_data, dict):
        id_val = user_data.get("user_id") or user_data.get("id")
        return int(id_val) if id_val is not None else None
    
    return None


def parse_telegram_chat_id(chat_data: Any) -> int | None:
    """Parse chat ID from various Telegram formats."""
    if chat_data is None:
        return None
    
    # Direct integer ID
    if isinstance(chat_data, int):
        return chat_data
    
    # Telethon PeerChat/PeerChannel
    if hasattr(chat_data, "chat_id"):
        return int(chat_data.chat_id) if chat_data.chat_id is not None else None
    elif hasattr(chat_data, "channel_id"):
        return int(chat_data.channel_id) if chat_data.channel_id is not None else None
    
    # Dictionary format
    if isinstance(chat_data, dict):
        id_val = (
            chat_data.get("chat_id") or 
            chat_data.get("channel_id") or 
            chat_data.get("id")
        )
        return int(id_val) if id_val is not None else None
    
    return None




def extract_message_text(message: Any) -> str:
    """Extract text from various message formats."""
    if hasattr(message, "message"):
        return CommonValidators.clean_text(message.message, preserve_newlines=True)
    elif hasattr(message, "text"):
        return CommonValidators.clean_text(message.text, preserve_newlines=True)
    elif isinstance(message, dict):
        return CommonValidators.clean_text(
            message.get("message") or 
            message.get("text") or 
            message.get("caption") or 
            "",
            preserve_newlines=True
        )
    
    return ""


class TelegramValidationHelpers:
    """Helper class for Telegram data validation."""
    
    @staticmethod
    def validate_message_batch(
        messages: list[dict[str, Any]], 
        skip_errors: bool = True
    ) -> list[BaseModel]:
        """Validate a batch of messages."""
        validated = []
        errors = []
        
        for idx, msg_data in enumerate(messages):
            try:
                # Ensure required fields
                if "id" not in msg_data and "message_id" in msg_data:
                    msg_data["id"] = msg_data["message_id"]
                
                if "message" not in msg_data and "text" in msg_data:
                    msg_data["message"] = msg_data["text"]
                
                # Parse message
                validated_msg = TelegramMessageData(**msg_data)
                validated.append(validated_msg)
                
            except ValidationError as e:
                if not skip_errors:
                    raise
                
                errors.append({
                    "index": idx,
                    "message_id": msg_data.get("id", "unknown"),
                    "error": str(e),
                    "data": msg_data
                })
        
        if errors:
            # Log errors
            logger = logging.getLogger(__name__)
            logger.warning(
                f"Validation errors for {len(errors)} out of {len(messages)} messages"
            )
        
        return cast(list[BaseModel], validated)
    
    @staticmethod
    def extract_user_info(entity: Any) -> dict[str, Any]:
        """Extract user information from Telethon entity."""
        user_data = {
            "id": getattr(entity, "id", None),
            "username": getattr(entity, "username", None),
            "first_name": getattr(entity, "first_name", ""),
            "last_name": getattr(entity, "last_name", ""),
            "phone": getattr(entity, "phone", None),
            "bot": getattr(entity, "bot", False),
            "verified": getattr(entity, "verified", False),
            "restricted": getattr(entity, "restricted", False),
            "scam": getattr(entity, "scam", False),
            "fake": getattr(entity, "fake", False),
            "premium": getattr(entity, "premium", False),
        }
        
        # Clean None values
        return {k: v for k, v in user_data.items() if v is not None}