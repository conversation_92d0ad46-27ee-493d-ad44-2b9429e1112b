include:
  - project: 'argo-media/infrastructure/ci-tools'
    ref: multibuild
    file: '_deploy_helm_chart.yaml'
  - project: 'argo-media/infrastructure/ci-tools'
    ref: gcp
    file: 'general_variables.yaml'
  - project: 'argo-media/infrastructure/ci-tools'
    ref: multibuild
    file: 'build_docker.yaml'
  - project: 'argo-media/infrastructure/ci-tools'
    ref: gcp
    file: 'push_image_to_gcp.yaml'
  - project: 'argo-media/infrastructure/ci-tools'
    ref: gcp
    file: 'test_deploy.yaml'
  - project: 'argo-media/infrastructure/ci-tools'
    ref: gcp
    file: 'production_deploy.yaml'

stages:
  - lint
  - test
  - build
  - push
  - deploy-test
  - production
  - rollback

cache:
  key: "$CI_COMMIT_REF_SLUG"

variables:
  GKE_CLUSTER_NAME: "ai-gke"
  GCP_DEPLOY_TEST: "true"
  GCP_DEPLOY_PROD: "true"
  GCP_NAMESPACE_TEST: "test"
  GCP_NAMESPACE_PROD: "prod"

  HELM_VALUES_FILE_TEST: "values/test.yaml"
  HELM_VALUES_FILE_PROD: "values/prod.yaml"
  HELM_SECRETS_FILE_TEST: "secrets/test.yaml"
  HELM_SECRETS_FILE_PROD: "secrets/prod.yaml"
  HELM_APP_NAME: "socialmanager"
  HELM_CHART_VERSION: "1.0.12"
  DOCKERFILES_DIR: "Dockerfiles"
