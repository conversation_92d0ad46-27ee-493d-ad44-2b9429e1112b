"""
Celery configuration for SocialManager project.
"""
import os

from celery import Celery

from core.logging.celery_handlers import setup_celery_logging

# Установка переменной окружения для настроек Django
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "SocialManager.settings")

# Создание экземпляра Celery
app = Celery("SocialManager")

# Загрузка настроек из Django settings с префиксом CELERY_
app.config_from_object("django.conf:settings", namespace="CELERY")

# Настройка логирования для Celery с использованием нашей централизованной конфигурации
from django.conf import settings  # noqa: E402

from core.logging.config import configure_celery_logging  # noqa: E402

# Настройка логирования Celery
configure_celery_logging(app, environment=getattr(settings, "ENVIRONMENT", "development"))

# Сохраняем старые обработчики сигналов для совместимости
setup_celery_logging()

# Автоматическое обнаружение и регистрация задач из файлов tasks.py
app.autodiscover_tasks()

# Настройка Sentry для Celery
try:
    from django.conf import settings
    if hasattr(settings, "SENTRY_DSN") and settings.SENTRY_DSN:
        import sentry_sdk
        
        # Sentry уже инициализирован в Django settings
        # Но нужно убедиться, что Celery интеграция добавлена
        if sentry_sdk.Hub.current.client:
            # Celery интеграция уже должна быть добавлена через SentryConfig
            pass
except ImportError:
    pass

@app.task(bind=True, ignore_result=True)
def debug_task(self):
    print(f"Request: {self.request!r}")