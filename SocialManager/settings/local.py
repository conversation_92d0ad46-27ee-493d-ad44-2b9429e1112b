"""
Local development settings for SocialManager project.

These settings are used for local development and override base settings.
"""

from .base import *  # noqa: F403, F405

from decouple import config

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = config(
    "SECRET_KEY",
    default="django-insecure-7$5n=)%#r7=9x7@ez9(2gtm=_p%85hyli12a1$=&o^em^#fu%^"
)

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = True

ALLOWED_HOSTS: list[str] = ["localhost", "127.0.0.1", "0.0.0.0"]


# Добавить поддержку ASGI
ASGI_APPLICATION = "SocialManager.asgi.application"

# Database
# PostgreSQL configuration (подключение к Docker контейнеру)
# ВАЖНО: Изменить хосты БД и Redis на localhost для локальной разработки
DATABASES = {
    "default": {
        "ENGINE": "django.db.backends.postgresql",
        "NAME": config("POSTGRES_DB", default="socialmanager"),
        "USER": config("POSTGRES_USER", default="postgres"),
        "PASSWORD": config("POSTGRES_PASSWORD", default="postgres"),
        "HOST": config("POSTGRES_HOST", default="localhost"),  # НЕ 'db', а localhost!
        "PORT": config("POSTGRES_PORT", default="5432"),
        "CONN_MAX_AGE": 0,  # Отключить persistent connections для async
        "OPTIONS": {
            "connect_timeout": 10,
        }
    }
}


# Email backend for local development (console output)
EMAIL_BACKEND = "django.core.mail.backends.console.EmailBackend"


# Static files (CSS, JavaScript, Images)
STATIC_ROOT = BASE_DIR / "staticfiles"  # noqa: F405
STATICFILES_DIRS = [
    BASE_DIR / "static",  # noqa: F405
]


# Logging configuration for local development
# The base configuration is already loaded, but we can customize it here
from core.logging.config import get_django_logging_config  # noqa: E402

# Set development environment
ENVIRONMENT = "development"
DJANGO_ENV = "development"

# Override with local development settings
LOGGING = get_django_logging_config(
    environment="development",
    log_dir=LOGS_DIR,  # noqa: F405
    enable_sentry=bool(config("SENTRY_DSN", default="")),
    sentry_dsn=config("SENTRY_DSN", default=""),
)


# Django Debug Toolbar (optional)
if config("ENABLE_DEBUG_TOOLBAR", default=False, cast=bool):
    INSTALLED_APPS += ["debug_toolbar"]  # noqa: F405
    MIDDLEWARE.insert(0, "debug_toolbar.middleware.DebugToolbarMiddleware")  # noqa: F405
    INTERNAL_IPS = ["127.0.0.1", "localhost"]
    
    DEBUG_TOOLBAR_CONFIG = {
        "SHOW_TOOLBAR_CALLBACK": lambda request: DEBUG,  # noqa: F405
    }


# Упрощенные лимиты для локальной разработки
INSTAGRAM_COMMENT_RATE_LIMITS = {
    "enabled": False,
    "comments_per_hour": 999999,
    "comments_per_day": 999999,
    "delay_between_comments": 0,
}


# Показывать SQL запросы в консоли (опционально)
if config("SHOW_SQL_QUERIES", default=False, cast=bool):
    LOGGING["loggers"]["django.db.backends"] = {
        "handlers": ["console"],
        "level": "DEBUG",
        "propagate": False,
    }


# Redis URL для кеша и Celery
REDIS_URL = config("REDIS_URL", default="redis://localhost:6379/0")

# Celery Configuration
CELERY_BROKER_URL = config("CELERY_BROKER_URL", default="redis://localhost:6379/0")
CELERY_RESULT_BACKEND = config("CELERY_RESULT_BACKEND", default="redis://localhost:6379/1")
CELERY_ACCEPT_CONTENT = ["json"]
CELERY_TASK_SERIALIZER = "json"
CELERY_RESULT_SERIALIZER = "json"
CELERY_TIMEZONE = TIME_ZONE  # noqa: F405

# Настройки надежности Redis
CELERY_TASK_ACKS_LATE = True
CELERY_TASK_REJECT_ON_WORKER_LOST = True
CELERY_BROKER_CONNECTION_RETRY_ON_STARTUP = True

CELERY_BROKER_TRANSPORT_OPTIONS = {
    "visibility_timeout": 3600,
    "fanout_prefix": True,
    "fanout_patterns": True,
}

# Периодические задачи (будут выполняться celery-beat)
CELERY_BEAT_SCHEDULE = {
    "clean-old-tasks-weekly": {
        "task": "core.tasks.maintenance.clean_old_tasks",
        "schedule": 60 * 60 * 24 * 7,  # Раз в неделю
        "args": (30,),  # удалять задачи старше 30 дней
    },
}

# Sentry configuration for local development
SENTRY_DSN = config("SENTRY_DSN", default="")
if SENTRY_DSN:
    from core.logging.sentry_config import get_sentry_config

    from .sentry import get_sentry_settings
    
    # Get environment-specific settings
    sentry_settings = get_sentry_settings("development")
    
    # Initialize Sentry with development configuration
    sentry_config = get_sentry_config("development")
    sentry_config.initialize(
        dsn=SENTRY_DSN,
        **sentry_settings
    )
    
    # Add middleware for request context enrichment
    MIDDLEWARE.insert(  # noqa: F405
        MIDDLEWARE.index("core.logging.middleware.LoggingContextMiddleware") + 1,  # noqa: F405
        "core.middleware.SentryContextMiddleware",
    )

print("=" * 50)
print("RUNNING WITH LOCAL SETTINGS")
print(f"DEBUG: {DEBUG}")  # noqa: F405
print("DATABASE: PostgreSQL (Docker, latest)")
print("CELERY: Redis broker (Docker, latest)")
if SENTRY_DSN:
    print("SENTRY: Enabled (development mode)")
else:
    print("SENTRY: Disabled (set SENTRY_DSN to enable)")
print("=" * 50)