"""
Centralized Sentry configuration settings.

This module provides Sentry-specific settings that can be imported
and used across different environment configurations.
"""

import os
from typing import Any


def get_sentry_settings(environment: str = "development") -> dict[str, Any]:
    """
    Get Sentry settings for the specified environment.
    
    Args:
        environment: Target environment (development/staging/production)
        
    Returns:
        Dictionary with Sentry configuration
    """
    # Base settings applicable to all environments
    base_settings: dict[str, Any] = {
        "attach_stacktrace": True,
        "send_default_pii": False,
        "max_breadcrumbs": 50,
        "auto_session_tracking": True,
    }
    
    # Environment-specific settings
    environment_settings: dict[str, dict[str, Any]] = {
        "development": {
            "debug": True,
            "traces_sample_rate": 1.0,
            "profiles_sample_rate": 1.0,
            "enable_tracing": True,
        },
        "staging": {
            "debug": False,
            "traces_sample_rate": 0.5,
            "profiles_sample_rate": 0.3,
            "enable_tracing": True,
        },
        "production": {
            "debug": False,
            "traces_sample_rate": 0.1,
            "profiles_sample_rate": 0.1,
            "enable_tracing": True,
        }
    }
    
    # Merge base and environment-specific settings
    settings: dict[str, Any] = base_settings.copy()
    settings.update(environment_settings.get(environment, {}))
    
    # Add environment tag
    settings["environment"] = environment
    
    # Configure ignored errors
    settings["ignore_errors"] = [
        # Django
        "django.core.exceptions.DisallowedHost",
        "django.core.exceptions.SuspiciousOperation",
        "django.core.exceptions.RequestAborted",
        "django.core.exceptions.PermissionDenied",
        
        # Common browser errors
        "KeyboardInterrupt",
        "SystemExit",
        
        # Specific app errors to ignore
        "instagram_manager.exceptions.RateLimitError",  # Handled by retry logic
        "telegram_manager.exceptions.FloodWaitError",   # Handled by backoff
    ]
    
    # Configure before_send_transaction to filter transactions
    def before_send_transaction(event, hint):
        """Filter out certain transactions."""
        transaction = event.get("transaction", "")
        ignore_patterns = [
            "/health/",
            "/static/",
            "/media/",
            "/favicon.ico",
            "/robots.txt",
        ]
        for pattern in ignore_patterns:
            if pattern in transaction:
                return None
        if transaction.startswith("OPTIONS "):
            return None
        return event
    
    settings["before_send_transaction"] = before_send_transaction
    
    # Performance monitoring settings are handled via traces_sampler
    
    # Profiling settings
    settings["profiles_sampler"] = lambda sampling_context: _profiles_sampler(
        sampling_context, environment
    )
    
    return settings


def _profiles_sampler(sampling_context: dict[str, Any], environment: str) -> float:
    """
    Dynamic profiling sampler based on transaction context.
    
    Args:
        sampling_context: Sampling context from Sentry
        environment: Current environment
        
    Returns:
        Profile sample rate
    """
    # Only profile transactions that are being traced
    if sampling_context.get("parent_sampled") is False:
        return 0.0
        
    transaction_name = sampling_context.get("transaction_context", {}).get("name", "")
    
    # High profiling for slow endpoints
    if any(endpoint in transaction_name for endpoint in ["/api/", "/admin/"]):
        return 0.5 if environment == "production" else 1.0
        
    # Low profiling for fast endpoints
    if any(pattern in transaction_name for pattern in ["/static/", "/media/", "/health/"]):
        return 0.0
        
    # Default based on environment
    defaults = {
        "development": 1.0,
        "staging": 0.3,
        "production": 0.1,
    }
    return defaults.get(environment, 0.1)


# Sentry release detection
def get_sentry_release() -> str:
    """
    Get release identifier for Sentry.
    
    Returns:
        Release string (git commit or version)
    """
    # Try environment variable first
    release = os.environ.get("SENTRY_RELEASE", "")
    if release:
        return release
        
    # Try to get from git
    try:
        import subprocess
        result = subprocess.run(
            ["git", "rev-parse", "HEAD"],
            capture_output=True,
            text=True,
            check=True,
            cwd=os.path.dirname(os.path.dirname(os.path.dirname(__file__))),
        )
        return f"socialmanager@{result.stdout.strip()[:7]}"
    except Exception:
        pass
        
    # Default to unknown
    return "socialmanager@unknown"


# Custom Sentry tags
def get_sentry_tags() -> dict[str, str]:
    """
    Get custom tags for Sentry events.
    
    Returns:
        Dictionary of tags
    """
    tags = {
        "app": "socialmanager",
        "component": "django",
    }
    
    # Add server hostname if available
    try:
        import socket
        tags["server_name"] = socket.gethostname()
    except Exception:
        pass
        
    # Add deployment info if available
    deployment = os.environ.get("DEPLOYMENT_ID", "")
    if deployment:
        tags["deployment"] = deployment
        
    return tags