"""
Test settings for SocialManager project.

These settings are used when running tests and override base settings.
"""

from .base import *  # noqa: F403, F405
from decouple import config


# Use in-memory database for tests
DATABASES = {
    "default": {
        "ENGINE": "django.db.backends.sqlite3",
        "NAME": ":memory:",
    }
}

# Speed up tests
SECRET_KEY = "test-secret-key-for-testing-only"
DEBUG = False
ALLOWED_HOSTS = ["testserver", "localhost", "127.0.0.1"]

# Enable migrations for tests to ensure proper database schema
# Note: This may slow down tests slightly, but ensures proper database setup

# Cache disabled for tests

# Email backend for testing (in-memory)
EMAIL_BACKEND = "django.core.mail.backends.locmem.EmailBackend"

# Disable external API calls in tests
BRIGHTDATA_API_TOKEN = "test-token"
BRIGHTDATA_BROWSER_URL = "ws://test-browser"

# Disable rate limiting in tests
INSTAGRAM_COMMENT_RATE_LIMITS = {
    "enabled": False,
    "comments_per_hour": 999999,
    "comments_per_day": 999999,
    "delay_between_comments": 0,
}

# Use temporary media directory for tests
MEDIA_ROOT = BASE_DIR / "test_media"  # noqa: F405
INSTAGRAM_MEDIA_ROOT = MEDIA_ROOT / "instagram"

# Disable auto-download in tests to avoid network calls
INSTAGRAM_AUTO_DOWNLOAD_MEDIA = False

# Static files
STATIC_ROOT = BASE_DIR / "test_staticfiles"  # noqa: F405

# Password hashers - use fast hasher for tests
PASSWORD_HASHERS = [
    "django.contrib.auth.hashers.MD5PasswordHasher",
]

# Set test environment
ENVIRONMENT = "test"
DJANGO_ENV = "test"

# Logging - reduce noise during tests
from core.logging.config import get_django_logging_config  # noqa: E402

# Use test-specific logging configuration
LOGGING = get_django_logging_config(
    environment="test",
    log_dir=BASE_DIR / "test_logs",  # noqa: F405
    enable_sentry=False,  # Explicitly disable Sentry for tests
    sentry_dsn="",
)

# Test-specific settings
TEST_RUNNER = "django.test.runner.DiscoverRunner"

# Disable monitoring middleware in tests for performance
MIDDLEWARE = [m for m in MIDDLEWARE if "monitoring" not in m.lower()]  # noqa: F405

# Use test encryption key
FIELD_ENCRYPTION_KEY = "test-encryption-key-32-chars-long!!"

# Majila test configuration
MAJILA_SERVICE_KEY = "test-majila-key"
MAJILA_BACKWARDS_URL = "http://test-majila.local"

# Test mode indicator
IS_TESTING = True

# Disable Sentry in tests
SENTRY_DSN = ""  # Explicitly disable Sentry for tests

# Celery configuration for tests
CELERY_TASK_ALWAYS_EAGER = True
CELERY_TASK_EAGER_PROPAGATES = True
CELERY_TASK_STORE_EAGER_RESULT = True  # Store results even in eager mode for tests

# MCP Server configuration for tests
MCP_HOST = config("MCP_HOST", default="localhost")
MCP_PORT = config("MCP_PORT", default=8001, cast=int)
MCP_BASE_PATH = config("MCP_BASE_PATH", default="/mcp")
MCP_URL = f"http://{MCP_HOST}:{MCP_PORT}{MCP_BASE_PATH}"

# If you need to test Sentry integration, set TEST_SENTRY_DSN in environment
TEST_SENTRY_DSN = config("TEST_SENTRY_DSN", default="")
if TEST_SENTRY_DSN:
    from core.logging.sentry_config import get_sentry_config

    from .sentry import get_sentry_settings
    
    # Get test-specific settings
    sentry_settings = get_sentry_settings("development")
    sentry_settings["debug"] = False  # Disable debug in tests
    sentry_settings["send_default_pii"] = False  # Never send PII in tests
    
    # Initialize Sentry with test configuration
    sentry_config = get_sentry_config("test")
    sentry_config.initialize(
        dsn=TEST_SENTRY_DSN,
        **sentry_settings
    )