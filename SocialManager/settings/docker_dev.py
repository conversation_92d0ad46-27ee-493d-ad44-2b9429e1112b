"""
Docker development settings for SocialManager project.

These settings are used when running in Docker development environment.
"""

from decouple import config

from .local import *  # noqa: F403, F405

# Override database host for Docker
DATABASES = {
    "default": {
        "ENGINE": "django.db.backends.postgresql",
        "NAME": config("POSTGRES_DB", default="socialmanager"),
        "USER": config("POSTGRES_USER", default="postgres"),
        "PASSWORD": config("POSTGRES_PASSWORD", default="postgres"),
        "HOST": config("POSTGRES_HOST", default="db"),  # Use 'db' for Docker
        "PORT": config("POSTGRES_PORT", default="5432"),
        "CONN_MAX_AGE": 0,
        "OPTIONS": {
            "connect_timeout": 10,
        },
    }
}

# Override Redis URL for Docker
REDIS_URL = config("REDIS_URL", default="redis://redis:6379/0")
CELERY_BROKER_URL = config("CELERY_BROKER_URL", default="redis://redis:6379/0")
CELERY_RESULT_BACKEND = config("CELERY_RESULT_BACKEND", default="redis://redis:6379/1")
ALLOWED_HOSTS = config("ALLOWED_HOSTS", default="*").split(",")
CSRF_TRUSTED_ORIGINS = ["https://*.maji.la", "https://*.ot4.dev"]

print("=" * 50)
print("RUNNING WITH DOCKER DEV SETTINGS")
print(f"DEBUG: {DEBUG}")  # noqa: F405
print(f"DATABASE HOST: {DATABASES['default']['HOST']}")
print(f"REDIS URL: {REDIS_URL}")
print("=" * 50)
