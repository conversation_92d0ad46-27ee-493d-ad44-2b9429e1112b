"""
Settings package for SocialManager project.

This package contains settings for different environments:
- base.py: Common settings for all environments
- local.py: Local development settings
- test.py: Test environment settings
- production.py: Production environment settings

The active settings module is determined by the DJANGO_SETTINGS_MODULE environment variable.
"""

import os
import sys

# Определение активного окружения
environment = os.environ.get("DJANGO_ENV", "local")

# Импорт настроек в зависимости от окружения
if "test" in sys.argv or "pytest" in sys.modules:
    # Используем тестовые настройки при запуске тестов
    from .test import *  # noqa: F403, F405  # type: ignore[assignment, misc, name-defined]
elif environment == "production":
    from .production import *  # noqa: F403, F405  # type: ignore[assignment, misc, name-defined]
elif environment == "local":
    from .local import *  # noqa: F403, F405  # type: ignore[assignment, misc, name-defined]
else:
    # По умолчанию используем локальные настройки
    from .local import *  # noqa: F403, F405  # type: ignore[assignment, misc, name-defined]