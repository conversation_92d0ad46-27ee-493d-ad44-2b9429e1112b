"""
Base Django settings for SocialManager project.

These settings are common to all environments (local, test, production).
Environment-specific settings should override these in their respective files.
"""

import os
from pathlib import Path

from decouple import config

from core.logging.django_config import configure_structlog

# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent.parent


# Application definition

INSTALLED_APPS = [
    "django.contrib.admin",
    "django.contrib.auth",
    "django.contrib.contenttypes",
    "django.contrib.sessions",
    "django.contrib.messages",
    "django.contrib.staticfiles",
    "django_celery_beat",
    "core.apps.CoreConfig",
    "telegram_manager.apps.TelegrammManagerConfig",
    "instagram_manager.apps.InstagramManagerConfig",
]

MIDDLEWARE = [
    "django.middleware.security.SecurityMiddleware",
    "core.logging.middleware.LoggingContextMiddleware",  # Добавлен для автоматического контекста запросов
    "django.contrib.sessions.middleware.SessionMiddleware",
    "django.middleware.common.CommonMiddleware",
    "django.middleware.csrf.CsrfViewMiddleware",
    "django.contrib.auth.middleware.AuthenticationMiddleware",
    "django.contrib.messages.middleware.MessageMiddleware",
    "django.middleware.clickjacking.XFrameOptionsMiddleware",
]

ROOT_URLCONF = "SocialManager.urls"

TEMPLATES = [
    {
        "BACKEND": "django.template.backends.django.DjangoTemplates",
        "DIRS": [BASE_DIR / "templates"],
        "APP_DIRS": True,
        "OPTIONS": {
            "context_processors": [
                "django.template.context_processors.debug",
                "django.template.context_processors.request",
                "django.contrib.auth.context_processors.auth",
                "django.contrib.messages.context_processors.messages",
                "django.template.context_processors.static",
                "django.template.context_processors.media",
                "django.template.context_processors.csrf",
                "django.template.context_processors.tz",
                "django.template.context_processors.i18n",
            ],
        },
    },
]

WSGI_APPLICATION = "SocialManager.wsgi.application"

# ASGI application
ASGI_APPLICATION = "SocialManager.asgi.application"

# Для async обработки файлов
FILE_UPLOAD_HANDLERS = [
    "django.core.files.uploadhandler.TemporaryFileUploadHandler",
]


# Password validation
# https://docs.djangoproject.com/en/5.2/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        "NAME": "django.contrib.auth.password_validation.UserAttributeSimilarityValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.MinimumLengthValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.CommonPasswordValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.NumericPasswordValidator",
    },
]


# Internationalization
# https://docs.djangoproject.com/en/5.2/topics/i18n/

LANGUAGE_CODE = "en-us"

TIME_ZONE = "UTC"

USE_I18N = True

USE_TZ = True


# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/5.2/howto/static-files/

STATIC_URL = "static/"

# Media files (Images, Documents, etc.)
MEDIA_URL = "/media/"
MEDIA_ROOT = BASE_DIR / "media"

# Default primary key field type
# https://docs.djangoproject.com/en/5.2/ref/settings/#default-auto-field

DEFAULT_AUTO_FIELD = "django.db.models.BigAutoField"


# BrightData API Configuration

BRIGHTDATA_API_TOKEN = config("BRIGHTDATA_API_TOKEN", default="")
BRIGHTDATA_API_BASE_URL = "https://api.brightdata.com/datasets/v3"

BRIGHTDATA_DATASETS = {
    "instagram_profile": "gd_l1vikfch901nx3by4",
    "instagram_posts": "gd_lk5ns7kz21pck8jpis",
    "instagram_comments": "gd_ltppn085pokosxh13",
    "instagram_reels": "gd_lyclm20il4r5helnj",
    "instagram_hashtag": "gd_lc80iuqzed8hjrqtm",
}

# Timeout for BrightData API requests (in seconds)
BRIGHTDATA_TIMEOUT = config("BRIGHTDATA_TIMEOUT", default=600, cast=int)

# Rate limiting
BRIGHTDATA_RATE_LIMIT = {
    "requests_per_minute": 60,
    "requests_per_hour": 1000,
    "retry_attempts": 3,
    "retry_delay": 5,  # seconds
}

# Instagram specific settings
INSTAGRAM_MEDIA_ROOT = MEDIA_ROOT / "instagram"
INSTAGRAM_CACHE_TIMEOUT = 3600  # 1 hour
INSTAGRAM_AUTO_DOWNLOAD_MEDIA = True  # Автоматически загружать медиафайлы при импорте постов

# BrightData Browser API
BRIGHTDATA_BROWSER_URL = config(
    "BRIGHTDATA_BROWSER_URL",
    default="wss://brd-customer-hl_66209a8b-zone-scraping_browser1:<EMAIL>:9222"
)

# Instagram comment rate limits (optional, can be disabled)
INSTAGRAM_COMMENT_RATE_LIMITS = {
    "enabled": config("INSTAGRAM_RATE_LIMITS_ENABLED", default=False, cast=bool),
    "comments_per_hour": config("INSTAGRAM_COMMENTS_PER_HOUR", default=999999, cast=int),
    "comments_per_day": config("INSTAGRAM_COMMENTS_PER_DAY", default=999999, cast=int),
    "delay_between_comments": config("INSTAGRAM_COMMENT_DELAY", default=0, cast=int),  # seconds
}

# Test configuration
INSTAGRAM_TEST_USERNAME = config("INSTAGRAM_TEST_USERNAME", default="testuser")

# Security - для шифрования паролей аккаунтов Instagram
FIELD_ENCRYPTION_KEY = config("FIELD_ENCRYPTION_KEY", default="")

# Google Cloud Storage Configuration
GCS_BUCKET_NAME = config("GCS_BUCKET_NAME", default="")
GOOGLE_APPLICATION_CREDENTIALS = config("GOOGLE_APPLICATION_CREDENTIALS", default="")

# Majila API Configuration
MAJILA_SERVICE_KEY = "KElHrqLOsUY0lohjxPajcQ"
MAJILA_BACKWARDS_URL = config("MAJILA_BACKWARDS_URL", default="")


# Logging configuration с поддержкой structlog
LOGS_DIR = BASE_DIR / "logs"
os.makedirs(LOGS_DIR, exist_ok=True)

# Настройка structlog
configure_structlog()

# Импортируем функцию для получения конфигурации логирования
from core.logging.config import get_django_logging_config  # noqa: E402

# Определение текущего окружения для Sentry
DJANGO_ENV = config("DJANGO_ENV", default="development")
ENVIRONMENT = DJANGO_ENV  # Alias for compatibility

# Log directory
LOG_DIR = LOGS_DIR

# Sentry DSN
SENTRY_DSN = config("SENTRY_DSN", default="")

# Устанавливаем конфигурацию логирования с поддержкой structlog
# Sentry будет включен только если задан SENTRY_DSN
LOGGING = get_django_logging_config(
    environment=DJANGO_ENV,
    log_dir=LOGS_DIR,
    enable_sentry=bool(SENTRY_DSN),
    sentry_dsn=SENTRY_DSN,
)

# Sentry SDK configuration
# Инициализация происходит в соответствующих файлах окружений
# с использованием core.logging.sentry_config.SentryConfig


# Instagram Import Settings
# Настройки импорта Instagram
INSTAGRAM_IMPORTS_ASYNC_BY_DEFAULT = True  # По умолчанию использовать Celery
INSTAGRAM_IMPORTS_FORCE_SYNC = config("INSTAGRAM_FORCE_SYNC", default=False, cast=bool)

# Дополнительные настройки Instagram
INSTAGRAM_CACHE_TIMEOUT = 60 * 60 * 24  # 24 часа по умолчанию
INSTAGRAM_COMMENT_RATE_LIMITS = {
    "enabled": True,
    "comments_per_hour": 60,
    "comments_per_day": 500,
    "delay_between_comments": 10,  # секунд
}


# Функция валидации обязательных переменных окружения
def validate_settings():
    """
    Проверка обязательных переменных окружения.
    Вызывается при старте приложения.
    """
    from core.utils.constants import Environment
    
    # Определяем текущее окружение
    env_name = config("DJANGO_ENV", default="development")
    try:
        current_env = Environment.from_string(env_name)
    except ValueError as e:
        raise ValueError(
            f"Invalid DJANGO_ENV value: '{env_name}'. {e}"
        )
    
    # Получаем обязательные переменные для текущего окружения
    required_vars = current_env.required_env_vars
    missing_vars = []
    
    # Проверяем каждую обязательную переменную
    for var in required_vars:
        if not config(var, default=""):
            missing_vars.append(var)
    
    # Дополнительные проверки для специфичных переменных
    if current_env.is_production:
        # Проверка ALLOWED_HOSTS в production
        allowed_hosts = config("ALLOWED_HOSTS", default="")
        if not allowed_hosts or allowed_hosts == "*":
            raise ValueError(
                "ALLOWED_HOSTS must be set to specific domains in production. "
                "Example: ALLOWED_HOSTS=example.com,www.example.com"
            )
        
        # Проверка DATABASE_URL
        db_url = config("DATABASE_URL", default="")
        if "sqlite" in db_url.lower():
            raise ValueError(
                "SQLite is not recommended for production. "
                "Please use PostgreSQL or MySQL."
            )
    
    if missing_vars:
        raise ValueError(
            f"Missing required environment variables for {current_env.value} environment:\n"
            f"{', '.join(missing_vars)}\n\n"
            f"Please set these variables in your .env file or environment.\n"
            f"See .env.{current_env.value} for examples."
        )


# Класс для управления настройками (для будущего расширения)
class Settings:
    """Базовый класс для управления настройками."""
    
    @classmethod
    def get_database_url(cls):
        """Получить URL базы данных из переменной окружения."""
        return config("DATABASE_URL", default="sqlite:///db.sqlite3")
    
    @classmethod
    def get_redis_url(cls):
        """Получить URL Redis из переменной окружения."""
        return config("REDIS_URL", default="redis://localhost:6379/0")