"""
Production settings for SocialManager project.

These settings are used in production environment and override base settings.
Security and performance are prioritized.
"""

from pathlib import Path
from typing import Any, cast

from decouple import Csv, config

from .base import *  # noqa: F403, F405

# Security settings
SECRET_KEY = config("SECRET_KEY")
DEBUG = False
ALLOWED_HOSTS = config("ALLOWED_HOSTS", cast=Csv())

# Force HTTPS
SECURE_SSL_REDIRECT = True
SESSION_COOKIE_SECURE = True
CSRF_COOKIE_SECURE = True
SECURE_BROWSER_XSS_FILTER = True
SECURE_CONTENT_TYPE_NOSNIFF = True
X_FRAME_OPTIONS = "DENY"

# HSTS settings
SECURE_HSTS_SECONDS = 31536000  # 1 year
SECURE_HSTS_INCLUDE_SUBDOMAINS = True
SECURE_HSTS_PRELOAD = True

# Database configuration (using DATABASE_URL)
try:
    import dj_database_url
    # dj_database_url.config returns DBConfig, but we need dict[str, Any]
    # Cast it to match the expected type
    DATABASES = {
        "default": cast(
            dict[str, Any],
            dj_database_url.config(
                default=config("DATABASE_URL", default="sqlite:///db.sqlite3"),
                conn_max_age=600,
                conn_health_checks=True,
            )
        )
    }
except ImportError:
    # Fallback to manual PostgreSQL configuration
    DATABASES = {
        "default": {
            "ENGINE": "django.db.backends.postgresql",
            "NAME": config("POSTGRES_DB", default="socialmanager"),
            "USER": config("POSTGRES_USER", default="postgres"),
            "PASSWORD": config("POSTGRES_PASSWORD", default="postgres"),
            "HOST": config("POSTGRES_HOST", default="db"),  # Use 'db' as default for Docker
            "PORT": config("POSTGRES_PORT", default="5432"),
            "CONN_MAX_AGE": 600,
            "OPTIONS": {
                "connect_timeout": 10,
            }
        }
    }

# Redis URL configuration (still needed for Celery)
REDIS_URL = config("REDIS_URL", default="redis://localhost:6379/0")

# Static files configuration
STATIC_ROOT = config("STATIC_ROOT", default="/var/www/static/")
STATICFILES_STORAGE = "django.contrib.staticfiles.storage.ManifestStaticFilesStorage"

# Media files
MEDIA_ROOT = config("MEDIA_ROOT", default="/var/www/media/")

# Email configuration removed - not used in the project

# Sentry error tracking with advanced configuration
SENTRY_DSN = config("SENTRY_DSN", default="")
if SENTRY_DSN:
    from core.logging.sentry_config import get_sentry_config

    from .sentry import get_sentry_settings
    
    # Get environment-specific settings
    sentry_settings = get_sentry_settings("production")
    
    # Initialize Sentry with advanced configuration
    sentry_config = get_sentry_config("production")
    sentry_config.initialize(
        dsn=SENTRY_DSN,
        **sentry_settings
    )
    
    # Add middleware for request context enrichment
    MIDDLEWARE.insert(  # noqa: F405
        MIDDLEWARE.index("core.logging.middleware.LoggingContextMiddleware") + 1,  # noqa: F405
        "core.middleware.SentryContextMiddleware",
    )

# Set production environment
ENVIRONMENT = "production"
DJANGO_ENV = "production"

# Production logging with JSON format
from core.logging.config import get_django_logging_config  # noqa: E402

# Override with production settings
LOGGING = get_django_logging_config(
    environment="production",
    log_dir=Path(config("LOG_DIR", default="/app/logs")),
    enable_sentry=bool(SENTRY_DSN),  # noqa: F405
    sentry_dsn=SENTRY_DSN,  # noqa: F405
)

# BrightData production settings (required)
BRIGHTDATA_API_TOKEN = config("BRIGHTDATA_API_TOKEN")
FIELD_ENCRYPTION_KEY = config("FIELD_ENCRYPTION_KEY")

# Rate limiting for production
INSTAGRAM_COMMENT_RATE_LIMITS = {
    "enabled": True,
    "comments_per_hour": config("INSTAGRAM_COMMENTS_PER_HOUR", default=60, cast=int),
    "comments_per_day": config("INSTAGRAM_COMMENTS_PER_DAY", default=200, cast=int),
    "delay_between_comments": config("INSTAGRAM_COMMENT_DELAY", default=30, cast=int),
}

# Performance optimizations
CONN_MAX_AGE = 60  # Keep database connections alive for 60 seconds

# Validate required settings
validate_settings()  # noqa: F405

print("=" * 50)
print("RUNNING WITH PRODUCTION SETTINGS")
print(f"ALLOWED_HOSTS: {ALLOWED_HOSTS}")  # noqa: F405
print(f"STATIC_ROOT: {STATIC_ROOT}")  # noqa: F405
print(f"Redis URL (for Celery): {REDIS_URL}")  # noqa: F405
print("=" * 50)