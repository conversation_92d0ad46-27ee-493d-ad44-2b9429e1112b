"""
URL configuration for SocialManager project.

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/5.2/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""

from django.conf import settings
from django.conf.urls.static import static
from django.contrib import admin
from django.http import HttpResponse
from django.urls import include, path, re_path
from django.views.decorators.csrf import csrf_exempt
from django.views.generic import RedirectView

from core.api.base import ping

# Custom error handlers
handler404 = "core.views.custom_page_not_found"


def favicon(request):
    """Return empty response for favicon.ico requests."""
    return HttpResponse(status=204)  # No Content


urlpatterns = [
    path("", RedirectView.as_view(url="/admin/", permanent=True), name="index"),
    path("favicon.ico", favicon, name="favicon"),
    path("admin/", admin.site.urls),
    path("telegram/", include("telegram_manager.urls", namespace="telegram_manager")),
    path(
        "instagram/", include("instagram_manager.urls", namespace="instagram_manager")
    ),
    path("core/", include("core.urls", namespace="core")),
    re_path(r"^-/ping$", csrf_exempt(ping), name="internal_ping"),
]

if settings.DEBUG:
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
    urlpatterns += static(settings.STATIC_URL, document_root=settings.STATIC_ROOT)