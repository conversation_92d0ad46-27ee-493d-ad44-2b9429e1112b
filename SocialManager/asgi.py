"""
ASGI config for SocialManager project.

It exposes the ASGI callable as a module-level variable named ``application``.

For more information on this file, see
https://docs.djangoproject.com/en/5.2/howto/deployment/asgi/
"""

import os
from typing import Any

from django.core.asgi import get_asgi_application

os.environ.setdefault("DJANGO_SETTINGS_MODULE", "SocialManager.settings")

# Get Django ASGI application
django_app = get_asgi_application()

async def application(scope: dict[str, Any], receive, send):
    """
    ASGI application wrapper that handles lifespan protocol.
    
    Django doesn't support ASGI lifespan events, but uvicorn sends them.
    This wrapper intercepts lifespan events and responds appropriately,
    while passing all other requests to Django.
    
    Fixes: SOCIAL-MANAGER-2
    """
    if scope["type"] == "lifespan":
        # Handle lifespan protocol
        while True:
            message = await receive()
            if message["type"] == "lifespan.startup":
                # Could add startup logic here in the future
                await send({"type": "lifespan.startup.complete"})
            elif message["type"] == "lifespan.shutdown":
                # Could add shutdown logic here in the future
                await send({"type": "lifespan.shutdown.complete"})
                break
    else:
        # Pass all other requests (HTTP, WebSocket) to Django
        await django_app(scope, receive, send)
