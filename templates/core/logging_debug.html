{% load logging_tags %}
{% if show_panel %}
<div id="logging-debug-panel" style="position: fixed; bottom: 0; right: 0; background: #f8f9fa; border: 1px solid #dee2e6; padding: 10px; margin: 10px; max-width: 400px; max-height: 300px; overflow: auto; z-index: 9999; font-family: monospace; font-size: 12px; box-shadow: 0 0 10px rgba(0,0,0,0.1);">
    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px; border-bottom: 1px solid #dee2e6; padding-bottom: 5px;">
        <strong style="color: #495057;">🔍 Logging Context Debug</strong>
        <button onclick="document.getElementById('logging-debug-panel').style.display='none'" style="background: none; border: none; cursor: pointer; font-size: 16px;">&times;</button>
    </div>
    
    {% if request_id %}
    <div style="margin-bottom: 8px;">
        <strong style="color: #6c757d;">Request ID:</strong>
        <code style="background: #e9ecef; padding: 2px 4px; border-radius: 3px;">{{ request_id|format_request_id }}</code>
    </div>
    {% endif %}
    
    {% if user_context %}
    <div style="margin-bottom: 8px;">
        <strong style="color: #6c757d;">User Context:</strong>
        <div style="margin-left: 10px;">
            {% if user_context.id %}
                <div>ID: <code style="background: #e9ecef; padding: 2px 4px; border-radius: 3px;">{{ user_context.id }}</code></div>
            {% endif %}
            {% if user_context.username %}
                <div>Username: <code style="background: #e9ecef; padding: 2px 4px; border-radius: 3px;">{{ user_context.username }}</code></div>
            {% endif %}
            {% if user_context.is_staff %}
                <div style="color: #28a745;">✓ Staff User</div>
            {% endif %}
            {% if user_context.is_superuser %}
                <div style="color: #dc3545;">✓ Superuser</div>
            {% endif %}
        </div>
    </div>
    {% endif %}
    
    {% if request_info %}
    <div style="margin-bottom: 8px;">
        <strong style="color: #6c757d;">Request Info:</strong>
        <div style="margin-left: 10px;">
            <div>Method: <code style="background: #e9ecef; padding: 2px 4px; border-radius: 3px;">{{ request_info.method }}</code></div>
            <div>Path: <code style="background: #e9ecef; padding: 2px 4px; border-radius: 3px;">{{ request_info.path }}</code></div>
            {% if request_info.is_ajax %}
                <div style="color: #17a2b8;">✓ AJAX Request</div>
            {% endif %}
        </div>
    </div>
    {% endif %}
    
    {% if logging_context %}
    <div style="margin-bottom: 8px;">
        <strong style="color: #6c757d;">Logging Context:</strong>
        <div style="margin-left: 10px; max-height: 100px; overflow-y: auto;">
            {% for key, value in logging_context.items %}
                <div>{{ key }}: <code style="background: #e9ecef; padding: 2px 4px; border-radius: 3px;">{{ value }}</code></div>
            {% endfor %}
        </div>
    </div>
    {% endif %}
    
    <div style="margin-top: 10px; padding-top: 5px; border-top: 1px solid #dee2e6;">
        <small style="color: #6c757d;">
            Press ESC to hide • 
            <a href="#" onclick="localStorage.setItem('logging-debug-panel-hidden', 'true'); document.getElementById('logging-debug-panel').style.display='none'; return false;" style="color: #007bff;">Disable</a>
        </small>
    </div>
</div>

<script>
(function() {
    // Check if panel was previously hidden
    if (localStorage.getItem('logging-debug-panel-hidden') === 'true') {
        document.getElementById('logging-debug-panel').style.display = 'none';
    }
    
    // ESC key handler
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            var panel = document.getElementById('logging-debug-panel');
            if (panel) {
                panel.style.display = 'none';
            }
        }
    });
    
    // Make panel draggable
    var panel = document.getElementById('logging-debug-panel');
    var isDragging = false;
    var currentX;
    var currentY;
    var initialX;
    var initialY;
    var xOffset = 0;
    var yOffset = 0;
    
    panel.addEventListener('mousedown', dragStart);
    document.addEventListener('mousemove', drag);
    document.addEventListener('mouseup', dragEnd);
    
    function dragStart(e) {
        if (e.target.tagName === 'BUTTON' || e.target.tagName === 'A' || e.target.tagName === 'CODE') {
            return;
        }
        
        initialX = e.clientX - xOffset;
        initialY = e.clientY - yOffset;
        
        if (e.target === panel || panel.contains(e.target)) {
            isDragging = true;
        }
    }
    
    function drag(e) {
        if (isDragging) {
            e.preventDefault();
            currentX = e.clientX - initialX;
            currentY = e.clientY - initialY;
            
            xOffset = currentX;
            yOffset = currentY;
            
            panel.style.transform = "translate(" + currentX + "px, " + currentY + "px)";
        }
    }
    
    function dragEnd(e) {
        initialX = currentX;
        initialY = currentY;
        isDragging = false;
    }
})();
</script>
{% endif %}