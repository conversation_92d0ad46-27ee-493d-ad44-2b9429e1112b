{% extends "admin/base_site.html" %}
{% load static %}

{% block title %}Task Queue Monitor{% endblock %}

{% block extrastyle %}
<style>
    .queue-section {
        background: #fff;
        padding: 20px;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        margin-bottom: 30px;
    }
    
    .queue-section h2 {
        margin-top: 0;
        color: #333;
        border-bottom: 2px solid #e9ecef;
        padding-bottom: 10px;
    }
    
    .queue-stats {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 20px;
        margin-bottom: 30px;
    }
    
    .stat-box {
        background: #f8f9fa;
        padding: 20px;
        border-radius: 8px;
        text-align: center;
        border: 1px solid #dee2e6;
    }
    
    .stat-box.pending {
        border-left: 4px solid #ffc107;
    }
    
    .stat-box.running {
        border-left: 4px solid #17a2b8;
    }
    
    .stat-box.retry {
        border-left: 4px solid #fd7e14;
    }
    
    .stat-number {
        font-size: 3em;
        font-weight: bold;
        margin: 10px 0;
    }
    
    .stat-label {
        color: #6c757d;
        font-size: 1.1em;
    }
    
    .task-list {
        list-style: none;
        padding: 0;
        margin: 0;
    }
    
    .task-item {
        background: #f8f9fa;
        border: 1px solid #dee2e6;
        border-radius: 5px;
        padding: 15px;
        margin-bottom: 10px;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
    
    .task-item:hover {
        background: #e9ecef;
    }
    
    .task-info {
        flex-grow: 1;
    }
    
    .task-id {
        font-family: monospace;
        font-size: 0.9em;
        color: #6c757d;
    }
    
    .task-type {
        font-weight: bold;
        margin: 5px 0;
    }
    
    .task-meta {
        font-size: 0.9em;
        color: #6c757d;
    }
    
    .task-status {
        padding: 3px 10px;
        border-radius: 15px;
        font-size: 12px;
        font-weight: bold;
        text-transform: uppercase;
    }
    
    .status-pending { background-color: #ffc107; color: #000; }
    .status-running { background-color: #17a2b8; color: #fff; }
    .status-success { background-color: #28a745; color: #fff; }
    .status-failed { background-color: #dc3545; color: #fff; }
    .status-retry { background-color: #fd7e14; color: #fff; }
    
    .priority-high {
        border-left: 3px solid #dc3545;
    }
    
    .empty-state {
        text-align: center;
        padding: 40px;
        color: #6c757d;
    }
    
    .empty-state i {
        font-size: 3em;
        margin-bottom: 10px;
        display: block;
    }
    
    .progress-mini {
        width: 150px;
        height: 15px;
        background: #e9ecef;
        border-radius: 10px;
        overflow: hidden;
        margin: 5px 0;
    }
    
    .progress-mini .fill {
        height: 100%;
        background: #17a2b8;
    }
    
    .auto-refresh-notice {
        background: #e3f2fd;
        color: #1565c0;
        padding: 10px 15px;
        border-radius: 5px;
        margin-bottom: 20px;
        display: flex;
        align-items: center;
        justify-content: space-between;
    }
</style>
{% endblock %}

{% block content %}
<h1>Task Queue Monitor</h1>

<div class="auto-refresh-notice">
    <span>⟳ This page auto-refreshes every 5 seconds</span>
    <a href="{% url 'core:task-list' %}" class="button small">View All Tasks</a>
</div>

<!-- Queue Statistics -->
<div class="queue-stats">
    <div class="stat-box pending">
        <div class="stat-label">Pending Tasks</div>
        <div class="stat-number">{{ queue_stats.pending_count }}</div>
    </div>
    <div class="stat-box running">
        <div class="stat-label">Running Tasks</div>
        <div class="stat-number">{{ queue_stats.running_count }}</div>
    </div>
    <div class="stat-box retry">
        <div class="stat-label">Retry Queue</div>
        <div class="stat-number">{{ queue_stats.retry_count }}</div>
    </div>
</div>

<!-- Running Tasks -->
<div class="queue-section">
    <h2>Currently Running ({{ running_tasks|length }})</h2>
    {% if running_tasks %}
        <ul class="task-list">
            {% for task in running_tasks %}
            <li class="task-item">
                <div class="task-info">
                    <div class="task-id">{{ task.task_id }}</div>
                    <div class="task-type">{{ task.task_type }}</div>
                    <div class="task-meta">
                        Started: {{ task.started_at|timesince }} ago
                        {% if task.created_by %} • By: {{ task.created_by }}{% endif %}
                    </div>
                    {% if task.total_items > 0 %}
                    <div class="progress-mini">
                        <div class="fill" style="width: {{ task.progress_percentage }}%"></div>
                    </div>
                    <div class="task-meta">
                        {{ task.processed_items }}/{{ task.total_items }} items • {{ task.progress_message|default:"Processing..." }}
                    </div>
                    {% endif %}
                </div>
                <div>
                    <a href="{% url 'core:task-detail' task.task_id %}" class="button small">View</a>
                </div>
            </li>
            {% endfor %}
        </ul>
    {% else %}
        <div class="empty-state">
            <p>No tasks currently running</p>
        </div>
    {% endif %}
</div>

<!-- Pending Tasks -->
<div class="queue-section">
    <h2>Pending Tasks ({{ pending_tasks|length }})</h2>
    {% if pending_tasks %}
        <ul class="task-list">
            {% for task in pending_tasks %}
            <li class="task-item {% if task.priority > 5 %}priority-high{% endif %}">
                <div class="task-info">
                    <div class="task-id">{{ task.task_id }}</div>
                    <div class="task-type">{{ task.task_type }}</div>
                    <div class="task-meta">
                        Priority: {{ task.priority }} • Created: {{ task.created_at|timesince }} ago
                        {% if task.created_by %} • By: {{ task.created_by }}{% endif %}
                    </div>
                </div>
                <div>
                    <span class="task-status status-pending">Pending</span>
                    <a href="{% url 'core:task-detail' task.task_id %}" class="button small">View</a>
                </div>
            </li>
            {% endfor %}
        </ul>
    {% else %}
        <div class="empty-state">
            <p>No pending tasks in queue</p>
        </div>
    {% endif %}
</div>

<!-- Recent Completed Tasks -->
<div class="queue-section">
    <h2>Recently Completed</h2>
    {% if recent_completed %}
        <ul class="task-list">
            {% for task in recent_completed %}
            <li class="task-item">
                <div class="task-info">
                    <div class="task-id">{{ task.task_id }}</div>
                    <div class="task-type">{{ task.task_type }}</div>
                    <div class="task-meta">
                        Completed: {{ task.completed_at|timesince }} ago
                        {% if task.started_at and task.completed_at %}
                        • Duration: {{ task.completed_at|timeuntil:task.started_at }}
                        {% endif %}
                    </div>
                </div>
                <div>
                    <span class="task-status status-{{ task.status }}">{{ task.get_status_display }}</span>
                    <a href="{% url 'core:task-detail' task.task_id %}" class="button small">View</a>
                </div>
            </li>
            {% endfor %}
        </ul>
    {% else %}
        <div class="empty-state">
            <p>No recently completed tasks</p>
        </div>
    {% endif %}
</div>

<script>
// Auto-refresh page every 5 seconds
setTimeout(function() {
    window.location.reload();
}, 5000);
</script>

{% endblock %}