{% extends "admin/base_site.html" %}
{% load static %}

{% block title %}Task {{ task.task_id }}{% endblock %}

{% block extrastyle %}
<style>
    .task-detail {
        background: #fff;
        padding: 20px;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    
    .task-header {
        border-bottom: 1px solid #dee2e6;
        padding-bottom: 20px;
        margin-bottom: 20px;
    }
    
    .task-status {
        display: inline-block;
        padding: 5px 15px;
        border-radius: 20px;
        font-weight: bold;
        text-transform: uppercase;
        font-size: 14px;
    }
    
    .status-pending { background-color: #ffc107; color: #000; }
    .status-running { background-color: #17a2b8; color: #fff; }
    .status-success { background-color: #28a745; color: #fff; }
    .status-failed { background-color: #dc3545; color: #fff; }
    .status-retry { background-color: #fd7e14; color: #fff; }
    .status-cancelled { background-color: #6c757d; color: #fff; }
    
    .info-grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 20px;
        margin-bottom: 30px;
    }
    
    .info-section {
        background: #f8f9fa;
        padding: 15px;
        border-radius: 5px;
    }
    
    .info-section h3 {
        margin-top: 0;
        color: #495057;
        font-size: 1.1em;
    }
    
    .info-row {
        display: flex;
        justify-content: space-between;
        padding: 8px 0;
        border-bottom: 1px solid #e9ecef;
    }
    
    .info-row:last-child {
        border-bottom: none;
    }
    
    .info-label {
        font-weight: bold;
        color: #6c757d;
    }
    
    .info-value {
        color: #212529;
    }
    
    .progress-section {
        background: #f8f9fa;
        padding: 20px;
        border-radius: 5px;
        margin-bottom: 20px;
    }
    
    .progress-bar-large {
        width: 100%;
        height: 30px;
        background: #e9ecef;
        border-radius: 15px;
        overflow: hidden;
        position: relative;
        margin: 10px 0;
    }
    
    .progress-fill {
        height: 100%;
        background: #17a2b8;
        transition: width 0.3s ease;
    }
    
    .progress-text {
        position: absolute;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%);
        font-size: 14px;
        font-weight: bold;
        color: #333;
    }
    
    .parameters-section, .result-section, .error-section {
        background: #f8f9fa;
        padding: 15px;
        border-radius: 5px;
        margin-bottom: 20px;
    }
    
    .json-content {
        background: #fff;
        border: 1px solid #dee2e6;
        padding: 10px;
        border-radius: 3px;
        font-family: monospace;
        font-size: 12px;
        overflow-x: auto;
        white-space: pre-wrap;
    }
    
    .action-buttons {
        margin-top: 20px;
        display: flex;
        gap: 10px;
    }
    
    .subtasks-table {
        width: 100%;
        border-collapse: collapse;
        margin-top: 10px;
    }
    
    .subtasks-table th, .subtasks-table td {
        padding: 8px;
        text-align: left;
        border-bottom: 1px solid #dee2e6;
    }
    
    .subtasks-table th {
        background: #e9ecef;
        font-weight: bold;
    }
</style>
{% endblock %}

{% block content %}
<h1>Task Details</h1>

<div class="task-detail">
    <div class="task-header">
        <h2>{{ task.task_type }}</h2>
        <p><strong>Task ID:</strong> {{ task.task_id }}</p>
        <div class="task-status status-{{ task.status }}">
            {{ task.get_status_display }}
        </div>
    </div>
    
    <div class="info-grid">
        <div class="info-section">
            <h3>General Information</h3>
            <div class="info-row">
                <span class="info-label">Created:</span>
                <span class="info-value">{{ task.created_at|date:"Y-m-d H:i:s" }}</span>
            </div>
            <div class="info-row">
                <span class="info-label">Started:</span>
                <span class="info-value">{{ task.started_at|date:"Y-m-d H:i:s"|default:"Not started" }}</span>
            </div>
            <div class="info-row">
                <span class="info-label">Completed:</span>
                <span class="info-value">{{ task.completed_at|date:"Y-m-d H:i:s"|default:"Not completed" }}</span>
            </div>
            {% if execution_time %}
            <div class="info-row">
                <span class="info-label">Execution Time:</span>
                <span class="info-value">{{ execution_time|floatformat:2 }} seconds</span>
            </div>
            {% endif %}
            <div class="info-row">
                <span class="info-label">Created By:</span>
                <span class="info-value">{{ task.created_by|default:"System" }}</span>
            </div>
            <div class="info-row">
                <span class="info-label">Priority:</span>
                <span class="info-value">{{ task.priority }}</span>
            </div>
        </div>
        
        <div class="info-section">
            <h3>Execution Details</h3>
            <div class="info-row">
                <span class="info-label">Retry Count:</span>
                <span class="info-value">{{ task.retry_count }} / {{ task.max_retries }}</span>
            </div>
            {% if task.parent_task %}
            <div class="info-row">
                <span class="info-label">Parent Task:</span>
                <span class="info-value">
                    <a href="{% url 'core:task-detail' task.parent_task.task_id %}">
                        {{ task.parent_task.task_id|truncatechars:20 }}
                    </a>
                </span>
            </div>
            {% endif %}
            {% if task.channel_name %}
            <div class="info-row">
                <span class="info-label">Channel:</span>
                <span class="info-value">{{ task.channel_name }}</span>
            </div>
            {% endif %}
        </div>
    </div>
    
    {% if task.status == "running" or task.total_items > 0 %}
    <div class="progress-section">
        <h3>Progress</h3>
        <div class="progress-bar-large">
            <div class="progress-fill" style="width: {{ task.progress_percentage }}%"></div>
            <span class="progress-text">
                {% if task.total_items > 0 %}
                    {{ task.processed_items }} / {{ task.total_items }} ({{ task.progress_percentage|floatformat:1 }}%)
                {% else %}
                    {{ task.progress_percentage|floatformat:1 }}%
                {% endif %}
            </span>
        </div>
        {% if task.progress_message %}
        <p><strong>Status:</strong> {{ task.progress_message }}</p>
        {% endif %}
    </div>
    {% endif %}
    
    {% if task.parameters %}
    <div class="parameters-section">
        <h3>Parameters</h3>
        <div class="json-content">{{ task.parameters|pprint }}</div>
    </div>
    {% endif %}
    
    {% if task.result and task.status == "success" %}
    <div class="result-section">
        <h3>Result</h3>
        <div class="json-content">{{ task.result|pprint }}</div>
    </div>
    {% endif %}
    
    {% if task.error_message and task.status in "failed,cancelled" %}
    <div class="error-section">
        <h3>Error Details</h3>
        <div class="json-content" style="color: #dc3545;">{{ task.error_message }}</div>
    </div>
    {% endif %}
    
    {% if subtasks %}
    <div class="info-section">
        <h3>Subtasks</h3>
        <table class="subtasks-table">
            <thead>
                <tr>
                    <th>Task ID</th>
                    <th>Type</th>
                    <th>Status</th>
                    <th>Progress</th>
                    <th>Created</th>
                </tr>
            </thead>
            <tbody>
                {% for subtask in subtasks %}
                <tr>
                    <td>
                        <a href="{% url 'core:task-detail' subtask.task_id %}">
                            {{ subtask.task_id|truncatechars:20 }}
                        </a>
                    </td>
                    <td>{{ subtask.task_type }}</td>
                    <td>
                        <span class="task-status status-{{ subtask.status }}" style="font-size: 11px; padding: 2px 8px;">
                            {{ subtask.get_status_display }}
                        </span>
                    </td>
                    <td>
                        {% if subtask.total_items > 0 %}
                            {{ subtask.processed_items }}/{{ subtask.total_items }}
                        {% else %}
                            -
                        {% endif %}
                    </td>
                    <td>{{ subtask.created_at|date:"Y-m-d H:i" }}</td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
    {% endif %}
    
    <div class="action-buttons">
        <a href="{% url 'core:task-list' %}" class="button">Back to List</a>
        {% if task.status in "pending,running" %}
            <button class="button" onclick="cancelTask()">Cancel Task</button>
        {% endif %}
        {% if task.status in "failed,cancelled" %}
            <button class="button default" onclick="retryTask()">Retry Task</button>
        {% endif %}
        {% if task.status == "running" %}
            <button class="button" onclick="refreshProgress()">Refresh Progress</button>
        {% endif %}
    </div>
</div>

<script>
// Auto-refresh for running tasks
{% if task.status == "running" %}
setInterval(function() {
    refreshProgress();
}, 3000); // Refresh every 3 seconds
{% endif %}

function refreshProgress() {
    fetch("{% url 'core:task-progress' task.task_id %}")
        .then(response => response.json())
        .then(data => {
            // Update progress bar
            const progressFill = document.querySelector('.progress-fill');
            const progressText = document.querySelector('.progress-text');
            
            if (progressFill && data.progress !== undefined) {
                progressFill.style.width = data.progress + '%';
            }
            
            if (progressText && data.total_items > 0) {
                progressText.textContent = `${data.processed_items} / ${data.total_items} (${data.progress.toFixed(1)}%)`;
            }
            
            // Update status
            const statusElement = document.querySelector('.task-status');
            if (statusElement && data.status !== '{{ task.status }}') {
                // Reload page if status changed
                window.location.reload();
            }
            
            // Update progress message
            const progressMessage = document.querySelector('.progress-section p');
            if (progressMessage && data.progress_message) {
                progressMessage.innerHTML = `<strong>Status:</strong> ${data.progress_message}`;
            }
        })
        .catch(error => console.error('Error fetching progress:', error));
}

function cancelTask() {
    if (confirm('Are you sure you want to cancel this task?')) {
        fetch("{% url 'core:task-action' task.task_id %}", {
            method: 'POST',
            headers: {
                'X-CSRFToken': getCookie('csrftoken'),
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: 'action=cancel'
        })
        .then(response => response.json())
        .then(data => {
            if (data.status === 'success') {
                alert(data.message);
                window.location.reload();
            } else {
                alert('Error: ' + data.message);
            }
        })
        .catch(error => alert('Error: ' + error));
    }
}

function retryTask() {
    if (confirm('Are you sure you want to retry this task?')) {
        fetch("{% url 'core:task-action' task.task_id %}", {
            method: 'POST',
            headers: {
                'X-CSRFToken': getCookie('csrftoken'),
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: 'action=retry'
        })
        .then(response => response.json())
        .then(data => {
            if (data.status === 'success') {
                alert(data.message + '\nNew task ID: ' + data.new_task_id);
                window.location.href = "{% url 'core:task-detail' '00000000-0000-0000-0000-000000000000' %}".replace('00000000-0000-0000-0000-000000000000', data.new_task_id);
            } else {
                alert('Error: ' + data.message);
            }
        })
        .catch(error => alert('Error: ' + error));
    }
}

function getCookie(name) {
    let cookieValue = null;
    if (document.cookie && document.cookie !== '') {
        const cookies = document.cookie.split(';');
        for (let i = 0; i < cookies.length; i++) {
            const cookie = cookies[i].trim();
            if (cookie.substring(0, name.length + 1) === (name + '=')) {
                cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                break;
            }
        }
    }
    return cookieValue;
}
</script>

{% endblock %}