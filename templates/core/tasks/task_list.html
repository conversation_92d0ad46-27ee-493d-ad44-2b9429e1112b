{% extends "admin/base_site.html" %}
{% load static %}

{% block title %}Task Monitor{% endblock %}

{% block extrastyle %}
<style>
    .task-status {
        padding: 2px 8px;
        border-radius: 3px;
        font-size: 11px;
        font-weight: bold;
        text-transform: uppercase;
    }
    .status-pending { background-color: #ffc107; color: #000; }
    .status-running { background-color: #17a2b8; color: #fff; }
    .status-success { background-color: #28a745; color: #fff; }
    .status-failed { background-color: #dc3545; color: #fff; }
    .status-retry { background-color: #fd7e14; color: #fff; }
    .status-cancelled { background-color: #6c757d; color: #fff; }
    
    .task-stats {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 20px;
        margin-bottom: 30px;
    }
    
    .stat-card {
        background: #f8f9fa;
        padding: 20px;
        border-radius: 8px;
        text-align: center;
        border: 1px solid #dee2e6;
    }
    
    .stat-value {
        font-size: 2em;
        font-weight: bold;
        color: #333;
    }
    
    .stat-label {
        color: #6c757d;
        font-size: 0.9em;
        margin-top: 5px;
    }
    
    .filter-form {
        background: #f8f9fa;
        padding: 15px;
        border-radius: 5px;
        margin-bottom: 20px;
    }
    
    .filter-form input, .filter-form select {
        margin-right: 10px;
    }
    
    .task-table {
        width: 100%;
        border-collapse: collapse;
    }
    
    .task-table th {
        background: #e9ecef;
        padding: 10px;
        text-align: left;
        font-weight: bold;
    }
    
    .task-table td {
        padding: 10px;
        border-bottom: 1px solid #dee2e6;
    }
    
    .task-table tr:hover {
        background: #f8f9fa;
    }
    
    .progress-bar {
        width: 100%;
        height: 20px;
        background: #e9ecef;
        border-radius: 10px;
        overflow: hidden;
        position: relative;
    }
    
    .progress-fill {
        height: 100%;
        background: #17a2b8;
        transition: width 0.3s ease;
    }
    
    .progress-text {
        position: absolute;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%);
        font-size: 12px;
        font-weight: bold;
    }
</style>
{% endblock %}

{% block content %}
<h1>Task Monitor</h1>

<!-- Statistics Dashboard -->
<div class="task-stats">
    <div class="stat-card">
        <div class="stat-value">{{ stats.total_tasks }}</div>
        <div class="stat-label">Total Tasks</div>
    </div>
    <div class="stat-card">
        <div class="stat-value">{{ stats.pending_tasks }}</div>
        <div class="stat-label">Pending</div>
    </div>
    <div class="stat-card">
        <div class="stat-value">{{ stats.running_tasks }}</div>
        <div class="stat-label">Running</div>
    </div>
    <div class="stat-card">
        <div class="stat-value">{{ stats.completed_today }}</div>
        <div class="stat-label">Completed Today</div>
    </div>
    <div class="stat-card">
        <div class="stat-value">{{ stats.failed_today }}</div>
        <div class="stat-label">Failed Today</div>
    </div>
</div>

<!-- Filters -->
<div class="filter-form">
    <form method="get" action="">
        <label>Status:</label>
        <select name="status">
            <option value="">All</option>
            {% for value, label in status_choices %}
                <option value="{{ value }}" {% if current_status == value %}selected{% endif %}>{{ label }}</option>
            {% endfor %}
        </select>
        
        <label>Task Type:</label>
        <select name="task_type">
            <option value="">All</option>
            {% for task_type in task_types %}
                <option value="{{ task_type }}" {% if current_task_type == task_type %}selected{% endif %}>{{ task_type }}</option>
            {% endfor %}
        </select>
        
        <label>From:</label>
        <input type="date" name="date_from" value="{{ date_from }}">
        
        <label>To:</label>
        <input type="date" name="date_to" value="{{ date_to }}">
        
        <button type="submit" class="button">Filter</button>
        <a href="{% url 'core:task-list' %}" class="button">Clear</a>
        <a href="{% url 'core:task-queue' %}" class="button default">View Queue</a>
    </form>
</div>

<!-- Task List -->
<table class="task-table">
    <thead>
        <tr>
            <th>Task ID</th>
            <th>Type</th>
            <th>Status</th>
            <th>Progress</th>
            <th>Created</th>
            <th>Started</th>
            <th>Completed</th>
            <th>Created By</th>
            <th>Actions</th>
        </tr>
    </thead>
    <tbody>
        {% for task in tasks %}
        <tr>
            <td>
                <a href="{% url 'core:task-detail' task.task_id %}">
                    {{ task.task_id|truncatechars:12 }}
                </a>
            </td>
            <td>{{ task.task_type }}</td>
            <td>
                <span class="task-status status-{{ task.status }}">
                    {{ task.get_status_display }}
                </span>
            </td>
            <td>
                {% if task.total_items > 0 %}
                <div class="progress-bar">
                    <div class="progress-fill" style="width: {{ task.progress_percentage }}%"></div>
                    <span class="progress-text">{{ task.processed_items }}/{{ task.total_items }}</span>
                </div>
                {% else %}
                -
                {% endif %}
            </td>
            <td>{{ task.created_at|date:"Y-m-d H:i" }}</td>
            <td>{{ task.started_at|date:"Y-m-d H:i"|default:"-" }}</td>
            <td>{{ task.completed_at|date:"Y-m-d H:i"|default:"-" }}</td>
            <td>{{ task.created_by|default:"System" }}</td>
            <td>
                <a href="{% url 'core:task-detail' task.task_id %}" class="button small">View</a>
            </td>
        </tr>
        {% empty %}
        <tr>
            <td colspan="9" style="text-align: center; padding: 40px;">
                No tasks found matching your criteria.
            </td>
        </tr>
        {% endfor %}
    </tbody>
</table>

<!-- Pagination -->
{% if page_obj.has_other_pages %}
<div class="pagination" style="margin-top: 20px; text-align: center;">
    <span class="page-links">
        {% if page_obj.has_previous %}
            <a href="?page=1">« first</a>
            <a href="?page={{ page_obj.previous_page_number }}">previous</a>
        {% endif %}
        
        <span class="current">
            Page {{ page_obj.number }} of {{ page_obj.paginator.num_pages }}
        </span>
        
        {% if page_obj.has_next %}
            <a href="?page={{ page_obj.next_page_number }}">next</a>
            <a href="?page={{ page_obj.paginator.num_pages }}">last »</a>
        {% endif %}
    </span>
</div>
{% endif %}

<!-- Auto-refresh for running tasks -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-refresh page if there are running tasks
    const runningTasks = document.querySelectorAll('.status-running');
    if (runningTasks.length > 0) {
        setTimeout(function() {
            window.location.reload();
        }, 5000); // Refresh every 5 seconds
    }
});
</script>

{% endblock %}