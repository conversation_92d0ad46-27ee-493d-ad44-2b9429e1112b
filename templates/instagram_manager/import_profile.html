{% extends "admin/base_site.html" %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block extra_css %}
<style>
    /* Modern styling */
    body {
        background-color: #f5f7fa;
    }
    
    /* Main container */
    .import-container {
        max-width: 800px;
        margin: 40px auto;
        padding-bottom: 50px;
    }
    
    /* Info box */
    .info-box {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 8px;
        padding: 20px;
        margin-bottom: 25px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        display: flex;
        align-items: center;
        gap: 15px;
    }
    
    .info-box .icon {
        font-size: 28px;
        opacity: 0.9;
    }
    
    .info-box p {
        margin: 0;
        font-size: 15px;
        line-height: 1.5;
    }
    
    /* Form groups */
    .form-group {
        background: white;
        border-radius: 12px;
        padding: 25px;
        margin-bottom: 20px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
        border: 1px solid #e1e8ed;
        transition: box-shadow 0.3s;
    }
    
    .form-group:hover {
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12);
    }
    
    .form-group h3 {
        margin: 0 0 20px 0;
        color: #2c3e50;
        font-size: 18px;
        font-weight: 600;
        display: flex;
        align-items: center;
        gap: 10px;
    }
    
    .form-group h3 .icon {
        color: #667eea;
        font-size: 20px;
    }
    
    /* Form rows */
    .form-row {
        margin-bottom: 18px;
    }
    
    .form-row:last-child {
        margin-bottom: 0;
    }
    
    .form-row label {
        font-weight: 500;
        color: #495057;
        margin-bottom: 6px;
        display: block;
        font-size: 14px;
    }
    
    /* Form controls */
    .form-control,
    input[type="text"] {
        width: 100%;
        max-width: 100%;
        box-sizing: border-box;
        padding: 10px 14px;
        border: 1px solid #ced4da;
        border-radius: 6px;
        font-size: 14px;
        transition: border-color 0.2s, box-shadow 0.2s;
        background-color: #fff;
    }
    
    .form-control:focus,
    input[type="text"]:focus {
        border-color: #667eea;
        outline: none;
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    }
    
    /* Checkbox styling */
    .checkbox-row {
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 12px 16px;
        background: #f8f9fa;
        border-radius: 6px;
        transition: background 0.2s;
        margin-bottom: 12px;
    }
    
    .checkbox-row:last-child {
        margin-bottom: 0;
    }
    
    .checkbox-row:hover {
        background: #e9ecef;
    }
    
    .checkbox-row input[type="checkbox"] {
        width: 18px;
        height: 18px;
        cursor: pointer;
    }
    
    .checkbox-row label {
        margin: 0;
        cursor: pointer;
        flex-grow: 1;
    }
    
    /* Help text */
    .helptext {
        font-size: 12px;
        color: #6c757d;
        margin-top: 4px;
        line-height: 1.4;
    }
    
    /* Submit row */
    .submit-row {
        background: white;
        border-radius: 12px;
        padding: 25px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
        border: 1px solid #e1e8ed;
        display: flex;
        gap: 15px;
        align-items: center;
        justify-content: center;
    }
    
    .submit-row input[type="submit"],
    .submit-row button {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border: none;
        padding: 12px 40px;
        border-radius: 6px;
        font-size: 16px;
        font-weight: 500;
        cursor: pointer;
        transition: transform 0.2s, box-shadow 0.2s;
    }
    
    .submit-row input[type="submit"]:hover,
    .submit-row button:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
    }
    
    .submit-row .cancel-link {
        background: #6c757d;
        color: white;
        padding: 12px 30px;
        border-radius: 6px;
        text-decoration: none;
        font-size: 16px;
        transition: background 0.2s;
    }
    
    .submit-row .cancel-link:hover {
        background: #5a6268;
    }
    
    /* Error styling */
    .errors input {
        border-color: #dc3545;
    }
    
    .errorlist {
        color: #dc3545;
        font-size: 13px;
        margin: 5px 0 0 0;
        padding: 0;
        list-style: none;
    }
</style>
{% endblock %}

{% block content %}
<div class="import-container">
    
    <div class="info-box">
        <span class="icon">👤</span>
        <div>
            <p><strong>Import Instagram Profile</strong> allows you to import profile data, posts, and followers from Instagram.</p>
        </div>
    </div>
    
    <form method="post" novalidate>
        {% csrf_token %}
        
        <div class="form-group">
            <h3><span class="icon">📱</span> Profile Information</h3>
            
            <!-- Username -->
            <div class="form-row{% if form.username.errors %} errors{% endif %}">
                {{ form.username.label_tag }}
                {{ form.username }}
                {% if form.username.help_text %}
                    <p class="helptext">{{ form.username.help_text }}</p>
                {% endif %}
                {% if form.username.errors %}
                    {{ form.username.errors }}
                {% endif %}
            </div>
        </div>
        
        <div class="form-group">
            <h3><span class="icon">⚙️</span> Import Options</h3>
            
            <!-- Import Posts -->
            <div class="checkbox-row">
                {{ form.import_posts }}
                <label for="{{ form.import_posts.id_for_label }}">
                    {{ form.import_posts.label }}
                    {% if form.import_posts.help_text %}
                        <span class="helptext">- {{ form.import_posts.help_text }}</span>
                    {% endif %}
                </label>
            </div>
            
            <!-- Import Followers -->
            <div class="checkbox-row">
                {{ form.import_followers }}
                <label for="{{ form.import_followers.id_for_label }}">
                    {{ form.import_followers.label }}
                    {% if form.import_followers.help_text %}
                        <span class="helptext">- {{ form.import_followers.help_text }}</span>
                    {% endif %}
                </label>
            </div>
            
            <!-- Download Media -->
            <div class="checkbox-row">
                {{ form.download_media }}
                <label for="{{ form.download_media.id_for_label }}">
                    {{ form.download_media.label }}
                    {% if form.download_media.help_text %}
                        <span class="helptext">- {{ form.download_media.help_text }}</span>
                    {% endif %}
                </label>
            </div>
            
            {% for field in form %}
                {% if field.errors and field.name in 'import_posts,import_followers,download_media' %}
                    {{ field.errors }}
                {% endif %}
            {% endfor %}
        </div>
        
        <!-- Hidden field -->
        {% if form.skip_media_download %}
            {{ form.skip_media_download }}
        {% endif %}
        
        {% if form.non_field_errors %}
            <div class="form-group">
                {{ form.non_field_errors }}
            </div>
        {% endif %}
        
        <div class="submit-row">
            <button type="submit">🚀 Import Profile</button>
            <a href="{% url 'instagram_manager:profile_list' %}" class="cancel-link">Cancel</a>
        </div>
    </form>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Username field focus
        const usernameField = document.querySelector('input[name="username"]');
        if (usernameField) {
            usernameField.focus();
        }
        
        // Highlight active form group
        const formGroups = document.querySelectorAll('.form-group');
        formGroups.forEach(group => {
            const inputs = group.querySelectorAll('input');
            inputs.forEach(input => {
                input.addEventListener('focus', () => {
                    group.style.boxShadow = '0 4px 12px rgba(102, 126, 234, 0.15)';
                    group.style.borderColor = '#667eea';
                });
                input.addEventListener('blur', () => {
                    group.style.boxShadow = '';
                    group.style.borderColor = '';
                });
            });
        });
        
        // Add loading state to submit button
        const form = document.querySelector('form');
        const submitBtn = document.querySelector('button[type="submit"]');
        
        form.addEventListener('submit', function() {
            submitBtn.innerHTML = '⏳ Importing...';
            submitBtn.disabled = true;
            submitBtn.style.opacity = '0.7';
        });
    });
</script>
{% endblock %}