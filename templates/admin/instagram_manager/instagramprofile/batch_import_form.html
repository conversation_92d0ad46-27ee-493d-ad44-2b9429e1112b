{% extends "admin/base_site.html" %}
{% load static %}

{% block title %}{{ title }} | {{ site_title|default:_('Django site admin') }}{% endblock %}

{% block extrastyle %}
    {{ block.super }}
    <link rel="stylesheet" href="{% static 'css/forms/media-options.css' %}">
    <style>
        /* Modern styling with better organization */
        body {
            background-color: #f5f7fa;
        }
        
        /* Main container */
        .batch-import-container {
            max-width: 1400px;
            margin: 0 auto;
            padding-bottom: 50px;
        }
        
        /* Info box styling */
        .info-box {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 25px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            display: flex;
            align-items: center;
            gap: 15px;
        }
        
        .info-box .icon {
            font-size: 28px;
            opacity: 0.9;
        }
        
        .info-box p {
            margin: 0;
            font-size: 15px;
            line-height: 1.5;
        }
        
        .info-box strong {
            font-size: 16px;
        }
        
        /* Profiles list */
        .profiles-list {
            background: white;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 25px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
            border: 1px solid #e1e8ed;
        }
        
        .profiles-list h3 {
            margin: 0 0 15px 0;
            color: #2c3e50;
            font-size: 18px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .profiles-list .count-badge {
            background: #764ba2;
            color: white;
            padding: 2px 10px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: normal;
        }
        
        .profiles-list ul {
            list-style: none;
            padding: 0;
            margin: 0;
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
            gap: 10px;
        }
        
        .profiles-list li {
            background: #f8f9fa;
            padding: 8px 12px;
            border-radius: 6px;
            display: flex;
            align-items: center;
            gap: 8px;
            transition: background 0.2s;
        }
        
        .profiles-list li:hover {
            background: #e9ecef;
        }
        
        .profiles-list .username {
            font-weight: 600;
            color: #667eea;
            flex-grow: 1;
        }
        
        .profiles-list .verified {
            color: #1da1f2;
        }
        
        .profiles-list .followers {
            font-size: 12px;
            color: #6c757d;
        }
        
        /* Form grid layout */
        .form-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 25px;
            margin-bottom: 25px;
        }
        
        @media (max-width: 1024px) {
            .form-grid {
                grid-template-columns: 1fr;
            }
        }
        
        /* Form groups */
        .form-group {
            background: white;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
            border: 1px solid #e1e8ed;
            transition: box-shadow 0.3s;
        }
        
        .form-group:hover {
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12);
        }
        
        .form-group h3 {
            margin: 0 0 20px 0;
            color: #2c3e50;
            font-size: 18px;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .form-group h3 .icon {
            color: #667eea;
            font-size: 20px;
        }
        
        /* Form rows */
        .form-row {
            margin-bottom: 18px;
        }
        
        .form-row:last-child {
            margin-bottom: 0;
        }
        
        .form-row label {
            font-weight: 500;
            color: #495057;
            margin-bottom: 6px;
            display: block;
            font-size: 14px;
        }
        
        /* Form controls */
        .form-control,
        select,
        textarea,
        input[type="text"],
        input[type="number"],
        input[type="date"] {
            width: 100%;
            max-width: 100%;
            box-sizing: border-box;
            padding: 10px 14px;
            border: 1px solid #ced4da;
            border-radius: 6px;
            font-size: 14px;
            transition: border-color 0.2s, box-shadow 0.2s;
            background-color: #fff;
        }
        
        .form-control:focus,
        select:focus,
        textarea:focus,
        input[type="text"]:focus,
        input[type="number"]:focus,
        input[type="date"]:focus {
            border-color: #667eea;
            outline: none;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }
        
        textarea {
            min-height: 100px;
            max-width: 100%;
            resize: vertical;
            box-sizing: border-box;
        }
        
        /* Checkbox styling */
        .checkbox-group {
            display: flex;
            flex-direction: column;
            gap: 12px;
        }
        
        /* Multiple checkbox widget */
        ul[id$="_post_types"] {
            list-style: none;
            padding: 0;
            margin: 0;
            display: flex;
            flex-direction: column;
            gap: 8px;
        }
        
        ul[id$="_post_types"] li {
            background: #f8f9fa;
            padding: 8px 12px;
            border-radius: 6px;
            transition: background 0.2s;
        }
        
        ul[id$="_post_types"] li:hover {
            background: #e9ecef;
        }
        
        ul[id$="_post_types"] li label {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            cursor: pointer;
            margin: 0;
        }
        
        ul[id$="_post_types"] li input[type="checkbox"] {
            width: 18px;
            height: 18px;
            cursor: pointer;
        }
        
        .checkbox-row {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 8px 12px;
            background: #f8f9fa;
            border-radius: 6px;
            transition: background 0.2s;
        }
        
        .checkbox-row:hover {
            background: #e9ecef;
        }
        
        .checkbox-row input[type="checkbox"] {
            width: 18px;
            height: 18px;
            cursor: pointer;
        }
        
        .checkbox-row label {
            margin: 0;
            cursor: pointer;
            flex-grow: 1;
            word-wrap: break-word;
            white-space: normal;
        }
        
        /* Help text */
        .helptext {
            font-size: 12px;
            color: #6c757d;
            margin-top: 4px;
            line-height: 1.4;
        }
        
        /* Submit row */
        .submit-row {
            background: white;
            border-radius: 12px;
            padding: 25px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
            border: 1px solid #e1e8ed;
            display: flex;
            gap: 15px;
            align-items: center;
            justify-content: center;
        }
        
        .submit-row input[type="submit"] {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 40px;
            border-radius: 6px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            transition: transform 0.2s, box-shadow 0.2s;
        }
        
        .submit-row input[type="submit"]:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
        }
        
        .submit-row .cancel-link {
            background: #6c757d;
            color: white;
            padding: 12px 30px;
            border-radius: 6px;
            text-decoration: none;
            font-size: 16px;
            transition: background 0.2s;
        }
        
        .submit-row .cancel-link:hover {
            background: #5a6268;
        }
        
        /* Error styling */
        .errors input,
        .errors select,
        .errors textarea {
            border-color: #dc3545;
        }
        
        .errorlist {
            color: #dc3545;
            font-size: 13px;
            margin: 5px 0 0 0;
            padding: 0;
            list-style: none;
        }
        
        /* Full width group for execution mode */
        .form-group-full {
            grid-column: 1 / -1;
        }
        
        /* Compact mode for smaller sections */
        .compact-fields .form-row {
            margin-bottom: 12px;
        }
        
        /* Counter for textarea */
        .char-counter {
            text-align: right;
            font-size: 12px;
            color: #6c757d;
            margin-top: 4px;
        }
    </style>
{% endblock %}

{% block extrahead %}
    {{ block.super }}
    <script src="{% static 'js/forms/media-options.js' %}"></script>
{% endblock %}

{% block content %}
<div class="batch-import-container">
    <div class="info-box">
        <span class="icon">📦</span>
        <div>
            <p><strong>Batch Import Posts</strong> allows you to import posts for multiple Instagram profiles in a single operation using BrightData's batch processing capabilities.</p>
        </div>
    </div>
    
    {% if profiles %}
        <div class="profiles-list">
            <h3>
                Selected Profiles
                <span class="count-badge">{{ profiles|length }}</span>
            </h3>
            <ul>
                {% for profile in profiles %}
                    <li>
                        <span class="username">@{{ profile.username }}</span>
                        {% if profile.is_verified %}<span class="verified">✓</span>{% endif %}
                        <span class="followers">({{ profile.follower_count|default:0 }} followers)</span>
                    </li>
                {% endfor %}
            </ul>
        </div>
    {% endif %}
    
    <form method="post" action="{% url 'instagram_manager:admin_batch_import_posts_do_import' %}" novalidate>
        {% csrf_token %}
        
        <div class="form-grid">
            <!-- Left Column -->
            <div>
                {% if show_usernames_field %}
                <div class="form-group">
                    <h3><span class="icon">👥</span> Instagram Profiles</h3>
                    <div class="form-row{% if form.usernames.errors %} errors{% endif %}">
                        {{ form.usernames.label_tag }}
                        {{ form.usernames }}
                        <div class="char-counter" id="username-counter"></div>
                        {% if form.usernames.help_text %}
                            <p class="helptext">{{ form.usernames.help_text }}</p>
                        {% endif %}
                        {% if form.usernames.errors %}
                            {{ form.usernames.errors }}
                        {% endif %}
                    </div>
                </div>
                {% endif %}
                
                <div class="form-group">
                    <h3><span class="icon">⚙️</span> Import Settings</h3>
                    
                    <div class="form-row{% if form.posts_limit.errors %} errors{% endif %}">
                        {{ form.posts_limit.label_tag }}
                        {{ form.posts_limit }}
                        {% if form.posts_limit.help_text %}
                            <p class="helptext">{{ form.posts_limit.help_text }}</p>
                        {% endif %}
                        {% if form.posts_limit.errors %}
                            {{ form.posts_limit.errors }}
                        {% endif %}
                    </div>
                    
                    <div class="form-row{% if form.batch_size.errors %} errors{% endif %}">
                        {{ form.batch_size.label_tag }}
                        {{ form.batch_size }}
                        {% if form.batch_size.help_text %}
                            <p class="helptext">{{ form.batch_size.help_text }}</p>
                        {% endif %}
                        {% if form.batch_size.errors %}
                            {{ form.batch_size.errors }}
                        {% endif %}
                    </div>
                </div>
            </div>
            
            <!-- Right Column -->
            <div>
                <div class="form-group">
                    <h3><span class="icon">🔍</span> Filters</h3>
                    
                    <div class="compact-fields">
                        <div class="form-row{% if form.date_from.errors %} errors{% endif %}">
                            {{ form.date_from.label_tag }}
                            {{ form.date_from }}
                            {% if form.date_from.help_text %}
                                <p class="helptext">{{ form.date_from.help_text }}</p>
                            {% endif %}
                            {% if form.date_from.errors %}
                                {{ form.date_from.errors }}
                            {% endif %}
                        </div>
                        
                        <div class="form-row{% if form.date_to.errors %} errors{% endif %}">
                            {{ form.date_to.label_tag }}
                            {{ form.date_to }}
                            {% if form.date_to.help_text %}
                                <p class="helptext">{{ form.date_to.help_text }}</p>
                            {% endif %}
                            {% if form.date_to.errors %}
                                {{ form.date_to.errors }}
                            {% endif %}
                        </div>
                        
                        <div class="form-row{% if form.post_types.errors %} errors{% endif %}">
                            {{ form.post_types.label_tag }}
                            {{ form.post_types }}
                            {% if form.post_types.help_text %}
                                <p class="helptext">{{ form.post_types.help_text }}</p>
                            {% endif %}
                            {% if form.post_types.errors %}
                                {{ form.post_types.errors }}
                            {% endif %}
                        </div>
                    </div>
                </div>
                
                <div class="form-group">
                    <h3><span class="icon">✨</span> Options</h3>
                    
                    <div class="checkbox-group">
                        <div class="checkbox-row{% if form.import_comments.errors %} errors{% endif %}">
                            {{ form.import_comments }} 
                            <label for="{{ form.import_comments.id_for_label }}">
                                {{ form.import_comments.label }}
                                {% if form.import_comments.help_text %}
                                    <span class="helptext">- {{ form.import_comments.help_text }}</span>
                                {% endif %}
                            </label>
                        </div>
                        
                        <div class="checkbox-row{% if form.download_media.errors %} errors{% endif %}">
                            {{ form.download_media }} 
                            <label for="{{ form.download_media.id_for_label }}">
                                {{ form.download_media.label }}
                                {% if form.download_media.help_text %}
                                    <span class="helptext">- {{ form.download_media.help_text }}</span>
                                {% endif %}
                            </label>
                        </div>
                        
                        {% if form.save_media_to_gcs %}
                            <div class="checkbox-row field-save_media_to_gcs{% if form.save_media_to_gcs.errors %} errors{% endif %}">
                                {{ form.save_media_to_gcs }} 
                                <label for="{{ form.save_media_to_gcs.id_for_label }}">
                                    {{ form.save_media_to_gcs.label }}
                                    {% if form.save_media_to_gcs.help_text %}
                                        <span class="helptext">- {{ form.save_media_to_gcs.help_text }}</span>
                                    {% endif %}
                                </label>
                                {% if gcs_bucket_name %}
                                    <div class="bucket-info" style="margin-left: 26px; margin-top: 5px;">
                                        Bucket: {{ gcs_bucket_name }}
                                    </div>
                                {% endif %}
                            </div>
                        {% endif %}
                    </div>
                    
                    {% for field in form %}
                        {% if field.errors and field.name in 'import_comments,download_media,save_media_to_gcs' %}
                            {{ field.errors }}
                        {% endif %}
                    {% endfor %}
                </div>
            </div>
        </div>
        
        <!-- Full width execution mode -->
        <div class="form-group form-group-full">
            <h3><span class="icon">🚀</span> Execution Mode</h3>
            
            <div class="form-row{% if form.execution_mode.errors %} errors{% endif %}">
                {{ form.execution_mode.label_tag }}
                {{ form.execution_mode }}
                {% if form.execution_mode.help_text %}
                    <p class="helptext">{{ form.execution_mode.help_text }}</p>
                {% endif %}
                {% if form.execution_mode.errors %}
                    {{ form.execution_mode.errors }}
                {% endif %}
            </div>
        </div>
        
        <!-- Periodic task settings -->
        <div class="form-group form-group-full">
            <h3><span class="icon">⏰</span> Periodic Import</h3>
            
            <div class="checkbox-row{% if form.create_periodic.errors %} errors{% endif %}">
                {{ form.create_periodic }} 
                <label for="{{ form.create_periodic.id_for_label }}">
                    {{ form.create_periodic.label }}
                    {% if form.create_periodic.help_text %}
                        <span class="helptext">- {{ form.create_periodic.help_text }}</span>
                    {% endif %}
                </label>
            </div>
            
            <div id="periodic-settings" style="display: none; margin-top: 15px;">
                <div class="form-row{% if form.interval_seconds.errors %} errors{% endif %}">
                    {{ form.interval_seconds.label_tag }}
                    {{ form.interval_seconds }}
                    {% if form.interval_seconds.help_text %}
                        <p class="helptext">{{ form.interval_seconds.help_text }}</p>
                    {% endif %}
                    {% if form.interval_seconds.errors %}
                        {{ form.interval_seconds.errors }}
                    {% endif %}
                </div>
                
                <div class="info-box" style="margin-top: 15px; background: linear-gradient(135deg, #48c6ef 0%, #6f86d6 100%);">
                    <span class="icon">ℹ️</span>
                    <div>
                        <p><strong>Note:</strong> When creating a periodic task, the "Start date" will be used as the initial date. Each subsequent run will use the previous run's date as the new start date.</p>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Hidden fields -->
        {% if form.skip_media_download %}
            {{ form.skip_media_download }}
        {% endif %}
        
        {% if form.non_field_errors %}
            <div class="form-group form-group-full">
                {{ form.non_field_errors }}
            </div>
        {% endif %}
        
        <div class="submit-row">
            <input type="submit" value="🚀 Start Batch Import" class="default" />
            <a href="{% url 'admin:instagram_manager_instagramprofile_changelist' %}" class="button cancel-link">Cancel</a>
        </div>
    </form>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Auto-resize textarea and character counter
        const textarea = document.getElementById('id_usernames');
        const counter = document.getElementById('username-counter');
        
        if (textarea) {
            function adjustHeight() {
                textarea.style.height = 'auto';
                textarea.style.height = Math.min(textarea.scrollHeight, 300) + 'px';
            }
            
            function updateCounter() {
                const lines = textarea.value.trim().split('\n').filter(line => line.trim());
                counter.textContent = `${lines.length} username${lines.length !== 1 ? 's' : ''} entered`;
            }
            
            textarea.addEventListener('input', function() {
                adjustHeight();
                updateCounter();
            });
            
            adjustHeight();
            updateCounter();
        }
        
        // Highlight active form group
        const formGroups = document.querySelectorAll('.form-group');
        formGroups.forEach(group => {
            const inputs = group.querySelectorAll('input, select, textarea');
            inputs.forEach(input => {
                input.addEventListener('focus', () => {
                    group.style.boxShadow = '0 4px 12px rgba(102, 126, 234, 0.15)';
                    group.style.borderColor = '#667eea';
                });
                input.addEventListener('blur', () => {
                    group.style.boxShadow = '';
                    group.style.borderColor = '';
                });
            });
        });
        
        // Add loading state to submit button
        const form = document.querySelector('form');
        const submitBtn = document.querySelector('input[type="submit"]');
        
        form.addEventListener('submit', function() {
            submitBtn.value = '⏳ Processing...';
            submitBtn.disabled = true;
            submitBtn.style.opacity = '0.7';
        });
        
        // Toggle periodic settings visibility
        const periodicCheckbox = document.getElementById('id_create_periodic');
        const periodicSettings = document.getElementById('periodic-settings');
        const executionMode = document.getElementById('id_execution_mode');
        
        if (periodicCheckbox && periodicSettings) {
            function togglePeriodicSettings() {
                if (periodicCheckbox.checked) {
                    periodicSettings.style.display = 'block';
                    // Force async mode for periodic tasks
                    if (executionMode) {
                        executionMode.value = 'async';
                        executionMode.disabled = true;
                    }
                } else {
                    periodicSettings.style.display = 'none';
                    if (executionMode) {
                        executionMode.disabled = false;
                    }
                }
            }
            
            periodicCheckbox.addEventListener('change', togglePeriodicSettings);
            togglePeriodicSettings(); // Initial state
        }
    });
</script>
{% endblock %}