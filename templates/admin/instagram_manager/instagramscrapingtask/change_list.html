{% extends "admin/change_list.html" %}

{% block extrahead %}
{{ block.super }}
<style>
    /* Styles for collapsible messages */
    .messagelist li {
        position: relative;
        padding-right: 40px;
    }
    
    .message-content {
        white-space: pre-wrap;
        word-wrap: break-word;
    }
    
    .message-collapsed {
        max-height: 100px;
        overflow: hidden;
        position: relative;
    }
    
    .message-collapsed::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        height: 30px;
        background: linear-gradient(to bottom, transparent, rgba(255,255,255,0.9));
    }
    
    .message-toggle {
        position: absolute;
        right: 10px;
        top: 10px;
        background: #fff;
        border: 1px solid #ddd;
        padding: 4px 12px;
        cursor: pointer;
        font-size: 12px;
        border-radius: 3px;
        color: #0066cc;
    }
    
    .message-toggle:hover {
        background: #f5f5f5;
        border-color: #bbb;
    }
    
    .message-expanded {
        max-height: none;
        overflow: visible;
    }
    
    .message-expanded::after {
        display: none;
    }
    
    /* Hide toggle button for short messages */
    .message-short .message-toggle {
        display: none;
    }
</style>
{% endblock %}

{% block messages %}
{% if messages %}
<ul class="messagelist">
    {% for message in messages %}
    <li class="{% if message.level_tag %}{{ message.level_tag }}{% endif %}" data-message-id="msg-{{ forloop.counter }}">
        <div class="message-content">{{ message|safe }}</div>
        <button class="message-toggle" onclick="toggleMessage(this)">Show details</button>
    </li>
    {% endfor %}
</ul>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Process all messages
    const messages = document.querySelectorAll('.messagelist li');
    
    messages.forEach(function(msg) {
        const content = msg.querySelector('.message-content');
        const toggle = msg.querySelector('.message-toggle');
        
        // Check if message needs collapsing (more than 5 lines or 300 characters)
        const text = content.textContent;
        const lineCount = (text.match(/\n/g) || []).length + 1;
        const charCount = text.length;
        
        if (lineCount > 5 || charCount > 300) {
            // This is a long message, collapse it
            content.classList.add('message-collapsed');
        } else {
            // This is a short message, mark it
            msg.classList.add('message-short');
        }
    });
});

function toggleMessage(button) {
    const content = button.previousElementSibling;
    
    if (content.classList.contains('message-collapsed')) {
        content.classList.remove('message-collapsed');
        content.classList.add('message-expanded');
        button.textContent = 'Hide details';
    } else {
        content.classList.remove('message-expanded');
        content.classList.add('message-collapsed');
        button.textContent = 'Show details';
    }
}
</script>
{% endif %}
{% endblock %}

{% block object-tools-items %}
    {% if show_import_button %}
        <li>
            <a href="{% url 'admin:instagram_manager_instagramscrapingtask_import' %}" class="addlink">
                Start Import
            </a>
        </li>
    {% endif %}
    {% if show_batch_import_button %}
        <li>
            <a href="{% url 'admin:instagram_manager_instagramscrapingtask_batch_import' %}" class="addlink">
                Batch Import Posts
            </a>
        </li>
    {% endif %}
    {{ block.super }}
{% endblock %}