{% extends "admin/base_site.html" %}
{% load static %}

{% block title %}{{ title }} | {{ site_title|default:_('Django site admin') }}{% endblock %}

{% block extrastyle %}
    {{ block.super }}
    <style>
        /* Modern styling */
        body {
            background-color: #f5f7fa;
        }
        
        /* Main container */
        .import-container {
            max-width: 1200px;
            margin: 0 auto;
            padding-bottom: 50px;
        }
        
        /* Info box */
        .info-box {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 25px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            display: flex;
            align-items: center;
            gap: 15px;
        }
        
        .info-box .icon {
            font-size: 28px;
            opacity: 0.9;
        }
        
        .info-box p {
            margin: 0;
            font-size: 15px;
            line-height: 1.5;
        }
        
        /* Form groups */
        .form-group {
            background: white;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
            border: 1px solid #e1e8ed;
            transition: box-shadow 0.3s;
        }
        
        .form-group:hover {
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12);
        }
        
        .form-group h3 {
            margin: 0 0 20px 0;
            color: #2c3e50;
            font-size: 18px;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .form-group h3 .icon {
            color: #667eea;
            font-size: 20px;
        }
        
        /* Form rows */
        .form-row {
            margin-bottom: 18px;
        }
        
        .form-row:last-child {
            margin-bottom: 0;
        }
        
        .form-row label {
            font-weight: 500;
            color: #495057;
            margin-bottom: 6px;
            display: block;
            font-size: 14px;
        }
        
        /* Form controls */
        .form-control,
        select,
        textarea,
        input[type="text"],
        input[type="number"] {
            width: 100%;
            max-width: 100%;
            box-sizing: border-box;
            padding: 10px 14px;
            border: 1px solid #ced4da;
            border-radius: 6px;
            font-size: 14px;
            transition: border-color 0.2s, box-shadow 0.2s;
            background-color: #fff;
        }
        
        .form-control:focus,
        select:focus,
        textarea:focus,
        input[type="text"]:focus,
        input[type="number"]:focus {
            border-color: #667eea;
            outline: none;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }
        
        /* Radio buttons */
        #id_execution_mode {
            list-style: none;
            padding: 0;
            margin: 0;
            display: flex;
            flex-direction: column;
            gap: 12px;
        }
        
        #id_execution_mode li {
            background: #f8f9fa;
            padding: 12px 16px;
            border-radius: 6px;
            transition: background 0.2s;
            margin: 0;
        }
        
        #id_execution_mode li:hover {
            background: #e9ecef;
        }
        
        #id_execution_mode input[type="radio"] {
            margin-right: 8px;
            width: 18px;
            height: 18px;
            cursor: pointer;
        }
        
        #id_execution_mode label {
            font-weight: normal;
            display: inline;
            cursor: pointer;
            margin: 0;
        }
        
        /* Checkbox styling */
        .checkbox-row {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 12px 16px;
            background: #f8f9fa;
            border-radius: 6px;
            transition: background 0.2s;
        }
        
        .checkbox-row:hover {
            background: #e9ecef;
        }
        
        .checkbox-row input[type="checkbox"] {
            width: 18px;
            height: 18px;
            cursor: pointer;
        }
        
        .checkbox-row label {
            margin: 0;
            cursor: pointer;
            flex-grow: 1;
        }
        
        /* Post types checkboxes */
        ul[id$="_post_types"] {
            list-style: none;
            padding: 0;
            margin: 0;
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 10px;
        }
        
        ul[id$="_post_types"] li {
            background: #f8f9fa;
            padding: 10px 14px;
            border-radius: 6px;
            transition: background 0.2s;
        }
        
        ul[id$="_post_types"] li:hover {
            background: #e9ecef;
        }
        
        ul[id$="_post_types"] li label {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            cursor: pointer;
            margin: 0;
        }
        
        ul[id$="_post_types"] li input[type="checkbox"] {
            width: 18px;
            height: 18px;
            cursor: pointer;
        }
        
        /* Help text */
        .helptext {
            font-size: 12px;
            color: #6c757d;
            margin-top: 4px;
            line-height: 1.4;
        }
        
        /* Submit row */
        .submit-row {
            background: white;
            border-radius: 12px;
            padding: 25px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
            border: 1px solid #e1e8ed;
            display: flex;
            gap: 15px;
            align-items: center;
            justify-content: center;
        }
        
        .submit-row input[type="submit"] {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 40px;
            border-radius: 6px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            transition: transform 0.2s, box-shadow 0.2s;
        }
        
        .submit-row input[type="submit"]:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
        }
        
        .submit-row .cancel-link {
            background: #6c757d;
            color: white;
            padding: 12px 30px;
            border-radius: 6px;
            text-decoration: none;
            font-size: 16px;
            transition: background 0.2s;
        }
        
        .submit-row .cancel-link:hover {
            background: #5a6268;
        }
        
        /* Error styling */
        .errors input,
        .errors select,
        .errors textarea {
            border-color: #dc3545;
        }
        
        .errorlist {
            color: #dc3545;
            font-size: 13px;
            margin: 5px 0 0 0;
            padding: 0;
            list-style: none;
        }
        
        /* Form grid */
        .form-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        
        @media (max-width: 768px) {
            .form-grid {
                grid-template-columns: 1fr;
            }
        }
        
        /* Full width group */
        .form-group-full {
            grid-column: 1 / -1;
        }
    </style>
{% endblock %}

{% block content %}
<div class="import-container">
    <div class="info-box">
        <span class="icon">🎯</span>
        <div>
            <p><strong>Instagram Import Task</strong> allows you to import Instagram profiles, posts, or hashtag-based content with flexible options.</p>
        </div>
    </div>
    
    <form method="post" novalidate>
        {% csrf_token %}
        
        <div class="form-grid">
            <!-- Left Column -->
            <div>
                <div class="form-group">
                    <h3><span class="icon">📋</span> Import Settings</h3>
                    
                    <!-- Task Type -->
                    <div class="form-row{% if form.task_type.errors %} errors{% endif %}">
                        {{ form.task_type.label_tag }}
                        {{ form.task_type }}
                        {% if form.task_type.help_text %}
                            <p class="helptext">{{ form.task_type.help_text }}</p>
                        {% endif %}
                        {% if form.task_type.errors %}
                            {{ form.task_type.errors }}
                        {% endif %}
                    </div>
                    
                    <!-- Target Identifier -->
                    <div class="form-row{% if form.target_identifier.errors %} errors{% endif %}">
                        {{ form.target_identifier.label_tag }}
                        {{ form.target_identifier }}
                        {% if form.target_identifier.help_text %}
                            <p class="helptext">{{ form.target_identifier.help_text }}</p>
                        {% endif %}
                        {% if form.target_identifier.errors %}
                            {{ form.target_identifier.errors }}
                        {% endif %}
                    </div>
                    
                    <!-- Posts Limit -->
                    <div class="form-row{% if form.posts_limit.errors %} errors{% endif %}">
                        {{ form.posts_limit.label_tag }}
                        {{ form.posts_limit }}
                        {% if form.posts_limit.help_text %}
                            <p class="helptext">{{ form.posts_limit.help_text }}</p>
                        {% endif %}
                        {% if form.posts_limit.errors %}
                            {{ form.posts_limit.errors }}
                        {% endif %}
                    </div>
                </div>
            </div>
            
            <!-- Right Column -->
            <div>
                <div class="form-group">
                    <h3><span class="icon">⚙️</span> Options</h3>
                    
                    <!-- Post Types -->
                    <div class="form-row{% if form.post_types.errors %} errors{% endif %}">
                        {{ form.post_types.label_tag }}
                        {{ form.post_types }}
                        {% if form.post_types.help_text %}
                            <p class="helptext">{{ form.post_types.help_text }}</p>
                        {% endif %}
                        {% if form.post_types.errors %}
                            {{ form.post_types.errors }}
                        {% endif %}
                    </div>
                    
                    <!-- Media Download Option -->
                    <div class="form-row{% if form.download_media.errors %} errors{% endif %}">
                        <div class="checkbox-row">
                            {{ form.download_media }}
                            <label for="{{ form.download_media.id_for_label }}">
                                {{ form.download_media.label }}
                                {% if form.download_media.help_text %}
                                    <span class="helptext">- {{ form.download_media.help_text }}</span>
                                {% endif %}
                            </label>
                        </div>
                        {% if form.download_media.errors %}
                            {{ form.download_media.errors }}
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Hidden fields -->
        {% if form.skip_media_download %}
            {{ form.skip_media_download }}
        {% endif %}
        
        <!-- Execution Mode (Full Width) -->
        <div class="form-group form-group-full">
            <h3><span class="icon">🚀</span> Execution Mode</h3>
            
            <div class="form-row{% if form.execution_mode.errors %} errors{% endif %}">
                {{ form.execution_mode.label_tag }}
                {{ form.execution_mode }}
                {% if form.execution_mode.help_text %}
                    <p class="helptext">{{ form.execution_mode.help_text }}</p>
                {% endif %}
                {% if form.execution_mode.errors %}
                    {{ form.execution_mode.errors }}
                {% endif %}
            </div>
        </div>
        
        {% if form.non_field_errors %}
            <div class="form-group form-group-full">
                {{ form.non_field_errors }}
            </div>
        {% endif %}
        
        <div class="submit-row">
            <input type="submit" value="🚀 Start Import" class="default" />
            <a href="{% url 'admin:instagram_manager_instagramscrapingtask_changelist' %}" class="button cancel-link">Cancel</a>
        </div>
    </form>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        const taskTypeField = document.getElementById('id_task_type');
        const targetField = document.getElementById('id_target_identifier');
        const postTypesRow = document.querySelector('.form-row:has(#id_post_types)');
        
        function updatePlaceholder() {
            const taskType = taskTypeField.value;
            if (taskType === 'hashtag') {
                targetField.placeholder = 'Enter hashtag (e.g., travel)';
                if (postTypesRow) postTypesRow.style.display = 'block';
            } else if (taskType === 'profile') {
                targetField.placeholder = 'Enter username (e.g., instagram)';
                if (postTypesRow) postTypesRow.style.display = 'none';
            } else {
                targetField.placeholder = 'Enter username (e.g., instagram)';
                if (postTypesRow) postTypesRow.style.display = 'block';
            }
        }
        
        taskTypeField.addEventListener('change', updatePlaceholder);
        updatePlaceholder();
        
        // Highlight active form group
        const formGroups = document.querySelectorAll('.form-group');
        formGroups.forEach(group => {
            const inputs = group.querySelectorAll('input, select, textarea');
            inputs.forEach(input => {
                input.addEventListener('focus', () => {
                    group.style.boxShadow = '0 4px 12px rgba(102, 126, 234, 0.15)';
                    group.style.borderColor = '#667eea';
                });
                input.addEventListener('blur', () => {
                    group.style.boxShadow = '';
                    group.style.borderColor = '';
                });
            });
        });
        
        // Add loading state to submit button
        const form = document.querySelector('form');
        const submitBtn = document.querySelector('input[type="submit"]');
        
        form.addEventListener('submit', function() {
            submitBtn.value = '⏳ Processing...';
            submitBtn.disabled = true;
            submitBtn.style.opacity = '0.7';
        });
    });
</script>
{% endblock %}