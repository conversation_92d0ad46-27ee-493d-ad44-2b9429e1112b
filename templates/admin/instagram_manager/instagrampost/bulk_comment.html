{% extends "admin/base_site.html" %}
{% load i18n admin_urls static admin_modify %}

{% block extrahead %}
    {{ block.super }}
    <script src="{% url 'admin:jsi18n' %}"></script>
    {{ form.media }}
{% endblock %}

{% block content %}
<div id="content-main">
    <form method="post" id="bulk-comment-form">
        {% csrf_token %}
        
        <h2>Posts to Comment On</h2>
        <div class="module">
            <div class="form-row">
                <p>You have selected <strong>{{ posts.count }}</strong> posts for commenting.</p>
                <ul style="max-height: 200px; overflow-y: auto; list-style: none; padding: 10px; background: #f8f8f8; border: 1px solid #ddd;">
                    {% for post in posts %}
                    <li style="margin-bottom: 5px;">
                        <a href="{% url 'admin:instagram_manager_instagrampost_change' post.id %}" target="_blank">
                            {{ post.shortcode }}
                        </a> - @{{ post.profile.username }}
                        {% if post.caption %}
                            <span style="color: #666; font-size: 0.9em;">({{ post.caption|truncatechars:50 }})</span>
                        {% endif %}
                    </li>
                    {% endfor %}
                </ul>
            </div>
        </div>

        <fieldset class="module aligned">
            <h2>Comment Settings</h2>
            
            {% for field in form %}
                <div class="form-row{% if field.errors %} errors{% endif %}">
                    {{ field.errors }}
                    {{ field.label_tag }}
                    {{ field }}
                    {% if field.help_text %}
                        <p class="help">{{ field.help_text|safe }}</p>
                    {% endif %}
                </div>
            {% endfor %}
        </fieldset>

        <div class="submit-row">
            <input type="submit" value="Post Comments" class="default" name="_save">
            <input type="submit" value="Cancel" name="_cancel" formnovalidate>
        </div>
    </form>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Handle cancel button
    const cancelBtn = document.querySelector('input[name="_cancel"]');
    if (cancelBtn) {
        cancelBtn.addEventListener('click', function(e) {
            e.preventDefault();
            window.location.href = "{% url 'admin:instagram_manager_instagrampost_changelist' %}";
        });
    }
    
    // Add character counter for comment template
    const commentTemplate = document.querySelector('#id_comment_template');
    if (commentTemplate) {
        const counter = document.createElement('div');
        counter.style.textAlign = 'right';
        counter.style.fontSize = '0.9em';
        counter.style.color = '#666';
        commentTemplate.parentNode.appendChild(counter);
        
        function updateCounter() {
            const length = commentTemplate.value.length;
            counter.textContent = `${length} / 2200 characters`;
            counter.style.color = length > 2200 ? '#d00' : '#666';
        }
        
        commentTemplate.addEventListener('input', updateCounter);
        updateCounter();
    }
});
</script>
{% endblock %}