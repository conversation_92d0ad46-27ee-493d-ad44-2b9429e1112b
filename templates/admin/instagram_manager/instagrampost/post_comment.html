{% extends "admin/base_site.html" %}
{% load static %}

{% block extrastyle %}
{{ block.super }}
<style>
    .form-row {
        margin-bottom: 15px;
    }
    .post-preview {
        background: #f8f8f8;
        padding: 15px;
        border-radius: 5px;
        margin-bottom: 20px;
    }
    .post-preview img {
        max-width: 200px;
        margin-right: 15px;
        float: left;
    }
    .post-info {
        overflow: hidden;
    }
    .clear {
        clear: both;
    }
</style>
{% endblock %}

{% block content %}
<h1>Post Comment to Instagram</h1>

<div class="post-preview">
    <h3>Post: {{ post.shortcode }}</h3>
    <div class="post-info">
        <p><strong>Author:</strong> @{{ post.profile.username }}</p>
        <p><strong>URL:</strong> <a href="{{ post.post_url }}" target="_blank">{{ post.post_url }}</a></p>
        <p><strong>Caption:</strong> {{ post.caption|truncatewords:30 }}</p>
        <p><strong>Likes:</strong> {{ post.like_count|floatformat:0 }} | <strong>Comments:</strong> {{ post.comment_count }}</p>
    </div>
    <div class="clear"></div>
</div>

<form method="post" id="post-comment-form">
    {% csrf_token %}
    
    <fieldset class="module aligned">
        <h2>Comment Details</h2>
        
        <div class="form-row">
            <div>
                {{ form.account.errors }}
                <label for="{{ form.account.id_for_label }}" class="required">
                    {{ form.account.label }}:
                </label>
                {{ form.account }}
                {% if form.account.help_text %}
                    <p class="help">{{ form.account.help_text }}</p>
                {% endif %}
            </div>
        </div>
        
        <div class="form-row">
            <div>
                {{ form.comment_text.errors }}
                <label for="{{ form.comment_text.id_for_label }}" class="required">
                    {{ form.comment_text.label }}:
                </label>
                {{ form.comment_text }}
                <p class="help">
                    Characters: <span id="char-count">0</span>/2200
                    {% if form.comment_text.help_text %}
                        <br>{{ form.comment_text.help_text }}
                    {% endif %}
                </p>
            </div>
        </div>
        
        <div class="form-row">
            <div>
                {{ form.reply_to_username.errors }}
                <label for="{{ form.reply_to_username.id_for_label }}">
                    {{ form.reply_to_username.label }}:
                </label>
                {{ form.reply_to_username }}
                {% if form.reply_to_username.help_text %}
                    <p class="help">{{ form.reply_to_username.help_text }}</p>
                {% endif %}
            </div>
        </div>
        
        <!-- Hidden field for post_url -->
        <input type="hidden" name="post_url" value="{{ post.post_url }}">
    </fieldset>
    
    <div class="submit-row">
        <input type="submit" value="Post Comment" class="default" />
        <a href="{% url 'admin:instagram_manager_instagrampost_change' post.pk %}" class="button cancel-link">Cancel</a>
    </div>
</form>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Character counter
    const textarea = document.querySelector('textarea[name="comment_text"]');
    const charCount = document.getElementById('char-count');
    
    function updateCharCount() {
        charCount.textContent = textarea.value.length;
    }
    
    textarea.addEventListener('input', updateCharCount);
    updateCharCount();
});
</script>
{% endblock %}