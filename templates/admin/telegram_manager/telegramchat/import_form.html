{% extends "admin/base_site.html" %}
{% load i18n admin_urls static %}

{% block extrahead %}
  {{ block.super }}
  <style>
    /* Modern styling */
    body {
        background-color: #f5f7fa;
    }
    
    /* Main container */
    .import-container {
        max-width: 800px;
        margin: 0 auto;
        padding-bottom: 50px;
    }
    
    /* Info box */
    .info-box {
        background: linear-gradient(135deg, #0088cc 0%, #0066aa 100%);
        color: white;
        border-radius: 8px;
        padding: 20px;
        margin-bottom: 25px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        display: flex;
        align-items: center;
        gap: 15px;
    }
    
    .info-box .icon {
        font-size: 28px;
        opacity: 0.9;
    }
    
    .info-box p {
        margin: 0;
        font-size: 15px;
        line-height: 1.5;
    }
    
    /* Form groups */
    .form-group {
        background: white;
        border-radius: 12px;
        padding: 25px;
        margin-bottom: 20px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
        border: 1px solid #e1e8ed;
        transition: box-shadow 0.3s;
    }
    
    .form-group:hover {
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12);
    }
    
    .form-group h3 {
        margin: 0 0 20px 0;
        color: #2c3e50;
        font-size: 18px;
        font-weight: 600;
        display: flex;
        align-items: center;
        gap: 10px;
    }
    
    .form-group h3 .icon {
        color: #0088cc;
        font-size: 20px;
    }
    
    /* Form rows */
    .form-row {
        margin-bottom: 18px;
    }
    
    .form-row:last-child {
        margin-bottom: 0;
    }
    
    .form-row label {
        font-weight: 500;
        color: #495057;
        margin-bottom: 6px;
        display: block;
        font-size: 14px;
    }
    
    /* Form controls */
    .form-control,
    input[type="number"] {
        width: 100%;
        max-width: 100%;
        box-sizing: border-box;
        padding: 10px 14px;
        border: 1px solid #ced4da;
        border-radius: 6px;
        font-size: 14px;
        transition: border-color 0.2s, box-shadow 0.2s;
        background-color: #fff;
    }
    
    .form-control:focus,
    input[type="number"]:focus {
        border-color: #0088cc;
        outline: none;
        box-shadow: 0 0 0 3px rgba(0, 136, 204, 0.1);
    }
    
    /* Radio buttons */
    .radio-group {
        display: flex;
        flex-direction: column;
        gap: 12px;
    }
    
    .radio-item {
        background: #f8f9fa;
        padding: 12px 16px;
        border-radius: 6px;
        transition: background 0.2s;
        display: flex;
        align-items: center;
    }
    
    .radio-item:hover {
        background: #e9ecef;
    }
    
    .radio-item input[type="radio"] {
        margin-right: 8px;
        width: 18px;
        height: 18px;
        cursor: pointer;
    }
    
    .radio-item label {
        margin: 0;
        font-weight: normal;
        cursor: pointer;
    }
    
    /* Checkbox styling */
    .checkbox-row {
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 12px 16px;
        background: #f8f9fa;
        border-radius: 6px;
        transition: background 0.2s;
        margin-bottom: 10px;
    }
    
    .checkbox-row:last-child {
        margin-bottom: 0;
    }
    
    .checkbox-row:hover {
        background: #e9ecef;
    }
    
    .checkbox-row input[type="checkbox"] {
        width: 18px;
        height: 18px;
        cursor: pointer;
    }
    
    .checkbox-row label {
        margin: 0;
        cursor: pointer;
        flex-grow: 1;
        font-weight: normal;
    }
    
    /* Help text */
    .helptext {
        font-size: 12px;
        color: #6c757d;
        margin-top: 4px;
        line-height: 1.4;
    }
    
    /* Submit row */
    .submit-row {
        background: white;
        border-radius: 12px;
        padding: 25px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
        border: 1px solid #e1e8ed;
        display: flex;
        gap: 15px;
        align-items: center;
        justify-content: center;
    }
    
    .submit-row button {
        background: linear-gradient(135deg, #0088cc 0%, #0066aa 100%);
        color: white;
        border: none;
        padding: 12px 40px;
        border-radius: 6px;
        font-size: 16px;
        font-weight: 500;
        cursor: pointer;
        transition: transform 0.2s, box-shadow 0.2s;
    }
    
    .submit-row button:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 136, 204, 0.4);
    }
    
    .submit-row .cancel {
        background: #6c757d;
        color: white;
        padding: 12px 30px;
        border-radius: 6px;
        text-decoration: none;
        font-size: 16px;
        transition: background 0.2s;
    }
    
    .submit-row .cancel:hover {
        background: #5a6268;
    }
    
    /* Error styling */
    .errors input {
        border-color: #dc3545;
    }
    
    .errorlist {
        color: #dc3545;
        font-size: 13px;
        margin: 5px 0 0 0;
        padding: 0;
        list-style: none;
    }
  </style>
{% endblock %}

{% block content %}
<div class="import-container">
  <div class="info-box">
    <span class="icon">💬</span>
    <div>
      <p><strong>Import Telegram Chats</strong> allows you to import your chats, groups, and channels from Telegram.</p>
    </div>
  </div>

  <form method="post" action="{% url 'telegram_manager:telegram_manager_telegramchat_do-import' %}">
    {% csrf_token %}

    <div class="form-group">
      <h3><span class="icon">📊</span> Import Settings</h3>
      
      <div class="form-row{% if form.chat_limit.errors %} errors{% endif %}">
        <label>{{ form.chat_limit.label }}:</label>
        <div class="radio-group">
          {{ form.chat_limit }}
        </div>
        {% if form.chat_limit.errors %}
          <ul class="errorlist">
            {% for error in form.chat_limit.errors %}
              <li>{{ error }}</li>
            {% endfor %}
          </ul>
        {% endif %}
      </div>

      <div class="form-row{% if form.custom_limit.errors %} errors{% endif %}" id="custom-limit-row">
        {{ form.custom_limit.label_tag }}
        {{ form.custom_limit }}
        {% if form.custom_limit.help_text %}
          <p class="helptext">{{ form.custom_limit.help_text }}</p>
        {% endif %}
        {% if form.custom_limit.errors %}
          <ul class="errorlist">
            {% for error in form.custom_limit.errors %}
              <li>{{ error }}</li>
            {% endfor %}
          </ul>
        {% endif %}
      </div>
    </div>

    <div class="form-group">
      <h3><span class="icon">🔍</span> Chat Types</h3>
      
      <div class="checkbox-row">
        {{ form.include_private }}
        <label for="{{ form.include_private.id_for_label }}">{{ form.include_private.label }}</label>
      </div>
      
      <div class="checkbox-row">
        {{ form.include_groups }}
        <label for="{{ form.include_groups.id_for_label }}">{{ form.include_groups.label }}</label>
      </div>
      
      <div class="checkbox-row">
        {{ form.include_supergroups }}
        <label for="{{ form.include_supergroups.id_for_label }}">{{ form.include_supergroups.label }}</label>
      </div>
      
      <div class="checkbox-row">
        {{ form.include_channels }}
        <label for="{{ form.include_channels.id_for_label }}">{{ form.include_channels.label }}</label>
      </div>
    </div>

    <div class="form-group">
      <h3><span class="icon">🚀</span> Execution Mode</h3>
      
      <div class="form-row{% if form.execution_mode.errors %} errors{% endif %}">
        {{ form.execution_mode.label_tag }}
        <div class="radio-group">
          {{ form.execution_mode }}
        </div>
        {% if form.execution_mode.help_text %}
          <p class="helptext">{{ form.execution_mode.help_text }}</p>
        {% endif %}
        {% if form.execution_mode.errors %}
          <ul class="errorlist">
            {% for error in form.execution_mode.errors %}
              <li>{{ error }}</li>
            {% endfor %}
          </ul>
        {% endif %}
      </div>
    </div>

    {% if form.non_field_errors %}
      <div class="form-group">
        <ul class="errorlist">
          {% for error in form.non_field_errors %}
            <li>{{ error }}</li>
          {% endfor %}
        </ul>
      </div>
    {% endif %}

    <div class="submit-row">
      <button type="submit">🚀 Start Import</button>
      <a href="{% url 'admin:telegram_manager_telegramchat_changelist' %}" class="cancel">Cancel</a>
    </div>
  </form>
</div>

<script>
  document.addEventListener('DOMContentLoaded', function() {
    const radioButtons = document.querySelectorAll('input[name="chat_limit"]');
    const customLimitRow = document.getElementById('custom-limit-row');

    function updateCustomLimitVisibility() {
      const selectedValue = document.querySelector('input[name="chat_limit"]:checked').value;
      customLimitRow.style.display = selectedValue === 'custom' ? 'block' : 'none';
    }

    // Set initial state
    updateCustomLimitVisibility();

    // Add change listeners
    radioButtons.forEach(radio => {
      radio.addEventListener('change', updateCustomLimitVisibility);
    });
  });
</script>
{% endblock %}
