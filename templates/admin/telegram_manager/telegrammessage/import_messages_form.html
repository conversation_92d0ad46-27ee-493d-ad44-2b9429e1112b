{% extends "admin/base_site.html" %}
{% load i18n admin_urls static %}

{% block extrastyle %}
{{ block.super }}
<style>
    /* Modern Design for Telegram Message Import Form */
    .telegram-import-container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 20px;
        padding-bottom: 60px;
    }

    .telegram-import-header {
        background: linear-gradient(135deg, #0088cc 0%, #0066aa 100%);
        color: white;
        padding: 30px;
        border-radius: 12px 12px 0 0;
        margin: -20px -20px 0 -20px;
        box-shadow: 0 4px 15px rgba(0, 136, 204, 0.3);
    }

    .telegram-import-header h1 {
        margin: 0;
        font-size: 28px;
        font-weight: 300;
        letter-spacing: -0.5px;
    }

    .telegram-import-header p {
        margin: 10px 0 0 0;
        opacity: 0.9;
        font-size: 16px;
    }

    .telegram-import-form {
        background: white;
        padding: 0;
        border-radius: 0 0 12px 12px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.08);
        margin: 0 -20px -20px -20px;
    }

    .form-section {
        padding: 30px;
        border-bottom: 1px solid #eef2f5;
    }

    .form-section:last-of-type {
        border-bottom: none;
    }

    .section-header {
        display: flex;
        align-items: center;
        margin-bottom: 25px;
        gap: 12px;
    }

    .section-icon {
        width: 40px;
        height: 40px;
        background: linear-gradient(135deg, #0088cc 0%, #0066aa 100%);
        border-radius: 10px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 20px;
    }

    .section-title {
        font-size: 20px;
        font-weight: 500;
        color: #2c3e50;
        margin: 0;
    }

    /* Chat Selection Styles */
    .chat-selection-grid {
        max-height: 400px;
        overflow-y: auto;
        border: 1px solid #e0e6ed;
        border-radius: 8px;
        padding: 15px;
        background: #f8f9fa;
    }

    .chat-item {
        display: flex;
        align-items: center;
        padding: 12px;
        margin-bottom: 8px;
        background: white;
        border-radius: 8px;
        border: 1px solid #e0e6ed;
        transition: all 0.2s ease;
    }

    .chat-item:hover {
        background: #f0f4f8;
        transform: translateX(5px);
        box-shadow: 0 2px 8px rgba(0,0,0,0.05);
    }

    .chat-item input[type="checkbox"] {
        width: 20px;
        height: 20px;
        margin-right: 15px;
        cursor: pointer;
    }

    .chat-item label {
        display: flex;
        align-items: center;
        cursor: pointer;
        flex: 1;
        margin: 0;
    }

    .chat-item img {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        margin-right: 12px;
        object-fit: cover;
    }

    .chat-info {
        flex: 1;
    }

    .chat-title {
        font-weight: 500;
        color: #2c3e50;
        margin-bottom: 2px;
    }

    .chat-meta {
        font-size: 12px;
        color: #7f8c8d;
    }

    .chat-type {
        display: inline-block;
        padding: 2px 8px;
        border-radius: 4px;
        font-size: 11px;
        font-weight: 500;
        margin-left: 8px;
    }

    .chat-type.channel {
        background: #e3f2fd;
        color: #1976d2;
    }

    .chat-type.supergroup {
        background: #f3e5f5;
        color: #7b1fa2;
    }

    .chat-type.chat {
        background: #e8f5e9;
        color: #388e3c;
    }

    /* Form Groups */
    .form-group {
        margin-bottom: 25px;
    }

    .form-group label {
        display: block;
        margin-bottom: 8px;
        font-weight: 500;
        color: #34495e;
        font-size: 14px;
    }

    .form-group input[type="text"],
    .form-group input[type="number"],
    .form-group input[type="date"],
    .form-group select,
    .form-group textarea {
        width: 100%;
        padding: 12px 16px;
        border: 2px solid #e0e6ed;
        border-radius: 8px;
        font-size: 14px;
        transition: all 0.3s ease;
        background: #fff;
        box-sizing: border-box;
        max-width: 100%;
    }

    .form-group input:focus,
    .form-group select:focus,
    .form-group textarea:focus {
        outline: none;
        border-color: #0088cc;
        box-shadow: 0 0 0 3px rgba(0, 136, 204, 0.1);
    }

    .form-group .helptext {
        display: block;
        margin-top: 6px;
        font-size: 13px;
        color: #7f8c8d;
        line-height: 1.4;
    }

    /* Radio Buttons */
    .radio-group {
        display: flex;
        flex-direction: column;
        gap: 12px;
        padding: 15px;
        background: #f8f9fa;
        border-radius: 8px;
        border: 1px solid #e0e6ed;
    }

    .radio-item {
        display: flex;
        align-items: center;
        padding: 8px;
        border-radius: 6px;
        transition: background 0.2s ease;
    }

    .radio-item:hover {
        background: #e8f0f8;
    }

    .radio-item input[type="radio"] {
        width: 18px;
        height: 18px;
        margin-right: 12px;
        cursor: pointer;
    }

    .radio-item label {
        cursor: pointer;
        margin: 0;
        flex: 1;
        color: #2c3e50;
    }

    /* Date Range Fields */
    .date-range-group {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 20px;
        padding: 20px;
        background: #f8f9fa;
        border-radius: 8px;
        margin-top: 15px;
    }

    /* Submit Section */
    .submit-section {
        padding: 30px;
        background: #f8f9fa;
        border-top: 1px solid #e0e6ed;
        display: flex;
        justify-content: flex-end;
        gap: 15px;
        margin: 0 -30px -30px -30px;
        border-radius: 0 0 12px 12px;
    }

    .btn {
        padding: 12px 24px;
        border: none;
        border-radius: 8px;
        font-size: 15px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.3s ease;
        text-decoration: none;
        display: inline-block;
        line-height: 1;
    }

    .btn-primary {
        background: linear-gradient(135deg, #0088cc 0%, #0066aa 100%);
        color: white;
        box-shadow: 0 2px 4px rgba(0, 136, 204, 0.3);
    }

    .btn-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 136, 204, 0.4);
    }

    .btn-secondary {
        background: #e0e6ed;
        color: #5a6c7d;
    }

    .btn-secondary:hover {
        background: #d1d8e0;
    }

    /* Error Styles */
    .errorlist {
        background: #fee;
        border: 1px solid #fcc;
        border-radius: 6px;
        padding: 10px 15px;
        margin: 10px 0;
        list-style: none;
    }

    .errorlist li {
        color: #c33;
        margin: 5px 0;
    }

    .form-group.errors input,
    .form-group.errors select,
    .form-group.errors textarea {
        border-color: #dc3545;
    }

    /* Empty State */
    .empty-state {
        text-align: center;
        padding: 60px 20px;
        color: #7f8c8d;
    }

    .empty-state-icon {
        font-size: 48px;
        margin-bottom: 15px;
        opacity: 0.5;
    }

    /* Scrollbar Styling */
    .chat-selection-grid::-webkit-scrollbar {
        width: 8px;
    }

    .chat-selection-grid::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 4px;
    }

    .chat-selection-grid::-webkit-scrollbar-thumb {
        background: #cbd5e0;
        border-radius: 4px;
    }

    .chat-selection-grid::-webkit-scrollbar-thumb:hover {
        background: #a0aec0;
    }
</style>
{% endblock %}

{% block content %}
<div class="telegram-import-container">
    <div class="telegram-import-header">
        <h1>Import Telegram Messages</h1>
        <p>Select chats and configure import options for messages</p>
    </div>

    <form method="post" action="{% url 'telegram_manager:telegram_manager_telegrammessage_do-import' %}" class="telegram-import-form">
        {% csrf_token %}

        <!-- Chat Selection Section -->
        <div class="form-section">
            <div class="section-header">
                <div class="section-icon">💬</div>
                <h2 class="section-title">Select Chats</h2>
            </div>

            <div class="chat-selection-grid">
                {% for chat in chats %}
                    <div class="chat-item">
                        <input type="checkbox" name="chat_ids" value="{{ chat.chat_id }}" id="chat_{{ chat.chat_id }}">
                        <label for="chat_{{ chat.chat_id }}">
                            {% if chat.photo %}
                                <img src="{{ chat.photo.url }}" alt="{{ chat.title }}" onerror="this.style.display='none';">
                            {% else %}
                                <div style="width: 40px; height: 40px; background: #e0e6ed; border-radius: 50%; margin-right: 12px;"></div>
                            {% endif %}
                            <div class="chat-info">
                                <div class="chat-title">{{ chat.title }}</div>
                                <div class="chat-meta">
                                    ID: {{ chat.chat_id }}
                                    {% if chat.broadcast %}
                                        <span class="chat-type channel">Channel</span>
                                    {% elif chat.megagroup %}
                                        <span class="chat-type supergroup">Supergroup</span>
                                    {% else %}
                                        <span class="chat-type chat">Chat</span>
                                    {% endif %}
                                </div>
                            </div>
                        </label>
                    </div>
                {% empty %}
                    <div class="empty-state">
                        <div class="empty-state-icon">📭</div>
                        <p>No chats available. Please import chats first.</p>
                    </div>
                {% endfor %}
            </div>
        </div>

        <!-- Import Options Section -->
        <div class="form-section">
            <div class="section-header">
                <div class="section-icon">⚙️</div>
                <h2 class="section-title">Import Options</h2>
            </div>

            <div class="form-group">
                <label for="{{ form.category.id_for_label }}">{{ form.category.label }}</label>
                {{ form.category }}
                {% if form.category.help_text %}
                    <span class="helptext">{{ form.category.help_text }}</span>
                {% endif %}
            </div>

            <div id="custom-date-range" class="date-range-group" style="display: none;">
                <div class="form-group">
                    <label for="{{ form.date_from.id_for_label }}">{{ form.date_from.label }}</label>
                    {{ form.date_from }}
                    {% if form.date_from.help_text %}
                        <span class="helptext">{{ form.date_from.help_text }}</span>
                    {% endif %}
                </div>

                <div class="form-group">
                    <label for="{{ form.date_to.id_for_label }}">{{ form.date_to.label }}</label>
                    {{ form.date_to }}
                    {% if form.date_to.help_text %}
                        <span class="helptext">{{ form.date_to.help_text }}</span>
                    {% endif %}
                </div>
            </div>

            <div class="form-group">
                <label for="{{ form.message_limit.id_for_label }}">{{ form.message_limit.label }}</label>
                {{ form.message_limit }}
                {% if form.message_limit.help_text %}
                    <span class="helptext">{{ form.message_limit.help_text }}</span>
                {% endif %}
            </div>

            <div class="form-group">
                <label>{{ form.execution_mode.label }}</label>
                <div class="radio-group">
                    {{ form.execution_mode }}
                </div>
                {% if form.execution_mode.help_text %}
                    <span class="helptext">{{ form.execution_mode.help_text }}</span>
                {% endif %}
                {% if form.execution_mode.errors %}
                    <ul class="errorlist">
                        {% for error in form.execution_mode.errors %}
                            <li>{{ error }}</li>
                        {% endfor %}
                    </ul>
                {% endif %}
            </div>
        </div>

        <!-- Submit Section -->
        <div class="submit-section">
            <a href="{% url 'admin:telegram_manager_telegrammessage_changelist' %}" class="btn btn-secondary">Cancel</a>
            <button type="submit" class="btn btn-primary">Import Messages</button>
        </div>
    </form>
</div>

<script>
// Show/hide custom date range fields based on category selection
document.addEventListener('DOMContentLoaded', function() {
    const categorySelect = document.getElementById('{{ form.category.id_for_label }}');
    const customDateRangeDiv = document.getElementById('custom-date-range');

    function toggleDateRange() {
        if (categorySelect.value === 'custom') {
            customDateRangeDiv.style.display = 'grid';
        } else {
            customDateRangeDiv.style.display = 'none';
        }
    }

    categorySelect.addEventListener('change', toggleDateRange);
    toggleDateRange(); // Call on initial load
});
</script>
{% endblock %}
