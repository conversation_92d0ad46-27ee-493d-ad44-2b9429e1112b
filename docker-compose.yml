services:
  # PostgreSQL Database
  db:
    image: postgres:latest
    volumes:
      - postgres_data:/var/lib/postgresql/data/
    environment:
      - POSTGRES_PASSWORD=postgres
      - POSTGRES_USER=postgres
      - POSTGRES_DB=socialmanager
    ports:
      - "5432:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres -d socialmanager"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis Cache and Message Broker
  redis:
    image: redis:latest
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5
      
  # Celery Worker
  celery:
    build:
      context: .
      dockerfile: Dockerfile.celery
    volumes:
      - .:/app
      - /app/.venv  # Exclude .venv from mounting
      - media_volume:/app/media
      - telegram_sessions:/app/telegram_sessions
    environment:
      - CELERY_BROKER_URL=redis://redis:6379/0
      - CELERY_RESULT_BACKEND=redis://redis:6379/1
      - DATABASE_URL=************************************/socialmanager
      - DJANGO_SETTINGS_MODULE=SocialManager.settings.local
      - PYTHONPATH=/app
      - POSTGRES_HOST=db
      - POSTGRES_PORT=5432
      - POSTGRES_DB=socialmanager
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=postgres
      - REDIS_URL=redis://redis:6379/2
      - BRIGHTDATA_API_TOKEN=${BRIGHTDATA_API_TOKEN}
      - BRIGHTDATA_TIMEOUT=${BRIGHTDATA_TIMEOUT:-600}
      - FIELD_ENCRYPTION_KEY=${FIELD_ENCRYPTION_KEY}
      - CELERY_COMMAND=worker
    depends_on:
      db:
        condition: service_healthy
      redis:
        condition: service_healthy

  # Celery Beat Scheduler
  celery-beat:
    build:
      context: .
      dockerfile: Dockerfile.celery
    volumes:
      - .:/app
      - /app/.venv  # Exclude .venv from mounting
    environment:
      - CELERY_BROKER_URL=redis://redis:6379/0
      - CELERY_RESULT_BACKEND=redis://redis:6379/1
      - DATABASE_URL=************************************/socialmanager
      - DJANGO_SETTINGS_MODULE=SocialManager.settings.local
      - PYTHONPATH=/app
      - POSTGRES_HOST=db
      - POSTGRES_PORT=5432
      - POSTGRES_DB=socialmanager
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=postgres
      - REDIS_URL=redis://redis:6379/2
      - BRIGHTDATA_API_TOKEN=${BRIGHTDATA_API_TOKEN}
      - BRIGHTDATA_TIMEOUT=${BRIGHTDATA_TIMEOUT:-600}
      - FIELD_ENCRYPTION_KEY=${FIELD_ENCRYPTION_KEY}
      - CELERY_COMMAND=beat --scheduler django_celery_beat.schedulers:DatabaseScheduler
    depends_on:
      db:
        condition: service_healthy
      redis:
        condition: service_healthy

  # Flower - Celery Monitoring (Optional)
  flower:
    build:
      context: .
      dockerfile: Dockerfile.celery
    ports:
      - "5555:5555"
    environment:
      - CELERY_BROKER_URL=redis://redis:6379/0
      - DJANGO_SETTINGS_MODULE=SocialManager.settings.local
      - PYTHONPATH=/app
      - CELERY_COMMAND=flower --port=5555
    depends_on:
      - redis

volumes:
  postgres_data:
  redis_data:
  media_volume:
  telegram_sessions: