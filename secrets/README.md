## Encrypted secrets

### Requirements

* Install [SOPS](https://github.com/getsops/sops) and [age](https://github.com/FiloSottile/age) tools.

  #### Mac<PERSON>
    ```bash
    brew install sops
    brew install age
    ```

* (Optional) Install [helm secrets](https://github.com/jkroepke/helm-secrets/wiki/Installation) plugin to be able to edit
  encrypted files in place with automatic encryption/decryption.


### Encrypt/Decrypt secrets

```bash
export SOPS_AGE_RECIPIENTS=<your age public key> (from group CI/CD variable SOPS_AGE)
export SOPS_AGE_KEY_FILE=<path to your age private key> (from group CI/CD variable SOPS_KEY_FILE)
# Encrypt
sops --encrypt --unencrypted-regex '(name|secretName)' secrets_test_plain.yaml > secrets_test.yaml
# Decrypt
sops --decrypt secrets_test.yaml > secrets_test_plain.yaml
```

### Edit file with Helm Secrets

Requires [helm secrets](https://github.com/jkroepke/helm-secrets/wiki/Installation) plugin.

Vim like editing of file with automatic encryption/decryption during open/close.
```bash
helm secrets edit secrets_test.yaml
```