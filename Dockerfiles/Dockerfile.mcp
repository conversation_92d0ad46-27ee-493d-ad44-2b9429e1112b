# MCP Server Dockerfile
FROM python:3.13-slim

# Copy uv from official image
COPY --from=ghcr.io/astral-sh/uv:latest /uv /uvx /bin/

# Set environment variables
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    UV_COMPILE_BYTECODE=1 \
    UV_LINK_MODE=copy \
    UV_CACHE_DIR=/app/.cache/uv

# Install system dependencies
RUN apt-get update && apt-get install -y \
    postgresql-client \
    build-essential \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Create non-root user
RUN groupadd -r mcp && useradd -r -g mcp mcp

# Set work directory
WORKDIR /app

# Copy project files
COPY --chown=mcp:mcp . /app/

# Install dependencies including FastMCP
RUN uv sync --frozen --no-dev

# Create necessary directories and set proper permissions
RUN mkdir -p /app/.cache /app/logs && chown -R mcp:mcp /app

# Switch to non-root user
USER mcp

# Expose MCP port
EXPOSE 8001

# Health check
HEALTHCHECK --interval=300s --timeout=10s --start-period=40s --retries=3 \
    CMD curl -f http://localhost:8001/health || exit 1

# Run MCP server with uvicorn
CMD ["uv", "run", "uvicorn", "mcp_server.main:app", "--host", "0.0.0.0", "--port", "8001", "--workers", "4"]