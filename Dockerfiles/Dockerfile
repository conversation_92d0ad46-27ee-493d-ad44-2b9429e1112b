# Django Production Dockerfile with ASGI support
FROM python:3.13-slim

# Copy uv from official image for fast package management
COPY --from=ghcr.io/astral-sh/uv:latest /uv /uvx /bin/

# Set environment variables
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    UV_COMPILE_BYTECODE=1 \
    UV_LINK_MODE=copy \
    UV_CACHE_DIR=/app/.cache/uv

# Install system dependencies
RUN apt-get update && apt-get install -y \
    postgresql-client \
    build-essential \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Create non-root user
RUN groupadd -r django && useradd -r -g django django && mkdir -p /home/<USER>/.cache/uv && chown -R django:django /home/<USER>/.cache/uv

# Set work directory
WORKDIR /app

# Copy project files
COPY --chown=django:django . /app/

# Install Python dependencies
RUN uv sync --frozen --no-dev

# Create necessary directories
RUN mkdir -p /app/media /app/static /app/logs /app/telegram_sessions && \
    chown -R django:django /app

# Collect static files
RUN uv run python manage.py collectstatic --noinput

# Switch to non-root user
RUN mkdir -p /app/logs/celery && chown -R django:django /app/logs

USER django

# Expose Django port
EXPOSE 8000

# Health check
# HEALTHCHECK --interval=300s --timeout=10s --start-period=40s --retries=3 \
#     CMD curl -f http://localhost:8000/health/ || exit 1

# Run Django with uvicorn (ASGI)
CMD ["uv", "run", "uvicorn", "SocialManager.asgi:application", \
     "--host", "0.0.0.0", \
     "--port", "8000", \
     "--workers", "4", \
     "--loop", "asyncio", \
     "--access-log"]
