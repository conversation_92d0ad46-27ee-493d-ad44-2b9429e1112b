FROM python:3.13-slim

# Копируем uv из официального образа
COPY --from=ghcr.io/astral-sh/uv:latest /uv /uvx /bin/

WORKDIR /app

# Системные зависимости
RUN apt-get update && apt-get install -y \
    postgresql-client \
    build-essential \
    && rm -rf /var/lib/apt/lists/*

# Устанавливаем переменную для uv
ENV UV_COMPILE_BYTECODE=1
ENV UV_LINK_MODE=copy

# Копируем весь проект
COPY . /app

# Устанавливаем зависимости проекта
RUN uv sync --frozen --no-dev

# Порт для Flower
EXPOSE 5555

# Переменная окружения для типа Celery процесса
ENV CELERY_COMMAND="worker"

# Используем CMD вместо ENTRYPOINT для большей гибкости
# По умолчанию запускаем worker, но можно переопределить через CELERY_COMMAND
CMD ["sh", "-c", "uv run celery -A SocialManager ${CELERY_COMMAND} -l info"]