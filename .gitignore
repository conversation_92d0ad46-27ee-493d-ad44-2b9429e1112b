.idea
.venv
db.sqlite3
.DS_Store
__pycache__/
*.py[cod]
*$py.class
# Environment variables
.claude
.env
.env.dev
.env.test
.env.prod
/telegram_manager.session
/examples
/media
/telegram_manager/sessions
/instagram_manager/browser_automation/storage/auth

# Scripts database
/scripts/scripts_db.sqlite3
/scripts/*.sqlite3
/logs
/test_media
/staticfiles

celerybeat-schedule
celerybeat-schedule-shm
celerybeat-schedule-wal
celery.log
/gcs.json
/htmlcov/
.coverage
