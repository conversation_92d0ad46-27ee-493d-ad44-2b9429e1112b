[pytest]
DJANGO_SETTINGS_MODULE = SocialManager.settings.test
pythonpath = .
# Match all Django test discovery patterns
python_files = test_*.py *_test.py *_tests.py tests.py
# Include all directories with tests, plus . to catch any new apps
testpaths = 
    tests
    core
    telegram_manager
    instagram_manager
    monitoring
timeout = 600
timeout_method = thread

# Clean and readable output configuration
addopts = 
    --tb=short
    --strict-markers
    --disable-warnings
    -p no:warnings
    --color=yes
    --create-db
    # Show test names with progress
    -v
    # Show summary of failures, errors, and skips
    -rfEs
    # Capture output but show only on failures
    --capture=fd
    # Show durations of 5 slowest tests
    --durations=5

# Logging configuration - capture logs but don't show them unless test fails
log_cli = false
log_cli_level = CRITICAL
log_file_level = CRITICAL
log_capture = true

# Disable structlog during tests
python_files_exclude = 
    structlog/*

# Exclude directories that should not be treated as test modules
collect_ignore_glob = tests/core/logging

# Filter all warnings
filterwarnings =
    ignore::DeprecationWarning
    ignore::django.utils.deprecation.RemovedInDjango50Warning
    ignore::django.utils.deprecation.RemovedInDjango51Warning
    ignore:.*CacheKeyWarning.*
    ignore::UserWarning
    ignore::RuntimeWarning
    ignore:DateTimeField .* received a naive datetime:RuntimeWarning:django.db.models.fields

# Markers
markers =
    django_db: mark a test as needing the database
    asyncio: mark a test as using asyncio
    slow: mark a test as slow running
    integration: mark a test as integration test
    unit: mark a test as unit test