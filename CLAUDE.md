# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

SocialManager is a Django-based application for managing social media integrations, currently focused on Telegram and Instagram. It provides:

- **Telegram Integration**: Models for chats, users, and messages with Telethon API integration
- **Instagram Integration**: Models for profiles, posts, media, comments with BrightData API integration
- **Repository Pattern**: Structured data access layer for all models
- **Service Layer**: Business logic implementation with validation
- **Pydantic Integration**: Schema validation for API responses and data processing
- **Management Commands**: CLI tools for data import, export, and automation
- **Admin Interface**: Enhanced Django admin with custom actions and bulk operations
- **Decorator Infrastructure**: Reusable decorators for validation and permissions
- **Unified Form Validation**: Django forms with Pydantic-based validators
- **Asynchronous Tasks**: Celery integration for background processing

## Development Commands

This project uses `uv` for package management.

### Virtual Environment
- Always use the project's .venv, activate it with `uv`
- Always use uv for packages

### Django Management
```bash
# Run the development server
python manage.py runserver

# Create database migrations
python manage.py makemigrations

# Apply database migrations
python manage.py migrate

# Create a superuser for admin access
python manage.py createsuperuser

# Run Django shell
python manage.py shell
```

### Code Formatting
```bash
# Format code with Black (configured in pyproject.toml)
black .
```

### linter
```bash
# Format code with Black (configured in pyproject.toml)
ruff check
```
### linter with fix code
```bash
# Format code with Black (configured in pyproject.toml)
ruff check --fix
```

### Python type checker 
```bash
# Type check with mypy
uv run mypy .
```

### Running Tests

```bash
# IMPORTANT: Use pytest instead of manage.py test for cleaner output
# Run all tests with default readable output (runs 533+ tests from all apps)
uv run pytest

# Run with enhanced readable output and progress bar
uv run pytest -c pytest-readable.ini

# Run with ultra-clean output (no logs, warnings, or headers)
uv run pytest -c pytest-clean.ini

# Run with minimal output
uv run pytest -q

# Run with detailed verbose output for debugging
uv run pytest -vv

# Show N slowest tests
uv run pytest --durations=10

# Run specific test file
uv run pytest tests/instagram_manager/test_models.py

# Run specific test class
uv run pytest tests/instagram_manager/test_models.py::TestInstagramPost

# Run specific test method
uv run pytest tests/instagram_manager/test_models.py::TestInstagramPost::test_post_creation

# Run tests with coverage
uv run pytest --cov=. --cov-report=html

# Run only failed tests from last run
uv run pytest --lf

# Run tests in parallel (requires pytest-xdist)
uv run pytest -n auto

# Disable pytest-sugar progress bar (if installed)
uv run pytest -p no:sugar

# Run tests and drop into debugger on failures
uv run pytest --pdb

# Skip automatic cleanup of test artifacts
uv run pytest --no-cleanup
```

#### Test Output Configurations

The project includes three pytest configuration files:

1. **pytest.ini** (default) - Clean, readable output with test progress and captured output
2. **pytest-readable.ini** - Enhanced output with detailed progress and timing
3. **pytest-clean.ini** - Minimal output, only shows failures

Note: All configurations now properly capture stdout/stderr and logging output during tests, showing them only when tests fail. This ensures clean progress bars when using pytest-sugar.

#### Automatic Test Cleanup

After test execution, the following directories are automatically cleaned:
- **test_media/** - All files in subdirectories are removed (directory structure preserved)
- **htmlcov/** - Completely removed and recreated as empty directory

To skip automatic cleanup, use the `--no-cleanup` flag:
```bash
uv run pytest --no-cleanup
```

## Architecture

### Django Apps
- **telegram_manager**: Core app managing Telegram integrations
  - Models: TelegramChat, TelegramUser, TelegramMessage
  - Admin interface with custom "Import from Telegram" functionality
  - TelegramManager class for direct Telegram API interactions
  
- **instagram_manager**: Core app managing Instagram integrations
  - Models: InstagramProfile, InstagramPost, InstagramMedia, InstagramComment, etc.
  - BrightData API integration for data collection
  - Management commands for importing and posting content
  
- **core**: Base application with shared functionality
  - Abstract models (TimestampedModel, SoftDeleteModel, etc.)
  - Base repository and service classes
  - Exception handling utilities

### Core Application Structure

#### Repository Pattern Implementation

Each app uses repositories for data access:
- `BaseRepository` - Abstract base with CRUD operations
- App-specific repositories (e.g., `InstagramPostRepository`, `TelegramChatRepository`)
- Bulk operations support
- Query optimization with select_related/prefetch_related

#### Service Layer

Business logic is implemented in services:
- Data validation with Pydantic schemas
- Transaction management
- Error handling and logging
- Integration with external APIs

#### Pydantic Schemas

- `schemas/` directory in each app for data validation
- Request/response schemas for external APIs
- Data transformation and validation helpers

#### Form Validation System

The project uses a unified validation approach that bridges Django forms and Pydantic schemas:

1. **Core Form Components** (`core/forms/`):
   - `validators.py`: Django validators that leverage Pydantic validation logic
   - `mixins.py`: Reusable form mixins for common functionality
   - `fields.py`: Specialized form fields with built-in validation
   
2. **Key Features**:
   - Single source of truth for validation logic
   - Instagram username validation with @-stripping
   - Bulk operations support (multiple usernames/URLs)
   - Date range validation for filtering
   - URL validation for social media platforms

3. **Usage Example**:
   ```python
   from core.forms.mixins import InstagramFormMixin
   
   class MyForm(InstagramFormMixin, forms.Form):
       username = forms.CharField()
       
       def clean_username(self):
           return super().clean_username('username')
   ```

### Key Components

#### Telegram Manager

1. **Models** (telegram_manager/models.py):
   - `TelegramChat`: Stores Telegram chats/channels
   - `TelegramUser`: Stores Telegram user information
   - `TelegramMessage`: Stores message content with relationships to chats and users

2. **Admin Interface** (telegram_manager/admin.py):
   - Custom admin views with import functionality
   - Integration with TelegramManager for fetching data from Telegram API

3. **Telegram API Client** (telegram_manager/telegram_api/):
   - Handles direct Telegram API communication using Telethon
   - Supports authentication, fetching dialogs, and retrieving messages
   - Data handlers for chats, messages, and users

#### Instagram Manager

1. **Models** (instagram_manager/models.py):
   - `InstagramProfile`: Instagram user profiles
   - `InstagramPost`: Posts with media and metrics
   - `InstagramMedia`: Photos and videos with local storage and GCS support
   - `InstagramComment`: Comments with reply hierarchy
   - `InstagramHashtag`: Hashtag tracking
   - `InstagramFollower`: Follower relationships
   - `InstagramScrapingTask`: Task tracking for data imports
   - `InstagramAccount`: Accounts for automation with encrypted passwords
   - `PostedComment`: Tracking posted comments

2. **BrightData Integration** (instagram_manager/instagram_api/):
   - `BrightDataClient`: API client with rate limiting and error handling
   - Data handlers for profiles, posts, comments, and media
   - Pydantic validation for all API responses

3. **Google Cloud Storage Integration**:
   - Media files can be saved to GCS instead of local storage
   - Automatic folder organization:
     - `images/` - for photos
     - `videos/` - for video files
     - `previews/` - for video thumbnails
   - MediaHandler supports GCS with automatic fallback to local storage
   - File validation and sanitization before upload

4. **Management Commands**:
   - `instagram_profiles`: Import Instagram profiles
   - `instagram_posts`: Import posts for profiles
   - `instagram_comments`: Import comments for posts
   - `instagram_post_comment`: Post comments to Instagram
   - `instagram_bulk_comments`: Bulk comment posting
   - `download_instagram_media`: Download media files locally

5. **Repositories**:
   - Profile, Post, Media, Comment, Hashtag, Follower repositories
   - Bulk operations and optimized queries
   - Transaction support

6. **Services**:
   - Business logic for profiles, posts, comments, media
   - Integration with repositories and external APIs
   - Data validation and error handling

### Configuration

- Environment variables managed via `python-decouple`
- Required environment variables:
  
  **Telegram:**
  - `API_ID`: Telegram API ID
  - `API_HASH`: Telegram API Hash  
  - `SESSION_NAME`: Telegram session name
  - `OUTPUT_DIR`: Directory for saving message exports
  
  **Instagram/BrightData:**
  - `BRIGHTDATA_API_KEY`: BrightData API key
  - `BRIGHTDATA_DATASET_ID`: Dataset ID for Instagram
  - `BRIGHTDATA_TIMEOUT`: API timeout (default: 120)
  - `FIELD_ENCRYPTION_KEY`: Key for encrypting sensitive data
  
  **General:**
  - `MEDIA_ROOT`: Directory for storing downloaded media
  - `REDIS_URL`: Redis connection for caching (optional)
  
  **Google Cloud Storage:**
  - `GCS_BUCKET_NAME`: GCS bucket name for media storage
  - `GOOGLE_APPLICATION_CREDENTIALS`: Path to GCS service account key file
  
  **Celery:**
  - `CELERY_BROKER_URL`: Message broker URL (Redis/RabbitMQ)
  - `CELERY_RESULT_BACKEND`: Result backend URL
  - `CELERY_TASK_ALWAYS_EAGER`: Set to True for synchronous execution in tests

### Asynchronous Tasks (Celery)

The project uses Celery for background task processing:

1. **Task Infrastructure** (`core/tasks/`):
   - `base.py`: BaseTask abstract class for all async tasks
   - `celery_integration.py`: Celery configuration and task execution
   - `exceptions.py`: Task-specific exceptions
   - `decorators.py`: Task decorators for monitoring and caching

2. **Available Tasks**:
   
   **Instagram Tasks** (`instagram_manager/tasks/`):
   - `ImportBatchPostsTask`: Batch import posts for multiple profiles
   - `ImportInstagramProfileTask`: Import single profile data
   - `ImportInstagramPostsTask`: Import posts for a profile
   - `ImportInstagramCommentsTask`: Import comments for posts
   
   **Telegram Tasks** (`telegram_manager/tasks/`):
   - `ImportTelegramChatsTask`: Import chats/channels
   - `ImportTelegramUsersTask`: Import users from chats
   - `ImportTelegramMessagesTask`: Import messages from chats

3. **Task Management**:
   - Progress tracking with `update_progress()` method
   - Retry logic with configurable max retries
   - Task results stored in `TaskResult` model
   - WebSocket/SSE support for real-time updates

4. **Running Tasks**:
   ```python
   # Synchronous execution
   task = ImportInstagramProfileTask()
   result = task.execute(username="example")
   
   # Asynchronous execution via Celery
   task = ImportInstagramProfileTask()
   celery_result = task.run_async(username="example")
   ```

5. **Monitoring**:
   - Use Django admin to view TaskResult records
   - Check Celery logs for task execution details
   - Monitor progress via task_id

### Sessions

Telegram session is stored in the `telegram_manager.session` file in the project root directory.

### Troubleshooting

**Import from Telegram error**: If you encounter event loop errors when using the "Import from Telegram" button in Django admin, the issue has been fixed by:
1. Running async operations in a separate thread with its own event loop
2. Using absolute paths for session file storage to ensure consistency across different execution contexts

**Telegram user import**: When importing messages from Telegram, user data is now automatically fetched from the Telegram API:
- Users are created with full data (username, first_name, last_name, etc.) instead of temporary names
- Use `--skip-user-fetch` flag with `telegram_messages` command for faster import without user data
- To fix existing users with temporary names like "user_12345", run: `python manage.py fix_telegram_users --fetch-api`

## Testing

### Test Structure
- Tests organized by app in `tests/` directory
- Comprehensive test coverage for models, repositories, services
- Integration tests for external APIs
- Fixtures and factories for test data

### Running Tests
The project uses pytest for all testing. Tests are configured to run with clean output and all logs disabled.

```bash
# Run all tests
uv run pytest

# Run tests for specific app
uv run pytest tests/instagram_manager/
uv run pytest tests/telegram_manager/

# Run with coverage
uv run pytest --cov=. --cov-report=html
uv run pytest --cov=. --cov-report=term-missing
```

### Test Categories
- Unit tests for models and utilities
- Integration tests for repositories and services
- API integration tests with mocking
- Admin interface tests
- Management command tests
- Async task tests with Celery mocking
- Form validation tests

### Key Test Files
- `tests/core/test_celery_tasks.py`: BaseTask and Celery integration
- `tests/instagram_manager/test_import_tasks.py`: Instagram import tasks
- `tests/telegram_manager/test_import_tasks.py`: Telegram import tasks
- Form validation tests in respective app test directories

## Decorators

The project includes reusable decorators in `core/decorators/`:
- **@validate_input**: Pydantic-based input validation
- **@deprecated**: Mark deprecated methods with warnings
- **@require_permission**: Django permission checking
- **@async_to_sync_django**: Convert async functions for Django

## Development Notes
- don't commit to git I will do it myself
- always use .venv and uv with this project
- always use ruff as a linter
- always use mypy as a Python type checker
- use uv where run tests
- all views always need to be in file views.py, and all forms in file forms.py
- for migrations always use uv
- follow a repository pattern—data access through repositories only
- use services for business logic, not in models or views
- validate external API data with Pydantic schemas
- use Django's transaction.atomic() for data consistency
- log errors and important operations
- handle BrightData rate limits with exponential backoff
- encrypt sensitive data (passwords, tokens) before storing
- use decorators for cross-cutting concerns (validation, permissions)
- Always check file with mypy and uv run ruff check --config ./pyproject.toml after editing

## Django 5.2 Compatibility

### BoundField attrs Fix
Django 5.2 templates expect `attrs` to always be a dict in `django/forms/attrs.html`. However, `BoundField.label_tag()` can pass `None` as attrs, causing "Failed lookup for key [items] in None" errors.

**Solution**: We patch `BoundField.label_tag` in `core/forms/boundfield_patch.py` to ensure attrs is always a dict before passing to templates. This patch is automatically applied when importing from `core.forms`.

### Safe Widgets
For additional safety, we provide `SafeRadioSelect` and `SafeCheckboxSelectMultiple` widgets that ensure subwidgets always have attrs as dict. Use these instead of the default Django widgets when needed.

## Memories
- Always reply in Russian.
- Always ultrathink before doing anything.
- Always ultrathink after using any tools.
- Always make a plan first and don't write any code until I approve the plan!
- After receiving tool results, carefully reflect on their quality and determine optimal next steps before proceeding. Use your thinking to plan and iterate based on this new information and then take the best next action.
- For maximum efficiency, whenever you need to perform multiple independent operations, invoke all relevant tools simultaneously rather than sequentially.
- If you create any temporary new files, scripts, or helper files for iteration, clean up these files by removing them at the end of the task.
- Always use the MCP server contex7 to get documentation.