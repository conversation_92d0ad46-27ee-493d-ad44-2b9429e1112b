#!/usr/bin/env python
"""
Utility script to clean the test/scripts database.
"""
import os
import sys
from pathlib import Path

# Add parent directory to Python path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))


def clean_database():
    """Remove the scripts_db.sqlite3 file if it exists."""
    db_path = Path(__file__).parent / "scripts_db.sqlite3"
    
    if db_path.exists():
        try:
            os.remove(db_path)
            print(f"✅ Database {db_path.name} has been removed from {db_path.parent}")
            return True
        except Exception as e:
            print(f"❌ Error removing database: {e}")
            return False
    else:
        print(f"ℹ️  Database scripts_db.sqlite3 does not exist in {Path(__file__).parent}")
        return True


def main():
    """Main function."""
    print("🧹 Cleaning test database...")
    success = clean_database()
    
    if success:
        print("\n✨ You can now run tests or scripts with a fresh database")
        print("   Run migrations first: python manage.py migrate --settings=scripts.settings")
    else:
        print("\n⚠️  Please close any applications using the database and try again")
        sys.exit(1)


if __name__ == "__main__":
    main()