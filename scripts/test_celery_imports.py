#!/usr/bin/env python
"""
Test script for Instagram import functionality with Celery.
"""
import os
import sys
import time
import django

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'SocialManager.settings')
django.setup()

from instagram_manager.services.import_service import ImportService  # noqa: E402
from core.models import TaskResult  # noqa: E402


def test_async_import():
    """Test asynchronous import via Celery."""
    print("\n=== Testing ASYNC import (via Celery) ===")
    
    # Import profile
    result = ImportService.import_profile("igorkishik")
    print(f"Profile import started: {result}")
    print(f"Celery task ID: {result.id}")
    
    # Wait a bit and check status
    time.sleep(2)
    task_result = TaskResult.objects.filter(celery_task_id=result.id).first()
    if task_result:
        print(f"Task status: {task_result.status}")
        print(f"Task type: {task_result.task_type}")
    
    # Get profile ID for posts import
    from instagram_manager.models import InstagramProfile
    profile = InstagramProfile.objects.filter(username="igorkishik").first()
    if profile:
        # Import posts
        result = ImportService.import_posts(profile.id, limit=5)
        print(f"\nPosts import started: {result}")
        print(f"Celery task ID: {result.id}")
    
    # Batch import with just igorkishik (single profile for testing)
    result = ImportService.import_batch_posts(["igorkishik"], limit=10)
    print(f"\nBatch import started: {result}")
    print(f"Celery task ID: {result.id}")


def test_sync_import():
    """Test synchronous import."""
    print("\n=== Testing SYNC import (force_sync=True) ===")
    
    # Import profile synchronously
    result = ImportService.import_profile("igorkishik", force_sync=True)
    print(f"Profile import result: {result}")
    
    # Get profile ID for posts import
    from instagram_manager.models import InstagramProfile
    profile = InstagramProfile.objects.filter(username="igorkishik").first()
    if profile:
        # Import posts synchronously using profile ID
        result = ImportService.import_posts(profile.id, limit=5, force_sync=True)
        print(f"\nPosts import result: {result}")


def test_global_sync_mode():
    """Test global sync mode via environment variable."""
    print("\n=== Testing GLOBAL SYNC mode (via env var) ===")
    print("Note: To test global sync mode, set INSTAGRAM_FORCE_SYNC=True in .env and restart")
    print("Current mode would use Celery by default unless INSTAGRAM_FORCE_SYNC is set")


def main():
    """Run all tests."""
    print("Testing Instagram Import with Celery Integration")
    print("=" * 50)
    
    # Check if Celery is running
    try:
        from celery import current_app
        inspector = current_app.control.inspect()
        stats = inspector.stats()
        if stats:
            print(f"✓ Celery is running with {len(stats)} worker(s)")
        else:
            print("✗ Celery is not running! Please start Celery workers.")
            return
    except Exception as e:
        print(f"✗ Error checking Celery status: {e}")
        return
    
    # Run tests
    test_async_import()
    test_sync_import()
    test_global_sync_mode()
    
    print("\n" + "=" * 50)
    print("All tests completed!")
    print("\nTo monitor tasks:")
    print("  - Flower: http://localhost:5555")
    print("  - Django Admin: http://localhost:8000/admin/core/taskresult/")
    print("  - Command: python manage.py celery_status")


if __name__ == "__main__":
    main()