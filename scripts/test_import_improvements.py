#!/usr/bin/env python
"""
Тестовый скрипт для проверки улучшений импорта постов из BrightData.
Проверяет новую статистику о дополнительных пользователях.
"""

import json
import sys
import os
from pathlib import Path

# Добавляем путь к проекту
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# Настраиваем Django
import django  # noqa: E402
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'SocialManager.settings')
django.setup()

from instagram_manager.models import InstagramScrapingTask  # noqa: E402


def check_latest_import_statistics():
    """Проверяет статистику последнего импорта."""
    
    print("Проверка статистики импорта постов из BrightData")
    print("=" * 50)
    
    # Получаем последние задачи импорта постов
    tasks = InstagramScrapingTask.objects.filter(
        task_type='posts'
    ).order_by('-created_at')[:5]
    
    if not tasks:
        print("Не найдено задач импорта постов")
        return
    
    for task in tasks:
        print(f"\nЗадача #{task.id} - {task.target_identifier}")
        print(f"Статус: {task.status}")
        print(f"Создана: {task.created_at}")
        
        if task.total_items_received > 0:
            print("\nСтатистика импорта:")
            print(f"  - Всего получено постов: {task.total_items_received}")
            print(f"  - Импортировано постов: {task.items_scraped}")
            print(f"  - Отфильтровано постов: {task.items_filtered}")
            
            if task.additional_users:
                print(f"\nДополнительные пользователи ({len(task.additional_users)} шт.):")
                for i, user in enumerate(sorted(task.additional_users)[:10]):
                    print(f"  {i+1}. {user}")
                if len(task.additional_users) > 10:
                    print(f"  ... и еще {len(task.additional_users) - 10} пользователей")
            else:
                print("\nДополнительных пользователей не найдено")
        else:
            print("\nСтатистика недоступна (старая версия задачи)")
        
        print("-" * 50)


def analyze_json_file():
    """Анализирует JSON файл от BrightData."""
    
    json_path = project_root / 'examples' / 'bd_20250619_143109_0.json'
    
    if not json_path.exists():
        print(f"\nФайл {json_path} не найден")
        return
    
    print(f"\nАнализ файла: {json_path}")
    print("=" * 50)
    
    with open(json_path, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    # Собираем статистику по пользователям
    users_posts = {}
    for post in data:
        user = post.get('user_posted', 'unknown')
        users_posts[user] = users_posts.get(user, 0) + 1
    
    print(f"Всего постов в файле: {len(data)}")
    print(f"Уникальных пользователей: {len(users_posts)}")
    
    # Показываем топ-20 пользователей по количеству постов
    print("\nТоп-20 пользователей по количеству постов:")
    sorted_users = sorted(users_posts.items(), key=lambda x: x[1], reverse=True)
    for i, (user, count) in enumerate(sorted_users[:20]):
        print(f"  {i+1}. {user}: {count} постов")


if __name__ == "__main__":
    check_latest_import_statistics()
    analyze_json_file()