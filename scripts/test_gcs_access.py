#!/usr/bin/env python
"""
Test script for Google Cloud Storage access.
This script helps verify that GCS credentials are properly configured.
"""
import os
import sys
from pathlib import Path

# Add project root to Python path
sys.path.insert(0, str(Path(__file__).parent.parent))

import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'SocialManager.settings.local')
django.setup()

from django.conf import settings  # noqa: E402


def test_gcs_connection():
    """Test connection to Google Cloud Storage."""
    print("Testing Google Cloud Storage connection...")
    print("-" * 50)
    
    # Check if settings are configured
    bucket_name = settings.GCS_BUCKET_NAME
    credentials_path = settings.GOOGLE_APPLICATION_CREDENTIALS
    
    if not bucket_name:
        print("❌ GCS_BUCKET_NAME is not set in environment variables")
        print("   Please add it to your .env file")
        return False
    
    if not credentials_path:
        print("❌ GOOGLE_APPLICATION_CREDENTIALS is not set in environment variables")
        print("   Please add it to your .env file")
        return False
    
    if not os.path.exists(credentials_path):
        print(f"❌ Credentials file not found at: {credentials_path}")
        print("   Please ensure the file exists")
        return False
    
    print(f"✓ Bucket name: {bucket_name}")
    print(f"✓ Credentials path: {credentials_path}")
    
    # Set credentials in environment
    os.environ['GOOGLE_APPLICATION_CREDENTIALS'] = credentials_path
    
    try:
        from google.cloud import storage
        
        # Try to connect
        print("\nAttempting to connect to GCS...")
        client = storage.Client()
        bucket = client.bucket(bucket_name)
        
        # Test bucket exists
        if not bucket.exists():
            print(f"❌ Bucket '{bucket_name}' does not exist or you don't have access")
            return False
        
        print(f"✓ Successfully connected to bucket: {bucket_name}")
        
        # Test write permissions
        print("\nTesting write permissions...")
        test_blob_name = "instagram/test_access.txt"
        blob = bucket.blob(test_blob_name)
        test_content = "Test content from SocialManager"
        
        try:
            blob.upload_from_string(test_content)
            print(f"✓ Successfully uploaded test file: {test_blob_name}")
            
            # Check public URL
            public_url = blob.public_url
            print(f"✓ Public URL: {public_url}")
            
            # Test read
            downloaded_content = blob.download_as_text()
            if downloaded_content == test_content:
                print("✓ Successfully read back test content")
            
            # Clean up
            blob.delete()
            print("✓ Successfully deleted test file")
            
            print("\n✅ All tests passed! GCS is properly configured.")
            return True
            
        except Exception as e:
            print(f"❌ Failed to upload test file: {e}")
            return False
            
    except ImportError:
        print("❌ google-cloud-storage library is not installed")
        print("   Run: uv pip install google-cloud-storage")
        return False
    except Exception as e:
        print(f"❌ Error connecting to GCS: {e}")
        return False


def print_setup_instructions():
    """Print instructions for setting up GCS."""
    print("\n" + "=" * 50)
    print("GCS Setup Instructions")
    print("=" * 50)
    print("\n1. Create a GCS bucket:")
    print("   - Go to https://console.cloud.google.com/storage")
    print("   - Click 'CREATE BUCKET'")
    print("   - Name it something like 'socialmanager-media-prod'")
    print("   - Choose Multi-region and Standard storage class")
    print("   - Set access control to 'Uniform'")
    
    print("\n2. Make bucket public (for MVP):")
    print("   - Go to bucket Permissions")
    print("   - Click 'ADD PRINCIPAL'")
    print("   - New principals: allUsers")
    print("   - Role: Storage Object Viewer")
    print("   - Save")
    
    print("\n3. Create service account:")
    print("   - Go to https://console.cloud.google.com/iam-admin/serviceaccounts")
    print("   - Create new service account")
    print("   - Grant role: 'Storage Object Creator'")
    print("   - Create and download JSON key")
    
    print("\n4. Configure environment:")
    print("   - Copy the JSON key to a secure location")
    print("   - Add to your .env file:")
    print("     GCS_BUCKET_NAME=your-bucket-name")
    print("     GOOGLE_APPLICATION_CREDENTIALS=/path/to/key.json")
    
    print("\n5. Run this script again to test")


if __name__ == "__main__":
    print("Google Cloud Storage Connection Test")
    print("====================================\n")
    
    if test_gcs_connection():
        print("\n🎉 GCS is ready to use!")
    else:
        print_setup_instructions()