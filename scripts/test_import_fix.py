#!/usr/bin/env python
"""Тест исправления импорта постов"""

import os
import sys
# Add parent directory to Python path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import django

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'scripts.settings')
django.setup()

from instagram_manager.services.post_service import PostService  # noqa: E402
from instagram_manager.models import InstagramProfile, InstagramPost  # noqa: E402


def main():
    """Main function for testing import fix."""
    # Проверяем текущее состояние
    print(f"Постов до импорта: {InstagramPost.objects.count()}")

    # Импортируем новые посты
    service = PostService()
    profile = InstagramProfile.objects.first()
    
    if not profile:
        # Создаем тестовый профиль
        from django.conf import settings
        profile = InstagramProfile.objects.create(
            username=settings.INSTAGRAM_TEST_USERNAME,
            profile_id="test_profile",
            full_name="Test Profile",
            bio="Test profile for script",
            follower_count=100,
            following_count=50,
            post_count=10,
        )
        print(f"Создан профиль: {profile.username}")

    if profile:
        print(f"\nИмпортируем 3 поста для {profile.username}...")
        try:
            posts = service.import_posts_for_profile(profile, limit=3)
            print(f"Успешно импортировано: {len(posts)} постов")
            
            for post in posts:
                print(f"  - {post.shortcode}: {post.post_type}, медиа: {post.media.count()}")
                
        except Exception as e:
            print(f"Ошибка при импорте: {str(e)}")
            
    print(f"\nПостов после импорта: {InstagramPost.objects.count()}")

    # Проверяем на наличие постов с пустыми полями
    empty_posts = InstagramPost.objects.filter(shortcode='') | InstagramPost.objects.filter(external_id='')
    if empty_posts.exists():
        print(f"\nВНИМАНИЕ: Найдено {empty_posts.count()} постов с пустыми полями!")
    else:
        print("\nВсе посты имеют корректные идентификаторы")


if __name__ == "__main__":
    main()