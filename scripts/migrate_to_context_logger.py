#!/usr/bin/env python
"""
Automatic migration script for converting logging.getLogger() to ContextLogger.get_logger().

This script uses AST transformation to safely migrate all Python files from:
    import logging
    logger = logging.getLogger(__name__)

To:
    from core.logging import get_logger
    logger = get_logger(__name__)

Usage:
    python scripts/migrate_to_context_logger.py [--dry-run] [--verbose] [path]

Options:
    --dry-run    Show what would be changed without making actual changes
    --verbose    Show detailed processing information
    path         Directory or file to process (default: current directory)
"""

import argparse
import ast
import os
import sys
from pathlib import Path

try:
    import astor
except ImportError:
    print("Error: astor package is required. Install with: pip install astor")
    sys.exit(1)


class LoggerMigrationTransformer(ast.NodeTransformer):
    """AST transformer for migrating logging.getLogger to ContextLogger."""
    
    def __init__(self):
        self.has_logging_import = False
        self.has_get_logger_import = False
        self.logging_import_alias = None
        self.modified = False
        self.getLogger_calls = []
        
    def visit_Import(self, node):
        """Handle 'import logging' statements."""
        for alias in node.names:
            if alias.name == "logging":
                self.has_logging_import = True
                self.logging_import_alias = alias.asname or "logging"
        return node
    
    def visit_ImportFrom(self, node):
        """Handle 'from ... import ...' statements."""
        # Check if we already have core.logging import
        if node.module == "core.logging" and any(alias.name == "get_logger" for alias in node.names):
            self.has_get_logger_import = True
        return node
    
    def visit_Call(self, node):
        """Transform logging.getLogger() calls."""
        self.generic_visit(node)
        
        # Check if this is a logging.getLogger call
        if (isinstance(node.func, ast.Attribute) and
            node.func.attr == "getLogger" and
            isinstance(node.func.value, ast.Name) and
            node.func.value.id == self.logging_import_alias):
            
            self.getLogger_calls.append(node)
            self.modified = True
            
            # Replace with get_logger call
            new_call = ast.Call(
                func=ast.Name(id="get_logger", ctx=ast.Load()),
                args=node.args,
                keywords=node.keywords
            )
            return ast.copy_location(new_call, node)
        
        return node
    
    def transform_imports(self, tree):
        """Add necessary imports at the beginning of the file."""
        if not self.modified:
            return tree
        
        # Find where to insert the import
        insert_idx = 0
        for i, node in enumerate(tree.body):
            if isinstance(node, (ast.Import, ast.ImportFrom)):
                insert_idx = i + 1
            else:
                break
        
        # Add core.logging import if not present
        if not self.has_get_logger_import:
            import_node = ast.ImportFrom(
                module="core.logging",
                names=[ast.alias(name="get_logger", asname=None)],
                level=0
            )
            tree.body.insert(insert_idx, import_node)
        
        # Remove standalone 'import logging' if it's only used for getLogger
        if self.has_logging_import and self.should_remove_logging_import(tree):
            tree.body = [node for node in tree.body if not (
                isinstance(node, ast.Import) and 
                any(alias.name == "logging" for alias in node.names)
            )]
        
        return tree
    
    def should_remove_logging_import(self, tree):
        """Check if logging import is only used for getLogger."""
        # This is a simplified check - in production, you'd want more thorough analysis
        for node in ast.walk(tree):
            if isinstance(node, ast.Attribute) and isinstance(node.value, ast.Name):
                if node.value.id == self.logging_import_alias and node.attr != "getLogger":
                    return False
        return True


def process_file(file_path: Path, dry_run: bool = False, verbose: bool = False) -> bool:
    """
    Process a single Python file for migration.
    
    Returns True if the file was modified, False otherwise.
    """
    if verbose:
        print(f"Processing: {file_path}")
    
    try:
        with open(file_path, encoding="utf-8") as f:
            source = f.read()
    except Exception as e:
        print(f"Error reading {file_path}: {e}")
        return False
    
    # Skip files that are already migrated
    if "from core.logging import get_logger" in source and "logging.getLogger" not in source:
        if verbose:
            print(f"  Already migrated: {file_path}")
        return False
    
    # Parse the AST
    try:
        tree = ast.parse(source)
    except SyntaxError as e:
        print(f"Syntax error in {file_path}: {e}")
        return False
    
    # Transform the AST
    transformer = LoggerMigrationTransformer()
    tree = transformer.visit(tree)
    
    if transformer.modified:
        tree = transformer.transform_imports(tree)
        
        # Generate new source code
        new_source = astor.to_source(tree)
        
        if dry_run:
            print(f"Would modify: {file_path}")
            if verbose:
                print("  Changes:")
                print(f"  - Found {len(transformer.getLogger_calls)} logging.getLogger() calls")
                print("  - Would add: from core.logging import get_logger")
                if transformer.should_remove_logging_import(tree):
                    print("  - Would remove: import logging")
        else:
            # Write the modified source back
            try:
                with open(file_path, "w", encoding="utf-8") as f:
                    f.write(new_source)
                print(f"Modified: {file_path}")
                if verbose:
                    print(f"  - Replaced {len(transformer.getLogger_calls)} logging.getLogger() calls")
            except Exception as e:
                print(f"Error writing {file_path}: {e}")
                return False
        
        return True
    
    return False


def find_python_files(path: Path, exclude_dirs: set[str] = None) -> list[Path]:
    """Find all Python files in the given path."""
    if exclude_dirs is None:
        exclude_dirs = {"venv", ".venv", "__pycache__", ".git", "migrations", "node_modules"}
    
    if path.is_file():
        return [path] if path.suffix == ".py" else []
    
    python_files = []
    for root, dirs, files in os.walk(path):
        # Remove excluded directories from dirs to prevent walking into them
        dirs[:] = [d for d in dirs if d not in exclude_dirs]
        
        for file in files:
            if file.endswith(".py"):
                python_files.append(Path(root) / file)
    
    return python_files


def main():
    """Main entry point for the migration script."""
    parser = argparse.ArgumentParser(
        description="Migrate logging.getLogger() to ContextLogger.get_logger()"
    )
    parser.add_argument(
        "path",
        nargs="?",
        default=".",
        help="Directory or file to process (default: current directory)"
    )
    parser.add_argument(
        "--dry-run",
        action="store_true",
        help="Show what would be changed without making actual changes"
    )
    parser.add_argument(
        "--verbose",
        action="store_true",
        help="Show detailed processing information"
    )
    parser.add_argument(
        "--exclude",
        action="append",
        help="Additional directories to exclude (can be used multiple times)"
    )
    
    args = parser.parse_args()
    
    path = Path(args.path)
    if not path.exists():
        print(f"Error: Path does not exist: {path}")
        sys.exit(1)
    
    # Set up exclusions
    exclude_dirs = {"venv", ".venv", "__pycache__", ".git", "migrations", "node_modules"}
    if args.exclude:
        exclude_dirs.update(args.exclude)
    
    # Find all Python files
    python_files = find_python_files(path, exclude_dirs)
    
    if not python_files:
        print("No Python files found to process.")
        return
    
    print(f"Found {len(python_files)} Python files to process.")
    if args.dry_run:
        print("DRY RUN MODE - No files will be modified.")
    print()
    
    # Process each file
    modified_count = 0
    for file_path in python_files:
        if process_file(file_path, dry_run=args.dry_run, verbose=args.verbose):
            modified_count += 1
    
    # Summary
    print()
    print(f"Summary: {'Would modify' if args.dry_run else 'Modified'} {modified_count} out of {len(python_files)} files.")
    
    if modified_count > 0 and not args.dry_run:
        print("\nMigration complete! Please:")
        print("1. Run your tests to ensure everything works correctly")
        print("2. Review the changes with 'git diff'")
        print("3. Consider running the validation script: python scripts/validate_migration.py")


if __name__ == "__main__":
    main()