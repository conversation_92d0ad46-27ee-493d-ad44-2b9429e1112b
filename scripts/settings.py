"""
Django settings for scripts and experiments.
This configuration uses a separate database to avoid polluting the main database.
"""
from SocialManager.settings import *  # noqa

# Always use a separate SQLite file for scripts
DATABASES = {
    "default": {
        "ENGINE": "django.db.backends.sqlite3",
        "NAME": str(BASE_DIR / "scripts" / "scripts_db.sqlite3"),  # noqa: F405
    }
}

print("🔧 Using scripts settings with database: scripts/scripts_db.sqlite3")

# Disable debug toolbar and other development tools for scripts
DEBUG_TOOLBAR = False
# Override INTERNAL_IPS from imported settings
INTERNAL_IPS = []  # type: ignore[no-redef]

# Simplify logging for scripts
LOGGING = {
    "version": 1,
    "disable_existing_loggers": False,
    "handlers": {
        "console": {
            "class": "logging.StreamHandler",
        },
    },
    "root": {
        "handlers": ["console"],
        "level": "INFO",
    },
    "loggers": {
        "django": {
            "handlers": ["console"],
            "level": "WARNING",
            "propagate": False,
        },
    },
}