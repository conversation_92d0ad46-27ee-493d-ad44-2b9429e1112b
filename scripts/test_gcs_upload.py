#!/usr/bin/env python
"""
Test script for GCS Service upload functionality.
This script tests the GCSService with real GCS operations.
"""
import os
import sys
from pathlib import Path

# Add project root to Python path
sys.path.insert(0, str(Path(__file__).parent.parent))

import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'SocialManager.settings.local')
django.setup()

from core.storage.gcs_service import GCSService  # noqa: E402


def test_folder_structure():
    """Test get_folder_by_media_type method."""
    print("Testing folder structure logic...")
    print("-" * 50)
    
    # Test folder determination
    test_cases = [
        ("image", False, "images"),
        ("photo", False, "images"),
        ("video", False, "videos"),
        ("video", True, "previews"),
        ("image", True, "previews"),
        (None, False, "images"),
        ("", False, "images"),
    ]
    
    for media_type, is_thumbnail, expected in test_cases:
        result = GCSService.get_folder_by_media_type(media_type, is_thumbnail)
        status = "✓" if result == expected else "✗"
        print(f"{status} get_folder_by_media_type('{media_type}', {is_thumbnail}) = '{result}' (expected: '{expected}')")
    
    print()


def test_gcs_upload():
    """Test GCS upload with real service."""
    print("Testing GCS Service...")
    print("-" * 50)
    
    try:
        # Initialize service
        service = GCSService()
        print(f"✓ Service initialized with bucket: {service.bucket_name}")
        
        # Check availability
        if service.is_available():
            print("✓ GCS service is available")
        else:
            print("✗ GCS service not available")
            print("  Please check your GCS configuration and credentials")
            return False
        
        # Test 1: Upload image to images folder
        print("\nTest 1: Upload test image to 'images' folder...")
        test_content = b'\x89PNG\r\n\x1a\n\x00\x00\x00\rIHDR\x00\x00\x00\x01\x00\x00\x00\x01\x08\x06\x00\x00\x00\x1f\x15\xc4\x89\x00\x00\x00\nIDATx\x9cc\x00\x01\x00\x00\x05\x00\x01\r\n-\xb4\x00\x00\x00\x00IEND\xaeB`\x82'  # Minimal valid PNG
        test_filename = "test_upload.png"
        
        try:
            url = service.upload_file(
                test_content,
                test_filename,
                "image/png",
                folder="images"  # Using correct folder
            )
            
            print("✓ File uploaded successfully!")
            print(f"  URL: {url}")
            print(f"  Size: {len(test_content)} bytes")
            
            # Verify URL contains correct folder
            if "/images/" in url:
                print("✓ URL contains correct folder 'images'")
            else:
                print("✗ URL doesn't contain expected folder")
            
            # Extract blob name from URL for cleanup
            blob_name = url.split(f"{service.bucket_name}/")[-1]
            cleanup_blob = blob_name
            
        except ValueError as e:
            print(f"✗ Validation error: {e}")
            return False
        except Exception as e:
            print(f"✗ Upload error: {e}")
            return False
        
        # Test 2: Upload video to videos folder
        print("\nTest 2: Upload test video to 'videos' folder...")
        special_filename = "test_video.mp4"
        
        try:
            url2 = service.upload_file(
                test_content,  # Using same content for simplicity
                special_filename,
                "video/mp4",
                folder="videos"  # Video folder
            )
            
            print("✓ Video uploaded successfully!")
            print(f"  Filename: {special_filename}")
            print(f"  URL: {url2}")
            
            # Verify URL contains correct folder
            if "/videos/" in url2:
                print("✓ URL contains correct folder 'videos'")
            else:
                print("✗ URL doesn't contain expected folder")
            
        except Exception as e:
            print(f"✗ Video upload test failed: {e}")
        
        # Test 3: Upload thumbnail to previews folder
        print("\nTest 3: Upload thumbnail to 'previews' folder...")
        thumbnail_filename = "thumbnail.jpg"
        
        try:
            url3 = service.upload_file(
                test_content,
                thumbnail_filename,
                "image/jpeg",
                folder="previews"  # Previews folder
            )
            
            print("✓ Thumbnail uploaded successfully!")
            print(f"  Filename: {thumbnail_filename}")
            print(f"  URL: {url3}")
            
            # Verify URL contains correct folder
            if "/previews/" in url3:
                print("✓ URL contains correct folder 'previews'")
            else:
                print("✗ URL doesn't contain expected folder")
            
        except Exception as e:
            print(f"✗ Thumbnail upload test failed: {e}")
        
        # Test 4: Validation errors
        print("\nTest 4: Testing validation...")
        
        # Test invalid content type
        try:
            service.upload_file(
                b"test",
                "test.txt",
                "text/plain",
                folder="images"
            )
            print("✗ Should have failed with invalid content type")
        except ValueError as e:
            print(f"✓ Correctly rejected invalid content type: {e}")
        
        # Test invalid extension
        try:
            service.upload_file(
                b"test",
                "test.exe",
                "image/jpeg",
                folder="images"
            )
            print("✗ Should have failed with invalid extension")
        except ValueError as e:
            print(f"✓ Correctly rejected invalid extension: {e}")
        
        # Test file too large
        try:
            large_content = b"x" * (101 * 1024 * 1024)  # 101MB
            service.upload_file(
                large_content,
                "large.jpg",
                "image/jpeg",
                folder="images"
            )
            print("✗ Should have failed with file too large")
        except ValueError as e:
            print(f"✓ Correctly rejected large file: {e}")
        
        # Test 5: Cleanup
        print("\nTest 5: Cleanup test file...")
        if 'cleanup_blob' in locals():
            if service.delete_file(cleanup_blob):
                print(f"✓ Successfully deleted test file: {cleanup_blob}")
            else:
                print(f"✗ Failed to delete test file: {cleanup_blob}")
        
        print("\n" + "=" * 50)
        print("✅ All tests completed successfully!")
        print("GCS Service is working properly.")
        return True
        
    except Exception as e:
        print(f"\n✗ Unexpected error: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_real_image_upload():
    """Test uploading a real image file if available."""
    print("\n" + "=" * 50)
    print("Testing with real image file...")
    print("=" * 50)
    
    # Look for a test image
    test_images = [
        "scripts/test_image.jpg",
        "tests/fixtures/test_image.jpg",
        "static/images/test.jpg",
    ]
    
    image_path = None
    for path in test_images:
        if os.path.exists(path):
            image_path = path
            break
    
    if not image_path:
        print("No test image found. Skipping real image test.")
        print("To test with a real image, place a test image at one of:")
        for path in test_images:
            print(f"  - {path}")
        return
    
    try:
        service = GCSService()
        
        with open(image_path, 'rb') as f:
            image_content = f.read()
        
        # Determine content type
        ext = os.path.splitext(image_path)[1].lower()
        content_type_map = {
            '.jpg': 'image/jpeg',
            '.jpeg': 'image/jpeg',
            '.png': 'image/png',
            '.gif': 'image/gif',
            '.webp': 'image/webp',
        }
        content_type = content_type_map.get(ext, 'image/jpeg')
        
        url = service.upload_file(
            image_content,
            os.path.basename(image_path),
            content_type,
            folder="images"  # Using correct folder for images
        )
        
        print("✓ Real image uploaded successfully!")
        print(f"  File: {image_path}")
        print(f"  Size: {len(image_content):,} bytes")
        print(f"  URL: {url}")
        
    except Exception as e:
        print(f"✗ Real image upload failed: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    print("Google Cloud Storage Service Test")
    print("=================================\n")
    
    # Check if GCS is configured
    from django.conf import settings
    
    if not settings.GCS_BUCKET_NAME:
        print("❌ GCS_BUCKET_NAME is not configured")
        print("   Please complete Task 001 (GCS Infrastructure Setup) first")
        exit(1)
    
    if not settings.GOOGLE_APPLICATION_CREDENTIALS:
        print("❌ GOOGLE_APPLICATION_CREDENTIALS is not configured")
        print("   Please complete Task 001 (GCS Infrastructure Setup) first")
        exit(1)
    
    # Run tests
    # First test folder structure logic
    test_folder_structure()
    
    # Then test actual uploads
    success = test_gcs_upload()
    
    if success:
        test_real_image_upload()
    
    exit(0 if success else 1)