#!/usr/bin/env python
"""<PERSON>ript to migrate deprecated validators to new centralized ones."""

import re
from pathlib import Path


def find_python_files(root_dir):
    """Find all Python files in the project."""
    for path in Path(root_dir).rglob("*.py"):
        if "migrations" not in str(path) and "__pycache__" not in str(path):
            yield path


def migrate_imports(content):
    """Replace old imports with new ones."""
    replacements = [
        # Instagram imports
        (
            r"from instagram_manager\.schemas\.validation import clean_text",
            "from core.schemas.validators import CommonValidators"
        ),
        (
            r"from instagram_manager\.schemas\.validation import extract_hashtags",
            "from core.schemas.validators import CommonValidators"
        ),
        (
            r"from instagram_manager\.schemas\.validation import extract_mentions",
            "from core.schemas.validators import CommonValidators"
        ),
        (
            r"from instagram_manager\.schemas\.validation import parse_brightdata_timestamp",
            "from core.schemas.validators import CommonValidators"
        ),
        (
            r"from instagram_manager\.schemas\.validation import normalize_media_type",
            "from core.schemas.validators import CommonValidators"
        ),
        (
            r"from instagram_manager\.schemas\.validation import parse_location",
            "from core.schemas.validators import CommonValidators"
        ),
        (
            r"from instagram_manager\.schemas\.validation import validate_instagram_username",
            "from core.schemas.validators import InstagramValidators"
        ),
        (
            r"from instagram_manager\.schemas\.validation import safe_int_conversion",
            "from core.schemas.validators import CommonValidators"
        ),
        # Telegram imports
        (
            r"from telegram_manager\.schemas\.validation import clean_telegram_text",
            "from core.schemas.validators import CommonValidators"
        ),
        (
            r"from telegram_manager\.schemas\.validation import extract_telegram_entities",
            "from core.schemas.validators import CommonValidators"
        ),
        (
            r"from telegram_manager\.schemas\.validation import normalize_telegram_phone",
            "from core.schemas.validators import TelegramValidators"
        ),
        (
            r"from telegram_manager\.schemas\.validation import parse_telegram_timestamp",
            "from core.schemas.validators import CommonValidators"
        ),
        (
            r"from telegram_manager\.schemas\.validation import determine_chat_type",
            "from core.schemas.validators import TelegramValidators"
        ),
    ]
    
    for old, new in replacements:
        if re.search(old, content):
            content = re.sub(old, new, content)
    
    return content


def migrate_function_calls(content):
    """Replace old function calls with new ones."""
    replacements = [
        # Instagram functions
        (r"\bclean_text\(", "CommonValidators.clean_text("),
        (r"\bextract_hashtags\(", "CommonValidators.extract_hashtags("),
        (r"\bextract_mentions\(", "CommonValidators.extract_mentions("),
        (r"\bparse_brightdata_timestamp\(", "CommonValidators.parse_timestamp("),
        (r"\bnormalize_media_type\(", "CommonValidators.normalize_media_type("),
        (r"\bparse_location\(", "CommonValidators.parse_location("),
        (r"\bvalidate_instagram_username\(", "InstagramValidators.validate_username("),
        (r"\bsafe_int_conversion\(", "CommonValidators.safe_int_conversion("),
        # Telegram functions
        (r"\bclean_telegram_text\(", "CommonValidators.clean_text("),
        (r"\bextract_telegram_entities\(", "CommonValidators.extract_entities("),
        (r"\bnormalize_telegram_phone\(", "TelegramValidators.normalize_phone("),
        (r"\bparse_telegram_timestamp\(", "CommonValidators.parse_timestamp("),
        (r"\bdetermine_chat_type\(", "TelegramValidators.determine_chat_type("),
    ]
    
    for old, new in replacements:
        content = re.sub(old, new, content)
    
    return content


def process_file(file_path):
    """Process a single file."""
    try:
        with open(file_path, encoding="utf-8") as f:
            content = f.read()
        
        original_content = content
        
        # Migrate imports
        content = migrate_imports(content)
        
        # Migrate function calls
        content = migrate_function_calls(content)
        
        # Only write if content changed
        if content != original_content:
            with open(file_path, "w", encoding="utf-8") as f:
                f.write(content)
            return True
        
        return False
    except Exception as e:
        print(f"Error processing {file_path}: {e}")
        return False


def main():
    """Main migration function."""
    project_root = Path(__file__).parent.parent
    
    print("Starting validator migration...")
    
    changed_files = []
    
    for file_path in find_python_files(project_root):
        if process_file(file_path):
            changed_files.append(file_path)
            print(f"✓ Migrated: {file_path.relative_to(project_root)}")
    
    print(f"\nMigration complete! Changed {len(changed_files)} files.")
    
    if changed_files:
        print("\nChanged files:")
        for file_path in changed_files:
            print(f"  - {file_path.relative_to(project_root)}")


if __name__ == "__main__":
    main()