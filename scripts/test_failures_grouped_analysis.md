# Test Failures Grouped Analysis

## Overview
- **Total Tests**: 204
- **Failed Tests**: 170 (83.3%)
- **Passed Tests**: 34 (16.7%)

## Test Failures by Category

### 1. Database Migration Issues (143 failures - 70%)

**Root Cause**: Database tables don't exist because migrations haven't been run for the test database.

**Error Pattern**:
```
sqlite3.OperationalError: no such table: instagram_profiles
sqlite3.OperationalError: no such table: telegram_manager_telegramchat
django.db.utils.OperationalError: no such table: instagram_profiles
```

**Affected Areas**:
- All Instagram model tests (`instagram_manager/test_models.py`)
- All Instagram repository tests (`instagram_manager/test_repositories.py`)
- All Instagram service tests (`instagram_manager/test_services.py`)
- All Telegram model tests (`telegram_manager/test_models.py`)
- All Telegram repository tests (`telegram_manager/test_repositories.py`)
- All Telegram service tests (`telegram_manager/test_services.py`)
- Admin tests that create model instances

**Fix Approach**:
1. Add `@pytest.mark.django_db` decorator to all test classes that access the database
2. Ensure migrations are created and applied:
   ```bash
   uv run python manage.py makemigrations instagram_manager
   uv run python manage.py makemigrations telegram_manager
   uv run python manage.py migrate
   ```
3. Verify test database settings in `tests/settings.py`

### 2. TelegramAPIClient Authentication Errors (12 failures - 6%)

**Root Cause**: The mock setup for TelegramClient is incorrect - the client is creating a real TelegramClient instead of using the mock.

**Error Pattern**:
```python
core.exceptions.AuthenticationError: [AUTH_ERROR] Verification code required. Check your Telegram app.
core.exceptions.AuthenticationError: [AUTH_ERROR] Failed to authenticate: [AUTH_ERROR] Verification code required
```

**Affected Tests**:
- `TelegramAPIClientTest::test_authenticate_success`
- `TelegramAPIClientTest::test_download_media`
- `TelegramAPIClientTest::test_get_chat_participants`
- All other TelegramAPIClient tests requiring authentication

**Fix Approach**:
```python
@patch('telegram_manager.telegram_api.new_telegram_client.TelegramClient')
def test_authenticate_success(self, mock_client_class):
    # Mock the class to return the mock instance
    mock_client_class.return_value = self.mock_telethon
    
    # Now when the code creates TelegramClient, it gets our mock
    credentials = {'phone': '+1234567890', 'code': '12345'}
    result = await self.client.authenticate(credentials)
```

### 3. new_telegram_users Command Issues (8 failures - 4%)

**Root Cause**: The `new_telegram_users.py` file exists but isn't tracked in git (shown in git status as `??`).

**Affected Tests**:
- All `NewTelegramUsersCommandTest` tests
- Tests expecting specific command argument parsing

**Fix Approach**:
1. Add the file to git:
   ```bash
   git add telegram_manager/management/commands/new_telegram_users.py
   ```
2. Verify the command implementation matches test expectations
3. Update tests if the actual implementation differs

### 4. SystemExit Assertion Failures (5 failures - 2.5%)

**Root Cause**: Tests expect `SystemExit` but the command is likely raising `CommandError` instead.

**Example**:
```python
# Test expects:
with self.assertRaises(SystemExit):
    cmd.get_credentials()

# But command likely does:
raise CommandError("Missing credentials")
```

**Affected Tests**:
- `NewBaseTelegramCommandTest::test_get_credentials_missing`
- Other command tests expecting SystemExit

**Fix Approach**:
1. Check the actual implementation in `new_base_telegram_command.py`
2. Update tests to expect `CommandError`:
   ```python
   from django.core.management import CommandError
   
   with self.assertRaises(CommandError):
       cmd.get_credentials()
   ```

### 5. Method/Attribute Errors (6 failures - 3%)

**Root Cause**: Tests calling non-existent methods or using incorrect method signatures.

**Examples**:
- Mock assertion failures
- Incorrect method names
- Missing attributes on objects

**Fix Approach**:
1. Review actual implementations to find correct method names
2. Update test method calls to match implementations
3. Fix mock assertions to use correct methods

### 6. Import/Module Issues (3 failures - 1.5%)

**Root Cause**: Incorrect import paths or missing modules.

**Fix Approach**:
1. Update import statements in test files
2. Ensure all required modules exist
3. Fix any circular import issues

## Recommended Fix Priority

### Priority 1: Database Issues (Quick Win - 70% of failures)
```python
# Add to all test classes that use models:
import pytest

@pytest.mark.django_db
class TestClassName(TestCase):
    # ... test methods ...
```

### Priority 2: Mock Setup (6% of failures)
Fix TelegramClient mock to prevent real API calls:
```python
@patch('telegram_manager.telegram_api.new_telegram_client.TelegramClient')
def setUp(self, mock_client_class):
    self.mock_telethon = AsyncMock()
    mock_client_class.return_value = self.mock_telethon
```

### Priority 3: Git Tracking (4% of failures)
```bash
git add telegram_manager/management/commands/new_telegram_users.py
git commit -m "Add new_telegram_users command"
```

### Priority 4: Exception Types (2.5% of failures)
Update tests to expect correct exception types (CommandError vs SystemExit)

### Priority 5: Remaining Issues
Fix method names, imports, and other minor issues

## Quick Fix Script

Create a script to add django_db markers:
```python
# fix_tests.py
import re
from pathlib import Path

test_dirs = [
    'tests/instagram_manager',
    'tests/telegram_manager'
]

for test_dir in test_dirs:
    for test_file in Path(test_dir).glob('test_*.py'):
        content = test_file.read_text()
        
        # Add import if not present
        if 'import pytest' not in content:
            content = 'import pytest\n' + content
        
        # Add decorator to test classes
        content = re.sub(
            r'(class \w+Test(?:Case)?.*?:)',
            r'@pytest.mark.django_db\n\1',
            content
        )
        
        test_file.write_text(content)
```

## Summary

The vast majority of test failures (70%) are due to missing database tables, which can be fixed by:
1. Adding the `@pytest.mark.django_db` decorator
2. Ensuring migrations are created and run

The remaining 30% of failures require:
- Fixing mock setup for TelegramClient
- Adding missing file to git
- Updating exception expectations
- Minor code fixes

With these fixes, the test suite should have a much higher pass rate.