# Scripts Directory

⚠️ **ВАЖНО**: Скрипты в этой директории используют **отдельную базу данных** (`scripts_db.sqlite3`)!

## Описание

Эта директория содержит скрипты для экспериментов, отладки и тестирования функциональности без риска повреждения основной базы данных.

## База данных

- **Используемая БД**: `scripts/scripts_db.sqlite3` (в папке scripts)
- **Изоляция от тестов**: Тесты используют in-memory БД, скрипты - файловую БД
- **НЕ основная БД**: Скрипты не трогают `db.sqlite3`
- **Персистентность**: Данные сохраняются между запусками скриптов

## Использование

### Первый запуск
Для скриптов необходимо создать и мигрировать базу данных:
```bash
python manage.py migrate --settings=scripts.settings
```

**Примечание**: Тесты используют отдельную in-memory базу данных и не влияют на данные скриптов.

### Запуск скриптов
```bash
# Запуск отдельного скрипта
python scripts/test_brightdata_api.py

# Запуск всех скриптов
python scripts/run_all.py
```

### Очистка данных
```bash
# Использовать утилиту
python scripts/clean_test_db.py

# Или запустить все скрипты с очисткой
python scripts/run_all.py --clean
```

### Запуск всех скриптов
```bash
# Запустить все скрипты
python scripts/run_all.py

# С предварительной очисткой БД
python scripts/run_all.py --clean

# Запустить только определенные скрипты
python scripts/run_all.py --only test_brightdata_api.py test_import_fix.py

# Исключить определенные скрипты
python scripts/run_all.py --exclude test_instagram_timeout.py

# Остановиться при первой ошибке
python scripts/run_all.py --fail-fast
```

## Python Scripts

All Python scripts now use `scripts.settings` which provides a separate database for safe experimentation.

### Instagram Diagnostics

#### `check_posts.py`
Checks the current state of Instagram posts and media in the database.
```bash
uv run python scripts/check_posts.py
```
Shows:
- Total posts and media count
- Downloaded media statistics
- Recent posts with media status
- Posts grouped by profile

#### `test_post_import_issue.py`
Diagnoses issues with post loading from Instagram.
```bash
uv run python scripts/test_post_import_issue.py
```
- Tests post import for the first profile in database
- Shows detailed logging of the import process
- Displays last scraping task status

#### `test_brightdata_api.py`
Tests connection to BrightData API with detailed logging.
```bash
uv run python scripts/test_brightdata_api.py
```
- Tests profile fetching
- Tests posts discovery API
- Shows API response status in real-time

#### `test_auto_download.py`
Tests automatic media download during post import.
```bash
uv run python scripts/test_auto_download.py
```
- Imports posts with auto-download enabled
- Shows download success/failure for each media item

#### `test_import_fix.py`
Tests post import and checks for empty/missing fields.
```bash
uv run python scripts/test_import_fix.py
```
- Validates imported data completeness
- Reports any missing required fields

#### `test_instagram_timeout.py`
Tests Instagram/BrightData API timeouts with extensive monitoring.
```bash
uv run python scripts/test_instagram_timeout.py
```
- Tests different timeout scenarios
- Monitors API response times
- Useful for debugging slow API responses

#### `test_photo_display.py`
Verifies photo display functionality in Django admin.
```bash
uv run python scripts/test_photo_display.py
```
- Creates sample posts with media
- Tests admin interface display methods
- Checks media URL generation

## JavaScript Scripts

These scripts use Node.js and Puppeteer for browser automation testing.

### Prerequisites
```bash
npm install puppeteer
```

#### `test_instagram_cookie_auth.js`
Tests Instagram authentication using cookies.
```bash
node scripts/test_instagram_cookie_auth.js
```

#### `test_instagram_login_brightdata.js`
Tests Instagram login through BrightData browser.
```bash
node scripts/test_instagram_login_brightdata.js
```

## Documentation

- `test_failures_analysis.md` - Detailed analysis of test suite failures
- `test_failures_grouped_analysis.md` - Test failures grouped by category

## Usage Notes

1. All Python scripts require the virtual environment to be activated
2. Scripts use the project's Django settings and database
3. Some scripts make real API calls to BrightData (ensure API token is configured)
4. JavaScript scripts require Node.js and may need additional setup

## Environment Variables

Ensure these are set in your `.env` file:

### Required Variables
- `BRIGHTDATA_API_TOKEN` - Required for BrightData API scripts
- `FIELD_ENCRYPTION_KEY` - Required for encrypted field access
- `MEDIA_ROOT` - Directory for downloaded media files

### Optional Test Configuration
- `INSTAGRAM_TEST_USERNAME` - Instagram username for testing (defaults to "instagram")
- `INSTAGRAM_TEST_PASSWORD` - Instagram password for login testing (required for JS scripts)

### Examples
```bash
# Basic API access
export BRIGHTDATA_API_TOKEN="your_token_here"
export FIELD_ENCRYPTION_KEY="your_encryption_key"

# Test configuration
export INSTAGRAM_TEST_USERNAME="your_test_account"
export INSTAGRAM_TEST_PASSWORD="your_test_password"
```

### Script-specific Configuration

#### Python Scripts
Most Python scripts that make BrightData API calls now use `INSTAGRAM_TEST_USERNAME`:
- `test_brightdata_api.py` - Uses for profile and posts testing
- `test_instagram_timeout.py` - Uses for timeout testing
- Defaults to "igorkishik" (Instagram's account) if not set

#### JavaScript Scripts  
Login testing scripts require credentials:
- `test_instagram_login_brightdata.js` - Requires both username and password
- Will exit with error message if credentials are not properly configured

## Common Issues

1. **Import errors**: All scripts now include proper path fixes
2. **API timeouts**: Increase timeout values in settings if needed
3. **Missing data**: Run migrations before using diagnostic scripts

## Alternative: Management Commands

Some functionality from these scripts is now available as Django management commands:

### `check_instagram_posts`
Replaces `check_posts.py` with additional features:
```bash
python manage.py check_instagram_posts
python manage.py check_instagram_posts --profile username --limit 10 --show-media
```

### `test_brightdata`
Replaces `test_brightdata_api.py` with better error handling:
```bash
python manage.py test_brightdata
python manage.py test_brightdata --profile username --test-posts --verbose
python manage.py test_brightdata --test-comments --post-url https://www.instagram.com/p/DKZ7A0XpB1U/
```