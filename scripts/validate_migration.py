#!/usr/bin/env python
"""
Validation script for ContextLogger migration.

This script validates that the migration from logging.getLogger() to ContextLogger.get_logger()
was successful by checking:
1. No remaining logging.getLogger() calls (except in allowed files)
2. Proper imports are in place
3. Code syntax is valid
4. Optional: Run basic import tests

Usage:
    python scripts/validate_migration.py [--strict] [--test-imports] [path]

Options:
    --strict        Fail on any warnings (not just errors)
    --test-imports  Try to import each Python file to check for errors
    path           Directory or file to validate (default: current directory)
"""

import argparse
import ast
import os
import sys
from pathlib import Path
from typing import Any

# Files that are allowed to use logging.getLogger()
ALLOWED_FILES = {
    "core/logging/logger.py",  # The implementation itself
    "core/logging/setup.py",   # Setup functions may need standard logging
    "scripts/migrate_to_context_logger.py",  # Migration script
    "scripts/validate_migration.py",  # This validation script
}


class ValidationResult:
    """Container for validation results."""
    
    def __init__(self):
        self.total_files = 0
        self.valid_files = 0
        self.files_with_issues = []
        self.files_with_old_logging = []
        self.files_missing_import = []
        self.files_with_syntax_errors = []
        self.files_with_import_errors = []
        self.warnings = []
    
    @property
    def has_errors(self) -> bool:
        """Check if there are any errors."""
        return bool(
            self.files_with_old_logging or 
            self.files_with_syntax_errors or
            self.files_with_import_errors
        )
    
    @property
    def has_warnings(self) -> bool:
        """Check if there are any warnings."""
        return bool(self.files_missing_import or self.warnings)
    
    def print_summary(self):
        """Print validation summary."""
        print("\nValidation Summary:")
        print(f"Total files checked: {self.total_files}")
        print(f"Valid files: {self.valid_files}")
        
        if self.files_with_old_logging:
            print(f"\n❌ Files still using logging.getLogger(): {len(self.files_with_old_logging)}")
            for file, lines in self.files_with_old_logging:
                print(f"  - {file}")
                for line_no, line in lines:
                    print(f"    Line {line_no}: {line.strip()}")
        
        if self.files_with_syntax_errors:
            print(f"\n❌ Files with syntax errors: {len(self.files_with_syntax_errors)}")
            for file, error in self.files_with_syntax_errors:
                print(f"  - {file}: {error}")
        
        if self.files_with_import_errors:
            print(f"\n❌ Files with import errors: {len(self.files_with_import_errors)}")
            for file, error in self.files_with_import_errors:
                print(f"  - {file}: {error}")
        
        if self.files_missing_import:
            print(f"\n⚠️  Files using logger but missing get_logger import: {len(self.files_missing_import)}")
            for file in self.files_missing_import:
                print(f"  - {file}")
        
        if self.warnings:
            print("\n⚠️  Warnings:")
            for warning in self.warnings:
                print(f"  - {warning}")
        
        # Final status
        print()
        if self.has_errors:
            print("❌ Validation FAILED - errors found!")
        elif self.has_warnings:
            print("⚠️  Validation passed with warnings")
        else:
            print("✅ Validation PASSED - all files migrated successfully!")


class MigrationValidator(ast.NodeVisitor):
    """AST visitor to validate migration."""
    
    def __init__(self):
        self.has_logging_getLogger = False
        self.has_get_logger_import = False
        self.uses_logger = False
        self.getLogger_calls = []
        self.line_map = {}
    
    def visit_Import(self, node):
        """Check for 'import logging' statements."""
        for alias in node.names:
            if alias.name == "logging":
                # Check if we have getLogger calls
                pass
        self.generic_visit(node)
    
    def visit_ImportFrom(self, node):
        """Check for 'from core.logging import get_logger' statements."""
        if node.module == "core.logging" and any(alias.name == "get_logger" for alias in node.names):
            self.has_get_logger_import = True
        self.generic_visit(node)
    
    def visit_Call(self, node):
        """Check for logging.getLogger() calls."""
        # Check for logging.getLogger pattern
        if (isinstance(node.func, ast.Attribute) and
            node.func.attr == "getLogger" and
            isinstance(node.func.value, ast.Name) and
            node.func.value.id == "logging"):
            
            self.has_logging_getLogger = True
            if hasattr(node, "lineno"):
                self.getLogger_calls.append(node.lineno)
        
        # Check for get_logger usage
        if isinstance(node.func, ast.Name) and node.func.id == "get_logger":
            self.uses_logger = True
        
        self.generic_visit(node)
    
    def visit_Assign(self, node):
        """Check for logger = ... assignments."""
        for target in node.targets:
            if isinstance(target, ast.Name) and target.id == "logger":
                self.uses_logger = True
        self.generic_visit(node)


def validate_file(file_path: Path, test_imports: bool = False) -> tuple[bool, list[tuple[str, Any]]]:
    """
    Validate a single Python file.
    
    Returns:
        Tuple of (is_valid, list_of_issues where each issue is (type, details))
    """
    issues: list[tuple[str, Any]] = []
    relative_path = str(file_path)
    
    # Check if this file is allowed to use logging.getLogger()
    is_allowed = any(relative_path.endswith(allowed) for allowed in ALLOWED_FILES)
    
    try:
        with open(file_path, encoding="utf-8") as f:
            source = f.read()
            lines = source.splitlines()
    except Exception as e:
        return False, [("read_error", f"Error reading file: {e}")]
    
    # Parse AST
    try:
        tree = ast.parse(source)
    except SyntaxError as e:
        return False, [("syntax_error", f"Syntax error: {e}")]
    
    # Validate using AST
    validator = MigrationValidator()
    validator.visit(tree)
    
    # Check for issues
    if validator.has_logging_getLogger and not is_allowed:
        # Find the actual lines with getLogger calls
        problem_lines = []
        for line_no in validator.getLogger_calls:
            if line_no <= len(lines):
                problem_lines.append((line_no, lines[line_no - 1]))
        
        if problem_lines:
            issues.append(("old_logging", problem_lines))
    
    # Check if file uses logger but doesn't have proper import
    if validator.uses_logger and not validator.has_get_logger_import and not is_allowed:
        # Check if it might be importing from somewhere else
        if "from core.logging import" not in source and "import core.logging" not in source:
            issues.append(("missing_import", None))
    
    # Test imports if requested
    if test_imports and not issues:
        try:
            # Create a temporary module name based on file path
            module_path = file_path.with_suffix("").parts
            if "scripts" in module_path:
                # Skip scripts directory for import tests
                pass
            else:
                # Try to compile the file
                compile(source, str(file_path), "exec")
        except Exception as e:
            issues.append(("import_error", str(e)))
    
    return len(issues) == 0, issues


def find_python_files(path: Path, exclude_dirs: set[str] = None) -> list[Path]:
    """Find all Python files in the given path."""
    if exclude_dirs is None:
        exclude_dirs = {"venv", ".venv", "__pycache__", ".git", "migrations", "node_modules"}
    
    if path.is_file():
        return [path] if path.suffix == ".py" else []
    
    python_files = []
    for root, dirs, files in os.walk(path):
        # Remove excluded directories
        dirs[:] = [d for d in dirs if d not in exclude_dirs]
        
        for file in files:
            if file.endswith(".py"):
                python_files.append(Path(root) / file)
    
    return python_files


def main():
    """Main validation function."""
    parser = argparse.ArgumentParser(
        description="Validate ContextLogger migration"
    )
    parser.add_argument(
        "path",
        nargs="?",
        default=".",
        help="Directory or file to validate (default: current directory)"
    )
    parser.add_argument(
        "--strict",
        action="store_true",
        help="Fail on any warnings (not just errors)"
    )
    parser.add_argument(
        "--test-imports",
        action="store_true",
        help="Try to import each file to check for errors"
    )
    parser.add_argument(
        "--exclude",
        action="append",
        help="Additional directories to exclude (can be used multiple times)"
    )
    
    args = parser.parse_args()
    
    path = Path(args.path)
    if not path.exists():
        print(f"Error: Path does not exist: {path}")
        sys.exit(1)
    
    # Set up exclusions
    exclude_dirs = {"venv", ".venv", "__pycache__", ".git", "migrations", "node_modules"}
    if args.exclude:
        exclude_dirs.update(args.exclude)
    
    # Find all Python files
    python_files = find_python_files(path, exclude_dirs)
    
    if not python_files:
        print("No Python files found to validate.")
        return
    
    print(f"Validating {len(python_files)} Python files...")
    if args.test_imports:
        print("Import testing enabled - this may take longer")
    print()
    
    # Validate each file
    result = ValidationResult()
    result.total_files = len(python_files)
    
    for file_path in python_files:
        is_valid, issues = validate_file(file_path, test_imports=args.test_imports)
        
        if is_valid:
            result.valid_files += 1
        else:
            result.files_with_issues.append(str(file_path))
            
            for issue_type, issue_data in issues:
                if issue_type == "old_logging":
                    result.files_with_old_logging.append((str(file_path), issue_data))
                elif issue_type == "missing_import":
                    result.files_missing_import.append(str(file_path))
                elif issue_type == "syntax_error":
                    result.files_with_syntax_errors.append((str(file_path), issue_data))
                elif issue_type == "import_error":
                    result.files_with_import_errors.append((str(file_path), issue_data))
    
    # Check for common issues
    if result.total_files == result.valid_files:
        result.warnings.append("Consider running tests to ensure functionality")
    
    # Print summary
    result.print_summary()
    
    # Exit with appropriate code
    if result.has_errors:
        sys.exit(1)
    elif args.strict and result.has_warnings:
        sys.exit(1)
    else:
        sys.exit(0)


if __name__ == "__main__":
    main()