#!/usr/bin/env python
"""
Production smoke test for GCS integration.

This script performs basic health checks to ensure GCS functionality is working correctly:
1. Verifies GCS configuration
2. Tests basic upload/download operations
3. Checks model integration
4. Validates form functionality

Usage:
    python scripts/gcs_smoke_test.py [--verbose]
"""

import argparse
import os
import sys
import time
from datetime import datetime

# Add project root to Python path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Setup Django
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "SocialManager.settings")
import django

django.setup()

from django.conf import settings  # noqa: E402
from django.utils import timezone  # noqa: E402

from core.storage.gcs_service import GCSService  # noqa: E402
from instagram_manager.forms import ProfileImportForm  # noqa: E402
from instagram_manager.instagram_api.data_handlers.media_handler import MediaHandler  # noqa: E402
from instagram_manager.models import InstagramMedia, InstagramPost, InstagramProfile  # noqa: E402


class Colors:
    """Terminal colors for output."""
    GREEN = "\033[92m"
    YELLOW = "\033[93m"
    RED = "\033[91m"
    BLUE = "\033[94m"
    ENDC = "\033[0m"
    BOLD = "\033[1m"


class SmokeTest:
    """GCS smoke test runner."""
    
    def __init__(self, verbose=False):
        self.verbose = verbose
        self.passed = 0
        self.failed = 0
        self.warnings = 0
    
    def log(self, message, level="info"):
        """Log message with color coding."""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        
        if level == "success":
            print(f"{Colors.GREEN}✓{Colors.ENDC} [{timestamp}] {message}")
        elif level == "error":
            print(f"{Colors.RED}✗{Colors.ENDC} [{timestamp}] {message}")
        elif level == "warning":
            print(f"{Colors.YELLOW}⚠{Colors.ENDC} [{timestamp}] {message}")
        elif level == "info":
            print(f"{Colors.BLUE}ℹ{Colors.ENDC} [{timestamp}] {message}")
        elif self.verbose:
            print(f"  [{timestamp}] {message}")
    
    def test_configuration(self):
        """Test 1: Verify GCS configuration."""
        self.log("Testing GCS configuration...", "info")
        
        try:
            # Check bucket name
            if not settings.GCS_BUCKET_NAME:
                self.log("GCS_BUCKET_NAME not configured", "error")
                self.failed += 1
                return False
            
            self.log(f"GCS bucket: {settings.GCS_BUCKET_NAME}", "debug")
            
            # Check credentials
            if "GOOGLE_APPLICATION_CREDENTIALS" not in os.environ:
                self.log("GOOGLE_APPLICATION_CREDENTIALS not set (using default credentials)", "warning")
                self.warnings += 1
            
            self.passed += 1
            self.log("GCS configuration OK", "success")
            return True
            
        except Exception as e:
            self.log(f"Configuration check failed: {e}", "error")
            self.failed += 1
            return False
    
    def test_gcs_service(self):
        """Test 2: Verify GCS service initialization and connectivity."""
        self.log("Testing GCS service...", "info")
        
        try:
            # Initialize service
            service = GCSService()
            
            # Check availability
            if service.is_available():
                self.log("GCS service is available", "success")
                self.passed += 1
                return True
            else:
                self.log("GCS service is not available", "error")
                self.failed += 1
                return False
                
        except Exception as e:
            self.log(f"GCS service test failed: {e}", "error")
            self.failed += 1
            return False
    
    def test_upload_functionality(self):
        """Test 3: Test basic upload functionality."""
        self.log("Testing GCS upload...", "info")
        
        try:
            service = GCSService()
            
            # Create test data
            test_content = b"GCS Smoke Test - " + datetime.now().isoformat().encode()
            test_filename = f"smoke_test_{int(time.time())}.txt"
            
            # Upload file
            start_time = time.time()
            url = service.upload_file(
                file_content=test_content,
                original_filename=test_filename,
                content_type="text/plain",
                folder="smoke_tests"
            )
            upload_time = time.time() - start_time
            
            if url:
                self.log(f"Upload successful: {url}", "debug")
                self.log(f"Upload time: {upload_time:.2f}s", "debug")
                
                # Verify URL format
                if url.startswith("https://storage.googleapis.com/"):
                    self.log("GCS upload OK", "success")
                    self.passed += 1
                    
                    # Try to delete test file
                    if self.verbose:
                        self.log("Cleaning up test file...", "debug")
                    
                    return True
                else:
                    self.log(f"Invalid GCS URL format: {url}", "error")
                    self.failed += 1
                    return False
            else:
                self.log("Upload returned no URL", "error")
                self.failed += 1
                return False
                
        except Exception as e:
            self.log(f"Upload test failed: {e}", "error")
            self.failed += 1
            return False
    
    def test_model_integration(self):
        """Test 4: Test InstagramMedia model with GCS."""
        self.log("Testing model integration...", "info")
        
        try:
            # Create test objects
            profile = InstagramProfile.objects.create(
                profile_id=f"smoke_test_{int(time.time())}",
                username="smoke_test_user",
                full_name="Smoke Test User"
            )
            
            post = InstagramPost.objects.create(
                external_id=f"SMOKE_{int(time.time())}",
                profile=profile,
                shortcode=f"SMOKE{int(time.time())}",
                caption="GCS smoke test post",
                posted_at=timezone.now()
            )
            
            media = InstagramMedia.objects.create(
                external_id=f"MEDIA_SMOKE_{int(time.time())}",
                post=post,
                media_type="photo",
                media_url="https://example.com/test.jpg"
            )
            
            # Test GCS URL field
            gcs_url = "https://storage.googleapis.com/test-bucket/smoke_test.jpg"
            media.gcs_url = gcs_url
            media.save()
            
            # Verify it was saved
            media.refresh_from_db()
            if media.gcs_url == gcs_url:
                self.log("GCS URL field working correctly", "debug")
            
            # Test get_display_url priority
            display_url = media.get_display_url()
            if display_url == gcs_url:
                self.log("URL priority (GCS > external) OK", "success")
                self.passed += 1
            else:
                self.log(f"URL priority incorrect: {display_url}", "error")
                self.failed += 1
            
            # Cleanup
            media.delete()
            post.delete()
            profile.delete()
            
            return True
            
        except Exception as e:
            self.log(f"Model integration test failed: {e}", "error")
            self.failed += 1
            return False
    
    def test_media_handler(self):
        """Test 5: Test MediaHandler with GCS enabled."""
        self.log("Testing MediaHandler integration...", "info")
        
        try:
            # Create handler with GCS
            handler = MediaHandler(save_to_gcs=True)
            
            if handler._gcs_service:
                self.log("MediaHandler initialized with GCS", "success")
                self.passed += 1
                return True
            else:
                self.log("MediaHandler GCS initialization failed", "warning")
                self.warnings += 1
                return True  # Not a critical failure
                
        except Exception as e:
            self.log(f"MediaHandler test failed: {e}", "error")
            self.failed += 1
            return False
    
    def test_form_integration(self):
        """Test 6: Test form GCS checkbox."""
        self.log("Testing form integration...", "info")
        
        try:
            # Test ProfileImportForm
            form = ProfileImportForm()
            
            if "save_media_to_gcs" in form.fields:
                field = form.fields["save_media_to_gcs"]
                
                # Check visibility based on configuration
                if settings.GCS_BUCKET_NAME:
                    if field.widget.input_type != "hidden":
                        self.log("GCS checkbox visible in forms", "success")
                        self.passed += 1
                    else:
                        self.log("GCS checkbox hidden despite configuration", "error")
                        self.failed += 1
                else:
                    if field.widget.input_type == "hidden":
                        self.log("GCS checkbox correctly hidden", "success")
                        self.passed += 1
                    else:
                        self.log("GCS checkbox visible without configuration", "error")
                        self.failed += 1
            else:
                self.log("save_media_to_gcs field missing from form", "error")
                self.failed += 1
                
            return True
            
        except Exception as e:
            self.log(f"Form integration test failed: {e}", "error")
            self.failed += 1
            return False
    
    def run_all_tests(self):
        """Run all smoke tests."""
        print(f"\n{Colors.BOLD}=== GCS Integration Smoke Test ==={Colors.ENDC}")
        print(f"Environment: {settings.ENVIRONMENT if hasattr(settings, 'ENVIRONMENT') else 'Unknown'}")
        print(f"Bucket: {settings.GCS_BUCKET_NAME or 'Not configured'}")
        print(f"Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        
        # Run tests
        tests = [
            self.test_configuration,
            self.test_gcs_service,
            self.test_upload_functionality,
            self.test_model_integration,
            self.test_media_handler,
            self.test_form_integration,
        ]
        
        for test in tests:
            try:
                test()
            except Exception as e:
                self.log(f"Unexpected error in {test.__name__}: {e}", "error")
                self.failed += 1
            print()  # Blank line between tests
        
        # Summary
        total = self.passed + self.failed
        print(f"{Colors.BOLD}=== Summary ==={Colors.ENDC}")
        print(f"{Colors.GREEN}Passed: {self.passed}/{total}{Colors.ENDC}")
        
        if self.warnings > 0:
            print(f"{Colors.YELLOW}Warnings: {self.warnings}{Colors.ENDC}")
        
        if self.failed > 0:
            print(f"{Colors.RED}Failed: {self.failed}/{total}{Colors.ENDC}")
            print(f"\n{Colors.RED}SMOKE TEST FAILED!{Colors.ENDC}")
            return False
        else:
            print(f"\n{Colors.GREEN}ALL SMOKE TESTS PASSED!{Colors.ENDC}")
            return True


def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(description="GCS Integration Smoke Test")
    parser.add_argument(
        "--verbose", "-v",
        action="store_true",
        help="Enable verbose output"
    )
    args = parser.parse_args()
    
    # Run tests
    tester = SmokeTest(verbose=args.verbose)
    success = tester.run_all_tests()
    
    # Exit with appropriate code
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()