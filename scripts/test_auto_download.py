#!/usr/bin/env python
"""Test automatic media download during post import"""

import os
import sys
# Add parent directory to Python path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import django

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'scripts.settings')
django.setup()

from instagram_manager.services.post_service import PostService  # noqa: E402
from instagram_manager.models import InstagramMedia, InstagramProfile  # noqa: E402


def main():
    """Main function for testing automatic media download."""
    # Test importing posts with automatic download
    post_service = PostService()

    # Check initial state
    initial_count = InstagramMedia.objects.filter(is_downloaded=True).count()
    print(f"Initial downloaded media count: {initial_count}")

    # Import one new post
    try:
        # Get a profile
        profile = InstagramProfile.objects.first()
        
        if not profile:
            # Create test profile
            from django.conf import settings
            profile = InstagramProfile.objects.create(
                username=settings.INSTAGRAM_TEST_USERNAME,
                profile_id="test_profile",
                full_name="Test Profile",
                bio="Test profile for script",
                follower_count=100,
                following_count=50,
                post_count=10,
            )
            print(f"Created test profile: {profile.username}")
            
        if profile:
            print(f"\nImporting 1 new post for {profile.username}...")
            posts = post_service.import_posts_for_profile(profile, limit=1)
            
            if posts:
                post = posts[0]
                print(f"Imported post: {post.shortcode}")
                
                # Check media status
                for media in post.media.all():
                    print(f"  Media {media.media_id}: downloaded={media.is_downloaded}, type={media.media_type}")
                    
            # Check new count
            new_count = InstagramMedia.objects.filter(is_downloaded=True).count()
            print(f"\nNew downloaded media count: {new_count}")
            print(f"Newly downloaded: {new_count - initial_count}")
        else:
            print("No profiles found. Please import a profile first.")
            
    except Exception as e:
        print(f"Error: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()