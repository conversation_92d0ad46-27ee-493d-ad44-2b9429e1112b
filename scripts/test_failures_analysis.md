# Test Failures Analysis Report

## Summary

The test suite has 177 failures out of 204 tests. The failures can be grouped into the following categories:

1. **Database Migration Issues** (143 failures) - Missing database tables
2. **Authentication/Mock Issues** (12 failures) - Incorrect mock setup
3. **Command Argument Parsing** (8 failures) - Missing/incorrect command arguments
4. **SystemExit Assertions** (5 failures) - Expected SystemExit not raised
5. **Method/Attribute Errors** (6 failures) - Missing methods or attributes
6. **Import/Module Issues** (3 failures) - Missing imports or modules

## Detailed Analysis by Category

### 1. Database Migration Issues (70% of failures)

**Root Cause**: Tests are trying to create model instances but the database tables don't exist because migrations haven't been run for the test database.

**Affected Tests**:
- All Instagram model tests (`instagram_manager/test_models.py`)
- All Instagram repository tests (`instagram_manager/test_repositories.py`)
- All Instagram service tests (`instagram_manager/test_services.py`)
- All Telegram model tests (`telegram_manager/test_models.py`)
- All Telegram repository tests (`telegram_manager/test_repositories.py`)
- All Telegram service tests (`telegram_manager/test_services.py`)
- Admin tests that create model instances

**Error Pattern**:
```
sqlite3.OperationalError: no such table: instagram_profiles
sqlite3.OperationalError: no such table: telegram_manager_telegramchat
```

**Fix Approach**:
1. Ensure migrations are run before tests
2. Add `@pytest.mark.django_db` decorator to all tests that access the database
3. Check that test settings are properly configured to run migrations

### 2. Authentication/Mock Issues (6% of failures)

**Root Cause**: The TelegramAPIClient tests are not properly mocking the Telethon client, causing real authentication attempts.

**Affected Tests**:
- `TelegramAPIClientTest` - All authentication-related tests
- `TelegramAPIClientIntegrationTest` - Integration tests

**Error Pattern**:
```python
core.exceptions.AuthenticationError: [AUTH_ERROR] Verification code required
```

**Fix Approach**:
1. Mock the TelegramClient constructor to return the mock object
2. Ensure all async methods are properly mocked with AsyncMock
3. Mock the authentication flow to bypass real API calls

### 3. Command Argument Parsing Issues (4% of failures)

**Root Cause**: The new_telegram_users command file exists but isn't tracked in git, and the tests expect different argument handling.

**Affected Tests**:
- `NewTelegramUsersCommandTest` - All tests
- Command tests expecting specific argument parsing

**Error Pattern**:
- Missing command file in git
- Incorrect argument parsing in tests

**Fix Approach**:
1. Add `new_telegram_users.py` to git tracking
2. Update tests to match the actual command implementation
3. Ensure command arguments are properly defined

### 4. SystemExit Assertion Issues (2.5% of failures)

**Root Cause**: Tests expect SystemExit to be raised when credentials are missing, but the code might be handling errors differently.

**Affected Tests**:
- `NewBaseTelegramCommandTest::test_get_credentials_missing`
- Other command tests expecting SystemExit

**Error Pattern**:
```python
AssertionError: SystemExit not raised
```

**Fix Approach**:
1. Check if the command is using `self.error()` which raises CommandError instead
2. Update tests to expect CommandError or the actual exception raised
3. Verify the error handling flow in base commands

### 5. Method/Attribute Errors (3% of failures)

**Root Cause**: Tests are calling methods or accessing attributes that don't exist on the objects.

**Affected Tests**:
- Service tests expecting certain methods
- Repository tests with incorrect method names

**Fix Approach**:
1. Review the actual implementation to find correct method names
2. Update tests to match the implementation
3. Add missing methods if they should exist

### 6. Import/Module Issues (1.5% of failures)

**Root Cause**: Some modules or classes are not properly imported or don't exist.

**Affected Tests**:
- Tests importing from incorrect paths
- Tests expecting classes that have been renamed

**Fix Approach**:
1. Fix import paths in test files
2. Update class names to match current implementation
3. Ensure all required modules are present

## Recommended Fix Order

1. **Fix Database Issues First** (Priority 1)
   - Add django_db markers to all database tests
   - Ensure migrations are created and run
   - This will fix 70% of failures

2. **Fix Mock Setup** (Priority 2)
   - Properly mock TelegramClient in tests
   - Fix authentication flow mocking
   - This will fix another 6% of failures

3. **Fix Command Issues** (Priority 3)
   - Add new_telegram_users.py to git
   - Update command tests to match implementation
   - Fix SystemExit vs CommandError expectations

4. **Fix Remaining Issues** (Priority 4)
   - Update method names and imports
   - Fix any remaining test logic issues

## Quick Fixes

### 1. Add django_db marker to test classes
```python
import pytest

@pytest.mark.django_db
class TestClassName(TestCase):
    ...
```

### 2. Create and run migrations
```bash
uv run python manage.py makemigrations
uv run python manage.py migrate
```

### 3. Fix mock setup for TelegramClient
```python
@patch('telegram_manager.telegram_api.new_telegram_client.TelegramClient')
def test_something(self, mock_client_class):
    mock_instance = AsyncMock()
    mock_client_class.return_value = mock_instance
    ...
```

### 4. Track new_telegram_users.py
```bash
git add telegram_manager/management/commands/new_telegram_users.py
```