#!/usr/bin/env python
import os
import sys
# Add parent directory to Python path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import django
import logging
import time
import json

# Django setup
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'scripts.settings')
django.setup()

# Настройка детального логирования
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

# Отключаем логи urllib3 чтобы не засорять вывод
logging.getLogger("urllib3").setLevel(logging.WARNING)

logger = logging.getLogger(__name__)

from instagram_manager.instagram_api import BrightDataClient  # noqa: E402

def test_post_loading_direct():
    """Тест прямой загрузки через trigger_discover"""
    from django.conf import settings
    
    client = BrightDataClient()
    username = settings.INSTAGRAM_TEST_USERNAME
    
    logger.info(f"Starting test for user: {username}")
    logger.info("=" * 80)
    
    try:
        # Кеш больше не используется
        logger.info("Starting fresh test")
        
        # Формируем URL
        profile_url = f"https://www.instagram.com/{username}"
        logger.info(f"Profile URL: {profile_url}")
        
        # Запускаем discover
        start_time = time.time()
        logger.info("Triggering discover request...")
        
        # Напрямую вызываем trigger_discover для отладки
        snapshot_id = client.trigger_discover(
            "instagram_posts",
            [profile_url],
            discover_by="url"
        )
        
        logger.info(f"Snapshot ID received: {snapshot_id}")
        logger.info(f"Time to trigger: {time.time() - start_time:.2f}s")
        
        # Мониторим статус
        logger.info("\nMonitoring snapshot status...")
        logger.info("-" * 40)
        
        attempt = 0
        max_attempts = 150  # 150 * 3 сек = 7.5 минут для полного тестирования новых таймаутов
        
        while attempt < max_attempts:
            status_start = time.time()
            status = client.get_snapshot_status(snapshot_id)
            status_time = time.time() - status_start
            
            logger.info(
                f"Attempt {attempt + 1}: "
                f"Status = {status.get('status', 'unknown')} "
                f"(request took {status_time:.2f}s)"
            )
            
            # Логируем полный ответ статуса
            if status:
                logger.debug(f"Full status response: {json.dumps(status, indent=2)}")
            
            if status.get("status") == "ready":
                logger.info(f"\n✅ Snapshot ready! Total time: {time.time() - start_time:.2f}s")
                
                # Получаем данные
                data_start = time.time()
                posts_data = client.get_snapshot_data(snapshot_id)
                data_time = time.time() - data_start
                
                logger.info(f"Data retrieval took: {data_time:.2f}s")
                logger.info(f"Posts count: {len(posts_data)}")
                
                if posts_data:
                    logger.info(f"First post example: {json.dumps(posts_data[0], indent=2)[:500]}...")
                
                break
                
            elif status.get("status") == "failed":
                error_msg = status.get("error", "Unknown error")
                logger.error(f"\n❌ Snapshot failed: {error_msg}")
                logger.error(f"Full error response: {json.dumps(status, indent=2)}")
                break
            
            # Ждем перед следующей проверкой
            time.sleep(3)
            attempt += 1
            
        if attempt >= max_attempts:
            logger.warning(f"\n⚠️ Timeout after {max_attempts} attempts ({time.time() - start_time:.2f}s)")
            
    except Exception as e:
        logger.error(f"\n💥 Exception occurred: {type(e).__name__}: {str(e)}")
        import traceback
        traceback.print_exc()
        
    logger.info("\n" + "=" * 80)
    logger.info("Test completed")

def test_get_posts_method():
    """Тест метода get_posts с новыми таймаутами"""
    from django.conf import settings
    
    client = BrightDataClient()
    username = settings.INSTAGRAM_TEST_USERNAME
    
    logger.info(f"Testing get_posts method for user: {username}")
    logger.info("=" * 80)
    
    start_time = time.time()
    
    try:
        # Кеш больше не используется
        logger.info("Starting fresh test")
        
        # Вызываем полный метод get_posts
        logger.info("Calling get_posts method...")
        posts = client.get_posts(username)
        
        total_time = time.time() - start_time
        logger.info(f"\n✅ get_posts completed! Total time: {total_time:.2f}s")
        logger.info(f"Posts count: {len(posts)}")
        
        if posts:
            logger.info(f"First post sample: {json.dumps(posts[0], indent=2)[:300]}...")
        
    except Exception as e:
        total_time = time.time() - start_time
        logger.error(f"\n❌ get_posts failed after {total_time:.2f}s")
        logger.error(f"Exception: {type(e).__name__}: {str(e)}")
        import traceback
        traceback.print_exc()
    
    logger.info("\n" + "=" * 80)
    logger.info("get_posts test completed")

if __name__ == "__main__":
    logger.info("Running Instagram timeout tests...")
    logger.info("Note: Posts loading now takes 5-7 minutes from BrightData")
    
    # Запускаем оба теста
    test_post_loading_direct()
    print("\n" + "="*50 + "\n")
    test_get_posts_method()