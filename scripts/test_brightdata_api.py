#!/usr/bin/env python
import os
import sys

# Add parent directory to Python path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import django
import logging
import time

# Django setup
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "scripts.settings")
django.setup()

logging.basicConfig(
    level=logging.DEBUG, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

from instagram_manager.instagram_api import BrightDataClient  # noqa: E402


def test_api_connection():
    """Test BrightData API connection and post fetching"""
    from django.conf import settings
    
    client = BrightDataClient()
    test_username = settings.INSTAGRAM_TEST_USERNAME

    try:
        logger.info("Testing BrightData API connection...")

        # Test 1: Simple profile fetch
        logger.info(f"Test 1: Fetching profile data for {test_username}...")
        profile_data = client.get_profile(test_username)
        logger.info(
            f"Profile fetch successful: {profile_data.get('username', 'Unknown')}"
        )

    except Exception as e:
        logger.error(f"Profile fetch failed: {type(e).__name__}: {str(e)}")

    try:
        # Test 2: Posts fetch with discover API
        logger.info("\nTest 2: Testing posts fetch with discover API...")
        profile_url = f"https://www.instagram.com/{test_username}"

        # Trigger discover
        logger.info("Triggering discover request...")
        snapshot_id = client.trigger_discover("instagram_posts", [profile_url])
        logger.info(f"Snapshot ID: {snapshot_id}")

        # Check status
        for i in range(10):
            status = client.get_snapshot_status(snapshot_id)
            logger.info(f"Attempt {i+1}: Status = {status.get('status', 'unknown')}")

            if status.get("status") == "ready":
                logger.info("Snapshot ready!")
                break
            elif status.get("status") == "failed":
                logger.error(f"Snapshot failed: {status.get('error', 'Unknown error')}")
                break

            time.sleep(3)

    except Exception as e:
        logger.error(f"Posts fetch failed: {type(e).__name__}: {str(e)}")
        import traceback

        traceback.print_exc()


def main():
    """Main function for testing BrightData API."""
    test_api_connection()


if __name__ == "__main__":
    main()
