"""Utility functions for scripts."""

import os
import sys


def setup_django():
    """Setup Django with appropriate settings based on execution context."""
    # Add parent directory to Python path
    sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

    # Only use scripts settings if not already in test mode
    if os.environ.get("DJANGO_SETTINGS_MODULE") != "tests.settings":
        os.environ.setdefault("DJANGO_SETTINGS_MODULE", "scripts.settings")

    import django

    django.setup()


def is_test_mode():
    """Check if running in test mode."""
    return os.environ.get("DJANGO_SETTINGS_MODULE") == "tests.settings"


def ensure_test_data():
    """Ensure test data exists for scripts when running in test mode."""
    if not is_test_mode():
        return

    from django.conf import settings

    from instagram_manager.models import InstagramProfile

    # Create a test profile if none exists
    if not InstagramProfile.objects.exists():
        InstagramProfile.objects.create(
            username=settings.INSTAGRAM_TEST_USERNAME,
            profile_id="test_instagram",
            full_name="Instagram",
            bio="Test profile for scripts",
            follower_count=1000,
            following_count=100,
            post_count=50,
        )
