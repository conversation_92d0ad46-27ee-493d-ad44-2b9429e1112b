#!/bin/bash
# Development startup script for MCP server

echo "🚀 Starting MCP Server in development mode..."

# Activate virtual environment if exists
if [ -f ".venv/bin/activate" ]; then
    source .venv/bin/activate
fi

# Set development environment
export DJANGO_SETTINGS_MODULE=SocialManager.settings.local
export DEBUG=True
export MCP_LOG_LEVEL=DEBUG

# Run the server with auto-reload
uvicorn mcp_server.main:app --reload --host 0.0.0.0 --port 8000