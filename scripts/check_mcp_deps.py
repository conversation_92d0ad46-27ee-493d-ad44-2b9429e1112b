#!/usr/bin/env python
"""Check if all MCP dependencies are properly installed"""

import sys


def check_imports():
    """Check required imports"""
    required_modules = [
        ("fastmcp", "FastMCP"),
        ("uvicorn", "Uvicorn"),
        ("pydantic", "Pydantic"),
        ("asyncpg", "AsyncPG"),
        ("httpx", "HTTPX"),
        ("asgiref.sync", "Django Async Support"),
    ]
    
    missing = []
    
    for module, name in required_modules:
        try:
            __import__(module)
            print(f"✅ {name} installed")
        except ImportError:
            print(f"❌ {name} missing")
            missing.append(name)
    
    if missing:
        print(f"\n❌ Missing dependencies: {', '.join(missing)}")
        print("Run: uv pip install -r requirements.txt")
        sys.exit(1)
    else:
        print("\n✅ All dependencies installed!")

if __name__ == "__main__":
    check_imports()