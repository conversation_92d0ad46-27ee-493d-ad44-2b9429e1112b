#!/usr/bin/env python
"""Test script to verify Instagram photo display in admin"""

import os
import sys
# Add parent directory to Python path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import django

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'scripts.settings')
django.setup()

from instagram_manager.models import InstagramMedia, InstagramPost  # noqa: E402


def main():
    """Main function for testing photo display."""
    # Check downloaded media
    downloaded_media = InstagramMedia.objects.filter(is_downloaded=True, media_type='photo')
    print(f"\nDownloaded photos: {downloaded_media.count()}")

    for media in downloaded_media[:5]:
        print(f"\nMedia ID: {media.media_id}")
        print(f"Post: {media.post.shortcode}")
        print(f"Local path: {media.local_path}")
        print(f"Display URL: {media.get_display_url()}")
        print(f"File exists: {os.path.exists(media.local_path.path) if media.local_path else False}")

    # Check posts with photos
    posts_with_photos = InstagramPost.objects.filter(media__media_type='photo').distinct()[:5]
    print(f"\n\nPosts with photos: {posts_with_photos.count()}")

    for post in posts_with_photos:
        photo = post.media.filter(media_type='photo').first()
        if photo:
            print(f"\nPost: {post.shortcode}")
            print(f"Photo downloaded: {photo.is_downloaded}")
            print(f"URL: {photo.get_display_url()}")


if __name__ == "__main__":
    main()