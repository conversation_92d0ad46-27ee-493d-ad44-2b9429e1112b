#!/usr/bin/env python
"""
Скрипт для генерации документации проекта
"""
import sys
from pathlib import Path

# Добавляем корневую директорию проекта в PYTHONPATH
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from docs.generators import ProjectDocumentationGenerator  # noqa: E402


def main():
    """Основная функция генерации документации"""
    print("🚀 Starting documentation generation...")
    
    # Создаем генератор документации
    generator = ProjectDocumentationGenerator(project_root)
    
    try:
        # Генерируем всю документацию
        generated_files = generator.generate_all_documentation()
        
        print("\n✅ Documentation generated successfully!")
        print(f"📁 Generated {len(generated_files)} files:")
        for file_path in generated_files:
            print(f"   - {Path(file_path).relative_to(project_root)}")
        
        print(f"\n📂 Documentation available in: {generator.docs_dir}")
        
    except Exception as e:
        print(f"\n❌ Error generating documentation: {e!s}")
        sys.exit(1)


if __name__ == "__main__":
    main()