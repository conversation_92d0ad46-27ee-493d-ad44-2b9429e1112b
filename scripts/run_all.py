#!/usr/bin/env python
"""
Run all scripts in the scripts directory.

Usage:
    python scripts/run_all.py                    # Run all scripts
    python scripts/run_all.py --clean           # Clean DB before running
    python scripts/run_all.py --only script1.py script2.py  # Run specific scripts
    python scripts/run_all.py --exclude script1.py          # Exclude scripts
    python scripts/run_all.py --fail-fast       # Stop on first failure
"""

import argparse
import os
import subprocess
import sys
import time
from pathlib import Path

# Add parent directory to Python path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))


# Scripts to always exclude
EXCLUDE_SCRIPTS = {
    "__init__.py",
    "settings.py",
    "clean_test_db.py",
    "run_all.py",  # Don't run ourselves
}


class ScriptRunner:
    """Runner for executing multiple scripts with nice output."""
    
    def __init__(self, clean_db=False, fail_fast=False):
        self.clean_db = clean_db
        self.fail_fast = fail_fast
        self.results = []
        
    def find_scripts(self, only=None, exclude=None):
        """Find all Python scripts to run."""
        scripts_dir = Path(__file__).parent
        scripts = []
        
        # Get all .py files
        for file in scripts_dir.glob("*.py"):
            if file.name in EXCLUDE_SCRIPTS:
                continue
                
            # Handle --only filter
            if only and file.name not in only:
                continue
                
            # Handle --exclude filter
            if exclude and file.name in exclude:
                continue
                
            # Skip test_*.py files that look like unit tests
            if file.name.startswith("test_") and file.name.endswith(".py"):
                # But include our specific test scripts
                if file.name.startswith("test_") and any(
                    keyword in file.name for keyword in 
                    ["brightdata", "import", "auto", "instagram", "photo", "post"]
                ):
                    scripts.append(file)
            elif file.name.endswith(".py"):
                scripts.append(file)
                
        return sorted(scripts)
    
    def clean_database(self):
        """Clean the database before running scripts."""
        print("🧹 Cleaning database...")
        clean_script = Path(__file__).parent / "clean_test_db.py"
        result = subprocess.run(
            [sys.executable, str(clean_script)],
            capture_output=True,
            text=True
        )
        if result.returncode == 0:
            print("✅ Database cleaned successfully")
            
            # Run migrations after cleaning
            print("🔄 Running migrations...")
            manage_py = Path(__file__).parent.parent / "manage.py"
            migrate_result = subprocess.run(
                [sys.executable, str(manage_py), "migrate", "--run-syncdb"],
                capture_output=True,
                text=True,
                env={**os.environ, "DJANGO_SETTINGS_MODULE": "scripts.settings"}
            )
            
            if migrate_result.returncode == 0:
                print("✅ Migrations completed successfully\n")
                return True
            else:
                print(f"❌ Failed to run migrations: {migrate_result.stderr}\n")
                return False
        else:
            print(f"❌ Failed to clean database: {result.stderr}\n")
            return False
    
    def run_script(self, script_path: Path) -> tuple[bool, float, str]:
        """Run a single script and return success, duration, and error message."""
        start_time = time.time()
        
        try:
            result = subprocess.run(
                [sys.executable, str(script_path)],
                capture_output=True,
                text=True,
                timeout=300  # 5 minute timeout per script
            )
            
            duration = time.time() - start_time
            
            if result.returncode == 0:
                return True, duration, ""
            else:
                # Extract error message
                error_lines = result.stderr.strip().split("\n")
                error_msg = error_lines[-1] if error_lines else "Unknown error"
                return False, duration, error_msg
                
        except subprocess.TimeoutExpired:
            duration = time.time() - start_time
            return False, duration, "Timeout (5 minutes)"
        except Exception as e:
            duration = time.time() - start_time
            return False, duration, str(e)
    
    def run_all(self, scripts: list[Path]):
        """Run all scripts and display results."""
        print(f"🚀 Running scripts from {Path(__file__).parent}")
        print("━" * 60)
        print()
        
        total_start = time.time()
        
        for i, script in enumerate(scripts, 1):
            print(f"[{i}/{len(scripts)}] Running {script.name}...")
            
            success, duration, error = self.run_script(script)
            self.results.append((script.name, success, duration, error))
            
            if success:
                print(f"✅ Success ({duration:.2f}s)")
            else:
                print(f"❌ Failed ({duration:.2f}s)")
                if error:
                    print(f"   Error: {error}")
            print()
            
            # Stop on first failure if requested
            if not success and self.fail_fast:
                print("⛔ Stopping due to --fail-fast")
                break
        
        total_duration = time.time() - total_start
        self.print_summary(total_duration)
    
    def print_summary(self, total_duration: float):
        """Print summary of all script runs."""
        print("━" * 60)
        
        successful = sum(1 for _, success, _, _ in self.results if success)
        total = len(self.results)
        
        print(f"📊 Summary: {successful}/{total} scripts completed successfully")
        print(f"⏱️  Total time: {total_duration:.2f}s")
        
        # List failed scripts
        failed = [(name, error) for name, success, _, error in self.results if not success]
        if failed:
            print("❌ Failed scripts:")
            for name, error in failed:
                print(f"   - {name}")
                if error:
                    print(f"     {error}")


def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(
        description="Run all scripts in the scripts directory",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog=__doc__
    )
    
    parser.add_argument(
        "--clean", 
        action="store_true",
        help="Clean the database before running scripts"
    )
    
    parser.add_argument(
        "--only",
        nargs="+",
        metavar="SCRIPT",
        help="Run only these specific scripts"
    )
    
    parser.add_argument(
        "--exclude",
        nargs="+",
        metavar="SCRIPT",
        help="Exclude these scripts from running"
    )
    
    parser.add_argument(
        "--fail-fast",
        action="store_true",
        help="Stop on first script failure"
    )
    
    args = parser.parse_args()
    
    # Initialize runner
    runner = ScriptRunner(
        clean_db=args.clean,
        fail_fast=args.fail_fast
    )
    
    # Clean database if requested
    if args.clean:
        if not runner.clean_database():
            sys.exit(1)
    
    # Find scripts to run
    scripts = runner.find_scripts(
        only=set(args.only) if args.only else None,
        exclude=set(args.exclude) if args.exclude else None
    )
    
    if not scripts:
        print("❌ No scripts found to run")
        sys.exit(1)
    
    # Run all scripts
    runner.run_all(scripts)
    
    # Exit with error if any scripts failed
    if any(not success for _, success, _, _ in runner.results):
        sys.exit(1)


if __name__ == "__main__":
    main()