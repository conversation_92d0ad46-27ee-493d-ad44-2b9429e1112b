#!/usr/bin/env python
"""Диагностика проблемы с загрузкой постов"""

import os
import sys
# Add parent directory to Python path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import django
import logging

os.environ.setdefault("DJANGO_SETTINGS_MODULE", "scripts.settings")
django.setup()

# Включаем подробное логирование
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)

from instagram_manager.services.post_service import PostService  # noqa: E402
from instagram_manager.models import (  # noqa: E402
    InstagramProfile,
    InstagramScrapingTask,
)


def main():
    """Main function for diagnosing post import issues."""
    try:
        # Пробуем загрузить посты
        service = PostService()

        # Получаем профиль
        profile = InstagramProfile.objects.first()
        if not profile:
            # Создаем тестовый профиль
            print("Создаем тестовый профиль...")
            from django.conf import settings
            profile = InstagramProfile.objects.create(
                username=settings.INSTAGRAM_TEST_USERNAME,
                profile_id="test_profile",
                full_name="Test Profile",
                bio="Test profile for script",
                follower_count=100,
                following_count=50,
                post_count=10,
            )
            print(f"Создан профиль: {profile.username}")

        print(f"Тестируем загрузку постов для профиля: {profile.username}")
        print(f"Текущее количество постов: {profile.posts.count()}")

        # Пробуем загрузить 1 пост
        print("\nПытаемся загрузить 1 пост...")
        posts = service.import_posts_for_profile(profile, limit=1)

        print(f"Загружено постов: {len(posts)}")

        # Проверяем последнюю задачу
        last_task = (
            InstagramScrapingTask.objects.filter(
                task_type="posts", target_identifier=profile.username
            )
            .order_by("-created_at")
            .first()
        )

        if last_task:
            print("\nПоследняя задача:")
            print(f"  Статус: {last_task.status}")
            print(f"  Загружено элементов: {last_task.items_scraped}")
            print(f"  Ошибка: {last_task.error_message}")

    except Exception as e:
        print(f"\nОШИБКА: {str(e)}")
        import traceback

        traceback.print_exc()

        # Проверяем логи Django
        print("\n\nПроверка логов Django:")
        from django.conf import settings

        print(f"DEBUG = {settings.DEBUG}")
        print(
            f"BRIGHTDATA_API_TOKEN настроен: {'Да' if settings.BRIGHTDATA_API_TOKEN else 'Нет'}"
        )


if __name__ == "__main__":
    main()
