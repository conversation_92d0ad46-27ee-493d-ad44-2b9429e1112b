#!/usr/bin/env python
"""
Simple script to test that environment variables are being loaded correctly from .env file.
"""
import os
import sys

# Add parent directory to Python path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Load environment variables from .env file
from decouple import config

import django
import logging

# Django setup
# Only use scripts settings if not already in test mode
if os.environ.get("DJANGO_SETTINGS_MODULE") != "tests.settings":
    os.environ.setdefault("DJANGO_SETTINGS_MODULE", "scripts.settings")
django.setup()

# Set up logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

def test_env_vars():
    """Test that environment variables are being loaded correctly from .env file."""
    # Test BRIGHTDATA_API_TOKEN
    brightdata_token = config("BRIGHTDATA_API_TOKEN", default="")
    logger.info(f"BRIGHTDATA_API_TOKEN: {'*' * 10}{brightdata_token[-5:] if brightdata_token else 'Not set'}")
    
    # Test INSTAGRAM_TEST_USERNAME
    instagram_username = config("INSTAGRAM_TEST_USERNAME", default="testuser")
    logger.info(f"INSTAGRAM_TEST_USERNAME: {instagram_username}")
    
    # Test FIELD_ENCRYPTION_KEY
    encryption_key = config("FIELD_ENCRYPTION_KEY", default="")
    logger.info(f"FIELD_ENCRYPTION_KEY: {'*' * 10}{encryption_key[-5:] if encryption_key else 'Not set'}")
    
    # Test a variable that doesn't exist
    non_existent = config("NON_EXISTENT_VAR", default="default_value")
    logger.info(f"NON_EXISTENT_VAR: {non_existent}")
    
    # Compare with direct os.environ access
    logger.info("\nComparing with direct os.environ access:")
    logger.info(f"os.environ.get('BRIGHTDATA_API_TOKEN'): {'*' * 10}{os.environ.get('BRIGHTDATA_API_TOKEN', '')[-5:] if os.environ.get('BRIGHTDATA_API_TOKEN', '') else 'Not set'}")
    logger.info(f"os.environ.get('INSTAGRAM_TEST_USERNAME'): {os.environ.get('INSTAGRAM_TEST_USERNAME', 'Not set')}")

if __name__ == "__main__":
    logger.info("Testing environment variables loading from .env file...")
    test_env_vars()
    logger.info("Test completed.")