#!/usr/bin/env python
"""Проверка состояния постов в базе данных"""

import os
import sys

# Add parent directory to Python path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import django

os.environ.setdefault("DJANGO_SETTINGS_MODULE", "scripts.settings")
django.setup()

from instagram_manager.models import InstagramMedia, InstagramPost, InstagramProfile  # noqa: E402


def main():
    """Main function for checking posts state."""
    print(f"Всего постов: {InstagramPost.objects.count()}")
    print(f"Всего медиа: {InstagramMedia.objects.count()}")
    print(f"Загружено медиа: {InstagramMedia.objects.filter(is_downloaded=True).count()}")

    print("\nПоследние 5 постов:")
    for post in InstagramPost.objects.order_by("-created_at")[:5]:
        media_info = []
        for m in post.media.all():
            media_info.append(f"{m.media_type}({'загружен' if m.is_downloaded else 'URL'})")
        print(f"  - {post.shortcode} ({post.posted_at.strftime('%Y-%m-%d')}) - медиа: {', '.join(media_info) if media_info else 'нет'}")

    print("\nПосты по профилям:")
    for profile in InstagramProfile.objects.all():
        print(f"  - {profile.username}: {profile.posts.count()} постов")


if __name__ == "__main__":
    main()