"""
Management command для массовой публикации комментариев в Instagram.
"""

import logging

from django.core.management.base import BaseCommand, CommandError

from instagram_manager.models import InstagramAccount, InstagramPost
from instagram_manager.services.playwright_comment_service import PlaywrightCommentService

logger = logging.getLogger(__name__)


class Command(BaseCommand):
    help = "Post bulk comments to Instagram posts"
    
    def add_arguments(self, parser):
        parser.add_argument(
            "--account",
            required=True,
            help="Instagram account username"
        )
        parser.add_argument(
            "--posts-file",
            help="File with post URLs (one per line)"
        )
        parser.add_argument(
            "--posts",
            nargs="+",
            help="List of post URLs"
        )
        parser.add_argument(
            "--template",
            default="Great post! {emoji}",
            help="Comment template (supports {emoji}, {number}, {username})"
        )
        parser.add_argument(
            "--randomize",
            action="store_true",
            default=True,
            help="Randomize comments (default: True)"
        )
        parser.add_argument(
            "--delay",
            type=int,
            default=30,
            help="Delay between comments in seconds (default: 30)"
        )
        parser.add_argument(
            "--dry-run",
            action="store_true",
            help="Show what would be posted without actually posting"
        )
        
    def handle(self, *args, **options):
        try:
            # Получаем аккаунт
            try:
                account = InstagramAccount.objects.get(username=options["account"])
            except InstagramAccount.DoesNotExist:
                raise CommandError(f"Account @{options['account']} not found")
            
            # Получаем список URL постов
            post_urls = []
            
            if options["posts_file"]:
                # Читаем из файла
                try:
                    with open(options["posts_file"]) as f:
                        post_urls = [line.strip() for line in f if line.strip()]
                except FileNotFoundError:
                    raise CommandError(f"File {options['posts_file']} not found")
            elif options["posts"]:
                # Используем переданный список
                post_urls = options["posts"]
            else:
                raise CommandError("Either --posts-file or --posts must be provided")
            
            if not post_urls:
                raise CommandError("No post URLs provided")
            
            self.stdout.write(f"Found {len(post_urls)} posts to comment on")
            
            # Находим посты в базе данных
            posts = []
            missing_posts = []
            
            for url in post_urls:
                # Извлекаем shortcode
                if "/p/" in url:
                    shortcode = url.split("/p/")[-1].split("/")[0].rstrip("/")
                elif "/reel/" in url:
                    shortcode = url.split("/reel/")[-1].split("/")[0].rstrip("/")
                else:
                    self.stdout.write(
                        self.style.WARNING(f"Invalid URL format: {url}")
                    )
                    continue
                
                try:
                    post = InstagramPost.objects.get(shortcode=shortcode)
                    posts.append(post)
                except InstagramPost.DoesNotExist:
                    missing_posts.append(url)
            
            if missing_posts:
                self.stdout.write(
                    self.style.WARNING(
                        f"\n{len(missing_posts)} posts not found in database:"
                    )
                )
                for url in missing_posts[:5]:  # Показываем первые 5
                    self.stdout.write(f"  - {url}")
                if len(missing_posts) > 5:
                    self.stdout.write(f"  ... and {len(missing_posts) - 5} more")
                    
                if not posts:
                    raise CommandError("No posts found in database")
                    
                self.stdout.write(
                    f"\nContinuing with {len(posts)} found posts..."
                )
            
            # Dry run
            if options["dry_run"]:
                self.stdout.write(
                    self.style.SUCCESS("\n🔍 DRY RUN MODE - No comments will be posted")
                )
                self.stdout.write(f"Account: @{account.username}")
                self.stdout.write(f"Template: {options['template']}")
                self.stdout.write(f"Randomize: {options['randomize']}")
                self.stdout.write(f"Delay: {options['delay']}s")
                self.stdout.write(f"\nWould comment on {len(posts)} posts:")
                
                for idx, post in enumerate(posts[:5], 1):
                    self.stdout.write(
                        f"  {idx}. @{post.profile.username} - {post.shortcode}"
                    )
                if len(posts) > 5:
                    self.stdout.write(f"  ... and {len(posts) - 5} more")
                    
                return
            
            # Запускаем массовую публикацию
            self._post_bulk_comments(
                account=account,
                posts=posts,
                template=options["template"],
                randomize=options["randomize"],
                delay=options["delay"]
            )
            
        except Exception as e:
            raise CommandError(f"Error: {e!s}")
        
    def _post_bulk_comments(self, account, posts, template, randomize, delay):
        """Функция для массовой публикации."""
        try:
            service = PlaywrightCommentService()
            
            self.stdout.write(
                self.style.SUCCESS(
                    f"\n🚀 Starting bulk comment posting...\n"
                    f"Account: @{account.username}\n"
                    f"Posts: {len(posts)}\n"
                    f"Template: {template}\n"
                )
            )
            
            # Prepare comments data for bulk posting
            comments_data = []
            for post in posts:
                comment_text = template
                # Simple template replacement
                if "{username}" in comment_text:
                    comment_text = comment_text.replace("{username}", post.profile.username)
                comments_data.append({
                    "post_url": post.post_url,
                    "comment_text": comment_text
                })
            
            results = service.post_bulk_comments_sync(
                account_id=account.id,
                comments_data=comments_data,
                delay_between_comments=delay
            )
            
            # Показываем результаты
            self.stdout.write(
                self.style.SUCCESS(
                    f"\n📊 Bulk posting completed!\n"
                    f"✅ Success: {results['successful']}\n"
                    f"❌ Failed: {results['failed']}\n"
                    f"📝 Total: {results['total']}"
                )
            )
            
            # Показываем детали ошибок если есть
            if results["errors"]:
                self.stdout.write("\n❌ Failed comments:")
                for error in results["errors"][:5]:
                    self.stdout.write(f"  - {error}")
                if len(results["errors"]) > 5:
                    self.stdout.write(
                        f"  ... and {len(results['errors']) - 5} more failures"
                    )
                    
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f"Error in bulk posting: {e!s}")
            )
            raise