import logging
from abc import abstractmethod

from django.conf import settings

from core.management.commands.base_async_command import BaseAsyncCommand
from instagram_manager.instagram_api import BrightDataClient

logger = logging.getLogger(__name__)


class BaseInstagramCommand(BaseAsyncCommand):
    """Базовый класс для Instagram management commands с поддержкой async"""
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.client = None
        
    def add_task_arguments(self, parser):
        """Базовые аргументы для Instagram команд"""
        parser.add_argument(
            "--verbose",
            action="store_true",
            help="Verbose output"
        )
        
    def handle(self, *args, **options):
        """Инициализация клиента и вызов базового обработчика"""
        self.verbose = options.get("verbose", False)
        
        # Проверяем наличие токена
        if not settings.BRIGHTDATA_API_TOKEN:
            self.stdout.write(
                self.style.ERROR("BRIGHTDATA_API_TOKEN not set in settings")
            )
            return
            
        # Инициализируем клиент для синхронного выполнения
        if not options.get("async"):
            self.client = BrightDataClient()
        
        # Вызываем базовый обработчик из BaseAsyncCommand
        super().handle(*args, **options)
    
    def _handle_sync(self, task_class, params, options):
        """Переопределяем для поддержки старого handle_command"""
        # Инициализируем клиент если еще не инициализирован
        if not self.client:
            self.client = BrightDataClient()
            
        # Проверяем, есть ли старый метод handle_command
        if hasattr(self, "handle_command"):
            # Используем старый метод для обратной совместимости
            self.handle_command(*[], **options)
        else:
            # Используем новый подход через задачи
            super()._handle_sync(task_class, params, options)
    
    @abstractmethod
    def get_task_class(self):
        """Должен быть определен в наследниках для поддержки async"""
        pass
        
    @abstractmethod
    def prepare_task_params(self, **options):
        """Должен быть определен в наследниках для поддержки async"""
        pass