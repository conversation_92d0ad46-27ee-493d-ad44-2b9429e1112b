"""
Management command для очистки медиа файлов Instagram.

Команда выполняет:
1. Удаление файлов без записей в БД (orphaned files)
2. Очистку пустых директорий
3. Проверку целостности медиа файлов
"""

import os

from django.conf import settings
from django.core.management.base import BaseCommand
from django.db.models import Q

from instagram_manager.models import InstagramMedia
from instagram_manager.repositories.media_repository import InstagramMediaRepository
from instagram_manager.signals import cleanup_empty_directories


class Command(BaseCommand):
    help = "Clean up Instagram media files (orphaned files and empty directories)"
    
    def add_arguments(self, parser):
        parser.add_argument(
            "--dry-run",
            action="store_true",
            help="Show what would be deleted without actually deleting",
        )
        parser.add_argument(
            "--check-integrity",
            action="store_true",
            help="Check integrity of media records (find records without files)",
        )
        parser.add_argument(
            "--fix-missing",
            action="store_true",
            help="Mark media as not downloaded if local file is missing",
        )
    
    def handle(self, *args, **options):
        """Execute the cleanup command."""
        self.stdout.write(self.style.MIGRATE_HEADING("Instagram Media Cleanup"))
        
        dry_run = options["dry_run"]
        if dry_run:
            self.stdout.write(self.style.WARNING("DRY RUN MODE - No files will be deleted"))
        
        # Check integrity if requested
        if options["check_integrity"]:
            self._check_integrity(fix_missing=options["fix_missing"])
        
        # Clean orphaned files
        self._clean_orphaned_files(dry_run=dry_run)
        
        # Clean empty directories
        if not dry_run:
            cleaned_dirs = cleanup_empty_directories()
            self.stdout.write(
                self.style.SUCCESS(f"Cleaned {cleaned_dirs} empty directories")
            )
    
    def _check_integrity(self, fix_missing=False):
        """Check integrity of media records."""
        self.stdout.write("\nChecking media integrity...")
        
        missing_files = []
        repo = InstagramMediaRepository()
        
        # Get all downloaded media
        downloaded_media = repo.filter_downloaded()
        
        for media in downloaded_media:
            if media.local_path and not os.path.exists(media.local_path.path):
                missing_files.append(media)
        
        if missing_files:
            self.stdout.write(
                self.style.WARNING(
                    f"Found {len(missing_files)} media records with missing files:"
                )
            )
            
            for media in missing_files[:10]:  # Show first 10
                self.stdout.write(
                    f"  - Media {media.media_id}: {media.local_path}"
                )
            
            if len(missing_files) > 10:
                self.stdout.write(f"  ... and {len(missing_files) - 10} more")
            
            if fix_missing:
                self.stdout.write("\nFixing missing files...")
                for media in missing_files:
                    media.is_downloaded = False
                    media.download_error = "Local file missing"
                    media.save(update_fields=["is_downloaded", "download_error"])
                
                self.stdout.write(
                    self.style.SUCCESS(
                        f"Fixed {len(missing_files)} media records"
                    )
                )
        else:
            self.stdout.write(
                self.style.SUCCESS("All media files are intact")
            )
    
    def _clean_orphaned_files(self, dry_run=False):
        """Clean orphaned media files."""
        self.stdout.write("\nLooking for orphaned files...")
        
        media_root = os.path.join(settings.MEDIA_ROOT, "instagram", "media")
        if not os.path.exists(media_root):
            self.stdout.write(self.style.WARNING("Media directory does not exist"))
            return
        
        # Get all file paths from database
        db_paths = set()
        
        # Get paths from InstagramMedia
        media_paths = InstagramMedia.objects.filter(
            is_downloaded=True
        ).exclude(
            Q(local_path__isnull=True) | Q(local_path="")
        ).values_list("local_path", flat=True)
        
        for path in media_paths:
            if path:
                full_path = os.path.join(settings.MEDIA_ROOT, path)
                db_paths.add(full_path)
                
                # Also add thumbnail path for videos
                if "video" in path.lower():
                    base_name = os.path.splitext(os.path.basename(path))[0]
                    thumbnail_path = os.path.join(
                        settings.MEDIA_ROOT,
                        "instagram/media/thumbnails",
                        f"{base_name}_thumbnail.jpg"
                    )
                    db_paths.add(thumbnail_path)
        
        # Find orphaned files
        orphaned_files = []
        total_size = 0
        
        for root, dirs, files in os.walk(media_root):
            # Skip hidden directories
            dirs[:] = [d for d in dirs if not d.startswith(".")]
            
            for file in files:
                # Skip hidden files
                if file.startswith("."):
                    continue
                
                file_path = os.path.join(root, file)
                
                if file_path not in db_paths:
                    orphaned_files.append(file_path)
                    try:
                        total_size += os.path.getsize(file_path)
                    except OSError:
                        pass
        
        if orphaned_files:
            self.stdout.write(
                self.style.WARNING(
                    f"Found {len(orphaned_files)} orphaned files "
                    f"({self._format_size(total_size)})"
                )
            )
            
            # Show sample of files
            for file_path in orphaned_files[:5]:
                rel_path = os.path.relpath(file_path, settings.MEDIA_ROOT)
                self.stdout.write(f"  - {rel_path}")
            
            if len(orphaned_files) > 5:
                self.stdout.write(f"  ... and {len(orphaned_files) - 5} more")
            
            if not dry_run:
                self.stdout.write("\nDeleting orphaned files...")
                deleted_count = 0
                
                for file_path in orphaned_files:
                    try:
                        os.remove(file_path)
                        deleted_count += 1
                    except Exception as e:
                        self.stdout.write(
                            self.style.ERROR(f"Failed to delete {file_path}: {e}")
                        )
                
                self.stdout.write(
                    self.style.SUCCESS(
                        f"Deleted {deleted_count} orphaned files "
                        f"({self._format_size(total_size)})"
                    )
                )
        else:
            self.stdout.write(
                self.style.SUCCESS("No orphaned files found")
            )
    
    def _format_size(self, size):
        """Format file size in human readable format."""
        for unit in ["B", "KB", "MB", "GB"]:
            if size < 1024.0:
                return f"{size:.2f} {unit}"
            size /= 1024.0
        return f"{size:.2f} TB"