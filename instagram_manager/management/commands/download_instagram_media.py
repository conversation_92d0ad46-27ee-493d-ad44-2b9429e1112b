import logging

from django.db.models import Q

from instagram_manager.instagram_api.data_handlers.media_handler import <PERSON>Hand<PERSON>
from instagram_manager.management.commands.base_instagram_command import BaseInstagramCommand
from instagram_manager.models import InstagramMedia, InstagramProfile
from instagram_manager.tasks.media_tasks import DownloadMediaTask

logger = logging.getLogger(__name__)


class Command(BaseInstagramCommand):
    help = "Download Instagram media files locally"
    
    def add_task_arguments(self, parser):
        super().add_task_arguments(parser)
        parser.add_argument(
            "--profile",
            type=str,
            help="Download media for a specific profile username"
        )
        parser.add_argument(
            "--type",
            type=str,
            choices=["photo", "video", "all"],
            default="all",
            help="Type of media to download"
        )
        parser.add_argument(
            "--limit",
            type=int,
            help="Limit number of media files to download"
        )
        parser.add_argument(
            "--force",
            action="store_true",
            help="Re-download even if already downloaded"
        )
        parser.add_argument(
            "--batch-size",
            type=int,
            default=10,
            help="Batch size for processing media files (default: 10)"
        )
        
    def get_task_class(self):
        """Return the task class for async execution."""
        return DownloadMediaTask
        
    def prepare_task_params(self, **options):
        """Prepare parameters for the async task."""
        # Build query to get media IDs
        query = Q()
        
        # Filter by profile if specified
        if options.get("profile"):
            try:
                profile = InstagramProfile.objects.get(username=options["profile"])
                query &= Q(post__profile=profile)
            except InstagramProfile.DoesNotExist:
                self.stdout.write(
                    self.style.ERROR(f"Profile '{options['profile']}' not found")
                )
                return None
                
        # Filter by type
        if options.get("type") != "all":
            query &= Q(media_type=options["type"])
            
        # Filter by download status unless forced
        if not options.get("force"):
            query &= Q(is_downloaded=False)
            
        # Get media IDs
        media_qs = InstagramMedia.objects.filter(query)
        
        if options.get("limit"):
            media_qs = media_qs[:options["limit"]]
            
        media_ids = list(media_qs.values_list("id", flat=True))
        
        if not media_ids:
            self.stdout.write(
                self.style.WARNING("No media files to download")
            )
            return None
            
        return {
            "media_ids": media_ids,
            "download_thumbnails": True,
            "force_redownload": options.get("force", False),
            "batch_size": options.get("batch_size", 10),
            "total_items": len(media_ids)  # For progress tracking
        }
        
    def handle_command(self, *args, **options):
        # Build query
        query = Q()
        
        # Filter by profile if specified
        if options["profile"]:
            try:
                profile = InstagramProfile.objects.get(username=options["profile"])
                query &= Q(post__profile=profile)
            except InstagramProfile.DoesNotExist:
                self.stdout.write(
                    self.style.ERROR(f"Profile '{options['profile']}' not found")
                )
                return
                
        # Filter by type
        if options["type"] != "all":
            query &= Q(media_type=options["type"])
            
        # Filter by download status unless forced
        if not options["force"]:
            query &= Q(is_downloaded=False)
            
        # Get media to download
        media_qs = InstagramMedia.objects.filter(query)
        
        if options["limit"]:
            media_qs = media_qs[:options["limit"]]
            
        total_count = media_qs.count()
        
        if total_count == 0:
            self.stdout.write(
                self.style.WARNING("No media files to download")
            )
            return
            
        self.stdout.write(
            self.style.SUCCESS(f"Starting download of {total_count} media files...")
        )
        
        success_count = 0
        error_count = 0
        
        # Create MediaHandler instance (without GCS for now)
        handler = MediaHandler(save_to_gcs=False)
        
        for i, media in enumerate(media_qs, 1):
            try:
                self.stdout.write(
                    f"[{i}/{total_count}] Downloading {media.media_type} "
                    f"for post {media.post.shortcode}..."
                )
                
                # Download media
                if handler.download_media(media):
                    success_count += 1
                    
                    # Download thumbnail for videos
                    if media.media_type == "video" and media.thumbnail_url:
                        handler.download_thumbnail(media)
                        
                    self.stdout.write(
                        self.style.SUCCESS("  ✓ Downloaded successfully")
                    )
                else:
                    error_count += 1
                    self.stdout.write(
                        self.style.ERROR("  ✗ Download failed")
                    )
                    
            except Exception as e:
                error_count += 1
                self.stdout.write(
                    self.style.ERROR(f"  ✗ Error: {e!s}")
                )
                logger.exception(f"Error downloading media {media.media_id}")
                
        # Summary
        self.stdout.write("")
        self.stdout.write(
            self.style.SUCCESS(
                f"Download complete: {success_count} successful, {error_count} failed"
            )
        )