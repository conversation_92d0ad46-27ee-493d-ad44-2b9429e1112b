"""
Management command для публикации комментариев в Instagram.
"""

import logging

from django.core.management.base import BaseCommand, CommandError

from instagram_manager.models import InstagramAccount, InstagramComment, InstagramPost
from instagram_manager.services.playwright_comment_service import PlaywrightCommentService

logger = logging.getLogger(__name__)


class Command(BaseCommand):
    help = "Post a comment to Instagram"
    
    def add_arguments(self, parser):
        parser.add_argument(
            "--account",
            required=True,
            help="Instagram account username"
        )
        parser.add_argument(
            "--post-url",
            required=True,
            help="URL of the post"
        )
        parser.add_argument(
            "--text",
            required=True,
            help="Comment text"
        )
        parser.add_argument(
            "--reply-to",
            help="Username to reply to (optional)"
        )
        parser.add_argument(
            "--create-post",
            action="store_true",
            help="Create post if not exists"
        )
        
    def handle(self, *args, **options):
        try:
            # Получаем аккаунт
            try:
                account = InstagramAccount.objects.get(username=options["account"])
            except InstagramAccount.DoesNotExist:
                raise CommandError(f"Account @{options['account']} not found")
            
            # Находим или создаем пост
            post_url = options["post_url"]
            
            # Извлекаем shortcode из URL
            if "/p/" in post_url:
                shortcode = post_url.split("/p/")[-1].split("/")[0].rstrip("/")
            elif "/reel/" in post_url:
                shortcode = post_url.split("/reel/")[-1].split("/")[0].rstrip("/")
            else:
                raise CommandError("Invalid Instagram post URL")
            
            try:
                post = InstagramPost.objects.get(shortcode=shortcode)
            except InstagramPost.DoesNotExist:
                if options["create_post"]:
                    # Создаем минимальный пост для тестирования
                    from instagram_manager.models import InstagramProfile
                    
                    # Извлекаем username из URL
                    username = post_url.split("instagram.com/")[-1].split("/")[0]
                    
                    # Создаем или получаем профиль
                    profile, _ = InstagramProfile.objects.get_or_create(
                        username=username,
                        defaults={
                            "profile_id": f"temp_{username}",
                            "full_name": username,
                        }
                    )
                    
                    # Создаем пост
                    post = InstagramPost.objects.create(
                        post_id=f"temp_{shortcode}",
                        profile=profile,
                        shortcode=shortcode,
                        post_type="photo",
                        post_url=post_url,
                        posted_at=self._get_current_time(),
                        caption="Temporary post for testing"
                    )
                    self.stdout.write(
                        self.style.SUCCESS(f"Created temporary post: {shortcode}")
                    )
                else:
                    raise CommandError(
                        f"Post with shortcode {shortcode} not found. "
                        f"Use --create-post to create it automatically."
                    )
            
            # Находим родительский комментарий если это ответ
            parent_comment = None
            if options["reply_to"]:
                parent_comment = InstagramComment.objects.filter(
                    post=post,
                    author_username=options["reply_to"]
                ).first()
                
                if not parent_comment:
                    self.stdout.write(
                        self.style.WARNING(
                            f"Parent comment by @{options['reply_to']} not found, "
                            f"posting as regular comment"
                        )
                    )
            
            # Запускаем функцию постинга
            self._post_comment_sync(
                post,
                options["text"],
                account,
                parent_comment
            )
            
        except Exception as e:
            raise CommandError(f"Error: {e!s}")
        
    def _get_current_time(self):
        """Получить текущее время."""
        from django.utils import timezone
        return timezone.now()
    
    def _post_comment_sync(self, post, text, account, parent_comment):
        """Синхронная функция для публикации комментария."""
        try:
            service = PlaywrightCommentService()
            success, error, posted_comment_id = service.post_comment_sync(
                account_id=account.id,
                post_url=post.post_url,
                comment_text=text,
                headless=True,
                debug=False,
                force_login=False
            )
            
            if success:
                self.stdout.write(
                    self.style.SUCCESS(
                        f"✅ Comment posted successfully!\n"
                        f"Account: @{account.username}\n"
                        f"Post: {post.post_url}\n"
                        f"Text: {text[:50]}..."
                    )
                )
            else:
                self.stdout.write(
                    self.style.ERROR(
                        f"❌ Failed to post comment\n"
                        f"Error: {error}"
                    )
                )
                
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f"Error posting comment: {e!s}")
            )
            raise