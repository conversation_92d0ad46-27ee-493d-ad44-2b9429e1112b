"""
Management команда для импорта комментариев Instagram.
"""

import logging
import re
from typing import Any

from instagram_manager.management.commands.base_instagram_command import BaseInstagramCommand
from instagram_manager.models import InstagramPost, InstagramProfile
from instagram_manager.tasks.import_tasks import ImportBatchCommentsTask

logger = logging.getLogger(__name__)


class Command(BaseInstagramCommand):
    """Команда для импорта комментариев к постам Instagram."""
    
    help = "Импорт комментариев к постам Instagram"
    
    def add_task_arguments(self, parser):
        """Добавление аргументов команды."""
        super().add_task_arguments(parser)
        
        # Способы указания постов
        parser.add_argument(
            "--post-url",
            type=str,
            help="URL конкретного поста для импорта комментариев"
        )
        
        parser.add_argument(
            "--post-id",
            type=str,
            help="ID поста для импорта комментариев"
        )
        
        parser.add_argument(
            "--profile",
            type=str,
            help="Username профиля для импорта комментариев ко всем постам"
        )
        
        # Параметры импорта
        parser.add_argument(
            "--limit",
            type=int,
            default=100,
            help="Максимальное количество комментариев на пост (по умолчанию: 100)"
        )
        
        parser.add_argument(
            "--recent-posts",
            type=int,
            help="Количество последних постов профиля для импорта комментариев"
        )
        
        parser.add_argument(
            "--include-replies",
            action="store_true",
            default=True,
            help="Включать ответы на комментарии (по умолчанию: да)"
        )
        
        parser.add_argument(
            "--min-likes",
            type=int,
            help="Минимальное количество лайков для импорта комментария"
        )
        
        parser.add_argument(
            "--update-existing",
            action="store_true",
            help="Обновить существующие комментарии"
        )
        
    def get_task_class(self):
        """Return the task class for async execution."""
        return ImportBatchCommentsTask
        
    def prepare_task_params(self, **options):
        """Prepare parameters for the async task."""
        # Определяем какие посты обрабатывать
        post_urls = []
        
        if options.get("post_url"):
            # Извлекаем shortcode из URL
            match = re.search(r"/p/([A-Za-z0-9-_]+)/", options["post_url"])
            if not match:
                self.stdout.write(
                    self.style.ERROR(f"Неверный формат URL поста: {options['post_url']}")
                )
                return None
            
            shortcode = match.group(1)
            try:
                post = InstagramPost.objects.get(shortcode=shortcode)
                post_urls = [f"https://www.instagram.com/p/{shortcode}/"]
            except InstagramPost.DoesNotExist:
                self.stdout.write(
                    self.style.WARNING(f"Пост {shortcode} не найден. Сначала импортируйте пост.")
                )
                return None
                
        elif options.get("post_id"):
            try:
                post = InstagramPost.objects.get(external_id=options["post_id"])
                post_urls = [f"https://www.instagram.com/p/{post.shortcode}/"]
            except InstagramPost.DoesNotExist:
                self.stdout.write(
                    self.style.ERROR(f"Пост с ID {options['post_id']} не найден")
                )
                return None
                
        elif options.get("profile"):
            try:
                profile = InstagramProfile.objects.get(username=options["profile"])
                posts = profile.posts.all()
                
                if options.get("recent_posts"):
                    posts = posts.order_by("-posted_at")[:options["recent_posts"]]
                
                post_urls = [f"https://www.instagram.com/p/{post.shortcode}/" for post in posts]
                
                if not post_urls:
                    self.stdout.write(
                        self.style.WARNING(f"У профиля {options['profile']} нет постов")
                    )
                    return None
                    
            except InstagramProfile.DoesNotExist:
                self.stdout.write(
                    self.style.ERROR(f"Профиль {options['profile']} не найден")
                )
                return None
        else:
            self.stdout.write(
                self.style.ERROR(
                    "Необходимо указать один из параметров: --post-url, --post-id или --profile"
                )
            )
            return None
        
        # Подсчитываем примерное количество комментариев
        total_items = len(post_urls) * options.get("limit", 100)
        
        return {
            "post_urls": post_urls,
            "limit_per_post": options.get("limit", 100),
            "include_replies": options.get("include_replies", True),
            "min_likes": options.get("min_likes"),
            "update_existing": options.get("update_existing", False),
            "total_items": total_items
        }
    
    def _display_result(self, result: dict[str, Any]) -> None:
        """Отображение результатов батчевого импорта."""
        if "batch_results" in result:
            # Получаем статистику
            stats = result["batch_results"].get("_statistics", {})
            
            # Выводим общую статистику
            self.stdout.write("\nСтатистика импорта:")
            self.stdout.write(f"  Всего получено комментариев: {stats.get('total_comments_received', 0)}")
            self.stdout.write(f"  Импортировано комментариев: {stats.get('total_comments_imported', 0)}")
            self.stdout.write(f"  Отфильтровано комментариев: {stats.get('comments_filtered', 0)}")
            self.stdout.write(f"  Постов с комментариями: {stats.get('posts_with_comments', 0)}")
            self.stdout.write(f"  Постов без комментариев: {stats.get('posts_without_comments', 0)}")
        else:
            # Используем базовый вывод
            super()._display_result(result)
