"""
Management command for posting Instagram comments using Playwright.
"""

import json
import logging
import time

from django.core.management.base import CommandError

from ...models import InstagramAccount
from ...services.playwright_comment_service import PlaywrightCommentService
from .base_instagram_command import BaseInstagramCommand

logger = logging.getLogger(__name__)


class Command(BaseInstagramCommand):
    """Post comments to Instagram using Playwright browser automation."""
    
    help = "Post comments to Instagram posts using Playwright"
    
    def add_arguments(self, parser):
        """Add command arguments."""
        super().add_arguments(parser)
        
        # Required arguments
        parser.add_argument(
            "--account-id",
            type=int,
            required=True,
            help="ID of InstagramAccount to use for posting"
        )
        
        parser.add_argument(
            "--post-url",
            type=str,
            help="Instagram post URL to comment on"
        )
        
        parser.add_argument(
            "--comment",
            type=str,
            help="Comment text to post"
        )
        
        # Bulk operation
        parser.add_argument(
            "--bulk-file",
            type=str,
            help="JSON file with bulk comments data"
        )
        
        parser.add_argument(
            "--delay",
            type=int,
            default=60,
            help="Delay between comments in bulk mode (seconds)"
        )
        
        # Browser options
        parser.add_argument(
            "--headed",
            action="store_true",
            help="Run browser in headed mode (visible)"
        )
        
        parser.add_argument(
            "--debug",
            action="store_true",
            help="Enable debug mode (implies --headed, opens DevTools, slows down operations)"
        )
        
        parser.add_argument(
            "--proxy",
            type=str,
            help="Proxy URL (*********************:port)"
        )
        
        parser.add_argument(
            "--test-login",
            action="store_true",
            help="Only test login without posting comment"
        )
        
        parser.add_argument(
            "--clear-session",
            action="store_true",
            help="Clear saved session and force new login"
        )
        
    def handle(self, *args, **options):
        """Handle the command."""
        # Validate arguments
        account_id = options["account_id"]
        post_url = options.get("post_url")
        comment_text = options.get("comment")
        bulk_file = options.get("bulk_file")
        
        if not bulk_file and (not post_url or not comment_text):
            raise CommandError(
                "Either provide --post-url and --comment for single comment "
                "or --bulk-file for bulk comments"
            )
            
        if bulk_file and (post_url or comment_text):
            raise CommandError(
                "Cannot use both single comment and bulk file options"
            )
            
        # Get account
        try:
            account = InstagramAccount.objects.get(id=account_id)
            self.stdout.write(f"Using account: {account.username}")
        except InstagramAccount.DoesNotExist:
            raise CommandError(f"Account with ID {account_id} not found")
            
        if not account.is_active:
            raise CommandError(f"Account {account.username} is not active")
            
        # Handle clear session
        if options["clear_session"]:
            self._clear_session(account.username)
            self.stdout.write(self.style.SUCCESS(f"✓ Cleared session for {account.username}"))
            
        # Parse proxy if provided
        proxy = self._parse_proxy(options.get("proxy"))
        
        # Initialize service
        service = PlaywrightCommentService()
        
        # Debug mode handling
        debug = options.get("debug", False)
        headless = not options["headed"] and not debug  # Debug mode forces headed
        force_login = options.get("clear_session", False)
        
        if debug:
            self.stdout.write(self.style.WARNING(
                "Debug mode enabled: Browser will be visible, DevTools will open, "
                "operations will be slowed down"
            ))
        
        # Test login only
        if options["test_login"]:
            self._test_login(service, account_id, headless, proxy, debug)
            return
            
        # Execute operation
        if bulk_file:
            self._handle_bulk_comments(
                service, account_id, bulk_file,
                headless, proxy, options["delay"], debug
            )
        else:
            self._handle_single_comment(
                service, account_id, post_url, comment_text,
                headless, proxy, debug, force_login
            )
            
    def _parse_proxy(self, proxy_url: str | None) -> dict[str, str] | None:
        """Parse proxy URL into dict format."""
        if not proxy_url:
            return None
            
        try:
            # Expected format: *********************:port
            if "@" in proxy_url:
                auth_part, server_part = proxy_url.split("@")
                protocol, auth = auth_part.split("://")
                username, password = auth.split(":")
                server = f"{protocol}://{server_part}"
            else:
                # No auth
                server = proxy_url
                username = None
                password = None
                
            proxy_dict = {"server": server}
            if username and password:
                proxy_dict["username"] = username
                proxy_dict["password"] = password
                
            return proxy_dict
            
        except Exception:
            raise CommandError(f"Invalid proxy format: {proxy_url}. Expected: *********************:port")
            
    def _test_login(
        self,
        service: PlaywrightCommentService,
        account_id: int,
        headless: bool,
        proxy: dict[str, str] | None,
        debug: bool = False
    ):
        """Test login functionality."""
        self.stdout.write("Testing login...")
        
        # We'll create a simple test by trying to post to a non-existent URL
        # This will test login but fail on navigation
        success, error, _ = service.post_comment_sync(
            account_id=account_id,
            post_url="https://www.instagram.com/p/test_login_only/",
            comment_text="test",
            headless=headless,
            proxy=proxy,
            debug=debug
        )
        
        if error and "Login failed" in error:
            self.stdout.write(self.style.ERROR(f"Login test failed: {error}"))
        else:
            self.stdout.write(self.style.SUCCESS("Login test successful!"))
            
    def _handle_single_comment(
        self,
        service: PlaywrightCommentService,
        account_id: int,
        post_url: str,
        comment_text: str,
        headless: bool,
        proxy: dict[str, str] | None,
        debug: bool = False,
        force_login: bool = False
    ):
        """Handle posting a single comment."""
        self.stdout.write(f"Posting comment to: {post_url}")
        self.stdout.write(f"Comment: {comment_text}")
        
        start_time = time.time()
        success, error, posted_comment_id = service.post_comment_sync(
            account_id=account_id,
            post_url=post_url,
            comment_text=comment_text,
            headless=headless,
            proxy=proxy,
            debug=debug,
            force_login=force_login
        )
        elapsed = time.time() - start_time
        self.stdout.write(f"Operation took {elapsed:.1f} seconds")
            
        if success:
            self.stdout.write(self.style.SUCCESS("✓ Comment posted successfully!"))
            if posted_comment_id:
                self.stdout.write(f"Posted comment ID: {posted_comment_id}")
        else:
            self.stdout.write(self.style.ERROR(f"✗ Failed to post comment: {error}"))
            
    def _handle_bulk_comments(
        self,
        service: PlaywrightCommentService,
        account_id: int,
        bulk_file: str,
        headless: bool,
        proxy: dict[str, str] | None,
        delay: int,
        debug: bool = False
    ):
        """Handle posting bulk comments."""
        # Load bulk data
        try:
            with open(bulk_file) as f:
                comments_data = json.load(f)
        except FileNotFoundError:
            raise CommandError(f"Bulk file not found: {bulk_file}")
        except json.JSONDecodeError as e:
            raise CommandError(f"Invalid JSON in bulk file: {e}")
            
        # Validate data
        if not isinstance(comments_data, list):
            raise CommandError("Bulk file must contain a JSON array")
            
        for i, item in enumerate(comments_data):
            if not isinstance(item, dict):
                raise CommandError(f"Item {i} must be a JSON object")
            if "post_url" not in item or "comment_text" not in item:
                raise CommandError(f"Item {i} must have 'post_url' and 'comment_text' fields")
                
        self.stdout.write(f"Loaded {len(comments_data)} comments from bulk file")
        self.stdout.write(f"Delay between comments: {delay} seconds")
        
        # Post comments
        start_time = time.time()
        results = service.post_bulk_comments_sync(
            account_id=account_id,
            comments_data=comments_data,
            headless=headless,
            proxy=proxy,
            delay_between_comments=delay,
            debug=debug
        )
        elapsed = time.time() - start_time
        self.stdout.write(f"Operation took {elapsed:.1f} seconds")
            
        # Display results
        self.stdout.write("\n" + "="*50)
        self.stdout.write("BULK OPERATION RESULTS")
        self.stdout.write("="*50)
        self.stdout.write(f"Total comments: {results['total']}")
        self.stdout.write(
            self.style.SUCCESS(f"✓ Successful: {results['successful']}")
        )
        self.stdout.write(
            self.style.ERROR(f"✗ Failed: {results['failed']}")
        )
        
        if results["errors"]:
            self.stdout.write("\nErrors:")
            for error in results["errors"]:
                self.stdout.write(f"  - {error}")
                
        if results["posted_comments"]:
            self.stdout.write(f"\nPosted comment IDs: {results['posted_comments']}")
            
    def _clear_session(self, username: str):
        """Clear saved session for user."""
        from pathlib import Path

        from django.conf import settings
        
        auth_dir = Path(settings.BASE_DIR) / "instagram_manager" / "browser_automation" / "storage" / "auth"
        auth_file = auth_dir / f"{username}.json"
        
        if auth_file.exists():
            auth_file.unlink()
            logger.info(f"Cleared session file for {username}")