from instagram_manager.instagram_api.data_handlers.post_handler import <PERSON><PERSON><PERSON><PERSON>
from instagram_manager.management.commands.base_instagram_command import BaseInstagramCommand
from instagram_manager.models import InstagramPost, InstagramProfile
from instagram_manager.tasks.import_tasks import ImportInstagramPostsTask


class Command(BaseInstagramCommand):
    help = "Import Instagram posts for profiles"
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.post_handler = None
    
    def add_task_arguments(self, parser):
        super().add_task_arguments(parser)
        parser.add_argument(
            "username",
            type=str,
            help="Instagram username"
        )
        parser.add_argument(
            "--limit",
            type=int,
            default=None,
            help="Number of posts to import (default: all posts)"
        )
        parser.add_argument(
            "--import-comments",
            action="store_true",
            help="Also import comments for posts"
        )
        parser.add_argument(
            "--start-date",
            type=str,
            default="",
            help="Start date for filtering posts (format: YYYY-MM-DD)"
        )
        parser.add_argument(
            "--end-date",
            type=str,
            default="",
            help="End date for filtering posts (format: YYYY-MM-DD)"
        )
        parser.add_argument(
            "--post-types",
            type=str,
            nargs="+",
            choices=["photo", "video", "carousel", "reel"],
            help="Filter posts by types (can specify multiple)"
        )
        
    def get_task_class(self):
        """Return the task class for async execution."""
        return ImportInstagramPostsTask
        
    def prepare_task_params(self, **options):
        """Prepare parameters for the async task."""
        username = options["username"]
        
        # Get profile
        try:
            profile = InstagramProfile.objects.get(username=username)
        except InstagramProfile.DoesNotExist:
            self.stdout.write(
                self.style.ERROR(f"Profile {username} not found. Import profile first.")
            )
            return None
            
        # Count total posts (approximate)
        total_items = options.get("limit", profile.post_count or 100)
        
        return {
            "profile_id": profile.id,
            "limit": options.get("limit"),
            "since_date": options.get("start_date"),
            "import_comments": options.get("import_comments", False),
            "import_media": True,
            "start_date": options.get("start_date", ""),
            "end_date": options.get("end_date", ""),
            "post_types": options.get("post_types", []),
            "total_items": total_items
        }
        
    def handle_command(self, *args, **options):
        username = options["username"]
        limit = options["limit"]
        import_comments = options.get("import_comments", False)
        start_date = options.get("start_date", "")
        end_date = options.get("end_date", "")
        post_types = options.get("post_types", [])
        
        # Проверяем профиль
        try:
            profile = InstagramProfile.objects.get(username=username)
        except InstagramProfile.DoesNotExist:
            self.stdout.write(
                self.style.ERROR(f"Profile {username} not found. Import profile first.")
            )
            return
            
        # Формируем сообщение о параметрах
        params_msg = f"Importing posts for {username}"
        if limit:
            params_msg += f" (limit: {limit})"
        else:
            params_msg += " (all posts)"
        
        if start_date or end_date or post_types:
            params_msg += "\nFilters:"
            if start_date:
                params_msg += f" start_date={start_date}"
            if end_date:
                params_msg += f" end_date={end_date}"
            if post_types:
                params_msg += f" post_types={', '.join(post_types)}"
                
        self.stdout.write(params_msg)
        
        try:
            # Получаем посты с новыми параметрами
            posts_data = self.client.get_posts(
                username, 
                start_date=start_date,
                end_date=end_date,
                post_types=post_types
            )
            
            self.stdout.write(f"Found {len(posts_data)} posts")
            
            # Если нужно ограничить количество
            if limit and len(posts_data) > limit:
                self.stdout.write(f"Limiting to {limit} posts")
                posts_data = posts_data[:limit]
            
            success_count = 0
            
            # Initialize PostHandler if needed
            if not self.post_handler:
                self.post_handler = PostHandler(save_to_gcs=False)  # Default for command line
            
            for post_data in posts_data:
                try:
                    # Обрабатываем пост
                    post = self.post_handler.process_post_data(post_data, profile)
                    
                    self.stdout.write(
                        f"Imported post: {post.shortcode} "
                        f"({post.like_count} likes, {post.comment_count} comments)"
                    )
                    
                    # Импортируем комментарии если нужно
                    # NOTE: Disabled for batch operations - use batch comment import instead
                    if import_comments and post.comment_count > 0:
                        self.stdout.write(
                            self.style.WARNING(
                                f"  - Skipping individual comment import for post {post.shortcode}. "
                                f"Use batch comment import instead."
                            )
                        )
                        # Disabled - use batch import
                        # self._import_comments(post)
                        
                    success_count += 1
                    
                except Exception as e:
                    self.stdout.write(
                        self.style.ERROR(f"Error importing post: {e!s}")
                    )
                    
            self.stdout.write(
                self.style.SUCCESS(
                    f"\nSuccessfully imported {success_count} posts"
                )
            )
            
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f"Error fetching posts: {e!s}")
            )
            
    def _import_comments(self, post: InstagramPost):
        """Импорт комментариев для поста"""
        try:
            from instagram_manager.instagram_api.data_handlers.comment_handler import CommentHandler
            
            comments_data = self.client.get_post_comments(post.post_url)
            comment_handler = CommentHandler()
            
            for comment_data in comments_data:
                comment_handler.process_comment_data(comment_data, post)
                
            self.stdout.write(
                f"  - Imported {len(comments_data)} comments"
            )
            
        except Exception as e:
            self.stdout.write(
                self.style.WARNING(f"  - Failed to import comments: {e!s}")
            )