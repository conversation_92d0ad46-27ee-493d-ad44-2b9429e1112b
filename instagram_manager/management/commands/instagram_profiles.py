from instagram_manager.instagram_api.data_handlers.profile_handler import <PERSON><PERSON><PERSON><PERSON>
from instagram_manager.management.commands.base_instagram_command import BaseInstagramCommand
from instagram_manager.models import InstagramProfile
from instagram_manager.tasks.import_tasks import ImportInstagramProfileTask


class Command(BaseInstagramCommand):
    help = "Import Instagram profiles"
    
    def add_task_arguments(self, parser):
        super().add_task_arguments(parser)
        parser.add_argument(
            "usernames",
            nargs="+",
            type=str,
            help="Instagram usernames to import"
        )
        parser.add_argument(
            "--update-existing",
            action="store_true",
            help="Update existing profiles"
        )
        parser.add_argument(
            "--deep-scan",
            action="store_true",
            help="Import posts and followers in addition to profile"
        )
        parser.add_argument(
            "--posts-limit",
            type=int,
            default=50,
            help="Maximum number of posts to import per profile (default: 50)"
        )
        parser.add_argument(
            "--followers-limit",
            type=int,
            default=100,
            help="Maximum number of followers to import per profile (default: 100)"
        )
        
    def handle_command(self, *args, **options):
        usernames = options["usernames"]
        update_existing = options.get("update_existing", False)
        
        self.stdout.write(f"Importing {len(usernames)} profiles...")
        
        success_count = 0
        error_count = 0
        
        for username in usernames:
            try:
                # Проверяем существующий профиль
                if not update_existing:
                    if InstagramProfile.objects.filter(username=username).exists():
                        self.stdout.write(
                            self.style.WARNING(f"Profile {username} already exists, skipping")
                        )
                        continue
                
                # Получаем данные из BrightData (уже валидированные)
                self.stdout.write(f"Fetching profile: {username}")
                profile_data = self.client.get_profile(username)
                
                # Данные уже валидированы pydantic, просто обрабатываем
                profile = ProfileHandler.process_profile_data(profile_data)
                
                self.stdout.write(
                    self.style.SUCCESS(
                        f"Successfully imported profile: {profile.username} "
                        f"({profile.follower_count} followers)"
                    )
                )
                success_count += 1
                
            except Exception as e:
                self.stdout.write(
                    self.style.ERROR(f"Error importing {username}: {e!s}")
                )
                error_count += 1
                
        # Итоговая статистика
        self.stdout.write(
            self.style.SUCCESS(
                f"\nImport completed: {success_count} success, {error_count} errors"
            )
        )
    
    def get_task_class(self):
        """Return the task class for async execution."""
        return ImportInstagramProfileTask
    
    def prepare_task_params(self, **options):
        """Prepare parameters for the async task."""
        usernames = options["usernames"]
        
        # For multiple usernames, we'll process them one by one
        # In future, we could create a batch task
        if len(usernames) == 1:
            return {
                "username": usernames[0],
                "deep_scan": options.get("deep_scan", False),
                "update_existing": options.get("update_existing", False),
                "import_posts": options.get("deep_scan", False),
                "import_followers": options.get("deep_scan", False),
                "posts_limit": options.get("posts_limit", 50),
                "followers_limit": options.get("followers_limit", 100),
            }
        else:
            # For multiple usernames, we need to handle differently
            # For now, we'll just process the first one
            self.stdout.write(
                self.style.WARNING(
                    f"Async mode currently supports only one username at a time. "
                    f"Processing only the first username: {usernames[0]}"
                )
            )
            return {
                "username": usernames[0],
                "deep_scan": options.get("deep_scan", False),
                "update_existing": options.get("update_existing", False),
                "import_posts": options.get("deep_scan", False),
                "import_followers": options.get("deep_scan", False),
                "posts_limit": options.get("posts_limit", 50),
                "followers_limit": options.get("followers_limit", 100),
            }