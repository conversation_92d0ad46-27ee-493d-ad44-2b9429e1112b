"""
Management command for batch importing Instagram posts for multiple profiles.
Uses a single BrightData snapshot to fetch posts for multiple profiles efficiently.
"""

import logging
from typing import cast

from django.utils import timezone

from instagram_manager.management.commands.base_instagram_command import (
    BaseInstagramCommand,
)
from instagram_manager.models import InstagramProfile, InstagramScrapingTask
from instagram_manager.services.batch_post_service import BatchPostService

logger = logging.getLogger(__name__)


class Command(BaseInstagramCommand):
    help = "Import Instagram posts for multiple profiles in a single batch"

    def get_task_class(self):
        """Required abstract method - not used since we use handle_command"""
        return None

    def prepare_task_params(self, **options):
        """Required abstract method - not used since we use handle_command"""
        return {}

    def add_arguments(self, parser):
        super().add_arguments(parser)
        parser.add_argument(
            "usernames",
            nargs="+",
            type=str,
            help="Instagram usernames to import posts for",
        )
        parser.add_argument(
            "--limit",
            type=int,
            default=None,
            help="Maximum number of posts per profile",
        )
        parser.add_argument(
            "--batch-size",
            type=int,
            default=10,
            help="Number of profiles to process in one batch (default: 10)",
        )
        parser.add_argument(
            "--import-comments",
            action="store_true",
            help="Also import comments for posts",
        )
        parser.add_argument(
            "--start-date",
            type=str,
            default="",
            help="Start date for filtering posts (format: YYYY-MM-DD)",
        )
        parser.add_argument(
            "--end-date",
            type=str,
            default="",
            help="End date for filtering posts (format: YYYY-MM-DD)",
        )
        parser.add_argument(
            "--post-types",
            type=str,
            nargs="+",
            choices=["photo", "video", "carousel", "reel"],
            help="Filter posts by types (can specify multiple)",
        )
        parser.add_argument(
            "--skip-media-download",
            action="store_true",
            help="Import posts without downloading media files",
        )
        parser.add_argument(
            "--import-all-users",
            action="store_true",
            help="Import posts from all users found in BrightData response (not just requested profiles)",
        )
        parser.add_argument(
            "--task-id",
            type=int,
            default=None,
            help="ID of existing task to update (instead of creating new one)",
        )

    def handle_command(self, *args, **options):
        usernames = options["usernames"]
        batch_size = options["batch_size"]
        limit = options.get("limit")
        start_date = options.get("start_date", "")
        end_date = options.get("end_date", "")
        post_types = options.get("post_types", [])
        skip_media_download = options.get("skip_media_download", False)
        import_all_users = options.get("import_all_users", False)
        task_id = options.get("task_id")

        # Валидация профилей
        profiles = self._validate_profiles(usernames)
        if not profiles:
            self.stdout.write(self.style.WARNING("No profiles to process"))
            return

        # Информация о параметрах
        self.stdout.write(
            self.style.SUCCESS(
                f"Processing {len(profiles)} profiles in batches of {batch_size}"
            )
        )
        if import_all_users:
            self.stdout.write(
                self.style.WARNING(
                    "Import ALL USERS mode enabled: Will import posts from all users found in BrightData response"
                )
            )
        self._print_filter_info(limit, start_date, end_date, post_types)

        # Создаём сервис для batch обработки
        batch_service = BatchPostService(self.client)

        # Получаем или создаем основную задачу
        main_task = None
        if task_id:
            try:
                main_task = InstagramScrapingTask.objects.get(pk=task_id)
                self.stdout.write(f"Using existing task ID: {task_id}")
            except InstagramScrapingTask.DoesNotExist:
                self.stdout.write(
                    self.style.WARNING(
                        f"Task ID {task_id} not found, will create new tasks"
                    )
                )

        # Обрабатываем профили батчами
        total_posts = 0
        failed_profiles = []
        last_task = main_task  # Для сохранения последней задачи

        for i in range(0, len(profiles), batch_size):
            batch_profiles = profiles[i : i + batch_size]
            batch_usernames = [p.username for p in batch_profiles]

            self.stdout.write(
                f"\nProcessing batch {i//batch_size + 1}/{(len(profiles) + batch_size - 1)//batch_size}: "
                f"{', '.join(batch_usernames)}"
            )

            # Создаём задачу отслеживания или используем существующую
            if main_task:
                task = main_task
            else:
                task = self._create_batch_task(batch_usernames, skip_media_download)

            try:
                # Выполняем batch загрузку
                results = batch_service.import_posts_batch(
                    profiles=batch_profiles,
                    start_date=start_date,
                    end_date=end_date,
                    post_types=post_types,
                    limit=limit,
                    task=task,
                    skip_media_download=skip_media_download,
                    import_all_users=import_all_users,
                )

                # Обрабатываем результаты
                for username, result in results.items():
                    if username == "_statistics":
                        continue

                    if result["success"]:
                        posts_count = result["post_count"]
                        total_posts += posts_count
                        self.stdout.write(
                            self.style.SUCCESS(
                                f"  ✓ {username}: {posts_count} posts imported"
                            )
                        )

                        # Комментарии импортируются асинхронно через отдельную подзадачу
                        # в ImportBatchPostsTask для всех постов сразу
                    else:
                        failed_profiles.append(username)
                        self.stdout.write(
                            self.style.ERROR(
                                f"  ✗ {username}: {result.get('error', 'Unknown error')}"
                            )
                        )

                # Обновляем статус задачи
                if main_task:
                    # Для основной задачи аккумулируем результаты
                    if not hasattr(task, "batch_results") or not task.batch_results:
                        task.batch_results = {}

                    # Добавляем результаты текущего батча
                    for username, result in results.items():
                        if username != "_statistics":
                            task.batch_results[username] = {
                                "success": result.get("success", False),
                                "post_count": result.get("post_count", 0),
                                "total_found": result.get("total_found", 0),
                                "error": result.get("error", None),
                            }

                    # Обновляем счетчик импортированных элементов
                    current_batch_items = sum(
                        r.get("post_count", 0)
                        for r in results.values()
                        if r.get("success", False)
                    )
                    task.items_scraped = (task.items_scraped or 0) + current_batch_items
                else:
                    # Для отдельных задач устанавливаем результаты напрямую
                    task.status = "completed"
                    task.items_scraped = sum(
                        r.get("post_count", 0)
                        for r in results.values()
                        if r.get("success", False)
                    )
                    task.completed_at = timezone.now()
                    # Конвертируем результаты в простой dict для JSON поля
                    # Исключаем объекты InstagramPost из результатов
                    task.batch_results = {}
                    for username, result in results.items():
                        task.batch_results[username] = {
                            "success": result.get("success", False),
                            "post_count": result.get("post_count", 0),
                            "total_found": result.get("total_found", 0),
                            "error": result.get("error", None),
                        }

                task.save()
                last_task = task  # Сохраняем задачу для статистики

            except Exception as e:
                logger.exception(f"Batch processing error: {e!s}")
                self.stdout.write(self.style.ERROR(f"Batch failed: {e!s}"))

                # Обновляем статус задачи
                task.status = "failed"
                task.error_message = str(e)
                task.completed_at = timezone.now()
                # Очищаем snapshot_id если это MagicMock
                if hasattr(task.brightdata_snapshot_id, "_mock_name"):
                    task.brightdata_snapshot_id = ""
                task.save()

                failed_profiles.extend(batch_usernames)

        # Обновляем финальный статус основной задачи
        if main_task:
            main_task.status = (
                "completed" if not failed_profiles else "completed_with_errors"
            )
            main_task.completed_at = timezone.now()
            main_task.save()

        # Итоговая статистика
        self._print_summary(total_posts, failed_profiles, last_task)

    def _validate_profiles(
        self, usernames: list[str]
    ) -> list[InstagramProfile]:
        """Проверяет существование профилей и создает отсутствующие"""
        from instagram_manager.services.batch_profile_service import BatchProfileService

        # Используем BatchProfileService для проверки и создания профилей
        batch_profile_service = BatchProfileService(self.client)

        self.stdout.write("Checking profiles...")

        # Валидируем и создаем отсутствующие профили
        profiles, failed_usernames = batch_profile_service.validate_and_create_profiles(
            usernames=usernames
        )

        # Выводим информацию о созданных профилях
        created_count = 0
        for profile in profiles:
            # Проверяем был ли профиль только что создан
            if (
                profile.created_at
                and (timezone.now() - profile.created_at).seconds < 60
            ):
                created_count += 1

        if created_count > 0:
            self.stdout.write(
                self.style.SUCCESS(f"Created {created_count} new profiles")
            )

        # Выводим информацию о неудачных профилях
        if failed_usernames:
            self.stdout.write(
                self.style.ERROR(
                    f"Failed to create profiles: {', '.join(failed_usernames)}"
                )
            )

        return cast(list[InstagramProfile], profiles)

    def _create_batch_task(
        self, usernames: list[str], skip_media_download: bool = False
    ) -> InstagramScrapingTask:
        """Создаёт задачу для отслеживания batch операции"""
        import uuid

        # Получаем dataset_id безопасно
        dataset_id = ""
        if hasattr(self.client, "datasets") and isinstance(self.client.datasets, dict):
            dataset_id = self.client.datasets.get("instagram_posts", "")

        task = InstagramScrapingTask.objects.create(
            task_id=f"batch_{uuid.uuid4()}",
            task_type="posts",
            target_identifier=f"batch_{len(usernames)}_profiles",
            status="in_progress",
            brightdata_dataset_id=dataset_id,
            batch_identifiers=usernames,
            skip_media_download=skip_media_download,
            started_at=timezone.now(),
        )
        return task

    # def _import_comments_for_profile(self, username: str, posts: list[Any]):
    #     """Импортирует комментарии для постов профиля"""
    #     # Этот метод больше не используется
    #     # Комментарии импортируются через асинхронную подзадачу в ImportBatchPostsTask
    #     pass

    def _print_filter_info(
        self, limit: int | None, start_date: str, end_date: str, post_types: list[str]
    ):
        """Выводит информацию о фильтрах"""
        filters = []
        if limit:
            filters.append(f"limit: {limit} posts per profile")
        if start_date:
            filters.append(f"from: {start_date}")
        if end_date:
            filters.append(f"to: {end_date}")
        if post_types:
            filters.append(f"types: {', '.join(post_types)}")

        if filters:
            self.stdout.write(f"Filters: {', '.join(filters)}")

    def _print_summary(
        self,
        total_posts: int,
        failed_profiles: list[str],
        task: InstagramScrapingTask | None = None,
    ):
        """Выводит итоговую статистику"""
        self.stdout.write("\n" + "=" * 50)
        self.stdout.write(self.style.SUCCESS(f"Total posts imported: {total_posts}"))

        # Показываем статистику о дополнительных данных, если есть
        if task and task.total_items_received > 0:
            self.stdout.write(
                f"Total posts received from BrightData: {task.total_items_received}"
            )
            if task.items_filtered > 0:
                self.stdout.write(
                    self.style.WARNING(
                        f"Posts filtered out: {task.items_filtered} "
                        f"(from {len(task.additional_users or [])} additional users)"
                    )
                )

                # Показываем первые 10 дополнительных пользователей
                if task.additional_users:
                    additional_users = sorted(task.additional_users)[:10]
                    more_count = len(task.additional_users) - 10
                    users_str = ", ".join(additional_users)
                    if more_count > 0:
                        users_str += f" and {more_count} more"
                    self.stdout.write(f"Additional users found: {users_str}")

        if failed_profiles:
            self.stdout.write(
                self.style.ERROR(
                    f"Failed profiles ({len(failed_profiles)}): {', '.join(failed_profiles)}"
                )
            )
        else:
            self.stdout.write(
                self.style.SUCCESS("All profiles processed successfully!")
            )
