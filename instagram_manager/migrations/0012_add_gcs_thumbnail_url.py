# Generated by Django 5.2.3 on 2025-07-11 00:52

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("instagram_manager", "0011_increase_url_length_limit"),
    ]

    operations = [
        migrations.AddField(
            model_name="instagrammedia",
            name="gcs_thumbnail_url",
            field=models.CharField(
                blank=True,
                help_text="Google Cloud Storage URL for the thumbnail/preview",
                max_length=1024,
                null=True,
                verbose_name="GCS Thumbnail URL",
            ),
        ),
    ]
