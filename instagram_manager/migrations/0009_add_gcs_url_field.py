# Generated by Django 5.2.3 on 2025-07-10 02:48

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("instagram_manager", "0008_add_batch_import_with_comments_task_type"),
    ]

    operations = [
        migrations.AddField(
            model_name="instagrammedia",
            name="gcs_url",
            field=models.CharField(
                blank=True,
                help_text="Google Cloud Storage URL for the media file",
                max_length=1024,
                null=True,
                verbose_name="GCS URL",
            ),
        ),
    ]
