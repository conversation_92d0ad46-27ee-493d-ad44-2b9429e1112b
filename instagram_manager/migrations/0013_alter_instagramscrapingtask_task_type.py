# Generated by Django 5.2.4 on 2025-07-29 22:12

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("instagram_manager", "0001_initial_squashed_0012_add_gcs_thumbnail_url"),
    ]

    operations = [
        migrations.Alter<PERSON>ield(
            model_name="instagramscrapingtask",
            name="task_type",
            field=models.CharField(
                choices=[
                    ("batch_import", "Batch Import"),
                    ("batch_import_with_comments", "Batch Import with Comments"),
                    ("profile", "Profile"),
                    ("posts", "Posts"),
                    ("followers", "Followers"),
                    ("comments", "Comments"),
                    ("hashtag", "Hashtag"),
                    ("roast", "Roast Analysis"),
                ],
                max_length=255,
            ),
        ),
    ]
