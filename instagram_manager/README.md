# Instagram Manager

D<PERSON><PERSON> приложение для интеграции с Instagram через BrightData API.

## Реализованная функциональность

### 1. Модели данных
- **InstagramProfile** - профили Instagram пользователей
- **InstagramPost** - посты (фото, видео, карусели, reels)
- **InstagramMedia** - медиафайлы постов
- **InstagramComment** - комментарии к постам
- **InstagramHashtag** - хештеги
- **InstagramFollower** - подписчики профилей
- **InstagramScrapingTask** - задачи для отслеживания импорта

### 2. BrightData интеграция
- **BrightDataClient** - клиент для работы с API
- Rate limiting и retry механизмы
- Кеширование результатов
- Обработка ошибок (приватные профили, rate limits, etc.)

### 3. Обработчики данных
- **ProfileHandler** - обработка данных профилей
- **PostHandler** - обработка постов и медиа
- **MediaHandler** - загрузка медиафайлов

### 4. Admin интерфейс
- Полнофункциональный admin для всех моделей
- Inline редактирование медиа и комментариев
- Actions для импорта постов и обновления профилей
- Превью изображений и видео
- Расчет engagement rate

### 5. Management команды
```bash
# Импорт профилей
python manage.py instagram_profiles username1 username2 --update-existing

# Импорт постов
python manage.py instagram_posts username --limit=20 --import-comments

# Очистка медиа файлов
python manage.py cleanup_instagram_media --check-integrity
python manage.py cleanup_instagram_media --dry-run
```

### 6. Views и Forms
- Dashboard со статистикой
- Импорт профилей через веб-интерфейс
- Детальная информация о профилях
- Список профилей с фильтрацией

### 7. Автоматическая очистка файлов
- Django signals для удаления медиа файлов при удалении записей
- Автоматическая очистка миниатюр видео
- Management команда для очистки orphaned файлов
- Подробнее см. [MEDIA_DELETION.md](docs/MEDIA_DELETION.md)

## Использование

### Настройка
1. Установите BRIGHTDATA_API_TOKEN в переменных окружения
2. Выполните миграции: `python manage.py migrate`

### Импорт данных через Admin
1. Перейдите в Admin → Instagram Profiles
2. Используйте Actions для импорта постов или обновления профилей

### Импорт через команды
```bash
# Импорт профиля
python manage.py instagram_profiles cristiano

# Импорт постов для профиля
python manage.py instagram_posts cristiano --limit=12
```

## Структура приложения
```
instagram_manager/
├── models.py              # Модели данных
├── admin.py               # Admin интерфейс
├── forms.py               # Формы для импорта
├── views.py               # Views для веб-интерфейса
├── urls.py                # URL routing
├── signals.py             # Django signals для очистки файлов
├── instagram_api/         # API интеграция
│   ├── brightdata_client.py    # BrightData клиент
│   └── data_handlers/          # Обработчики данных
│       ├── profile_handler.py
│       ├── post_handler.py
│       └── media_handler.py
└── management/commands/   # Django команды
    ├── base_instagram_command.py
    ├── instagram_profiles.py
    ├── instagram_posts.py
    └── cleanup_instagram_media.py
```

## Дальнейшие улучшения
1. Добавить Celery для асинхронной обработки
2. Реализовать streaming загрузку больших медиафайлов
3. Добавить S3 хранилище для медиа
4. Создать REST API endpoints
5. Добавить веб-интерфейс с шаблонами