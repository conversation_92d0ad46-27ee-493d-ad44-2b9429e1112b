from django.urls import path

from instagram_manager import views

app_name = "instagram_manager"

urlpatterns = [
    path("", views.dashboard, name="dashboard"),
    path("profiles/", views.profile_list, name="profile_list"),
    path("profiles/<int:pk>/", views.profile_detail, name="profile_detail"),
    path("import/profile/", views.import_profile, name="import_profile"),
    # Batch import URLs
    path("admin/batch-import-posts/", views.admin_batch_import_posts_form, name="admin_batch_import_posts_form"),
    path("admin/do-batch-import-posts/", views.admin_batch_import_posts_do_import, name="admin_batch_import_posts_do_import"),
    # Majila export URLs
    path("admin/export-to-majila/", views.admin_export_posts_to_majila_form, name="admin_export_posts_to_majila_form"),
    path("admin/do-export-to-majila/", views.admin_export_posts_to_majila_do_export, name="admin_export_posts_to_majila_do_export"),
    path("admin/majila-account/<int:account_id>/test-auth/", views.admin_majila_account_test_auth, name="admin_majila_account_test_auth"),
]