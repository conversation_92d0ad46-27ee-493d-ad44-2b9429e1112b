{% extends "admin/base_site.html" %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block extrahead %}
{{ block.super }}
<style>
    .export-info {
        background-color: #f8f9fa;
        border: 1px solid #dee2e6;
        border-radius: 4px;
        padding: 15px;
        margin-bottom: 20px;
    }
    .export-info h3 {
        margin-top: 0;
        color: #333;
    }
    .export-stats {
        display: flex;
        gap: 30px;
        margin-top: 10px;
    }
    .export-stat {
        display: flex;
        flex-direction: column;
    }
    .export-stat-value {
        font-size: 24px;
        font-weight: bold;
        color: #0066cc;
    }
    .export-stat-label {
        font-size: 14px;
        color: #666;
    }
    
    /* Стили для чекбоксов */
    #id_posts {
        max-height: 400px;
        overflow-y: auto;
        border: 1px solid #ddd;
        border-radius: 4px;
        padding: 10px;
        background-color: #fff;
    }
    
    #id_posts ul {
        list-style: none;
        margin: 0;
        padding: 0;
    }
    
    #id_posts li {
        padding: 5px 0;
        border-bottom: 1px solid #f0f0f0;
    }
    
    #id_posts li:last-child {
        border-bottom: none;
    }
    
    #id_posts label {
        display: flex;
        align-items: center;
        cursor: pointer;
        padding: 2px 0;
    }
    
    #id_posts input[type="checkbox"] {
        margin-right: 8px;
        cursor: pointer;
    }
    
    #id_posts label:hover {
        background-color: #f5f5f5;
    }
    
    /* Выделение выбранных элементов */
    #id_posts input[type="checkbox"]:checked + label {
        font-weight: 500;
    }
</style>
{% endblock %}

{% block content %}
<div class="export-info">
    <h3>Export Posts to Majila</h3>
    <div class="export-stats">
        <div class="export-stat">
            <span class="export-stat-value">{{ posts_count }}</span>
            <span class="export-stat-label">Posts Selected</span>
        </div>
        <div class="export-stat">
            <span class="export-stat-value">{{ profiles_count }}</span>
            <span class="export-stat-label">Profile{{ profiles_count|pluralize }}</span>
        </div>
    </div>
    
</div>

<form method="post" action="{% url 'instagram_manager:admin_export_posts_to_majila_do_export' %}">
    {% csrf_token %}
    
    <fieldset class="module aligned">
        <h2>Export Settings</h2>
        
        {% if form.non_field_errors %}
        <div class="errornote">
            {{ form.non_field_errors }}
        </div>
        {% endif %}
        
        {% for field in form %}
        <div class="form-row{% if field.errors %} errors{% endif %}">
            {{ field.errors }}
            <div>
                {{ field.label_tag }}
                {% if field.help_text %}
                <div class="help">{{ field.help_text|safe }}</div>
                {% endif %}
                {% if field.name == 'posts' %}
                <div style="margin: 10px 0;">
                    <button type="button" id="select-all-posts" class="button">Select All</button>
                    <button type="button" id="deselect-all-posts" class="button">Deselect All</button>
                </div>
                {% endif %}
                {{ field }}
            </div>
        </div>
        {% endfor %}
    </fieldset>
    
    <div class="submit-row">
        <input type="submit" value="Export Posts" class="default" />
        <a href="{% url 'admin:instagram_manager_instagrampost_changelist' %}" class="button cancel-link">Cancel</a>
    </div>
</form>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Управление выбором всех постов
    const selectAllBtn = document.getElementById('select-all-posts');
    const deselectAllBtn = document.getElementById('deselect-all-posts');
    const postsCheckboxes = document.querySelectorAll('input[name="posts"]');
    
    // Функция для обновления счетчика выбранных постов
    const updateSelectedCount = () => {
        const selectedCount = document.querySelectorAll('input[name="posts"]:checked').length;
        const totalCount = postsCheckboxes.length;
        
        // Обновляем заголовок с количеством выбранных
        const label = document.querySelector('label[for="id_posts"]');
        if (label) {
            label.textContent = `Select posts to export (${selectedCount} of ${totalCount} selected)`;
        }
    };
    
    // Выбрать все
    if (selectAllBtn) {
        selectAllBtn.addEventListener('click', function() {
            postsCheckboxes.forEach(checkbox => {
                checkbox.checked = true;
            });
            updateSelectedCount();
        });
    }
    
    // Снять выбор со всех
    if (deselectAllBtn) {
        deselectAllBtn.addEventListener('click', function() {
            postsCheckboxes.forEach(checkbox => {
                checkbox.checked = false;
            });
            updateSelectedCount();
        });
    }
    
    // Отслеживание изменений чекбоксов
    postsCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', updateSelectedCount);
    });
    
    // Инициализация счетчика
    updateSelectedCount();
});
</script>
{% endblock %}