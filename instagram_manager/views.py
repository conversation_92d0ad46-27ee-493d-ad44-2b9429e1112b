
import logging
from datetime import timed<PERSON>ta

from celery.result import AsyncResult
from django.conf import settings
from django.contrib import admin, messages
from django.contrib.admin.views.decorators import staff_member_required
from django.contrib.auth.decorators import login_required
from django.db import transaction
from django.db.models import Avg, Count, Sum
from django.http import JsonResponse
from django.shortcuts import get_object_or_404, redirect, render
from django.utils import timezone

from core.models import TaskResult
from instagram_manager.forms import BatchImportPostsForm, MajilaExportForm, ProfileImportForm
from instagram_manager.models import (
    InstagramMedia,
    InstagramPost,
    InstagramProfile,
    InstagramScrapingTask,
    MajilaServiceAccount,
)
from instagram_manager.services import ImportService
from instagram_manager.services.majila_export_service import MajilaExportService
from instagram_manager.utils.celery_beat import register_periodic_task

logger = logging.getLogger(__name__)


@login_required
def dashboard(request):
    """Главная страница с общей статистикой"""
    context = {
        "total_profiles": InstagramProfile.objects.count(),
        "total_posts": InstagramPost.objects.count(),
        "total_media": InstagramMedia.objects.count(),
        "recent_profiles": InstagramProfile.objects.order_by("-created_at")[:5],
        "recent_tasks": InstagramScrapingTask.objects.order_by("-created_at")[:10],
        "stats": {
            "verified_profiles": InstagramProfile.objects.filter(is_verified=True).count(),
            "private_profiles": InstagramProfile.objects.filter(is_private=True).count(),
            "total_followers": InstagramProfile.objects.aggregate(
                total=Sum("follower_count")
            )["total"] or 0,
            "avg_engagement": _calculate_avg_engagement(),
        }
    }
    return render(request, "instagram_manager/dashboard.html", context)


def _calculate_avg_engagement():
    """Расчет среднего engagement rate"""
    profiles = InstagramProfile.objects.filter(follower_count__gt=0)
    total_engagement_rate = 0.0
    count = 0
    
    for profile in profiles:
        recent_posts = profile.posts.order_by("-posted_at")[:12]
        if recent_posts:
            total_engagement = sum(
                post.like_count + post.comment_count 
                for post in recent_posts
            )
            avg_engagement = total_engagement / len(recent_posts)
            engagement_rate = (avg_engagement / profile.follower_count) * 100
            total_engagement_rate += engagement_rate
            count += 1
    
    return total_engagement_rate / count if count > 0 else 0


@staff_member_required
def import_profile(request):
    """Импорт профиля Instagram"""
    if request.method == "POST":
        form = ProfileImportForm(request.POST)
        if form.is_valid():
            try:
                username = form.cleaned_data["username"]
                execution_mode = form.cleaned_data.get("execution_mode", "async")
                force_sync = execution_mode == "sync"
                
                # Используем ImportService
                result = ImportService.import_profile(
                    username=username,
                    force_sync=force_sync,
                    update_existing=form.cleaned_data.get("update_existing", False),
                    import_posts=form.cleaned_data.get("import_posts", False),
                    posts_limit=form.cleaned_data.get("posts_limit", 50),
                )
                
                if isinstance(result, AsyncResult):
                    # Асинхронный режим
                    messages.success(
                        request,
                        f"Profile import for @{username} started asynchronously. "
                        f"Task ID: {result.id}"
                    )
                    # Можно перенаправить на страницу статуса
                    return redirect("instagram_manager:dashboard")
                else:
                    # Синхронный режим
                    if result.get("status") == "success":
                        profile_id = result.get("profile_id")
                        if profile_id:
                            messages.success(
                                request,
                                f"Profile @{username} imported successfully!"
                            )
                            return redirect("instagram_manager:profile_detail", pk=profile_id)
                        else:
                            messages.warning(
                                request,
                                f"Profile @{username} already exists (skipped)"
                            )
                    else:
                        messages.error(
                            request,
                            f"Profile import failed: {result.get('errors', ['Unknown error'])[0]}"
                        )
                        
                    return redirect("instagram_manager:profile_list")
                
            except Exception as e:
                messages.error(request, f"Import failed: {e!s}")
    else:
        form = ProfileImportForm()
    
    return render(request, "instagram_manager/import_profile.html", {
        "form": form,
        "title": "Import Instagram Profile"
    })


@login_required
def profile_detail(request, pk):
    """Детальная информация о профиле"""
    profile = get_object_or_404(InstagramProfile, pk=pk)
    
    # Статистика постов
    posts_stats = profile.posts.aggregate(
        total_posts=Count("id"),
        total_likes=Sum("like_count"),
        total_comments=Sum("comment_count"),
        avg_likes=Avg("like_count"),
        avg_comments=Avg("comment_count")
    )
    
    # Последние посты
    recent_posts = profile.posts.order_by("-posted_at")[:12]
    
    # Популярные посты
    popular_posts = profile.posts.order_by("-like_count")[:6]
    
    context = {
        "profile": profile,
        "posts_stats": posts_stats,
        "recent_posts": recent_posts,
        "popular_posts": popular_posts,
        "engagement_rate": _calculate_profile_engagement(profile)
    }
    
    return render(request, "instagram_manager/profile_detail.html", context)


def _calculate_profile_engagement(profile):
    """Расчет engagement rate для профиля"""
    if profile.follower_count == 0:
        return 0
        
    recent_posts = profile.posts.order_by("-posted_at")[:12]
    if not recent_posts:
        return 0
        
    total_engagement = sum(
        post.like_count + post.comment_count 
        for post in recent_posts
    )
    
    avg_engagement = total_engagement / len(recent_posts)
    return (avg_engagement / profile.follower_count) * 100


@login_required
def profile_list(request):
    """Список всех профилей"""
    profiles = InstagramProfile.objects.all().order_by("-follower_count")
    
    # Фильтры
    is_verified = request.GET.get("verified")
    is_private = request.GET.get("private")
    search = request.GET.get("search")
    
    if is_verified:
        profiles = profiles.filter(is_verified=is_verified == "true")
    if is_private:
        profiles = profiles.filter(is_private=is_private == "true")
    if search:
        profiles = profiles.filter(username__icontains=search)
    
    context = {
        "profiles": profiles,
        "filters": {
            "verified": is_verified,
            "private": is_private,
            "search": search
        }
    }
    
    return render(request, "instagram_manager/profile_list.html", context)


@staff_member_required
def admin_batch_import_posts_form(request):
    """View для отображения формы batch импорта"""
    # Проверяем откуда пришел запрос
    selected_ids = request.session.get("selected_profiles_for_batch_import", [])
    
    if selected_ids:
        # Пришли из InstagramProfileAdmin
        profiles = InstagramProfile.objects.filter(id__in=selected_ids)
        initial_data = {
            "usernames": "\n".join([p.username for p in profiles])
        }
        show_usernames_field = False
        title = f"Batch Import Posts for {profiles.count()} Selected Profiles"
    else:
        # Пришли из InstagramScrapingTaskAdmin
        initial_data = {}
        show_usernames_field = True
        profiles = None
        title = "Batch Import Posts"
    
    form = BatchImportPostsForm(initial=initial_data)
    
    # Check if GCS is configured for template context
    show_gcs_info = bool(getattr(settings, "GCS_BUCKET_NAME", ""))
    
    context = {
        **admin.site.each_context(request),
        "form": form,
        "title": title,
        "subtitle": "Import posts for Instagram profiles",
        "profiles": profiles,
        "show_usernames_field": show_usernames_field,
        "show_gcs_info": show_gcs_info,
        "gcs_bucket_name": getattr(settings, "GCS_BUCKET_NAME", ""),
        "opts": InstagramProfile._meta,
        "has_view_permission": True,
        "has_add_permission": False,
        "has_change_permission": True,
        "has_delete_permission": False,
    }
    
    return render(request, "admin/instagram_manager/instagramprofile/batch_import_form.html", context)


@staff_member_required
def admin_batch_import_posts_do_import(request):
    """View для обработки batch импорта"""
    if request.method != "POST":
        return redirect("instagram_manager:admin_batch_import_posts_form")
    
    form = BatchImportPostsForm(request.POST)
    
    if form.is_valid():
        # Получаем usernames либо из сессии, либо из формы
        selected_ids = request.session.get("selected_profiles_for_batch_import", [])
        
        if selected_ids:
            # Из InstagramProfileAdmin
            profiles = InstagramProfile.objects.filter(id__in=selected_ids)
            usernames = [p.username for p in profiles]
            # Очищаем сессию
            request.session.pop("selected_profiles_for_batch_import", None)
        else:
            # Из формы
            usernames = form.cleaned_data["usernames"]
            if not usernames:
                messages.error(request, "No usernames provided")
                return redirect("instagram_manager:admin_batch_import_posts_form")
        
        # Получаем параметры из формы
        posts_limit = form.cleaned_data.get("posts_limit", 0)
        batch_size = form.cleaned_data.get("batch_size", 10)
        date_from = form.cleaned_data.get("date_from")
        date_to = form.cleaned_data.get("date_to")
        post_types = form.cleaned_data.get("post_types", [])
        import_comments = form.cleaned_data.get("import_comments", False)
        skip_media_download = form.cleaned_data.get("skip_media_download", False)
        save_media_to_gcs = form.cleaned_data.get("save_media_to_gcs", False)
        
        # Логируем параметры из формы
        logger.info(
            f"[FORM DEBUG] Form parameters: date_from={date_from}, date_to={date_to}, "
            f"create_periodic={form.cleaned_data.get('create_periodic', False)}, "
            f"save_media_to_gcs={save_media_to_gcs}"
        )
        
        # Проверяем наличие API токена
        if not hasattr(settings, "BRIGHTDATA_API_TOKEN") or not settings.BRIGHTDATA_API_TOKEN:
            messages.error(request, "BRIGHTDATA_API_TOKEN not configured in settings")
            return redirect("admin:instagram_manager_instagramscrapingtask_changelist")
        
        # Создаем главную задачу для отслеживания
        is_periodic = form.cleaned_data.get("create_periodic", False)
        
        logger.info(
            f"[TASK DEBUG] Creating task: is_periodic={is_periodic}, "
            f"initial_start_date={date_from}, usernames={usernames}"
        )
        
        # Use transaction to ensure task is saved before async execution
        # Store task_id outside transaction scope
        task_id = None
        
        with transaction.atomic():
            logger.debug(
                f"[TRANSACTION START] Starting atomic transaction for creating batch import task "
                f"with {len(usernames)} profiles"
            )
            
            # Определяем тип задачи в зависимости от параметров
            if import_comments:
                task_type = "batch_import_with_comments"
            else:
                task_type = "batch_import"
            
            # Prepare parameters for the task
            task_parameters = {
                "posts_limit": posts_limit,
                "batch_size": batch_size,
                "post_types": post_types or [],
                "import_comments": import_comments,
                "skip_media_download": skip_media_download,
                "save_media_to_gcs": save_media_to_gcs,
                # Save information about which dates were provided by user
                "has_end_date": date_to is not None,
                "has_start_date": date_from is not None,
            }
            
            task = InstagramScrapingTask.objects.create(
                task_type=task_type,  # Используем соответствующий тип
                target_identifier=f"batch_{len(usernames)}_profiles",
                status="pending",
                batch_identifiers=usernames,
                skip_media_download=skip_media_download,
                # Новые поля для периодических задач
                is_periodic=is_periodic,
                interval_seconds=form.cleaned_data.get("interval_seconds") if is_periodic else None,
                initial_start_date=date_from if date_from else None,  # Только если дата действительно задана
                # Save parameters in the correct field based on task type
                parameters=task_parameters if not is_periodic else {},
                periodic_parameters=task_parameters if is_periodic else {}
            )
            
            # Save task ID for use after transaction commits
            task_id = task.id
            
            logger.debug(
                f"[TRANSACTION END] Successfully created task {task_id} in transaction "
                f"(type: {task.task_type}, is_periodic: {task.is_periodic})"
            )
        
        logger.info(
            f"[TASK DEBUG] Task created: id={task.id}, initial_start_date={task.initial_start_date}, "
            f"parameters={task.parameters}, periodic_parameters={task.periodic_parameters}"
        )
        
        # IMPORTANT: For periodic tasks DO NOT create subtasks here!
        # They will be created in periodic_tasks.py with correct dates
        # For regular tasks subtasks are created inside ImportBatchPostsTask
        if is_periodic:
            logger.info(
                "[SUBTASK DEBUG] Skipping subtask creation for periodic task - "
                "they will be created during execution"
            )
        else:
            logger.info(
                "[SUBTASK DEBUG] Subtasks will be created inside ImportBatchPostsTask"
            )
        
        # Если это периодическая задача
        if task_id and is_periodic:
            
            def setup_periodic_task():
                logger.info(f"[TASK DEBUG] Setting up periodic task {task_id} after transaction commit")
                # Получаем задачу из БД
                from instagram_manager.models import InstagramScrapingTask
                task = InstagramScrapingTask.objects.get(id=task_id)
                
                # Генерируем уникальное имя для Celery Beat
                task.celery_beat_name = f"periodic_instagram_import_{task.id}"
                task.next_periodic_run = timezone.now() + timedelta(seconds=task.interval_seconds)
                task.save()
                
                # Регистрируем в Celery Beat
                register_periodic_task(task)
                
                messages.success(
                    request,
                    f"Periodic import task created. Will run every {task.interval_seconds} seconds."
                )
            
            # Регистрируем функцию для выполнения после коммита транзакции
            transaction.on_commit(setup_periodic_task)
            
            # Периодическая задача начнет выполняться автоматически через Celery Beat
            return redirect("admin:instagram_manager_instagramscrapingtask_changelist")
        
        # Обычный (не периодический) импорт
        # Определяем функцию для запуска после коммита транзакции
        def start_import_task():
            logger.info(f"[TASK DEBUG] Starting import task {task_id} after transaction commit")
            
            # Получаем задачу из БД (она уже точно сохранена)
            from instagram_manager.models import InstagramScrapingTask
            task = InstagramScrapingTask.objects.get(id=task_id)
            
            # Запускаем batch импорт через ImportService
            try:
                # Подготавливаем параметры
                import_kwargs = {
                    "batch_size": batch_size,
                    "limit": posts_limit,
                    "start_date": date_from.strftime("%Y-%m-%d") if date_from else None,
                    "end_date": date_to.strftime("%Y-%m-%d") if date_to else None,
                    "post_types": post_types or [],
                    "import_comments": import_comments,
                    "skip_media_download": skip_media_download,
                    "save_media_to_gcs": save_media_to_gcs,
                    "scraping_task_id": task.id,  # Renamed to avoid conflict with BaseTask.task_id
                }
                
                # Проверяем режим выполнения из формы
                execution_mode = form.cleaned_data.get("execution_mode", "async")
                force_sync = execution_mode == "sync"
                
                # Запускаем импорт
                result = ImportService.import_batch_posts(
                    usernames=usernames,
                    force_sync=force_sync,
                    **import_kwargs
                )
                
                if isinstance(result, AsyncResult):
                    # Асинхронный режим - сохраняем Celery ID в задаче
                    try:
                        TaskResult.objects.get(celery_task_id=result.id)
                        task.celery_task_id = result.id
                        task.save()
                    except TaskResult.DoesNotExist:
                        pass
                    
                    messages.success(
                        request,
                        f"Batch import started asynchronously. Task ID: {result.id}"
                    )
                    
                    # Добавляем сообщение о GCS если включено
                    if save_media_to_gcs and not skip_media_download:
                        messages.info(
                            request,
                            f"Media files will be saved to Google Cloud Storage (bucket: {getattr(settings, 'GCS_BUCKET_NAME', 'N/A')})"
                        )
                    
                    # Можно перенаправить на страницу прогресса
                    # return redirect('admin:task_progress', task_id=result.id)
                else:
                    # Синхронный режим - обрабатываем результат
                    task.refresh_from_db()
                    
                    if result.get("status") == "success":
                        messages.success(
                            request,
                            f"Batch import completed: {result['profiles_processed']} profiles, "
                            f"{result['total_posts']} posts imported"
                        )
                    elif result.get("status") == "partial_success":
                        messages.warning(
                            request,
                            f"Batch import completed with errors: {result['profiles_processed']} profiles, "
                            f"{result['total_posts']} posts imported. "
                            f"Failed profiles: {', '.join(result['profiles_failed'])}"
                        )
                    else:
                        messages.error(
                            request,
                            f"Batch import failed: {', '.join(result.get('errors', ['Unknown error']))}"
                        )
                    
            except Exception as e:
                logger.error(
                    f"[TRANSACTION ERROR] Error during batch import task {task_id}: {e}"
                )
                # Обновляем задачу при ошибке
                task.status = "failed"
                task.error_message = str(e)
                task.completed_at = timezone.now()
                task.save()
                
                messages.error(request, f"Error during batch import: {e!s}")
        
        # Регистрируем функцию для выполнения после коммита транзакции
        transaction.on_commit(start_import_task)
        
        # Перенаправляем на список задач
        return redirect("admin:instagram_manager_instagramscrapingtask_changelist")
    
    else:
        # Форма невалидна, показываем ошибки
        error_count = sum(len(errors) for errors in form.errors.values())
        messages.error(request, f"Please correct {error_count} error{'s' if error_count > 1 else ''} below")
        
        # Получаем профили из сессии для отображения
        selected_ids = request.session.get("selected_profiles_for_batch_import", [])
        profiles = InstagramProfile.objects.none()  # Empty QuerySet instead of empty list
        show_usernames_field = True
        
        if selected_ids:
            profiles = InstagramProfile.objects.filter(id__in=selected_ids)
            show_usernames_field = False
        
        # Проверяем наличие GCS конфигурации
        show_gcs_info = bool(getattr(settings, "GCS_BUCKET_NAME", ""))
        
        # Готовим контекст для шаблона
        context = {
            **admin.site.each_context(request),
            "form": form,  # Невалидная форма с ошибками
            "title": "Batch Import Posts",
            "subtitle": "Import posts for Instagram profiles",
            "profiles": profiles,
            "show_usernames_field": show_usernames_field,
            "show_gcs_info": show_gcs_info,
            "gcs_bucket_name": getattr(settings, "GCS_BUCKET_NAME", ""),
            "opts": InstagramProfile._meta,
            "has_view_permission": True,
            "has_add_permission": False,
            "has_change_permission": True,
            "has_delete_permission": False,
        }
        
        return render(request, "admin/instagram_manager/instagramprofile/batch_import_form.html", context)


@staff_member_required
def admin_export_posts_to_majila_form(request):
    """Форма для экспорта постов в Majila"""
    # Получаем выбранные посты из сессии
    selected_post_ids = request.session.get("selected_posts_for_majila_export", [])
    
    if not selected_post_ids:
        messages.error(request, "No posts selected for export")
        return redirect("admin:instagram_manager_instagrampost_changelist")
    
    # Получаем посты с предзагрузкой медиа для избежания N+1 запросов
    posts = InstagramPost.objects.filter(id__in=selected_post_ids).select_related("profile").prefetch_related("media")
    
    # Определяем профили для фильтрации аккаунтов Majila
    profiles = set(post.profile for post in posts)
    
    # Если все посты от одного профиля, фильтруем аккаунты
    instagram_profile = profiles.pop() if len(profiles) == 1 else None
    
    # Создаем форму
    form = MajilaExportForm(
        posts_queryset=posts,
        instagram_profile=instagram_profile
    )
    
    context = {
        **admin.site.each_context(request),
        "form": form,
        "posts": posts,
        "posts_count": posts.count(),
        "profiles_count": len(profiles) if len(profiles) > 1 else 1,
        "title": f"Export {posts.count()} Posts to Majila",
        "subtitle": "Export selected posts to Majila service",
        "opts": InstagramPost._meta,
        "has_view_permission": True,
        "has_change_permission": True,
    }
    
    return render(request, "admin/instagram_manager/instagrampost/majila_export_form.html", context)


@staff_member_required
def admin_export_posts_to_majila_do_export(request):
    """Обработка экспорта постов в Majila"""
    if request.method != "POST":
        return redirect("admin:instagram_manager_instagrampost_changelist")
    
    # Получаем выбранные посты
    selected_post_ids = request.session.get("selected_posts_for_majila_export", [])
    if not selected_post_ids:
        messages.error(request, "No posts selected for export")
        return redirect("admin:instagram_manager_instagrampost_changelist")
    
    posts = InstagramPost.objects.filter(id__in=selected_post_ids)
    
    form = MajilaExportForm(request.POST, posts_queryset=posts)
    
    if form.is_valid():
        try:
            # Получаем данные из формы
            majila_account = form.cleaned_data["majila_account"]
            posts_to_export = form.cleaned_data["posts"]
            
            # Создаем сервис и экспортируем
            service = MajilaExportService()
            summary = service.export_posts(
                account=majila_account,
                posts=list(posts_to_export)
            )
            
            # Очищаем сессию
            request.session.pop("selected_posts_for_majila_export", None)
            
            # Показываем результаты
            if summary.successful > 0:
                messages.success(
                    request,
                    f"Successfully exported {summary.successful} posts to Majila"
                )
            if summary.failed > 0:
                messages.warning(
                    request,
                    f"Failed to export {summary.failed} posts"
                )
            if summary.skipped > 0:
                messages.info(
                    request,
                    f"Skipped {summary.skipped} posts (already exist)"
                )
            
            # Перенаправляем на список задач экспорта
            return redirect("admin:instagram_manager_majilaexporttask_changelist")
            
        except Exception as e:
            messages.error(request, f"Export error: {e!s}")
            return redirect("instagram_manager:admin_export_posts_to_majila_form")
    
    else:
        messages.error(request, "Please correct the form errors")
        return redirect("instagram_manager:admin_export_posts_to_majila_form")


@staff_member_required  
def admin_majila_account_test_auth(request, account_id):
    """Тестирование авторизации аккаунта Majila"""
    try:
        account = MajilaServiceAccount.objects.get(pk=account_id)
        
        service = MajilaExportService()
        
        # Пробуем авторизоваться
        success = service.authenticate(account)
        
        if success:
            return JsonResponse({
                "status": "success",
                "message": f"Successfully authenticated as {account.username}",
                "user_uuid": str(account.user_uuid) if account.user_uuid else None
            })
        else:
            return JsonResponse({
                "status": "error",
                "message": account.auth_error or "Authentication failed"
            })
            
    except MajilaServiceAccount.DoesNotExist:
        return JsonResponse({
            "status": "error",
            "message": "Account not found"
        }, status=404)
    except Exception as e:
        return JsonResponse({
            "status": "error",
            "message": str(e)
        }, status=500)