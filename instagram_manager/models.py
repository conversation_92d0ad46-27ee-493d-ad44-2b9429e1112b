from typing import cast

from cryptography.fernet import Fernet
from django.conf import settings
from django.db import models
from django.utils import timezone

from core.models import (
    ImportTask,
    SocialMediaComment,
    SocialMediaHashtag,
    SocialMediaMedia,
    SocialMediaPost,
    SocialMediaUser,
    SoftDeleteModel,
    TimestampedModel,
)
from core.utils.constants import InstagramLimits


class InstagramProfile(SocialMediaUser, SoftDeleteModel):
    """Модель для хранения информации о профилях Instagram"""
    # Уникальные поля Instagram
    profile_id = models.CharField(max_length=255, unique=True, db_index=True, help_text="Instagram-specific profile ID", default="")
    is_business = models.BooleanField(default=False)
    profile_pic_hd_url = models.URLField(max_length=500, blank=True)
    external_url = models.URLField(max_length=500, blank=True, null=True)
    category_name = models.CharField(max_length=255, blank=True)
    
    # Метаданные
    last_scraped_at = models.DateTimeField(null=True, blank=True)
    
    # Переопределение полей родительского класса для правильных лимитов
    username = models.CharField(max_length=InstagramLimits.USERNAME_MAX_LENGTH, unique=True, db_index=True)
    full_name = models.CharField(max_length=InstagramLimits.FULL_NAME_MAX_LENGTH, blank=True)
    bio = models.TextField(max_length=InstagramLimits.BIO_MAX_LENGTH, blank=True)
    
    class Meta:
        db_table = "instagram_profiles"
        indexes = [
            models.Index(fields=["profile_id"]),
            models.Index(fields=["last_scraped_at"]),
        ]
        
    def __str__(self):
        return f"@{self.username}"


class InstagramPost(SocialMediaPost):
    """Модель для хранения постов Instagram"""
    POST_TYPES = [
        ("photo", "Photo"),
        ("video", "Video"),
        ("carousel", "Carousel"),
        ("reel", "Reel"),
    ]
    
    # Уникальный идентификатор поста в Instagram
    post_id = models.CharField(max_length=255, db_index=True, default="")
    
    # Переопределяем author как profile
    profile = models.ForeignKey(InstagramProfile, on_delete=models.CASCADE, related_name="posts")
    
    # Уникальные поля Instagram
    shortcode = models.CharField(max_length=50, unique=True, db_index=True)
    caption = models.TextField(blank=True)  # вместо content из родителя
    
    # Дополнительные метрики Instagram
    video_play_count = models.IntegerField(null=True, blank=True)
    
    # Дополнительная информация
    location = models.CharField(max_length=255, blank=True, null=True)
    location_id = models.CharField(max_length=255, blank=True, null=True)
    is_sponsored = models.BooleanField(default=False)
    is_comments_disabled = models.BooleanField(default=False)
    
    # BrightData специфичные поля
    accessibility_caption = models.TextField(blank=True)
    
    # Переопределяем тип поста с расширенными опциями
    post_type = models.CharField(max_length=20, choices=POST_TYPES)
    
    class Meta:
        db_table = "instagram_posts"
        indexes = [
            models.Index(fields=["shortcode"]),
        ]
        ordering = ["-posted_at"]
    
    @property
    def content(self):
        """Для совместимости с SocialMediaPost."""
        return self.caption
    
    @property
    def author(self):
        """Для совместимости с SocialMediaPost."""
        return self.profile
        
    def __str__(self):
        return f"{self.profile.username} - {self.shortcode}"


class InstagramMedia(SocialMediaMedia):
    """Модель для хранения медиафайлов постов"""
    MEDIA_TYPES = [
        ("photo", "Photo"),
        ("video", "Video"),
    ]
    
    # Уникальный идентификатор медиа в Instagram
    media_id = models.CharField(max_length=255, db_index=True, default="")
    
    # Связь с постом
    post = models.ForeignKey(InstagramPost, on_delete=models.CASCADE, related_name="media")
    
    # Дополнительные URLs для Instagram
    audio_url = models.URLField(max_length=2000, blank=True, null=True)  # Для Reels
    
    # Дополнительные поля Instagram
    order_index = models.IntegerField(default=0)  # Для carousel
    
    # Переопределяем local_file как local_path для совместимости
    local_path = models.FileField(upload_to="instagram/media/", null=True, blank=True)
    local_thumbnail_path = models.FileField(
        upload_to="instagram/media/thumbnails/", 
        null=True, 
        blank=True,
        help_text="Local path to downloaded thumbnail"
    )
    
    # GCS Integration (MVP)
    gcs_url = models.CharField(
        max_length=1024,
        null=True,
        blank=True,
        help_text="Google Cloud Storage URL for the media file",
        verbose_name="GCS URL"
    )
    
    gcs_thumbnail_url = models.CharField(
        max_length=1024,
        null=True,
        blank=True,
        help_text="Google Cloud Storage URL for the thumbnail/preview",
        verbose_name="GCS Thumbnail URL"
    )
    
    @property
    def local_file(self):
        """Для совместимости с SocialMediaMedia."""
        return self.local_path
    
    def get_display_url(self) -> str:
        """
        Returns the URL to display the media.
        Priority: GCS URL > Local file > External URL
        """
        # NEW: Check GCS URL first
        if self.gcs_url:
            return self.gcs_url
        
        # Existing logic
        if self.is_downloaded and self.local_path:
            return cast(str, self.local_path.url)
        return self.media_url
    
    def get_thumbnail_display_url(self) -> str:
        """
        Возвращает URL для отображения thumbnail.
        Priority: GCS URL > Local file > External URL
        """
        # Check GCS URL first
        if self.gcs_thumbnail_url:
            return self.gcs_thumbnail_url
        
        # Then check local file
        if self.is_downloaded and self.local_thumbnail_path:
            return cast(str, self.local_thumbnail_path.url)
        
        # Fallback to external URL
        return self.thumbnail_url or ""
    
    class Meta:
        db_table = "instagram_media"
        indexes = [
            models.Index(fields=["post", "order_index"]),
        ]
        ordering = ["post", "order_index"]


class InstagramComment(SocialMediaComment):
    """Модель для хранения комментариев к постам"""
    # Уникальный идентификатор комментария в Instagram
    comment_id = models.CharField(max_length=255, db_index=True, default="")
    
    # Переопределяем post для правильной связи
    post = models.ForeignKey(InstagramPost, on_delete=models.CASCADE, related_name="comments")
    
    # Информация об авторе (расширенная)
    author_username = models.CharField(max_length=InstagramLimits.USERNAME_MAX_LENGTH, db_index=True)
    author_external_id = models.CharField(max_length=255, blank=True)
    author_profile_pic = models.URLField(max_length=500, blank=True, null=True)
    author_is_verified = models.BooleanField(default=False)
    
    # Переопределяем content как text
    text = models.TextField(max_length=InstagramLimits.COMMENT_MAX_LENGTH)
    
    # Переопределяем parent_comment как reply_to
    reply_to = models.ForeignKey("self", on_delete=models.CASCADE, null=True, blank=True, related_name="replies")
    
    # Дополнительные статусы Instagram
    is_pinned = models.BooleanField(default=False, help_text="Закрепленный комментарий")
    is_hidden = models.BooleanField(default=False, help_text="Скрытый/удаленный комментарий")
    
    @property
    def content(self):
        """Для совместимости с SocialMediaComment."""
        return self.text
    
    @property
    def parent_comment(self):
        """Для совместимости с SocialMediaComment."""
        return self.reply_to
    
    def __str__(self):
        return f"@{self.author_username}: {self.text[:50]}..."
    
    class Meta:
        db_table = "instagram_comments"
        indexes = [
            models.Index(fields=["author_username"]),
            models.Index(fields=["is_hidden", "is_pinned"]),
        ]
        ordering = ["-commented_at"]


class InstagramHashtag(SocialMediaHashtag):
    """Модель для хранения хештегов"""
    # Дополнительные поля Instagram
    posts = models.ManyToManyField(InstagramPost, related_name="hashtags")
    last_scraped_at = models.DateTimeField(null=True, blank=True)
    
    class Meta:
        db_table = "instagram_hashtags"
        
    def __str__(self):
        return f"#{self.name}"


class InstagramFollower(models.Model):
    """Модель для хранения подписчиков профиля"""
    profile = models.ForeignKey(InstagramProfile, on_delete=models.CASCADE, related_name="followers")
    
    # Информация о подписчике
    follower_username = models.CharField(max_length=150, db_index=True)
    follower_id = models.CharField(max_length=255)
    follower_full_name = models.CharField(max_length=255, blank=True, null=True)
    is_verified = models.BooleanField(default=False)
    profile_pic_url = models.URLField(max_length=500, blank=True)
    
    # Временные метки
    followed_at = models.DateTimeField(null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        db_table = "instagram_followers"
        unique_together = ["profile", "follower_id"]
        indexes = [
            models.Index(fields=["profile", "follower_username"]),
            models.Index(fields=["follower_username"]),
        ]


class InstagramScrapingTask(ImportTask):
    """Модель для отслеживания задач скрапинга"""
    TASK_TYPES = [
        ("batch_import", "Batch Import"),  # Новый тип для комбинированных задач
        ("batch_import_with_comments", "Batch Import with Comments"),  # Для импорта с комментариями
        ("profile", "Profile"),
        ("posts", "Posts"),
        ("followers", "Followers"),
        ("comments", "Comments"),
        ("hashtag", "Hashtag"),
        ("roast", "Roast Analysis"),  # Для анализа профиля и загрузки фото в GCS
    ]
    
    # Переопределяем task_type с расширенными опциями
    task_type = models.CharField(max_length=255, choices=TASK_TYPES)
    
    # Основная информация
    target_identifier = models.CharField(max_length=255)  # username, post_id, hashtag
    
    # BrightData специфичные поля
    brightdata_snapshot_id = models.CharField(max_length=255, blank=True)
    brightdata_dataset_id = models.CharField(max_length=255)
    
    # Celery integration
    celery_task_id = models.CharField(
        max_length=255,
        blank=True,
        null=True,
        db_index=True,
        help_text="Celery AsyncResult ID for async tasks"
    )
    
    # Результаты (дополнительные к ImportTask)
    items_scraped = models.IntegerField(default=0)
    
    # Статистика импорта
    total_items_received = models.IntegerField(default=0, help_text="Total items received from BrightData")
    items_filtered = models.IntegerField(default=0, help_text="Items filtered out during import")
    additional_users = models.JSONField(
        null=True, blank=True,
        help_text="List of additional users found in response but not imported"
    )
    
    # Опции импорта
    skip_media_download = models.BooleanField(default=False, help_text="Skip downloading media files during import")
    
    # Batch операции
    batch_identifiers = models.JSONField(
        null=True, blank=True,
        help_text="List of identifiers for batch operations (e.g., multiple usernames)"
    )
    batch_results = models.JSONField(
        null=True, blank=True,
        help_text="Results for each identifier in batch operation"
    )
    
    # Поля для периодических задач
    is_periodic = models.BooleanField(
        default=False,
        help_text="Whether this is a periodic task"
    )
    
    interval_seconds = models.IntegerField(
        null=True,
        blank=True,
        help_text="Interval between runs in seconds (for periodic tasks)"
    )
    
    # Динамические даты для периодических задач
    initial_start_date = models.DateField(
        null=True,
        blank=True,
        help_text="Initial start date for periodic imports"
    )
    
    last_periodic_run = models.DateTimeField(
        null=True,
        blank=True,
        help_text="Last time periodic task was executed"
    )
    
    next_periodic_run = models.DateTimeField(
        null=True,
        blank=True,
        help_text="Next scheduled run time"
    )
    
    # Счетчики для периодических задач
    periodic_runs_count = models.IntegerField(
        default=0,
        help_text="Total number of periodic runs"
    )
    
    periodic_success_count = models.IntegerField(
        default=0,
        help_text="Number of successful periodic runs"
    )
    
    periodic_fail_count = models.IntegerField(
        default=0,
        help_text="Number of failed periodic runs"
    )
    
    # Общая статистика для периодических задач
    total_periodic_items = models.IntegerField(
        default=0,
        help_text="Total items imported across all periodic runs"
    )
    
    # Celery Beat интеграция
    celery_beat_name = models.CharField(
        max_length=255,
        blank=True,
        unique=True,
        null=True,
        help_text="Unique name for Celery Beat scheduler"
    )
    
    # Дополнительные параметры для периодических задач
    periodic_parameters = models.JSONField(
        null=True,
        blank=True,
        help_text="Additional parameters for periodic execution"
    )
    
    # Счетчики подзадач для задач верхнего уровня
    subtasks_count = models.IntegerField(
        default=0,
        help_text="Total number of subtasks"
    )
    subtasks_completed = models.IntegerField(
        default=0,
        help_text="Number of completed subtasks"
    )
    subtasks_failed = models.IntegerField(
        default=0,
        help_text="Number of failed subtasks"
    )
    
    # Иерархия задач (оставляем для обратной совместимости)
    parent_task = models.ForeignKey(
        "self",
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name="subtasks",
        help_text="Parent task for hierarchical task structure (deprecated, use InstagramSubTask instead)"
    )
    
    class Meta:
        db_table = "instagram_scraping_tasks"
        indexes = [
            models.Index(fields=["target_identifier"]),
            models.Index(fields=["is_periodic", "next_periodic_run"]),
        ]
    
    def create_subtask(self, subtask_type, target_identifier, **params):
        """Создает подзадачу для этой задачи"""
        import logging
        logger = logging.getLogger(__name__)
        
        subtask = InstagramSubTask.objects.create(
            parent_task=self,
            subtask_type=subtask_type,
            target_identifier=target_identifier,
            execution_params=params
        )
        # Увеличиваем счетчик подзадач
        self.subtasks_count += 1
        self.save(update_fields=["subtasks_count"])
        
        logger.info(
            f"[SUBTASK LIFECYCLE] Created subtask {subtask.id} (type={subtask_type}) "
            f"for parent task {self.id} (is_periodic={self.is_periodic})"
        )
        return subtask
    
    def update_subtask_counters(self):
        """Обновляет счетчики подзадач на основе их текущего статуса"""
        from django.db.models import Count
        
        stats = self.subtasks_new.aggregate(
            total=Count("id"),
            completed=Count("id", filter=models.Q(status="completed")),
            failed=Count("id", filter=models.Q(status="failed"))
        )
        
        self.subtasks_count = stats["total"] or 0
        self.subtasks_completed = stats["completed"] or 0
        self.subtasks_failed = stats["failed"] or 0
        self.save(update_fields=["subtasks_count", "subtasks_completed", "subtasks_failed"])
    
    def clear_subtasks(self):
        """Удаляет все подзадачи (используется для периодических задач)"""
        import logging
        logger = logging.getLogger(__name__)
        
        # Логируем какие подзадачи будут удалены
        subtasks_to_delete = list(self.subtasks_new.values_list("id", "subtask_type", "status"))
        if subtasks_to_delete:
            logger.warning(
                f"[SUBTASK LIFECYCLE] Clearing {len(subtasks_to_delete)} subtasks for task {self.id}: "
                f"{subtasks_to_delete}"
            )
        
        self.subtasks_new.all().delete()
        self.subtasks_count = 0
        self.subtasks_completed = 0
        self.subtasks_failed = 0
        self.save(update_fields=["subtasks_count", "subtasks_completed", "subtasks_failed"])
        
        logger.info(f"[SUBTASK LIFECYCLE] Cleared all subtasks for task {self.id}")
    
    @property
    def subtasks_progress_percentage(self):
        """Возвращает процент выполненных подзадач"""
        if self.subtasks_count == 0:
            return 0
        return int((self.subtasks_completed / self.subtasks_count) * 100)


class InstagramAccount(TimestampedModel):
    """Аккаунт Instagram для автоматизации"""
    username = models.CharField(max_length=InstagramLimits.USERNAME_MAX_LENGTH, unique=True, db_index=True)
    _encrypted_password = models.TextField(db_column="password", blank=True, default="")  # Храним зашифрованный пароль
    
    # Статус аккаунта
    is_active = models.BooleanField(default=True)
    is_logged_in = models.BooleanField(default=False)
    
    # Данные сессии
    session_data = models.JSONField(null=True, blank=True)
    cookies = models.JSONField(null=True, blank=True)
    
    # Лимиты и статистика
    daily_comment_count = models.IntegerField(default=0)
    last_comment_at = models.DateTimeField(null=True, blank=True)
    
    # Метаданные
    last_used_at = models.DateTimeField(null=True, blank=True)
    
    class Meta:
        db_table = "instagram_accounts"
        indexes = [
            models.Index(fields=["username"]),
            models.Index(fields=["is_active", "is_logged_in"]),
        ]
        
    def __str__(self):
        return f"@{self.username} ({'Active' if self.is_active else 'Inactive'})"
    
    @property
    def password(self):
        """Расшифровать пароль при чтении"""
        if self._encrypted_password:
            try:
                key = settings.FIELD_ENCRYPTION_KEY.encode()
                f = Fernet(key)
                decrypted = f.decrypt(self._encrypted_password.encode())
                return decrypted.decode()
            except Exception:
                return None
        return None
    
    @password.setter
    def password(self, value):
        """Зашифровать пароль при записи"""
        if value:
            key = settings.FIELD_ENCRYPTION_KEY.encode()
            f = Fernet(key)
            encrypted = f.encrypt(value.encode())
            self._encrypted_password = encrypted.decode()
        else:
            self._encrypted_password = None


class PostedComment(TimestampedModel):
    """Отслеживание опубликованных комментариев"""
    # Связь с постом и аккаунтом
    post = models.ForeignKey(InstagramPost, on_delete=models.CASCADE, related_name="posted_comments")
    account = models.ForeignKey(InstagramAccount, on_delete=models.CASCADE, related_name="posted_comments")
    
    # Данные комментария
    text = models.TextField()
    post_url = models.URLField(max_length=500)
    
    # Статус
    STATUS_CHOICES = [
        ("pending", "Pending"),
        ("posted", "Posted"),
        ("failed", "Failed"),
    ]
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default="pending")
    
    # Результат
    posted_at = models.DateTimeField(null=True, blank=True)
    error_message = models.TextField(blank=True)
    attempts = models.IntegerField(default=0)
    
    # Уникальный ID для отслеживания (если Instagram вернет ID комментария)
    instagram_comment_id = models.CharField(max_length=255, blank=True, db_index=True)
    
    class Meta:
        db_table = "instagram_posted_comments"
        indexes = [
            models.Index(fields=["status", "created_at"]),
            models.Index(fields=["account", "posted_at"]),
            models.Index(fields=["post", "account"]),
        ]
        
    def __str__(self):
        return f"{self.account.username} - {self.text[:50]}... ({self.status})"


class MajilaServiceAccount(TimestampedModel):
    """Аккаунт для экспорта в сервис Majila"""
    instagram_profile = models.ForeignKey(InstagramProfile, on_delete=models.CASCADE, related_name="majila_accounts")
    username = models.CharField(max_length=150)
    _encrypted_password = models.TextField(db_column="password", blank=True, default="")  # Зашифрованный пароль
    
    # Данные авторизации
    access_token = models.TextField(blank=True)
    user_uuid = models.UUIDField(null=True, blank=True)
    
    # Статус
    is_active = models.BooleanField(default=True)
    last_auth_at = models.DateTimeField(null=True, blank=True)
    auth_error = models.TextField(blank=True)
    
    class Meta:
        db_table = "instagram_majila_service_accounts"
        indexes = [
            models.Index(fields=["instagram_profile", "is_active"]),
            models.Index(fields=["username"]),
        ]
        unique_together = ["instagram_profile", "username"]
        
    def __str__(self):
        return f"@{self.instagram_profile.username} -> {self.username} ({'Active' if self.is_active else 'Inactive'})"
    
    @property
    def password(self):
        """Расшифровать пароль при чтении"""
        if self._encrypted_password:
            try:
                key = settings.FIELD_ENCRYPTION_KEY.encode()
                f = Fernet(key)
                decrypted = f.decrypt(self._encrypted_password.encode())
                return decrypted.decode()
            except Exception:
                return None
        return None
    
    @password.setter
    def password(self, value):
        """Зашифровать пароль при записи"""
        if value:
            key = settings.FIELD_ENCRYPTION_KEY.encode()
            f = Fernet(key)
            encrypted = f.encrypt(value.encode())
            self._encrypted_password = encrypted.decode()
        else:
            self._encrypted_password = None


class MajilaExportTask(ImportTask):
    """Отслеживание задач экспорта в Majila"""
    TASK_STATUSES = [
        ("pending", "Pending"),
        ("in_progress", "In Progress"),
        ("completed", "Completed"),
        ("failed", "Failed"),
        ("partial", "Partially Completed"),
    ]
    
    # Переопределяем task_type со значением по умолчанию
    task_type = models.CharField(max_length=50, default="majila_export")
    
    # Переопределяем status с расширенными опциями
    status = models.CharField(max_length=20, choices=TASK_STATUSES, default="pending")
    
    # Основная информация
    account = models.ForeignKey(MajilaServiceAccount, on_delete=models.CASCADE, related_name="export_tasks")
    posts = models.ManyToManyField(InstagramPost, related_name="majila_export_tasks")
    
    # Статус и результаты
    total_posts = models.IntegerField(default=0)
    exported_count = models.IntegerField(default=0)
    failed_count = models.IntegerField(default=0)
    
    # Детали выполнения
    export_results = models.JSONField(null=True, blank=True, help_text="Detailed results for each post")
    
    class Meta:
        db_table = "instagram_majila_export_tasks"
        
    def __str__(self):
        return f"Export Task #{self.pk} - {self.account} ({self.status})"
    
    def get_duration(self):
        """Возвращает продолжительность выполнения задачи."""
        if self.started_at and self.completed_at:
            return self.completed_at - self.started_at
        return None


class InstagramSubTask(models.Model):
    """
    Подзадача для выполнения конкретного действия импорта.
    НЕ отображается в основном списке задач админки.
    Используется только как часть родительской задачи.
    """
    
    SUBTASK_TYPES = [
        ("import_profile", "Import Profile Data"),
        ("import_posts", "Import Posts"),
        ("import_comments", "Import Comments"),
        ("import_followers", "Import Followers"),
        ("download_media", "Download Media Files"),
    ]
    
    # Связь с родительской задачей
    parent_task = models.ForeignKey(
        InstagramScrapingTask,
        on_delete=models.CASCADE,
        related_name="subtasks_new",
        help_text="Parent task that owns this subtask"
    )
    
    # Основная информация о подзадаче
    subtask_type = models.CharField(
        max_length=50, 
        choices=SUBTASK_TYPES,
        help_text="Type of import operation"
    )
    target_identifier = models.CharField(
        max_length=255,
        help_text="Username, post_id, or other identifier for this subtask"
    )
    
    # Статус выполнения
    status = models.CharField(
        max_length=20, 
        choices=ImportTask.STATUS_CHOICES, 
        default="pending"
    )
    
    # Прогресс выполнения (0-100)
    progress = models.IntegerField(default=0)
    
    # Счетчики элементов
    total_items = models.IntegerField(default=0)
    processed_items = models.IntegerField(default=0)
    failed_items = models.IntegerField(default=0)
    
    # Результаты выполнения
    result_data = models.JSONField(
        default=dict,
        blank=True,
        help_text="Detailed results of subtask execution"
    )
    error_message = models.TextField(blank=True)
    
    # Временные метки
    created_at = models.DateTimeField(auto_now_add=True)
    started_at = models.DateTimeField(null=True, blank=True)
    completed_at = models.DateTimeField(null=True, blank=True)
    
    # Параметры выполнения
    execution_params = models.JSONField(
        default=dict,
        help_text="Parameters for subtask execution (start_date, limit, etc.)"
    )
    
    class Meta:
        db_table = "instagram_subtasks"
        ordering = ["created_at"]
        indexes = [
            models.Index(fields=["parent_task", "status"]),
            models.Index(fields=["subtask_type", "created_at"]),
            models.Index(fields=["parent_task", "subtask_type"]),
        ]
        
    def __str__(self):
        return f"{self.get_subtask_type_display()} - {self.target_identifier} ({self.status})"
    
    @property
    def duration(self):
        """Возвращает продолжительность выполнения"""
        if self.started_at and self.completed_at:
            return self.completed_at - self.started_at
        return None
    
    @property
    def progress_percentage(self):
        """Возвращает процент выполнения на основе обработанных элементов"""
        if self.total_items == 0:
            return self.progress
        return int((self.processed_items / self.total_items) * 100)
    
    def start(self):
        """Отмечает начало выполнения подзадачи"""
        self.status = "running"
        self.started_at = timezone.now()
        
        # Add protection against DatabaseError
        try:
            from django.db import DatabaseError
            if InstagramSubTask.objects.filter(pk=self.pk).exists():
                self.save(update_fields=["status", "started_at"])
            else:
                import logging
                logger = logging.getLogger(__name__)
                logger.warning(f"Subtask {self.id} no longer exists in database, skipping start")
        except DatabaseError as e:
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"Database error starting subtask {self.id}: {e}")
    
    def complete(self, result_data=None, processed_items=None, total_items=None):
        """Отмечает успешное завершение подзадачи"""
        self.status = "completed"
        self.completed_at = timezone.now()
        self.progress = 100
        if result_data:
            self.result_data = result_data
        if processed_items is not None:
            self.processed_items = processed_items
        if total_items is not None:
            self.total_items = total_items
        
        # Add protection against DatabaseError
        try:
            from django.db import DatabaseError
            if InstagramSubTask.objects.filter(pk=self.pk).exists():
                self.save(update_fields=["status", "completed_at", "progress", "result_data", "processed_items", "total_items"])
                
                # Обновляем счетчики родительской задачи
                if self.parent_task:
                    self.parent_task.update_subtask_counters()
            else:
                import logging
                logger = logging.getLogger(__name__)
                logger.warning(f"Subtask {self.id} no longer exists in database, skipping complete")
        except DatabaseError as e:
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"Database error completing subtask {self.id}: {e}")
    
    def fail(self, error_message):
        """Отмечает неудачное завершение подзадачи"""
        self.status = "failed"
        self.completed_at = timezone.now()
        self.error_message = error_message
        
        # Add protection against DatabaseError
        try:
            from django.db import DatabaseError
            if InstagramSubTask.objects.filter(pk=self.pk).exists():
                self.save(update_fields=["status", "completed_at", "error_message"])
                
                # Обновляем счетчики родительской задачи
                if self.parent_task:
                    self.parent_task.update_subtask_counters()
            else:
                import logging
                logger = logging.getLogger(__name__)
                logger.warning(f"Subtask {self.id} no longer exists in database, skipping fail")
        except DatabaseError as e:
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"Database error failing subtask {self.id}: {e}")
    
    def update_progress(self, processed_items, total_items=None):
        """Обновляет прогресс выполнения"""
        self.processed_items = processed_items
        if total_items is not None:
            self.total_items = total_items
        
        # Вычисляем процент прогресса
        if self.total_items > 0:
            self.progress = int((self.processed_items / self.total_items) * 100)
        
        # Add protection against DatabaseError
        try:
            from django.db import DatabaseError
            # Check if subtask still exists before saving
            if InstagramSubTask.objects.filter(pk=self.pk).exists():
                self.save(update_fields=["processed_items", "total_items", "progress"])
            else:
                import logging
                logger = logging.getLogger(__name__)
                logger.warning(f"Subtask {self.id} no longer exists in database, skipping save")
        except DatabaseError as e:
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"Database error saving subtask {self.id} progress: {e}")
