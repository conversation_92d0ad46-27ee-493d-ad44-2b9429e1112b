"""
Utilities for managing periodic tasks with Celery Beat.

These utilities provide an interface for registering, updating, and
disabling periodic tasks using django-celery-beat models.
"""

import json

from django_celery_beat.models import IntervalSchedule, PeriodicTask

from core.logging import ContextLogger

logger = ContextLogger(__name__)


def register_periodic_task(scraping_task):
    """
    Register a periodic task in django-celery-beat.
    
    Creates an IntervalSchedule and PeriodicTask for the given
    InstagramScrapingTask instance.
    
    Args:
        scraping_task: InstagramScrapingTask instance with is_periodic=True
        
    Returns:
        PeriodicTask: The created PeriodicTask instance
    """
    logger.info(
        "Registering periodic task",
        extra={
            "task_id": scraping_task.id,
            "name": scraping_task.celery_beat_name,
            "interval_seconds": scraping_task.interval_seconds
        }
    )
    
    # Create or get interval schedule
    schedule, created = IntervalSchedule.objects.get_or_create(
        every=scraping_task.interval_seconds,
        period=IntervalSchedule.SECONDS,
    )
    
    if created:
        logger.info(f"Created new interval schedule: every {scraping_task.interval_seconds} seconds")
    
    # Create periodic task
    periodic_task = PeriodicTask.objects.create(
        interval=schedule,
        name=scraping_task.celery_beat_name,
        task="instagram.execute_periodic_import",
        args=json.dumps([scraping_task.id]),
        enabled=True,
        description=f"Periodic import for {scraping_task.target_identifier}"
    )
    
    logger.info(
        "Created periodic task",
        extra={
            "periodic_task_id": periodic_task.id,
            "name": periodic_task.name
        }
    )
    
    return periodic_task


def update_periodic_task(scraping_task):
    """
    Update an existing periodic task.
    
    Updates the schedule and enabled status of a periodic task.
    If the task doesn't exist, creates a new one.
    
    Args:
        scraping_task: InstagramScrapingTask instance with updated parameters
        
    Returns:
        PeriodicTask: The updated PeriodicTask instance
    """
    try:
        periodic_task = PeriodicTask.objects.get(name=scraping_task.celery_beat_name)
        
        logger.info(
            "Updating periodic task",
            extra={
                "task_id": scraping_task.id,
                "periodic_task_id": periodic_task.id
            }
        )
        
        # Update schedule if interval changed
        if periodic_task.interval.every != scraping_task.interval_seconds:
            schedule, _ = IntervalSchedule.objects.get_or_create(
                every=scraping_task.interval_seconds,
                period=IntervalSchedule.SECONDS,
            )
            periodic_task.interval = schedule
            logger.info(f"Updated interval to {scraping_task.interval_seconds} seconds")
        
        # Update enabled status based on task status
        periodic_task.enabled = scraping_task.status not in ["failed", "cancelled"]
        periodic_task.save()
        
        return periodic_task
        
    except PeriodicTask.DoesNotExist:
        logger.warning(
            "Periodic task not found, creating new one",
            extra={"name": scraping_task.celery_beat_name}
        )
        # If task doesn't exist, create it
        return register_periodic_task(scraping_task)


def disable_periodic_task(celery_beat_name):
    """
    Disable a periodic task.
    
    Sets the enabled flag to False for the specified task.
    
    Args:
        celery_beat_name: The unique name of the periodic task
        
    Returns:
        bool: True if task was disabled, False if not found
    """
    try:
        periodic_task = PeriodicTask.objects.get(name=celery_beat_name)
        periodic_task.enabled = False
        periodic_task.save()
        
        logger.info(
            "Disabled periodic task",
            extra={
                "name": celery_beat_name,
                "periodic_task_id": periodic_task.id
            }
        )
        
        return True
        
    except PeriodicTask.DoesNotExist:
        logger.warning(
            "Periodic task not found for disabling",
            extra={"name": celery_beat_name}
        )
        return False


def delete_periodic_task(celery_beat_name):
    """
    Delete a periodic task completely.
    
    Removes the PeriodicTask from the database.
    
    Args:
        celery_beat_name: The unique name of the periodic task
        
    Returns:
        bool: True if task was deleted, False if not found
    """
    try:
        periodic_task = PeriodicTask.objects.get(name=celery_beat_name)
        periodic_task.delete()
        
        logger.info(
            "Deleted periodic task",
            extra={"name": celery_beat_name}
        )
        
        return True
        
    except PeriodicTask.DoesNotExist:
        logger.warning(
            "Periodic task not found for deletion",
            extra={"name": celery_beat_name}
        )
        return False


def get_periodic_task_status(celery_beat_name):
    """
    Get the status of a periodic task.
    
    Args:
        celery_beat_name: The unique name of the periodic task
        
    Returns:
        dict: Status information or None if not found
    """
    try:
        periodic_task = PeriodicTask.objects.get(name=celery_beat_name)
        
        return {
            "id": periodic_task.id,
            "name": periodic_task.name,
            "enabled": periodic_task.enabled,
            "interval": f"every {periodic_task.interval.every} {periodic_task.interval.period}",
            "last_run_at": periodic_task.last_run_at.isoformat() if periodic_task.last_run_at else None,
            "total_run_count": periodic_task.total_run_count,
            "task": periodic_task.task,
            "args": periodic_task.args,
        }
        
    except PeriodicTask.DoesNotExist:
        return None