"""
Instagram actions module for posting comments and other interactions.
"""

import logging
import re
from datetime import datetime
from typing import Any

from django.utils import timezone
from playwright.async_api import Page

from ..models import InstagramPost, InstagramScrapingTask, PostedComment
from .anti_detection import AntiDetection

logger = logging.getLogger(__name__)


class InstagramActions:
    """Handles Instagram actions like posting comments."""
    
    def __init__(self, page: Page):
        """
        Initialize Instagram actions handler.
        
        Args:
            page: Playwright Page instance
        """
        self.page = page
        
    async def navigate_to_post(self, post_url: str) -> tuple[bool, str | None]:
        """
        Navigate to an Instagram post.
        
        Args:
            post_url: Full Instagram post URL
            
        Returns:
            Tuple of (success, error_message)
        """
        try:
            logger.info(f"Navigating to post: {post_url}")
            
            # Check current URL before navigation
            current_url = self.page.url
            logger.info(f"Current URL before navigation: {current_url}")
            
            # Navigate to post with retry logic
            navigation_success = False
            for attempt in range(2):
                try:
                    await self.page.goto(post_url, wait_until="domcontentloaded", timeout=30000)
                    navigation_success = True
                    break
                except Exception as nav_error:
                    logger.warning(f"Navigation attempt {attempt + 1} failed: {nav_error}")
                    if attempt == 0:
                        # Try alternative navigation method
                        logger.info("Trying alternative navigation...")
                        await self.page.evaluate(f'window.location.href = "{post_url}"')
                        await self.page.wait_for_load_state("domcontentloaded", timeout=30000)
                        navigation_success = True
                        break
                    
            if not navigation_success:
                raise Exception("Failed to navigate to post after multiple attempts")
            
            # Check URL after navigation
            final_url = self.page.url
            logger.info(f"Final URL after navigation: {final_url}")
            
            # Screenshot removed - was creating fixed files in project root
            
            # Wait for post to load
            await AntiDetection.human_like_delay(1000, 2000)
            
            # Wait for main content to be visible
            # Try multiple selectors as Instagram may use different structures
            content_selectors = [
                "article",
                'main[role="main"]',
                'div[role="dialog"]',  # For modal posts
                "section",  # Alternative container
            ]
            
            content_found = False
            for selector in content_selectors:
                try:
                    await self.page.wait_for_selector(selector, state="visible", timeout=3000)
                    logger.info(f"Post content loaded with selector: {selector}")
                    content_found = True
                    break
                except Exception:
                    continue
                    
            if not content_found:
                logger.debug("Standard content containers not found, but continuing as page loaded")
            
            # Verify we're on a post page
            if not await self._verify_post_page():
                logger.error("Post page verification failed")
                return False, "Not a valid Instagram post page"
                
            logger.info("Successfully navigated to post page")
            
            # Random scroll to appear human
            await AntiDetection.random_scroll(self.page)
            
            return True, None
            
        except Exception as e:
            error = f"Failed to navigate to post: {e!s}"
            logger.exception(error)
            return False, error
            
    async def post_comment(
        self,
        comment_text: str,
        post: InstagramPost | None = None,
        account_id: int | None = None
    ) -> tuple[bool, str | None, PostedComment | None]:
        """
        Post a comment on the current Instagram post.
        
        Args:
            comment_text: Text to comment
            post: InstagramPost model instance
            account_id: ID of InstagramAccount used
            
        Returns:
            Tuple of (success, error_message, posted_comment)
        """
        task = None
        posted_comment = None
        
        if post:
            # Create tracking task
            task = await self._create_task(post, "post_comment")
            
        try:
            logger.info(f"Starting comment posting process: '{comment_text}'")
            
            # Screenshot removed - was creating fixed files in project root
            
            # Check if comments are enabled
            logger.info("Checking if comments are enabled...")
            comments_disabled = await self._check_comments_disabled()
            if comments_disabled:
                error = "Comments are disabled on this post"
                logger.error(error)
                await self._update_task(task, "failed", error)
                return False, error, None
                
            # Find comment input
            logger.info("Looking for comment input field...")
            comment_input = await self._find_comment_input()
            if not comment_input:
                # Try scrolling to find it
                logger.info("Comment input not immediately visible, trying to scroll...")
                await self.page.evaluate("window.scrollBy(0, 300)")
                await AntiDetection.human_like_delay(500, 1000)
                
                # Try again after scroll
                comment_input = await self._find_comment_input()
                if not comment_input:
                    error = "Comment input field not found"
                    logger.error(error)
                    await self._update_task(task, "failed", error)
                    return False, error, None
                
            # Click on comment input to focus
            logger.info("Clicking comment input")
            try:
                await comment_input.click()
            except Exception:
                # If click fails, try to scroll to element first
                await comment_input.scroll_into_view_if_needed()
                await comment_input.click()
                
            await AntiDetection.human_like_delay(500, 1000)
            
            # Type comment with human-like behavior
            logger.info(f"Typing comment: {comment_text[:50]}...")
            await AntiDetection.human_like_typing(
                self.page,
                'textarea[aria-label*="comment" i], textarea[placeholder*="comment" i]',
                comment_text
            )
            
            # Random mouse movement
            await AntiDetection.random_mouse_movement(self.page)
            
            # Find and click post button
            post_button = await self._find_post_button()
            if not post_button:
                error = "Post button not found"
                await self._update_task(task, "failed", error)
                return False, error, None
                
            # Double-check we have the right button
            button_text = await post_button.text_content()
            logger.info(f"About to click button with text: '{button_text}'")
                
            # Check if button is enabled
            is_disabled = await post_button.get_attribute("disabled")
            if is_disabled:
                logger.info("Post button disabled, waiting...")
                await self.page.wait_for_function(
                    """() => {
                        const buttons = Array.from(document.querySelectorAll('button[type="submit"]'));
                        const postButton = buttons.find(b => b.textContent.includes('Post'));
                        return postButton && !postButton.disabled;
                    }""",
                    timeout=5000
                )
                
            logger.info("Clicking post button")
            await post_button.click()
            
            # Wait for comment to be posted
            await AntiDetection.human_like_delay(3000, 4000)
            
            # Verify comment was posted
            success = await self._verify_comment_posted(comment_text)
            
            if success:
                logger.info("Comment posted successfully")
                
                # Create PostedComment record
                if post and account_id:
                    posted_comment = await self._create_posted_comment(
                        post, account_id, comment_text
                    )
                    
                await self._update_task(task, "completed")
                return True, None, posted_comment
            else:
                # Check for rate limiting or errors
                error = await self._check_comment_errors()
                if not error:
                    error = "Comment verification failed"
                    
                await self._update_task(task, "failed", error)
                return False, error, None
                
        except Exception as e:
            error = f"Error posting comment: {e!s}"
            logger.error(error, exc_info=True)
            await self._update_task(task, "failed", error)
            
            # Save screenshot for debugging
            screenshot_path = f"/tmp/comment_error_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png"
            await self.page.screenshot(path=screenshot_path)
            
            return False, error, None
            
    async def like_post(self) -> tuple[bool, str | None]:
        """
        Like the current Instagram post.
        
        Returns:
            Tuple of (success, error_message)
        """
        try:
            # Find like button
            like_button = await self._find_like_button()
            if not like_button:
                return False, "Like button not found"
                
            # Check if already liked
            aria_label = await like_button.get_attribute("aria-label")
            if aria_label and "unlike" in aria_label.lower():
                logger.info("Post already liked")
                return True, None
                
            # Click like button
            logger.info("Clicking like button")
            await like_button.click()
            await AntiDetection.human_like_delay(500, 1000)
            
            # Verify like
            aria_label = await like_button.get_attribute("aria-label")
            if aria_label and "unlike" in aria_label.lower():
                logger.info("Post liked successfully")
                return True, None
            else:
                return False, "Like verification failed"
                
        except Exception as e:
            error = f"Error liking post: {e!s}"
            logger.exception(error)
            return False, error
            
    async def _verify_post_page(self) -> bool:
        """Verify we're on a valid Instagram post page."""
        # Check URL pattern
        url = self.page.url
        logger.info(f"Verifying post page URL: {url}")
        
        if not re.match(r"https://www\.instagram\.com/p/[\w-]+/?", url):
            logger.error(f"URL doesn't match post pattern: {url}")
            return False
            
        # Check for post elements
        post_selectors = [
            'article[role="presentation"]',
            "article",
            'img[style*="object-fit"]',
            "video",
            '[data-testid="post-content"]',
            'div[role="button"][tabindex="0"]',  # Instagram media containers
        ]
        
        for selector in post_selectors:
            element = await self.page.query_selector(selector)
            if element:
                logger.info(f"Found post element with selector: {selector}")
                return True
                
        logger.error("No post elements found on page")
        return False
        
    async def _check_comments_disabled(self) -> bool:
        """Check if comments are disabled on the post."""
        # Look for disabled comments indicators
        disabled_texts = [
            "Comments on this post have been limited",
            "Commenting has been turned off",
        ]
        
        content = await self.page.content()
        for text in disabled_texts:
            if text in content:
                return True
                
        # Also check if comment section exists
        comment_section = await self.page.query_selector("section > div > form")
        return comment_section is None
        
    async def _find_comment_input(self) -> Any | None:
        """Find the comment input field."""
        logger.info("Searching for comment input field...")
        
        # Multiple possible selectors for comment input
        selectors = [
            'textarea[aria-label*="comment" i]',
            'textarea[placeholder*="comment" i]',
            'textarea[aria-label*="Add a comment" i]',
            'textarea[placeholder*="Add a comment" i]',
            'form[method="POST"] textarea',
            "form textarea",
            'textarea[data-testid="post-comment-text-area"]',
            "section textarea",  # Comments are often in section
            'div[role="textbox"]',  # Instagram sometimes uses contenteditable
            "textarea",  # Fallback
            'input[aria-label*="comment" i]',
            'input[placeholder*="comment" i]',
        ]
        
        for selector in selectors:
            element = await self.page.query_selector(selector)
            if element:
                logger.info(f"Found comment input with selector: {selector}")
                # Check if element is visible
                is_visible = await element.is_visible()
                if is_visible:
                    return element
                else:
                    logger.warning(f"Comment input found but not visible: {selector}")
                    
        logger.error("No comment input field found")
        return None
        
    async def _find_post_button(self) -> Any | None:
        """Find the post comment button."""
        logger.info("Searching for Post button...")
        
        # Get all buttons on the page
        all_buttons = await self.page.query_selector_all('button[type="submit"], button, div[role="button"]')
        
        for button in all_buttons:
            try:
                text = await button.text_content()
                if text:
                    text = text.strip()
                    logger.debug(f"Found button with text: '{text}'")
                    
                    # Look for exact "Post" text, not "Boost Post"
                    if text == "Post" or text.lower() == "post":
                        # Make sure it's not disabled
                        is_disabled = await button.get_attribute("disabled")
                        if not is_disabled:
                            logger.info(f"Found Post button with text: '{text}'")
                            return button
            except Exception:
                continue
        
        # Alternative: look for Post button near the comment textarea
        logger.info("Trying alternative method: looking for Post button near comment input")
        post_button = await self.page.query_selector('form button:has-text("Post"):not(:has-text("Boost"))')
        if post_button:
            logger.info("Found Post button using alternative selector")
            return post_button
            
        # Last resort: find submit button in the same form as the textarea
        textarea = await self.page.query_selector("textarea")
        if textarea:
            form = await self.page.evaluate_handle("""
                (textarea) => {
                    return textarea.closest('form');
                }
            """, textarea)
            
            if form:
                submit_button = await form.query_selector('button[type="submit"]:not(:has-text("Boost"))')
                if submit_button:
                    logger.info("Found submit button in comment form")
                    return submit_button
                    
        logger.error("Could not find Post button")
        return None
        
    async def _find_like_button(self) -> Any | None:
        """Find the like button."""
        selectors = [
            'svg[aria-label*="like" i]',
            'button svg[aria-label*="like" i]',
            'span[aria-label*="like" i]',
        ]
        
        for selector in selectors:
            element = await self.page.query_selector(selector)
            if element:
                # Get parent button if SVG
                if element.element_type == "svg":
                    parent = await element.query_selector("..")
                    if parent and await parent.get_attribute("role") == "button":
                        return parent
                return element
                
        return None
        
            
    async def _verify_comment_posted(self, comment_text: str) -> bool:
        """Verify the comment was posted successfully."""
        try:
            # Wait for comment to appear
            # Comments might take a moment to show up
            await self.page.wait_for_selector(
                f'span:has-text("{comment_text[:20]}")',
                timeout=5000
            )
            return True
        except Exception:
            # Try alternative verification
            content = await self.page.content()
            return comment_text in content
            
    async def _check_comment_errors(self) -> str | None:
        """Check for comment posting errors."""
        # Common error patterns
        error_patterns = [
            "Try again later",
            "We restrict certain activity",
            "Action Blocked",
            "Something went wrong",
            "Couldn't post comment",
        ]
        
        content = await self.page.content()
        for pattern in error_patterns:
            if pattern.lower() in content.lower():
                return f"Instagram error: {pattern}"
                
        # Check for rate limit modal
        modal = await self.page.query_selector('[role="dialog"]')
        if modal:
            modal_text = await modal.text_content()
            if modal_text and any(word in modal_text.lower() for word in ["limit", "restrict", "block"]):
                return f"Rate limit detected: {modal_text[:100]}"
                
        return None
        
    async def _create_task(
        self,
        post: InstagramPost,
        task_type: str
    ) -> InstagramScrapingTask | None:
        """Create a scraping task for tracking."""
        try:
            # Map our task type to valid choices
            task_type_mapping = {
                "post_comment": "comments",
                "like_post": "posts"
            }
            valid_task_type = task_type_mapping.get(task_type, "posts")
            
            task = InstagramScrapingTask(
                task_type=valid_task_type,
                target_identifier=post.shortcode,
                status="in_progress",
                brightdata_dataset_id="playwright_comment",
                started_at=timezone.now()
            )
            await task.asave()
            return task
        except Exception as e:
            logger.exception(f"Failed to create task: {e}")
            return None
            
    async def _update_task(
        self,
        task: InstagramScrapingTask | None,
        status: str,
        error_message: str | None = None
    ) -> None:
        """Update task status."""
        if not task:
            return
            
        try:
            task.status = status
            task.completed_at = timezone.now()
            if error_message:
                task.error_message = error_message
            await task.asave()
        except Exception as e:
            logger.exception(f"Failed to update task: {e}")
            
    async def _create_posted_comment(
        self,
        post: InstagramPost,
        account_id: int,
        comment_text: str
    ) -> PostedComment | None:
        """Create PostedComment record."""
        try:
            # Get the account
            from instagram_manager.models import InstagramAccount
            account = await InstagramAccount.objects.aget(id=account_id)
            
            # Get the post URL
            post_url = post.post_url or f"https://www.instagram.com/p/{post.shortcode}/"
            
            # Create only the PostedComment record
            posted_comment = PostedComment(
                post=post,
                account=account,
                text=comment_text,
                post_url=post_url,
                status="posted",
                posted_at=timezone.now(),
                attempts=1
            )
            await posted_comment.asave()
            
            logger.info(f"Created PostedComment record: {posted_comment.id}")
            return posted_comment
        except Exception as e:
            logger.exception(f"Failed to create posted comment: {e}")
            return None
            
    def extract_post_id_from_url(self, url: str) -> str | None:
        """
        Extract Instagram post ID from URL.
        
        Args:
            url: Instagram post URL
            
        Returns:
            Post ID or None
        """
        match = re.match(r"https://www\.instagram\.com/p/([\w-]+)/?", url)
        if match:
            return match.group(1)
        return None