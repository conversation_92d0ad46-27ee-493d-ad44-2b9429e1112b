"""
Anti-detection utilities for browser automation.
"""

import asyncio
import random

from playwright.async_api import <PERSON><PERSON>er<PERSON>ontex<PERSON>, Page


class AntiDetection:
    """Implements various anti-detection strategies for browser automation."""
    
    # Real user agents for different platforms
    USER_AGENTS = [
        # macOS Chrome
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
        # Windows Chrome
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
        # macOS Safari
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Safari/605.1.15",
    ]
    
    # Realistic viewport sizes
    VIEWPORTS = [
        {"width": 1920, "height": 1080},  # Full HD
        {"width": 1366, "height": 768},   # Common laptop
        {"width": 1440, "height": 900},   # MacBook
        {"width": 1536, "height": 864},   # Surface
    ]
    
    @classmethod
    def get_browser_args(cls) -> list[str]:
        """Get browser launch arguments for anti-detection."""
        return [
            "--disable-blink-features=AutomationControlled",
            "--disable-dev-shm-usage",
            "--no-sandbox",
            "--disable-setuid-sandbox",
            "--disable-infobars",
            "--disable-notifications",
            "--disable-gpu",
            "--disable-dev-tools",
            "--disable-extensions",
            "--disable-images",  # Optional: faster loading
        ]
    
    @classmethod
    def get_random_user_agent(cls) -> str:
        """Get a random user agent string."""
        return random.choice(cls.USER_AGENTS)
    
    @classmethod
    def get_random_viewport(cls) -> dict[str, int]:
        """Get a random viewport size."""
        return random.choice(cls.VIEWPORTS)
    
    @classmethod
    async def apply_stealth_scripts(cls, context: BrowserContext) -> None:
        """Apply stealth JavaScript to hide automation indicators."""
        await context.add_init_script("""
            // Override the navigator.webdriver property
            Object.defineProperty(navigator, 'webdriver', {
                get: () => undefined
            });
            
            // Override navigator.plugins to appear non-empty
            Object.defineProperty(navigator, 'plugins', {
                get: () => [1, 2, 3, 4, 5]
            });
            
            // Override navigator.languages
            Object.defineProperty(navigator, 'languages', {
                get: () => ['en-US', 'en']
            });
            
            // Override Permissions API
            const originalQuery = window.navigator.permissions.query;
            window.navigator.permissions.query = (parameters) => (
                parameters.name === 'notifications' ?
                    Promise.resolve({ state: Notification.permission }) :
                    originalQuery(parameters)
            );
            
            // Mock chrome runtime
            window.chrome = {
                runtime: {}
            };
            
            // Override console.debug to prevent detection
            const originalConsoleDebug = console.debug;
            console.debug = (...args) => {
                if (!args[0]?.includes?.('webdriver')) {
                    originalConsoleDebug.apply(console, args);
                }
            };
        """)
    
    @classmethod
    async def human_like_delay(cls, min_ms: int = 500, max_ms: int = 2000) -> None:
        """Add a random human-like delay."""
        delay = random.randint(min_ms, max_ms)
        await asyncio.sleep(delay / 1000)
    
    @classmethod
    async def human_like_typing(cls, page: Page, selector: str, text: str) -> None:
        """Type text with human-like speed and variations."""
        element = await page.wait_for_selector(selector, state="visible")
        await element.click()
        
        # Small delay after click
        await cls.human_like_delay(300, 700)
        
        # Type with variable speed
        for char in text:
            await page.keyboard.type(char)
            # Variable delay between keystrokes
            await asyncio.sleep(random.uniform(0.05, 0.15))
            
            # Occasionally longer pauses (thinking)
            if random.random() < 0.1:
                await asyncio.sleep(random.uniform(0.5, 1.0))
    
    @classmethod
    async def random_mouse_movement(cls, page: Page) -> None:
        """Perform random mouse movements on the page."""
        viewport = page.viewport_size
        if not viewport:
            return
            
        for _ in range(random.randint(2, 5)):
            x = random.randint(100, viewport["width"] - 100)
            y = random.randint(100, viewport["height"] - 100)
            await page.mouse.move(x, y)
            await cls.human_like_delay(100, 300)
    
    @classmethod
    async def random_scroll(cls, page: Page) -> None:
        """Perform random scrolling on the page."""
        for _ in range(random.randint(1, 3)):
            scroll_amount = random.randint(100, 500)
            direction = random.choice(["up", "down"])
            
            if direction == "down":
                await page.mouse.wheel(0, scroll_amount)
            else:
                await page.mouse.wheel(0, -scroll_amount)
                
            await cls.human_like_delay(500, 1500)
    
    @classmethod
    async def handle_detection_check(cls, page: Page) -> bool:
        """Check if we've been detected as a bot."""
        detection_indicators = [
            "unusual activity",
            "automated behavior",
            "suspicious activity",
            "verify you're human",
            "are you a robot",
        ]
        
        page_content = await page.content()
        page_content_lower = page_content.lower()
        
        for indicator in detection_indicators:
            if indicator in page_content_lower:
                return True
                
        return False
    
    @classmethod
    def get_random_location(cls) -> dict[str, float]:
        """Get random geolocation coordinates."""
        locations = [
            {"latitude": 40.7128, "longitude": -74.0060},  # New York
            {"latitude": 34.0522, "longitude": -118.2437},  # Los Angeles
            {"latitude": 51.5074, "longitude": -0.1278},   # London
            {"latitude": 48.8566, "longitude": 2.3522},    # Paris
        ]
        return random.choice(locations)