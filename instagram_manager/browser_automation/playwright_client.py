"""
Core Playwright client for browser automation.
"""

import logging
import os
from contextlib import asynccontextmanager
from pathlib import Path
from typing import Any

from django.conf import settings
from playwright.async_api import <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, Playwright, async_playwright

from .anti_detection import AntiDetection

logger = logging.getLogger(__name__)


class PlaywrightClient:
    """Manages Playwright browser instances with anti-detection features."""
    
    def __init__(
        self,
        headless: bool = True,
        proxy: dict[str, str] | None = None,
        browser_type: str = "chromium",
        debug: bool = False
    ):
        """
        Initialize Playwright client.
        
        Args:
            headless: Run browser in headless mode
            proxy: Proxy configuration dict with server, username, password
            browser_type: Browser type - chromium, firefox, or webkit
            debug: Enable debug mode with DevTools, slow motion, etc.
        """
        self.headless = headless
        self.proxy = proxy
        self.browser_type = browser_type
        self.debug = debug
        self.playwright: Playwright | None = None
        self.browser: Browser | None = None
        self._auth_dir = Path(settings.BASE_DIR) / "instagram_manager" / "browser_automation" / "storage" / "auth"
        self._auth_dir.mkdir(parents=True, exist_ok=True)
        
    async def start(self) -> None:
        """Start Playwright and launch browser."""
        logger.info("Starting Playwright...")
        self.playwright = await async_playwright().start()
        logger.info("Playwright started successfully")
        
        # Get browser launcher
        if self.browser_type == "chromium":
            launcher = self.playwright.chromium
        elif self.browser_type == "firefox":
            launcher = self.playwright.firefox
        elif self.browser_type == "webkit":
            launcher = self.playwright.webkit
        else:
            raise ValueError(f"Unsupported browser type: {self.browser_type}")
        
        # Launch browser with anti-detection args
        launch_options: dict[str, Any] = {
            "headless": self.headless if not self.debug else False,
            "args": AntiDetection.get_browser_args(),
        }
        
        # Debug mode options
        if self.debug:
            launch_options["devtools"] = True  # Open DevTools automatically
            launch_options["slow_mo"] = 1000  # Slow down operations by 1 second
            # Remove some anti-detection args in debug mode for better debugging
            launch_options["args"] = [
                arg for arg in launch_options["args"] 
                if arg not in ["--disable-dev-tools", "--disable-extensions"]
            ]
            logger.info("Debug mode enabled: DevTools will open, operations will be slowed down")
        
        if self.proxy:
            launch_options["proxy"] = self.proxy
            
        logger.info(f"Launching {self.browser_type} browser with options: headless={self.headless}, debug={self.debug}")
        self.browser = await launcher.launch(**launch_options)
        logger.info(f"Successfully launched {self.browser_type} browser")
        
    async def stop(self) -> None:
        """Stop browser and Playwright."""
        if self.browser:
            await self.browser.close()
            self.browser = None
            
        if self.playwright:
            await self.playwright.stop()
            self.playwright = None
            
        logger.info("Stopped Playwright client")
        
    @asynccontextmanager
    async def new_context(
        self,
        user_agent: str | None = None,
        viewport: dict[str, int] | None = None,
        storage_state_path: str | None = None,
        save_storage_state_path: str | None = None,
        locale: str = "en-US",
        timezone: str = "America/New_York",
        geolocation: dict[str, float] | None = None,
    ):
        """
        Create a new browser context with anti-detection features.
        
        Args:
            user_agent: Custom user agent string
            viewport: Viewport size dict with width and height
            storage_state_path: Path to load storage state from
            save_storage_state_path: Path to save storage state to
            locale: Browser locale
            timezone: Browser timezone
            geolocation: Geolocation coordinates
            
        Yields:
            BrowserContext instance
        """
        if not self.browser:
            raise RuntimeError("Browser not started. Call start() first.")
            
        # Context options
        context_options: dict[str, Any] = {
            "user_agent": user_agent or AntiDetection.get_random_user_agent(),
            "viewport": viewport or AntiDetection.get_random_viewport(),
            "locale": locale,
            "timezone_id": timezone,
            "accept_downloads": True,
            "ignore_https_errors": True,
        }
        
        # Load existing storage state if provided
        if storage_state_path and os.path.exists(storage_state_path):
            context_options["storage_state"] = storage_state_path
            logger.info(f"Loading storage state from: {storage_state_path}")
            
        # Set geolocation if provided
        if geolocation:
            context_options["geolocation"] = geolocation
            context_options["permissions"] = ["geolocation"]
            
        # Create context
        context = await self.browser.new_context(**context_options)
        
        # Apply stealth scripts
        await AntiDetection.apply_stealth_scripts(context)
        
        try:
            yield context
            
            # Save storage state if path provided
            if save_storage_state_path:
                await context.storage_state(path=save_storage_state_path)
                logger.info(f"Saved storage state to: {save_storage_state_path}")
                
        finally:
            await context.close()
            
    @asynccontextmanager
    async def new_page(self, context: BrowserContext):
        """
        Create a new page in the given context.
        
        Args:
            context: Browser context
            
        Yields:
            Page instance
        """
        page = await context.new_page()
        
        # Set default timeout
        page.set_default_timeout(30000)  # 30 seconds
        
        # Enable request interception for monitoring
        await self._setup_request_monitoring(page)
        
        try:
            yield page
        finally:
            await page.close()
            
    async def _setup_request_monitoring(self, page: Page) -> None:
        """Setup request monitoring for debugging and rate limit detection."""
        
        async def log_response(response):
            """Log responses for monitoring."""
            if response.status >= 400:
                logger.warning(
                    f"HTTP {response.status} - {response.url} - "
                    f"Headers: {response.headers}"
                )
                
            # Check for rate limiting
            if response.status == 429:
                logger.error("Rate limit detected!")
                
        page.on("response", log_response)
        
    def get_auth_state_path(self, username: str) -> str:
        """Get path for storing authentication state."""
        return str(self._auth_dir / f"{username}.json")
        
    async def check_auth_state_exists(self, username: str) -> bool:
        """Check if authentication state exists for user."""
        auth_path = self.get_auth_state_path(username)
        return os.path.exists(auth_path)
        
    async def clear_auth_state(self, username: str) -> None:
        """Clear stored authentication state for user."""
        auth_path = self.get_auth_state_path(username)
        if os.path.exists(auth_path):
            os.remove(auth_path)
            logger.info(f"Cleared auth state for: {username}")
            
    async def take_screenshot(
        self,
        page: Page,
        path: str,
        full_page: bool = False
    ) -> None:
        """Take a screenshot of the current page."""
        await page.screenshot(path=path, full_page=full_page)
        logger.info(f"Screenshot saved to: {path}")
        
    async def save_page_content(self, page: Page, path: str) -> None:
        """Save page HTML content for debugging."""
        content = await page.content()
        with open(path, "w", encoding="utf-8") as f:
            f.write(content)
        logger.info(f"Page content saved to: {path}")
        
    async def wait_for_network_idle(
        self,
        page: Page,
        timeout: int = 30000
    ) -> None:
        """Wait for network to be idle."""
        await page.wait_for_load_state("networkidle", timeout=timeout)
        
    @classmethod
    @asynccontextmanager
    async def create(
        cls,
        headless: bool = True,
        proxy: dict[str, str] | None = None,
        browser_type: str = "chromium",
        debug: bool = False
    ):
        """
        Context manager for creating and managing PlaywrightClient.
        
        Args:
            headless: Run browser in headless mode
            proxy: Proxy configuration
            browser_type: Browser type - chromium, firefox, or webkit
            debug: Enable debug mode with DevTools and slow motion
        
        Example:
            async with PlaywrightClient.create(debug=True) as client:
                async with client.new_context() as context:
                    async with client.new_page(context) as page:
                        await page.goto('https://example.com')
        """
        client = cls(headless=headless, proxy=proxy, browser_type=browser_type, debug=debug)
        await client.start()
        try:
            yield client
        finally:
            await client.stop()