# Instagram Browser Automation with <PERSON><PERSON>

This module provides browser automation capabilities for Instagram using <PERSON><PERSON>, allowing you to post comments and perform other actions programmatically.

## Features

- ✅ Full Instagram login with session persistence
- ✅ 2FA (Two-Factor Authentication) support with console input
- ✅ Comment posting on Instagram posts
- ✅ Bulk comment operations
- ✅ Anti-detection measures (human-like behavior)
- ✅ Proxy support
- ✅ Headless and headed modes
- ✅ Comprehensive error handling
- ✅ Django integration

## Installation

1. Install Playwright and dependencies:
```bash
uv pip install playwright playwright-stealth
```

2. Install Playwright browsers:
```bash
uv run playwright install chromium
```

## Usage

### Command Line

#### Single Comment
```bash
python manage.py instagram_playwright_comment \
    --account-id 1 \
    --post-url "https://www.instagram.com/p/DKZ7A0XpB1U/" \
    --comment "Great post! 🔥"
```

#### Bulk Comments
```bash
# First create a JSON file with comments
echo '[
  {
    "post_url": "https://www.instagram.com/p/DKZ7A0XpB1U/",
    "comment_text": "Amazing content!"
  },
  {
    "post_url": "https://www.instagram.com/p/DKVPW5_T6dZ/",
    "comment_text": "Love this! ❤️"
  }
]' > comments.json

# Run bulk comment posting
python manage.py instagram_playwright_comment \
    --account-id 1 \
    --bulk-file comments.json \
    --delay 60
```

#### Test Login
```bash
python manage.py instagram_playwright_comment \
    --account-id 1 \
    --test-login \
    --headed
```

#### Debug Mode
```bash
# Debug mode for troubleshooting
python manage.py instagram_playwright_comment \
    --account-id 1 \
    --post-url "https://www.instagram.com/p/DKZ7A0XpB1U/" \
    --comment "Test comment" \
    --debug
```

### Python API

```python
from instagram_manager.services import PlaywrightCommentService

# Initialize service
service = PlaywrightCommentService()

# Post single comment
success, error, posted_comment = service.post_comment_sync(
    account_id=1,
    post_url="https://www.instagram.com/p/DKZ7A0XpB1U/",
    comment_text="Great post! 🔥",
    headless=True
)

# Post bulk comments
results = service.post_bulk_comments_sync(
    account_id=1,
    comments_data=[
        {"post_url": "...", "comment_text": "..."},
        {"post_url": "...", "comment_text": "..."}
    ],
    delay_between_comments=60
)
```

## Command Options

- `--account-id`: Instagram account ID from database (required)
- `--post-url`: Instagram post URL for single comment
- `--comment`: Comment text for single comment
- `--bulk-file`: JSON file path for bulk comments
- `--delay`: Seconds between comments in bulk mode (default: 60)
- `--headed`: Show browser window (default: headless)
- `--debug`: Enable debug mode (forces headed, opens DevTools, slows operations)
- `--proxy`: Proxy URL (format: *********************:port)
- `--test-login`: Only test login without posting

## Anti-Detection Features

1. **Human-like Typing**: Variable speed typing with occasional pauses
2. **Random Delays**: Random wait times between actions
3. **Mouse Movements**: Random mouse movements on page
4. **Browser Fingerprinting**: Removes automation indicators
5. **Realistic Viewports**: Uses common screen resolutions
6. **Session Persistence**: Reuses login sessions

## Best Practices

1. **Rate Limiting**: 
   - Wait 30-60 seconds between comments
   - Limit to 10-15 comments per session
   - Take breaks between sessions

2. **Content Variation**:
   - Use unique, meaningful comments
   - Include emojis for natural appearance
   - Vary comment length and style

3. **Account Safety**:
   - Use proxies for different accounts
   - Rotate between multiple accounts
   - Monitor for action blocks

4. **Error Handling**:
   - Check logs for rate limit warnings
   - Handle 2FA prompts manually
   - Save screenshots on errors

## Troubleshooting

### Login Issues
- Ensure account credentials are correct
- If 2FA is enabled, the script will prompt you to enter the code
- Try with `--headed` to see what's happening
- Clear saved session: delete `storage/auth/{username}.json`

### 2FA (Two-Factor Authentication)
When 2FA is enabled on your Instagram account:
1. The script will automatically detect when 2FA is required
2. You'll see a prompt in the console asking for the 6-digit code
3. Enter the code sent to your registered device
4. The script will continue automatically after successful verification

### Comment Posting Issues
- Verify the post URL is correct
- Check if comments are enabled on the post
- Look for rate limiting messages
- Try increasing delays between actions

### Browser Issues
- Run `playwright install chromium` to update browser
- Check system requirements for Playwright
- Try different browser types (firefox, webkit)

## Examples

See `instagram_manager/examples/test_playwright_comment.py` for complete examples.

## Architecture

- `PlaywrightClient`: Core browser automation client
- `InstagramAuth`: Handles login and session management
- `InstagramActions`: Performs actions like commenting
- `AntiDetection`: Implements human-like behavior
- `PlaywrightCommentService`: Django service layer integration