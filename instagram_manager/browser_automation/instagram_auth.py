"""
Instagram authentication module using <PERSON><PERSON>.
"""

import logging
from datetime import datetime
from typing import Any

from django.utils import timezone
from playwright.async_api import Page

from ..models import InstagramAccount, InstagramScrapingTask
from .anti_detection import AntiDetection
from .playwright_client import PlaywrightClient

logger = logging.getLogger(__name__)


class InstagramAuth:
    """Handles Instagram authentication and session management."""
    
    INSTAGRAM_URL = "https://www.instagram.com"
    LOGIN_URL = f"{INSTAGRAM_URL}/accounts/login/"
    
    def __init__(self, client: PlaywrightClient):
        """
        Initialize Instagram authentication handler.
        
        Args:
            client: PlaywrightClient instance
        """
        self.client = client
        
    async def login(
        self,
        username: str,
        password: str,
        account: InstagramAccount | None = None,
        force_new_login: bool = False
    ) -> tuple[bool, str | None]:
        """
        Login to Instagram account.
        
        Args:
            username: Instagram username
            password: Instagram password
            account: InstagramAccount model instance for tracking
            force_new_login: Force new login even if session exists
            
        Returns:
            Tuple of (success, error_message)
        """
        auth_state_path = self.client.get_auth_state_path(username)
        
        # Check for existing session if not forcing new login
        if not force_new_login and await self.client.check_auth_state_exists(username):
            logger.info(f"Found existing session for {username}, validating...")
            
            # Try to use existing session
            async with self.client.new_context(
                storage_state_path=auth_state_path
            ) as context:
                async with self.client.new_page(context) as page:
                    is_valid = await self._validate_session(page)
                    if is_valid:
                        logger.info(f"Existing session valid for {username}")
                        return True, None
                    else:
                        logger.info(f"Existing session invalid for {username}, re-authenticating...")
                        
        # Perform new login
        async with self.client.new_context(
            save_storage_state_path=auth_state_path
        ) as context:
            async with self.client.new_page(context) as page:
                success, error = await self._perform_login(page, username, password, account)
                return success, error
                
    async def _perform_login(
        self,
        page: Page,
        username: str,
        password: str,
        account: InstagramAccount | None = None
    ) -> tuple[bool, str | None]:
        """Perform the actual login process."""
        task = None
        # Don't create task for login as it's not a scraping task
            
        try:
            # Navigate to Instagram
            logger.info(f"Navigating to Instagram for {username}")
            await page.goto(self.INSTAGRAM_URL, wait_until="networkidle")
            
            # Random delay
            await AntiDetection.human_like_delay(1000, 2000)
            
            # Handle cookie banner
            await self._handle_cookie_banner(page)
            
            # Check if we're already on login page or need to navigate
            current_url = page.url
            if "/accounts/login" not in current_url:
                # Try to navigate directly to login page
                logger.info("Navigating directly to login page")
                await page.goto(self.LOGIN_URL, wait_until="networkidle")
                await AntiDetection.human_like_delay(1000, 2000)
                    
            # Wait for login form
            logger.info("Waiting for login form")
            username_input = await page.wait_for_selector(
                'input[name="username"], input[aria-label*="username" i], input[aria-label*="phone" i]',
                state="visible",
                timeout=15000
            )
            
            if not username_input:
                error = "Username input not found"
                await self._update_task(task, "failed", error)
                return False, error
                
            # Fill username
            logger.info("Filling username")
            # Click on username field first to ensure it's focused
            await username_input.click()
            await AntiDetection.human_like_delay(500, 1000)
            await username_input.fill(username)
            
            # Find and fill password
            logger.info("Finding password field")
            password_input = await page.wait_for_selector(
                'input[type="password"], input[name="password"]',
                state="visible",
                timeout=5000
            )
            
            if not password_input:
                error = "Password input not found"
                await self._update_task(task, "failed", error)
                return False, error
                
            logger.info("Filling password")
            await password_input.click()
            await AntiDetection.human_like_delay(500, 1000)
            await password_input.fill(password)
            
            # Random mouse movement
            await AntiDetection.random_mouse_movement(page)
            
            # Find and click login button
            login_submit = await page.query_selector('button[type="submit"]')
            if not login_submit:
                error = "Login submit button not found"
                await self._update_task(task, "failed", error)
                return False, error
                
            # Check if button is enabled
            is_disabled = await login_submit.get_attribute("disabled")
            if is_disabled:
                logger.info("Submit button disabled, waiting...")
                await page.wait_for_function(
                    'document.querySelector(\'button[type="submit"]\').disabled === false',
                    timeout=5000
                )
                
            logger.info("Clicking login button")
            await login_submit.click()
            
            # Wait for navigation or error
            try:
                await page.wait_for_url("**/*", timeout=30000)
            except Exception:
                # Navigation might not happen if there's an error
                pass
                
            # Check for errors
            error_msg = await self._check_login_errors(page)
            if error_msg:
                await self._update_task(task, "failed", error_msg)
                return False, error_msg
                
            # Check for 2FA
            await AntiDetection.human_like_delay(2000, 3000)
            logger.info("Checking for 2FA requirement...")
            
            # Log current page content for debugging
            try:
                content_snippet = await page.content()
                if len(content_snippet) > 500:
                    content_snippet = content_snippet[:500] + "..."
                logger.debug(f"Page content snippet: {content_snippet}")
            except Exception:
                pass
            
            if await self._check_2fa_required(page):
                logger.info("2FA verification required")
                # Screenshot for debugging removed - was creating fixed files
                    
                # Handle 2FA
                success = await self._handle_2fa(page)
                if not success:
                    error = "2FA verification failed"
                    await self._update_task(task, "failed", error)
                    return False, error
                logger.info("2FA verification completed")
                
            # Check for suspicious login
            if await self._check_suspicious_login(page):
                error = "Suspicious login detected - manual verification required"
                await self._update_task(task, "failed", error)
                return False, error
                
            # Verify successful login
            await AntiDetection.human_like_delay(2000, 3000)
            
            # Wait for navigation to complete
            await page.wait_for_load_state("networkidle")
            
            # Check URL first - if we're still on login page, it failed
            current_url = page.url
            logger.info(f"Current URL after login: {current_url}")
            
            # Debugging output removed - was creating fixed files
            
            if "/accounts/login" in current_url:
                # Check for error messages
                error_msg = await self._check_login_errors(page)
                if error_msg:
                    logger.error(f"Login error detected: {error_msg}")
                    return False, error_msg
                    
                # Check if there's a challenge or verification needed
                page_content = await page.content()
                if "challenge" in page_content.lower() or "verify" in page_content.lower():
                    logger.info("Challenge or verification detected")
                    # Wait a bit more
                    await AntiDetection.human_like_delay(3000, 5000)
                    # Re-check URL
                    current_url = page.url
                    if "/accounts/login" not in current_url:
                        logger.info(f"Redirected to: {current_url}")
                    else:
                        return False, "Challenge or verification required"
                
                return False, "Still on login page after submit"
            
            # Wait a bit more and check for logged-in indicators
            await AntiDetection.human_like_delay(1000, 2000)
            is_logged_in = await self._check_logged_in(page)
            
            if is_logged_in:
                logger.info(f"Successfully logged in as {username}")
                await self._update_task(task, "completed")
                
                # Update account last login
                if account:
                    account.last_login = timezone.now()
                    await account.asave()
                    
                return True, None
            else:
                error = "Login verification failed"
                # Take screenshot for debugging
                try:
                    screenshot_path = f"login_failed_{username}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png"
                    await page.screenshot(path=screenshot_path)
                    logger.error(f"Login verification failed. Screenshot saved to: {screenshot_path}")
                except Exception:
                    pass
                await self._update_task(task, "failed", error)
                return False, error
                
        except Exception as e:
            error = f"Login error: {e!s}"
            logger.error(error, exc_info=True)
            await self._update_task(task, "failed", error)
            
            # Save screenshot for debugging
            if account:
                screenshot_path = f"/tmp/login_error_{username}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png"
                await self.client.take_screenshot(page, screenshot_path)
                
            return False, error
            
    async def _validate_session(self, page: Page) -> bool:
        """Validate existing session is still active."""
        try:
            logger.info("Validating existing session...")
            # Try to navigate to a protected page that requires login
            # Using explore page as it's faster and requires auth
            await page.goto(f"{self.INSTAGRAM_URL}/explore/", wait_until="domcontentloaded")
            
            # Quick check - if redirected to login, session is invalid
            current_url = page.url
            if "/accounts/login" in current_url:
                logger.info("Session is invalid - redirected to login")
                return False
            
            # Wait a bit for page to settle
            await AntiDetection.human_like_delay(500, 1000)
            
            # Check if we're logged in
            is_logged_in = await self._check_logged_in(page)
            
            if is_logged_in:
                logger.info("Session is valid - no need to login again!")
            else:
                logger.info("Session is invalid or expired")
                
            return is_logged_in
            
        except Exception as e:
            logger.exception(f"Session validation error: {e}")
            return False
            
    async def _check_logged_in(self, page: Page) -> bool:
        """Check if currently logged in to Instagram."""
        # Multiple indicators of being logged in
        logged_in_selectors = [
            '[aria-label="Home"]',
            '[aria-label="Search"]',
            '[aria-label="New post"]',
            'svg[aria-label="New post"]',
            'a[href="/direct/inbox/"]',
            'div[role="menu"]',
            # Additional selectors for newer Instagram UI
            'a[href="/"]',  # Home link
            'nav[role="navigation"]',  # Main navigation
            'input[placeholder*="Search" i]',  # Search input
            '[data-testid="user-avatar"]',  # User avatar
            'img[alt*="profile picture" i]',  # Profile picture
        ]
        
        for selector in logged_in_selectors:
            element = await page.query_selector(selector)
            if element:
                logger.debug(f"Found logged-in indicator: {selector}")
                return True
                
        # Also check we're not on login page
        if "/accounts/login" in page.url:
            return False
            
        return False
        
    async def _handle_cookie_banner(self, page: Page) -> None:
        """Handle cookie consent banner if present."""
        try:
            # Look for cookie banner buttons
            cookie_selectors = [
                'button:has-text("Allow essential and optional cookies")',
                'button:has-text("Allow all cookies")',
                'button:has-text("Only allow essential cookies")',
                'button[type="button"]:has-text("Accept")',
            ]
            
            for selector in cookie_selectors:
                try:
                    button = await page.wait_for_selector(selector, timeout=3000)
                    if button:
                        logger.info("Accepting cookies")
                        await button.click()
                        await AntiDetection.human_like_delay(500, 1000)
                        break
                except Exception:
                    continue
                    
        except Exception as e:
            logger.debug(f"No cookie banner found: {e}")
            
    async def _find_login_button(self, page: Page) -> Any | None:
        """Find login button on homepage."""
        login_selectors = [
            'a[href="/accounts/login/"]',
            'button:has-text("Log in")',
            'a:has-text("Log in")',
            'span:has-text("Log in")',
        ]
        
        for selector in login_selectors:
            element = await page.query_selector(selector)
            if element:
                return element
                
        return None
        
    async def _check_login_errors(self, page: Page) -> str | None:
        """Check for login error messages."""
        error_selectors = [
            '[data-testid="login-error-message"]',
            '[role="alert"]',
            'div[id*="error"]',
            'span[id*="error"]',
            'p[id*="error"]',
            "#slfErrorAlert",
        ]
        
        for selector in error_selectors:
            element = await page.query_selector(selector)
            if element:
                error_text = await element.text_content()
                if error_text:
                    logger.warning(f"Login error: {error_text}")
                    return error_text.strip()
                    
        # Check for specific error patterns in page content
        content = await page.content()
        error_patterns = [
            "Sorry, your password was incorrect",
            "The username you entered doesn't belong to an account",
            "We couldn't connect to Instagram",
            "Please wait a few minutes before you try again",
            "suspicious activity",
            "Your account has been temporarily locked",
            "Please enter the code",
            "Confirm Your Identity",
        ]
        
        for pattern in error_patterns:
            if pattern in content:
                return pattern
                
        return None
        
    async def _check_2fa_required(self, page: Page) -> bool:
        """Check if 2FA is required."""
        current_url = page.url
        logger.info(f"Checking for 2FA on URL: {current_url}")
        
        # Check URL patterns
        if any(pattern in current_url for pattern in ["/two_factor", "/challenge", "/accounts/login/two_factor"]):
            logger.info("2FA detected by URL pattern")
            return True
            
        # Check for 2FA elements
        twofa_selectors = [
            'input[name="verificationCode"]',
            'input[aria-label*="security code" i]',
            'input[aria-label*="confirmation code" i]',
            'input[type="tel"][maxlength="6"]',
            'input[placeholder*="code" i]',
            'button:has-text("Confirm")',
            'button:has-text("Submit")',
            'button:has-text("Verify")',
        ]
        
        for selector in twofa_selectors:
            element = await page.query_selector(selector)
            if element:
                logger.info(f"2FA detected by selector: {selector}")
                return True
                
        # Check page content for 2FA patterns
        try:
            content = await page.content()
            twofa_patterns = [
                "two-factor authentication",
                "Enter the 6-digit code",
                "security code",
                "verification code",
                "confirm your identity",
                "We sent you a security code",
                "Enter confirmation code",
            ]
            
            for pattern in twofa_patterns:
                if pattern.lower() in content.lower():
                    logger.info(f"2FA detected by content pattern: {pattern}")
                    return True
        except Exception:
            pass
                
        return False
        
    async def _handle_2fa(self, page: Page) -> bool:
        """Handle 2FA verification by requesting code from user."""
        try:
            # Wait for 2FA input field
            logger.info("Waiting for 2FA input field...")
            
            # Try multiple selectors
            selectors = [
                'input[name="verificationCode"]',
                'input[name="security_code"]',
                'input[aria-label*="security code" i]',
                'input[aria-label*="confirmation code" i]',
                'input[aria-label*="Enter confirmation code" i]',
                'input[type="tel"][maxlength="6"]',
                'input[placeholder*="code" i]',
                'input[placeholder*="######" i]',
                'input[autocomplete="one-time-code"]',
                'input[inputmode="numeric"]',
                'input[data-testid*="confirmation"]',
                'form input[type="text"]',  # Fallback
                'form input[type="tel"]',   # Fallback
                "form input",               # Last resort
            ]
            
            code_input = None
            # First wait for any form element to appear
            await page.wait_for_selector("form", state="visible", timeout=10000)
            
            for selector in selectors:
                try:
                    code_input = await page.wait_for_selector(
                        selector,
                        state="visible",
                        timeout=5000
                    )
                    if code_input:
                        logger.info(f"Found 2FA input with selector: {selector}")
                        break
                except Exception:
                    continue
            
            if not code_input:
                logger.error("2FA code input field not found")
                # HTML debugging output removed - was creating fixed files
                return False
            
            # Request code from user via console
            print("\n" + "="*50)
            print("2FA VERIFICATION REQUIRED")
            print("="*50)
            print("Instagram has sent a verification code to your registered device.")
            print("Please enter the 6-digit code:")
            
            # Get code from user input
            # Use asyncio to run input in executor to avoid blocking
            import asyncio
            loop = asyncio.get_event_loop()
            code = await loop.run_in_executor(None, input, "Code: ")
            code = code.strip()
            
            # Validate code format
            if not code or not code.isdigit() or len(code) != 6:
                logger.error("Invalid 2FA code format")
                print("Error: Please enter a valid 6-digit code")
                return False
            
            # Enter the code
            logger.info("Entering 2FA code...")
            await code_input.click()
            await AntiDetection.human_like_delay(300, 500)
            # Clear any existing text first
            await code_input.fill("")
            await AntiDetection.human_like_delay(100, 200)
            await code_input.fill(code)
            
            # Wait a bit for the button to become enabled
            await AntiDetection.human_like_delay(500, 1000)
            
            # Find and click submit button
            submit_selectors = [
                'button[type="submit"]',
                'button:has-text("Confirm")',
                'button:has-text("Submit")', 
                'button:has-text("Verify")',
                'button:has-text("Continue")',
                'button:has-text("Next")',
                'button:has-text("Далее")',  # Russian
                'button:has-text("Подтвердить")',  # Russian
                'input[type="submit"]',
                "form button:not([disabled])",  # Any enabled button in form
                "form button",  # Fallback
            ]
            
            submit_button = None
            for selector in submit_selectors:
                try:
                    submit_button = await page.wait_for_selector(
                        selector,
                        state="visible",
                        timeout=3000
                    )
                    if submit_button:
                        logger.info(f"Found submit button with selector: {selector}")
                        break
                except Exception:
                    continue
            
            if submit_button:
                # Check if button is enabled
                is_disabled = await submit_button.get_attribute("disabled")
                if is_disabled:
                    logger.info("Waiting for submit button to be enabled...")
                    await page.wait_for_function(
                        'document.querySelector(\'button[type="submit"]\')?.disabled === false',
                        timeout=5000
                    )
                
                logger.info("Submitting 2FA code...")
                await submit_button.click()
                
                # Wait for navigation or error
                await AntiDetection.human_like_delay(3000, 5000)
                
                # Wait for page to load
                await page.wait_for_load_state("networkidle")
                
                # Check current URL and content
                current_url = page.url
                logger.info(f"URL after 2FA submit: {current_url}")
                
                # Screenshot for debugging removed - was creating fixed files
                
                # Check if we're still on 2FA page
                if await self._check_2fa_required(page):
                    # Check for error message
                    error_msg = await self._check_login_errors(page)
                    if error_msg:
                        logger.error(f"2FA error: {error_msg}")
                        print(f"\nError: {error_msg}")
                        print("Please try again.")
                        # Recursively call to try again
                        return await self._handle_2fa(page)
                    
                    logger.error("Still on 2FA page after submit, verification may have failed")
                    return False
                
                logger.info("2FA verification successful")
                return True
            else:
                # Try pressing Enter
                logger.info("Submit button not found, pressing Enter...")
                await page.keyboard.press("Enter")
                await AntiDetection.human_like_delay(2000, 3000)
                
                # Check if 2FA passed
                if not await self._check_2fa_required(page):
                    return True
                    
                return False
                
        except Exception as e:
            logger.exception(f"Error handling 2FA: {e!s}")
            return False
        
    async def _check_suspicious_login(self, page: Page) -> bool:
        """Check for suspicious login prompt."""
        suspicious_patterns = [
            "Was this you?",
            "Suspicious Login Attempt",
            "We Detected An Unusual Login Attempt",
            "Confirm it's You",
        ]
        
        content = await page.content()
        for pattern in suspicious_patterns:
            if pattern.lower() in content.lower():
                return True
                
        return False
        
    async def _create_task(
        self,
        account: InstagramAccount,
        task_type: str
    ) -> InstagramScrapingTask | None:
        """Create a scraping task for tracking."""
        try:
            task = InstagramScrapingTask(
                task_type=task_type,
                target_identifier=account.username,
                status="in_progress",
                started_at=timezone.now(),
                brightdata_dataset_id="playwright_automation"
            )
            await task.asave()
            return task
        except Exception as e:
            logger.exception(f"Failed to create task: {e}")
            return None
            
    async def _update_task(
        self,
        task: InstagramScrapingTask | None,
        status: str,
        error_message: str | None = None
    ) -> None:
        """Update task status."""
        if not task:
            return
            
        try:
            task.status = status
            task.completed_at = timezone.now()
            if error_message:
                task.error_message = error_message
            await task.asave()
        except Exception as e:
            logger.exception(f"Failed to update task: {e}")
            
    async def logout(self, page: Page) -> bool:
        """
        Logout from Instagram.
        
        Args:
            page: Page instance
            
        Returns:
            Success status
        """
        try:
            # Go to profile menu
            profile_button = await page.query_selector('[aria-label="Profile"]')
            if profile_button:
                await profile_button.click()
                await AntiDetection.human_like_delay(1000, 2000)
                
                # Click logout
                logout_button = await page.query_selector('button:has-text("Log out")')
                if logout_button:
                    await logout_button.click()
                    await page.wait_for_url("**/*")
                    return True
                    
            return False
            
        except Exception as e:
            logger.exception(f"Logout error: {e}")
            return False