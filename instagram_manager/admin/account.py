"""
Админ классы для работы с аккаунтами Instagram для автоматизации.
"""

from django.contrib import admin, messages

from instagram_manager.models import InstagramAccount

from .base import BaseInstagramAdmin


@admin.register(InstagramAccount)
class InstagramAccountAdmin(BaseInstagramAdmin):
    """Админ класс для аккаунтов Instagram"""
    
    list_display = [
        "username",
        "is_active",
        "is_logged_in",
        "daily_comment_count",
        "last_comment_at",
        "last_used_at",
    ]
    list_filter = [
        "is_active",
        "is_logged_in",
        "created_at"
    ]
    search_fields = ["username"]
    readonly_fields = [
        "cookies",
        "session_data",
        "last_used_at",
        "created_at",
        "updated_at",
    ]
    
    def get_fieldsets(self, request, obj=None):
        """Dynamic fieldsets based on whether we're creating or editing"""
        if obj is None:  # Creating new account
            return (
                (
                    "Account Information",
                    {"fields": ("username", "password", "confirm_password")},
                ),
            )
        else:  # Editing existing account
            return (
                ("Account Information", {"fields": ("username",)}),
                ("Status", {"fields": ("is_active", "is_logged_in")}),
                ("Rate Limits", {"fields": ("daily_comment_count", "last_comment_at")}),
                (
                    "Session Data",
                    {"classes": ("collapse",), "fields": ("session_data", "cookies")},
                ),
                (
                    "Timestamps",
                    {"fields": ("created_at", "updated_at", "last_used_at")},
                ),
            )
    
    actions = [
        "reset_daily_limits",
        "mark_as_logged_out",
        "change_password"
    ]
    
    @admin.action(description="Reset daily comment limits")
    def reset_daily_limits(self, request, queryset):
        """Сброс дневных лимитов комментариев"""
        updated = queryset.update(daily_comment_count=0)
        messages.success(request, f"Reset daily limits for {updated} accounts")
    
    @admin.action(description="Mark as logged out")
    def mark_as_logged_out(self, request, queryset):
        """Пометить аккаунты как разлогиненные"""
        updated = queryset.update(
            is_logged_in=False,
            cookies=None,
            session_data=None
        )
        messages.success(request, f"Marked {updated} accounts as logged out")
    
    @admin.action(description="Change password")
    def change_password(self, request, queryset):
        """Изменить пароль аккаунта"""
        if queryset.count() != 1:
            messages.error(
                request,
                "Please select exactly one account to change password"
            )
            return
        
        account = queryset.first()
        messages.info(
            request,
            f"To change password for {account.username}, "
            f"edit the account and use the password fields",
        )
    
    def get_form(self, request, obj=None, **kwargs):
        """Use custom form for password handling"""
        from instagram_manager.forms import AccountPasswordForm
        
        if obj is None:  # Creating new account
            kwargs["form"] = AccountPasswordForm
        return super().get_form(request, obj, **kwargs)