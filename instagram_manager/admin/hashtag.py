"""
Админ классы для работы с хештегами Instagram.
"""

from django.contrib import admin
from django.db.models import Count

from instagram_manager.models import InstagramHashtag

from .base import BaseInstagramAdmin


@admin.register(InstagramHashtag)
class InstagramHashtagAdmin(BaseInstagramAdmin):
    """Админ класс для хештегов Instagram"""
    
    list_display = (
        "name",
        "post_count",
        "created_at",
        "last_scraped_at"
    )
    search_fields = ["name"]
    readonly_fields = (
        "created_at",
        "updated_at",
        "last_scraped_at"
    )
    list_filter = (
        "last_scraped_at",
        "created_at"
    )
    
    def get_queryset(self, request):
        """Оптимизированный queryset с подсчетом постов"""
        qs = super().get_queryset(request)
        return qs.annotate(calculated_post_count=Count("posts"))
    
    def post_count(self, obj):
        """Количество постов с хештегом"""
        return getattr(obj, "calculated_post_count", 0)
    
    post_count.short_description = "Posts"
    post_count.admin_order_field = "calculated_post_count"