"""
Админ классы для работы с медиа файлами Instagram.
"""

from django.contrib import admin
from django.utils.html import format_html

from instagram_manager.models import InstagramMedia

from .base import MediaPreviewMixin


class InstagramMediaInline(MediaPreviewMixin, admin.TabularInline):
    """Inline админ для медиа файлов в постах"""
    
    model = InstagramMedia
    extra = 0
    readonly_fields = (
        "media_preview_display",
        "media_type",
        "dimensions",
        "is_downloaded",
        "download_status",
    )
    fields = (
        "media_preview_display",
        "media_type",
        "dimensions",
        "order_index",
        "is_downloaded",
        "download_status",
    )
    
    def media_preview_display(self, obj):
        """Отображение превью медиа"""
        return self.media_preview(obj, width=100, height=100)
    
    media_preview_display.short_description = "Preview"
    
    def dimensions(self, obj):
        """Отображение размеров медиа"""
        if obj.width and obj.height:
            return f"{obj.width}x{obj.height}"
        return "-"
    
    dimensions.short_description = "Dimensions"
    
    def download_status(self, obj):
        """Статус загрузки медиа"""
        if obj.is_downloaded:
            return format_html('<span style="color: green;">✓ Downloaded</span>')
        else:
            return format_html('<span style="color: orange;">⚠ Remote URL</span>')
    
    download_status.short_description = "Status"
    
    def has_add_permission(self, request, obj=None):
        """Запрещаем добавление медиа через админку"""
        return False
    
    def has_delete_permission(self, request, obj=None):
        """Разрешаем удаление только суперадминам"""
        return request.user.is_superuser