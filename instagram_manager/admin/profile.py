"""
Админ классы для работы с профилями Instagram.
"""

import logging

from django.contrib import admin, messages
from django.shortcuts import redirect
from django.urls import reverse
from django.utils.html import format_html

from instagram_manager.models import InstagramMedia, InstagramPost, InstagramProfile

from .base import BaseInstagramAdmin

logger = logging.getLogger(__name__)


@admin.register(InstagramProfile)
class InstagramProfileAdmin(BaseInstagramAdmin):
    """Админ класс для профилей Instagram"""
    
    list_display = (
        "username",
        "full_name",
        "follower_count_display",
        "following_count",
        "post_count",
        "is_verified_icon",
        "is_private",
        "last_scraped_display",
    )
    list_filter = (
        "is_verified",
        "is_business",
        "is_private",
        "last_scraped_at"
    )
    search_fields = [
        "username",
        "full_name",
        "bio"
    ]
    
    # Оптимизация запросов
    list_select_related = []  # Нет FK в InstagramProfile
    list_prefetch_related = ["posts"]  # Если нужны посты в list_display
    
    readonly_fields = (
        "profile_id",
        "created_at",
        "updated_at",
        "last_scraped_at",
        "profile_pic_preview",
        "profile_stats",
    )
    
    fieldsets = (
        (
            "Basic Information",
            {
                "fields": (
                    "profile_id",
                    "username",
                    "full_name",
                    "bio",
                    "profile_pic_preview",
                    "profile_pic_url",
                    "profile_pic_hd_url",
                )
            },
        ),
        (
            "Profile Stats",
            {
                "fields": (
                    "profile_stats",
                    "follower_count",
                    "following_count",
                    "post_count",
                )
            },
        ),
        (
            "Profile Type",
            {
                "fields": (
                    "is_verified",
                    "is_business",
                    "is_private",
                    "category_name",
                    "external_url",
                )
            },
        ),
        ("Metadata", {"fields": ("created_at", "updated_at", "last_scraped_at")}),
    )
    
    actions = [
        "import_posts",
        "batch_import_posts",
        "refresh_profile",
        "download_media",
        "export_posts_to_majila"
    ]
    
    def follower_count_display(self, obj):
        """Форматированное отображение подписчиков"""
        return self.format_count(obj.follower_count, use_colors=True)
    
    follower_count_display.short_description = "Followers"
    follower_count_display.admin_order_field = "follower_count"
    
    def is_verified_icon(self, obj):
        """Иконка верификации"""
        return self.boolean_icon(obj.is_verified, "Verified", "Not verified")
    
    is_verified_icon.short_description = "Verified"
    is_verified_icon.admin_order_field = "is_verified"
    
    def last_scraped_display(self, obj):
        """Время последнего обновления с подсветкой"""
        return self.colored_date_display(
            obj.last_scraped_at,
            thresholds={"green": 3, "orange": 7, "red": 14}
        )
    
    last_scraped_display.short_description = "Last Updated"
    last_scraped_display.admin_order_field = "last_scraped_at"
    
    def profile_pic_preview(self, obj):
        """Превью фото профиля"""
        if obj.profile_pic_url:
            return format_html(
                '<img src="{}" style="max-width: 150px; max-height: 150px; '
                'border-radius: 50%;" onerror="this.style.display=\'none\';" />',
                obj.profile_pic_url,
            )
        return "No photo"
    
    profile_pic_preview.short_description = "Profile Picture"
    
    def profile_stats(self, obj):
        """Статистика профиля"""
        engagement_rate = self._calculate_engagement_rate(obj)
        
        return format_html(
            '<div style="font-size: 14px;">'
            "<strong>Followers:</strong> {:,}<br>"
            "<strong>Following:</strong> {:,}<br>"
            "<strong>Posts:</strong> {:,}<br>"
            "<strong>Engagement Rate:</strong> {:.2f}%"
            "</div>",
            obj.follower_count,
            obj.following_count,
            obj.post_count,
            engagement_rate,
        )
    
    profile_stats.short_description = "Statistics"
    
    def _calculate_engagement_rate(self, profile):
        """Расчет engagement rate"""
        if profile.follower_count == 0:
            return 0
        
        # Получаем последние посты с оптимизацией
        recent_posts = profile.posts.select_related("profile").order_by("-posted_at")[:12]
        if not recent_posts:
            return 0
        
        total_engagement = sum(
            post.like_count + post.comment_count for post in recent_posts
        )
        
        avg_engagement = total_engagement / len(recent_posts)
        engagement_rate = (avg_engagement / profile.follower_count) * 100
        
        return engagement_rate
    
    @admin.action(description="Import posts for selected profiles")
    def import_posts(self, request, queryset):
        """Action для импорта постов"""
        from instagram_manager.services import ImportService
        
        force_sync = request.GET.get("sync", False)
        
        for profile in queryset:
            result = ImportService.import_posts(
                profile_id=profile.id,
                force_sync=force_sync,
                limit=50,  # Default limit
                import_comments=False,
                import_media=True,
            )
            
            if isinstance(result, dict):
                # Синхронный режим
                if result.get("status") == "success":
                    messages.success(
                        request,
                        f"Imported {result['posts_imported']} posts for {profile.username}"
                    )
                else:
                    messages.error(
                        request,
                        f"Failed to import posts for {profile.username}: {result.get('errors', ['Unknown error'])[0]}"
                    )
            else:
                # Асинхронный режим
                messages.info(
                    request,
                    f"Started async posts import for {profile.username} (Task: {result.id})"
                )
    
    @admin.action(description="Refresh profile data")
    def refresh_profile(self, request, queryset):
        """Action для обновления данных профиля"""
        from instagram_manager.services import ImportService
        
        force_sync = request.GET.get("sync", False)
        
        for profile in queryset:
            result = ImportService.import_profile(
                username=profile.username,
                force_sync=force_sync,
                update_existing=True,
                import_posts=False,
                import_followers=False,
            )
            
            if isinstance(result, dict):
                # Синхронный режим
                if result.get("status") == "success":
                    messages.success(
                        request,
                        f"Profile {profile.username} refreshed successfully"
                    )
                else:
                    messages.error(
                        request,
                        f"Failed to refresh {profile.username}: {result.get('errors', ['Unknown error'])[0]}"
                    )
            else:
                # Асинхронный режим
                messages.info(
                    request,
                    f"Started async profile refresh for {profile.username} (Task: {result.id})"
                )
    
    @admin.action(description="Download media for selected profiles")
    def download_media(self, request, queryset):
        """Action для загрузки медиафайлов"""
        from instagram_manager.instagram_api.data_handlers.media_handler import MediaHandler
        
        total_downloaded = 0
        total_errors = 0
        
        # Create MediaHandler instance (without GCS for now)
        handler = MediaHandler(save_to_gcs=False)
        
        for profile in queryset:
            # Получаем все медиа для постов профиля
            media_items = InstagramMedia.objects.filter(
                post__profile=profile,
                is_downloaded=False
            )
            
            for media in media_items:
                try:
                    if handler.download_media(media):
                        total_downloaded += 1
                        # Для видео также загружаем миниатюру
                        if media.media_type == "video" and media.thumbnail_url:
                            handler.download_thumbnail(media)
                except Exception as e:
                    total_errors += 1
                    logger.exception(f"Error downloading media {media.external_id}: {e!s}")
        
        if total_downloaded > 0:
            messages.success(
                request,
                f"Successfully downloaded {total_downloaded} media files"
            )
        if total_errors > 0:
            messages.warning(
                request,
                f"Failed to download {total_errors} media files"
            )
    
    @admin.action(description="Batch import posts for selected profiles")
    def batch_import_posts(self, request, queryset):
        """Action для batch импорта постов"""
        selected_ids = list(queryset.values_list("id", flat=True))
        request.session["selected_profiles_for_batch_import"] = selected_ids
        
        # Перенаправляем на форму batch импорта
        return redirect(reverse("instagram_manager:admin_batch_import_posts_form"))
    
    @admin.action(description="Export posts to Majila Community")
    def export_posts_to_majila(self, request, queryset):
        """Action для экспорта постов профилей в Majila"""
        # Собираем все посты выбранных профилей
        posts = InstagramPost.objects.filter(profile__in=queryset)
        
        if not posts.exists():
            messages.warning(request, "No posts found for selected profiles")
            return
        
        # Сохраняем ID постов в сессии
        request.session["selected_posts_for_majila_export"] = list(
            posts.values_list("id", flat=True)
        )
        
        # Перенаправляем на форму экспорта
        return redirect("instagram_manager:admin_export_posts_to_majila_form")


class InstagramProfileAdminWithMajila(InstagramProfileAdmin):
    """Extended InstagramProfileAdmin with Majila accounts inline"""
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Импортируем здесь чтобы избежать циклических импортов
        from .majila import MajilaServiceAccountInline
        self.inlines = [MajilaServiceAccountInline]