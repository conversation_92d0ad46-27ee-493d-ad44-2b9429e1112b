"""
Админ классы для работы с задачами скрапинга Instagram.
"""

import logging

from celery.result import AsyncResult
from django.conf import settings
from django.contrib import admin, messages
from django.shortcuts import redirect, render
from django.urls import path, reverse
from django.utils.html import format_html

from instagram_manager.models import InstagramScrapingTask, InstagramSubTask

from .base import BaseInstagramAdmin

logger = logging.getLogger(__name__)


class InstagramSubTaskInline(admin.TabularInline):
    """Inline класс для отображения подзадач"""
    model = InstagramSubTask
    fk_name = "parent_task"
    fields = (
        "subtask_type",
        "target_identifier", 
        "status",
        "progress",
        "processed_items",
        "total_items",
        "result_summary",
        "created_at",
        "started_at",
        "completed_at",
    )
    readonly_fields = fields
    extra = 0
    can_delete = False
    verbose_name = "Subtask"
    verbose_name_plural = "Subtasks (All)"
    
    def has_add_permission(self, request, obj=None):
        return False
    
    def get_queryset(self, request):
        """Получаем все подзадачи, включая completed"""
        qs = super().get_queryset(request)
        # Сортируем по времени создания (новые сверху)
        return qs.order_by("-created_at")
    
    def result_summary(self, obj):
        """Отображение краткой сводки результатов"""
        if not obj.result_data:
            return "-"
        
        # Извлекаем основные данные из result_data
        result_data = obj.result_data
        message = result_data.get("message", "")
        
        # Добавляем информацию об источниках если есть
        sources_info = result_data.get("sources_info", "")
        if sources_info and obj.status == "completed":
            message_with_sources = f"{message} (from {sources_info})" if message else f"Completed (from {sources_info})"
        else:
            message_with_sources = message
        
        if obj.status == "completed":
            return format_html(
                '<span style="color: #5cb85c;" title="{}">{}</span>',
                message_with_sources,
                message_with_sources[:50] + "..." if len(message_with_sources) > 50 else message_with_sources
            )
        elif obj.status == "failed":
            error = obj.error_message or result_data.get("error", "Unknown error")
            return format_html(
                '<span style="color: #d9534f;" title="{}">{}</span>',
                error,
                error[:50] + "..." if len(error) > 50 else error
            )
        else:
            return "-"
    
    result_summary.short_description = "Result"


@admin.register(InstagramScrapingTask)
class InstagramScrapingTaskAdmin(BaseInstagramAdmin):
    """Админ класс для задач скрапинга Instagram"""
    
    inlines = [InstagramSubTaskInline]

    list_display = (
        "task_type",
        "target_identifier",
        "status",
        "subtasks_progress_display",
        "is_periodic_display",
        "next_run_display",
        "periodic_stats_display",
        "skip_media_download",
        "items_scraped_display",
        "created_at",
        "completed_at",
    )
    list_filter = ("task_type", "status", "is_periodic", "created_at")
    
    def get_queryset(self, request):
        """Показываем только задачи верхнего уровня"""
        qs = super().get_queryset(request)
        # Фильтруем старые подзадачи (которые имеют parent_task)
        return qs.filter(parent_task__isnull=True)
    search_fields = ["target_identifier", "brightdata_snapshot_id", "celery_beat_name"]
    readonly_fields = (
        "brightdata_snapshot_id",
        "created_at",
        "started_at",
        "completed_at",
        "error_message",
        "parent_task_display",
        "subtasks_list",
    )

    fieldsets = (
        (
            "Task Information",
            {
                "fields": (
                    "task_type",
                    "target_identifier",
                    "status",
                    "skip_media_download",
                    "brightdata_dataset_id",
                    "brightdata_snapshot_id",
                )
            },
        ),
        (
            "Task Hierarchy",
            {
                "fields": ("parent_task_display", "subtasks_list"),
                "classes": ("collapse",),
            },
        ),
        ("Results", {"fields": ("items_scraped", "error_message")}),
        ("Timestamps", {"fields": ("created_at", "started_at", "completed_at")}),
    )

    def get_urls(self):
        """Добавляем кастомные URLs для импорта"""
        urls = super().get_urls()
        custom_urls = [
            path(
                "import/",
                self.admin_site.admin_view(self.import_view),
                name="instagram_manager_instagramscrapingtask_import",
            ),
            # Новый URL для batch импорта
            path(
                "batch-import/",
                self.admin_site.admin_view(self.batch_import_view),
                name="instagram_manager_instagramscrapingtask_batch_import",
            ),
        ]
        return custom_urls + urls

    def changelist_view(self, request, extra_context=None):
        """Добавляем кнопки импорта в список"""
        extra_context = extra_context or {}
        extra_context["show_import_button"] = True
        extra_context["show_batch_import_button"] = True
        return super().changelist_view(request, extra_context=extra_context)

    def import_view(self, request):
        """Форма для создания задачи импорта"""
        from instagram_manager.forms import InstagramImportTaskForm

        if request.method == "POST":
            form = InstagramImportTaskForm(request.POST)
            if form.is_valid():
                task_type = form.cleaned_data["task_type"]
                target_identifier = form.cleaned_data["target_identifier"]
                posts_limit = form.cleaned_data.get("posts_limit", 0)
                skip_media_download = form.cleaned_data.get(
                    "skip_media_download", False
                )
                post_types = form.cleaned_data.get("post_types", [])
                execution_mode = form.cleaned_data.get("execution_mode", "async")
                force_sync = execution_mode == "sync"

                # Создаем задачу импорта
                dataset_mapping = {
                    "profile": settings.BRIGHTDATA_DATASETS.get(
                        "instagram_profile", "default"
                    ),
                    "posts": settings.BRIGHTDATA_DATASETS.get(
                        "instagram_posts", "default"
                    ),
                    "hashtag": settings.BRIGHTDATA_DATASETS.get(
                        "instagram_posts", "default"
                    ),
                }

                task = InstagramScrapingTask.objects.create(
                    task_type=task_type,
                    target_identifier=target_identifier,
                    brightdata_dataset_id=dataset_mapping.get(task_type),
                    skip_media_download=skip_media_download,
                )

                messages.success(
                    request,
                    f"Import task created successfully for {task_type}: {target_identifier}",
                )

                # Запускаем импорт
                try:
                    from instagram_manager.services import ImportService

                    # Логируем количество постов для импорта
                    posts_limit_msg = (
                        "all available" if posts_limit == 0 else str(posts_limit)
                    )
                    messages.info(request, f"Importing {posts_limit_msg} posts")

                    if task_type == "profile":
                        # Используем batch import даже для одного профиля
                        # чтобы обеспечить создание подзадач и правильное обновление статусов
                        result = ImportService.import_batch_profiles(
                            usernames=[target_identifier],  # Передаем как список
                            force_sync=force_sync,
                            import_posts=True,
                            posts_limit=posts_limit if posts_limit > 0 else None,
                            skip_media_download=skip_media_download,
                            scraping_task_id=task.id,
                        )

                        if isinstance(result, AsyncResult):
                            # Асинхронный режим
                            task.celery_task_id = result.id
                            task.save()
                            messages.success(
                                request,
                                f"Profile import started asynchronously. Task ID: {result.id}",
                            )
                        else:
                            # Синхронный режим
                            # Обновляем задачу из БД для получения актуальных данных
                            task.refresh_from_db()
                            
                            if result.get("status") == "success":
                                posts_count = result.get("total_posts", 0)
                                messages.success(
                                    request,
                                    f"Successfully imported profile @{target_identifier} with {posts_count} posts",
                                )
                            elif result.get("status") == "partial_success":
                                messages.warning(
                                    request,
                                    f"Profile import completed with warnings: {result.get('message', 'Check task details')}",
                                )
                            else:
                                error_msg = "Unknown error"
                                if isinstance(result, dict):
                                    errors = result.get("errors", [])
                                    if errors and isinstance(errors, list):
                                        error_msg = errors[0]
                                    elif result.get("error"):
                                        error_msg = result["error"]
                                messages.error(
                                    request,
                                    f"Failed to import profile: {error_msg}",
                                )
                    elif task_type == "posts":
                        # Используем batch import даже для одного профиля
                        # чтобы обеспечить создание подзадач и правильное обновление статусов
                        result = ImportService.import_batch_posts(
                            usernames=[target_identifier],  # Передаем как список
                            force_sync=force_sync,
                            limit=posts_limit if posts_limit > 0 else None,
                            post_types=post_types,
                            skip_media_download=skip_media_download,
                            scraping_task_id=task.id,
                        )
                        
                        if isinstance(result, AsyncResult):
                            # Асинхронный режим
                            task.celery_task_id = result.id
                            task.save()
                            messages.success(
                                request,
                                f"Posts import started asynchronously. Task ID: {result.id}",
                            )
                        else:
                            # Синхронный режим
                            # Обновляем задачу из БД для получения актуальных данных
                            task.refresh_from_db()
                            
                            if result.get("status") == "success":
                                posts_count = result.get("total_posts", 0)
                                messages.success(
                                    request,
                                    f"Successfully imported {posts_count} posts for @{target_identifier}",
                                )
                            elif result.get("status") == "partial_success":
                                messages.warning(
                                    request,
                                    f"Import completed with errors: {result.get('total_posts', 0)} posts imported. "
                                    f"Errors: {', '.join(result.get('errors', []))}",
                                )
                            else:
                                error_msg = "Unknown error"
                                if isinstance(result, dict):
                                    errors = result.get("errors", [])
                                    if errors and isinstance(errors, list):
                                        error_msg = errors[0]
                                    elif result.get("error"):
                                        error_msg = result["error"]
                                messages.error(
                                    request,
                                    f"Failed to import posts: {error_msg}",
                                )
                    elif task_type == "hashtag":
                        from instagram_manager.services import PostService
                        hashtag_service = PostService()
                        limit = 20 if posts_limit == 0 else posts_limit
                        imported_posts = hashtag_service.import_posts_by_hashtag(
                            target_identifier, task=task, limit=limit
                        )

                        if len(imported_posts) == 0:
                            messages.warning(
                                request,
                                f"No posts found for hashtag #{target_identifier}",
                            )
                        else:
                            messages.success(
                                request,
                                f"Successfully imported {len(imported_posts)} posts "
                                f"for #{target_identifier}",
                            )

                except Exception as e:
                    import traceback
                    error_details = traceback.format_exc()
                    logger.error(f"Import error for task {task.id}: {error_details}")
                    
                    task.status = "failed"
                    task.error_message = str(e)
                    task.save()
                    
                    # Более детальное сообщение об ошибке
                    if "ConnectionError" in str(type(e)):
                        messages.error(
                            request, 
                            "Connection error: Could not connect to BrightData API. Please check your network and API settings."
                        )
                    elif "Unauthorized" in str(e) or "403" in str(e):
                        messages.error(
                            request,
                            "Authentication error: Invalid BrightData API credentials. Please check your API key."
                        )
                    elif "Timeout" in str(e):
                        messages.error(
                            request,
                            "Timeout error: The import request took too long. Please try again with fewer items."
                        )
                    else:
                        messages.error(request, f"Error starting import: {e!s}")

                return redirect("..")
        else:
            form = InstagramImportTaskForm()

        context = {
            **self.admin_site.each_context(request),
            "form": form,
            "title": "Start Instagram Import",
            "subtitle": "Import Instagram profiles, posts or hashtag content",
            "opts": self.model._meta,
            "has_view_permission": True,
        }

        return render(
            request,
            "admin/instagram_manager/instagramscrapingtask/import_form.html",
            context,
        )

    def batch_import_view(self, request):
        """View для batch импорта через InstagramScrapingTaskAdmin"""
        # Просто перенаправляем на общую форму batch импорта
        return redirect(reverse("instagram_manager:admin_batch_import_posts_form"))

    def items_scraped_display(self, obj):
        """Отображение количества импортированных элементов"""
        if obj.status == "completed":
            if obj.items_scraped == 0:
                return format_html(
                    '<span style="color: #f0ad4e;">0 (no data found)</span>'
                )
            else:
                return format_html(
                    '<span style="color: #5cb85c;">{}</span>', obj.items_scraped
                )
        elif obj.status == "failed":
            return format_html('<span style="color: #d9534f;">Failed</span>')
        else:
            return "-"

    items_scraped_display.short_description = "Items Imported"
    items_scraped_display.admin_order_field = "items_scraped"
    
    def is_periodic_display(self, obj):
        """Отображение периодичности задачи"""
        if obj.is_periodic:
            # Форматируем интервал в человекочитаемый вид
            seconds = obj.interval_seconds
            if seconds < 60:
                interval_str = f"{seconds}s"
            elif seconds < 3600:
                minutes = seconds // 60
                secs = seconds % 60
                interval_str = f"{minutes}m {secs}s" if secs else f"{minutes}m"
            else:
                hours = seconds // 3600
                minutes = (seconds % 3600) // 60
                interval_str = f"{hours}h {minutes}m" if minutes else f"{hours}h"
            
            return format_html(
                '<span style="color: #5bc0de;">Every {}</span>',
                interval_str
            )
        return "-"
    
    is_periodic_display.short_description = "Schedule"
    
    def next_run_display(self, obj):
        """Отображение времени следующего запуска"""
        if obj.is_periodic and obj.next_periodic_run:
            return obj.next_periodic_run.strftime("%Y-%m-%d %H:%M")
        return "-"
    
    next_run_display.short_description = "Next Run"
    
    def periodic_stats_display(self, obj):
        """Отображение статистики периодических запусков"""
        if obj.is_periodic:
            return format_html(
                '<span style="color: #5cb85c;">✓{}</span> '
                '<span style="color: #d9534f;">✗{}</span>',
                obj.periodic_success_count,
                obj.periodic_fail_count
            )
        return "-"
    
    periodic_stats_display.short_description = "Success/Fail"
    
    def parent_task_display(self, obj):
        """Отображение родительской задачи"""
        if obj.parent_task:
            return format_html(
                '<a href="{}">{} - {}</a>',
                reverse(
                    "admin:instagram_manager_instagramscrapingtask_change",
                    args=[obj.parent_task.pk]
                ),
                obj.parent_task.task_type,
                obj.parent_task.target_identifier
            )
        return "-"
    
    parent_task_display.short_description = "Parent Task"
    
    def subtasks_progress_display(self, obj):
        """Отображение прогресса подзадач"""
        if obj.subtasks_count == 0:
            return "-"
        
        # Показываем прогресс бар
        percentage = obj.subtasks_progress_percentage
        
        # Цвет в зависимости от прогресса
        if percentage == 100:
            bar_color = "#5cb85c"  # Зеленый
        elif obj.subtasks_failed > 0:
            bar_color = "#d9534f"  # Красный
        else:
            bar_color = "#f0ad4e"  # Оранжевый
        
        return format_html(
            '<div style="width: 100px; background-color: #f5f5f5; border: 1px solid #ddd; height: 20px; position: relative;">' 
            '<div style="background-color: {}; width: {}%; height: 100%;"></div>'
            '<span style="position: absolute; left: 50%; top: 50%; transform: translate(-50%, -50%); font-size: 11px;">{}/{}</span>'
            '</div>',
            bar_color,
            percentage,
            obj.subtasks_completed,
            obj.subtasks_count
        )
    
    subtasks_progress_display.short_description = "Subtasks Progress"
    
    def subtasks_list(self, obj):
        """Детальный список подзадач для страницы редактирования"""
        # Показываем новые подзадачи
        subtasks = obj.subtasks_new.all().order_by("-created_at")
        if not subtasks:
            return "No subtasks"
        
        html_parts = ['<ul style="margin: 0; padding-left: 20px;">']
        for subtask in subtasks[:10]:  # Показываем только первые 10
            status_colors = {
                "completed": "#5cb85c",
                "failed": "#d9534f",
                "running": "#f0ad4e",
                "pending": "#777"
            }
            color = status_colors.get(subtask.status, "#333")
            
            # Добавляем информацию об источниках из result_data если есть
            sources_info = ""
            if subtask.result_data and isinstance(subtask.result_data, dict):
                sources_info = subtask.result_data.get("sources_info", "")
                if sources_info:
                    sources_info = f" from {sources_info}"
            
            # Отображаем количество обработанных элементов
            items_display = f"{subtask.processed_items} items"
            if subtask.total_items and subtask.total_items > 0:
                # Если total_items установлен, показываем прогресс
                items_display = f"{subtask.processed_items}/{subtask.total_items} items"
            
            html_parts.append(
                f'<li style="color: {color};">{subtask.get_subtask_type_display()}: {subtask.target_identifier} '
                f'({subtask.status}) - {items_display}{sources_info}</li>'
            )
        
        if subtasks.count() > 10:
            html_parts.append(f"<li><small>... and {subtasks.count() - 10} more</small></li>")
        
        html_parts.append("</ul>")
        return format_html("".join(html_parts))
    
    subtasks_list.short_description = "Subtasks Details"

    actions = ["run_periodic_now", "enable_periodic", "disable_periodic", "delete_periodic"]
    
    def run_periodic_now(self, request, queryset):
        """Запустить выбранные периодические задачи сейчас"""
        from instagram_manager.tasks import execute_periodic_import
        
        count = 0
        for task in queryset.filter(is_periodic=True):
            execute_periodic_import.delay(task.id)
            count += 1
        
        if count:
            self.message_user(
                request,
                f"Started {count} periodic task(s)",
                messages.SUCCESS
            )
        else:
            self.message_user(
                request,
                "No periodic tasks selected",
                messages.WARNING
            )
    
    run_periodic_now.short_description = "Run periodic tasks now"
    
    def enable_periodic(self, request, queryset):
        """Включить периодические задачи"""
        from instagram_manager.utils.celery_beat import update_periodic_task
        
        count = 0
        for task in queryset.filter(is_periodic=True):
            task.status = "pending"
            task.save()
            update_periodic_task(task)
            count += 1
        
        if count:
            self.message_user(
                request,
                f"Enabled {count} periodic task(s)",
                messages.SUCCESS
            )
    
    enable_periodic.short_description = "Enable periodic tasks"
    
    def disable_periodic(self, request, queryset):
        """Отключить периодические задачи"""
        from instagram_manager.utils.celery_beat import disable_periodic_task
        
        count = 0
        for task in queryset.filter(is_periodic=True):
            if task.celery_beat_name:
                disable_periodic_task(task.celery_beat_name)
                count += 1
        
        if count:
            self.message_user(
                request,
                f"Disabled {count} periodic task(s)",
                messages.SUCCESS
            )
    
    disable_periodic.short_description = "Disable periodic tasks"
    
    def delete_periodic(self, request, queryset):
        """Удалить периодические задачи из планировщика"""
        from instagram_manager.utils.celery_beat import delete_periodic_task
        
        count = 0
        for task in queryset.filter(is_periodic=True):
            if task.celery_beat_name:
                delete_periodic_task(task.celery_beat_name)
                count += 1
        
        if count:
            self.message_user(
                request,
                f"Deleted {count} periodic task(s) from scheduler",
                messages.SUCCESS
            )
    
    delete_periodic.short_description = "Delete from scheduler"

    def has_add_permission(self, request):
        """Запрещаем создание задач через стандартную кнопку"""
        return False
