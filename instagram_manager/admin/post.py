"""
Админ классы для работы с постами Instagram.
"""

import logging

from django.contrib import admin, messages
from django.shortcuts import redirect, render
from django.urls import path, reverse
from django.utils.html import format_html

from instagram_manager.models import InstagramPost

from .base import BaseInstagramAdmin
from .comment import InstagramCommentInline
from .media import InstagramMediaInline

logger = logging.getLogger(__name__)


@admin.register(InstagramPost)
class InstagramPostAdmin(BaseInstagramAdmin):
    """Админ класс для постов Instagram"""
    
    list_display = (
        "shortcode",
        "profile",
        "post_type",
        "like_count",
        "comment_count",
        "view_count",
        "posted_at",
        "media_count",
        "media_status",
    )
    list_filter = (
        "profile",
        "post_type",
        "is_sponsored",
        "is_comments_disabled",
        "posted_at"
    )
    search_fields = [
        "shortcode",
        "caption",
        "profile__username"
    ]
    
    # Оптимизация запросов
    list_select_related = ["profile"]  # Для profile в list_display
    list_prefetch_related = ["media", "hashtags"]  # Для media_count и media_status
    
    readonly_fields = (
        "post_id",
        "post_url",
        "created_at",
        "updated_at",
        "post_preview",
        "engagement_metrics",
        "hashtags_display",
    )
    
    inlines = [InstagramMediaInline, InstagramCommentInline]
    
    fieldsets = (
        (
            "Post Information",
            {
                "fields": (
                    "post_id",
                    "profile",
                    "shortcode",
                    "post_type",
                    "post_url",
                    "post_preview",
                )
            },
        ),
        (
            "Content",
            {"fields": ("caption", "hashtags_display", "location", "location_id")},
        ),
        (
            "Metrics",
            {
                "fields": (
                    "engagement_metrics",
                    "like_count",
                    "comment_count",
                    "view_count",
                    "video_play_count",
                )
            },
        ),
        (
            "Settings",
            {
                "fields": (
                    "is_sponsored",
                    "is_comments_disabled",
                    "accessibility_caption",
                )
            },
        ),
        ("Timestamps", {"fields": ("posted_at", "created_at", "updated_at")}),
    )
    
    actions = [
        "download_post_media",
        "import_comments_action",
        "post_comment_action",
        "export_to_majila"
    ]
    
    def post_preview(self, obj):
        """Превью поста"""
        media = obj.media.first()
        if media:
            return self.media_preview(media, width=300, height=300)
        return "No media"
    
    post_preview.short_description = "Preview"
    
    def media_count(self, obj):
        """Количество медиа файлов"""
        return obj.media.count()
    
    media_count.short_description = "Media Files"
    
    def media_status(self, obj):
        """Статус загрузки медиа"""
        total = obj.media.count()
        if total == 0:
            return "-"
        
        downloaded = obj.media.filter(is_downloaded=True).count()
        
        if downloaded == total:
            return format_html('<span style="color: green;">✓ All downloaded</span>')
        elif downloaded > 0:
            return format_html(
                '<span style="color: orange;">{}/{} downloaded</span>',
                downloaded,
                total,
            )
        else:
            return format_html('<span style="color: red;">Not downloaded</span>')
    
    media_status.short_description = "Download Status"
    
    def engagement_metrics(self, obj):
        """Метрики вовлеченности"""
        engagement_rate = 0
        # profile уже загружен через select_related
        if obj.profile and obj.profile.follower_count > 0:
            engagement = obj.like_count + obj.comment_count
            engagement_rate = (engagement / obj.profile.follower_count) * 100
        
        return format_html(
            '<div style="font-size: 14px;">'
            "<strong>Likes:</strong> {:,}<br>"
            "<strong>Comments:</strong> {:,}<br>"
            "<strong>Views:</strong> {:,}<br>"
            "<strong>Engagement Rate:</strong> {:.2f}%"
            "</div>",
            obj.like_count,
            obj.comment_count,
            obj.view_count or 0,
            engagement_rate,
        )
    
    engagement_metrics.short_description = "Engagement"
    
    def hashtags_display(self, obj):
        """Отображение хештегов"""
        # hashtags уже загружены через prefetch_related
        hashtags = obj.hashtags.all()
        if hashtags:
            return ", ".join([f"#{tag.name}" for tag in hashtags])
        return "No hashtags"
    
    hashtags_display.short_description = "Hashtags"
    
    @admin.action(description="Download media for selected posts")
    def download_post_media(self, request, queryset):
        """Action для загрузки медиафайлов выбранных постов"""
        from instagram_manager.instagram_api.data_handlers.media_handler import MediaHandler
        
        total_downloaded = 0
        total_errors = 0
        
        # Create MediaHandler instance (without GCS for now)
        handler = MediaHandler(save_to_gcs=False)
        
        for post in queryset:
            for media in post.media.filter(is_downloaded=False):
                try:
                    if handler.download_media(media):
                        total_downloaded += 1
                        # Для видео также загружаем миниатюру
                        if media.media_type == "video" and media.thumbnail_url:
                            handler.download_thumbnail(media)
                except Exception as e:
                    total_errors += 1
                    logger.exception(f"Error downloading media {media.external_id}: {e!s}")
        
        if total_downloaded > 0:
            messages.success(
                request,
                f"Successfully downloaded {total_downloaded} media files"
            )
        if total_errors > 0:
            messages.warning(
                request,
                f"Failed to download {total_errors} media files"
            )
    
    @admin.action(description="Import comments for selected posts")
    def import_comments_action(self, request, queryset):
        """Action для импорта комментариев выбранных постов"""
        from instagram_manager.services import CommentService
        
        service = CommentService()
        total_imported = 0
        total_errors = 0
        
        for post in queryset:
            try:
                comments = service.import_comments_for_post(
                    post=post,
                    limit=100,
                    include_replies=True
                )
                total_imported += len(comments)
                
            except Exception as e:
                total_errors += 1
                logger.exception(
                    f"Error importing comments for post {post.shortcode}: {e!s}"
                )
                messages.error(request, f"Error for post {post.shortcode}: {e!s}")
        
        if total_imported > 0:
            messages.success(
                request,
                f"Successfully imported {total_imported} comments"
            )
        if total_errors > 0:
            messages.warning(
                request,
                f"Failed to import comments for {total_errors} posts"
            )
    
    @admin.action(description="Export to Majila Community")
    def export_to_majila(self, request, queryset):
        """Action для экспорта постов в Majila"""
        # Сохраняем выбранные посты в сессии
        request.session["selected_posts_for_majila_export"] = list(
            queryset.values_list("id", flat=True)
        )
        # Перенаправляем на форму экспорта
        return redirect("instagram_manager:admin_export_posts_to_majila_form")
    
    def get_urls(self):
        """Add custom URL for posting comments"""
        urls = super().get_urls()
        custom_urls = [
            path(
                "<path:object_id>/post_comment/",
                self.admin_site.admin_view(self.post_comment_view),
                name="instagram_manager_instagrampost_post_comment",
            ),
            path(
                "bulk_comment/",
                self.admin_site.admin_view(self.bulk_comment_view),
                name="instagram_manager_instagrampost_bulk_comment",
            ),
        ]
        return custom_urls + urls
    
    def post_comment_view(self, request, object_id):
        """View for posting a comment to Instagram"""
        post = self.get_object(request, object_id)
        
        if not post:
            messages.error(request, "Post not found")
            return redirect(reverse("admin:instagram_manager_instagrampost_changelist"))
        
        from instagram_manager.forms import CommentPostForm
        
        if request.method == "POST":
            form = CommentPostForm(request.POST)
            if form.is_valid():
                try:
                    from instagram_manager.services.playwright_comment_service import PlaywrightCommentService
                    
                    account = form.cleaned_data["account"]
                    comment_text = form.cleaned_data["comment_text"]
                    debug_mode = form.cleaned_data.get("debug_mode", False)
                    force_login = form.cleaned_data.get("force_login", False)
                    
                    # Use the Playwright service with sync wrapper
                    service = PlaywrightCommentService()
                    success, error, posted_comment_id = service.post_comment_sync(
                        account_id=account.id,
                        post_url=post.post_url,
                        comment_text=comment_text,
                        headless=not debug_mode,  # If debug mode is on, run visible
                        debug=debug_mode,
                        force_login=force_login
                    )
                    
                    if success:
                        messages.success(
                            request,
                            f"Successfully posted comment from @{account.username}"
                        )
                    else:
                        messages.error(
                            request,
                            f"Failed to post comment: {error}"
                        )
                    
                except Exception as e:
                    messages.error(request, f"Error posting comment: {e!s}")
                
                return redirect(
                    reverse("admin:instagram_manager_instagrampost_change", args=[post.id])
                )
        else:
            # Pre-fill the form with post URL
            initial_data = {
                "post_url": post.post_url
            }
            form = CommentPostForm(initial=initial_data)
        
        context = {
            **self.admin_site.each_context(request),
            "title": f"Post Comment to {post.shortcode}",
            "form": form,
            "post": post,
            "opts": self.model._meta,
            "has_view_permission": True,
            "has_add_permission": False,
            "has_change_permission": True,
            "has_delete_permission": False,
        }
        
        return render(
            request,
            "admin/instagram_manager/instagrampost/post_comment.html",
            context
        )
    
    def change_view(self, request, object_id, form_url="", extra_context=None):
        """Add post comment button to the change view"""
        extra_context = extra_context or {}
        extra_context["show_post_comment"] = True
        return super().change_view(request, object_id, form_url, extra_context=extra_context)
    
    @admin.action(description="Post comment to selected posts")
    def post_comment_action(self, request, queryset):
        """Action to post comments to multiple posts"""
        # Store selected posts in session
        selected_ids = list(queryset.values_list("id", flat=True))
        request.session["selected_posts_for_comment"] = selected_ids
        
        # Redirect to bulk comment form
        return redirect(reverse("admin:instagram_manager_instagrampost_bulk_comment"))
    
    def bulk_comment_view(self, request):
        """View for posting comments to multiple posts"""
        # Get selected posts from session
        selected_ids = request.session.get("selected_posts_for_comment", [])
        if not selected_ids:
            messages.error(request, "No posts selected")
            return redirect(reverse("admin:instagram_manager_instagrampost_changelist"))
        
        posts = InstagramPost.objects.filter(id__in=selected_ids)
        
        if request.method == "POST":
            from instagram_manager.forms import BulkCommentForm
            form = BulkCommentForm(request.POST)
            
            if form.is_valid():
                # Clear the session
                request.session.pop("selected_posts_for_comment", None)
                
                # Process bulk comments
                account = form.cleaned_data["account"]
                # TODO: Implement bulk comment posting with these parameters:
                # comment_template = form.cleaned_data['comment_template']
                # randomize = form.cleaned_data['randomize']
                # delay = form.cleaned_data['delay_between']
                
                # For now, just show a message
                messages.success(
                    request,
                    f"Bulk comment posting started for {posts.count()} posts "
                    f"using account @{account.username}"
                )
                
                # TODO: Implement actual bulk comment posting logic
                # This could be done via a background task or management command
                
                return redirect(reverse("admin:instagram_manager_instagrampost_changelist"))
        else:
            # Pre-fill the form with post URLs
            post_urls = "\n".join([post.post_url for post in posts if post.post_url])
            from instagram_manager.forms import BulkCommentForm
            form = BulkCommentForm(initial={"posts_list": post_urls})
        
        context = {
            **self.admin_site.each_context(request),
            "title": f"Post Comments to {posts.count()} Selected Posts",
            "form": form,
            "posts": posts,
            "opts": self.model._meta,
            "has_view_permission": True,
            "has_add_permission": False,
            "has_change_permission": True,
            "has_delete_permission": False,
        }
        
        # Create template if it doesn't exist
        return render(
            request,
            "admin/instagram_manager/instagrampost/bulk_comment.html",
            context
        )