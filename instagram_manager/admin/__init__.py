"""
Instagram Manager Admin Module.

Этот модуль обеспечивает обратную совместимость при импорте админ классов.
Все админ классы теперь организованы в отдельные файлы для лучшей структуры.
"""

# Импортируем все админ классы для обратной совместимости
# Регистрируем модели, которые используют базовый админ
from django.contrib import admin

from instagram_manager.models import InstagramFollower, InstagramMedia

from .account import InstagramAccountAdmin
from .base import BaseInstagramAdmin
from .comment import InstagramCommentAdmin, InstagramCommentInline
from .hashtag import InstagramHashtagAdmin
from .majila import (
    MajilaExportTaskAdmin,
    MajilaServiceAccountAdmin,
    MajilaServiceAccountInline,
)
from .media import InstagramMediaInline
from .post import InstagramPostAdmin
from .posted_comment import PostedCommentAdmin
from .profile import InstagramProfileAdmin, InstagramProfileAdminWithMajila
from .task import InstagramScrapingTaskAdmin

# Регистрируем модели без специального админ класса
# (они были зарегистрированы в оригинальном admin.py)
try:
    admin.site.unregister(InstagramMedia)
except admin.sites.NotRegistered:
    pass
admin.site.register(InstagramMedia, BaseInstagramAdmin)

try:
    admin.site.unregister(InstagramFollower)
except admin.sites.NotRegistered:
    pass
admin.site.register(InstagramFollower, BaseInstagramAdmin)

# Экспортируем все классы для обратной совместимости
__all__ = [
    # Основные админ классы
    "InstagramProfileAdmin",
    "InstagramProfileAdminWithMajila",
    "InstagramPostAdmin",
    "InstagramCommentAdmin",
    "InstagramHashtagAdmin",
    "InstagramAccountAdmin",
    "InstagramScrapingTaskAdmin",
    "PostedCommentAdmin",
    
    # Majila админы
    "MajilaServiceAccountAdmin",
    "MajilaExportTaskAdmin",
    
    # Inline классы
    "InstagramMediaInline",
    "InstagramCommentInline",
    "MajilaServiceAccountInline",
    
    # Базовые классы (для расширения)
    "BaseInstagramAdmin",
]