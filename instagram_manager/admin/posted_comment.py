"""
Админ классы для работы с опубликованными комментариями Instagram.
"""

from django.contrib import admin, messages

from instagram_manager.models import PostedComment

from .base import BaseInstagramAdmin


@admin.register(PostedComment)
class PostedCommentAdmin(BaseInstagramAdmin):
    """Админ класс для отслеживания опубликованных комментариев"""
    
    list_display = [
        "account_username",
        "comment_preview",
        "post_link",
        "status",
        "posted_at",
        "attempts",
    ]
    list_filter = [
        "status",
        "created_at",
        "account"
    ]
    search_fields = [
        "text",
        "account__username",
        "post__shortcode"
    ]
    readonly_fields = [
        "post",
        "account",
        "posted_at",
        "error_message",
        "attempts",
        "instagram_comment_id",
        "created_at",
        "updated_at",
    ]
    
    fieldsets = (
        ("Comment Information", {"fields": ("post", "account", "text", "post_url")}),
        ("Status", {"fields": ("status", "attempts", "posted_at")}),
        ("Instagram Data", {"fields": ("instagram_comment_id",)}),
        ("Error Details", {"classes": ("collapse",), "fields": ("error_message",)}),
        ("Timestamps", {"fields": ("created_at", "updated_at")}),
    )
    
    actions = ["retry_failed_comments"]
    
    def account_username(self, obj):
        """Имя аккаунта с префиксом @"""
        return f"@{obj.account.username}"
    
    account_username.short_description = "Account"
    account_username.admin_order_field = "account__username"
    
    def comment_preview(self, obj):
        """Превью текста комментария"""
        text = obj.text
        return text[:50] + "..." if len(text) > 50 else text
    
    comment_preview.short_description = "Comment"
    
    def post_link(self, obj):
        """Ссылка на пост"""
        if not obj.post:
            return "-"
        
        return self.link_to_object(obj.post, text=obj.post.shortcode)
    
    post_link.short_description = "Post"
    post_link.admin_order_field = "post__shortcode"
    
    def has_add_permission(self, request):
        """Disable direct creation - comments should be posted through the service"""
        return False
    
    @admin.action(description="Retry failed comments")
    def retry_failed_comments(self, request, queryset):
        """Повторить попытку для неудачных комментариев"""
        failed_comments = queryset.filter(status="failed")
        count = failed_comments.count()
        
        if count == 0:
            messages.warning(request, "No failed comments selected")
            return
        
        # Reset status to pending for retry
        failed_comments.update(
            status="pending",
            error_message="",
            attempts=0
        )
        messages.success(
            request,
            f"Marked {count} comments for retry. "
            f"Run the posting command to retry them.",
        )