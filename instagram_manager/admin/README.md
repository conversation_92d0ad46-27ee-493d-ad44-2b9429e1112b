# Instagram Manager Admin Structure

Этот модуль содержит все админ классы для Instagram Manager, организованные в модульную структуру для лучшей поддерживаемости.

## Структура файлов

### Базовые компоненты
- **base.py** - Базовые классы и миксины для всех админов:
  - `ReadOnlyAdminMixin` - Для read-only админок
  - `BulkActionsMixin` - Массовые операции
  - `ExportMixin` - Экспорт данных (CSV, JSON)
  - `FilteredAdminMixin` - Оптимизация запросов
  - `DateDisplayMixin` - Форматирование дат
  - `MediaPreviewMixin` - Превью медиа файлов
  - `CounterMixin` - Форматирование счетчиков
  - `LinksMixin` - Создание ссылок на объекты
  - `BaseInstagramAdmin` - Базовый класс со всеми миксинами

### Админ классы по моделям
- **profile.py** - `InstagramProfileAdmin`, `InstagramProfileAdminWithMajila`
- **post.py** - `InstagramPostAdmin`
- **comment.py** - `InstagramCommentAdmin`, `InstagramCommentInline`
- **media.py** - `InstagramMediaInline`
- **hashtag.py** - `InstagramHashtagAdmin`
- **account.py** - `InstagramAccountAdmin`
- **posted_comment.py** - `PostedCommentAdmin`
- **task.py** - `InstagramScrapingTaskAdmin`
- **majila.py** - `MajilaServiceAccountAdmin`, `MajilaExportTaskAdmin`, `MajilaServiceAccountInline`
- **follower.py** - Базовая регистрация для `InstagramFollower`

### Главный файл
- **__init__.py** - Импортирует все классы для обратной совместимости

## Использование

### Импорт админ классов
```python
# Старый способ (все еще работает)
from instagram_manager.admin import InstagramProfileAdmin

# Новый способ (рекомендуется)
from instagram_manager.admin.profile import InstagramProfileAdmin
```

### Создание нового админ класса
```python
from instagram_manager.admin.base import BaseInstagramAdmin

@admin.register(YourModel)
class YourModelAdmin(BaseInstagramAdmin):
    list_display = ['field1', 'field2']
    # BaseInstagramAdmin уже включает все полезные миксины
```

### Использование отдельных миксинов
```python
from instagram_manager.admin.base import ExportMixin, BulkActionsMixin

class CustomAdmin(ExportMixin, BulkActionsMixin, admin.ModelAdmin):
    # Получаете функциональность экспорта и массовых операций
    actions = ['export_as_csv', 'export_as_json', 'bulk_delete_selected']
```

## Преимущества новой структуры

1. **Модульность** - Каждый админ класс в отдельном файле
2. **Переиспользование** - Общие миксины в base.py
3. **Поддерживаемость** - Легче находить и изменять код
4. **Расширяемость** - Легко добавлять новые админ классы
5. **Обратная совместимость** - Старые импорты продолжают работать

## Миграция со старой структуры

Не требуется никаких изменений в существующем коде. Файл `__init__.py` обеспечивает полную обратную совместимость.

Старый файл `admin_old.py` можно удалить после проверки, что все работает корректно.