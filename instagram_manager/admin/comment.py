"""
Админ классы для работы с комментариями Instagram.
"""

from django.contrib import admin, messages
from django.urls import reverse
from django.utils.html import format_html

from instagram_manager.models import InstagramComment

from .base import BaseInstagramAdmin


class InstagramCommentInline(admin.TabularInline):
    """Inline админ для комментариев в постах"""
    
    model = InstagramComment
    extra = 0  # Don't show empty form for new comments
    readonly_fields = (
        "comment_id",
        "author_username",
        "author_is_verified",
        "like_count",
        "commented_at",
        "is_pinned",
        "is_hidden",
    )
    fields = (
        "author_username",
        "text",
        "like_count",
        "reply_to",
        "is_pinned",
        "is_hidden",
        "commented_at",
    )
    show_change_link = True
    ordering = ["-commented_at"]
    
    def get_queryset(self, request):
        queryset = super().get_queryset(request)
        # Показываем только комментарии верхнего уровня в inline
        return queryset.filter(reply_to__isnull=True)
    
    def has_add_permission(self, request, obj=None):
        """Запрещаем добавление комментариев через админку"""
        return False


@admin.register(InstagramComment)
class InstagramCommentAdmin(BaseInstagramAdmin):
    """Админ класс для комментариев Instagram"""
    
    list_display = (
        "short_text",
        "author_username",
        "post_link",
        "like_count",
        "is_pinned",
        "is_hidden",
        "has_replies",
        "commented_at",
    )
    list_filter = (
        "is_pinned",
        "is_hidden",
        "author_is_verified",
        "commented_at"
    )
    search_fields = [
        "text",
        "author_username",
        "post__shortcode"
    ]
    
    # Оптимизация запросов
    list_select_related = ["post", "post__profile", "reply_to"]
    
    readonly_fields = (
        "comment_id",
        "author_profile_pic_preview",
        "post_link",
        "parent_comment_link",
        "replies_count",
        "created_at",
        "updated_at",
    )
    
    date_hierarchy = "commented_at"
    ordering = ["-commented_at"]
    
    fieldsets = (
        (
            "Comment Information",
            {"fields": ("comment_id", "post_link", "parent_comment_link")},
        ),
        (
            "Author",
            {
                "fields": (
                    "author_username",
                    "author_id",
                    "author_profile_pic_preview",
                    "author_is_verified",
                )
            },
        ),
        ("Content", {"fields": ("text", "like_count")}),
        ("Status", {"fields": ("is_pinned", "is_hidden", "replies_count")}),
        ("Timestamps", {"fields": ("commented_at", "created_at", "updated_at")}),
    )
    
    actions = ["hide_comments", "unhide_comments", "import_replies"]
    
    def short_text(self, obj):
        """Короткий текст комментария"""
        return obj.text[:50] + "..." if len(obj.text) > 50 else obj.text
    
    short_text.short_description = "Comment"
    
    def post_link(self, obj):
        """Ссылка на пост"""
        if not obj.post:
            return "-"
        
        url = reverse(
            "admin:instagram_manager_instagrampost_change",
            args=[obj.post.id]
        )
        return format_html(
            '<a href="{}">@{} - {}</a>',
            url,
            obj.post.profile.username if obj.post.profile else "Unknown",
            obj.post.shortcode,
        )
    
    post_link.short_description = "Post"
    
    def parent_comment_link(self, obj):
        """Ссылка на родительский комментарий"""
        if obj.reply_to:
            url = reverse(
                "admin:instagram_manager_instagramcomment_change",
                args=[obj.reply_to.id],
            )
            return format_html(
                '<a href="{}">Reply to @{}</a>',
                url,
                obj.reply_to.author_username
            )
        return "-"
    
    parent_comment_link.short_description = "Parent Comment"
    
    def has_replies(self, obj):
        """Количество ответов с визуальной индикацией"""
        count = obj.replies.count()
        return format_html(
            '<span style="color: {};">{}</span>',
            "green" if count > 0 else "gray",
            f"{count} replies" if count > 0 else "No replies",
        )
    
    has_replies.short_description = "Replies"
    
    def replies_count(self, obj):
        """Общее количество ответов"""
        return obj.get_replies_count()
    
    replies_count.short_description = "Total Replies"
    
    def author_profile_pic_preview(self, obj):
        """Превью фото автора"""
        if obj.author_profile_pic:
            return format_html(
                '<img src="{}" style="max-width: 50px; max-height: 50px; '
                'border-radius: 50%;" onerror="this.style.display=\'none\';" />',
                obj.author_profile_pic,
            )
        return "No photo"
    
    author_profile_pic_preview.short_description = "Author Photo"
    
    def hide_comments(self, request, queryset):
        """Скрыть выбранные комментарии"""
        updated = queryset.update(is_hidden=True)
        messages.success(request, f"{updated} comments hidden")
    
    hide_comments.short_description = "Hide selected comments"
    
    def unhide_comments(self, request, queryset):
        """Показать выбранные комментарии"""
        updated = queryset.update(is_hidden=False)
        messages.success(request, f"{updated} comments unhidden")
    
    unhide_comments.short_description = "Unhide selected comments"
    
    def import_replies(self, request, queryset):
        """Импортировать ответы на комментарии"""
        total_imported = 0
        
        for comment in queryset:
            if comment.replies.exists():
                continue
            
            try:
                # TODO: Реализовать импорт ответов через сервис
                messages.info(
                    request,
                    f"Import replies feature coming soon for @{comment.author_username}",
                )
            except Exception as e:
                messages.error(request, f"Error importing replies: {e}")
        
        if total_imported > 0:
            messages.success(request, f"Imported {total_imported} replies")
    
    import_replies.short_description = "Import replies for selected comments"