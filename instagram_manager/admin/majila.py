"""
Админ классы для работы с Majila экспортом.
"""

import logging

from django.contrib import admin, messages
from django.urls import reverse
from django.utils.html import format_html

from instagram_manager.models import MajilaExportTask, MajilaServiceAccount

from .base import BaseInstagramAdmin

logger = logging.getLogger(__name__)


class MajilaServiceAccountInline(admin.TabularInline):
    """Inline для отображения Majila аккаунтов в профиле Instagram"""
    
    model = MajilaServiceAccount
    extra = 0
    fields = ("username", "is_active", "last_auth_at", "auth_status")
    readonly_fields = ("last_auth_at", "auth_status")
    
    def auth_status(self, obj):
        """Статус аутентификации"""
        if obj.access_token and obj.user_uuid:
            return format_html('<span style="color: green;">✓ Authenticated</span>')
        elif obj.auth_error:
            return format_html(
                '<span style="color: red;">✗ Error: {}</span>',
                obj.auth_error[:50]
            )
        else:
            return format_html('<span style="color: orange;">⚠ Not authenticated</span>')
    
    auth_status.short_description = "Auth Status"


@admin.register(MajilaServiceAccount)
class MajilaServiceAccountAdmin(BaseInstagramAdmin):
    """Админ класс для аккаунтов Majila"""
    
    list_display = [
        "username",
        "instagram_profile_link",
        "is_active",
        "auth_status",
        "last_auth_at",
        "export_tasks_count",
        "created_at"
    ]
    list_filter = [
        "is_active",
        "created_at",
        "last_auth_at"
    ]
    search_fields = [
        "username",
        "instagram_profile__username"
    ]
    readonly_fields = [
        "user_uuid",
        "access_token",
        "last_auth_at",
        "auth_error",
        "created_at",
        "updated_at"
    ]
    
    fieldsets = (
        ("Account Information", {
            "fields": ("instagram_profile", "username", "password", "is_active")
        }),
        ("Authentication", {
            "fields": ("user_uuid", "access_token", "last_auth_at", "auth_error")
        }),
        ("Timestamps", {
            "fields": ("created_at", "updated_at")
        })
    )
    
    actions = ["test_authentication"]
    
    def instagram_profile_link(self, obj):
        """Ссылка на профиль Instagram"""
        if not obj.instagram_profile:
            return "-"
        
        return self.link_to_object(
            obj.instagram_profile,
            text=f"@{obj.instagram_profile.username}"
        )
    
    instagram_profile_link.short_description = "Instagram Profile"
    instagram_profile_link.admin_order_field = "instagram_profile__username"
    
    def auth_status(self, obj):
        """Статус аутентификации с подсказкой"""
        if obj.access_token and obj.user_uuid:
            return format_html('<span style="color: green;">✓ Authenticated</span>')
        elif obj.auth_error:
            return format_html(
                '<span style="color: red;" title="{}">✗ Error</span>',
                obj.auth_error
            )
        else:
            return format_html('<span style="color: orange;">⚠ Not authenticated</span>')
    
    auth_status.short_description = "Status"
    
    def export_tasks_count(self, obj):
        """Количество задач экспорта с ссылкой"""
        count = obj.export_tasks.count()
        if count > 0:
            url = reverse("admin:instagram_manager_majilaexporttask_changelist")
            url += f"?account__id__exact={obj.id}"
            return format_html('<a href="{}">{} tasks</a>', url, count)
        return "0 tasks"
    
    export_tasks_count.short_description = "Export Tasks"
    
    @admin.action(description="Test authentication for selected accounts")
    def test_authentication(self, request, queryset):
        """Test authentication for selected Majila accounts"""
        from instagram_manager.services.majila_export_service import MajilaExportService
        
        service = MajilaExportService()
        success_count = 0
        fail_count = 0
        
        for account in queryset:
            if service.authenticate(account):
                success_count += 1
            else:
                fail_count += 1
        
        if success_count > 0:
            messages.success(
                request,
                f"Successfully authenticated {success_count} accounts"
            )
        if fail_count > 0:
            messages.warning(
                request,
                f"Failed to authenticate {fail_count} accounts"
            )
    
    def get_form(self, request, obj=None, **kwargs):
        """Use custom form for password handling"""
        from instagram_manager.forms import MajilaAccountForm
        kwargs["form"] = MajilaAccountForm
        return super().get_form(request, obj, **kwargs)
    
    def save_model(self, request, obj, form, change):
        """Handle password encryption when saving"""
        if form.cleaned_data.get("password"):
            obj.password = form.cleaned_data["password"]
        super().save_model(request, obj, form, change)
    
    def change_view(self, request, object_id, form_url="", extra_context=None):
        """Add test auth button to change view"""
        extra_context = extra_context or {}
        extra_context["show_test_auth"] = True
        extra_context["test_auth_url"] = reverse(
            "instagram_manager:admin_majila_account_test_auth",
            args=[object_id]
        )
        return super().change_view(request, object_id, form_url, extra_context=extra_context)


@admin.register(MajilaExportTask)
class MajilaExportTaskAdmin(BaseInstagramAdmin):
    """Админ класс для задач экспорта в Majila"""
    
    list_display = [
        "id",
        "account_link",
        "status_badge",
        "posts_summary",
        "duration",
        "created_at",
        "completed_at"
    ]
    list_filter = [
        "status",
        "created_at",
        "completed_at"
    ]
    search_fields = [
        "account__username",
        "error_message"
    ]
    readonly_fields = [
        "account",
        "status",
        "total_posts",
        "exported_count",
        "failed_count",
        "export_results_display",
        "error_message",
        "created_at",
        "started_at",
        "completed_at",
        "duration"
    ]
    filter_horizontal = ["posts"]
    
    fieldsets = (
        ("Task Information", {
            "fields": ("account", "status", "posts")
        }),
        ("Results", {
            "fields": (
                "total_posts",
                "exported_count",
                "failed_count",
                "export_results_display",
                "error_message"
            )
        }),
        ("Timestamps", {
            "fields": ("created_at", "started_at", "completed_at", "duration")
        })
    )
    
    actions = ["retry_failed_tasks"]
    
    def account_link(self, obj):
        """Ссылка на аккаунт Majila"""
        return self.link_to_object(obj.account, text=obj.account.username)
    
    account_link.short_description = "Account"
    account_link.admin_order_field = "account__username"
    
    def status_badge(self, obj):
        """Цветной бейдж статуса"""
        colors = {
            "pending": "orange",
            "in_progress": "blue",
            "completed": "green",
            "failed": "red",
            "partial": "purple"
        }
        color = colors.get(obj.status, "gray")
        return format_html(
            '<span style="color: {}; font-weight: bold;">{}</span>',
            color,
            obj.get_status_display()
        )
    
    status_badge.short_description = "Status"
    status_badge.admin_order_field = "status"
    
    def posts_summary(self, obj):
        """Сводка по постам"""
        return format_html(
            '<div>'
            'Total: {}<br>'
            'Success: <span style="color: green;">{}</span><br>'
            'Failed: <span style="color: red;">{}</span>'
            '</div>',
            obj.total_posts,
            obj.exported_count,
            obj.failed_count
        )
    
    posts_summary.short_description = "Posts"
    
    def duration(self, obj):
        """Продолжительность выполнения"""
        duration = obj.get_duration()
        if duration:
            total_seconds = int(duration.total_seconds())
            hours, remainder = divmod(total_seconds, 3600)
            minutes, seconds = divmod(remainder, 60)
            
            if hours:
                return f"{hours}h {minutes}m {seconds}s"
            elif minutes:
                return f"{minutes}m {seconds}s"
            else:
                return f"{seconds}s"
        return "-"
    
    duration.short_description = "Duration"
    
    def export_results_display(self, obj):
        """Display export results in a formatted way"""
        if not obj.export_results:
            return "No results available"
        
        html = '<div style="max-height: 300px; overflow-y: auto;">'
        for result in obj.export_results:
            post_id = result.get("post_id")
            status = result.get("status")
            error = result.get("error_message", "")
            
            if status == "success":
                html += f'<div style="margin: 5px 0;">✓ Post #{post_id} - Success</div>'
            elif status == "failed":
                html += f'<div style="margin: 5px 0; color: red;">✗ Post #{post_id} - Failed: {error}</div>'
            elif status == "skipped":
                html += f'<div style="margin: 5px 0; color: orange;">⚠ Post #{post_id} - Skipped</div>'
        
        html += "</div>"
        return format_html(html)
    
    export_results_display.short_description = "Export Results"
    
    def has_add_permission(self, request):
        """Disable manual creation - tasks should be created through export service"""
        return False
    
    @admin.action(description="Retry failed export tasks")
    def retry_failed_tasks(self, request, queryset):
        """Retry failed export tasks"""
        failed_tasks = queryset.filter(status__in=["failed", "partial"])
        count = failed_tasks.count()
        
        if count == 0:
            messages.warning(request, "No failed tasks selected")
            return
        
        from instagram_manager.services.majila_export_service import MajilaExportService
        service = MajilaExportService()
        
        success_count = 0
        for task in failed_tasks:
            try:
                # Get posts that failed or weren't exported
                posts_to_retry = []
                if task.export_results:
                    failed_post_ids = [
                        r["post_id"] for r in task.export_results
                        if r["status"] in ["failed", "skipped"]
                    ]
                    posts_to_retry = task.posts.filter(id__in=failed_post_ids)
                else:
                    posts_to_retry = task.posts.all()
                
                if posts_to_retry:
                    summary = service.export_posts(
                        account=task.account,
                        posts=list(posts_to_retry),
                        skip_existing=False
                    )
                    if summary.successful > 0:
                        success_count += 1
                        
            except Exception as e:
                logger.exception(f"Error retrying task {task.id}: {e!s}")
        
        if success_count > 0:
            messages.success(
                request,
                f"Successfully retried {success_count} tasks"
            )