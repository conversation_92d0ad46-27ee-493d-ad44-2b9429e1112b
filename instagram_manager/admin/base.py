"""
Базовые классы и миксины для Instagram админки.
"""

import csv

from django.contrib import admin, messages
from django.http import HttpResponse
from django.utils import timezone
from django.utils.html import format_html


class ReadOnlyAdminMixin:
    """Миксин для read-only админки"""
    
    def has_add_permission(self, request):
        return False
    
    def has_change_permission(self, request, obj=None):
        return False
    
    def has_delete_permission(self, request, obj=None):
        return False


class BulkActionsMixin:
    """Миксин для массовых операций"""
    
    def bulk_delete_selected(self, request, queryset):
        """Массовое удаление с подтверждением"""
        count = queryset.count()
        if hasattr(queryset.model, "is_deleted"):
            queryset.update(is_deleted=True)
            self.message_user(request, f"Мягко удалено {count} объектов", messages.SUCCESS)
        else:
            deleted_count, _ = queryset.delete()
            self.message_user(request, f"Удалено {deleted_count} объектов", messages.SUCCESS)
    
    bulk_delete_selected.short_description = "Удалить выбранные"
    
    def bulk_restore_selected(self, request, queryset):
        """Массовое восстановление удаленных"""
        if hasattr(queryset.model, "is_deleted"):
            count = queryset.filter(is_deleted=True).count()
            queryset.update(is_deleted=False)
            self.message_user(request, f"Восстановлено {count} объектов", messages.SUCCESS)
        else:
            self.message_user(request, "Модель не поддерживает мягкое удаление", messages.ERROR)
    
    bulk_restore_selected.short_description = "Восстановить удаленные"


class ExportMixin:
    """Миксин для экспорта данных"""
    
    def export_as_csv(self, request, queryset):
        """Экспорт в CSV"""
        meta = self.model._meta
        field_names = [field.name for field in meta.fields]
        
        response = HttpResponse(content_type="text/csv")
        response["Content-Disposition"] = f'attachment; filename="{meta.model_name}_export.csv"'
        response.write("\ufeff")  # BOM для корректного отображения UTF-8 в Excel
        
        writer = csv.writer(response)
        writer.writerow(field_names)
        
        for obj in queryset:
            row = []
            for field in field_names:
                value = getattr(obj, field)
                if value is None:
                    value = ""
                elif isinstance(value, bool):
                    value = "Да" if value else "Нет"
                elif hasattr(value, "strftime"):
                    value = value.strftime("%Y-%m-%d %H:%M:%S")
                row.append(str(value))
            writer.writerow(row)
        
        return response
    
    export_as_csv.short_description = "Экспортировать в CSV"
    
    def export_as_json(self, request, queryset):
        """Экспорт в JSON"""
        import json

        from django.core.serializers.json import DjangoJSONEncoder
        
        data = []
        for obj in queryset:
            obj_data = {}
            for field in self.model._meta.fields:
                value = getattr(obj, field.name)
                if hasattr(value, "pk"):  # ForeignKey
                    obj_data[field.name] = value.pk
                else:
                    obj_data[field.name] = value
            data.append(obj_data)
        
        response = HttpResponse(
            json.dumps(data, cls=DjangoJSONEncoder, ensure_ascii=False, indent=2),
            content_type="application/json"
        )
        response["Content-Disposition"] = f'attachment; filename="{self.model._meta.model_name}_export.json"'
        
        return response
    
    export_as_json.short_description = "Экспортировать в JSON"


class FilteredAdminMixin:
    """Миксин для расширенной фильтрации и оптимизации запросов"""
    
    def get_queryset(self, request):
        """Оптимизированный queryset с автоматическим prefetch"""
        qs = super().get_queryset(request)
        
        # Оптимизация для list_display
        if hasattr(self, "list_select_related"):
            if self.list_select_related is True:
                # Если True, Django сам определит связи
                qs = qs.select_related()
            elif self.list_select_related:
                # Если это итерируемый объект (список/кортеж)
                qs = qs.select_related(*self.list_select_related)
        
        if hasattr(self, "list_prefetch_related"):
            if isinstance(self.list_prefetch_related, (list, tuple)):
                qs = qs.prefetch_related(*self.list_prefetch_related)
        
        # Автоматическая оптимизация для ForeignKey полей в list_display
        if hasattr(self, "list_display"):
            for field_name in self.list_display:
                if "__" in field_name:  # Обращение к связанному полю
                    related_field = field_name.split("__")[0]
                    # Проверяем, что select_related является итерируемым объектом (не bool)
                    if isinstance(qs.query.select_related, (list, tuple)) and \
                       not any(related_field in str(s) for s in qs.query.select_related):
                        qs = qs.select_related(related_field)
        
        return qs


class DateDisplayMixin:
    """Миксин для форматированного отображения дат"""
    
    def format_datetime(self, dt, show_relative=True):
        """Форматирует datetime с относительным временем"""
        if not dt:
            return "-"
        
        now = timezone.now()
        delta = now - dt
        
        if show_relative:
            if delta.days == 0:
                if delta.seconds < 3600:
                    minutes = delta.seconds // 60
                    return format_html(
                        '<span title="{}">{} мин. назад</span>',
                        dt.strftime("%Y-%m-%d %H:%M:%S"),
                        minutes
                    )
                else:
                    hours = delta.seconds // 3600
                    return format_html(
                        '<span title="{}">{} ч. назад</span>',
                        dt.strftime("%Y-%m-%d %H:%M:%S"),
                        hours
                    )
            elif delta.days == 1:
                return format_html(
                    '<span title="{}">Вчера</span>',
                    dt.strftime("%Y-%m-%d %H:%M:%S")
                )
            elif delta.days < 7:
                return format_html(
                    '<span title="{}">{} дн. назад</span>',
                    dt.strftime("%Y-%m-%d %H:%M:%S"),
                    delta.days
                )
        
        return dt.strftime("%Y-%m-%d %H:%M")
    
    def colored_date_display(self, dt, thresholds=None):
        """Отображает дату с цветовой индикацией давности"""
        if not dt:
            return "-"
        
        if thresholds is None:
            thresholds = {
                "green": 3,    # дней
                "orange": 7,   # дней
                "red": 30      # дней
            }
        
        now = timezone.now()
        delta = now - dt
        
        if delta.days <= thresholds["green"]:
            color = "green"
        elif delta.days <= thresholds["orange"]:
            color = "orange"
        else:
            color = "red"
        
        return format_html(
            '<span style="color: {};">{}</span>',
            color,
            self.format_datetime(dt)
        )


class MediaPreviewMixin:
    """Миксин для превью медиа файлов"""
    
    def media_preview(self, obj, width=100, height=100):
        """Создает превью для медиа объекта"""
        if not obj:
            return "-"
        
        if hasattr(obj, "media_type"):
            if obj.media_type == "photo":
                return self._photo_preview(obj, width, height)
            elif obj.media_type == "video":
                return self._video_preview(obj, width, height)
        
        # Fallback для обычных изображений
        if hasattr(obj, "get_display_url"):
            url = obj.get_display_url()
            if url:
                return format_html(
                    '<img src="{}" style="max-width: {}px; max-height: {}px;" />',
                    url, width, height
                )
        
        return "-"
    
    def _photo_preview(self, obj, width, height):
        """Превью для фото"""
        url = obj.get_display_url() if hasattr(obj, "get_display_url") else None
        if not url:
            return "-"
        
        return format_html(
            '<img src="{}" style="max-width: {}px; max-height: {}px;" '
            'onerror="this.style.display=\'none\'; this.nextElementSibling.style.display=\'block\';" />'
            '<div style="display: none; width: {}px; height: {}px; '
            'background: #f0f0f0; border: 1px solid #ddd; '
            'text-align: center; line-height: {}px; color: #666; font-size: 12px;">No image</div>',
            url, width, height, width, height, height
        )
    
    def _video_preview(self, obj, width, height):
        """Превью для видео"""
        video_url = obj.get_display_url() if hasattr(obj, "get_display_url") else None
        thumbnail_url = obj.get_thumbnail_display_url() if hasattr(obj, "get_thumbnail_display_url") else None
        
        if not video_url:
            return "-"
        
        if thumbnail_url:
            return format_html(
                '<video src="{}" poster="{}" style="max-width: {}px; max-height: {}px;" '
                'controls preload="metadata" />',
                video_url, thumbnail_url, width, height
            )
        else:
            return format_html(
                '<video src="{}" style="max-width: {}px; max-height: {}px;" '
                'controls preload="metadata" />',
                video_url, width, height
            )


class CounterMixin:
    """Миксин для форматированного отображения счетчиков"""
    
    def format_count(self, count, use_colors=True, use_abbreviations=True):
        """Форматирует числовые счетчики"""
        if count is None:
            return "-"
        
        count = int(count)
        
        if use_abbreviations:
            if count >= 1000000:
                formatted = f"{count / 1000000:.1f}M"
                color = "gold" if use_colors else None
            elif count >= 1000:
                formatted = f"{count / 1000:.1f}K"
                color = "green" if use_colors else None
            else:
                formatted = str(count)
                color = None
        else:
            formatted = f"{count:,}"
            color = None
        
        if color and use_colors:
            return format_html(
                '<span style="color: {}; font-weight: bold;">{}</span>',
                color, formatted
            )
        
        return formatted
    
    def boolean_icon(self, value, true_text="Да", false_text="Нет"):
        """Отображает булево значение как иконку"""
        if value:
            return format_html(
                '<img src="/static/admin/img/icon-yes.svg" alt="{}" />',
                true_text
            )
        else:
            return format_html(
                '<img src="/static/admin/img/icon-no.svg" alt="{}" />',
                false_text
            )


class LinksMixin:
    """Миксин для создания ссылок на связанные объекты"""
    
    def link_to_object(self, obj, text=None, target="_blank"):
        """Создает ссылку на объект в админке"""
        if not obj:
            return "-"
        
        from django.contrib.contenttypes.models import ContentType
        from django.urls import reverse
        
        ct = ContentType.objects.get_for_model(obj)
        url = reverse(f"admin:{ct.app_label}_{ct.model}_change", args=[obj.pk])
        
        display_text = text or str(obj)
        target_attr = f'target="{target}"' if target else ""
        
        return format_html(
            '<a href="{}" {}>{}</a>',
            url, target_attr, display_text
        )


class BaseInstagramAdmin(
    BulkActionsMixin,
    ExportMixin,
    FilteredAdminMixin,
    DateDisplayMixin,
    CounterMixin,
    MediaPreviewMixin,
    LinksMixin,
    admin.ModelAdmin
):
    """Базовый класс для всех Instagram админов с общей функциональностью"""
    
    # Базовые действия доступные всем
    actions = ["export_as_csv", "export_as_json"]
    
    # Включаем поиск по умолчанию
    search_fields = []
    
    # Базовая пагинация
    list_per_page = 50
    
    # Сохранение фильтров
    preserve_filters = True
    
    def save_model(self, request, obj, form, change):
        """Добавляет пользователя при сохранении"""
        if hasattr(obj, "updated_by") and hasattr(request, "user"):
            obj.updated_by = request.user
        super().save_model(request, obj, form, change)