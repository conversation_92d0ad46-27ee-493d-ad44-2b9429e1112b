"""
Service for analyzing Instagram profiles and uploading photos to GCS.
"""

import logging
import time
from typing import Any, Callable

from django.db import transaction

from instagram_manager.instagram_api import BrightDataClient
from instagram_manager.instagram_api.data_handlers.media_handler import <PERSON>Hand<PERSON>
from instagram_manager.models import InstagramPost, InstagramProfile
from instagram_manager.repositories import PostRepository, ProfileRepository
from instagram_manager.services.batch_post_service import BatchPostService
from instagram_manager.services.profile_service import ProfileService

logger = logging.getLogger(__name__)


class RoastService:
    """Service for analyzing Instagram profiles and uploading photos to GCS."""
    
    def __init__(self):
        """Initialize service with dependencies."""
        self.profile_repo = ProfileRepository()
        self.post_repo = PostRepository()
        self.profile_service = ProfileService()
        self.client = BrightDataClient()
        self.batch_post_service = BatchPostService(self.client)
        self.media_handler = Media<PERSON>andler(save_to_gcs=True, skip_local_save=True)
        
    def process_profile(
        self,
        username: str,
        post_count: int = 10,
        profile_url: str | None = None,
        progress_callback: Callable[[int, int, str], None] | None = None
    ) -> dict[str, Any]:
        """
        Process Instagram profile: fetch data and upload photos to GCS.
        
        Args:
            username: Instagram username (without @)
            post_count: Number of posts to analyze (1-100)
            profile_url: Optional profile URL (will be derived if not provided)
            progress_callback: Optional callback for progress updates (current, total, message)
            
        Returns:
            Dictionary with profile_url, username, post_count, media_urls, and statistics
            
        Raises:
            ValueError: If profile not found or invalid parameters
            Exception: For other errors during processing
        """
        start_time = time.time()
        
        # Progress helper
        def update_progress(current: int, total: int, message: str):
            if progress_callback:
                progress_callback(current, total, message)
            logger.info(f"[ROAST] Progress: {current}/{total} - {message}")
        
        # Step 1: Validate and get/import profile
        update_progress(1, 10, f"Checking profile @{username}")
        
        profile = self._ensure_profile_exists(username)
        
        # Derive profile URL if not provided
        if not profile_url:
            profile_url = f"https://www.instagram.com/{username}/"
            
        # Step 2: Get posts with photos
        update_progress(2, 10, f"Fetching posts for @{username}")
        
        # First, check total posts count for the profile
        total_posts_count = self.post_repo.filter(profile=profile).count()
        logger.info(f"[ROAST] Profile @{username} has {total_posts_count} total posts in DB")
        
        posts_with_photos = self._get_posts_with_photos(profile, post_count)
        photos_count = len(posts_with_photos)
        logger.info(f"[ROAST] Profile @{username} has {photos_count} posts with photos")
        
        # Check if we need more posts
        # Only import if we don't have enough posts with photos AND total posts is less than requested
        need_more_posts = photos_count < post_count and total_posts_count < post_count
        
        if need_more_posts:
            update_progress(3, 10, f"Need more posts (have {photos_count} photos out of {total_posts_count} total posts, need {post_count})")
            posts_with_photos = self._import_more_posts(profile, post_count)
        else:
            # Log why we're not importing
            if photos_count >= post_count:
                logger.info(f"[ROAST] Not importing: already have {photos_count} posts with photos (need {post_count})")
            else:
                logger.info(
                    f"[ROAST] Not importing: have {photos_count} photos out of {total_posts_count} total posts. "
                    f"Total posts >= requested ({total_posts_count} >= {post_count}), likely posts are videos."
                )
            
        # Limit to requested count
        posts_with_photos = posts_with_photos[:post_count]
        
        # Step 3: Upload photos to GCS
        update_progress(4, 10, "Starting photo uploads to GCS")
        
        media_urls = []
        total_photos = sum(
            post.media.filter(media_type="photo").count() 
            for post in posts_with_photos
        )
        
        photos_processed = 0
        photos_uploaded = 0
        
        with transaction.atomic():
            for post_idx, post in enumerate(posts_with_photos):
                # Get photos for this post
                photos = post.media.filter(media_type="photo")
                
                for photo in photos:
                    photos_processed += 1
                    update_progress(
                        4 + int((photos_processed / total_photos) * 5),
                        10,
                        f"Uploading photo {photos_processed}/{total_photos}"
                    )
                    
                    if not photo.gcs_url:
                        # Download and upload to GCS
                        try:
                            success = self.media_handler.download_media(photo)
                            if success:
                                # Refresh from DB to get updated GCS URL
                                photo.refresh_from_db()
                                if photo.gcs_url:
                                    media_urls.append(photo.gcs_url)
                                    photos_uploaded += 1
                                    logger.info(f"[ROAST] Uploaded photo to GCS: {photo.gcs_url}")
                                else:
                                    logger.warning(f"[ROAST] Photo downloaded but no GCS URL: {photo.id}")
                            else:
                                logger.warning(f"[ROAST] Failed to download photo: {photo.media_url}")
                        except Exception as e:
                            logger.error(f"[ROAST] Error uploading photo {photo.id}: {e}")
                    else:
                        # Already has GCS URL
                        media_urls.append(photo.gcs_url)
                        logger.info(f"[ROAST] Photo already in GCS: {photo.gcs_url}")
                        
        # Step 4: Prepare result
        update_progress(10, 10, "Analysis complete")
        
        duration = time.time() - start_time
        
        result = {
            "profile_url": profile_url,
            "username": username,
            "post_count": len(posts_with_photos),
            "media_urls": media_urls,
            "statistics": {
                "total_posts_analyzed": len(posts_with_photos),
                "photos_found": total_photos,
                "photos_uploaded": photos_uploaded,
                "photos_already_in_gcs": len(media_urls) - photos_uploaded,
                "upload_duration_seconds": round(duration, 2)
            }
        }
        
        logger.info(
            f"[ROAST] Completed analysis for @{username}: "
            f"{len(posts_with_photos)} posts, {len(media_urls)} photos, "
            f"{photos_uploaded} uploaded, {duration:.2f}s"
        )
        
        return result
        
    def _ensure_profile_exists(self, username: str) -> InstagramProfile:
        """Ensure profile exists in DB, import if needed."""
        profile = self.profile_repo.get_by_username(username)
        
        if not profile:
            logger.info(f"[ROAST] Profile @{username} not found, importing...")
            profile = self.profile_service.import_profile(username)
            
        if not profile:
            raise ValueError(f"Profile @{username} not found and could not be imported")
            
        return profile
        
    def _get_posts_with_photos(
        self, 
        profile: InstagramProfile, 
        limit: int
    ) -> list[InstagramPost]:
        """Get posts that have photos (not videos)."""
        # Get posts with photos, ordered by date
        posts = self.post_repo.filter(
            profile=profile,
            media__media_type="photo"
        ).distinct().order_by("-posted_at")[:limit]
        
        # Filter to ensure we only get posts with photos
        posts_with_photos = []
        for post in posts:
            if post.media.filter(media_type="photo").exists():
                posts_with_photos.append(post)
                if len(posts_with_photos) >= limit:
                    break
                    
        return posts_with_photos
        
    def _import_more_posts(
        self,
        profile: InstagramProfile,
        target_count: int
    ) -> list[InstagramPost]:
        """Import more posts to reach target count."""
        logger.info(f"[ROAST] Importing more posts for @{profile.username}")
        
        # Import posts with media
        import_result = self.batch_post_service.import_posts_batch(
            profiles=[profile],
            limit=target_count,
            skip_media_download=False,
            save_media_to_gcs=True  # Upload to GCS immediately
        )
        
        # Check result
        profile_result = import_result.get(profile.username, {})
        if not profile_result.get("success"):
            logger.warning(
                f"[ROAST] Failed to import posts for @{profile.username}: "
                f"{profile_result.get('error', 'Unknown error')}"
            )
            
        # Re-fetch posts with photos
        return self._get_posts_with_photos(profile, target_count)