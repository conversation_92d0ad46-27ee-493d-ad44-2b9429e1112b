import logging
from datetime import UTC, datetime
from uuid import uuid4

from django.db import transaction
from django.utils import timezone as django_timezone

from instagram_manager.instagram_api.community_client import CommunityApiClient
from instagram_manager.instagram_api.passport_client import PassportApiClient
from instagram_manager.models import (
    InstagramPost,
    MajilaExportTask,
    MajilaServiceAccount,
)
from instagram_manager.repositories import MajilaExportTaskRepository
from instagram_manager.schemas.majila import (
    ImageContent,
    MajilaExportResult,
    MajilaExportSummary,
    MajilaPost,
    MajilaPostContent,
    SocialMediaContent,
    VideoContent,
)

logger = logging.getLogger(__name__)


class MajilaExportService:
    """Сервис для экспорта постов Instagram в Majila Community"""

    def __init__(self):
        self.passport_client = PassportApiClient()
        self.community_client = None  # Инициализируется после авторизации
        self.logger = logging.getLogger(self.__class__.__name__)
        self.export_task_repository = MajilaExportTaskRepository()

    def authenticate(self, account: MajilaServiceAccount) -> bool:
        """
        Авторизация аккаунта в сервисе Majila

        Args:
            account: Аккаунт для авторизации

        Returns:
            True если авторизация успешна, False в противном случае
        """
        try:
            # Авторизация в Passport API
            auth_response = self.passport_client.login(
                username=account.username, password=account.password
            )

            # Сохранение токена
            account.access_token = auth_response["access_token"]
            account.last_auth_at = django_timezone.now()
            account.auth_error = ""

            # Инициализация Community API клиента с токеном
            self.community_client = CommunityApiClient(
                access_token=account.access_token
            )

            # Получение информации о пользователе из Community API
            user_info = self.community_client.get_user()
            account.user_uuid = user_info.get("uuid")

            account.save()
            logger.info(f"Successfully authenticated account: {account.username}")
            return True

        except Exception as e:
            logger.exception(
                f"Authentication failed for account {account.username}: {e!s}"
            )
            account.auth_error = str(e)
            account.is_active = False
            account.save()
            return False

    def _ensure_authenticated(self, account: MajilaServiceAccount) -> bool:
        """Проверка и обновление аутентификации при необходимости"""
        if not account.access_token or not account.user_uuid:
            return self.authenticate(account)

        # Инициализация клиента с существующим токеном
        self.community_client = CommunityApiClient(access_token=account.access_token)

        try:
            # Проверка валидности токена
            self.community_client.get_user()
            return True
        except Exception as e:
            # Проверяем, является ли это ошибкой 401 (Unauthorized)
            if hasattr(e, "response") and e.response and e.response.status_code == 401:
                logger.info("Token expired, attempting to update token")

                try:
                    # Пытаемся обновить токен
                    token_response = self.passport_client.update_token(
                        account.access_token
                    )

                    # Сохраняем новый токен
                    account.access_token = token_response["access_token"]
                    account.last_auth_at = django_timezone.now()
                    account.auth_error = ""
                    account.save()

                    # Переинициализируем клиент с новым токеном
                    self.community_client = CommunityApiClient(
                        access_token=account.access_token
                    )

                    # Проверяем новый токен
                    try:
                        self.community_client.get_user()
                        logger.info("Successfully updated and validated token")
                        return True
                    except Exception:
                        logger.exception(
                            "New token is also invalid, falling back to full auth"
                        )
                        return self.authenticate(account)

                except Exception as update_error:
                    logger.exception(f"Failed to update token: {update_error!s}")
                    # Если обновление токена не удалось, пробуем полную переавторизацию
                    return self.authenticate(account)
            else:
                # Другая ошибка, не связанная с токеном
                logger.exception(f"Authentication check failed: {e!s}")
                return self.authenticate(account)

    def _convert_instagram_post_to_majila(self, post: InstagramPost) -> MajilaPost:
        """
        Конвертация поста Instagram в формат Majila

        Args:
            post: Пост Instagram

        Returns:
            MajilaPost объект
        """
        # Подготовка social media данных
        social_media = SocialMediaContent(
            external_id=post.external_id,
            external_url=post.post_url,
            external_username=post.profile.username,
            external_user_url=f"https://www.instagram.com/{post.profile.username}/",
        )

        # Подготовка изображений
        images = []
        videos = []

        for media in post.media.all():
            if media.media_type == "photo":
                images.append(
                    ImageContent(
                        image_url=media.get_display_url(),
                        width=media.width,
                        height=media.height,
                    )
                )
            elif media.media_type == "video":
                # Используем thumbnail_url если есть, иначе используем основной URL видео
                preview_url = media.thumbnail_url if media.thumbnail_url else media.get_display_url()
                logger.debug(
                    f"Video media {media.media_id}: "
                    f"thumbnail_url='{media.thumbnail_url}', "
                    f"using preview_url='{preview_url[:100]}...'"
                )
                videos.append(
                    VideoContent(
                        video_url=media.get_display_url(),
                        preview_url=preview_url,
                    )
                )

        # Создание контента поста
        content = MajilaPostContent(
            social_media=social_media,
            text=post.caption,
            images=images if images else None,
            videos=videos if videos else None,
            title=None,  # Instagram посты обычно не имеют заголовков
        )

        # Создание поста для Majila
        majila_post = MajilaPost(
            post_uuid=uuid4(),
            external_id=post.external_id,
            content=content,
            post_type="social_media_post",
            original_platform="instagram",
            posted_at=post.posted_at,
            is_active=True,
        )

        return majila_post

    @transaction.atomic
    def export_posts(
        self,
        account: MajilaServiceAccount,
        posts: list[InstagramPost],
        skip_existing: bool = True,
    ) -> MajilaExportSummary:
        """
        Экспорт постов в сервис Majila

        Args:
            account: Аккаунт для экспорта
            posts: Список постов для экспорта
            skip_existing: Пропускать уже экспортированные посты

        Returns:
            MajilaExportSummary с результатами экспорта
        """
        # Создание задачи экспорта
        export_task = self.export_task_repository.create(
            account=account,
            status="in_progress",
            total_posts=len(posts),
            started_at=django_timezone.now(),
        )
        export_task.posts.set(posts)

        # Проверка аутентификации
        if not self._ensure_authenticated(account):
            export_task.status = "failed"
            export_task.error_message = "Authentication failed"
            export_task.completed_at = django_timezone.now()
            export_task.save()

            return MajilaExportSummary(
                total_posts=len(posts),
                successful=0,
                failed=len(posts),
                skipped=0,
                results=[],
            )

        # Результаты экспорта
        results = []
        successful = 0
        failed = 0
        skipped = 0

        for post in posts:
            try:
                # Проверка существования поста в Majila (если реализовано)
                if skip_existing and self.community_client.check_post_exists(
                    post.post_id
                ):
                    results.append(
                        MajilaExportResult(
                            post_id=post.id,
                            status="skipped",
                            error_message="Post already exists in Majila",
                        )
                    )
                    skipped += 1
                    continue

                # Конвертация поста
                majila_post = self._convert_instagram_post_to_majila(post)

                # Отправка поста в Majila с правильной сериализацией UUID и datetime
                post_data = majila_post.model_dump(mode="json")
                
                # Логирование данных для отладки
                logger.debug(f"Exporting post {post.post_id} to Majila")
                logger.debug(f"Post type: {post.post_type}")
                logger.debug(f"Media count: {post.media.count()}")
                
                # Логируем видео контент если есть
                if post_data.get("content", {}).get("videos"):
                    for i, video in enumerate(post_data["content"]["videos"]):
                        logger.debug(
                            f"Video {i}: preview_url='{video.get('preview_url', 'MISSING')[:100]}...'"
                        )
                
                self.community_client.create_post(post_data)

                results.append(
                    MajilaExportResult(
                        post_id=post.id,
                        status="success",
                        majila_post_uuid=majila_post.post_uuid,
                        exported_at=datetime.now(UTC),
                    )
                )
                successful += 1

                logger.info(f"Successfully exported post {post.post_id} to Majila")

            except Exception as e:
                logger.exception(f"Failed to export post {post.post_id}: {e!s}")
                results.append(
                    MajilaExportResult(
                        post_id=post.id, status="failed", error_message=str(e)
                    )
                )
                failed += 1

        # Обновление задачи экспорта
        export_task.status = (
            "completed" if failed == 0 else ("partial" if successful > 0 else "failed")
        )
        export_task.exported_count = successful
        export_task.failed_count = failed
        export_task.export_results = [r.model_dump(mode="json") for r in results]
        export_task.completed_at = django_timezone.now()
        export_task.save()

        return MajilaExportSummary(
            total_posts=len(posts),
            successful=successful,
            failed=failed,
            skipped=skipped,
            results=results,
        )

    def get_export_history(
        self, account: MajilaServiceAccount | None = None, limit: int = 10
    ) -> list[MajilaExportTask]:
        """
        Получение истории экспорта

        Args:
            account: Фильтр по аккаунту (если указан)
            limit: Максимальное количество записей

        Returns:
            Список задач экспорта
        """
        if account:
            return list(self.export_task_repository.get_tasks_by_account(account, limit))
        else:
            return list(self.export_task_repository.get_recent_tasks(limit))
