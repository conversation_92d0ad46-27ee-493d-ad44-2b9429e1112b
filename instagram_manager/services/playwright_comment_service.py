"""Service for posting Instagram comments using <PERSON><PERSON>."""

import asyncio
import logging
import re
from datetime import datetime
from typing import Any

from instagram_manager.browser_automation import (
    InstagramActions,
    InstagramAuth,
    PlaywrightClient,
)
from instagram_manager.models import InstagramAccount, InstagramPost, PostedComment
from instagram_manager.repositories import (
    AccountRepository,
    ProfileRepository,
)
from instagram_manager.repositories import (
    PostRepository as InstagramPostRepository,
)

logger = logging.getLogger(__name__)


class PlaywrightCommentService:
    """Service for posting comments to Instagram using Playwright."""
    
    def __init__(self):
        """Initialize the service."""
        self.post_repository = InstagramPostRepository()
        self.account_repository = AccountRepository()
        self.profile_repository = ProfileRepository()
        
    async def post_comment(
        self,
        account_id: int,
        post_url: str,
        comment_text: str,
        headless: bool = True,
        proxy: dict[str, str] | None = None,
        debug: bool = False,
        force_login: bool = False,
    ) -> tuple[bool, str | None, PostedComment | None]:
        """
        Post a comment to an Instagram post.
        
        Args:
            account_id: ID of InstagramAccount to use
            post_url: URL of the Instagram post
            comment_text: Text to comment
            headless: Run browser in headless mode
            proxy: Optional proxy configuration
            debug: Enable debug mode with DevTools and slow motion
            
        Returns:
            Tuple of (success, error_message, posted_comment)
        """
        try:
            # Get account
            account = await self.account_repository.aget_by_id_with_related(account_id)
            if not account:
                raise InstagramAccount.DoesNotExist(f"Account with id {account_id} not found")
            if not account.is_active:
                return False, "Account is not active", None
                
            # Get password
            password = account.password
            if not password:
                return False, "Account password not available", None
                
            # Extract post ID and get/create post
            post = await self._get_or_create_post(post_url)
            
            # Run the browser automation
            logger.info(f"Creating Playwright client (debug={debug}, headless={headless})")
            async with PlaywrightClient.create(
                headless=headless,
                proxy=proxy,
                debug=debug
            ) as client:
                logger.info("Playwright client created successfully")
                
                # Get auth state path
                auth_state_path = client.get_auth_state_path(account.username)
                
                # Check if we have existing session and should use it
                if not force_login and await client.check_auth_state_exists(account.username):
                    logger.info(f"Found existing session for {account.username}, using it directly")
                    
                    # Use existing session directly for commenting
                    async with client.new_context(
                        storage_state_path=auth_state_path
                    ) as context:
                        async with client.new_page(context) as page:
                            # Validate session quickly
                            auth = InstagramAuth(client)
                            is_valid = await auth._validate_session(page)
                            
                            if is_valid:
                                logger.info("Session is valid, proceeding to comment posting")
                                
                                # Initialize actions on the same page
                                actions = InstagramActions(page)
                                
                                # Navigate to post
                                nav_success, nav_error = await actions.navigate_to_post(post_url)
                                if not nav_success:
                                    return False, nav_error, None
                                    
                                # Post comment
                                success, error, posted_comment = await actions.post_comment(
                                    comment_text=comment_text,
                                    post=post,
                                    account_id=account.id
                                )
                                
                                return success, error, posted_comment
                            else:
                                logger.info("Session invalid, need to re-login")
                                # Continue to login flow below
                
                # Need to login (either no session or invalid session)
                logger.info("Performing login...")
                auth = InstagramAuth(client)
                
                # Create context for login and save session
                async with client.new_context(
                    save_storage_state_path=auth_state_path
                ) as context:
                    async with client.new_page(context) as page:
                        # Perform login
                        success, error = await auth._perform_login(page, account.username, password, account)
                        
                        if not success:
                            return False, f"Login failed: {error}", None
                            
                        logger.info("Login successful, proceeding to comment posting")
                        
                        # Use the same authenticated page for commenting
                        actions = InstagramActions(page)
                        
                        # Navigate to post
                        nav_success, nav_error = await actions.navigate_to_post(post_url)
                        if not nav_success:
                            return False, nav_error, None
                            
                        # Post comment
                        success, error, posted_comment = await actions.post_comment(
                            comment_text=comment_text,
                            post=post,
                            account_id=account.id
                        )
                        
                        return success, error, posted_comment
                        
        except InstagramAccount.DoesNotExist:
            return False, f"Account with ID {account_id} not found", None
        except Exception as e:
            error = f"Unexpected error: {e!s}"
            logger.error(error, exc_info=True)
            return False, error, None
            
    async def post_bulk_comments(
        self,
        account_id: int,
        comments_data: list[dict[str, str]],
        headless: bool = True,
        proxy: dict[str, str] | None = None,
        delay_between_comments: int = 60,
        debug: bool = False,
    ) -> dict[str, Any]:
        """
        Post multiple comments using the same account.
        
        Args:
            account_id: ID of InstagramAccount to use
            comments_data: List of dicts with 'post_url' and 'comment_text'
            headless: Run browser in headless mode
            proxy: Optional proxy configuration
            delay_between_comments: Seconds to wait between comments
            debug: Enable debug mode with DevTools and slow motion
            
        Returns:
            Dict with results summary
        """
        results: dict[str, Any] = {
            "total": len(comments_data),
            "successful": 0,
            "failed": 0,
            "errors": [],
            "posted_comments": []
        }
        
        try:
            # Get account
            account = await self.account_repository.aget_by_id_with_related(account_id)
            if not account:
                results["errors"].append(f"Account with ID {account_id} not found")
                results["failed"] = results["total"]
                return results
            if not account.is_active:
                results["errors"].append("Account is not active")
                results["failed"] = results["total"]
                return results
                
            # Get password
            password = account.password
            if not password:
                results["errors"].append("Account password not available")
                results["failed"] = results["total"]
                return results
                
            # Run browser automation
            async with PlaywrightClient.create(
                headless=headless,
                proxy=proxy,
                debug=debug
            ) as client:
                # Authenticate once
                auth = InstagramAuth(client)
                login_success, login_error = await auth.login(
                    username=account.username,
                    password=password,
                    account=account
                )
                
                if not login_success:
                    results["errors"].append(f"Login failed: {login_error}")
                    results["failed"] = results["total"]
                    return results
                    
                # Use same authenticated context for all comments
                auth_state_path = client.get_auth_state_path(account.username)
                async with client.new_context(
                    storage_state_path=auth_state_path
                ) as context:
                    async with client.new_page(context) as page:
                        actions = InstagramActions(page)
                        
                        # Post each comment
                        for i, comment_data in enumerate(comments_data):
                            post_url = comment_data.get("post_url")
                            comment_text = comment_data.get("comment_text")
                            
                            if not post_url or not comment_text:
                                results["failed"] += 1
                                results["errors"].append(f"Invalid data at index {i}")
                                continue
                                
                            try:
                                # Get/create post
                                post = await self._get_or_create_post(post_url)
                                
                                # Navigate to post
                                nav_success, nav_error = await actions.navigate_to_post(post_url)
                                if not nav_success:
                                    results["failed"] += 1
                                    results["errors"].append(f"{post_url}: {nav_error}")
                                    continue
                                    
                                # Post comment
                                success, error, posted_comment = await actions.post_comment(
                                    comment_text=comment_text,
                                    post=post,
                                    account_id=account.id
                                )
                                
                                if success:
                                    results["successful"] += 1
                                    if posted_comment:
                                        results["posted_comments"].append(posted_comment.id)
                                else:
                                    results["failed"] += 1
                                    results["errors"].append(f"{post_url}: {error}")
                                    
                                    # Stop if rate limited
                                    if error and "limit" in error.lower():
                                        results["errors"].append("Rate limit detected, stopping bulk operation")
                                        break
                                        
                                # Delay between comments (except for last one)
                                if i < len(comments_data) - 1:
                                    logger.info(f"Waiting {delay_between_comments}s before next comment...")
                                    await asyncio.sleep(delay_between_comments)
                                    
                            except Exception as e:
                                results["failed"] += 1
                                results["errors"].append(f"{post_url}: {e!s}")
                                
        except Exception as e:
            error = f"Bulk comment error: {e!s}"
            logger.error(error, exc_info=True)
            results["errors"].append(error)
            results["failed"] = results["total"] - results["successful"]
            
        return results
        
    async def _get_or_create_post(self, post_url: str) -> InstagramPost:
        """Get existing post or create placeholder."""
        # Extract post code from URL
        match = re.match(r"https://www\.instagram\.com/p/([\w-]+)/?", post_url)
        if not match:
            raise ValueError(f"Invalid Instagram post URL: {post_url}")
            
        post_code = match.group(1)
        
        # Try to get existing post
        post = await self.post_repository.model.objects.filter(shortcode=post_code).afirst()
        if post:
            return post
        
        # Create placeholder post with minimal required fields
        # We need to create a minimal profile if not exists
        profile = await self.profile_repository.model.objects.filter(username="unknown_user").afirst()
        if not profile:
            profile = await self.profile_repository.model.objects.acreate(
                username="unknown_user",
                profile_id="unknown",
                full_name="Unknown User",
            )
        
        post = InstagramPost(
            post_id=f"placeholder_{post_code}",
            shortcode=post_code,
            profile=profile,
            caption="[Placeholder for comment posting]",
            post_type="photo",  # Default type
            posted_at=datetime.now(),
            post_url=post_url,
            comment_count=0,
            like_count=0
        )
        await post.asave()
        return post
            
    def post_comment_sync(
        self,
        account_id: int,
        post_url: str,
        comment_text: str,
        headless: bool = True,
        proxy: dict[str, str] | None = None,
        debug: bool = False,
        force_login: bool = False,
    ) -> tuple[bool, str | None, int | None]:
        """Synchronous wrapper for post_comment.
        
        Returns:
            Tuple of (success, error_message, posted_comment_id)
        """
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            success, error, posted_comment = loop.run_until_complete(
                self.post_comment(account_id, post_url, comment_text, headless, proxy, debug, force_login)
            )
            posted_comment_id = posted_comment.id if posted_comment else None
            return success, error, posted_comment_id
        finally:
            loop.close()
            
    def post_bulk_comments_sync(
        self,
        account_id: int,
        comments_data: list[dict[str, str]],
        headless: bool = True,
        proxy: dict[str, str] | None = None,
        delay_between_comments: int = 60,
        debug: bool = False,
    ) -> dict[str, Any]:
        """Synchronous wrapper for post_bulk_comments."""
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            return loop.run_until_complete(
                self.post_bulk_comments(
                    account_id,
                    comments_data,
                    headless,
                    proxy,
                    delay_between_comments,
                    debug,
                ),
            )
        finally:
            loop.close()
