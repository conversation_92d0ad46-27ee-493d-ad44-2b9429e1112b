"""
Service for batch processing Instagram posts for multiple profiles.
Handles efficient data fetching and distribution across profiles.
"""
import logging
from datetime import datetime
from typing import Any
from urllib.parse import urlparse

from django.db import transaction
from django.db import utils as django_db_utils
from django.utils import timezone

from instagram_manager.instagram_api import BrightDataClient
from instagram_manager.instagram_api.data_handlers.post_handler import PostHandler
from instagram_manager.models import InstagramPost, InstagramProfile, InstagramScrapingTask
from instagram_manager.repositories import ProfileRepository, TaskRepository
from instagram_manager.schemas.brightdata import InstagramPostResponse

logger = logging.getLogger(__name__)


class BatchPostService:
    """Service for batch importing posts for multiple Instagram profiles"""
    
    def __init__(self, client: BrightDataClient):
        self.client = client
        self.post_handler: PostHandler | None = None  # Will be initialized when needed with proper params
        self.profile_repository = ProfileRepository()
        self.task_repository = TaskRepository()
    
    @staticmethod
    def _convert_date_format(date_str: str | None) -> str:
        """Convert date from YYYY-MM-DD to MM-DD-YYYY format for BrightData API."""
        if not date_str:
            return ""
        
        try:
            # Parse YYYY-MM-DD format
            dt = datetime.strptime(date_str, "%Y-%m-%d")
            # Return MM-DD-YYYY format
            return dt.strftime("%m-%d-%Y")
        except ValueError:
            # If already in correct format or invalid, return as is
            return date_str
    
    def import_posts_batch(
        self,
        profiles: list[InstagramProfile],
        start_date: str | None = None,
        end_date: str | None = None,
        post_types: list[str] | None = None,
        limit: int | None = None,
        task: InstagramScrapingTask | None = None,
        skip_media_download: bool = False,
        save_media_to_gcs: bool = False,
        import_all_users: bool = False
    ) -> dict[str, dict[str, Any]]:
        """
        Import posts for multiple profiles in a single batch.
        
        Args:
            profiles: List of InstagramProfile objects
            start_date: Filter posts from this date
            end_date: Filter posts until this date
            post_types: Filter by post types
            limit: Maximum posts per profile
            task: Scraping task for tracking
            skip_media_download: Skip downloading media files
            save_media_to_gcs: Save media to Google Cloud Storage
            import_all_users: Import posts from all users in response
            
        Returns:
            Dictionary with results for each profile
        """
        results = {}
        
        # Initialize PostHandler with proper params
        # save_to_gcs should be independent of skip_media_download
        logger.info(
            f"[BATCH SERVICE] Creating PostHandler with save_to_gcs={save_media_to_gcs}, "
            f"skip_local_save={save_media_to_gcs}, skip_media_download={skip_media_download}"
        )
        self.post_handler = PostHandler(save_to_gcs=save_media_to_gcs, skip_local_save=save_media_to_gcs)
            
        logger.info(
            f"[BATCH SERVICE] PostHandler initialized with save_to_gcs={save_media_to_gcs}, "
            f"skip_local_save={save_media_to_gcs}, skip_media_download={skip_media_download}, "
            f"handler._gcs_service={self.post_handler.media_handler._gcs_service is not None}"
        )
        
        try:
            # Подготавливаем URLs для batch запроса
            profile_urls = [f"https://www.instagram.com/{p.username}" for p in profiles]
            
            logger.info(f"Starting batch import for {len(profiles)} profiles")
            logger.info(f"Parameters: limit={limit}, start_date={start_date}, end_date={end_date}, post_types={post_types}")
            logger.info(f"Profile URLs: {profile_urls}")
            logger.info(f"[BATCH DEBUG] Raw parameters received: start_date={start_date}, end_date={end_date}")
            
            # Convert dates to BrightData format (MM-DD-YYYY)
            api_start_date = self._convert_date_format(start_date) if start_date else None
            api_end_date = self._convert_date_format(end_date) if end_date else None
            
            # Convert post_types list to single post_type if needed
            api_post_type = None
            if post_types and len(post_types) > 0:
                # Если выбраны все 4 типа, не передаем фильтр
                all_types = ["photo", "video", "carousel", "reel"]
                if set(post_types) != set(all_types):
                    api_post_type = post_types[0]  # BrightData accepts only single type
            
            # Подготавливаем параметры для batch discover
            discover_params: dict[str, Any] = {
                "dataset_type": "instagram_posts",
                "urls": profile_urls,
                "discover_by": "url",
            }
            
            # Добавляем опциональные параметры только если они заданы
            if limit:
                discover_params["num_of_posts"] = limit
            if api_start_date:
                discover_params["start_date"] = api_start_date
            if api_end_date:
                discover_params["end_date"] = api_end_date
            if api_post_type:
                discover_params["post_type"] = api_post_type
            
            # Запускаем batch discover
            snapshot_id = self.client.trigger_discover(**discover_params)
            
            # Обновляем задачу
            if task:
                # Проверяем что snapshot_id это строка, а не MagicMock
                if isinstance(snapshot_id, str):
                    task.brightdata_snapshot_id = snapshot_id
                else:
                    task.brightdata_snapshot_id = str(snapshot_id) if snapshot_id else ""
                task.skip_media_download = skip_media_download
                task.save()
            
            # Получаем и обрабатываем результаты
            posts_data = self.client.get_batch_posts_from_snapshot(
                snapshot_id=snapshot_id,
                profile_urls=profile_urls
            )
            
            # Распределяем посты по профилям
            distribution_result = self._distribute_posts_by_profile(posts_data, profiles)
            posts_by_profile = distribution_result["posts_by_profile"]
            statistics = distribution_result["statistics"]
            
            # Сохраняем статистику в задаче
            if task:
                task.total_items_received = statistics["total_received"]
                task.items_filtered = statistics["unmatched_posts"]
                task.additional_users = statistics["additional_users"]
                task.save()
            
            # Если включен import_all_users, создаем профили для дополнительных пользователей
            if import_all_users and statistics["additional_users"]:
                logger.info(f"Creating profiles for {len(statistics['additional_users'])} additional users...")
                
                # Создаем профили для дополнительных пользователей
                from instagram_manager.services.batch_profile_service import BatchProfileService
                batch_profile_service = BatchProfileService(self.client)
                
                additional_profiles, failed_usernames = batch_profile_service.validate_and_create_profiles(
                    usernames=statistics["additional_users"]
                )
                
                if additional_profiles:
                    logger.info(f"Created {len(additional_profiles)} additional profiles")
                    
                    # Перераспределяем посты с учетом новых профилей
                    all_profiles = profiles + additional_profiles
                    distribution_result = self._distribute_posts_by_profile(posts_data, all_profiles)
                    posts_by_profile = distribution_result["posts_by_profile"]
                    
                    # Добавляем новые профили в список для обработки
                    profiles = all_profiles
            
            # Обрабатываем посты для каждого профиля
            for profile in profiles:
                username = profile.username
                profile_posts = posts_by_profile.get(username, [])
                
                try:
                    # Применяем лимит если указан
                    if limit and len(profile_posts) > limit:
                        profile_posts = profile_posts[:limit]
                    
                    # Сохраняем посты
                    saved_posts = self._save_posts_for_profile(
                        profile, profile_posts, skip_media_download, save_media_to_gcs
                    )
                    
                    results[username] = {
                        "success": True,
                        "post_count": len(saved_posts),
                        "posts": saved_posts,
                        "total_found": len(profile_posts)
                    }
                    
                except Exception as e:
                    logger.exception(f"Error processing posts for {username}: {e!s}")
                    results[username] = {
                        "success": False,
                        "post_count": 0,
                        "error": str(e)
                    }
            
            # Добавляем общую статистику
            results["_statistics"] = distribution_result.get("statistics", {})
            
            return results
            
        except Exception as e:
            logger.exception(f"Batch import failed: {e!s}")
            
            # Возвращаем ошибку для всех профилей
            for profile in profiles:
                results[profile.username] = {
                    "success": False,
                    "post_count": 0,
                    "error": f"Batch error: {e!s}"
                }
            
            return results
    
    def _distribute_posts_by_profile(
        self,
        posts_data: list[dict[Any, Any]],
        profiles: list[InstagramProfile]
    ) -> dict[str, Any]:
        """
        Распределяет посты по профилям на основе URL или username.
        Также собирает статистику о дополнительных пользователях.
        
        Returns:
            Dictionary с постами по профилям и статистикой
        """
        posts_by_profile: dict[str, list[InstagramPostResponse]] = {
            profile.username: [] for profile in profiles
        }
        
        # Для сбора статистики
        additional_users = set()
        unmatched_posts = 0
        error_count = 0
        
        # Создаём маппинг для быстрого поиска
        username_to_profile_id = {p.username: p.profile_id for p in profiles}
        
        for post_data in posts_data:
            # Проверяем, не является ли это ошибкой или предупреждением от BrightData
            if isinstance(post_data, dict):
                # Check for various error/warning patterns from BrightData
                is_error_response = False
                
                # Pattern 1: Direct error fields
                if "error" in post_data or "error_code" in post_data:
                    error_msg = post_data.get("error", "Unknown error")
                    error_code = post_data.get("error_code", "unknown")
                    url = post_data.get("input", {}).get("url", "unknown")
                    logger.warning(f"BrightData error for {url}: [{error_code}] {error_msg}")
                    error_count += 1
                    is_error_response = True
                    
                # Pattern 2: Warning responses with timestamp (from logs)
                elif "warning" in post_data and "timestamp" in post_data:
                    warning_msg = post_data.get("warning", "Unknown warning")
                    logger.warning(f"BrightData warning: {warning_msg} at {post_data.get('timestamp')}")
                    error_count += 1
                    is_error_response = True
                    
                # Pattern 3: Missing required fields (only timestamp present)
                elif "timestamp" in post_data and not any(key in post_data for key in ["pk", "shortcode", "url", "content_id"]):
                    logger.warning(f"BrightData returned incomplete data: {post_data}")
                    error_count += 1
                    is_error_response = True
                
                if is_error_response:
                    continue
                
            try:
                # Валидируем данные поста
                post = InstagramPostResponse(**post_data)
                
                # Определяем к какому профилю относится пост
                # Способ 1: По user_posted (основное поле от BrightData)
                if hasattr(post, "user_posted") and post.user_posted:
                    username = post.user_posted
                    if username in posts_by_profile:
                        posts_by_profile[username].append(post)
                        continue
                
                # Способ 2: По owner_username если есть
                if hasattr(post, "owner_username") and post.owner_username:
                    username = post.owner_username
                    if username in posts_by_profile:
                        posts_by_profile[username].append(post)
                        continue
                
                # Способ 2: По owner_id если есть
                if hasattr(post, "owner_id") and post.owner_id:
                    for username, profile_id in username_to_profile_id.items():
                        if str(profile_id) == str(post.owner_id):
                            posts_by_profile[username].append(post)
                            break
                    else:
                        # Не нашли соответствие, пробуем другие способы
                        pass
                
                # Способ 3: Парсим URL поста
                if post.url:
                    parsed_url = urlparse(post.url)
                    path_parts = parsed_url.path.strip("/").split("/")
                    
                    # Instagram URL обычно: /username/p/shortcode/ или /p/shortcode/
                    if len(path_parts) >= 3 and path_parts[1] == "p":
                        # Формат: /username/p/shortcode/
                        potential_username = path_parts[0]
                        if potential_username in posts_by_profile:
                            posts_by_profile[potential_username].append(post)
                            continue
                
                # Если не смогли определить - добавляем в статистику
                unmatched_posts += 1
                # Пытаемся найти username из данных поста
                if hasattr(post, "user_posted") and post.user_posted:
                    additional_users.add(post.user_posted)
                elif hasattr(post, "owner_username") and post.owner_username:
                    additional_users.add(post.owner_username)
                
                logger.debug(
                    f"Post {post.shortcode or 'unknown'} from user "
                    f"{getattr(post, 'user_posted', 'unknown')} not matched to requested profiles"
                )
                
            except Exception as e:
                # Provide more detailed error information
                if hasattr(e, "__class__"):
                    error_type = e.__class__.__name__
                else:
                    error_type = type(e).__name__
                    
                logger.error(f"Error processing post data: {error_type}: {e!s}")
                
                # Log specific validation errors if it's a Pydantic error
                if "ValidationError" in error_type and hasattr(e, "errors"):
                    logger.error(f"Validation errors: {e.errors()}")
                    
                # Log first few fields of problematic data for debugging
                if isinstance(post_data, dict):
                    sample_fields = {k: v for k, v in list(post_data.items())[:5]}
                    logger.debug(f"Post data sample (first 5 fields): {sample_fields}")
                else:
                    logger.debug(f"Post data type: {type(post_data)}")
                    
                error_count += 1
        
        # Логируем статистику
        total_requested_posts = sum(len(posts) for posts in posts_by_profile.values())
        logger.info(f"Total posts received: {len(posts_data)}")
        logger.info(f"Posts matched to requested profiles: {total_requested_posts}")
        logger.info(f"Posts from other users: {unmatched_posts}")
        if error_count > 0:
            logger.warning(f"BrightData errors: {error_count}")
        
        if additional_users:
            logger.info(f"Additional users found in response ({len(additional_users)}): {', '.join(sorted(additional_users)[:10])}...")
        
        for username, posts in posts_by_profile.items():
            if posts:
                logger.info(f"  {username}: {len(posts)} posts")
        
        return {
            "posts_by_profile": posts_by_profile,
            "statistics": {
                "total_received": len(posts_data),
                "matched_posts": total_requested_posts,
                "unmatched_posts": unmatched_posts,
                "additional_users": list(additional_users),
                "error_count": error_count
            }
        }
    
    def _save_posts_for_profile(
        self,
        profile: InstagramProfile,
        posts: list[InstagramPostResponse],
        skip_media_download: bool = False,
        save_media_to_gcs: bool = False
    ) -> list[InstagramPost]:
        """Сохраняет посты для конкретного профиля"""
        saved_posts = []
        failed_posts = []
        
        try:
            with transaction.atomic():
                logger.debug(
                    f"[TRANSACTION START] Starting atomic transaction for saving {len(posts)} posts "
                    f"for profile {profile.username}"
                )
                
                for post_data in posts:
                    try:
                        # Используем PostHandler для обработки
                        # post_data уже является InstagramPostResponse, нужно передать как словарь
                        post = self.post_handler.process_post_data(
                            post_data,
                            profile,
                            skip_media_download=skip_media_download
                        )
                        saved_posts.append(post)
                        
                    except Exception as e:
                        shortcode = getattr(post_data, "shortcode", "unknown")
                        logger.exception(
                            f"Error saving post {shortcode} "
                            f"for {profile.username}: {e!s}"
                        )
                        failed_posts.append(shortcode)
                        # Re-raise database errors to rollback transaction
                        if "value too long" in str(e) or isinstance(e, django_db_utils.DataError):
                            logger.warning(
                                f"[TRANSACTION ROLLBACK] Database error will cause transaction rollback "
                                f"for all {len(posts)} posts"
                            )
                            raise
                
                logger.debug(
                    f"[TRANSACTION END] Successfully saved {len(saved_posts)} posts in transaction"
                )
                        
        except django_db_utils.DataError as e:
            # Database error - all posts in this transaction will fail
            logger.error(
                f"[TRANSACTION ERROR] Database error saving posts for {profile.username}: {e!s}. "
                f"Transaction rolled back, no posts saved. This often happens with data that exceeds "
                f"field size limits. Check content field sizes."
            )
            # Re-raise to let parent handler know about the critical error
            raise
            
        except Exception as e:
            logger.exception(
                f"[TRANSACTION ERROR] Unexpected error saving posts for {profile.username}: {e!s}. "
                f"Transaction rolled back."
            )
            raise
        
        # Обновляем время последнего скрапинга только если хоть что-то сохранили
        if saved_posts:
            profile.last_scraped_at = timezone.now()
            profile.save()
        
        if failed_posts:
            logger.warning(
                f"Failed to save {len(failed_posts)} posts for {profile.username}: "
                f"{', '.join(failed_posts[:5])}{'...' if len(failed_posts) > 5 else ''}"
            )
        
        return saved_posts
    
    def process_snapshot_data(
        self,
        snapshot_data: list[dict[Any, Any]],
        profiles: list[InstagramProfile],
        task: InstagramScrapingTask | None = None,
        skip_media_download: bool = False,
        limit_per_user: int | None = None
    ) -> dict[str, dict[str, Any]]:
        """
        Process Instagram posts from a BrightData snapshot JSON file.
        
        Args:
            snapshot_data: List of post data from BrightData snapshot
            profiles: List of InstagramProfile objects to match posts to
            task: Optional scraping task for tracking
            skip_media_download: Skip downloading media files
            limit_per_user: Maximum posts to import per user
            
        Returns:
            Dictionary with results for each profile and statistics
        """
        results = {}
        
        try:
            logger.info(f"Processing snapshot with {len(snapshot_data)} entries for {len(profiles)} profiles")
            
            # Distribute posts by profile
            distribution_result = self._distribute_posts_by_profile(snapshot_data, profiles)
            posts_by_profile = distribution_result["posts_by_profile"]
            statistics = distribution_result["statistics"]
            
            # Update task if provided
            if task:
                task.total_items_received = statistics["total_received"]
                task.items_filtered = statistics["unmatched_posts"]
                task.additional_users = statistics["additional_users"]
                task.save()
            
            # Process posts for each profile
            for profile in profiles:
                username = profile.username
                profile_posts = posts_by_profile.get(username, [])
                
                try:
                    # Apply limit if specified
                    if limit_per_user and len(profile_posts) > limit_per_user:
                        profile_posts = profile_posts[:limit_per_user]
                    
                    # Save posts
                    saved_posts = self._save_posts_for_profile(
                        profile, 
                        profile_posts, 
                        skip_media_download
                    )
                    
                    results[username] = {
                        "success": True,
                        "post_count": len(saved_posts),
                        "posts": saved_posts,
                        "total_found": len(posts_by_profile.get(username, []))
                    }
                    
                    logger.info(f"Successfully imported {len(saved_posts)} posts for {username}")
                    
                except Exception as e:
                    logger.exception(f"Error processing posts for {username}: {e!s}")
                    results[username] = {
                        "success": False,
                        "post_count": 0,
                        "error": str(e)
                    }
            
            # Add statistics to results
            results["_statistics"] = statistics
            
            # Update task status
            if task:
                successful_count = sum(1 for r in results.values() if isinstance(r, dict) and r.get("success"))
                total_posts = sum(r.get("post_count", 0) for r in results.values() if isinstance(r, dict))
                
                task.items_scraped = total_posts
                task.batch_results = {
                    username: {
                        "success": result.get("success", False),
                        "post_count": result.get("post_count", 0),
                        "error": result.get("error")
                    }
                    for username, result in results.items()
                    if username != "_statistics"
                }
                
                if successful_count == len(profiles):
                    task.status = "completed"
                elif successful_count > 0:
                    task.status = "partial"
                else:
                    task.status = "failed"
                    
                task.completed_at = timezone.now()
                task.save()
            
            return results
            
        except Exception as e:
            logger.exception(f"Snapshot processing failed: {e!s}")
            
            # Mark all profiles as failed
            for profile in profiles:
                results[profile.username] = {
                    "success": False,
                    "post_count": 0,
                    "error": f"Snapshot processing error: {e!s}"
                }
            
            # Update task as failed
            if task:
                task.status = "failed"
                task.error_message = str(e)
                task.completed_at = timezone.now()
                task.save()
            
            return results