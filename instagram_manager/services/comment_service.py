"""
Service для работы с Instagram комментариями.
"""

import logging
from typing import Any, cast

from django.db import transaction

from core.services.base import BaseService
from instagram_manager.instagram_api import BrightDataClient
from instagram_manager.instagram_api.data_handlers.comment_handler import CommentHandler
from instagram_manager.models import InstagramComment, InstagramPost
from instagram_manager.repositories.comment_repository import InstagramCommentRepository
from instagram_manager.schemas.brightdata import InstagramCommentResponse
from instagram_manager.schemas.service_schemas import CommentImportSchema

logger = logging.getLogger(__name__)


class CommentService(BaseService[InstagramComment, InstagramCommentRepository]):
    """Service для управления Instagram комментариями."""
    
    @property
    def entity_name(self) -> str:
        return "InstagramComment"
    
    def __init__(self):
        # Инициализируем базовый класс с репозиторием
        super().__init__(InstagramCommentRepository())
        
        # Дополнительные зависимости
        self.client = BrightDataClient()
        self.comment_handler = CommentHandler()
        
    def import_comments_for_post(
        self,
        post: InstagramPost,
        limit: int | None = None,
        include_replies: bool = True,
        min_likes: int | None = None,
        force_update: bool = False
    ) -> list[InstagramComment]:
        """
        Импортировать комментарии для поста.
        
        Args:
            post: Пост Instagram
            limit: Максимальное количество комментариев
            include_replies: Включать ответы на комментарии
            min_likes: Минимальное количество лайков
            force_update: Обновить существующие комментарии
            
        Returns:
            Список импортированных комментариев
        """
        # Validate input parameters
        _ = CommentImportSchema(
            limit=limit,
            include_replies=include_replies,
            min_likes=min_likes,
            force_update=force_update
        )
        
        try:
            logger.info(f"Importing comments for post {post.post_id}")
            
            # Получаем комментарии через API
            comments_data = self.client.get_post_comments(
                post_url=post.post_url,
                skip_validation_errors=True,
            )
            
            if not comments_data:
                logger.warning(f"No comments found for post {post.post_id}")
                return []
            
            logger.info(f"Found {len(comments_data)} comments for post {post.post_id}")
            
            # Фильтруем по лимитам
            if limit:
                comments_data = comments_data[:limit]
                
            # Фильтруем по минимальным лайкам
            if min_likes:
                comments_data = [c for c in comments_data if c.like_count >= min_likes]
            
            # Обрабатываем комментарии
            imported_comments = []
            
            with transaction.atomic():
                logger.debug(
                    f"[TRANSACTION START] Starting atomic transaction for importing {len(comments_data)} comments "
                    f"for post {post.shortcode}"
                )
                
                # Сначала обрабатываем комментарии верхнего уровня
                top_level_count = 0
                for comment_response in comments_data:
                    if comment_response.parent_comment_id:
                        continue
                        
                    comment = self._process_comment(
                        comment_response=comment_response,
                        post=post,
                        force_update=force_update
                    )
                    
                    if comment:
                        imported_comments.append(comment)
                        top_level_count += 1
                
                # Затем обрабатываем ответы
                reply_count = 0
                if include_replies:
                    for comment_response in comments_data:
                        if not comment_response.parent_comment_id:
                            continue
                            
                        comment = self._process_comment(
                            comment_response=comment_response,
                            post=post,
                            force_update=force_update
                        )
                        
                        if comment:
                            imported_comments.append(comment)
                            reply_count += 1
                
                logger.debug(
                    f"[TRANSACTION END] Successfully imported {len(imported_comments)} comments "
                    f"({top_level_count} top-level, {reply_count} replies) in transaction"
                )
            
            # Обновляем счетчик комментариев в посте
            post.comment_count = len(self.repository.filter_by_post(post))
            post.save(update_fields=["comment_count"])
            
            logger.info(f"Successfully imported {len(imported_comments)} comments for post {post.post_id}")
            
        except Exception:
            logger.exception(f"Error importing comments for post {post.post_id}")
            raise
        else:
            return imported_comments
    
    def _process_comment(
        self,
        comment_response: InstagramCommentResponse,
        post: InstagramPost,
        force_update: bool = False
    ) -> InstagramComment | None:
        """
        Обработать данные комментария.
        
        Args:
            comment_response: Данные комментария от API
            post: Пост
            force_update: Обновить существующий комментарий
            
        Returns:
            Созданный или обновленный комментарий
        """
        try:
            # Проверяем существующий комментарий
            existing = self.repository.get_by_external_id(comment_response.comment_id)
            
            if existing and not force_update:
                logger.debug(f"Comment {comment_response.comment_id} already exists, skipping")
                return cast(InstagramComment | None, existing)
            
            # Обрабатываем иерархию
            reply_to = None
            if comment_response.parent_comment_id:
                reply_to = self.repository.get_by_external_id(comment_response.parent_comment_id)
                if not reply_to:
                    logger.warning(
                        f"Parent comment {comment_response.parent_comment_id} not found "
                        f"for comment {comment_response.comment_id}"
                    )
                    return None
            
            # Получаем все данные из схемы, включая дополнительные поля от BrightData
            all_data = comment_response.model_dump(exclude_unset=True, by_alias=False)
            
            # Подготавливаем основные данные для модели
            comment_data = {
                "external_id": comment_response.comment_id,
                "post": post,
                "author_username": comment_response.author_username,
                "author_external_id": comment_response.user_id or comment_response.comment_id,  # Use comment_id as fallback
                "author_profile_pic": comment_response.user_profile_pic_url,
                "author_is_verified": comment_response.is_verified_author,
                "text": comment_response.text,
                "like_count": comment_response.like_count,
                "reply_to": reply_to,
                "is_pinned": comment_response.is_pinned,
                "is_hidden": comment_response.is_hidden,
                "commented_at": comment_response.date_created,
            }
            
            # Извлекаем все дополнительные поля для сохранения в raw_data
            # Исключаем поля, которые уже есть в comment_data
            known_fields = {
                "comment_id", "external_id", "post", "author_username", "author_external_id",
                "user_id", "user_profile_pic_url", "is_verified_author", "text", 
                "like_count", "reply_to", "parent_comment_id", "is_pinned", "is_hidden", 
                "date_created", "commented_at"
            }
            
            extra_fields = {k: v for k, v in all_data.items() 
                           if k not in known_fields and v is not None}
            
            # Добавляем дополнительные данные в raw_data
            if extra_fields:
                comment_data["raw_data"] = extra_fields
            
            # Создаем или обновляем комментарий
            if existing:
                for key, value in comment_data.items():
                    if key == "raw_data":
                        # Объединяем существующие raw_data с новыми
                        existing_raw = getattr(existing, "raw_data", {}) or {}
                        if isinstance(value, dict):
                            existing_raw.update(value)
                        setattr(existing, key, existing_raw)
                    else:
                        setattr(existing, key, value)
                existing.save()
                return cast(InstagramComment | None, existing)
            else:
                return self.repository.create(**comment_data)
                
        except Exception:
            logger.exception(f"Error processing comment {comment_response.comment_id}")
            return None
    
    def import_comments_batch(
        self,
        posts: list[InstagramPost],
        max_comments_per_post: int = 100,
        include_replies: bool = True
    ) -> dict[str, list[InstagramComment]]:
        """
        Массовый импорт комментариев для нескольких постов.
        
        Args:
            posts: Список постов
            max_comments_per_post: Максимум комментариев на пост
            include_replies: Включать ответы
            
        Returns:
            Словарь {post_id: список комментариев}
        """
        results = {}
        
        for post in posts:
            try:
                comments = self.import_comments_for_post(
                    post=post,
                    limit=max_comments_per_post,
                    include_replies=include_replies
                )
                results[post.post_id] = comments
                
            except Exception:
                logger.exception(f"Error importing comments for post {post.post_id}")
                results[post.post_id] = []
                
        return results
    
    def build_comment_tree(self, post: InstagramPost) -> list[dict[str, Any]]:
        """
        Построить дерево комментариев для поста.
        
        Args:
            post: Пост
            
        Returns:
            Дерево комментариев
        """
        return self.repository.get_comment_tree(post)
    
    def get_comment_analytics(self, post: InstagramPost) -> dict[str, Any]:
        """
        Получить аналитику по комментариям поста.
        
        Args:
            post: Пост
            
        Returns:
            Словарь с аналитикой
        """
        # Базовая статистика
        stats = self.repository.get_comment_stats(post)
        
        # Дополнительная аналитика
        all_comments = self.repository.filter_by_post(post)
        
        if all_comments:
            # Временной анализ
            comments_by_hour: dict[int, int] = {}
            for comment in all_comments:
                hour = comment.commented_at.hour
                comments_by_hour[hour] = comments_by_hour.get(hour, 0) + 1
            
            stats["comments_by_hour"] = comments_by_hour
            
            # Анализ вовлеченности
            engagement_comments = self.repository.get_engagement_comments(post)
            stats["engagement_rate"] = len(engagement_comments) / len(all_comments) * 100
            
            # Популярные комментарии
            popular_comments = self.repository.get_popular_comments(post, min_likes=10)
            stats["popular_comments"] = [
                {
                    "author": c.author_username,
                    "text": c.text[:100],
                    "likes": c.like_count
                }
                for c in popular_comments[:5]
            ]
            
            # Закрепленные комментарии
            pinned = self.repository.get_pinned_comments(post)
            stats["pinned_comments"] = len(pinned)
            
        return stats
    
    def update_comments(self, post: InstagramPost) -> dict[str, int]:
        """
        Обновить существующие комментарии поста.
        
        Args:
            post: Пост
            
        Returns:
            Словарь с количеством обновленных и новых комментариев
        """
        try:
            # Получаем текущие комментарии
            existing_comments = {
                c.comment_id: c
                for c in self.repository.filter_by_post(post, include_hidden=True)
            }
            
            # Получаем свежие данные
            comments_data = self.client.get_post_comments(post_url=post.post_url)
            
            updated_count = 0
            new_count = 0
            
            with transaction.atomic():
                logger.debug(
                    f"[TRANSACTION START] Starting atomic transaction for updating {len(comments_data)} comments "
                    f"for post {post.shortcode}"
                )
                
                for comment_response in comments_data:
                    if comment_response.comment_id in existing_comments:
                        # Обновляем существующий
                        comment = existing_comments[comment_response.comment_id]
                        
                        # Проверяем изменения
                        if (comment.like_count != comment_response.like_count or
                            comment.text != comment_response.text or
                            comment.is_hidden != comment_response.is_hidden):
                            
                            comment.like_count = comment_response.like_count
                            comment.text = comment_response.text
                            comment.is_hidden = comment_response.is_hidden
                            comment.save()
                            updated_count += 1
                    else:
                        # Создаем новый
                        self._process_comment(comment_response, post)
                        new_count += 1
                
                logger.debug(
                    f"[TRANSACTION END] Transaction completed. Updated {updated_count} comments, "
                    f"created {new_count} new comments"
                )
            
            return {
                "updated": updated_count,
                "new": new_count,
                "total": len(comments_data)
            }
            
        except Exception:
            logger.exception(f"Error updating comments for post {post.post_id}")
            raise
    
    def delete_hidden_comments(self, post: InstagramPost) -> int:
        """
        Удалить скрытые комментарии поста.
        
        Args:
            post: Пост
            
        Returns:
            Количество удаленных комментариев
        """
        hidden_comments = self.repository.filter(post=post, is_hidden=True)
        count = hidden_comments.count()
        hidden_comments.delete()
        
        logger.info(f"Deleted {count} hidden comments for post {post.post_id}")
        return count