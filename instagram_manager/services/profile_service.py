import logging
from typing import Any, cast

from django.utils import timezone

from core.exceptions import DataValidationError
from core.services.base import BaseService
from instagram_manager.instagram_api import BrightDataClient
from instagram_manager.instagram_api.data_handlers.profile_handler import <PERSON><PERSON>andler
from instagram_manager.models import InstagramProfile, InstagramScrapingTask
from instagram_manager.repositories import ProfileRepository, TaskRepository
from instagram_manager.schemas.profile_schemas import ProfileCreateSchema, ProfileUpdateSchema

logger = logging.getLogger(__name__)


class ProfileService(BaseService[InstagramProfile, ProfileRepository]):
    """Сервис для работы с профилями Instagram"""
    
    @property
    def entity_name(self) -> str:
        return "InstagramProfile"
    
    def __init__(self):
        # Инициализируем базовый класс с репозиторием
        super().__init__(ProfileRepository())
        
        # Дополнительные зависимости
        self.client = BrightDataClient()
        self.api_client = self.client  # Алиас для совместимости с тестами
        self.cache = None  # Для совместимости с тестами
        self.task_repository = TaskRepository()
        
    def import_profile(self, username: str, import_posts: bool = False, 
                      posts_limit: int | None = None, import_followers: bool = False, 
                      skip_media_download: bool = False,
                      task: InstagramScrapingTask | None = None) -> InstagramProfile:
        """Импорт профиля с опциональным импортом постов и подписчиков"""
        
        # Если задача не передана, пытаемся найти существующую
        if not task:
            task = self.task_repository.filter(
                task_type="profile",
                target_identifier=username,
                status__in=["pending", "in_progress"]
            ).order_by("-created_at").first()
        
        # Обновляем статус задачи
        if task:
            task.status = "in_progress"
            task.started_at = timezone.now()
            task.save()
        
        try:
            # Получаем данные профиля (уже валидированные через pydantic)
            logger.info(f"Fetching profile data for {username} with posts_limit={posts_limit}")
            profile_data = self.client.get_profile(username)
            
            # Данные уже валидированы pydantic, просто обрабатываем
            profile = ProfileHandler.process_profile_data(profile_data)
            
            # Обновляем задачу
            if task:
                task.status = "completed"
                task.items_scraped = 1
                task.completed_at = timezone.now()
                task.save()
            
            # Импортируем посты если нужно
            if import_posts:
                try:
                    from django.conf import settings

                    from instagram_manager.services.post_service import PostService
                    
                    post_service = PostService()
                    
                    # Создаем отдельную задачу для импорта постов
                    posts_task = self.task_repository.create(
                        task_type="posts",
                        target_identifier=profile.username,
                        brightdata_dataset_id=settings.BRIGHTDATA_DATASETS["instagram_posts"],
                        skip_media_download=skip_media_download,
                        status="in_progress",
                        started_at=timezone.now()
                    )
                    
                    logger.info(f"Starting post import for profile {profile.username} with limit={posts_limit}")
                    
                    # Импортируем посты с указанным лимитом
                    imported_posts = post_service.import_posts_for_profile(
                        profile=profile,
                        limit=posts_limit,
                        task=posts_task
                    )
                    
                    logger.info(f"Successfully imported {len(imported_posts)} posts for profile {profile.username}")
                    
                except Exception as e:
                    logger.exception(f"Failed to import posts for profile {profile.username}: {e!s}")
                    # Не прерываем выполнение - импорт профиля прошел успешно
                    # Можно добавить уведомление пользователю о частичном успехе
                
            # Импортируем подписчиков если нужно
            if import_followers:
                # Note: Followers import requires BrightData API upgrade
                # Implementation will use InstagramFollowerRepository when available
                logger.warning("Followers import not yet implemented")
            
            return profile
            
        except Exception as e:
            # Обновляем задачу при ошибке
            if task:
                task.status = "failed"
                task.error_message = str(e)
                task.completed_at = timezone.now()
                task.save()
            
            logger.exception(f"Failed to import profile {username}: {e!s}")
            raise
            
    def update_profile(self, profile: InstagramProfile) -> InstagramProfile:
        """Обновление данных существующего профиля"""
        return cast(InstagramProfile, self.import_profile(profile.username))
        
    def get_profile_stats(self, profile: InstagramProfile) -> dict:
        """Получение статистики профиля"""
        # Используем репозиторий для получения постов
        from instagram_manager.repositories import PostRepository
        post_repository = PostRepository()
        posts = post_repository.filter_by_profile(profile)
        
        total_likes = sum(post.like_count for post in posts)
        total_comments = sum(post.comment_count for post in posts)
        total_views = sum(post.view_count or 0 for post in posts if post.post_type == "video")
        
        posts_count = len(posts)
        engagement_rate = 0.0
        if profile.follower_count > 0 and posts_count > 0:
            avg_engagement = (total_likes + total_comments) / posts_count
            engagement_rate = (avg_engagement / profile.follower_count) * 100
            
        return {
            "total_posts": posts_count,
            "total_likes": total_likes,
            "total_comments": total_comments,
            "total_views": total_views,
            "engagement_rate": round(engagement_rate, 2),
            "avg_likes_per_post": round(total_likes / posts_count, 0) if posts_count > 0 else 0,
            "avg_comments_per_post": round(total_comments / posts_count, 0) if posts_count > 0 else 0,
        }
        
    def search_profiles(self, query: str) -> list[InstagramProfile]:
        """Поиск профилей по username или имени"""
        return list(self.repository.search(query, search_fields=["username", "full_name"]))
    
    def validate_create_data(self, data: dict[str, Any]):
        """Валидация данных перед созданием профиля"""
        schema = ProfileCreateSchema(**data)
        # Проверяем уникальность username
        if self.repository.get_by_username(schema.username):
            raise DataValidationError("Username already exists", field="username")
        if schema.profile_id and self.repository.get_by_profile_id(schema.profile_id):
            raise DataValidationError("Profile ID already exists", field="profile_id")
    
    def validate_update_data(self, entity: InstagramProfile, data: dict[str, Any]):
        """Валидация данных перед обновлением профиля"""
        ProfileUpdateSchema(**data)
        # Проверяем уникальность username если он изменяется
        if "username" in data and data["username"] != entity.username:
            if self.repository.get_by_username(data["username"]):
                raise DataValidationError("Username already exists", field="username")
    
    def soft_delete(self, profile_id: int) -> InstagramProfile:
        """Мягкое удаление профиля"""
        success = self.repository.soft_delete(profile_id)
        if success:
            return self.repository.get_by_id(profile_id)
        raise ValueError(f"Profile with id {profile_id} not found")
    
    def apply_list_filters(self, queryset, filters: dict[str, Any]):
        """Применяет дополнительные фильтры к queryset"""
        # Обеспечиваем сортировку для стабильной пагинации
        if not queryset.ordered:
            queryset = queryset.order_by("-created_at", "id")
        return queryset
    
    def get_profile_with_stats(self, profile_id: int) -> dict:
        """Получение профиля со статистикой"""
        profile = self.repository.get_by_id(profile_id)
        if not profile:
            raise InstagramProfile.DoesNotExist(f"Profile with id {profile_id} not found")
        stats = self.get_profile_stats(profile)
        
        return {
            "profile": profile,
            "stats": stats
        }
    
    def bulk_create(self, profiles_data: list[dict]) -> list[InstagramProfile]:
        """Массовое создание профилей"""
        # Преобразуем словари в экземпляры модели
        profiles = [InstagramProfile(**data) for data in profiles_data]
        return self.repository.bulk_create(profiles)