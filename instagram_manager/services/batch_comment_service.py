"""
Service for batch processing Instagram comments for multiple posts.
Handles efficient data fetching in a single request to BrightData.
"""
import logging
from typing import Any

from django.db import transaction
from django.utils import timezone

from instagram_manager.instagram_api import BrightDataClient
from instagram_manager.instagram_api.data_handlers.comment_handler import Comment<PERSON>and<PERSON>
from instagram_manager.models import InstagramComment, InstagramPost, InstagramScrapingTask
from instagram_manager.repositories.comment_repository import InstagramCommentRepository

logger = logging.getLogger(__name__)


class BatchCommentService:
    """Service for batch importing comments for multiple Instagram posts"""
    
    def __init__(self, client: BrightDataClient):
        self.client = client
        self.comment_handler = CommentHandler()
        self.comment_repository = InstagramCommentRepository()
    
    def import_comments_batch(
        self,
        post_urls: list[str],
        limit_per_post: int = 100,
        include_replies: bool = True,
        min_likes: int | None = None,
        update_existing: bool = False,
        task: InstagramScrapingTask | None = None,
    ) -> dict[str, dict[str, Any]]:
        """
        Import comments for multiple posts in a single batch request.
        
        Args:
            post_urls: List of Instagram post URLs
            limit_per_post: Maximum comments per post
            include_replies: Include comment replies
            min_likes: Minimum likes threshold
            update_existing: Update existing comments
            task: Scraping task for tracking
            
        Returns:
            Dictionary with results for each post URL
        """
        results: dict[str, dict[str, Any]] = {}
        statistics: dict[str, Any] = {
            "total_comments_received": 0,
            "total_comments_imported": 0,
            "comments_filtered": 0,
            "posts_with_comments": 0,
            "posts_without_comments": 0,
        }
        
        try:
            logger.info(f"[BATCH COMMENTS] Starting batch comment import for {len(post_urls)} posts")
            logger.info(f"[BATCH COMMENTS] Post URLs sample: {post_urls[:3]}...")
            
            # Проверяем существование постов в БД перед импортом
            missing_posts = []
            existing_posts = {}
            logger.info("[BATCH COMMENTS] Checking if posts exist in database...")
            
            for post_url in post_urls:
                post = self._get_post_by_url(post_url)
                if post:
                    existing_posts[post_url] = post
                else:
                    missing_posts.append(post_url)
            
            logger.info(
                f"[BATCH COMMENTS] Post check result: {len(existing_posts)} posts found, "
                f"{len(missing_posts)} posts missing"
            )
            
            if missing_posts:
                logger.warning(
                    f"[BATCH COMMENTS] Missing posts in database: {missing_posts[:5]}... "
                    f"(showing first 5 of {len(missing_posts)})"
                )
            
            # Если нет постов в БД, возвращаем ошибку для всех
            if not existing_posts:
                logger.error("[BATCH COMMENTS] No posts found in database. Cannot import comments.")
                for post_url in post_urls:
                    results[post_url] = {
                        "success": False,
                        "error": "Post not found in database",
                        "comment_count": 0,
                    }
                return results
            
            # Получаем все комментарии одним запросом
            logger.info("[BATCH COMMENTS] Fetching comments from BrightData API with single batch request...")
            comments_by_post = self.client.get_batch_comments(
                post_urls=post_urls,
                skip_validation_errors=True,
            )
            logger.info(f"[BATCH COMMENTS] Received response for {len(comments_by_post)} posts")
            
            # Считаем общее количество полученных комментариев
            for _post_url, comments in comments_by_post.items():
                statistics["total_comments_received"] += len(comments)
                if comments:
                    statistics["posts_with_comments"] += 1
                else:
                    statistics["posts_without_comments"] += 1
            
            logger.info(
                f"Received {statistics['total_comments_received']} comments "
                f"for {statistics['posts_with_comments']} posts"
            )
            
            # Обновляем задачу если передана
            if task:
                task.total_items_received = statistics["total_comments_received"]
                task.save(update_fields=["total_items_received"])
            
            # Обрабатываем комментарии для каждого поста
            for post_url in post_urls:
                try:
                    # Получаем комментарии для этого поста
                    post_comments = comments_by_post.get(post_url, [])
                    logger.info(f"Processing {len(post_comments)} comments for {post_url}")
                    
                    # Используем уже найденный пост
                    post = existing_posts.get(post_url)
                    if not post:
                        logger.warning(f"Post not found for URL: {post_url} (should have been caught earlier)")
                        results[post_url] = {
                            "success": False,
                            "error": "Post not found in database",
                            "comment_count": 0,
                        }
                        continue
                    
                    # Фильтруем комментарии
                    filtered_comments = self._filter_comments(
                        comments=post_comments,
                        limit=limit_per_post,
                        min_likes=min_likes,
                        include_replies=include_replies,
                    )
                    
                    comments_filtered = len(post_comments) - len(filtered_comments)
                    statistics["comments_filtered"] += comments_filtered
                    
                    # Импортируем комментарии
                    logger.info(f"Importing {len(filtered_comments)} filtered comments for post {post.shortcode}")
                    imported_count = self._import_comments_for_post(
                        post=post,
                        comments=filtered_comments,
                        update_existing=update_existing,
                    )
                    logger.info(f"Actually imported {imported_count} comments for post {post.shortcode}")
                    
                    statistics["total_comments_imported"] += imported_count
                    
                    results[post_url] = {
                        "success": True,
                        "comment_count": imported_count,
                        "total_found": len(post_comments),
                        "filtered": comments_filtered,
                    }
                    
                    logger.info(
                        f"Imported {imported_count} comments for post {post.shortcode} "
                        f"(filtered {comments_filtered} of {len(post_comments)})"
                    )
                    
                except Exception as e:
                    logger.exception(f"Error processing comments for {post_url}")
                    results[post_url] = {
                        "success": False,
                        "error": str(e),
                        "comment_count": 0,
                    }
            
            # Добавляем информацию о пропущенных постах
            for missing_url in missing_posts:
                results[missing_url] = {
                    "success": False,
                    "error": "Post not found in database - please import posts first",
                    "comment_count": 0,
                }
            
            # Добавляем статистику в результаты
            statistics["posts_missing"] = len(missing_posts)
            statistics["posts_found"] = len(existing_posts)
            results["_statistics"] = statistics
            
            # Обновляем задачу с финальной статистикой
            if task:
                task.items_scraped = statistics["total_comments_imported"]
                task.items_filtered = statistics["comments_filtered"]
                task.status = "completed"
                task.completed_at = timezone.now()
                task.save()
            
            logger.info(
                f"Batch comment import completed: "
                f"{statistics['total_comments_imported']} comments imported, "
                f"{statistics['comments_filtered']} filtered, "
                f"{statistics['posts_found']} posts found, "
                f"{statistics['posts_missing']} posts missing"
            )
            
            return results
            
        except Exception as e:
            logger.exception("Error during batch comment import")
            
            if task:
                task.status = "failed"
                task.error_message = str(e)
                task.completed_at = timezone.now()
                task.save()
            
            # Возвращаем ошибку для всех постов
            for post_url in post_urls:
                if post_url not in results:
                    results[post_url] = {
                        "success": False,
                        "error": str(e),
                        "comment_count": 0,
                    }
            
            return results
    
    def _get_post_by_url(self, post_url: str) -> InstagramPost | None:
        """
        Find post by URL.
        
        Args:
            post_url: Instagram post URL
            
        Returns:
            InstagramPost instance or None
        """
        import re
        
        logger.info(f"[POST LOOKUP] Looking for post by URL: {post_url}")
        
        # Извлекаем shortcode из URL
        # Updated patterns to handle trailing slashes and query parameters properly
        patterns = [
            r"/p/([A-Za-z0-9_-]+)",     # Match /p/SHORTCODE with or without trailing content
            r"/reel/([A-Za-z0-9_-]+)",  # Match /reel/SHORTCODE
            r"/tv/([A-Za-z0-9_-]+)",    # Match /tv/SHORTCODE
        ]
        
        shortcode_found = None
        for pattern in patterns:
            match = re.search(pattern, post_url)
            if match:
                shortcode = match.group(1)
                shortcode_found = shortcode
                logger.info(f"[POST LOOKUP] Extracted shortcode: {shortcode} from URL: {post_url}")
                try:
                    post = InstagramPost.objects.get(shortcode=shortcode)
                    logger.info(f"[POST LOOKUP] Found post {post.id} with shortcode {shortcode}")
                    return post
                except InstagramPost.DoesNotExist:
                    logger.warning(f"[POST LOOKUP] Post with shortcode {shortcode} not found in database")
                    # Попробуем найти все посты и вывести их shortcodes для диагностики
                    all_posts = InstagramPost.objects.all().values_list("shortcode", flat=True)[:10]
                    logger.info(f"[POST LOOKUP] Sample of existing shortcodes in DB: {list(all_posts)}")
        
        if not shortcode_found:
            logger.error(f"[POST LOOKUP] Could not extract shortcode from URL: {post_url}")
        
        return None
    
    def _filter_comments(
        self,
        comments: list[Any],
        limit: int,
        min_likes: int | None,
        include_replies: bool,
    ) -> list[Any]:
        """
        Filter comments based on criteria.
        
        Args:
            comments: List of comment responses
            limit: Maximum number of comments
            min_likes: Minimum likes threshold
            include_replies: Include replies
            
        Returns:
            Filtered list of comments
        """
        filtered = []
        
        for comment in comments:
            # Фильтр по ответам
            if not include_replies and hasattr(comment, "parent_comment_id") and comment.parent_comment_id:
                continue
            
            # Фильтр по лайкам
            if min_likes is not None:
                # Use the correct attribute name for InstagramCommentResponse
                comment_likes = getattr(comment, "like_count", 0)
                if comment_likes < min_likes:
                    continue
            
            filtered.append(comment)
            
            # Проверяем лимит
            if len(filtered) >= limit:
                break
        
        return filtered
    
    def _import_comments_for_post(
        self,
        post: InstagramPost,
        comments: list[Any],
        update_existing: bool,
    ) -> int:
        """
        Import comments for a single post.
        
        Args:
            post: InstagramPost instance
            comments: List of comment responses
            update_existing: Update existing comments
            
        Returns:
            Number of imported comments
        """
        imported_count = 0
        
        logger.debug(f"Starting import for post {post.shortcode}, {len(comments)} comments")
        
        with transaction.atomic():
            logger.debug(
                f"[TRANSACTION START] Starting atomic transaction for importing {len(comments)} comments "
                f"for post {post.shortcode}"
            )
            
            # Сначала импортируем комментарии верхнего уровня
            parent_mapping = {}  # external_id -> InstagramComment
            
            for comment_response in comments:
                # Check if it has parent_comment_id attribute
                parent_id = getattr(comment_response, "parent_comment_id", None)
                if parent_id:
                    continue
                
                logger.debug(f"Processing top-level comment {getattr(comment_response, 'comment_id', 'unknown')}")
                comment = self._process_comment(
                    comment_response=comment_response,
                    post=post,
                    update_existing=update_existing,
                )
                
                if comment:
                    comment_id = getattr(comment_response, "comment_id", None)
                    if comment_id:
                        parent_mapping[comment_id] = comment
                    imported_count += 1
                    logger.debug(f"Imported top-level comment {comment.external_id}")
            
            # Затем импортируем ответы
            for comment_response in comments:
                parent_id = getattr(comment_response, "parent_comment_id", None)
                if not parent_id:
                    continue
                
                # Находим родительский комментарий
                parent_comment = parent_mapping.get(parent_id)
                
                logger.debug(f"Processing reply comment {getattr(comment_response, 'comment_id', 'unknown')} to parent {parent_id}")
                comment = self._process_comment(
                    comment_response=comment_response,
                    post=post,
                    parent_comment=parent_comment,
                    update_existing=update_existing,
                )
                
                if comment:
                    imported_count += 1
                    logger.debug(f"Imported reply comment {comment.external_id}")
            
            logger.debug(
                f"[TRANSACTION END] Successfully imported {imported_count} comments in transaction"
            )
        
        logger.debug(f"Finished import for post {post.shortcode}, imported {imported_count} comments")
        return imported_count
    
    def _process_comment(
        self,
        comment_response: Any,
        post: InstagramPost,
        parent_comment: InstagramComment | None = None,
        update_existing: bool = False,
    ) -> InstagramComment | None:
        """
        Process single comment response.
        
        Args:
            comment_response: Comment data from API
            post: InstagramPost instance
            parent_comment: Parent comment for replies
            update_existing: Update if exists
            
        Returns:
            InstagramComment instance or None
        """
        try:
            comment_id = getattr(comment_response, "comment_id", "unknown")
            logger.info(f"Processing comment {comment_id} for post {post.shortcode}")
            logger.debug(f"Comment data type: {type(comment_response)}")
            logger.debug(f"Comment attributes: {dir(comment_response) if hasattr(comment_response, '__dict__') else 'N/A'}")
            
            # Проверяем существование
            existing = None
            if not update_existing:
                existing = self.comment_repository.get_by_external_id(comment_id)
                if existing:
                    logger.info(f"Comment {comment_id} already exists, skipping")
                    return existing
            
            # Используем comment_handler для обработки
            comment = self.comment_handler.process_comment_data(
                comment_response=comment_response,
                post=post,
                parent_comment=parent_comment,
            )
            
            logger.info(f"Successfully processed comment {comment_id}")
            return comment
            
        except Exception as e:
            logger.exception(
                f"Error processing comment {getattr(comment_response, 'comment_id', 'unknown')}: {e}"
            )
            return None