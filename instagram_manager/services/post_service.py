import logging
from typing import cast

from django.conf import settings
from django.utils import timezone

from core.services.base import BaseService
from instagram_manager.instagram_api import BrightDataClient
from instagram_manager.instagram_api.data_handlers.post_handler import PostHandler
from instagram_manager.models import (
    InstagramPost,
    InstagramProfile,
    InstagramScrapingTask,
)
from instagram_manager.repositories import (
    PostRepository,
    ProfileRepository,
    TaskRepository,
)
from instagram_manager.schemas.brightdata import InstagramPostResponse
from instagram_manager.schemas.service_schemas import PostImportSchema

logger = logging.getLogger(__name__)


class PostService(BaseService[InstagramPost, PostRepository]):
    """Сервис для работы с постами Instagram"""
    
    @property
    def entity_name(self) -> str:
        return "InstagramPost"

    def __init__(self):
        # Инициализируем базовый класс с репозиторием
        super().__init__(PostRepository())
        
        # Дополнительные зависимости
        self.client = BrightDataClient()
        self.post_repository = self.repository  # Для совместимости
        self.profile_repository = ProfileRepository()
        self.task_repository = TaskRepository()
        self.post_handler = None  # Will be initialized when needed

    def import_posts_for_profile(
        self,
        profile: InstagramProfile,
        limit: int | None = None,
        import_comments: bool = False,
        import_comments_limit: int | None = 100,
        task: InstagramScrapingTask | None = None,
        start_date: str = "",
        end_date: str = "",
        post_types: list[str] | None = None,
    ) -> list[InstagramPost]:
        """Импорт постов для профиля

        Args:
            profile: Профиль Instagram
            limit: Ограничение количества постов (если не указано - загружаются все)
            import_comments: Импортировать ли комментарии
            import_comments_limit: Лимит комментариев на пост при импорте
            task: Задача скрапинга
            start_date: Начальная дата для фильтрации (формат: YYYY-MM-DD)
            end_date: Конечная дата для фильтрации (формат: YYYY-MM-DD)
            post_type: Тип постов для фильтрации (например: "image", "video", "reel")
        """
        # Validate input parameters
        validation_data = PostImportSchema(
            limit=limit,
            import_comments=import_comments,
            import_comments_limit=import_comments_limit,
            start_date=start_date,
            end_date=end_date,
            post_types=post_types
        )
        
        # Use validated data
        limit = validation_data.limit
        import_comments = validation_data.import_comments
        import_comments_limit = validation_data.import_comments_limit
        start_date = validation_data.start_date
        end_date = validation_data.end_date
        post_types = validation_data.post_types

        # Если задача не передана, создаем новую
        if not task:
            task = self.task_repository.create(
                task_type="posts",
                target_identifier=profile.username,
                brightdata_dataset_id=settings.BRIGHTDATA_DATASETS["instagram_posts"],
                status="in_progress",
                started_at=timezone.now(),
            )

        imported_posts = []

        try:
            # Получаем посты
            logger.info(f"Fetching posts for {profile.username} using discover_by_url")
            if start_date or end_date or post_types:
                logger.info(
                    f"Filters: start_date={start_date}, end_date={end_date}, post_types={post_types}"
                )

            # Используем новый метод который загружает ВСЕ посты через discover
            posts_data = self.client.get_posts(
                profile.username,
                start_date=start_date,
                end_date=end_date,
                post_types=post_types,
            )
            
            # Логируем результат
            if not posts_data:
                logger.warning(
                    f"No posts returned from BrightData for {profile.username}. "
                    f"Filters: start_date={start_date}, end_date={end_date}, post_types={post_types}"
                )
            else:
                logger.info(f"Received {len(posts_data)} posts from BrightData for {profile.username}")

            # Если указан limit, ограничиваем количество обрабатываемых постов
            if limit is not None and len(posts_data) > limit:
                logger.info(
                    f"Limiting results to {limit} posts out of {len(posts_data)} fetched"
                )
                posts_data = posts_data[:limit]

            # Initialize PostHandler if needed
            if not self.post_handler:
                # Get save_to_gcs setting from task or default to False
                save_to_gcs = False
                if task and hasattr(task, "save_media_to_gcs"):
                    save_to_gcs = task.save_media_to_gcs
                self.post_handler = PostHandler(save_to_gcs=save_to_gcs)
            
            # Обрабатываем каждый пост
            for post_data in posts_data:
                try:
                    post = self.post_handler.process_post_data(
                        post_data,
                        profile,
                        skip_media_download=task.skip_media_download if task else False,
                    )
                    imported_posts.append(post)

                    # Импортируем комментарии если нужно
                    # NOTE: This is disabled for batch imports to avoid individual requests
                    # Comments should be imported via BatchCommentService
                    if import_comments and post.comment_count > 0:
                        logger.info(
                            f"Importing comments for post {post.shortcode} "
                            f"(comment_count: {post.comment_count})"
                        )
                        self._import_comments_for_post(
                            post, limit=import_comments_limit
                        )

                except Exception as e:
                    logger.exception(f"Failed to process post: {e!s}")
                    continue

            # Обновляем задачу
            task.status = "completed"
            task.items_scraped = len(imported_posts)
            task.completed_at = timezone.now()
            task.save()

            logger.info(f"Successfully imported {len(imported_posts)} posts")
            return imported_posts

        except Exception as e:
            # Обновляем задачу при ошибке
            task.status = "failed"
            task.error_message = str(e)
            task.completed_at = timezone.now()
            task.save()

            logger.exception(f"Failed to import posts: {e!s}")
            raise

    def _import_comments_for_post(
        self, post: InstagramPost, limit: int | None = 100
    ):
        """Импорт комментариев для поста"""
        try:
            from instagram_manager.services.comment_service import CommentService

            comment_service = CommentService()
            comments = comment_service.import_comments_for_post(
                post=post, limit=limit, include_replies=True
            )

            logger.info(f"Imported {len(comments)} comments for post {post.shortcode}")

        except Exception as e:
            logger.exception(
                f"Failed to import comments for post {post.shortcode}: {e!s}"
            )

    def import_posts_by_username(
        self,
        username: str,
        limit: int | None = None,
        task: InstagramScrapingTask | None = None,
        start_date: str = "",
        end_date: str = "",
        post_types: list[str] | None = None,
    ) -> list[InstagramPost]:
        """Импорт постов по username

        Args:
            username: Имя пользователя Instagram
            limit: Ограничение количества постов (если не указано - загружаются все)
            task: Задача скрапинга
            start_date: Начальная дата для фильтрации (формат: YYYY-MM-DD)
            end_date: Конечная дата для фильтрации (формат: YYYY-MM-DD)
            post_type: Тип постов для фильтрации (например: "image", "video", "reel")
        """
        # Получаем или создаем профиль
        profile = self.profile_repository.get_by_username(username)
        if not profile:
            # Если профиля нет, сначала импортируем его
            from instagram_manager.services.profile_service import ProfileService

            profile_service = ProfileService()
            profile = profile_service.import_profile(username)

        # Импортируем посты для профиля
        return cast(list[InstagramPost], self.import_posts_for_profile(
            profile,
            limit=limit,
            task=task,
            start_date=start_date,
            end_date=end_date,
            post_types=post_types,
        ))


    def get_posts_by_hashtag(
        self,
        hashtag: str,
        limit: int = 20,
        task: InstagramScrapingTask | None = None,
    ) -> list[InstagramPost]:
        """Получение постов по хештегу"""

        # Если задача не передана, создаем новую
        if not task:
            task = self.task_repository.create(
                task_type="hashtag",
                target_identifier=hashtag,
                brightdata_dataset_id=settings.BRIGHTDATA_DATASETS["instagram_hashtag"],
                status="in_progress",
                started_at=timezone.now(),
            )

        imported_posts = []

        try:
            # Получаем посты по хештегу
            logger.info(f"Fetching posts for hashtag #{hashtag}")
            posts_data = self.client.get_hashtag_posts(hashtag, limit=limit)

            # Обрабатываем посты
            for post_data in posts_data:
                try:
                    # Сначала нужно получить или создать профиль
                    username = post_data.get("owner_username", "")
                    if username:
                        profile = self.profile_repository.get_by_username(username)
                        if not profile:
                            profile = self.profile_repository.create(
                                username=username,
                                profile_id=post_data.get("owner_id", ""),
                                full_name=post_data.get("owner_full_name", ""),
                            )

                        # Convert dict to InstagramPostResponse
                        post_response = InstagramPostResponse(**post_data)
                        
                        # Initialize PostHandler if needed
                        if not self.post_handler:
                            save_to_gcs = False
                            if task and hasattr(task, "save_media_to_gcs"):
                                save_to_gcs = task.save_media_to_gcs
                            self.post_handler = PostHandler(save_to_gcs=save_to_gcs)
                        
                        post = self.post_handler.process_post_data(
                            post_response,
                            profile,
                            skip_media_download=(
                                task.skip_media_download if task else False
                            ),
                        )
                        imported_posts.append(post)

                except Exception as e:
                    logger.exception(f"Failed to process hashtag post: {e!s}")
                    continue

            # Обновляем задачу
            task.status = "completed"
            task.items_scraped = len(imported_posts)
            task.completed_at = timezone.now()
            task.save()

            return imported_posts

        except Exception as e:
            task.status = "failed"
            task.error_message = str(e)
            task.completed_at = timezone.now()
            task.save()

            logger.exception(f"Failed to import posts by hashtag: {e!s}")
            raise


