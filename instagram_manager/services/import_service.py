"""
Unified import service for Instagram data.

This service provides a consistent interface for importing Instagram data
with support for both synchronous and asynchronous execution modes.
"""

import logging
from typing import Any

from celery.result import AsyncResult
from django.conf import settings

from core.models import TaskResult

logger = logging.getLogger(__name__)


class ImportService:
    """
    Unified service for importing Instagram data with sync/async support.
    
    By default, all imports run asynchronously via Celery unless:
    1. force_sync=True is passed explicitly
    2. INSTAGRAM_IMPORTS_FORCE_SYNC=True in settings (for debugging)
    
    Example:
        # Async by default
        result = ImportService.import_profile("username")
        
        # Force sync mode
        result = ImportService.import_profile("username", force_sync=True)
    """
    
    @classmethod
    def import_profile(
        cls,
        username: str,
        force_sync: bool | None = None,
        **kwargs
    ) -> AsyncResult | dict[str, Any]:
        """
        Import Instagram profile.
        
        Args:
            username: Instagram username to import
            force_sync: Force synchronous execution (None = use settings)
            **kwargs: Additional parameters for the task
            
        Returns:
            AsyncResult for async mode or dict with results for sync mode
        """
        if cls._should_run_async(force_sync):
            logger.info(f"Starting async profile import for @{username}")
            from instagram_manager.tasks import import_profile_async
            async_result: AsyncResult[Any] = import_profile_async.delay(username, **kwargs)
            return async_result
        else:
            logger.info(f"Starting sync profile import for @{username}")
            from instagram_manager.tasks.import_tasks import ImportInstagramProfileTask
            task = ImportInstagramProfileTask()
            sync_result: dict[str, Any] = task.execute_task(username=username, **kwargs)
            return sync_result
    
    @classmethod
    def import_posts(
        cls,
        profile_id: int,
        force_sync: bool | None = None,
        **kwargs
    ) -> AsyncResult | dict[str, Any]:
        """
        Import posts for a profile.
        
        Args:
            profile_id: Profile ID to import posts for
            force_sync: Force synchronous execution
            **kwargs: Additional parameters
            
        Returns:
            AsyncResult or dict with results
        """
        if cls._should_run_async(force_sync):
            logger.info(f"Starting async posts import for profile ID {profile_id}")
            from instagram_manager.tasks import import_posts_async
            async_result: AsyncResult[Any] = import_posts_async.delay(profile_id, **kwargs)
            return async_result
        else:
            logger.info(f"Starting sync posts import for profile ID {profile_id}")
            from instagram_manager.tasks.import_tasks import ImportInstagramPostsTask
            task = ImportInstagramPostsTask()
            sync_result: dict[str, Any] = task.execute_task(profile_id=profile_id, **kwargs)
            return sync_result
    
    @classmethod
    def import_batch_posts(
        cls,
        usernames: list[str],
        force_sync: bool | None = None,
        **kwargs
    ) -> AsyncResult | dict[str, Any]:
        """
        Batch import posts for multiple profiles.
        
        Args:
            usernames: List of usernames to import posts for
            force_sync: Force synchronous execution
            **kwargs: Additional parameters
            
        Returns:
            AsyncResult or dict with results
        """
        if cls._should_run_async(force_sync):
            logger.info(f"Starting async batch posts import for {len(usernames)} profiles")
            from instagram_manager.tasks import import_batch_posts_async
            async_result: AsyncResult[Any] = import_batch_posts_async.delay(usernames, **kwargs)
            return async_result
        else:
            logger.info(f"Starting sync batch posts import for {len(usernames)} profiles")
            # Log if this is a subtask execution
            if "subtask_id" in kwargs:
                logger.info(f"[SUBTASK LIFECYCLE] Executing as subtask ID: {kwargs.get('subtask_id')}")
            from instagram_manager.tasks.import_tasks import ImportBatchPostsTask
            task = ImportBatchPostsTask()
            sync_result: dict[str, Any] = task.execute_task(usernames=usernames, **kwargs)
            return sync_result
    
    @classmethod
    def import_batch_profiles(
        cls,
        usernames: list[str],
        force_sync: bool | None = None,
        **kwargs
    ) -> AsyncResult | dict[str, Any]:
        """
        Batch import profiles.
        
        Args:
            usernames: List of usernames to import
            force_sync: Force synchronous execution
            **kwargs: Additional parameters
            
        Returns:
            AsyncResult or dict with results
        """
        if cls._should_run_async(force_sync):
            logger.info(f"Starting async batch profiles import for {len(usernames)} profiles")
            from instagram_manager.tasks import import_batch_profiles_async
            async_result: AsyncResult[Any] = import_batch_profiles_async.delay(usernames, **kwargs)
            return async_result
        else:
            logger.info(f"Starting sync batch profiles import for {len(usernames)} profiles")
            from instagram_manager.tasks.import_tasks import ImportBatchProfilesTask
            task = ImportBatchProfilesTask()
            sync_result: dict[str, Any] = task.execute_task(usernames=usernames, **kwargs)
            return sync_result
    
    @classmethod
    def import_comments(
        cls,
        post_ids: list[int],
        force_sync: bool | None = None,
        **kwargs
    ) -> AsyncResult | dict[str, Any]:
        """
        Import comments for posts.
        
        Args:
            post_ids: List of post IDs to import comments for
            force_sync: Force synchronous execution
            **kwargs: Additional parameters
            
        Returns:
            AsyncResult or dict with results
        """
        if cls._should_run_async(force_sync):
            logger.info(f"Starting async comments import for {len(post_ids)} posts")
            from instagram_manager.tasks import import_comments_async
            async_result: AsyncResult[Any] = import_comments_async.delay(post_ids, **kwargs)
            return async_result
        else:
            logger.info(f"Starting sync comments import for {len(post_ids)} posts")
            from instagram_manager.tasks.import_tasks import ImportInstagramCommentsTask
            task = ImportInstagramCommentsTask()
            sync_result: dict[str, Any] = task.execute_task(post_ids=post_ids, **kwargs)
            return sync_result
    
    @classmethod
    def import_batch_comments(
        cls,
        post_urls: list[str],
        force_sync: bool | None = None,
        **kwargs
    ) -> AsyncResult | dict[str, Any]:
        """
        Batch import comments for multiple posts.
        
        Args:
            post_urls: List of post URLs to import comments for
            force_sync: Force synchronous execution
            **kwargs: Additional parameters
            
        Returns:
            AsyncResult or dict with results
        """
        if cls._should_run_async(force_sync):
            logger.info(f"Starting async batch comments import for {len(post_urls)} posts")
            from instagram_manager.tasks import import_batch_comments_async
            async_result: AsyncResult[Any] = import_batch_comments_async.delay(post_urls, **kwargs)
            return async_result
        else:
            logger.info(f"Starting sync batch comments import for {len(post_urls)} posts")
            from instagram_manager.tasks.import_tasks import ImportBatchCommentsTask
            task = ImportBatchCommentsTask()
            sync_result: dict[str, Any] = task.execute_task(post_urls=post_urls, **kwargs)
            return sync_result
    
    @staticmethod
    def _should_run_async(force_sync: bool | None = None) -> bool:
        """
        Determine if task should run asynchronously.
        
        Args:
            force_sync: Explicit sync/async override
            
        Returns:
            True if should run async, False for sync
        """
        # Explicit override takes precedence
        if force_sync is not None:
            return not force_sync
        
        # Check global setting
        if settings.INSTAGRAM_IMPORTS_FORCE_SYNC:
            logger.debug("Running in sync mode due to INSTAGRAM_IMPORTS_FORCE_SYNC=True")
            return False
        
        # Default behavior from settings
        return bool(settings.INSTAGRAM_IMPORTS_ASYNC_BY_DEFAULT)
    
    @classmethod
    def get_task_status(cls, task_id: str) -> dict[str, Any]:
        """
        Get status of an async task.
        
        Args:
            task_id: Celery task ID or TaskResult task_id
            
        Returns:
            Dict with task status information
        """
        # Try to find TaskResult first
        try:
            task_result = TaskResult.objects.get(task_id=task_id)
            task_result.sync_with_celery()
            
            return {
                "task_id": task_result.task_id,
                "status": task_result.status,
                "progress": task_result.progress_percentage,
                "message": task_result.progress_message,
                "result": task_result.result if task_result.status == "completed" else None,
                "error": task_result.error_message if task_result.status == "failed" else None,
            }
        except TaskResult.DoesNotExist:
            pass
        
        # Try as Celery task ID
        try:
            task_result = TaskResult.objects.get(celery_task_id=task_id)
            task_result.sync_with_celery()
            
            return {
                "task_id": task_result.task_id,
                "status": task_result.status,
                "progress": task_result.progress_percentage,
                "message": task_result.progress_message,
                "result": task_result.result if task_result.status == "completed" else None,
                "error": task_result.error_message if task_result.status == "failed" else None,
            }
        except TaskResult.DoesNotExist:
            pass
        
        # Direct Celery check
        result: AsyncResult[Any] = AsyncResult(task_id)
        return {
            "task_id": task_id,
            "status": result.status,
            "ready": result.ready(),
            "successful": result.successful() if result.ready() else None,
            "result": result.result if result.successful() else None,
            "error": str(result.info) if result.failed() else None,
        }
    
    @classmethod
    def wait_for_task(cls, task_id: str, timeout: int = 300) -> dict[str, Any]:
        """
        Wait for an async task to complete.
        
        Args:
            task_id: Task ID to wait for
            timeout: Maximum seconds to wait
            
        Returns:
            Final task result
        """
        import time
        
        start_time = time.time()
        while time.time() - start_time < timeout:
            status = cls.get_task_status(task_id)
            
            if status.get("status") in ["completed", "failed", "cancelled"]:
                return status
            
            time.sleep(1)
        
        return {
            "task_id": task_id,
            "status": "timeout",
            "error": f"Task did not complete within {timeout} seconds"
        }