import logging
import os

from django.conf import settings

from core.services.base import MediaService as BaseMediaService
from instagram_manager.instagram_api.data_handlers.media_handler import MediaHandler
from instagram_manager.models import InstagramMedia, InstagramPost, InstagramProfile
from instagram_manager.repositories import MediaRepository

logger = logging.getLogger(__name__)


class MediaService(BaseMediaService):
    """Сервис для работы с медиафайлами Instagram"""
    
    def __init__(self):
        # Инициализируем базовый класс
        super().__init__(storage_path=os.path.join(settings.MEDIA_ROOT, "instagram", "media"))
        
        # Дополнительные зависимости
        self.repository = MediaRepository()
        
        # Create MediaHandler instance (without GCS for now)
        self._media_handler = MediaHandler(save_to_gcs=False)
    
    def download_media_for_profile(self, profile: InstagramProfile, 
                                 download_photos: bool = True,
                                 download_videos: bool = True,
                                 download_thumbnails: bool = True) -> dict[str, int]:
        """Загрузка всех медиафайлов для профиля"""
        
        media_query = self.repository.filter(post__profile=profile, is_downloaded=False)
        
        # Фильтруем по типу медиа
        if download_photos and not download_videos:
            media_query = media_query.filter(media_type="photo")
        elif download_videos and not download_photos:
            media_query = media_query.filter(media_type="video")
            
        stats = {
            "downloaded": 0,
            "failed": 0,
            "skipped": 0
        }
        
        for media in media_query:
            try:
                # Загружаем основной файл
                if self._media_handler.download_media(media):
                    stats["downloaded"] += 1
                    
                    # Загружаем thumbnail для видео
                    if download_thumbnails and media.media_type == "video":
                        self._media_handler.download_thumbnail(media)
                else:
                    stats["failed"] += 1
                    
            except Exception as e:
                logger.exception(f"Failed to download media {media.media_id}: {e!s}")
                stats["failed"] += 1
                
        return stats
        
    def download_media_for_post(self, post: InstagramPost) -> dict[str, int]:
        """Загрузка медиафайлов для конкретного поста"""
        
        stats = {
            "downloaded": 0,
            "failed": 0
        }
        
        for media in post.media.filter(is_downloaded=False):
            try:
                if self._media_handler.download_media(media):
                    stats["downloaded"] += 1
                    
                    # Загружаем thumbnail для видео
                    if media.media_type == "video":
                        self._media_handler.download_thumbnail(media)
                else:
                    stats["failed"] += 1
                    
            except Exception as e:
                logger.exception(f"Failed to download media {media.media_id}: {e!s}")
                stats["failed"] += 1
                
        return stats
        
    def get_media_stats(self, profile: InstagramProfile | None = None) -> dict:
        """Получение статистики по медиафайлам"""
        
        if profile:
            media_query = self.repository.filter(post__profile=profile)
        else:
            media_query = self.repository.all()
            
        total_media = media_query.count()
        downloaded_media = media_query.filter(is_downloaded=True).count()
        
        # Подсчет по типам
        photos = media_query.filter(media_type="photo").count()
        videos = media_query.filter(media_type="video").count()
        
        # Подсчет размера на диске
        total_size = 0
        if settings.INSTAGRAM_MEDIA_ROOT and os.path.exists(settings.INSTAGRAM_MEDIA_ROOT):
            for media in media_query.filter(is_downloaded=True):
                if media.local_path and os.path.exists(media.local_path.path):
                    total_size += os.path.getsize(media.local_path.path)
                    
        return {
            "total_media": total_media,
            "downloaded": downloaded_media,
            "pending": total_media - downloaded_media,
            "photos": photos,
            "videos": videos,
            "total_size_mb": round(total_size / 1024 / 1024, 2),
            "download_percentage": round((downloaded_media / total_media * 100), 2) if total_media > 0 else 0
        }
        
    def cleanup_orphaned_files(self) -> int:
        """Удаление файлов, которые больше не связаны с записями в БД"""
        
        if not settings.INSTAGRAM_MEDIA_ROOT or not os.path.exists(settings.INSTAGRAM_MEDIA_ROOT):
            return 0
            
        cleaned = 0
        
        # Получаем все пути из БД
        db_paths = set()
        for media in self.repository.filter(is_downloaded=True):
            if media.local_path:
                db_paths.add(os.path.basename(media.local_path.name))
                
        # Проверяем файлы на диске
        for root, dirs, files in os.walk(settings.INSTAGRAM_MEDIA_ROOT):
            for file in files:
                if file not in db_paths and not file.startswith("."):
                    file_path = os.path.join(root, file)
                    try:
                        os.remove(file_path)
                        cleaned += 1
                        logger.info(f"Removed orphaned file: {file}")
                    except Exception as e:
                        logger.exception(f"Failed to remove file {file}: {e!s}")
                        
        return cleaned
        
    def get_failed_downloads(self) -> list[InstagramMedia]:
        """Получение списка медиа с ошибками загрузки"""
        return list(self.repository.filter(
            is_downloaded=False,
            download_error__isnull=False
        ).exclude(download_error=""))
        
    def retry_failed_downloads(self) -> dict[str, int]:
        """Повторная попытка загрузки неудачных медиа"""
        
        failed_media = self.get_failed_downloads()
        stats = {
            "downloaded": 0,
            "failed": 0
        }
        
        for media in failed_media:
            # Очищаем ошибку для повторной попытки
            media.download_error = ""
            media.save()
            
            try:
                if self._media_handler.download_media(media):
                    stats["downloaded"] += 1
                else:
                    stats["failed"] += 1
            except Exception as e:
                logger.exception(f"Retry failed for media {media.media_id}: {e!s}")
                stats["failed"] += 1
                
        return stats