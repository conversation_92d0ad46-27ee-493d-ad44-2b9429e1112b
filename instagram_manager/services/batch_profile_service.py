"""
Service for batch processing Instagram profiles.
Handles efficient profile fetching and creation for multiple usernames.
"""
import logging
from typing import Any

from django.db import transaction

from instagram_manager.instagram_api import BrightDataClient
from instagram_manager.instagram_api.data_handlers.profile_handler import <PERSON><PERSON>andler
from instagram_manager.models import InstagramProfile, InstagramScrapingTask, InstagramSubTask
from instagram_manager.repositories import ProfileRepository, TaskRepository
from instagram_manager.schemas.brightdata import InstagramProfileResponse

logger = logging.getLogger(__name__)


class BatchProfileService:
    """Service for batch importing Instagram profiles"""
    
    def __init__(self, client: BrightDataClient):
        self.client = client
        self.profile_handler = ProfileHandler()
        self.profile_repository = ProfileRepository()
        self.task_repository = TaskRepository()
    
    def import_profiles_batch(
        self,
        usernames: list[str],
        task: InstagramScrapingTask | None = None
    ) -> dict[str, dict[str, Any]]:
        """
        Import multiple profiles in a single batch request.
        
        Args:
            usernames: List of Instagram usernames
            task: Scraping task for tracking
            
        Returns:
            Dictionary with results for each username
        """
        results: dict[str, dict[str, Any]] = {}
        
        try:
            # Подготавливаем URLs для batch запроса
            profile_urls = [f"https://www.instagram.com/{username}/" for username in usernames]
            
            logger.info(f"Starting batch profile import for {len(usernames)} usernames")
            
            # Запускаем batch snapshot для профилей
            snapshot_id = self.client.trigger_snapshot(
                dataset_type="instagram_profile",
                urls=profile_urls
            )
            
            # Обновляем задачу
            if task:
                task.brightdata_snapshot_id = str(snapshot_id) if snapshot_id else ""
                task.save()
            
            # Ждем завершения и получаем данные
            profiles_data = self._wait_for_snapshot(snapshot_id, profile_urls)
            
            # Проверяем что получили корректные данные
            if not profiles_data:
                logger.warning("No profile data received from snapshot")
                raise Exception("No profile data received from BrightData")
            
            # Обрабатываем результаты
            username_to_data = self._map_profiles_to_usernames(profiles_data, usernames)
            
            # Сохраняем профили
            with transaction.atomic():
                logger.debug(
                    f"[TRANSACTION START] Starting atomic transaction for saving {len(usernames)} profiles"
                )
                
                for username in usernames:
                    profile_data = username_to_data.get(username)
                    
                    if profile_data:
                        try:
                            # Создаем или обновляем профиль
                            profile = self.profile_handler.process_profile_data(profile_data)
                            
                            results[username] = {
                                "success": True,
                                "profile": profile,
                                "created": profile._state.adding if hasattr(profile, "_state") else True
                            }
                            
                            logger.info(f"Successfully imported profile: {username}")
                            
                        except Exception as e:
                            logger.exception(
                                f"[TRANSACTION WARNING] Error processing profile {username}: {e!s}. "
                                f"This profile will be skipped but transaction continues."
                            )
                            results[username] = {
                                "success": False,
                                "error": str(e)
                            }
                    else:
                        results[username] = {
                            "success": False,
                            "error": "Profile data not found in response"
                        }
                
                logger.debug(
                    f"[TRANSACTION END] Transaction completed. Processed {len(results)} profiles"
                )
            
            return results
            
        except Exception as e:
            logger.exception(f"Batch profile import failed: {e!s}")
            
            # Возвращаем ошибку для всех профилей
            for username in usernames:
                if username not in results:
                    results[username] = {
                        "success": False,
                        "error": f"Batch error: {e!s}"
                    }
            
            return results
    
    def validate_and_create_profiles(
        self,
        usernames: list[str],
        parent_task: "InstagramScrapingTask | None" = None
    ) -> tuple[list[InstagramProfile], list[str]]:
        """
        Проверяет существование профилей и создает отсутствующие.
        
        Args:
            usernames: Список имен пользователей
            parent_task: Родительская задача для создания иерархии
            
        Returns:
            Tuple: (список профилей, список неудачных username)
        """
        logger.info(
            f"[PROFILE DEBUG] validate_and_create_profiles called with {len(usernames)} usernames, "
            f"parent_task={'YES' if parent_task else 'NO'} (ID: {parent_task.id if parent_task else 'None'})"
        )
        
        # Validate usernames manually since we removed the decorator
        if not usernames:
            raise ValueError("Usernames list cannot be empty")
        if len(usernames) > 100:
            raise ValueError("Cannot process more than 100 usernames at once")
        
        # Normalize usernames
        validated_usernames = []
        for username in usernames:
            username = username.strip().lower().lstrip("@")
            validated_usernames.append(username)
        usernames = validated_usernames
        
        # Всегда создаем подзадачу для импорта профилей, если есть родительская задача
        subtask = None
        if parent_task and usernames:
            # Создаем ОДНУ общую подзадачу для всех профилей
            logger.info(
                f"Creating single profile subtask for batch import of {len(usernames)} profiles"
            )
            
            subtask = parent_task.create_subtask(
                subtask_type="import_profile",
                target_identifier=f"batch_{len(usernames)}_profiles",
                usernames=usernames,  # Сохраняем полный список usernames
                total_profiles=len(usernames)
            )
            logger.info(f"Created profile subtask ID: {subtask.id} for parent task ID: {parent_task.id}")
            
            subtask.start()  # Отмечаем начало выполнения
            subtask.total_items = len(usernames)
            
            # Add protection against DatabaseError
            try:
                from django.db import DatabaseError
                # Check if subtask still exists before saving
                if InstagramSubTask.objects.filter(pk=subtask.pk).exists():
                    subtask.save(update_fields=["total_items"])
                else:
                    logger.warning(f"Subtask {subtask.id} no longer exists in database")
            except DatabaseError as e:
                logger.error(f"Database error saving subtask {subtask.id}: {e}")
            
            logger.info(f"Profile subtask ID {subtask.id} started with {len(usernames)} profiles to process")
        
        existing_profiles = []
        missing_usernames = []
        failed_usernames = []
        profiles_to_update = []
        
        # Проверяем какие профили уже существуют
        for username in usernames:
            profile = self.profile_repository.get_by_username(username)
            if profile:
                existing_profiles.append(profile)
                profiles_to_update.append(username)
            else:
                missing_usernames.append(username)
        
        # Создаем отсутствующие профили
        if missing_usernames:
            logger.info(f"Creating {len(missing_usernames)} missing profiles: {', '.join(missing_usernames)}")
            
            try:
                # Импортируем профили (передаем None вместо task)
                results = self.import_profiles_batch(missing_usernames, None)
                
                # Обрабатываем результаты
                created_profiles = []
                for username, result in results.items():
                    if result["success"]:
                        created_profiles.append(result["profile"])
                    else:
                        failed_usernames.append(username)
                        logger.error(f"Failed to create profile {username}: {result.get('error')}")
                
                existing_profiles.extend(created_profiles)
                
            except Exception as e:
                logger.exception(f"Failed to create profiles: {e!s}")
                failed_usernames.extend(missing_usernames)
        
        # Обновляем подзадачу если она есть
        if subtask:
            profiles_created = len(missing_usernames) - len(failed_usernames) if missing_usernames else 0
            profiles_processed = len(usernames) - len(failed_usernames)
            
            if failed_usernames and profiles_processed == 0:
                # All profiles failed
                subtask.fail(f"Failed to process all {len(failed_usernames)} profiles")
                logger.info(f"Profile subtask ID {subtask.id} failed - all profiles failed")
            else:
                # Хотя бы некоторые профили обработаны успешно
                status = "partial_success" if failed_usernames else "success"
                subtask.complete(result_data={
                    "status": status,
                    "profiles_created": profiles_created,
                    "profiles_updated": len(profiles_to_update),
                    "profiles_failed": len(failed_usernames),
                    "failed_usernames": failed_usernames if failed_usernames else [],
                    "message": f"Processed {profiles_processed}/{len(usernames)} profiles. "
                               f"Created: {profiles_created}, Updated: {len(profiles_to_update)}, Failed: {len(failed_usernames)}"
                })
                logger.info(
                    f"Profile subtask ID {subtask.id} completed with status: {status}. "
                    f"Created: {profiles_created}, Updated: {len(profiles_to_update)}, Failed: {len(failed_usernames)}"
                )
            
            subtask.processed_items = profiles_processed
            subtask.save(update_fields=["processed_items"])
        
        return existing_profiles, failed_usernames
    
    def _wait_for_snapshot(self, snapshot_id: str, profile_urls: list[str]) -> list[dict[Any, Any]]:
        """Ожидает завершения snapshot и возвращает данные"""
        max_attempts = 300
        attempt = 0
        
        while attempt < max_attempts:
            status = self.client.get_snapshot_status(snapshot_id)
            
            # Проверяем тип ответа для безопасности
            if not isinstance(status, dict):
                logger.error(f"Unexpected status type: {type(status)}")
                raise Exception(f"Unexpected snapshot status response type: {type(status)}")
            
            current_status = status.get("status")
            logger.debug(f"Snapshot {snapshot_id} status: {current_status}")
            
            if current_status == "ready":
                # Если данные уже есть в ответе статуса, используем их
                if "data" in status:
                    all_profiles_data = status["data"]
                    logger.info(
                        f"Batch profile snapshot completed (data in status). "
                        f"Total profiles: {len(all_profiles_data)} for {len(profile_urls)} requested"
                    )
                    return all_profiles_data  # type: ignore[no-any-return]
                
                # Иначе получаем данные отдельным запросом
                all_profiles_data = self.client.get_snapshot_data(snapshot_id)
                
                logger.info(
                    f"Batch profile snapshot completed. Total profiles: {len(all_profiles_data)} "
                    f"for {len(profile_urls)} requested"
                )
                
                return all_profiles_data  # type: ignore[no-any-return]
                
            elif current_status == "failed":
                error_msg = status.get("error", "")
                urls_str = ", ".join(profile_urls[:3]) + ("..." if len(profile_urls) > 3 else "")
                
                raise Exception(
                    f"Batch profile snapshot failed: {error_msg} for URLs: {urls_str}"
                )
            
            elif current_status in ["building", "processing", "running"]:
                # Продолжаем ждать
                pass
            else:
                logger.warning(f"Unknown snapshot status: {current_status}")
            
            attempt += 1
            from time import sleep
            sleep(2)
        
        raise Exception("Timeout waiting for batch profile snapshot")
    
    def _map_profiles_to_usernames(
        self,
        profiles_data: list[dict[Any, Any]],
        usernames: list[str]
    ) -> dict[str, InstagramProfileResponse]:
        """
        Maps profile data to usernames.
        
        Returns:
            Dictionary where key is username, value is InstagramProfileResponse
        """
        username_to_data = {}
        
        for profile_data in profiles_data:
            try:
                # Пропускаем если это статусные сообщения
                if "status" in profile_data and "profile_id" not in profile_data:
                    logger.debug(f"Skipping status message: {profile_data}")
                    continue
                
                # Валидируем данные через Pydantic
                profile = InstagramProfileResponse(**profile_data)
                
                # Сопоставляем по username
                if profile.username in usernames:
                    username_to_data[profile.username] = profile
                else:
                    logger.warning(f"Received profile {profile.username} not in requested list")
                    
            except Exception as e:
                logger.exception(f"Error validating profile data: {e!s}")
                logger.debug(f"Profile data: {profile_data}")
        
        return username_to_data