from django import forms
from django.core.exceptions import ValidationError

from core.forms import (
    BulkURLsField,
    BulkUsernamesField,
    CleanHashtagMixin,
    CleanUsernameMixin,
    DateRangeMixin,
    ExecutionModeMixin,
    HashtagField,
    InstagramURLField,
    InstagramURLMixin,
    MediaDownloadOptionsMixin,
    PostTypesField,
    SafeCheckboxSelectMultiple,
    SocialUsernameField,
)
from instagram_manager.models import (
    InstagramAccount,
    InstagramComment,
    InstagramPost,
    InstagramProfile,
    MajilaServiceAccount,
)


class ProfileImportForm(CleanUsernameMixin, ExecutionModeMixin, MediaDownloadOptionsMixin, forms.Form):
    """Форма для импорта профиля Instagram"""

    username = SocialUsernameField(
        platform="instagram",
        required=True,
        help_text="Enter Instagram username without @"
    )

    import_posts = forms.BooleanField(
        required=False,
        initial=True,
        label="Import recent posts",
        widget=forms.CheckboxInput(),
    )

    posts_limit = forms.IntegerField(
        required=False,
        initial=0,  # 0 означает "все доступные посты"
        min_value=0,
        max_value=500,
        label="Number of posts to import (0 = all available)",
        help_text="Enter 0 to import all available posts, or specify a number (max 500)",
        widget=forms.NumberInput(
            attrs={"class": "form-control", "placeholder": "0 for all posts"}
        ),
    )

    post_types = PostTypesField()

    import_followers = forms.BooleanField(
        required=False,
        initial=False,
        label="Import followers list",
        widget=forms.CheckboxInput(),
    )
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Update widget attrs after initialization
        self.fields["import_posts"].widget.attrs.update({"class": "form-check-input"})
        self.fields["import_followers"].widget.attrs.update({"class": "form-check-input"})


class HashtagImportForm(CleanHashtagMixin, forms.Form):
    """Форма для импорта постов по хештегу"""

    hashtag = HashtagField(
        required=True,
        help_text="Enter hashtag with or without #"
    )

    limit = forms.IntegerField(
        required=False,
        initial=20,
        min_value=1,
        max_value=100,
        widget=forms.NumberInput(attrs={"class": "form-control"}),
    )


class MediaDownloadForm(forms.Form):
    """Форма для массовой загрузки медиафайлов"""

    profile = forms.ModelChoiceField(
        queryset=InstagramProfile.objects.all(),
        required=False,
        widget=forms.Select(attrs={"class": "form-control"}),
    )

    download_photos = forms.BooleanField(
        required=False,
        initial=True,
        widget=forms.CheckboxInput(attrs={"class": "form-check-input"}),
    )

    download_videos = forms.BooleanField(
        required=False,
        initial=True,
        widget=forms.CheckboxInput(attrs={"class": "form-check-input"}),
    )

    download_thumbnails = forms.BooleanField(
        required=False,
        initial=True,
        label="Download video thumbnails",
        widget=forms.CheckboxInput(attrs={"class": "form-check-input"}),
    )


class InstagramImportTaskForm(ExecutionModeMixin, MediaDownloadOptionsMixin, forms.Form):
    """Форма для создания задачи импорта Instagram"""

    TASK_TYPE_CHOICES = [
        ("profile", "Import Profile"),
        ("posts", "Import Posts by Username"),
        ("hashtag", "Import Posts by Hashtag"),
    ]

    task_type = forms.ChoiceField(
        choices=TASK_TYPE_CHOICES,
        required=True,
        widget=forms.Select(attrs={"class": "form-control", "id": "id_task_type"}),
    )

    target_identifier = forms.CharField(
        max_length=150,
        required=True,
        help_text="Enter username for profile/posts import, or hashtag for hashtag import",
        widget=forms.TextInput(
            attrs={"class": "form-control", "placeholder": "username or hashtag"}
        ),
    )

    posts_limit = forms.IntegerField(
        required=False,
        initial=0,  # 0 означает "все доступные посты"
        min_value=0,
        max_value=500,
        label="Number of posts to import (0 = all available)",
        help_text="Enter 0 to import all available posts, or specify a number (max 500)",
        widget=forms.NumberInput(
            attrs={"class": "form-control", "placeholder": "0 for all posts"}
        ),
    )

    post_types = PostTypesField(
        help_text="Select which types of posts to import (applies to posts import only)"
    )

    def clean_target_identifier(self):
        from core.schemas.validators import CommonValidators
        
        target_identifier = self.cleaned_data["target_identifier"]
        task_type = self.cleaned_data.get("task_type")

        # Очищаем идентификатор в зависимости от типа задачи
        if task_type in ["profile", "posts"]:
            target_identifier = CommonValidators.clean_username(target_identifier)
        elif task_type == "hashtag":
            from core.forms.validators import hashtag_validator
            target_identifier = hashtag_validator(target_identifier)

        return target_identifier


class CommentPostForm(InstagramURLMixin, forms.Form):
    """Форма для постинга одного комментария"""

    account = forms.ModelChoiceField(
        queryset=InstagramAccount.objects.filter(is_active=True),
        label="Instagram Account",
        help_text="Select the account to post from",
        widget=forms.Select(attrs={"class": "form-control"}),
    )
    
    post_url = InstagramURLField(
        label="Post URL",
        help_text="Instagram post URL (e.g., https://instagram.com/p/DKZ7A0XpB1U/)"
    )
    
    comment_text = forms.CharField(
        label="Comment Text",
        widget=forms.Textarea(
            attrs={
                "rows": 3,
                "cols": 50,
                "placeholder": "Enter your comment here...",
                "maxlength": 2200,  # Instagram comment limit
                "class": "form-control",
            }
        ),
        max_length=2200,
    )
    
    reply_to_username = SocialUsernameField(
        platform="instagram",
        label="Reply to User (optional)",
        required=False,
        help_text="Username to reply to (leave empty for regular comment)"
    )
    
    debug_mode = forms.BooleanField(
        label="Debug Mode",
        required=False,
        initial=False,
        help_text="Run browser in visible mode with slow actions for debugging",
        widget=forms.CheckboxInput(attrs={"class": "form-check-input"}),
    )
    
    force_login = forms.BooleanField(
        label="Force Login",
        required=False,
        initial=False,
        help_text="Force re-login even if session exists",
        widget=forms.CheckboxInput(attrs={"class": "form-check-input"}),
    )


class BulkCommentForm(forms.Form):
    """Форма для массового постинга комментариев"""

    account = forms.ModelChoiceField(
        queryset=InstagramAccount.objects.filter(is_active=True),
        label="Instagram Account",
        widget=forms.Select(attrs={"class": "form-control"}),
    )
    
    posts_list = BulkURLsField(
        url_type="instagram_post",
        max_count=50,
        label="Posts URLs (one per line)",
        help_text="Enter Instagram post URLs, one per line (max 50)",
    )
    
    comment_template = forms.CharField(
        label="Comment Template",
        widget=forms.Textarea(
            attrs={
                "rows": 5,
                "cols": 60,
                "placeholder": "Great post! {emoji}\n\nVariables: {username}, {emoji}, {number}",
                "class": "form-control",
            }
        ),
        help_text="Use {username} for post author, {emoji} for random emoji, {number} for random number",
    )
    
    randomize = forms.BooleanField(
        label="Randomize comments",
        required=False,
        initial=True,
        help_text="Add slight variations to each comment",
        widget=forms.CheckboxInput(attrs={"class": "form-check-input"}),
    )
    
    delay_between = forms.IntegerField(
        label="Delay between comments (seconds)",
        initial=30,
        min_value=0,
        max_value=300,
        help_text="Wait time between posting comments",
        widget=forms.NumberInput(attrs={"class": "form-control"}),
    )


class CommentReplyForm(forms.ModelForm):
    """Inline форма для ответа на комментарий"""

    account = forms.ModelChoiceField(
        queryset=InstagramAccount.objects.filter(is_active=True),
        label="Reply from Account",
        widget=forms.Select(attrs={"class": "form-control"}),
    )

    class Meta:
        model = InstagramComment
        fields = ["text"]
        widgets = {
            "text": forms.Textarea(
                attrs={
                    "rows": 2,
                    "cols": 40,
                    "placeholder": "Type your reply...",
                    "class": "form-control",
                }
            )
        }


class AccountPasswordForm(CleanUsernameMixin, forms.ModelForm):
    """Форма для безопасного ввода пароля аккаунта"""

    password = forms.CharField(
        widget=forms.PasswordInput(attrs={"class": "form-control"}),
        help_text="Password will be encrypted and stored securely",
    )
    confirm_password = forms.CharField(
        widget=forms.PasswordInput(attrs={"class": "form-control"}),
        label="Confirm Password",
    )

    class Meta:
        model = InstagramAccount
        fields = ["username", "password"]
        field_classes = {
            "username": SocialUsernameField,
        }

    def clean(self):
        cleaned_data = super().clean()
        password = cleaned_data.get("password")
        confirm_password = cleaned_data.get("confirm_password")

        if password and confirm_password and password != confirm_password:
            raise ValidationError("Passwords don't match")

        return cleaned_data

    def save(self, commit=True):
        """Save the account with encrypted password"""
        account = super().save(commit=False)
        # Set the password using the property which handles encryption
        account.password = self.cleaned_data["password"]
        if commit:
            account.save()
        return account


class BatchImportPostsForm(DateRangeMixin, ExecutionModeMixin, MediaDownloadOptionsMixin, forms.Form):
    """Форма для batch импорта постов для нескольких профилей"""
    
    # Поле для ручного ввода usernames
    usernames = BulkUsernamesField(
        platform="instagram",
        max_count=100,
        required=False,
        label="Instagram Usernames",
        help_text="Enter Instagram usernames, one per line (max 100)"
    )
    
    posts_limit = forms.IntegerField(
        required=False,
        initial=0,
        min_value=0,
        max_value=500,
        label="Number of posts per profile (0 = all available)",
        help_text="Enter 0 to import all available posts, or specify a number (max 500)",
        widget=forms.NumberInput(
            attrs={"class": "form-control", "placeholder": "0 for all posts"}
        ),
    )
    
    batch_size = forms.IntegerField(
        required=False,
        initial=10,
        min_value=1,
        max_value=50,
        label="Batch size",
        help_text="Number of profiles to process in one BrightData request",
        widget=forms.NumberInput(attrs={"class": "form-control"}),
    )
    
    # Фильтры дат
    date_from = forms.DateField(
        required=False,
        label="Start date",
        widget=forms.DateInput(attrs={"class": "form-control", "type": "date"}),
        help_text="Import posts starting from this date"
    )
    
    date_to = forms.DateField(
        required=False,
        label="End date",
        widget=forms.DateInput(attrs={"class": "form-control", "type": "date"}),
        help_text="Import posts up to this date"
    )
    
    post_types = PostTypesField()
    
    import_comments = forms.BooleanField(
        required=False,
        initial=False,
        label="Also import comments",
        widget=forms.CheckboxInput(attrs={"class": "form-check-input"}),
        help_text="Import comments for each post (slower)"
    )
    
    # Новые поля для периодических задач
    create_periodic = forms.BooleanField(
        required=False,
        initial=False,
        label="Make this a periodic import",
        widget=forms.CheckboxInput(attrs={
            "class": "form-check-input",
            "id": "id_create_periodic"
        }),
        help_text="Schedule this import to run automatically at regular intervals"
    )
    
    interval_seconds = forms.IntegerField(
        required=False,
        initial=86400,  # 24 часа в секундах
        min_value=1,
        label="Run every (seconds)",
        widget=forms.NumberInput(attrs={
            "class": "form-control",
            "id": "id_interval_seconds"
        }),
        help_text="How often to run this import in seconds"
    )
    
    def clean(self):
        cleaned_data = super().clean()
        
        # Если создаем периодическую задачу
        if cleaned_data.get("create_periodic"):
            # Периодические задачи всегда асинхронные
            cleaned_data["execution_mode"] = "async"
            
            # Проверяем интервал
            if not cleaned_data.get("interval_seconds"):
                raise ValidationError({
                    "interval_seconds": "Interval is required for periodic tasks"
                })
            
            # НЕ устанавливаем date_from автоматически!
            # Пусть периодические задачи сами решают, нужны ли фильтры на первом запуске
        
        return cleaned_data


class MajilaAccountForm(forms.ModelForm):
    """Форма для создания/редактирования аккаунта Majila"""
    password = forms.CharField(
        widget=forms.PasswordInput(attrs={
            "class": "form-control",
            "placeholder": "Majila password"
        }),
        required=False,
        help_text="Leave empty to keep current password"
    )
    
    class Meta:
        model = MajilaServiceAccount
        fields = ["instagram_profile", "username", "is_active"]
        widgets = {
            "username": forms.TextInput(attrs={
                "class": "form-control",
                "placeholder": "Majila username"
            }),
            "instagram_profile": forms.Select(attrs={
                "class": "form-control"
            })
        }
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # При создании нового объекта пароль обязателен
        if not self.instance.pk:
            self.fields["password"].required = True
            self.fields["password"].help_text = "Enter password for Majila account"
    
    def save(self, commit=True):
        instance = super().save(commit=False)
        password = self.cleaned_data.get("password")
        if password:
            instance.password = password
        if commit:
            instance.save()
        return instance


class PostModelMultipleChoiceField(forms.ModelMultipleChoiceField):
    """Кастомное поле для выбора постов с информативными метками"""
    def label_from_instance(self, obj):
        """Формирование информативной метки для поста"""
        return f"{obj.shortcode} - @{obj.profile.username} - {obj.posted_at.strftime('%Y-%m-%d')}"


class MajilaExportForm(forms.Form):
    """Форма для экспорта постов в Majila"""
    majila_account = forms.ModelChoiceField(
        queryset=MajilaServiceAccount.objects.filter(is_active=True),
        widget=forms.Select(attrs={"class": "form-control"}),
        label="Select Majila account",
        help_text="Choose the account to export posts to"
    )
    posts = PostModelMultipleChoiceField(
        queryset=InstagramPost.objects.none(),
        widget=SafeCheckboxSelectMultiple,
        label="Select posts to export"
    )
    skip_existing = forms.BooleanField(
        required=False,
        initial=True,
        label="Skip already exported posts",
        help_text="If checked, posts that already exist in Majila will be skipped"
    )
    
    def __init__(self, *args, **kwargs):
        # Получаем queryset постов из kwargs
        posts_queryset = kwargs.pop("posts_queryset", None)
        instagram_profile = kwargs.pop("instagram_profile", None)
        
        super().__init__(*args, **kwargs)
        
        # Устанавливаем queryset для постов
        if posts_queryset:
            self.fields["posts"].queryset = posts_queryset
            # По умолчанию выбираем все посты
            self.fields["posts"].initial = list(posts_queryset.values_list("id", flat=True))
        
        # Фильтруем аккаунты Majila по профилю Instagram
        if instagram_profile:
            # Сначала пробуем найти аккаунты для конкретного профиля
            profile_accounts = MajilaServiceAccount.objects.filter(
                instagram_profile=instagram_profile,
                is_active=True
            )
            if profile_accounts.exists():
                self.fields["majila_account"].queryset = profile_accounts
            else:
                # Если нет аккаунтов для профиля, показываем все активные
                self.fields["majila_account"].queryset = MajilaServiceAccount.objects.filter(
                    is_active=True
                )
        else:
            # Если профиль не указан, показываем все активные аккаунты
            self.fields["majila_account"].queryset = MajilaServiceAccount.objects.filter(
                is_active=True
            )


class MajilaBulkExportForm(forms.Form):
    """Форма для массового экспорта постов из разных профилей"""
    profiles = forms.ModelMultipleChoiceField(
        queryset=InstagramProfile.objects.all(),
        widget=SafeCheckboxSelectMultiple,
        label="Select Instagram profiles"
    )
    limit_per_profile = forms.IntegerField(
        min_value=1,
        max_value=100,
        initial=10,
        label="Posts per profile",
        help_text="Maximum number of posts to export from each profile"
    )
    skip_existing = forms.BooleanField(
        required=False,
        initial=True,
        label="Skip already exported posts"
    )
