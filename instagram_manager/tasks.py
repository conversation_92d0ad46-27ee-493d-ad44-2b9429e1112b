"""
Celery tasks for Instagram manager.

This file is automatically discovered by Ce<PERSON><PERSON>.
"""
from typing import Any

from celery import shared_task

# Import the tasks that should be available at module level


@shared_task(name="instagram.update_active_profiles")
def update_active_profiles(**kwargs) -> dict[str, Any]:
    """Update active Instagram profiles asynchronously."""
    from instagram_manager.tasks.maintenance_tasks import UpdateActiveProfilesTask
    task = UpdateActiveProfilesTask()
    return task.execute_task(**kwargs)


@shared_task(name="instagram.execute_periodic_import")
def execute_periodic_import(task_id: int) -> dict[str, Any]:
    """Execute a periodic import for an existing InstagramScrapingTask."""
    from instagram_manager.tasks.periodic_tasks import execute_periodic_import as _execute_periodic_import
    return _execute_periodic_import(task_id)