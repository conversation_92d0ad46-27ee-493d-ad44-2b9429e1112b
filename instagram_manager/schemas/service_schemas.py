"""
Pydantic схемы для валидации параметров сервисов Instagram
"""

from datetime import datetime
from typing import Literal

from pydantic import BaseModel, ConfigDict, field_validator

from core.schemas.validators import CommonValidators


class PostImportSchema(BaseModel):
    """Схема для импорта постов"""
    model_config = ConfigDict(str_strip_whitespace=True)
    
    limit: int | None = None
    import_comments: bool = False
    import_comments_limit: int | None = 100
    start_date: str | None = ""
    end_date: str | None = ""
    post_types: list[str] | None = None
    
    @field_validator("limit", "import_comments_limit")
    @classmethod
    def validate_positive_int(cls, v: int | None) -> int | None:
        if v is not None and v <= 0:
            raise ValueError("Limit must be positive")
        return v
    
    @field_validator("start_date", "end_date")
    @classmethod
    def validate_date_format(cls, v: str) -> str:
        if v and not CommonValidators.is_valid_date_format(v):
            raise ValueError("Date must be in YYYY-MM-DD format")
        return v
    
    @field_validator("post_types")
    @classmethod
    def validate_post_types(cls, v: list[str] | None) -> list[str] | None:
        if v:
            valid_types = {"photo", "video", "carousel", "reel"}
            invalid = set(v) - valid_types
            if invalid:
                raise ValueError(f"Invalid post types: {invalid}")
        return v


class CommentImportSchema(BaseModel):
    """Схема для импорта комментариев"""
    model_config = ConfigDict(str_strip_whitespace=True)
    
    limit: int | None = None
    include_replies: bool = True
    min_likes: int | None = None
    force_update: bool = False
    
    @field_validator("limit", "min_likes")
    @classmethod
    def validate_positive_int(cls, v: int | None) -> int | None:
        if v is not None and v < 0:
            raise ValueError("Value must be non-negative")
        return v


class MediaDownloadSchema(BaseModel):
    """Схема для загрузки медиа"""
    model_config = ConfigDict(str_strip_whitespace=True)
    
    url: str
    media_type: Literal["photo", "video", "thumbnail"]
    quality: str | None = "high"
    force_download: bool = False
    
    @field_validator("url")
    @classmethod
    def validate_url(cls, v: str) -> str:
        if not v.startswith(("http://", "https://")):
            raise ValueError("URL must start with http:// or https://")
        return v
    
    @field_validator("quality")
    @classmethod
    def validate_quality(cls, v: str) -> str:
        valid_qualities = {"low", "medium", "high", "original"}
        if v not in valid_qualities:
            raise ValueError(f"Quality must be one of {valid_qualities}")
        return v


class BatchOperationSchema(BaseModel):
    """Схема для batch операций"""
    model_config = ConfigDict(str_strip_whitespace=True)
    
    usernames: list[str]
    operation_type: Literal["import_profiles", "import_posts", "export_data"]
    parallel_workers: int = 3
    skip_errors: bool = True
    
    @field_validator("usernames")
    @classmethod
    def validate_usernames(cls, v: list[str]) -> list[str]:
        if not v:
            raise ValueError("Usernames list cannot be empty")
        if len(v) > 100:
            raise ValueError("Cannot process more than 100 usernames at once")
        return [u.strip().lower() for u in v]
    
    @field_validator("parallel_workers")
    @classmethod
    def validate_workers(cls, v: int) -> int:
        if v < 1 or v > 10:
            raise ValueError("Workers must be between 1 and 10")
        return v


class PostFilterSchema(BaseModel):
    """Схема для фильтрации постов"""
    
    profile_id: int | None = None
    post_type: str | None = None
    date_from: datetime | None = None
    date_to: datetime | None = None
    min_likes: int | None = None
    max_likes: int | None = None
    has_media: bool | None = None
    
    @field_validator("post_type")
    @classmethod
    def validate_post_type(cls, v: str | None) -> str | None:
        if v:
            valid_types = {"photo", "video", "carousel", "reel"}
            if v not in valid_types:
                raise ValueError(f"Post type must be one of {valid_types}")
        return v


class CommentCreateSchema(BaseModel):
    """Схема для создания комментария"""
    model_config = ConfigDict(str_strip_whitespace=True)
    
    post_id: int
    text: str
    reply_to_comment_id: int | None = None
    
    @field_validator("text")
    @classmethod
    def validate_text(cls, v: str) -> str:
        v = v.strip()
        if not v:
            raise ValueError("Comment text cannot be empty")
        if len(v) > 2200:
            raise ValueError("Comment text cannot exceed 2200 characters")
        return v


class BatchPostImportSchema(BaseModel):
    """Schema for batch post import validation"""
    model_config = ConfigDict(str_strip_whitespace=True)
    
    limit: int | None = None
    start_date: str = ""
    end_date: str = ""
    post_types: list[str] | None = None
    skip_media_download: bool = False
    import_all_users: bool = False
    
    @field_validator("limit")
    @classmethod
    def validate_limit(cls, v: int | None) -> int | None:
        if v is not None:
            if v <= 0:
                raise ValueError("Limit must be positive")
            if v > 1000:
                raise ValueError("Limit cannot exceed 1000")
        return v
    
    @field_validator("start_date", "end_date")
    @classmethod
    def validate_date(cls, v: str) -> str:
        if v and not CommonValidators.is_valid_date_format(v):
            raise ValueError("Invalid date format. Use YYYY-MM-DD")
        return v
    
    @field_validator("post_types")
    @classmethod
    def validate_post_types(cls, v: list[str] | None) -> list[str] | None:
        valid_types = {"photo", "video", "reel", "carousel"}
        if v:
            for post_type in v:
                if post_type not in valid_types:
                    raise ValueError(f"Invalid post type: {post_type}. Valid types: {valid_types}")
        return v


class BatchProfileSchema(BaseModel):
    """Schema for batch profile operations"""
    model_config = ConfigDict(str_strip_whitespace=True)
    
    usernames: list[str]
    
    @field_validator("usernames")
    @classmethod
    def validate_usernames(cls, v: list[str]) -> list[str]:
        if not v:
            raise ValueError("Usernames list cannot be empty")
        if len(v) > 100:
            raise ValueError("Cannot process more than 100 usernames at once")
        
        validated = []
        for username in v:
            username = username.strip().lower()
            if not CommonValidators.is_valid_instagram_username(username):
                raise ValueError(f"Invalid Instagram username: {username}")
            validated.append(username)
        return validated