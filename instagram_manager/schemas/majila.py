from datetime import datetime
from uuid import UUID

from pydantic import BaseModel, Field, field_serializer


class SocialMediaContent(BaseModel):
    """Социальные медиа данные для поста"""

    external_id: str = Field(..., description="Instagram post ID")
    external_url: str = Field(..., description="Instagram post URL")
    external_username: str = Field(..., description="Instagram username")
    external_user_url: str | None = Field(
        None, description="Instagram user profile URL"
    )


class ImageContent(BaseModel):
    """Контент изображения"""

    image_url: str = Field(..., description="URL изображения")
    width: int | None = Field(None, description="Ширина изображения")
    height: int | None = Field(None, description="Высота изображения")


class VideoContent(BaseModel):
    """Контент видео"""

    video_url: str = Field(..., description="URL видео")
    preview_url: str | None = Field(None, description="URL превью видео")


class MajilaPostContent(BaseModel):
    """Контент поста для экспорта в Majila"""

    social_media: SocialMediaContent
    text: str | None = Field(None, description="Текст поста (caption)")
    images: list[ImageContent] | None = Field(None, description="Изображения поста")
    videos: list[VideoContent] | None = Field(None, description="Видео поста")
    title: str | None = Field(None, description="Заголовок поста")


class MajilaPost(BaseModel):
    """Пост для отправки в Community API"""

    @field_serializer("post_uuid")
    def serialize_uuid(self, value: UUID, _info) -> str:
        return str(value)
    
    @field_serializer("posted_at")
    def serialize_datetime(self, value: datetime | None, _info) -> str | None:
        return value.isoformat() if value else None

    post_uuid: UUID = Field(..., description="UUID поста")
    external_id: str = Field(..., description="Внешний ID поста (Instagram post ID)")
    content: MajilaPostContent = Field(..., description="Контент поста")
    post_type: str = Field(default="social_media_post", description="Тип поста")
    original_platform: str = Field(
        default="instagram", description="Оригинальная платформа"
    )
    posted_at: datetime | None = Field(
        None, description="Время публикации оригинального поста"
    )
    is_active: bool = Field(default=True, description="Активность поста")


class MajilaAuthResponse(BaseModel):
    """Ответ от Passport API при авторизации"""

    access_token: str = Field(..., description="Токен доступа")
    backwards_url: str | None = Field(None, description="URL для возврата")


class MajilaUserResponse(BaseModel):
    """Ответ от Community API о пользователе"""

    @field_serializer("uuid")
    def serialize_uuid(self, value: UUID, _info) -> str:
        return str(value)

    uuid: UUID = Field(..., description="UUID пользователя")
    username: str = Field(..., description="Имя пользователя")
    email: str | None = Field(None, description="Email пользователя")
    avatar_url: str | None = Field(None, description="URL аватара")
    display_name: str | None = Field(None, description="Отображаемое имя")


class MajilaExportRequest(BaseModel):
    """Запрос на экспорт постов"""

    account_id: int = Field(..., description="ID аккаунта Majila")
    post_ids: list[int] = Field(..., description="Список ID постов для экспорта")


class MajilaExportResult(BaseModel):
    """Результат экспорта одного поста"""

    @field_serializer("majila_post_uuid")
    def serialize_uuid(self, value: UUID | None, _info) -> str | None:
        return str(value) if value else None

    post_id: int = Field(..., description="ID поста в Instagram")
    status: str = Field(..., description="Статус экспорта (success, failed, skipped)")
    majila_post_uuid: UUID | None = Field(
        None, description="UUID созданного поста в Majila"
    )
    error_message: str | None = Field(None, description="Сообщение об ошибке")
    exported_at: datetime | None = Field(None, description="Время экспорта")


class MajilaExportSummary(BaseModel):
    """Сводка результатов экспорта"""

    total_posts: int = Field(..., description="Всего постов для экспорта")
    successful: int = Field(..., description="Успешно экспортировано")
    failed: int = Field(..., description="Не удалось экспортировать")
    skipped: int = Field(..., description="Пропущено (уже существуют)")
    results: list[MajilaExportResult] = Field(
        ..., description="Детальные результаты по каждому посту"
    )
