"""Pydantic models for BrightData Instagram API responses."""

from datetime import UTC, datetime
from typing import Any

from pydantic import ConfigDict, Field, field_validator, model_validator

# Импорт новых базовых классов и миксинов
from core.schemas.base import BaseSchema, CommentSchema, ContentSchema, MediaSchema, SocialMediaSchema
from core.schemas.mixins import ValidationHelperMixin
from core.schemas.validators import CommonValidators, InstagramValidators


class MediaItem(MediaSchema):
    """Model for Instagram media items (photos/videos)."""

    model_config = ConfigDict(populate_by_name=True, extra="allow")

    # Переопределяем поля для совместимости с BrightData
    media_id: str = Field(..., alias="id")
    media_type: str = Field(default="photo", alias="type")
    
    @field_validator("duration", mode="before")
    @classmethod
    def parse_duration(cls, v: Any) -> int | None:
        """Parse duration from string or number."""
        if v is None:
            return None
        if isinstance(v, int | float):
            return int(v)
        if isinstance(v, str):
            try:
                # Try to parse as float first (e.g. "45.5")
                return int(float(v))
            except ValueError:
                return None
        return None
    
    @property
    def type(self) -> str:
        """Backward compatibility property."""
        return self.media_type


class InstagramProfileResponse(SocialMediaSchema, ValidationHelperMixin):
    """Model for Instagram profile data from BrightData API."""

    model_config = ConfigDict(
        populate_by_name=True,
        extra="allow"  # Allow extra fields for backward compatibility
    )

    # Основные поля (наследуются от SocialMediaSchema)
    external_id: str = Field(..., alias="pk", serialization_alias="profile_id")  # Переопределяем для BrightData
    full_name: str | None = Field(default="", alias="name")
    biography: str | None = Field(default="", alias="bio")
    
    # Instagram специфичные поля
    follower_count: int = Field(default=0, alias="followers")
    following_count: int = Field(default=0, alias="following")
    post_count: int = Field(default=0, alias="media_count")
    is_verified: bool = Field(default=False)
    is_private: bool = Field(default=False)
    profile_pic_url: str | None = Field(default=None, alias="profile_pic_url_hd")
    external_url: str | None = None
    is_business: bool = Field(default=False, alias="is_business_account")
    category: str | None = Field(default=None, alias="category_name")
    
    # Поля для автоматического извлечения entities (унаследованы от SocialMediaSchema)
    # hashtags, mentions, urls будут автоматически заполняться из biography

    @field_validator("external_url", mode="before")
    @classmethod
    def parse_external_url(cls, v: Any) -> str | None:
        """Parse external URL from various formats."""
        if v is None:
            return None
        if isinstance(v, str):
            return v
        if isinstance(v, list) and v:
            # Return the first URL if it's a list
            return str(v[0])
        return None

    @field_validator("external_id", "username")
    @classmethod
    def not_empty(cls, v: str) -> str:
        """Ensure critical fields are not empty."""
        if not v:
            raise ValueError("Cannot be empty")
        return v
    
    @field_validator("username")
    @classmethod
    def validate_instagram_username(cls, v: str) -> str:
        """Validate Instagram username."""
        if v:
            return InstagramValidators.validate_username(v)
        return v
    
    @field_validator("follower_count", "following_count", "post_count", mode="before")
    @classmethod
    def parse_count_fields(cls, v: Any) -> int:
        """Parse count fields that may come as formatted strings."""
        return CommonValidators.safe_int_conversion(v, default=0)

    @model_validator(mode="before")
    @classmethod
    def handle_field_mappings(cls, data: dict[str, Any]) -> dict[str, Any]:
        """Handle different field name mappings for backward compatibility."""
        if isinstance(data, dict):
            # Handle external_id: can come as 'id' or 'pk' or 'profile_id'
            if "id" in data and "external_id" not in data and "pk" not in data:
                data["pk"] = data["id"]  # Используем alias
            elif "profile_id" in data and "pk" not in data:
                data["pk"] = data["profile_id"]
            
            # Handle username: can come as 'account' or 'username'
            if "account" in data and "username" not in data:
                data["username"] = data["account"]
            
            # Handle post_count: can come as 'posts_count' or 'media_count'
            if "posts_count" in data and "media_count" not in data:
                data["media_count"] = data["posts_count"]
                
        return data
    
    def model_dump(self, **kwargs) -> dict[str, Any]:
        """Override to ensure backward compatible field names."""
        # Check if by_alias is True
        by_alias = kwargs.get("by_alias", False)
        data = super().model_dump(**kwargs)
        
        # When by_alias=True, we need to ensure pk is present and profile_id is removed
        if by_alias:
            # The alias for external_id is 'pk', but it's being returned as 'profile_id'
            # because of our field definition. We need to add 'pk' and remove 'profile_id'
            if "profile_id" in data:
                data["pk"] = data["profile_id"]
                del data["profile_id"]  # Remove profile_id when using aliases
        else:
            # Ensure profile_id and pk are included for backward compatibility
            if "external_id" in data:
                data["profile_id"] = data["external_id"]
                data["pk"] = data["external_id"]
        return data
    
    @property
    def profile_id(self) -> str:
        """Backward compatibility property."""
        return self.external_id
    
    @property 
    def pk(self) -> str:
        """Backward compatibility property."""
        return self.external_id



class InstagramPostResponse(ContentSchema, ValidationHelperMixin):
    """Model for Instagram post data from BrightData API."""

    model_config = ConfigDict(
        populate_by_name=True,
        extra="allow"  # Allow extra fields for backward compatibility
    )

    # Основные поля (наследуются от ContentSchema)
    content_id: str = Field(..., alias="shortcode")  # Переопределяем для BrightData
    external_id: str = Field(..., alias="pk")  # post_id
    post_id: str = Field(..., alias="pk")  # Дублируем для обратной совместимости
    text: str | None = Field(default="", alias="caption")  # description
    description: str | None = Field(default="", alias="caption")  # Дублируем для обратной совместимости
    
    # Instagram специфичные поля
    shortcode: str = Field(..., alias="content_id")
    
    @field_validator("shortcode")
    @classmethod
    def validate_instagram_shortcode(cls, v: str) -> str:
        """Validate Instagram shortcode."""
        if v:
            return InstagramValidators.validate_shortcode(v)
        return v
    
    @field_validator("date_posted", mode="before")
    @classmethod 
    def parse_date_posted_strict(cls, v):
        """Parse date_posted with strict validation."""
        if v is None:
            return None
        # Use the parent's parser first
        result = CommonValidators.parse_timestamp(v)
        # If the input was provided but couldn't be parsed, raise an error
        if v is not None and result is None:
            raise ValueError(f"Invalid date format: {v}")
        return result
    content_type: str = Field(default="Image")
    like_count: int = Field(default=0, alias="likes")
    comment_count: int = Field(default=0, alias="comments")
    view_count: int | None = Field(default=None, alias="views")
    url: str
    is_video: bool = Field(default=False)
    video_duration: int | None = None
    location: str | None = None
    
    # Медиа содержимое
    # Override with same type as base class to maintain compatibility
    # MediaItem inherits from MediaSchema so this is valid
    media: list[MediaSchema] = Field(default_factory=list, alias="post_content")
    thumbnail: str | None = None  # Video thumbnail from top level
    
    # Автор поста
    user_posted: str | None = None  # Username from BrightData
    user_posted_id: str | None = None  # User ID from BrightData
    owner_username: str | None = None  # Alternative field name
    owner_id: str | None = None  # Alternative field name

    # Валидаторы теперь наследуются от миксинов InstagramMixin

    @field_validator("media", mode="before")
    @classmethod
    def convert_media_items(cls, v: Any) -> Any:
        """Convert media items to proper format."""
        if not v:
            return []
        if isinstance(v, list):
            result = []
            for item in v:
                if isinstance(item, dict):
                    # Convert aliases if present
                    converted = {}
                    if "id" in item:
                        converted["media_id"] = item["id"]
                    elif "media_id" in item:
                        converted["media_id"] = item["media_id"]
                    
                    if "type" in item:
                        converted["media_type"] = item["type"]
                    elif "media_type" in item:
                        converted["media_type"] = item["media_type"]
                    
                    # Copy other fields with special handling for duration
                    for key in ["url", "thumbnail_url", "width", "height", "file_size"]:
                        if key in item:
                            converted[key] = item[key]
                    
                    # Handle duration specially
                    if "duration" in item:
                        dur = item["duration"]
                        if isinstance(dur, str):
                            try:
                                converted["duration"] = int(float(dur))
                            except (ValueError, TypeError):
                                converted["duration"] = None
                        else:
                            converted["duration"] = dur
                    
                    result.append(converted)
                else:
                    result.append(item)
            return result
        return v
    
    @field_validator("location", mode="before")
    @classmethod
    def parse_location(cls, v: Any) -> str | None:
        """Parse location from various formats."""
        if not v:
            return None
        if isinstance(v, str):
            return v
        if isinstance(v, dict):
            # Extract name field if it's a dict (e.g., {"name": "Times Square"})
            name = v.get("name")
            return str(name) if name is not None else str(v)
        if isinstance(v, list) and len(v) > 0:
            # If it's a list, join with comma
            return ", ".join(str(item) for item in v)
        return str(v)

    @model_validator(mode="before")
    @classmethod
    def handle_field_mappings(cls, data: dict[str, Any]) -> dict[str, Any]:
        """Handle different field name mappings from BrightData."""
        if isinstance(data, dict):
            # Маппинг полей для новой структуры
            
            # post_id -> external_id (pk alias)
            if "post_id" in data and "pk" not in data:
                data["pk"] = data["post_id"]
            
            # description -> text (caption alias)
            if "description" in data and "caption" not in data:
                data["caption"] = data["description"]
            
            # likes -> likes_count (like_count alias)
            if "likes" in data and "like_count" not in data:
                data["like_count"] = data["likes"]
                
            # num_comments -> comments_count (comment_count alias)
            if "num_comments" in data and "comment_count" not in data:
                data["comment_count"] = data["num_comments"]
            
            # Маппинг username полей
            if "user_posted" in data and "owner_username" not in data:
                data["owner_username"] = data["user_posted"]
            elif "owner_username" in data and "user_posted" not in data:
                data["user_posted"] = data["owner_username"]
                
            # Маппинг user ID полей
            if "user_posted_id" in data and "owner_id" not in data:
                data["owner_id"] = data["user_posted_id"]
            elif "owner_id" in data and "user_posted_id" not in data:
                data["user_posted_id"] = data["owner_id"]
                
            # Маппинг даты
            if "timestamp" in data and "date_posted" not in data:
                data["date_posted"] = data["timestamp"]
            
            # Don't map media here - it's handled by the field alias
            
            # Извлекаем хештеги, упоминания и URL из текста
            caption_text = data.get("caption") or data.get("text") or data.get("description") or ""
            if caption_text:
                # Extract hashtags if not already present
                if "hashtags" not in data or not data["hashtags"]:
                    data["hashtags"] = CommonValidators.extract_hashtags(caption_text)
                # Extract mentions if not already present
                if "mentions" not in data or not data["mentions"]:
                    data["mentions"] = CommonValidators.extract_mentions(caption_text)
                # Extract URLs if not already present
                if "urls" not in data or not data["urls"]:
                    data["urls"] = CommonValidators.extract_urls(caption_text)
                
        return data

    @model_validator(mode="after")
    def post_processing(self) -> "InstagramPostResponse":
        """Post-process the post data."""
        # Определяем тип контента если не указан
        if self.content_type == "Image":  # Default value
            if self.is_video:
                self.content_type = "Video"
            elif self.media:
                if any(m.media_type == "video" for m in self.media):
                    self.content_type = "Video"
                elif len(self.media) > 1:
                    self.content_type = "Carousel"

        return self
    
    def model_dump(self, **kwargs) -> dict[str, Any]:
        """Override to ensure backward compatible field names."""
        data = super().model_dump(**kwargs)
        # Ensure backward compatible field names are included
        if "external_id" in data and "post_id" not in data:
            data["post_id"] = data["external_id"]
        if "text" in data and "description" not in data:
            data["description"] = data["text"]
        if "like_count" in data and "likes" not in data:
            data["likes"] = data["like_count"]
        if "comment_count" in data and "num_comments" not in data:
            data["num_comments"] = data["comment_count"]
        return data
    
    @property
    def likes(self) -> int:
        """Backward compatibility property."""
        return self.like_count
    
    @property
    def num_comments(self) -> int:
        """Backward compatibility property."""
        return self.comment_count
    
    @property
    def post_content(self) -> list[MediaItem]:
        """Backward compatibility property."""
        # Convert media items to MediaItem instances if they're dicts
        if self.media and isinstance(self.media[0], dict):
            return [MediaItem(**item) for item in self.media]
        # Cast MediaSchema items to MediaItem for backward compatibility
        # MediaItem inherits from MediaSchema so this is safe
        return [item if isinstance(item, MediaItem) else MediaItem(**item.model_dump()) 
                for item in self.media]


class InstagramCommentResponse(CommentSchema, ValidationHelperMixin):
    """Model for Instagram comment data from BrightData API."""

    model_config = ConfigDict(
        populate_by_name=True, 
        extra="allow",
        # Override parent's configuration to allow extra fields
        from_attributes=True,
        validate_assignment=True,
        str_strip_whitespace=True,
        arbitrary_types_allowed=False,
        validate_default=True
    )

    # ID комментария
    comment_id: str = Field(..., alias="id")
    
    # Основные поля (наследуются от CommentSchema)
    text: str | None = Field(default="", alias="comment")  # Made optional with default
    author_username: str | None = Field(default="unknown", alias="comment_user")  # Made optional
    date_created: datetime | None = Field(default=None, alias="comment_date")  # Made optional
    like_count: int = Field(default=0, alias="likes_number")
    replies_count: int = Field(default=0, alias="replies_number")
    
    # Instagram специфичные поля
    user_id: str | None = Field(default=None)  # Not provided in BrightData response
    is_verified_author: bool = Field(default=False, alias="is_verified_user")
    has_replies: bool = Field(default=False)
    is_pinned: bool = Field(default=False)
    is_hidden: bool = Field(default=False)

    # Дополнительные BrightData поля
    parent_comment_id: str | None = Field(default=None, alias="reply_to_comment_id")
    user_profile_pic_url: str | None = Field(default=None, alias="profile_pic_url")
    comment_user_url: str | None = Field(default=None)
    tagged_users_in_comment: list[str] | None = Field(default_factory=list)
    post_url: str | None = Field(default=None)
    post_user: str | None = Field(default=None)
    post_id: str | None = Field(default=None)

    # Валидаторы теперь наследуются от миксинов InstagramMixin
    
    @field_validator("date_created", mode="before")
    @classmethod 
    def parse_comment_date(cls, v: Any) -> datetime | None:
        """Parse comment date with flexible handling."""
        if v is None:
            return None
        # Try common validators first
        result = CommonValidators.parse_timestamp(v)
        if result:
            return result
        # If still no result, return current time as fallback
        return datetime.now(UTC)
    
    @model_validator(mode="before")
    @classmethod
    def handle_field_mappings(cls, data: dict[str, Any]) -> dict[str, Any]:
        """Handle different field name mappings from BrightData."""
        if isinstance(data, dict):
            # Маппинг ID комментария
            if "id" in data and "comment_id" not in data:
                data["comment_id"] = data["id"]
            
            # Handle alternative field names from BrightData
            # Text field variations
            if "text" in data and "comment" not in data:
                data["comment"] = data["text"]
            elif "comment_text" in data and "comment" not in data:
                data["comment"] = data["comment_text"]
                
            # Username variations
            if "username" in data and "comment_user" not in data:
                data["comment_user"] = data["username"]
            elif "user" in data and "comment_user" not in data:
                data["comment_user"] = data["user"]
                
            # Date variations  
            if "timestamp" in data and "comment_date" not in data:
                data["comment_date"] = data["timestamp"]
            elif "created_at" in data and "comment_date" not in data:
                data["comment_date"] = data["created_at"]
                
            # Likes variations
            if "likes" in data and "likes_number" not in data:
                data["likes_number"] = data["likes"]
            elif "like_count" in data and "likes_number" not in data:
                data["likes_number"] = data["like_count"]
                
            # Сохраняем оригинальные метаданные
            # Они будут доступны благодаря extra="allow"
            
        return data


class BrightDataError(BaseSchema):
    """Model for BrightData API error responses."""

    error: str
    message: str
    code: str | None = None
    details: dict[str, Any] | None = None

    @classmethod
    def from_exception(cls, e: Exception) -> "BrightDataError":
        """Create error model from exception."""
        return cls(
            error=type(e).__name__,
            message=str(e),
            details={"original_error": repr(e)}
        )
