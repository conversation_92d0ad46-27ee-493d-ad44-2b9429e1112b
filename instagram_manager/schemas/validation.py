"""Custom validators and utilities for Instagram schemas.

This module now primarily contains Instagram-specific validation helpers.
Common validators have been moved to core.schemas.validators.
"""

from typing import Any, TypeVar

from pydantic import BaseModel, ValidationError

# Импорт централизованных валидаторов для удобства использования
from core.schemas.validators import CommonValidators

# Type variable for generic model validation
T = TypeVar("T", bound=BaseModel)


class ValidationHelpers:
    """Helper class for common validation patterns."""
    
    @staticmethod
    def validate_response_list(
        data: list[dict[str, Any]], 
        model_class: type[T],
        skip_errors: bool = True
    ) -> list[T]:
        """Validate a list of responses, optionally skipping errors."""
        validated = []
        errors = []
        
        for idx, item in enumerate(data):
            try:
                validated.append(model_class(**item))
            except ValidationError as e:
                if not skip_errors:
                    raise
                errors.append({
                    "index": idx,
                    "data": item,
                    "error": str(e)
                })
        
        if errors:
            # Log errors or handle as needed
            pass
        
        return validated
    
    @staticmethod
    def extract_media_info(media_data: dict | list[dict]) -> list[dict]:
        """Extract and normalize media information."""
        if not media_data:
            return []
        
        # Single media item
        if isinstance(media_data, dict):
            media_data = [media_data]
        
        normalized = []
        for item in media_data:
            if not isinstance(item, dict):
                continue
            
            media_info = {
                "id": item.get("id") or item.get("pk") or "",
                "type": CommonValidators.normalize_media_type(item.get("type")),
                "url": item.get("url") or item.get("src") or "",
            }
            
            # Optional fields
            if "thumbnail" in item or "thumbnail_url" in item:
                media_info["thumbnail"] = item.get("thumbnail") or item.get("thumbnail_url")
            
            if "duration" in item:
                media_info["duration"] = item["duration"]
            
            if "width" in item:
                media_info["width"] = CommonValidators.safe_int_conversion(item["width"])
            
            if "height" in item:
                media_info["height"] = CommonValidators.safe_int_conversion(item["height"])
            
            if media_info["url"]:  # Only add if we have a URL
                normalized.append(media_info)
        
        return normalized