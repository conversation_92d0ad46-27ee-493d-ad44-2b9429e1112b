"""
Pydantic схемы для валидации данных профилей
"""


from pydantic import BaseModel, ConfigDict, field_validator

from core.schemas.validators import InstagramValidators


class ProfileCreateSchema(BaseModel):
    """Схема для создания профиля"""
    model_config = ConfigDict(str_strip_whitespace=True)
    
    profile_id: str
    username: str
    full_name: str | None = ""
    bio: str | None = ""
    is_business: bool = False
    is_verified: bool = False
    profile_pic_url: str | None = ""
    follower_count: int | None = 0
    following_count: int | None = 0
    post_count: int | None = 0
    external_id: str | None = None
    
    @field_validator("username")
    @classmethod
    def validate_username(cls, v: str) -> str:
        return InstagramValidators.validate_username(v)


class ProfileUpdateSchema(BaseModel):
    """Схема для обновления профиля"""
    model_config = ConfigDict(str_strip_whitespace=True)
    
    full_name: str | None = None
    bio: str | None = None
    is_business: bool | None = None
    is_verified: bool | None = None
    is_private: bool | None = None
    follower_count: int | None = None
    following_count: int | None = None
    post_count: int | None = None
    
    @field_validator("follower_count", "following_count", "post_count")
    @classmethod
    def validate_positive_int(cls, v: int | None) -> int | None:
        if v is not None and v < 0:
            raise ValueError("Count must be non-negative")
        return v


class ProfileFilterSchema(BaseModel):
    """Схема для фильтрации профилей"""
    
    username: str | None = None
    is_business: bool | None = None
    is_verified: bool | None = None
    is_private: bool | None = None
    min_followers: int | None = None
    max_followers: int | None = None
    
    @field_validator("min_followers", "max_followers")
    @classmethod
    def validate_positive_filter(cls, v: int | None) -> int | None:
        if v is not None and v < 0:
            raise ValueError("Filter value must be non-negative")
        return v