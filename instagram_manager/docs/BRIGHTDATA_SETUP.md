# BrightData Setup для Instagram Manager

## Проблема
Для работы с BrightData API нужны правильные идентификаторы коллекторов и корректная настройка API endpoints.

## Настройка BrightData

### 1. Получите API токен
- Войдите в [BrightData Dashboard](https://brightdata.com)
- Перейдите в раздел API Settings
- Скопируйте ваш API токен
- Добавьте его в файл `.env`:
  ```
  BRIGHTDATA_API_TOKEN=ваш_токен_здесь
  ```

### 2. Создайте коллекторы для Instagram
1. В BrightData Dashboard перейдите в раздел "Web Scraper" или "Data Collector"
2. Создайте новые коллекторы для каждого типа данных:
   - Instagram Profile
   - Instagram Posts
   - Instagram Comments
   - Instagram Reels
   - Instagram Hashtag Posts

3. Для каждого коллектора:
   - Выберите "Instagram" как источник
   - Настройте поля для сбора данных
   - Сохраните коллектор

### 3. Получите ID коллекторов
- ID коллектора обычно начинается с `c_` (например: `c_abc123def`)
- Найдите ID в деталях коллектора или в URL при его просмотре

### 4. Обновите настройки в settings.py
```python
BRIGHTDATA_DATASETS = {
    "instagram_profile": "c_YOUR_PROFILE_COLLECTOR_ID",
    "instagram_posts": "c_YOUR_POSTS_COLLECTOR_ID",
    "instagram_comments": "c_YOUR_COMMENTS_COLLECTOR_ID",
    "instagram_reels": "c_YOUR_REELS_COLLECTOR_ID",
    "instagram_hashtag": "c_YOUR_HASHTAG_COLLECTOR_ID",
}
```

## Текущий статус API

### DCA API (Data Collector API)
- Base URL: `https://api.brightdata.com/dca`
- Trigger endpoint: `/trigger?collector={collector_id}`
- Status endpoint: `/dataset/{snapshot_id}`
- Параметр: `collector` (не `dataset_id`)

### Datasets API v3
- Base URL: `https://api.brightdata.com/datasets/v3`
- Trigger endpoint: `/trigger?dataset_id={dataset_id}`
- Status endpoint: `/snapshots/{snapshot_id}`
- Параметр: `dataset_id`

## Решение проблем

### Ошибка 401 Unauthorized
- Проверьте, что токен правильно загружается из .env файла
- Убедитесь, что токен имеет права на использование Web Scraper API

### Ошибка 404 Not Found
- Проверьте правильность ID коллекторов
- Убедитесь, что используете правильный base URL для вашего типа API

### Ошибка "Collector not found"
- ID должен быть в формате `c_xxxxx` для коллекторов
- ID должен быть в формате `gd_xxxxx` для датасетов

## Тестирование

После настройки протестируйте импорт:
1. Перейдите в Django Admin
2. Откройте Instagram Manager > Instagram Scraping Tasks
3. Нажмите "Start Import"
4. Введите username для тестирования (например: "instagram")
5. Проверьте логи на наличие ошибок