# Media File Deletion in Instagram Manager

This document describes how media files are handled when Instagram models are deleted.

## Overview

Instagram Manager now includes automatic cleanup of media files when database records are deleted. This prevents orphaned files from accumulating on disk.

## Automatic Cleanup

### When InstagramMedia is deleted:
- The associated local media file is automatically removed
- For videos, the thumbnail file is also removed
- Empty directories are cleaned up

### When InstagramPost is deleted:
- All associated InstagramMedia records are deleted (CASCADE)
- Media files are cleaned via InstagramMedia pre_delete signal

### When InstagramProfile is deleted:
- All posts are deleted (CASCADE)
- All media files are cleaned via cascading deletions

## Implementation Details

### Signals (instagram_manager/signals.py)

The `pre_delete` signal for InstagramMedia:
1. Checks if a local file exists
2. Deletes the main media file
3. For videos, also deletes the thumbnail

### PostHandler Updates

The `_process_media` method now:
1. Preserves existing media when updating posts
2. Only deletes media that no longer exists in the post
3. Properly triggers deletion signals

### Management Command

Use the cleanup command to maintain media storage:

```bash
# Check media integrity
python manage.py cleanup_instagram_media --check-integrity

# Fix missing files (mark as not downloaded)
python manage.py cleanup_instagram_media --fix-missing

# Clean orphaned files (dry run)
python manage.py cleanup_instagram_media --dry-run

# Clean orphaned files and empty directories
python manage.py cleanup_instagram_media
```

## Best Practices

1. **Regular Cleanup**: Run the cleanup command periodically (e.g., weekly)
2. **Monitor Disk Usage**: Check media directory size regularly
3. **Backup Before Cleanup**: Always backup media before running cleanup in production

## Troubleshooting

### Files not being deleted
- Check Django logs for error messages
- Verify file permissions
- Ensure signals are properly registered in apps.py

### Orphaned files accumulating
- Run cleanup command regularly
- Check if deletions are happening through Django ORM (not raw SQL)

### Performance issues
- Consider running cleanup during off-peak hours
- Use --dry-run first to assess impact

## Migration from Previous Version

If upgrading from a version without automatic cleanup:

1. Backup all media files
2. Run `cleanup_instagram_media --check-integrity`
3. Fix any integrity issues with `--fix-missing`
4. Clean orphaned files with `cleanup_instagram_media`

## Configuration

No additional configuration required. The cleanup happens automatically when:
- Using Django ORM for deletions
- Signals are properly loaded (apps.py configured)