"""
Repository для работы с Instagram комментариями.
"""

from typing import Any

from django.db.models import Avg, Count, Q, QuerySet

from core.repositories.base import BaseRepository
from core.repositories.mixins import (
    BulkOperationsMixin,
    DateFilterMixin,
    SearchMixin,
    SoftDeleteMixin,
    StatisticsMixin,
)
from instagram_manager.models import InstagramComment, InstagramPost


class InstagramCommentRepository(
    BaseRepository[InstagramComment],
    BulkOperationsMixin,
    DateFilterMixin,
    SearchMixin,
    SoftDeleteMixin,
    StatisticsMixin,
):
    """Repository для Instagram комментариев."""
    
    model = InstagramComment
    
    # Оптимизация запросов
    select_related_fields = ["post", "post__profile", "reply_to"]
    prefetch_related_fields = []
    
    def get_by_external_id(self, external_id: str) -> InstagramComment | None:
        """
        Получить комментарий по external_id.
        
        Args:
            external_id: ID комментария
            
        Returns:
            Комментарий или None
        """
        return self.filter(external_id=external_id).first()
    
    def filter_by_post(self, post: InstagramPost, include_hidden: bool = False) -> list[InstagramComment]:
        """
        Получить комментарии поста.
        
        Args:
            post: Пост
            include_hidden: Включать скрытые комментарии
            
        Returns:
            Список комментариев отсортированный по времени
        """
        queryset = self.filter(post=post)
        if not include_hidden:
            queryset = queryset.filter(is_hidden=False)
        queryset = self._apply_optimizations(queryset)
        return list(queryset.order_by("-commented_at"))
    
    def filter_by_author(self, username: str) -> list[InstagramComment]:
        """
        Получить комментарии пользователя.
        
        Args:
            username: Username автора
            
        Returns:
            Список комментариев
        """
        return list(self.filter(author_username=username).order_by("-commented_at"))
    
    def filter_top_level_comments(self, post: InstagramPost = None, include_hidden: bool = False) -> list[InstagramComment]:
        """
        Получить комментарии верхнего уровня (не ответы).
        
        Args:
            post: Пост для фильтрации (опционально)
            include_hidden: Включать скрытые комментарии
            
        Returns:
            Список комментариев верхнего уровня
        """
        queryset = self.filter(reply_to__isnull=True)
        
        if not include_hidden:
            queryset = queryset.filter(is_hidden=False)
        
        if post:
            queryset = queryset.filter(post=post)
        
        queryset = self._apply_optimizations(queryset)
        return list(queryset.order_by("-commented_at"))
    
    def filter_replies(self, comment: InstagramComment, include_hidden: bool = False) -> list[InstagramComment]:
        """
        Получить ответы на комментарий.
        
        Args:
            comment: Родительский комментарий
            include_hidden: Включать скрытые комментарии
            
        Returns:
            Список ответов
        """
        queryset = self.filter(reply_to=comment)
        if not include_hidden:
            queryset = queryset.filter(is_hidden=False)
        return list(queryset.order_by("commented_at"))
    
    def filter_verified_authors(self) -> list[InstagramComment]:
        """
        Получить комментарии от верифицированных авторов.
        
        Returns:
            Список комментариев
        """
        return list(self.filter(author_is_verified=True))
    
    def search_in_text(self, query: str) -> list[InstagramComment]:
        """
        Поиск по тексту комментариев.
        
        Args:
            query: Поисковый запрос
            
        Returns:
            Список найденных комментариев
        """
        # Используем search из SearchMixin
        queryset = self.search(query, search_fields=["text"])
        return list(queryset)
    
    def get_popular_comments(self, post: InstagramPost = None, 
                           min_likes: int = 10,
                           include_hidden: bool = False) -> list[InstagramComment]:
        """
        Получить популярные комментарии.
        
        Args:
            post: Пост для фильтрации (опционально)
            min_likes: Минимальное количество лайков
            
        Returns:
            Список популярных комментариев
        """
        queryset = self.filter(like_count__gte=min_likes)
        
        if not include_hidden:
            queryset = queryset.filter(is_hidden=False)
        
        if post:
            queryset = queryset.filter(post=post)
        
        queryset = self._apply_optimizations(queryset)
        return list(queryset.order_by("-like_count"))
    
    def get_comment_tree(self, post: InstagramPost, include_hidden: bool = False) -> list[dict[str, Any]]:
        """
        Получить дерево комментариев для поста.
        
        Args:
            post: Пост
            include_hidden: Включать скрытые комментарии
            
        Returns:
            Список комментариев с вложенными ответами
        """
        # Получаем все комментарии поста
        queryset = self.filter(post=post)
        if not include_hidden:
            queryset = queryset.filter(is_hidden=False)
        
        # Используем оптимизацию
        queryset = self._apply_optimizations(queryset)
        all_comments = queryset.order_by("commented_at")
        
        # Строим дерево
        tree = []
        replies_map: dict[int, list[InstagramComment]] = {}
        
        # Сначала группируем ответы по родительским комментариям
        for comment in all_comments:
            if comment.reply_to_id:
                if comment.reply_to_id not in replies_map:
                    replies_map[comment.reply_to_id] = []
                replies_map[comment.reply_to_id].append(comment)
        
        # Затем строим дерево
        for comment in all_comments:
            if comment.reply_to_id is None:
                # Комментарий верхнего уровня
                comment_data = {
                    "comment": comment,
                    "replies": replies_map.get(comment.id, [])
                }
                tree.append(comment_data)
                
        return tree
    
    def bulk_create_or_update(self, comments_data: list[dict[str, Any]], 
                            post: InstagramPost) -> tuple[int, int]:
        """
        Массовое создание или обновление комментариев.
        
        Args:
            comments_data: Список данных комментариев
            post: Пост
            
        Returns:
            Кортеж (количество созданных, количество обновленных)
        """
        created_count = 0
        updated_count = 0
        
        # Сначала создаем комментарии верхнего уровня
        for comment_data in comments_data:
            if comment_data.get("reply_to"):
                continue
                
            comment_id = comment_data.get("external_id")
            if not comment_id:
                continue
                
            # Создаем копию данных, чтобы не изменять оригинал
            data_copy = comment_data.copy()
            data_copy["post"] = post
            
            comment, created = self.model.objects.update_or_create(
                external_id=comment_id,
                defaults=data_copy
            )
            
            if created:
                created_count += 1
            else:
                updated_count += 1
        
        # Затем создаем ответы
        for comment_data in comments_data:
            reply_to_id = comment_data.get("reply_to")
            if not reply_to_id:
                continue
                
            comment_id = comment_data.get("external_id")
            if not comment_id:
                continue
                
            # Находим родительский комментарий
            parent = self.get_by_external_id(reply_to_id)
            if parent:
                # Создаем копию данных, чтобы не изменять оригинал
                data_copy = comment_data.copy()
                data_copy["post"] = post
                data_copy["reply_to"] = parent
                
                comment, created = self.model.objects.update_or_create(
                    external_id=comment_id,
                    defaults=data_copy
                )
                
                if created:
                    created_count += 1
                else:
                    updated_count += 1
                    
        return created_count, updated_count
    
    def get_comment_stats(self, post: InstagramPost = None) -> dict[str, Any]:
        """
        Получить статистику комментариев.
        
        Args:
            post: Пост для фильтрации (опционально)
            
        Returns:
            Словарь со статистикой
        """
        queryset = self.all()
        if post:
            queryset = queryset.filter(post=post)
            
        stats = queryset.aggregate(
            total_comments=Count("id"),
            total_replies=Count("id", filter=Q(reply_to__isnull=False)),
            avg_likes=Avg("like_count"),
            verified_authors=Count("id", filter=Q(author_is_verified=True))
        )
        
        stats["top_level_comments"] = stats["total_comments"] - stats["total_replies"]
        
        # Топ комментаторы
        top_commenters = (
            queryset
            .values("author_username")
            .annotate(comment_count=Count("id"))
            .order_by("-comment_count")[:5]
        )
        stats["top_commenters"] = list(top_commenters)
        
        return stats
    
    def get_engagement_comments(self, post: InstagramPost) -> list[InstagramComment]:
        """
        Получить комментарии, способствующие вовлечению.
        
        Это комментарии с вопросами, призывами к действию и т.д.
        
        Args:
            post: Пост
            
        Returns:
            Список комментариев
        """
        engagement_keywords = ["?", "как", "где", "когда", "почему", 
                              "расскажи", "поделись", "научи"]
        
        query = Q()
        for keyword in engagement_keywords:
            query |= Q(text__icontains=keyword)
        
        queryset = self.filter(post=post).filter(query).filter(is_hidden=False)
        queryset = self._apply_optimizations(queryset)
        return list(queryset)
    
    def get_comment_thread(self, comment: InstagramComment) -> list[InstagramComment]:
        """
        Получить всю ветку комментариев начиная с данного.
        
        Args:
            comment: Корневой комментарий ветки
            
        Returns:
            Список всех комментариев в ветке
        """
        thread = [comment]
        replies = self.filter_replies(comment, include_hidden=True)
        
        for reply in replies:
            thread.extend(self.get_comment_thread(reply))
            
        return thread
    
    def get_comments_by_authors(self, authors: list[str], 
                               post: InstagramPost = None) -> list[InstagramComment]:
        """
        Получить комментарии от списка авторов.
        
        Args:
            authors: Список usernames авторов
            post: Пост для фильтрации (опционально)
            
        Returns:
            Список комментариев
        """
        queryset = self.filter(author_username__in=authors, is_hidden=False)
        
        if post:
            queryset = queryset.filter(post=post)
        
        queryset = self._apply_optimizations(queryset)
        return list(queryset.order_by("-commented_at"))
    
    def get_pinned_comments(self, post: InstagramPost) -> list[InstagramComment]:
        """
        Получить закрепленные комментарии поста.
        
        Args:
            post: Пост
            
        Returns:
            Список закрепленных комментариев
        """
        queryset = self.filter(post=post, is_pinned=True, is_hidden=False)
        queryset = self._apply_optimizations(queryset)
        return list(queryset.order_by("commented_at"))
    
    # Light методы для оптимизации производительности
    
    def get_comments_tree_light(self, post: InstagramPost) -> QuerySet[InstagramComment]:
        """
        Получить легкое дерево комментариев без тяжелых полей.
        
        Загружает только: id, comment_id, text, author_username, 
        like_count, reply_to_id, commented_at.
        
        Args:
            post: Пост
            
        Returns:
            QuerySet для построения дерева
        """
        only_fields = [
            "id", "comment_id", "text", "author_username",
            "author_is_verified", "like_count", "reply_to_id",
            "commented_at", "is_pinned", "is_hidden"
        ]
        
        return self.filter(
            post=post,
            is_hidden=False,
            only_fields=only_fields
        ).select_related("reply_to").order_by("commented_at")
    
    def get_comments_list_light(self) -> QuerySet[InstagramComment]:
        """
        Получить легкий список комментариев для отображения.
        
        Returns:
            QuerySet с минимальными полями
        """
        only_fields = [
            "id", "comment_id", "text", "author_username",
            "like_count", "commented_at", "post_id"
        ]
        
        return self.filter(only_fields=only_fields).select_related("post")
    
    def get_comments_for_export(self, post: InstagramPost) -> QuerySet[InstagramComment]:
        """
        Получить комментарии для экспорта без служебных полей.
        
        Args:
            post: Пост
            
        Returns:
            QuerySet для экспорта
        """
        defer_fields = ["author_profile_pic", "author_id"]
        
        return self.filter(
            post=post,
            is_hidden=False,
            defer_fields=defer_fields
        ).order_by("-commented_at")
    
    def get_comments_metrics_only(self, post: InstagramPost | None = None) -> QuerySet[InstagramComment]:
        """
        Получить только метрики комментариев для аналитики.
        
        Args:
            post: Пост для фильтрации (опционально)
            
        Returns:
            QuerySet с метриками
        """
        only_fields = [
            "id", "like_count", "author_is_verified",
            "is_pinned", "reply_to_id", "commented_at"
        ]
        
        queryset = self.filter(only_fields=only_fields)
        
        if post:
            queryset = queryset.filter(post=post)
            
        return queryset