"""
Repository для работы с Instagram хештегами.
"""

from datetime import timed<PERSON>ta
from typing import Any

from django.db.models import Count, F, Q
from django.utils import timezone

from core.repositories.base import BaseRepository
from core.repositories.mixins import (
    BulkOperationsMixin,
    DateFilterMixin,
    SearchMixin,
    StatisticsMixin,
)
from instagram_manager.models import InstagramHashtag, InstagramPost


class InstagramHashtagRepository(
    BaseRepository[InstagramHashtag],
    BulkOperationsMixin,
    DateFilterMixin,
    SearchMixin,
    StatisticsMixin,
):
    """Repository для Instagram хештегов."""
    
    model = InstagramHashtag
    
    def get_by_name(self, name: str) -> InstagramHashtag | None:
        """
        Получить хештег по имени.
        
        Args:
            name: <PERSON><PERSON>я хештега (без #)
            
        Returns:
            Хештег или None
        """
        # Очищаем имя от # если есть
        clean_name = name.lstrip("#").lower()
        return self.filter(name=clean_name).first()
    
    def get_or_create_hashtag(self, name: str) -> tuple[InstagramHashtag, bool]:
        """
        Получить или создать хештег.
        
        Args:
            name: Имя хештега
            
        Returns:
            Кортеж (хештег, создан ли новый)
        """
        clean_name = name.lstrip("#").lower()
        hashtag, created = InstagramHashtag.objects.get_or_create(
            name=clean_name,
            defaults={"post_count": 0}
        )
        
        return hashtag, created
    
    def filter_popular(self, min_posts: int = 100) -> list[InstagramHashtag]:
        """
        Получить популярные хештеги.
        
        Args:
            min_posts: Минимальное количество постов
            
        Returns:
            Список популярных хештегов
        """
        return list(self.filter(post_count__gte=min_posts).order_by("-post_count"))
    
    def filter_trending(self, days: int = 7, min_growth: int = 10) -> list[InstagramHashtag]:
        """
        Получить трендовые хештеги.
        
        Args:
            days: Период для анализа в днях
            min_growth: Минимальный рост количества постов
            
        Returns:
            Список трендовых хештегов
        """
        cutoff_date = timezone.now() - timedelta(days=days)
        
        # Подсчитываем посты за период
        trending = self.model.objects.annotate(
            recent_posts=Count(
                "posts",
                filter=Q(posts__posted_at__gte=cutoff_date)
            )
        ).filter(recent_posts__gte=min_growth).order_by("-recent_posts")
        
        return list(trending)
    
    def search_hashtags(self, query: str) -> list[InstagramHashtag]:
        """
        Поиск хештегов используя SearchMixin.
        
        Args:
            query: Поисковый запрос
            
        Returns:
            Список найденных хештегов
        """
        clean_query = query.lstrip("#").lower()
        # Используем search из SearchMixin
        return list(self.search(clean_query, search_fields=["name"]).order_by("-post_count"))
    
    def get_related_hashtags(self, hashtag: InstagramHashtag, 
                           limit: int = 10) -> list[InstagramHashtag]:
        """
        Получить связанные хештеги.
        
        Находит хештеги, которые часто используются вместе.
        
        Args:
            hashtag: Исходный хештег
            limit: Максимальное количество
            
        Returns:
            Список связанных хештегов
        """
        # Получаем посты с данным хештегом
        posts_with_hashtag = InstagramPost.objects.filter(
            hashtags=hashtag
        ).values_list("id", flat=True)
        
        # Находим другие хештеги в этих постах
        related = (
            self.model.objects.exclude(id=hashtag.id)
            .filter(posts__id__in=posts_with_hashtag)
            .annotate(
                co_occurrence=Count("posts")
            )
            .order_by("-co_occurrence")[:limit]
        )
        
        return list(related)
    
    def get_hashtags_needing_update(self, hours: int = 24) -> list[InstagramHashtag]:
        """
        Получить хештеги, требующие обновления используя DateFilterMixin.
        
        Args:
            hours: Количество часов с последнего обновления
            
        Returns:
            Список хештегов для обновления
        """
        # Используем get_stale_objects из DateFilterMixin
        stale = self.get_stale_objects(field="last_scraped_at", hours=hours)
        return list(stale.filter(post_count__gt=0))  # Только используемые хештеги
    
    def update_post_count(self, hashtag: InstagramHashtag) -> InstagramHashtag:
        """
        Обновить количество постов для хештега.
        
        Args:
            hashtag: Хештег
            
        Returns:
            Обновленный хештег
        """
        actual_count = hashtag.posts.count()
        hashtag.post_count = actual_count
        hashtag.save(update_fields=["post_count"])
        
        return hashtag
    
    def bulk_update_post_count(self) -> int:
        """
        Массовое обновление количества постов для всех хештегов.
        
        Returns:
            Количество обновленных хештегов
        """
        hashtags = self.model.objects.annotate(
            actual_count=Count("posts")
        ).exclude(
            post_count=F("actual_count")
        )
        
        updated_count = 0
        for hashtag in hashtags:
            hashtag.post_count = hashtag.actual_count
            hashtag.save(update_fields=["post_count"])
            updated_count += 1
        
        return updated_count
    
    def extract_and_save_hashtags(self, text: str, post: InstagramPost) -> list[InstagramHashtag]:
        """
        Извлечь хештеги из текста и связать с постом.
        
        Args:
            text: Текст для анализа (обычно caption)
            post: Пост
            
        Returns:
            Список найденных хештегов
        """
        import re
        
        # Находим все хештеги в тексте
        hashtag_pattern = r"#[a-zA-Z0-9а-яА-Я_]+"
        found_hashtags = re.findall(hashtag_pattern, text)
        
        hashtags = []
        for tag_name in found_hashtags:
            hashtag, created = self.get_or_create_hashtag(tag_name)
            
            # Связываем с постом
            hashtag.posts.add(post)
            
            # Обновляем счетчик
            if created:
                hashtag.post_count = 1
            else:
                hashtag.post_count = F("post_count") + 1
            hashtag.save(update_fields=["post_count"])
            
            hashtags.append(hashtag)
            
        return hashtags
    
    def get_hashtag_groups(self, min_co_occurrence: int = 5) -> list[dict[str, Any]]:
        """
        Получить группы часто используемых вместе хештегов.
        
        Args:
            min_co_occurrence: Минимальное количество совместных появлений
            
        Returns:
            Список групп хештегов
        """
        # Это упрощенная версия. В реальности нужен более сложный алгоритм
        groups = []
        processed_hashtags = set()
        
        for hashtag in self.filter_popular(min_posts=50):
            if hashtag.id in processed_hashtags:
                continue
                
            # Находим связанные хештеги
            related = self.get_related_hashtags(hashtag, limit=5)
            
            if len(related) >= 2:
                group = {
                    "main_hashtag": hashtag,
                    "related_hashtags": related,
                    "total_posts": hashtag.post_count + sum(h.post_count for h in related)
                }
                groups.append(group)
                
                # Отмечаем как обработанные
                processed_hashtags.add(hashtag.id)
                for h in related:
                    processed_hashtags.add(h.id)
                    
        return groups
    
    def get_hashtag_stats(self) -> dict[str, Any]:
        """
        Получить общую статистику по хештегам используя StatisticsMixin.
        
        Returns:
            Словарь со статистикой
        """
        # Calculate statistics directly using aggregation
        from django.db.models import Count
        
        stats = self.model.objects.aggregate(
            total_hashtags=Count("id"),
            total_posts=Count("posts", distinct=True)
        )
        
        # Calculate average posts per hashtag
        if stats["total_hashtags"] > 0:
            stats["avg_posts_per_hashtag"] = stats["total_posts"] / stats["total_hashtags"]
        else:
            stats["avg_posts_per_hashtag"] = 0
        
        # Топ хештеги
        stats["top_hashtags"] = list(
            self.model.objects.order_by("-post_count")[:10]
            .values("name", "post_count")
        )
        
        # Распределение по популярности используя get_statistics
        distribution_stats: list[dict[str, Any]] = [
            {"category": "rare", "filter": {"post_count__lt": 10}},
            {"category": "common", "filter": {"post_count__gte": 10, "post_count__lt": 100}},
            {"category": "popular", "filter": {"post_count__gte": 100, "post_count__lt": 1000}},
            {"category": "viral", "filter": {"post_count__gte": 1000}}
        ]
        
        stats["popularity_distribution"] = {}
        for dist in distribution_stats:
            count = self.filter(**dist["filter"]).count()
            stats["popularity_distribution"][dist["category"]] = count
        
        return stats