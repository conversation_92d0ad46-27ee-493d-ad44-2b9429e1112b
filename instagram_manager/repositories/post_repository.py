"""
Repository для работы с Instagram постами.
"""

from datetime import datetime
from typing import Any

from django.db.models import Avg, Count, F, Sum

from core.repositories.base import BaseRepository
from core.repositories.mixins import (
    BulkOperationsMixin,
    DateFilterMixin,
    SearchMixin,
    SoftDeleteMixin,
    StatisticsMixin,
)
from instagram_manager.models import InstagramPost, InstagramProfile


class InstagramPostRepository(
    BaseRepository[InstagramPost],
    BulkOperationsMixin,
    DateFilterMixin,
    SearchMixin,
    SoftDeleteMixin,
    StatisticsMixin,
):
    """Repository для Instagram постов."""
    
    model = InstagramPost
    
    # Оптимизация запросов
    select_related_fields = ["profile"]
    prefetch_related_fields = ["media", "hashtags", "comments"]
    
    def get_by_external_id(self, external_id: str) -> InstagramPost | None:
        """
        Получить пост по external_id.
        
        Args:
            external_id: ID поста
            
        Returns:
            Пост или None
        """
        return self.filter(external_id=external_id).first()
    
    def get_by_shortcode(self, shortcode: str) -> InstagramPost | None:
        """
        Получить пост по shortcode.
        
        Args:
            shortcode: Shortcode поста
            
        Returns:
            Пост или None
        """
        return self.filter(shortcode=shortcode).first()
    
    def filter_by_profile(self, profile: InstagramProfile) -> list[InstagramPost]:
        """
        Получить посты профиля.
        
        Args:
            profile: Профиль
            
        Returns:
            Список постов
        """
        queryset = self.filter(profile=profile)
        queryset = self._apply_optimizations(queryset)
        return list(queryset.order_by("-posted_at"))
    
    def filter_by_type(self, post_type: str) -> list[InstagramPost]:
        """
        Фильтровать посты по типу.
        
        Args:
            post_type: Тип поста (photo, video, carousel, reel)
            
        Returns:
            Список постов
        """
        queryset = self.filter(post_type=post_type)
        queryset = self._apply_optimizations(queryset)
        return list(queryset)
    
    def filter_posts_by_date(self, date_from: datetime | None = None, 
                           date_to: datetime | None = None) -> list[InstagramPost]:
        """
        Фильтровать посты по диапазону дат.
        
        Args:
            date_from: Начальная дата
            date_to: Конечная дата
            
        Returns:
            Список постов
        """
        # Build queryset manually since there's a signature conflict
        queryset = self.all(optimized=True)
        if date_from:
            queryset = queryset.filter(posted_at__gte=date_from)
        if date_to:
            queryset = queryset.filter(posted_at__lte=date_to)
        return list(queryset.order_by("-posted_at"))
    
    
    
    def search_by_caption(self, query: str) -> list[InstagramPost]:
        """
        Поиск постов по тексту в caption.
        
        Args:
            query: Поисковый запрос
            
        Returns:
            Список найденных постов
        """
        # Используем search из SearchMixin
        queryset = self.search(query, search_fields=["caption"])
        return list(queryset)
    
    def get_top_posts(self, limit: int = 10, metric: str = "likes") -> list[InstagramPost]:
        """
        Получить топ постов по метрике.
        
        Args:
            limit: Количество постов
            metric: Метрика (likes, comments, engagement)
            
        Returns:
            Список топ постов
        """
        queryset = self.all()
        
        if metric == "likes":
            return list(queryset.order_by("-like_count")[:limit])
        elif metric == "comments":
            return list(queryset.order_by("-comment_count")[:limit])
        elif metric == "engagement":
            return list(
                queryset.annotate(
                    engagement=F("like_count") + F("comment_count")
                ).order_by("-engagement")[:limit]
            )
        else:
            raise ValueError(f"Unknown metric: {metric}")
    
    
    
    # Используем bulk_create_or_update из BulkOperationsMixin
    def bulk_create_or_update_posts(self, posts_data: list[dict[str, Any]]) -> tuple[int, int]:
        """
        Массовое создание или обновление постов по external_id.
        
        Args:
            posts_data: Список данных постов
            
        Returns:
            Кортеж (количество созданных, количество обновленных)
        """
        # Предварительная обработка данных для FK
        processed_data = []
        for post_data in posts_data:
            data = post_data.copy()
            
            # Извлекаем profile отдельно, так как это FK
            profile_data = data.pop("profile", None)
            if isinstance(profile_data, dict):
                try:
                    profile = InstagramProfile.objects.get(
                        profile_id=profile_data.get("profile_id")
                    )
                    data["profile"] = profile
                except InstagramProfile.DoesNotExist:
                    self.logger.warning(
                        f"Profile {profile_data.get('profile_id')} not found for post {data.get('external_id')}"
                    )
                    continue
                    
            processed_data.append(data)
        
        # Используем метод из миксина с external_id как lookup field
        return self.bulk_create_or_update(processed_data, lookup_field="external_id")
    
    def get_engagement_stats(self, profile: InstagramProfile = None) -> dict[str, Any]:
        """
        Получить статистику engagement.
        
        Args:
            profile: Профиль для фильтрации (опционально)
            
        Returns:
            Словарь со статистикой
        """
        queryset = self.all()
        if profile:
            queryset = queryset.filter(profile=profile)
        
        # Для агрегаций не нужна оптимизация с prefetch
            
        stats = queryset.aggregate(
            total_posts=Count("id"),
            total_likes=Sum("like_count"),
            total_comments=Sum("comment_count"),
            avg_likes=Avg("like_count"),
            avg_comments=Avg("comment_count"),
            total_views=Sum("view_count")
        )
        
        # Добавляем engagement rate
        if stats["total_likes"] and stats["total_comments"]:
            stats["avg_engagement"] = (
                (stats["avg_likes"] or 0) + (stats["avg_comments"] or 0)
            )
        else:
            stats["avg_engagement"] = 0
            
        return stats
    
    def update(self, pk: Any, **kwargs) -> InstagramPost | None:
        """
        Обновить пост с инвалидацией кеша.
        
        Args:
            pk: Primary key поста
            **kwargs: Поля для обновления
            
        Returns:
            Обновленный пост или None
        """
        # Получаем пост до обновления для инвалидации кеша
        post = self.get_by_id(pk)
        if not post:
            return None
            
        # Обновляем пост
        result = super().update(pk, **kwargs)
        
        return result
    
    # Light методы для оптимизации производительности
    
    
    
    
