from typing import cast

from django.db.models import Q, QuerySet

from core.repositories.base import BaseRepository
from core.repositories.mixins import (
    DateFilterMixin,
    SearchMixin,
    SoftDeleteMixin,
)
from instagram_manager.models import InstagramProfile, MajilaServiceAccount


class MajilaAccountRepository(
    BaseRepository[MajilaServiceAccount],
    DateFilterMixin,
    SearchMixin,
    SoftDeleteMixin,
):
    """Репозиторий для работы с аккаунтами Majila"""
    model = MajilaServiceAccount
    
    def get_active_accounts(self) -> QuerySet[MajilaServiceAccount]:
        """Получить все активные аккаунты"""
        return self.filter(is_active=True).select_related("instagram_profile")
    
    def get_by_instagram_profile(self, profile: InstagramProfile) -> QuerySet[MajilaServiceAccount]:
        """Получить аккаунты Majila для конкретного Instagram профиля"""
        return self.filter(instagram_profile=profile).order_by("-created_at")
    
    def get_by_username(self, username: str) -> MajilaServiceAccount | None:
        """Получить аккаунт по имени пользователя Majila"""
        return cast(MajilaServiceAccount | None, self.filter(username=username).first())
    
    def get_authenticated_accounts(self) -> QuerySet[MajilaServiceAccount]:
        """Получить аккаунты с валидными токенами"""
        return self.filter(
            is_active=True,
            access_token__isnull=False,
            user_uuid__isnull=False
        ).select_related("instagram_profile")
    
    def get_or_create_account(
        self, 
        instagram_profile: InstagramProfile,
        username: str,
        password: str | None = None
    ) -> tuple[MajilaServiceAccount, bool]:
        """
        Получить или создать аккаунт Majila
        
        Args:
            instagram_profile: Профиль Instagram
            username: Имя пользователя Majila
            password: Пароль (только для создания нового аккаунта)
            
        Returns:
            Кортеж (аккаунт, создан ли новый)
        """
        account, created = self.get_or_create(
            instagram_profile=instagram_profile,
            username=username,
            defaults={"password": password} if password else {}
        )
        return account, created
    
    def deactivate_account(self, account: MajilaServiceAccount) -> MajilaServiceAccount:
        """Деактивировать аккаунт"""
        account.is_active = False
        account.save(update_fields=["is_active", "updated_at"])
        return account
    
    def update_auth_data(
        self, 
        account: MajilaServiceAccount,
        access_token: str,
        user_uuid: str
    ) -> MajilaServiceAccount:
        """
        Обновить данные авторизации
        
        Args:
            account: Аккаунт для обновления
            access_token: Новый токен доступа
            user_uuid: UUID пользователя
            
        Returns:
            Обновленный аккаунт
        """
        account.access_token = access_token
        account.user_uuid = user_uuid
        account.auth_error = ""
        account.save(update_fields=["access_token", "user_uuid", "auth_error", "updated_at"])
        return account
    
    def search_accounts(self, query: str) -> QuerySet[MajilaServiceAccount]:
        """
        Поиск аккаунтов по различным полям
        
        Args:
            query: Поисковый запрос
            
        Returns:
            QuerySet с найденными аккаунтами
        """
        return cast(
            QuerySet[MajilaServiceAccount],
            self.model.objects.filter(
                Q(username__icontains=query) |
                Q(instagram_profile__username__icontains=query) |
                Q(instagram_profile__full_name__icontains=query)
            ).select_related("instagram_profile").distinct()
        )