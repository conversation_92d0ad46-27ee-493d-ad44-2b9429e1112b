"""
Repository для работы с Instagram медиа.
"""

from typing import Any

from django.db.models import Count, Q, QuerySet

from core.repositories.base import BaseRepository
from core.repositories.mixins import (
    BulkOperationsMixin,
    SearchMixin,
    StatisticsMixin,
)
from instagram_manager.models import InstagramMedia, InstagramPost


class InstagramMediaRepository(
    BaseRepository[InstagramMedia],
    BulkOperationsMixin,
    SearchMixin,
    StatisticsMixin,
):
    """Repository для Instagram медиа."""
    
    model = InstagramMedia
    
    # Оптимизация запросов
    select_related_fields = ["post", "post__profile"]
    prefetch_related_fields = []
    
    def get_by_external_id(self, external_id: str) -> InstagramMedia | None:
        """
        Получить медиа по external_id.
        
        Args:
            external_id: ID медиа
            
        Returns:
            Медиа или None
        """
        return self.filter(external_id=external_id).first()
    
    def filter_by_post(self, post: InstagramPost) -> list[InstagramMedia]:
        """
        Получить все медиа поста.
        
        Args:
            post: Пост
            
        Returns:
            Список медиа отсортированный по order_index
        """
        queryset = self.filter(post=post)
        queryset = self._apply_optimizations(queryset)
        return list(queryset.order_by("order_index"))
    
    def filter_by_type(self, media_type: str) -> list[InstagramMedia]:
        """
        Фильтровать медиа по типу.
        
        Args:
            media_type: Тип медиа (photo, video)
            
        Returns:
            Список медиа
        """
        return list(self.filter(media_type=media_type))
    
    def filter_downloaded(self) -> list[InstagramMedia]:
        """
        Получить загруженные медиа.
        
        Returns:
            Список загруженных медиа
        """
        queryset = self.filter(is_downloaded=True)
        queryset = self._apply_optimizations(queryset)
        return list(queryset)
    
    def filter_not_downloaded(self) -> list[InstagramMedia]:
        """
        Получить не загруженные медиа.
        
        Returns:
            Список не загруженных медиа
        """
        queryset = self.filter(is_downloaded=False)
        queryset = self._apply_optimizations(queryset)
        return list(queryset)
    
    def filter_failed_downloads(self) -> list[InstagramMedia]:
        """
        Получить медиа с ошибками загрузки.
        
        Returns:
            Список медиа с ошибками
        """
        queryset = self.filter(
            is_downloaded=False
        ).exclude(download_error="")
        queryset = self._apply_optimizations(queryset)
        return list(queryset)
    
    def filter_videos_without_thumbnails(self) -> list[InstagramMedia]:
        """
        Получить видео без миниатюр.
        
        Returns:
            Список видео без миниатюр
        """
        queryset = self.filter(
            media_type="video",
            thumbnail_url__isnull=True
        )
        queryset = self._apply_optimizations(queryset)
        return list(queryset)
    
    def get_media_for_download(self, limit: int = 100) -> list[InstagramMedia]:
        """
        Получить медиа для загрузки.
        
        Args:
            limit: Максимальное количество
            
        Returns:
            Список медиа для загрузки
        """
        queryset = self.filter(
            is_downloaded=False,
            download_error=""
        )
        queryset = self._apply_optimizations(queryset)
        return list(queryset[:limit])
    
    def mark_as_downloaded(self, media: InstagramMedia, 
                          local_path: str) -> InstagramMedia:
        """
        Отметить медиа как загруженное.
        
        Args:
            media: Медиа объект
            local_path: Путь к локальному файлу
            
        Returns:
            Обновленный медиа объект
        """
        media.is_downloaded = True
        media.local_path = local_path
        media.download_error = ""
        media.save(update_fields=["is_downloaded", "local_path", "download_error"])
        
        return media
    
    def mark_download_failed(self, media: InstagramMedia, 
                           error_message: str) -> InstagramMedia:
        """
        Отметить ошибку загрузки.
        
        Args:
            media: Медиа объект
            error_message: Сообщение об ошибке
            
        Returns:
            Обновленный медиа объект
        """
        media.is_downloaded = False
        media.download_error = error_message[:1000]  # Ограничиваем длину
        media.save(update_fields=["is_downloaded", "download_error"])
        return media
    
    def bulk_create_media(self, media_data_list: list[dict[str, Any]], 
                         post: InstagramPost) -> tuple[int, int]:
        """
        Массовое создание медиа для поста используя BulkOperationsMixin.
        
        Args:
            media_data_list: Список данных медиа
            post: Пост
            
        Returns:
            Кортеж (количество созданных, количество обновленных)
        """
        # Подготавливаем данные
        for index, media_data in enumerate(media_data_list):
            media_data["post"] = post
            media_data["order_index"] = media_data.get("order_index", index)
        
        # Используем bulk_create_or_update из BulkOperationsMixin
        created_count, updated_count = self.bulk_create_or_update(
            media_data_list,
            lookup_field="media_id",
            update_fields=["media_url", "thumbnail_url", "width", "height", "duration"]
        )
        
        return created_count, updated_count
    
    def get_storage_stats(self) -> dict[str, Any]:
        """
        Получить статистику хранилища используя StatisticsMixin.
        
        Returns:
            Словарь со статистикой
        """
        # Calculate statistics directly using aggregation
        
        stats = self.model.objects.aggregate(
            total_media=Count("id"),
            downloaded=Count("id", filter=Q(is_downloaded=True)),
            failed=Count("id", filter=Q(is_downloaded=False) & ~Q(download_error=""))
        )
        
        # Добавляем pending
        stats["pending"] = stats["total_media"] - stats["downloaded"] - stats["failed"]
        
        # Добавляем download_rate
        if stats["total_media"] > 0:
            stats["download_rate"] = round(stats["downloaded"] / stats["total_media"] * 100, 2)
        else:
            stats["download_rate"] = 0
        
        # Статистика по типам медиа
        type_stats = (
            self.model.objects
            .values("media_type")
            .annotate(
                total=Count("id"),
                downloaded=Count("id", filter=Q(is_downloaded=True))
            )
        )
        
        # Преобразуем в словарь для обратной совместимости
        stats["video_stats"] = next((s for s in type_stats if s["media_type"] == "video"), 
                                   {"total": 0, "downloaded": 0})
        stats["photo_stats"] = next((s for s in type_stats if s["media_type"] == "photo"), 
                                   {"total": 0, "downloaded": 0})
        
        return stats
    
    def cleanup_orphaned_files(self) -> int:
        """
        Очистить файлы без связанных записей в БД.
        
        Returns:
            Количество удаленных файлов
        """
        import os

        from django.conf import settings
        
        media_root = os.path.join(settings.MEDIA_ROOT, "instagram", "media")
        if not os.path.exists(media_root):
            return 0
            
        deleted_count = 0
        
        # Получаем все пути из БД
        db_paths = set(
            self.filter(is_downloaded=True)
            .values_list("local_path", flat=True)
        )
        
        # Проходим по файлам в директории
        for root, dirs, files in os.walk(media_root):
            for file in files:
                file_path = os.path.join(root, file)
                relative_path = os.path.relpath(file_path, settings.MEDIA_ROOT)
                
                # Если файл не в БД - удаляем
                if relative_path not in db_paths:
                    try:
                        os.remove(file_path)
                        deleted_count += 1
                    except Exception:
                        pass
                        
        return deleted_count
    
    # Light методы для оптимизации производительности
    
    def get_media_urls_only(self, post: InstagramPost | None = None) -> QuerySet[InstagramMedia]:
        """
        Получить только URLs медиа без метаданных.
        
        Загружает только: id, media_id, media_url, thumbnail_url, media_type.
        
        Args:
            post: Пост для фильтрации (опционально)
            
        Returns:
            QuerySet с URLs
        """
        only_fields = [
            "id", "media_id", "media_url", "thumbnail_url", 
            "media_type", "order_index"
        ]
        queryset = self.filter(only_fields=only_fields)
        
        if post:
            queryset = queryset.filter(post=post)
            
        return queryset.order_by("order_index")
    
    def get_download_queue(self, limit: int = 100) -> QuerySet[InstagramMedia]:
        """
        Получить очередь для загрузки с минимальными полями.
        
        Args:
            limit: Максимальное количество
            
        Returns:
            QuerySet для загрузки
        """
        only_fields = [
            "id", "media_id", "media_url", "thumbnail_url",
            "media_type", "is_downloaded", "post_id"
        ]
        
        return self.filter(
            is_downloaded=False,
            download_error="",
            only_fields=only_fields
        )[:limit]
    
    def get_media_stats_light(self) -> QuerySet[InstagramMedia]:
        """
        Получить легкую статистику медиа.
        
        Returns:
            QuerySet для статистики
        """
        only_fields = [
            "id", "media_type", "is_downloaded", 
            "width", "height", "duration"
        ]
        return self.filter(only_fields=only_fields)
    
    def get_media_for_display(self, post: InstagramPost) -> QuerySet[InstagramMedia]:
        """
        Получить медиа для отображения в UI.
        
        Args:
            post: Пост
            
        Returns:
            QuerySet для отображения
        """
        defer_fields = ["download_error", "audio_url"]
        
        return self.filter(
            post=post,
            defer_fields=defer_fields
        ).order_by("order_index")