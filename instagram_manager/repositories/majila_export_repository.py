from datetime import datetime, timed<PERSON>ta
from typing import cast

from django.db.models import Avg, <PERSON>, <PERSON>, QuerySet, Sum
from django.utils import timezone

from core.repositories.base import BaseRepository
from core.repositories.mixins import (
    BulkOperationsMixin,
    DateFilterMixin,
    StatisticsMixin,
)
from instagram_manager.models import InstagramPost, MajilaExportTask, MajilaServiceAccount


class MajilaExportRepository(
    BaseRepository[MajilaExportTask],
    BulkOperationsMixin,
    DateFilterMixin,
    StatisticsMixin,
):
    """Репозиторий для работы с задачами экспорта в Majila"""
    model = MajilaExportTask
    
    def get_recent_tasks(self, limit: int = 10) -> QuerySet[MajilaExportTask]:
        """Получить недавние задачи экспорта"""
        return (
            self.all()
            .select_related("account", "account__instagram_profile")
            .prefetch_related("posts")
            .order_by("-created_at")[:limit]
        )
    
    def get_by_account(self, account: MajilaServiceAccount) -> QuerySet[MajilaExportTask]:
        """Получить задачи экспорта для конкретного аккаунта"""
        return (
            self.filter(account=account)
            .select_related("account")
            .prefetch_related("posts")
            .order_by("-created_at")
        )
    
    def get_by_status(self, status: str) -> QuerySet[MajilaExportTask]:
        """Получить задачи по статусу"""
        return (
            self.filter(status=status)
            .select_related("account", "account__instagram_profile")
            .order_by("-created_at")
        )
    
    def get_pending_tasks(self) -> QuerySet[MajilaExportTask]:
        """Получить задачи ожидающие выполнения"""
        return self.get_by_status("pending")
    
    def get_failed_tasks(self) -> QuerySet[MajilaExportTask]:
        """Получить неудачные задачи"""
        return self.filter(status__in=["failed", "partial"]).order_by("-created_at")
    
    def get_task_statistics(self, account: MajilaServiceAccount | None = None) -> dict:
        """
        Получить статистику по задачам экспорта
        
        Args:
            account: Фильтр по аккаунту (опционально)
            
        Returns:
            Словарь со статистикой
        """
        # Строим queryset с фильтрами
        queryset = self.all()
        if account:
            queryset = queryset.filter(account=account)
        
        # Получаем общую статистику
        from django.db.models import Count, Sum
        
        general_stats = queryset.aggregate(
            total_tasks=Count("id"),
            total_posts_exported=Sum("exported_count"),
            total_posts_failed=Sum("failed_count"),
            avg_posts_per_task=Avg("total_posts")
        )
        
        # Обеспечиваем значения по умолчанию
        stats = {
            "total_tasks": general_stats.get("total_tasks", 0) or 0,
            "total_posts_exported": general_stats.get("total_posts_exported", 0) or 0,
            "total_posts_failed": general_stats.get("total_posts_failed", 0) or 0,
            "avg_posts_per_task": general_stats.get("avg_posts_per_task", 0) or 0
        }
        
        # Статистика по статусам
        status_stats = (
            queryset.values("status")
            .annotate(count=Count("id"))
            .order_by("status")
        )
        
        stats["by_status"] = {item["status"]: item["count"] for item in status_stats}
        
        return stats
    
    def get_posts_export_status(self, posts: list[InstagramPost]) -> dict[int, list[str]]:
        """
        Получить статус экспорта для списка постов
        
        Args:
            posts: Список постов для проверки
            
        Returns:
            Словарь {post_id: [список статусов экспорта]}
        """
        post_ids = [post.id for post in posts]
        
        # Получаем все задачи содержащие эти посты
        tasks = (
            self.filter(posts__in=post_ids)
            .prefetch_related("posts")
            .values("id", "status", "export_results")
        )
        
        # Собираем статусы для каждого поста
        post_statuses: dict[int, list[str]] = {}
        for task in tasks:
            if task["export_results"]:
                for result in task["export_results"]:
                    post_id = result.get("post_id")
                    if post_id and int(post_id) in post_ids:
                        post_id_int = int(post_id)
                        if post_id_int not in post_statuses:
                            post_statuses[post_id_int] = []
                        post_statuses[post_id_int].append(result.get("status", task["status"]))
        
        return post_statuses
    
    def create_export_task(
        self,
        account: MajilaServiceAccount,
        posts: list[InstagramPost]
    ) -> MajilaExportTask:
        """
        Создать новую задачу экспорта
        
        Args:
            account: Аккаунт для экспорта
            posts: Список постов для экспорта
            
        Returns:
            Созданная задача
        """
        task = self.create(
            account=account,
            status="pending",
            total_posts=len(posts)
        )
        task.posts.set(posts)
        return cast(MajilaExportTask, task)
    
    def get_export_summary(
        self, 
        start_date: datetime | None = None,
        end_date: datetime | None = None
    ) -> dict:
        """
        Получить сводку по экспорту за период
        
        Args:
            start_date: Начальная дата
            end_date: Конечная дата
            
        Returns:
            Словарь со сводной информацией
        """
        queryset = self.all()
        
        if start_date:
            queryset = queryset.filter(created_at__gte=start_date)
        if end_date:
            queryset = queryset.filter(created_at__lte=end_date)
        
        summary = queryset.aggregate(
            total_tasks=Count("id"),
            successful_tasks=Count("id", filter=Q(status="completed")),
            failed_tasks=Count("id", filter=Q(status="failed")),
            partial_tasks=Count("id", filter=Q(status="partial")),
            total_posts_attempted=Sum("total_posts"),
            total_posts_exported=Sum("exported_count"),
            total_posts_failed=Sum("failed_count")
        )
        
        # Добавляем процент успешности
        if summary["total_posts_attempted"]:
            summary["success_rate"] = (
                summary["total_posts_exported"] / summary["total_posts_attempted"] * 100
            )
        else:
            summary["success_rate"] = 0
        
        return summary
    
    def cleanup_old_tasks(self, days: int = 30) -> int:
        """
        Удалить старые завершенные задачи
        
        Args:
            days: Количество дней для хранения задач
            
        Returns:
            Количество удаленных задач
        """
        cutoff_date = timezone.now() - timedelta(days=days)
        old_tasks = self.filter(
            completed_at__lt=cutoff_date,
            status__in=["completed", "failed"]
        )
        count = old_tasks.count()
        old_tasks.delete()
        return count