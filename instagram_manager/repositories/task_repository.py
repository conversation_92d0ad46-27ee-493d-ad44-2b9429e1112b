"""
Repository для работы с задачами скрапинга Instagram.
"""

from datetime import timedelta
from typing import Any

from django.db.models import Avg, Count, Q, Sum
from django.utils import timezone

from core.repositories.base import BaseRepository
from core.repositories.mixins import (
    DateFilterMixin,
    SearchMixin,
    StatisticsMixin,
)
from instagram_manager.models import InstagramScrapingTask


class InstagramScrapingTaskRepository(
    BaseRepository[InstagramScrapingTask],
    DateFilterMixin,
    SearchMixin,
    StatisticsMixin,
):
    """Repository для задач скрапинга Instagram."""

    model = InstagramScrapingTask

    def get_by_snapshot_id(self, snapshot_id: str) -> InstagramScrapingTask | None:
        """
        Получить задачу по snapshot_id от BrightData.

        Args:
            snapshot_id: ID снимка BrightData

        Returns:
            Задача или None
        """
        return self.filter(brightdata_snapshot_id=snapshot_id).first()

    def filter_by_status(self, status: str) -> list[InstagramScrapingTask]:
        """
        Фильтровать задачи по статусу.

        Args:
            status: Статус (pending, in_progress, completed, failed)

        Returns:
            Список задач
        """
        return list(self.filter(status=status).order_by("-created_at"))

    def filter_by_type(self, task_type: str) -> list[InstagramScrapingTask]:
        """
        Фильтровать задачи по типу.

        Args:
            task_type: Тип задачи (profile, posts, followers, comments, hashtag)

        Returns:
            Список задач
        """
        return list(self.filter(task_type=task_type).order_by("-created_at"))

    def get_pending_tasks(self, limit: int = 10) -> list[InstagramScrapingTask]:
        """
        Получить ожидающие задачи.

        Args:
            limit: Максимальное количество

        Returns:
            Список ожидающих задач
        """
        return list(self.filter(status="pending").order_by("created_at")[:limit])

    def get_active_tasks(self) -> list[InstagramScrapingTask]:
        """
        Получить активные задачи.

        Returns:
            Список активных задач
        """
        return list(self.filter(status="in_progress").order_by("started_at"))

    def get_failed_tasks(self, hours: int = 24) -> list[InstagramScrapingTask]:
        """
        Получить проваленные задачи за период используя DateFilterMixin.

        Args:
            hours: Период в часах

        Returns:
            Список проваленных задач
        """
        # Используем get_recent из DateFilterMixin
        recent = self.get_recent(days=int(hours / 24), date_field="completed_at")
        return list(recent.filter(status="failed").order_by("-completed_at"))

    def get_stuck_tasks(self, timeout_minutes: int = 30) -> list[InstagramScrapingTask]:
        """
        Получить застрявшие задачи.

        Args:
            timeout_minutes: Таймаут в минутах

        Returns:
            Список застрявших задач
        """
        timeout_threshold = timezone.now() - timedelta(minutes=timeout_minutes)

        return list(self.filter(status="in_progress", started_at__lt=timeout_threshold))

    def check_duplicate_task(
        self, task_type: str, target_identifier: str, hours: int = 1
    ) -> bool:
        """
        Проверить наличие дубликата задачи.

        Args:
            task_type: Тип задачи
            target_identifier: Идентификатор цели
            hours: Период для проверки в часах

        Returns:
            True если дубликат существует
        """
        cutoff_time = timezone.now() - timedelta(hours=hours)

        return (
            self.filter(
                task_type=task_type,
                target_identifier=target_identifier,
                created_at__gte=cutoff_time,
            )
            .exclude(status="failed")
            .exists()
        )

    def start_task(self, task: InstagramScrapingTask) -> InstagramScrapingTask:
        """
        Отметить начало выполнения задачи.

        Args:
            task: Задача

        Returns:
            Обновленная задача
        """
        task.status = "in_progress"
        task.started_at = timezone.now()
        task.save(update_fields=["status", "started_at"])

        return task

    def complete_task(
        self, task: InstagramScrapingTask, items_scraped: int = 0
    ) -> InstagramScrapingTask:
        """
        Отметить успешное завершение задачи.

        Args:
            task: Задача
            items_scraped: Количество обработанных элементов

        Returns:
            Обновленная задача
        """
        task.status = "completed"
        task.completed_at = timezone.now()
        task.items_scraped = items_scraped
        task.save(update_fields=["status", "completed_at", "items_scraped"])

        return task

    def fail_task(
        self, task: InstagramScrapingTask, error_message: str
    ) -> InstagramScrapingTask:
        """
        Отметить провал задачи.

        Args:
            task: Задача
            error_message: Сообщение об ошибке

        Returns:
            Обновленная задача
        """
        task.status = "failed"
        task.completed_at = timezone.now()
        task.error_message = error_message[:1000]  # Ограничиваем длину
        task.save(update_fields=["status", "completed_at", "error_message"])
        return task

    def retry_failed_task(self, task: InstagramScrapingTask) -> InstagramScrapingTask:
        """
        Создать повторную задачу для проваленной.

        Args:
            task: Проваленная задача

        Returns:
            Новая задача
        """
        new_task = InstagramScrapingTask.objects.create(
            task_type=task.task_type,
            target_identifier=task.target_identifier,
            brightdata_dataset_id=task.brightdata_dataset_id,
            skip_media_download=task.skip_media_download,
            status="pending",
        )
        return new_task

    def get_task_stats(self, hours: int = 24) -> dict[str, Any]:
        """
        Получить статистику задач используя StatisticsMixin.

        Args:
            hours: Период для анализа в часах

        Returns:
            Словарь со статистикой
        """
        # Calculate statistics directly using aggregation
        from datetime import timedelta

        from django.db.models import Avg, Count, Q, Sum
        from django.utils import timezone

        cutoff_date = timezone.now() - timedelta(hours=hours)
        recent_tasks = self.filter(created_at__gte=cutoff_date)

        # Calculate stats using aggregation
        stats = recent_tasks.aggregate(
            total_tasks=Count("id"),
            completed_tasks=Count("id", filter=Q(status="completed")),
            failed_tasks=Count("id", filter=Q(status="failed")),
            pending_tasks=Count("id", filter=Q(status="pending")),
            in_progress_tasks=Count("id", filter=Q(status="in_progress")),
            total_items_scraped=Sum("items_scraped"),
            avg_items_per_task=Avg("items_scraped", filter=Q(status="completed")),
        )
        # Handle None values in aggregation results
        if stats["total_items_scraped"] is None:
            stats["total_items_scraped"] = 0
        if stats["avg_items_per_task"] is None:
            stats["avg_items_per_task"] = 0

        # Успешность
        if stats["total_tasks"] > 0:
            stats["success_rate"] = round(
                stats["completed_tasks"] / stats["total_tasks"] * 100, 2
            )
        else:
            stats["success_rate"] = 0

        # Статистика по типам
        type_stats = recent_tasks.values("task_type").annotate(
            count=Count("id"),
            completed=Count("id", filter=Q(status="completed")),
            failed=Count("id", filter=Q(status="failed")),
        )
        stats["by_type"] = list(type_stats)

        # Среднее время выполнения
        completed_with_time = recent_tasks.filter(
            status="completed", started_at__isnull=False, completed_at__isnull=False
        )

        if completed_with_time.exists():
            total_duration = timedelta()
            for task in completed_with_time:
                total_duration += task.completed_at - task.started_at

            avg_duration = total_duration / completed_with_time.count()
            stats["avg_duration_seconds"] = avg_duration.total_seconds()
        else:
            stats["avg_duration_seconds"] = 0

        return stats

    def get_dataset_performance(self) -> list[dict[str, Any]]:
        """
        Получить производительность по датасетам BrightData.

        Returns:
            Статистика по датасетам
        """
        dataset_stats = (
            self.values("brightdata_dataset_id")
            .annotate(
                total_tasks=Count("id"),
                completed=Count("id", filter=Q(status="completed")),
                failed=Count("id", filter=Q(status="failed")),
                total_items=Sum("items_scraped"),
                avg_items=Avg("items_scraped", filter=Q(status="completed")),
            )
            .order_by("-total_tasks")
        )

        # Добавляем процент успешности
        result = []
        for stat in dataset_stats:
            if stat["total_tasks"] > 0:
                stat["success_rate"] = round(
                    stat["completed"] / stat["total_tasks"] * 100, 2
                )
            else:
                stat["success_rate"] = 0
            result.append(stat)

        return result

    def cleanup_old_tasks(self, days: int = 30) -> int:
        """
        Удалить старые завершенные задачи.

        Args:
            days: Возраст задач в днях

        Returns:
            Количество удаленных задач
        """
        cutoff_date = timezone.now() - timedelta(days=days)

        old_tasks = self.filter(
            status__in=["completed", "failed"], completed_at__lt=cutoff_date
        )

        count = old_tasks.count()
        old_tasks.delete()

        return count

    def get_queue_status(self) -> dict[str, Any]:
        """
        Получить статус очереди задач.

        Returns:
            Информация о состоянии очереди
        """
        return {
            "pending": self.filter(status="pending").count(),
            "in_progress": self.filter(status="in_progress").count(),
            "oldest_pending": self.filter(status="pending")
            .order_by("created_at")
            .first(),
            "stuck_tasks": len(self.get_stuck_tasks()),
            "estimated_queue_time": self._estimate_queue_time(),
        }

    def _estimate_queue_time(self) -> int:
        """
        Оценить время очереди в минутах.

        Returns:
            Примерное время в минутах
        """
        # Получаем среднее время выполнения за последние 24 часа
        recent_completed = self.filter(
            status="completed",
            started_at__isnull=False,
            completed_at__isnull=False,
            completed_at__gte=timezone.now() - timedelta(hours=24),
        )

        if not recent_completed.exists():
            return 0

        total_duration = timedelta()
        for task in recent_completed:
            total_duration += task.completed_at - task.started_at

        avg_duration = total_duration / recent_completed.count()

        # Учитываем количество задач в очереди и выполняющихся
        queue_size = self.filter(status__in=["pending", "in_progress"]).count()

        # Оценка в минутах
        return int((avg_duration * queue_size).total_seconds() / 60)
