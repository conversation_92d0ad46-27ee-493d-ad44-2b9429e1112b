"""
Repository для работы с Instagram подписчиками.
"""

from datetime import timedelta
from typing import Any

from django.db.models import Count, Q
from django.utils import timezone

from core.repositories.base import BaseRepository
from core.repositories.mixins import BulkOperationsMixin, DateFilterMixin, SearchMixin, StatisticsMixin
from instagram_manager.models import InstagramFollower, InstagramProfile


class InstagramFollowerRepository(
    BaseRepository[InstagramFollower],
    BulkOperationsMixin,
    SearchMixin,
    StatisticsMixin,
    DateFilterMixin
):
    """Repository для Instagram подписчиков."""
    
    model = InstagramFollower
    
    def get_follower(self, profile: InstagramProfile, 
                    follower_username: str) -> InstagramFollower | None:
        """
        Получить конкретного подписчика профиля.
        
        Args:
            profile: Профиль
            follower_username: Username подписчика
            
        Returns:
            Подписчик или None
        """
        return self.filter(
            profile=profile,
            follower_username=follower_username
        ).first()
    
    def filter_by_profile(self, profile: InstagramProfile) -> list[InstagramFollower]:
        """
        Получить всех подписчиков профиля.
        
        Args:
            profile: Профиль
            
        Returns:
            Список подписчиков
        """
        return list(self.filter(profile=profile).order_by("-followed_at"))
    
    def filter_verified_followers(self, profile: InstagramProfile = None) -> list[InstagramFollower]:
        """
        Получить верифицированных подписчиков.
        
        Args:
            profile: Профиль для фильтрации (опционально)
            
        Returns:
            Список верифицированных подписчиков
        """
        queryset = self.filter(is_verified=True)
        
        if profile:
            queryset = queryset.filter(profile=profile)
            
        return list(queryset)
    
    def search_followers(self, profile: InstagramProfile, query: str) -> list[InstagramFollower]:
        """
        Поиск подписчиков по username или имени.
        
        Args:
            profile: Профиль
            query: Поисковый запрос
            
        Returns:
            Список найденных подписчиков
        """
        # Используем SearchMixin с фильтрацией по профилю
        return list(self.search(query, search_fields=["follower_username", "follower_full_name"]).filter(profile=profile))
    
    def get_new_followers(self, profile: InstagramProfile, 
                         hours: int = 24) -> list[InstagramFollower]:
        """
        Получить новых подписчиков за период.
        
        Args:
            profile: Профиль
            hours: Период в часах
            
        Returns:
            Список новых подписчиков
        """
        # Calculate cutoff date
        from datetime import timedelta

        from django.utils import timezone
        
        cutoff_date = timezone.now() - timedelta(hours=hours)
        return list(
            self.filter(
                profile=profile,
                followed_at__gte=cutoff_date
            ).order_by("-followed_at")
        )
    
    def get_mutual_followers(self, profile1: InstagramProfile, 
                           profile2: InstagramProfile) -> list[str]:
        """
        Получить общих подписчиков двух профилей.
        
        Args:
            profile1: Первый профиль
            profile2: Второй профиль
            
        Returns:
            Список username общих подписчиков
        """
        followers1 = set(
            self.filter(profile=profile1)
            .values_list("follower_username", flat=True)
        )
        
        followers2 = set(
            self.filter(profile=profile2)
            .values_list("follower_username", flat=True)
        )
        
        return list(followers1.intersection(followers2))
    
    def bulk_create_followers(self, followers_data: list[dict[str, Any]], 
                            profile: InstagramProfile) -> tuple[int, int]:
        """
        Массовое создание или обновление подписчиков используя BulkOperationsMixin.
        
        Args:
            followers_data: Список данных подписчиков
            profile: Профиль
            
        Returns:
            Кортеж (количество созданных, количество обновленных)
        """
        # Добавляем профиль к каждому элементу
        for item in followers_data:
            item["profile"] = profile
            
        # Используем BulkOperationsMixin
        created_count, updated_count = self.bulk_create_or_update(
            followers_data, 
            lookup_field="follower_id",
            update_fields=["follower_username", "follower_full_name", "is_verified", "profile_pic_url"]
        )
                
        return created_count, updated_count
    
    def detect_unfollowers(self, profile: InstagramProfile, 
                         current_followers: list[str]) -> list[InstagramFollower]:
        """
        Обнаружить отписавшихся пользователей.
        
        Args:
            profile: Профиль
            current_followers: Список текущих username подписчиков
            
        Returns:
            Список отписавшихся
        """
        # Получаем всех сохраненных подписчиков
        saved_followers = self.filter(profile=profile).values_list(
            "follower_username", flat=True
        )
        
        # Находим отписавшихся
        unfollowers_usernames = set(saved_followers) - set(current_followers)
        
        # Возвращаем объекты отписавшихся
        return list(self.filter(
            profile=profile,
            follower_username__in=unfollowers_usernames
        ))
    
    def remove_unfollowers(self, profile: InstagramProfile, 
                          current_followers: list[str]) -> int:
        """
        Удалить отписавшихся пользователей из БД.
        
        Args:
            profile: Профиль
            current_followers: Список текущих username подписчиков
            
        Returns:
            Количество удаленных
        """
        unfollowers = self.detect_unfollowers(profile, current_followers)
        count = len(unfollowers)
        unfollowers.delete()
        return count
    
    def get_follower_stats(self, profile: InstagramProfile) -> dict[str, Any]:
        """
        Получить статистику подписчиков используя StatisticsMixin.
        
        Args:
            profile: Профиль
            
        Returns:
            Словарь со статистикой
        """
        # Calculate statistics directly using aggregation
        from django.db.models import Count
        
        base_stats = self.model.objects.filter(profile=profile).aggregate(
            total_followers=Count("id"),
            verified_followers=Count("id", filter=Q(is_verified=True))
        )
        
        # Добавляем рост подписчиков
        from datetime import timedelta

        from django.utils import timezone
        
        for days in [1, 7, 30]:
            cutoff_date = timezone.now() - timedelta(days=days)
            recent_count = self.filter(
                profile=profile,
                created_at__gte=cutoff_date
            ).count()
            base_stats[f"new_followers_{days}d"] = recent_count
            
        # Процент верифицированных
        total = base_stats.get("total_followers", 0)
        if total > 0:
            base_stats["verified_percentage"] = round(
                base_stats.get("verified_followers", 0) / total * 100, 2
            )
        else:
            base_stats["verified_percentage"] = 0
            
        return base_stats
    
    def get_follower_growth_trend(self, profile: InstagramProfile, 
                                 days: int = 30) -> list[dict[str, Any]]:
        """
        Получить тренд роста подписчиков по дням.
        
        Args:
            profile: Профиль
            days: Период в днях
            
        Returns:
            Список с данными по дням
        """
        from django.db.models.functions import TruncDate
        
        cutoff_date = timezone.now() - timedelta(days=days)
        
        daily_growth = (
            self.filter(
                profile=profile,
                created_at__gte=cutoff_date
            )
            .annotate(date=TruncDate("created_at"))
            .values("date")
            .annotate(new_followers=Count("id"))
            .order_by("date")
        )
        
        return list(daily_growth)
    
    def get_top_followers(self, profile: InstagramProfile, 
                         limit: int = 10) -> list[InstagramFollower]:
        """
        Получить топ подписчиков (верифицированные и известные).
        
        Args:
            profile: Профиль
            limit: Количество
            
        Returns:
            Список топ подписчиков
        """
        # Сначала верифицированные
        verified = list(
            self.filter(
                profile=profile,
                is_verified=True
            )[:limit]
        )
        
        # Если недостаточно, добавляем обычных
        if len(verified) < limit:
            regular = list(
                self.filter(
                    profile=profile,
                    is_verified=False
                )
                .exclude(follower_full_name="")
                .order_by("-created_at")[:limit - len(verified)]
            )
            verified.extend(regular)
            
        return verified
    
    def analyze_follower_quality(self, profile: InstagramProfile) -> dict[str, Any]:
        """
        Анализ качества аудитории.
        
        Args:
            profile: Профиль
            
        Returns:
            Анализ качества подписчиков
        """
        followers = self.filter(profile=profile)
        total = followers.count()
        
        if total == 0:
            return {
                "quality_score": 0,
                "verified_ratio": 0,
                "has_profile_pic_ratio": 0,
                "has_full_name_ratio": 0
            }
        
        # Подсчитываем различные метрики
        verified_count = followers.filter(is_verified=True).count()
        has_pic_count = followers.exclude(profile_pic_url="").count()
        has_name_count = followers.exclude(follower_full_name="").count()
        
        # Вычисляем соотношения
        verified_ratio = verified_count / total
        has_pic_ratio = has_pic_count / total
        has_name_ratio = has_name_count / total
        
        # Общий показатель качества (0-100)
        quality_score = round(
            (verified_ratio * 50 +  # Верифицированные важнее
             has_pic_ratio * 30 +
             has_name_ratio * 20) * 100
        )
        
        return {
            "quality_score": quality_score,
            "verified_ratio": round(verified_ratio * 100, 2),
            "has_profile_pic_ratio": round(has_pic_ratio * 100, 2),
            "has_full_name_ratio": round(has_name_ratio * 100, 2),
            "total_analyzed": total
        }