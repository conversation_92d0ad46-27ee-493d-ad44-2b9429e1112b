"""
Repository для работы с Instagram аккаунтами.
"""

from typing import Any

from django.db.models import Q, QuerySet

from core.repositories.base import BaseRepository
from core.repositories.mixins import (
    BulkOperationsMixin,
    DateFilterMixin,
    SearchMixin,
    SoftDeleteMixin,
    StatisticsMixin,
)
from instagram_manager.models import InstagramAccount


class InstagramAccountRepository(
    BaseRepository[InstagramAccount],
    BulkOperationsMixin,
    DateFilterMixin,
    SearchMixin,
    SoftDeleteMixin,
    StatisticsMixin,
):
    """Repository для Instagram аккаунтов."""
    
    model = InstagramAccount
    
    # Оптимизация запросов
    select_related_fields = []
    prefetch_related_fields = []
    
    def get_by_username(self, username: str) -> InstagramAccount | None:
        """
        Получить аккаунт по username.
        
        Args:
            username: Username аккаунта
            
        Returns:
            Аккаунт или None
        """
        return self.filter(username=username).first()
    
    def get_active_accounts(self) -> QuerySet[InstagramAccount]:
        """
        Получить активные аккаунты.
        
        Returns:
            QuerySet активных аккаунтов
        """
        return self.filter(is_active=True)
    
    def get_accounts_needing_reauth(self) -> QuerySet[InstagramAccount]:
        """
        Получить аккаунты, требующие повторной авторизации.
        
        Returns:
            QuerySet аккаунтов с ошибками авторизации
        """
        return self.model.objects.filter(
            Q(requires_auth=True) | Q(auth_error__isnull=False)
        ).exclude(auth_error="")
    
    async def aget_by_id_with_related(self, account_id: int) -> InstagramAccount | None:
        """
        Асинхронно получить аккаунт по ID с select_related.
        
        Args:
            account_id: ID аккаунта
            
        Returns:
            Аккаунт или None
        """
        return await self.model.objects.select_related().aget(id=account_id)
    
    def search_accounts(self, query: str) -> QuerySet[InstagramAccount]:
        """
        Поиск аккаунтов по username или заметкам.
        
        Args:
            query: Поисковый запрос
            
        Returns:
            QuerySet найденных аккаунтов
        """
        return self.model.objects.filter(
            Q(username__icontains=query) | Q(notes__icontains=query)
        )
    
    def get_stats(self) -> dict[str, Any]:
        """
        Получить статистику по аккаунтам.
        
        Returns:
            Словарь со статистикой
        """
        total = self.count()
        active = self.get_active_accounts().count()
        needs_reauth = self.get_accounts_needing_reauth().count()
        
        return {
            "total": total,
            "active": active,
            "inactive": total - active,
            "needs_reauth": needs_reauth,
            "active_percentage": round((active / total * 100), 2) if total > 0 else 0,
        }