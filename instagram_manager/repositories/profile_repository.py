"""
Repository для работы с Instagram профилями.
"""

from datetime import timed<PERSON><PERSON>
from typing import Any

from django.db.models import Avg, Count, F, Q, QuerySet
from django.utils import timezone

from core.repositories.base import BaseRepository
from core.repositories.mixins import (
    BulkOperationsMixin,
    DateFilterMixin,
    SearchMixin,
    SoftDeleteMixin,
    StatisticsMixin,
)
from instagram_manager.models import InstagramProfile


class InstagramProfileRepository(
    BaseRepository[InstagramProfile],
    BulkOperationsMixin,
    DateFilterMixin,
    SearchMixin,
    SoftDeleteMixin,
    StatisticsMixin,
):
    """Repository для Instagram профилей."""
    
    model = InstagramProfile
    
    # Оптимизация запросов
    select_related_fields = []
    prefetch_related_fields = ["posts", "posts__media", "posts__comments"]
    
    def get_by_username(self, username: str) -> InstagramProfile | None:
        """
        Получить профиль по username.
        
        Args:
            username: Userna<PERSON> профиля
            
        Returns:
            Профиль или None
        """
        return self.filter(username=username).first()
    
    def get_by_profile_id(self, profile_id: str) -> InstagramProfile | None:
        """
        Получить профиль по profile_id.
        
        Args:
            profile_id: ID профиля Instagram
            
        Returns:
            Профиль или None
        """
        return self.filter(profile_id=profile_id).first()
    
    def filter_business_accounts(self) -> list[InstagramProfile]:
        """
        Получить бизнес-аккаунты.
        
        Returns:
            Список бизнес-аккаунтов
        """
        queryset = self.filter(is_business=True)
        queryset = self._apply_optimizations(queryset)
        return list(queryset)
    
    def filter_verified_accounts(self) -> list[InstagramProfile]:
        """
        Получить верифицированные аккаунты.
        
        Returns:
            Список верифицированных аккаунтов
        """
        queryset = self.filter(is_verified=True)
        queryset = self._apply_optimizations(queryset)
        return list(queryset)
    
    def filter_public_accounts(self) -> list[InstagramProfile]:
        """
        Получить публичные аккаунты.
        
        Returns:
            Список публичных аккаунтов
        """
        queryset = self.filter(is_private=False)
        queryset = self._apply_optimizations(queryset)
        return list(queryset)
    
    def filter_by_follower_count(self, min_followers: int = None, 
                                max_followers: int = None) -> list[InstagramProfile]:
        """
        Фильтровать профили по количеству подписчиков.
        
        Args:
            min_followers: Минимальное количество подписчиков
            max_followers: Максимальное количество подписчиков
            
        Returns:
            Список профилей
        """
        queryset = self.get_optimized_queryset()
        
        if min_followers is not None:
            queryset = queryset.filter(follower_count__gte=min_followers)
        if max_followers is not None:
            queryset = queryset.filter(follower_count__lte=max_followers)
            
        return list(queryset)
    
    def search_profiles(self, query: str) -> list[InstagramProfile]:
        """
        Поиск профилей по username и full_name.
        
        Args:
            query: Поисковый запрос
            
        Returns:
            Список найденных профилей
        """
        # Используем search из SearchMixin с указанием полей
        queryset = self.search(query, search_fields=["username", "full_name"])
        return list(queryset)
    
    def get_profiles_needing_update(self, hours: int = 24) -> list[InstagramProfile]:
        """
        Получить профили, требующие обновления.
        
        Args:
            hours: Количество часов с последнего обновления
            
        Returns:
            Список профилей для обновления
        """
        # Используем get_stale_objects из DateFilterMixin
        queryset = self.get_stale_objects(field="last_scraped_at", hours=hours)
        return list(queryset)
    
    def get_profiles_with_stats(self) -> list[InstagramProfile]:
        """
        Получить профили со статистикой постов.
        
        Returns:
            Список профилей с аннотированной статистикой
        """
        queryset = self.model.objects.annotate(
            actual_post_count=Count("posts"),
            avg_likes=Avg("posts__like_count"),
            avg_comments=Avg("posts__comment_count")
        )
        # Для агрегаций prefetch не нужен
        return list(queryset)
    
    # Используем bulk_create_or_update из BulkOperationsMixin
    # Переопределяем только если нужна специфическая логика
    def bulk_create_or_update_profiles(self, profiles_data: list[dict[str, Any]]) -> tuple[int, int]:
        """
        Массовое создание или обновление профилей по profile_id.
        
        Args:
            profiles_data: Список данных профилей
            
        Returns:
            Кортеж (количество созданных, количество обновленных)
        """
        # Используем метод из миксина с profile_id как lookup field
        return self.bulk_create_or_update(profiles_data, lookup_field="profile_id")
    
    def update_scraping_timestamp(self, profile: InstagramProfile) -> InstagramProfile:
        """
        Обновить время последнего скрапинга.
        
        Args:
            profile: Профиль для обновления
            
        Returns:
            Обновленный профиль
        """
        profile.last_scraped_at = timezone.now()
        profile.save(update_fields=["last_scraped_at"])
        return profile
    
    def update(self, pk: Any, **kwargs) -> InstagramProfile | None:
        """
        Обновить профиль с инвалидацией кеша.
        
        Args:
            pk: Primary key профиля
            **kwargs: Поля для обновления
            
        Returns:
            Обновленный профиль или None
        """
        # Получаем профиль до обновления для инвалидации кеша
        profile = self.get_by_id(pk)
        if not profile:
            return None
            
        # Обновляем профиль
        result = super().update(pk, **kwargs)
        
        return result
    
    def get_engagement_leaders(self, limit: int = 10) -> list[InstagramProfile]:
        """
        Получить профили с наибольшим engagement.
        
        Args:
            limit: Количество профилей
            
        Returns:
            Список профилей
        """
        queryset = self.model.objects.annotate(
            avg_engagement=Avg(
                (F("posts__like_count") + F("posts__comment_count")) / 
                F("follower_count")
            )
        )
        # Для агрегаций prefetch не нужен
        return list(queryset.order_by("-avg_engagement")[:limit])
    
    # Light методы для оптимизации производительности
    
    def get_profiles_list_light(self) -> QuerySet[InstagramProfile]:
        """
        Получить легкий список профилей для отображения.
        
        Загружает только: id, username, full_name, follower_count, 
        is_verified, is_business, profile_pic_url.
        
        Returns:
            QuerySet с ограниченными полями
        """
        only_fields = [
            "id", "profile_id", "username", "full_name", 
            "follower_count", "is_verified", "is_business", 
            "profile_pic_url", "is_private"
        ]
        return self.filter(only_fields=only_fields)
    
    def get_profiles_for_dropdown(self) -> QuerySet[InstagramProfile]:
        """
        Получить минимальный набор полей для выпадающих списков.
        
        Returns:
            QuerySet только с id, username, is_verified
        """
        only_fields = ["id", "username", "is_verified"]
        return self.filter(only_fields=only_fields).order_by("username")
    
    def get_profiles_metrics_only(self) -> QuerySet[InstagramProfile]:
        """
        Получить только метрики профилей для аналитики.
        
        Returns:
            QuerySet с метриками
        """
        only_fields = [
            "id", "username", "follower_count", "following_count", 
            "post_count", "is_verified", "is_business"
        ]
        return self.filter(only_fields=only_fields)
    
    def get_profiles_for_sync(self, hours: int = 24) -> QuerySet[InstagramProfile]:
        """
        Получить профили для синхронизации с минимальными данными.
        
        Args:
            hours: Часов с последнего обновления
            
        Returns:
            QuerySet для синхронизации
        """
        cutoff_time = timezone.now() - timedelta(hours=hours)
        only_fields = ["id", "profile_id", "username", "last_scraped_at"]
        
        return self.filter(
            only_fields=only_fields
        ).filter(
            Q(last_scraped_at__lt=cutoff_time) | 
            Q(last_scraped_at__isnull=True)
        )