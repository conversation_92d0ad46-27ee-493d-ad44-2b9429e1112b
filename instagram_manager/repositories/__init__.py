"""
Instagram repositories module.
"""

from .account_repository import InstagramAccountRepository as AccountRepository
from .comment_repository import InstagramCommentRepository as CommentRepository
from .follower_repository import InstagramFollowerRepository as FollowerRepository
from .hashtag_repository import InstagramHashtagRepository as HashtagRepository
from .majila_account_repository import MajilaAccountRepository as MajilaServiceAccountRepository
from .majila_export_repository import MajilaExportRepository as MajilaExportTaskRepository
from .media_repository import InstagramMediaRepository as MediaRepository
from .post_repository import InstagramPostRepository as PostRepository
from .profile_repository import InstagramProfileRepository as ProfileRepository
from .task_repository import InstagramScrapingTaskRepository as TaskRepository

# Экспортируем с короткими именами
__all__ = [
    "AccountRepository",
    "ProfileRepository",
    "PostRepository",
    "MediaRepository",
    "CommentRepository",
    "HashtagRepository",
    "FollowerRepository",
    "TaskRepository",
    "MajilaServiceAccountRepository",
    "MajilaExportTaskRepository",
    # Также экспортируем полные имена для обратной совместимости
    "InstagramAccountRepository",
    "InstagramProfileRepository",
    "InstagramPostRepository",
    "InstagramMediaRepository",
    "InstagramCommentRepository",
    "InstagramHashtagRepository",
    "InstagramFollowerRepository",
    "InstagramScrapingTaskRepository",
]

# Для обратной совместимости
InstagramAccountRepository = AccountRepository
InstagramProfileRepository = ProfileRepository
InstagramPostRepository = PostRepository
InstagramMediaRepository = MediaRepository
InstagramCommentRepository = CommentRepository
InstagramHashtagRepository = HashtagRepository
InstagramFollowerRepository = FollowerRepository
InstagramScrapingTaskRepository = TaskRepository