"""
Django signals для Instagram моделей.

Обработчики сигналов для автоматической очистки файлов
и управления связанными объектами при удалении.
"""

import logging
import os

from django.conf import settings
from django.db.models.signals import pre_delete
from django.dispatch import receiver

from instagram_manager.models import (
    InstagramMedia,
    InstagramPost,
    InstagramProfile,
)

logger = logging.getLogger(__name__)


# Обработчики для InstagramMedia

@receiver(pre_delete, sender=InstagramMedia)
def delete_media_files(sender, instance, **kwargs):
    """
    Удаляет локальные файлы медиа при удалении записи InstagramMedia.
    
    Удаляет:
    - Основной медиа файл (фото/видео)
    - Мини<PERSON><PERSON>юру для видео
    
    Args:
        sender: Класс модели InstagramMedia
        instance: Экземпляр удаляемого медиа
        **kwargs: Дополнительные аргументы
    """
    # Удаляем основной файл
    if instance.local_path and instance.local_path.name:
        if os.path.isfile(instance.local_path.path):
            try:
                os.remove(instance.local_path.path)
                logger.info(
                    f"Deleted media file for InstagramMedia {instance.media_id}: "
                    f"{instance.local_path.path}"
                )
            except Exception as e:
                logger.exception(
                    f"Failed to delete media file for InstagramMedia {instance.media_id}: {e}"
                )
    
    # Удаляем thumbnail для видео
    if instance.local_thumbnail_path and instance.local_thumbnail_path.name:
        if os.path.isfile(instance.local_thumbnail_path.path):
            try:
                os.remove(instance.local_thumbnail_path.path)
                logger.info(
                    f"Deleted thumbnail for InstagramMedia {instance.external_id}: "
                    f"{instance.local_thumbnail_path.path}"
                )
            except Exception as e:
                logger.exception(
                    f"Failed to delete thumbnail for InstagramMedia {instance.external_id}: {e}"
                )


# Обработчики для InstagramPost

@receiver(pre_delete, sender=InstagramPost)
def cache_post_media(sender, instance, **kwargs):
    """
    Кэширует медиа файлы поста перед удалением.
    
    Это необходимо, так как при каскадном удалении
    связанные InstagramMedia будут удалены автоматически,
    и их сигналы pre_delete сработают корректно.
    
    Args:
        sender: Класс модели InstagramPost
        instance: Экземпляр удаляемого поста
        **kwargs: Дополнительные аргументы
    """
    # Просто логируем удаление поста
    media_count = instance.media.count()
    logger.info(
        f"Deleting InstagramPost {instance.post_id} with {media_count} media files. "
        f"Media files will be deleted automatically via cascade."
    )


# Обработчики для InstagramProfile

@receiver(pre_delete, sender=InstagramProfile)
def log_profile_deletion(sender, instance, **kwargs):
    """
    Логирует удаление профиля и связанных данных.
    
    При удалении профиля каскадно удаляются:
    - Все посты профиля
    - Все медиа файлы постов
    - Все комментарии к постам
    - Все подписчики
    
    Args:
        sender: Класс модели InstagramProfile
        instance: Экземпляр удаляемого профиля
        **kwargs: Дополнительные аргументы
    """
    # Подсчитываем связанные объекты
    posts_count = instance.posts.count()
    media_count = InstagramMedia.objects.filter(post__profile=instance).count()
    
    logger.warning(
        f"Deleting InstagramProfile @{instance.username} (ID: {instance.profile_id}). "
        f"This will cascade delete: {posts_count} posts and {media_count} media files."
    )




def cleanup_empty_directories():
    """
    Очищает пустые директории в хранилище медиа файлов Instagram.
    
    Эта функция может быть вызвана периодически
    для очистки пустых папок после удаления файлов.
    """
    media_root = os.path.join(settings.MEDIA_ROOT, "instagram", "media")
    thumbnails_root = os.path.join(media_root, "thumbnails")
    
    cleaned_dirs = 0
    
    # Очищаем пустые директории в обратном порядке (от листьев к корню)
    for root_path in [thumbnails_root, media_root]:
        if not os.path.exists(root_path):
            continue
            
        for dirpath, dirnames, filenames in os.walk(root_path, topdown=False):
            # Пропускаем корневые директории
            if dirpath in [media_root, thumbnails_root]:
                continue
                
            # Если директория пуста, удаляем её
            if not dirnames and not filenames:
                try:
                    os.rmdir(dirpath)
                    cleaned_dirs += 1
                    logger.debug(f"Removed empty directory: {dirpath}")
                except Exception as e:
                    logger.exception(f"Failed to remove empty directory {dirpath}: {e}")
    
    if cleaned_dirs > 0:
        logger.info(f"Cleaned up {cleaned_dirs} empty directories")
    
    return cleaned_dirs