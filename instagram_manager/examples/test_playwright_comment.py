#!/usr/bin/env python
"""
Example script for testing Instagram comment posting with <PERSON><PERSON>.
"""

import os
import sys
import django
import asyncio
import json
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'scripts.settings')
django.setup()

from instagram_manager.services import PlaywrightCommentService  # noqa: E402
from instagram_manager.models import InstagramAccount  # noqa: E402


async def example_single_comment():
    """Example of posting a single comment."""
    # Configuration
    ACCOUNT_ID = 1  # Change to your account ID
    POST_URL = "https://www.instagram.com/p/C123456789/"  # Change to real post
    COMMENT_TEXT = "Great post! 🔥"
    
    # Initialize service
    service = PlaywrightCommentService()
    
    print("Testing single comment posting...")
    print(f"Account ID: {ACCOUNT_ID}")
    print(f"Post URL: {POST_URL}")
    print(f"Comment: {COMMENT_TEXT}")
    
    # Post comment
    success, error, posted_comment = await service.post_comment(
        account_id=ACCOUNT_ID,
        post_url=POST_URL,
        comment_text=COMMENT_TEXT,
        headless=False,  # Set to True for headless mode
        debug=True  # Enable debug mode for better visibility
    )
    
    if success:
        print("✅ Comment posted successfully!")
        if posted_comment:
            print(f"Posted comment ID: {posted_comment.id}")
    else:
        print(f"❌ Failed to post comment: {error}")
        

async def example_bulk_comments():
    """Example of posting multiple comments."""
    # Configuration
    ACCOUNT_ID = 1  # Change to your account ID
    
    # Multiple comments data
    comments_data = [
        {
            "post_url": "https://www.instagram.com/p/C123456789/",
            "comment_text": "Amazing content! 👏"
        },
        {
            "post_url": "https://www.instagram.com/p/C987654321/",
            "comment_text": "Love this! ❤️"
        },
        {
            "post_url": "https://www.instagram.com/p/C555555555/",
            "comment_text": "So inspiring! ✨"
        }
    ]
    
    # Initialize service
    service = PlaywrightCommentService()
    
    print("Testing bulk comment posting...")
    print(f"Account ID: {ACCOUNT_ID}")
    print(f"Number of comments: {len(comments_data)}")
    
    # Post comments
    results = await service.post_bulk_comments(
        account_id=ACCOUNT_ID,
        comments_data=comments_data,
        headless=False,  # Set to True for headless mode
        delay_between_comments=30  # 30 seconds between comments
    )
    
    # Display results
    print("\n" + "="*50)
    print("RESULTS:")
    print("="*50)
    print(f"Total: {results['total']}")
    print(f"✅ Successful: {results['successful']}")
    print(f"❌ Failed: {results['failed']}")
    
    if results['errors']:
        print("\nErrors:")
        for error in results['errors']:
            print(f"  - {error}")
            

async def example_login_only():
    """Example of login functionality only."""
    # Configuration
    ACCOUNT_ID = 1  # Change to your account ID
    
    print("Testing login only...")
    
    # Get account
    account = await InstagramAccount.objects.aget(id=ACCOUNT_ID)
    print(f"Account: {account.username}")
    
    from instagram_manager.browser_automation import PlaywrightClient, InstagramAuth  # noqa: E402
    
    async with PlaywrightClient.create(headless=False) as client:
        auth = InstagramAuth(client)
        success, error = await auth.login(
            username=account.username,
            password=account.password,
            account=account
        )
        
        if success:
            print("✅ Login successful!")
        else:
            print(f"❌ Login failed: {error}")
            

def create_bulk_file_example():
    """Create an example bulk comments JSON file."""
    bulk_data = [
        {
            "post_url": "https://www.instagram.com/p/C123456789/",
            "comment_text": "Great post! Love the content 🔥"
        },
        {
            "post_url": "https://www.instagram.com/p/C987654321/",
            "comment_text": "This is amazing! Keep it up 💪"
        },
        {
            "post_url": "https://www.instagram.com/p/C555555555/",
            "comment_text": "So inspiring! Thanks for sharing ✨"
        }
    ]
    
    filename = "bulk_comments_example.json"
    with open(filename, 'w') as f:
        json.dump(bulk_data, f, indent=2)
        
    print(f"Created example bulk file: {filename}")
    

if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="Example Instagram comment posting")
    parser.add_argument(
        "--mode",
        choices=["single", "bulk", "login", "create-bulk-file"],
        default="single",
        help="Test mode"
    )
    
    args = parser.parse_args()
    
    if args.mode == "create-bulk-file":
        create_bulk_file_example()
    else:
        # Run async tests
        if args.mode == "single":
            asyncio.run(example_single_comment())
        elif args.mode == "bulk":
            asyncio.run(example_bulk_comments())
        elif args.mode == "login":
            asyncio.run(example_login_only())