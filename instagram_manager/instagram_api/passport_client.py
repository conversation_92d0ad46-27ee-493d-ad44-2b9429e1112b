import logging
from json import J<PERSON><PERSON><PERSON>ode<PERSON><PERSON>r
from typing import Any

import requests
from django.conf import settings

logger = logging.getLogger(__name__)


class PassportApiClient:
    """Клиент для работы с Passport API сервиса Majila"""

    API_BASE_URL = "https://passport-api.maji.la/api/v1"
    LOGIN_ENDPOINT = "/internal_auth/auth/login/"
    UPDATE_TOKEN_ENDPOINT = "/internal_auth/auth/update_token/"

    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update(
            {
                "Content-Type": "application/json",
                "Accept": "application/json",
            }
        )
        # Опциональные заголовки для сервисной аутентификации
        self.service_key = getattr(settings, "MAJILA_SERVICE_KEY", None)
        self.backwards_url = getattr(settings, "MAJILA_BACKWARDS_URL", None)

    def login(self, username: str, password: str) -> dict[str, Any]:
        """
        Авторизация в сервисе Passport и получение токена

        Args:
            username: Имя пользователя
            password: Пароль

        Returns:
            Dict с access_token и дополнительной информацией

        Raises:
            requests.RequestException: При ошибке запроса
            ValueError: При некорректном ответе от API
        """
        url = f"{self.API_BASE_URL}{self.LOGIN_ENDPOINT}"

        # Подготовка данных для запроса - используем плоскую структуру
        data = {"login": username, "password": password}

        # Добавление опциональных заголовков
        headers = {}
        if self.service_key:
            headers["service_key"] = self.service_key
        if self.backwards_url:
            headers["backwards_url"] = self.backwards_url

        try:
            logger.info(f"Attempting login for user: {username}")
            logger.debug(f"Login URL: {url}")
            logger.debug(f"Request data: {data}")
            logger.debug(f"Request headers: {headers}")

            response = self.session.post(url, json=data, headers=headers)

            # Логируем ответ для отладки
            if response.status_code != 200:
                logger.error(f"Response status: {response.status_code}")
                logger.error(f"Response headers: {response.headers}")
                try:
                    logger.error(f"Response content: {response.text}")
                except (AttributeError, UnicodeDecodeError):
                    pass

            response.raise_for_status()

            result: dict[str, Any] = response.json()

            # Проверка наличия access_token в ответе
            if "access_token" not in result:
                raise ValueError("Missing access_token in response")

            logger.info(f"Successfully logged in user: {username}")
            return result

        except requests.RequestException as e:
            logger.exception(f"Request failed for login: {e!s}")
            if hasattr(e, "response") and e.response is not None:
                try:
                    error_detail = e.response.json()
                    logger.exception(f"Error detail: {error_detail}")
                except (ValueError, JSONDecodeError, AttributeError):
                    logger.exception(f"Error response: {e.response.text}")
            raise
        except ValueError as e:
            logger.exception(f"Invalid response format: {e!s}")
            raise

    def update_token(self, current_token: str) -> dict[str, Any]:
        """
        Обновление истекшего токена

        Args:
            current_token: Текущий (возможно истекший) токен

        Returns:
            Dict с новым access_token

        Raises:
            requests.RequestException: При ошибке запроса
            ValueError: При некорректном ответе от API
        """
        url = f"{self.API_BASE_URL}{self.UPDATE_TOKEN_ENDPOINT}"

        # Подготовка заголовков с service key
        headers = {
            "Authorization": f"Bearer {current_token}",
            "service_key": self.service_key or settings.MAJILA_SERVICE_KEY,
        }

        if self.backwards_url:
            headers["backwards_url"] = self.backwards_url

        # Тело запроса - пустой объект
        data: dict[str, Any] = {}

        try:
            logger.info("Attempting to update token")
            logger.debug(f"Update token URL: {url}")

            response = self.session.post(url, json=data, headers=headers)

            # Логируем ответ для отладки
            if response.status_code == 403:
                logger.info("Token is still active (403 response)")
                # Если токен еще активен, возвращаем его же
                return {"access_token": current_token}

            response.raise_for_status()

            result: dict[str, Any] = response.json()

            # Проверка наличия access_token в ответе
            if "access_token" not in result:
                raise ValueError("Missing access_token in response")

            logger.info("Successfully updated token")
            return result

        except requests.RequestException as e:
            logger.exception(f"Request failed for token update: {e!s}")
            if hasattr(e, "response") and e.response is not None:
                try:
                    error_detail = e.response.json()
                    logger.exception(f"Error detail: {error_detail}")
                except (ValueError, JSONDecodeError, AttributeError):
                    logger.exception(f"Error response: {e.response.text}")
            raise
        except ValueError as e:
            logger.exception(f"Invalid response format: {e!s}")
            raise

    def validate_token(self, access_token: str) -> bool:
        """
        Проверка валидности токена (если endpoint будет доступен)

        Args:
            access_token: Токен для проверки

        Returns:
            True если токен валидный, False в противном случае
        """
        # TODO: Реализовать когда будет доступен endpoint для проверки токена
        return bool(access_token)
