"""
Base BrightData API client.

Базовый клиент с общей функциональностью для всех типов запросов.
"""

import json
import logging
import time
from typing import Any, cast

import requests
from django.conf import settings

from .exceptions import BrightDataAPIError, RateLimitError, SnapshotNotReadyError

logger = logging.getLogger(__name__)


class BaseBrightDataClient:
    """Базовый клиент для работы с BrightData API."""
    
    def __init__(self):
        """Инициализация клиента."""
        self.api_token = settings.BRIGHTDATA_API_TOKEN
        self.base_url = settings.BRIGHTDATA_API_BASE_URL
        self.datasets = settings.BRIGHTDATA_DATASETS
        self.timeout = getattr(settings, "BRIGHTDATA_TIMEOUT", 600)
        self.rate_limits = getattr(settings, "BRIGHTDATA_RATE_LIMIT", {})
        
        
        # Настройка сессии
        self.session = requests.Session()
        self.session.headers.update({
            "Authorization": f"Bearer {self.api_token}",
            "Content-Type": "application/json",
        })
        
        logger.info(f"Initialized BrightData client with token: {self.api_token[:10]}...")
    
    def _make_request(
        self,
        method: str,
        endpoint: str,
        data: dict[Any, Any] | list[dict[str, Any]] | None = None,
        params: dict | None = None,
    ) -> Any:
        """
        Базовый метод для выполнения запросов к API.
        
        Args:
            method: HTTP метод
            endpoint: Эндпоинт API
            data: Данные для отправки
            params: Query параметры
            
        Returns:
            Ответ от API
            
        Raises:
            RateLimitError: При превышении лимита запросов
            BrightDataAPIError: При других ошибках API
        """
        url = f"{self.base_url}/{endpoint}"
        
        if params is None:
            params = {}
        params["format"] = "json"
        
        # Логирование запроса
        logger.info(f"Making {method} request to {url}")
        if data:
            logger.debug(f"Request data: {json.dumps(data, indent=2)}")
        
        try:
            response = self.session.request(
                method=method,
                url=url,
                json=data,
                params=params,
                timeout=self.timeout,  # This now uses the 600 second timeout
            )
            
            # Обработка ответа
            if response.status_code == 429:
                retry_after = int(response.headers.get("Retry-After", 60))
                raise RateLimitError(f"Rate limit exceeded. Retry after {retry_after} seconds", url=url)
            
            # Log error response body for debugging
            if response.status_code >= 400:
                logger.error(f"API error response: {response.text}")
            
            response.raise_for_status()
            
            return response.json() if response.text else {}
            
        except requests.exceptions.Timeout:
            raise BrightDataAPIError(f"Request timeout after {self.timeout} seconds", url=url)
        except requests.exceptions.RequestException as e:
            raise BrightDataAPIError(f"Request failed: {e!s}", url=url)
    
    def trigger_snapshot(
        self, 
        dataset_type: str, 
        urls: list[str], 
        snapshot_type: str = "standard",
        **kwargs
    ) -> str:
        """
        Запуск создания снапшота данных.
        
        Args:
            dataset_type: Тип датасета
            urls: Список URL для обработки
            snapshot_type: Тип снапшота (standard, discover_new)
            **kwargs: Дополнительные параметры
            
        Returns:
            ID созданного снапшота
            
        Raises:
            ValueError: При неизвестном типе датасета
            BrightDataAPIError: При ошибке API
        """
        if dataset_type not in self.datasets:
            raise ValueError(f"Unknown dataset type: {dataset_type}")
        
        dataset_id = self.datasets[dataset_type]
        
        # Подготовка данных
        data = []
        for url in urls:
            item = {"url": url}
            # Filter out empty values from kwargs
            for key, value in kwargs.items():
                # Skip discover_by (it goes to query params) and empty values
                if key == "discover_by":
                    continue
                # Skip empty strings, None, and empty lists
                if value is not None and value != "" and value != []:
                    item[key] = value
            data.append(item)
        
        # Параметры запроса
        params = {
            "dataset_id": dataset_id,
            "include_errors": "true",
        }
        
        if snapshot_type == "discover_new":
            params["type"] = "discover_new"
            params["discover_by"] = kwargs.get("discover_by", "url")
        
        # Запрос к API
        response = self._make_request(
            method="POST",
            endpoint="trigger",
            params=params,
            data=data,
        )
        
        # Извлечение snapshot_id
        if isinstance(response, dict) and "snapshot_id" in response:
            snapshot_id = str(response["snapshot_id"])
        elif isinstance(response, str):
            snapshot_id = response
        else:
            raise BrightDataAPIError(f"Unexpected response format: {response}")
        
        logger.info(f"Triggered snapshot {snapshot_id} for {dataset_type}")
        return snapshot_id
    
    def get_snapshot_status(self, snapshot_id: str) -> dict[str, Any]:
        """
        Получение статуса снапшота.
        
        Args:
            snapshot_id: ID снапшота
            
        Returns:
            Информация о статусе снапшота
        """
        response = self._make_request(
            method="GET",
            endpoint=f"snapshot/{snapshot_id}",
        )
        
        # BrightData API возвращает список когда snapshot готов
        if isinstance(response, list):
            logger.info(f"Snapshot {snapshot_id} is ready with {len(response)} items")
            return {"status": "ready", "data": response}
        
        # Если response - словарь, возвращаем как есть
        logger.debug(f"Snapshot {snapshot_id} status: {response}")
        return cast(dict[str, Any], response)
    
    def get_snapshot_data(self, snapshot_id: str) -> list[dict[Any, Any]]:
        """
        Получение данных из готового снапшота.
        
        Args:
            snapshot_id: ID снапшота
            
        Returns:
            Список данных из снапшота
            
        Raises:
            SnapshotNotReadyError: Если снапшот еще не готов
        """
        # Проверяем статус
        status = self.get_snapshot_status(snapshot_id)
        
        if status.get("status") != "ready":
            raise SnapshotNotReadyError(
                f"Snapshot {snapshot_id} is not ready. Status: {status.get('status')}"
            )
        
        # Если данные уже есть в статусе (после нашей модификации get_snapshot_status)
        if "data" in status:
            data = status["data"]
            logger.info(f"Using {len(data)} items from snapshot {snapshot_id} status response")
            return cast(list[dict[Any, Any]], data)
        
        # Иначе получаем данные отдельным запросом
        response = self._make_request(
            method="GET",
            endpoint=f"snapshot/{snapshot_id}/data",
        )
        
        if not isinstance(response, list):
            logger.warning(f"Unexpected response format for snapshot {snapshot_id}: {type(response)}")
            return []
        
        logger.info(f"Retrieved {len(response)} items from snapshot {snapshot_id}")
        return response
    
    def wait_for_snapshot(
        self, 
        snapshot_id: str, 
        max_attempts: int = 200, 
        check_interval: int = 3
    ) -> dict[str, Any]:
        """
        Ожидание готовности снапшота.
        
        Args:
            snapshot_id: ID снапшота
            max_attempts: Максимальное количество попыток
            check_interval: Интервал между проверками в секундах
            
        Returns:
            Финальный статус снапшота
            
        Raises:
            TimeoutError: При превышении времени ожидания
            BrightDataAPIError: При ошибке снапшота
        """
        attempt = 0
        
        while attempt < max_attempts:
            status = self.get_snapshot_status(snapshot_id)
            current_status = status.get("status")
            
            if current_status == "ready":
                return status
            elif current_status == "failed":
                error_msg = status.get("error", "Unknown error")
                raise BrightDataAPIError(f"Snapshot failed: {error_msg}", snapshot_id=snapshot_id)
            
            logger.info(
                f"Waiting for snapshot {snapshot_id}... "
                f"Status: {current_status}, Attempt: {attempt + 1}/{max_attempts}"
            )
            
            time.sleep(check_interval)
            attempt += 1
        
        raise TimeoutError(
            f"Snapshot {snapshot_id} did not complete after {max_attempts * check_interval} seconds"
        )