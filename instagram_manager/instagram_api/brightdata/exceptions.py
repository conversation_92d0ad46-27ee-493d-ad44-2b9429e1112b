"""
BrightData API exceptions.

Все кастомные исключения для работы с BrightData API.
"""


class BrightDataAPIError(Exception):
    """Базовое исключение для ошибок BrightData API."""

    def __init__(
        self, message: str, url: str | None = None, snapshot_id: str | None = None
    ):
        super().__init__(message)
        self.url = url
        self.snapshot_id = snapshot_id

    def __str__(self):
        base_msg = super().__str__()
        if self.url:
            base_msg += f" | URL: {self.url}"
        if self.snapshot_id:
            base_msg += f" | Snapshot ID: {self.snapshot_id}"
        return base_msg


class RateLimitError(BrightDataAPIError):
    """Исключение для превышения rate limit."""

    pass


class ProfileNotFoundError(BrightDataAPIError):
    """Исключение для недоступного профиля."""

    pass


class PrivateProfileError(BrightDataAPIError):
    """Исключение для приватного профиля."""

    pass


class InstagramRateLimitError(BrightDataAPIError):
    """Исключение для rate limit от Instagram."""

    pass


class SnapshotNotReadyError(BrightDataAPIError):
    """Исключение для неготового снапшота."""

    pass


class ValidationError(BrightDataAPIError):
    """Исключение для ошибок валидации данных."""

    pass