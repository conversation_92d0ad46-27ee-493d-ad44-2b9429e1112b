"""
Utility functions for BrightData API.

Вспомогательные функции для работы с API.
"""

import re
from typing import Any
from urllib.parse import urlparse


def extract_username_from_url(url: str) -> str | None:
    """
    Извлечение имени пользователя из URL Instagram.
    
    Args:
        url: URL профиля Instagram
        
    Returns:
        Имя пользователя или None
    """
    patterns = [
        r"instagram\.com/([A-Za-z0-9_.]+)/?$",
        r"instagram\.com/([A-Za-z0-9_.]+)/\?",
        r"instagram\.com/stories/([A-Za-z0-9_.]+)",
    ]
    
    for pattern in patterns:
        match = re.search(pattern, url)
        if match:
            username = match.group(1)
            # Исключаем системные пути
            if username not in ["p", "reel", "tv", "explore", "accounts"]:
                return username
    
    return None


def extract_post_id_from_url(url: str) -> str | None:
    """
    Извлечение ID поста из URL.
    
    Args:
        url: URL поста Instagram
        
    Returns:
        ID поста (shortcode) или None
    """
    patterns = [
        r"/p/([A-Za-z0-9_-]+)/",
        r"/reel/([A-Za-z0-9_-]+)/",
        r"/tv/([A-Za-z0-9_-]+)/",
    ]
    
    for pattern in patterns:
        match = re.search(pattern, url)
        if match:
            return match.group(1)
    
    return None


def extract_hashtag_from_url(url: str) -> str | None:
    """
    Извлечение хэштега из URL.
    
    Args:
        url: URL страницы хэштега
        
    Returns:
        Название хэштега (без #) или None
    """
    pattern = r"/explore/tags/([^/]+)/"
    match = re.search(pattern, url)
    
    if match:
        return match.group(1)
    
    return None


def build_instagram_url(entity_type: str, identifier: str) -> str:
    """
    Построение URL Instagram.
    
    Args:
        entity_type: Тип сущности (profile, post, hashtag, reel)
        identifier: Идентификатор (username, shortcode, hashtag)
        
    Returns:
        Полный URL Instagram
    """
    base_url = "https://www.instagram.com"
    
    url_patterns = {
        "profile": f"{base_url}/{identifier}/",
        "post": f"{base_url}/p/{identifier}/",
        "reel": f"{base_url}/reel/{identifier}/",
        "hashtag": f"{base_url}/explore/tags/{identifier}/",
        "stories": f"{base_url}/stories/{identifier}/",
    }
    
    return url_patterns.get(entity_type, f"{base_url}/{identifier}/")


def normalize_instagram_url(url: str) -> str:
    """
    Нормализация URL Instagram.
    
    Args:
        url: Исходный URL
        
    Returns:
        Нормализованный URL
    """
    # Удаляем query параметры
    parsed = urlparse(url)
    clean_url = f"{parsed.scheme}://{parsed.netloc}{parsed.path}"
    
    # Убеждаемся что есть слеш в конце
    if not clean_url.endswith("/"):
        clean_url += "/"
    
    # Заменяем www. на основной домен
    clean_url = clean_url.replace("www.instagram.com", "instagram.com")
    
    return clean_url


def parse_instagram_date(date_str: str | None) -> str | None:
    """
    Парсинг даты Instagram в стандартный формат.
    
    Args:
        date_str: Строка с датой
        
    Returns:
        Дата в формате ISO или None
    """
    if not date_str:
        return None
    
    from datetime import datetime
    
    # Попробуем разные форматы
    date_formats = [
        "%Y-%m-%d %H:%M:%S",
        "%Y-%m-%dT%H:%M:%S",
        "%Y-%m-%dT%H:%M:%SZ",
        "%Y-%m-%dT%H:%M:%S.%fZ",
    ]
    
    for fmt in date_formats:
        try:
            dt = datetime.strptime(date_str, fmt)
            return dt.isoformat()
        except ValueError:
            continue
    
    # Если не удалось распарсить, возвращаем как есть
    return date_str


def format_number(num: int | float | None) -> str:
    """
    Форматирование числа для отображения.
    
    Args:
        num: Число для форматирования
        
    Returns:
        Отформатированная строка
    """
    if num is None:
        return "0"
    
    if num >= 1_000_000:
        return f"{num / 1_000_000:.1f}M"
    elif num >= 1_000:
        return f"{num / 1_000:.1f}K"
    else:
        return str(int(num))


def extract_media_urls(post_data: dict) -> list[str]:
    """
    Извлечение URL медиафайлов из данных поста.
    
    Args:
        post_data: Данные поста
        
    Returns:
        Список URL медиафайлов
    """
    media_urls = []
    
    # Основное изображение/видео
    if post_data.get("image_url"):
        media_urls.append(post_data["image_url"])
    
    if post_data.get("video_url"):
        media_urls.append(post_data["video_url"])
    
    # Медиа из carousel
    if post_data.get("media"):
        for media_item in post_data["media"]:
            if isinstance(media_item, dict):
                if media_item.get("image_url"):
                    media_urls.append(media_item["image_url"])
                if media_item.get("video_url"):
                    media_urls.append(media_item["video_url"])
    
    return media_urls


def merge_duplicate_data(items: list[dict[str, Any]], key_field: str) -> list[dict[str, Any]]:
    """
    Объединение дубликатов в списке данных.
    
    Args:
        items: Список элементов
        key_field: Поле для идентификации дубликатов
        
    Returns:
        Список уникальных элементов
    """
    seen = {}
    
    for item in items:
        key = item.get(key_field)
        if key and key not in seen:
            seen[key] = item
    
    return list(seen.values())