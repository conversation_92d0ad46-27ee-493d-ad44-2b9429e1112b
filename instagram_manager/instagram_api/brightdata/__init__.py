"""
BrightData API module.

Модуль для работы с BrightData API для получения данных Instagram.
Предоставляет фасад BrightDataClient для обратной совместимости.
"""

import logging

from .clients import CommentClient, HashtagClient, PostClient, ProfileClient
from .exceptions import (
    BrightDataAPIError,
    InstagramRateLimitError,
    PrivateProfileError,
    ProfileNotFoundError,
    RateLimitError,
    SnapshotNotReadyError,
    ValidationError,
)

logger = logging.getLogger(__name__)


class BrightDataClient:
    """
    Фасад для обратной совместимости.
    
    Объединяет функциональность всех специализированных клиентов
    в один класс для сохранения существующего API.
    """
    
    def __init__(self):
        """Инициализация всех клиентов."""
        # Инициализируем специализированные клиенты
        self.profile_client = ProfileClient()
        self.post_client = PostClient()
        self.comment_client = CommentClient()
        self.hashtag_client = HashtagClient()
        
        
        # Копируем атрибуты из базового клиента для совместимости
        self.api_token = self.profile_client.api_token
        self.base_url = self.profile_client.base_url
        self.datasets = self.profile_client.datasets
        self.rate_limits = self.profile_client.rate_limits
        self.session = self.profile_client.session
        
        logger.info("Initialized BrightDataClient facade")
    
    # Методы для профилей
    def get_profile(self, username: str, **kwargs):
        """Получение профиля Instagram."""
        return self.profile_client.get_profile(username, **kwargs)
    
    def get_batch_profiles(self, usernames: list[str], **kwargs):
        """Получение нескольких профилей."""
        return self.profile_client.get_batch_profiles(usernames, **kwargs)
    
    # Методы для постов
    def get_posts(self, username: str, **kwargs):
        """Получение постов профиля."""
        return self.post_client.get_posts(username, **kwargs)
    
    def get_posts_by_urls(self, urls: list[str], **kwargs):
        """Получение постов по URL."""
        return self.post_client.get_posts_by_urls(urls, **kwargs)
    
    def get_batch_posts(self, urls: list[str], **kwargs):
        """Псевдоним для get_posts_by_urls для совместимости."""
        return self.post_client.get_posts_by_urls(urls, **kwargs)
    
    def get_hashtag_posts(self, hashtag: str, **kwargs):
        """Получение постов по хэштегу."""
        return self.post_client.get_hashtag_posts(hashtag, **kwargs)
    
    # Методы для комментариев
    def get_post_comments(self, post_url: str, **kwargs):
        """Получение комментариев к посту."""
        return self.comment_client.get_post_comments(post_url, **kwargs)
    
    def get_batch_comments(self, post_urls: list[str], **kwargs):
        """Получение комментариев для нескольких постов."""
        return self.comment_client.get_batch_comments(post_urls, **kwargs)
    
    # Низкоуровневые методы для совместимости
    def trigger_snapshot(self, dataset_type: str, urls: list[str], **kwargs):
        """Запуск снапшота (для обратной совместимости)."""
        return self.profile_client.trigger_snapshot(dataset_type, urls, **kwargs)
    
    def trigger_discover(self, dataset_type: str, urls: list[str], **kwargs):
        """Запуск discover (для обратной совместимости)."""
        return self.profile_client.trigger_snapshot(
            dataset_type, urls, snapshot_type="discover_new", **kwargs
        )
    
    def get_snapshot_status(self, snapshot_id: str):
        """Получение статуса снапшота."""
        return self.profile_client.get_snapshot_status(snapshot_id)
    
    def get_snapshot_data(self, snapshot_id: str):
        """Получение данных снапшота."""
        return self.profile_client.get_snapshot_data(snapshot_id)
    
    def _make_request(self, method: str, endpoint: str, **kwargs):
        """Низкоуровневый метод для запросов (для совместимости)."""
        return self.profile_client._make_request(method, endpoint, **kwargs)
    
    
    def _determine_post_type(self, post):
        """Определение типа поста."""
        return self.post_client._determine_post_type(post)
    
    def get_batch_posts_from_snapshot(self, snapshot_id: str, profile_urls: list[str], **kwargs):
        """Получение постов из готового batch snapshot."""
        # Ожидаем готовности снапшота  
        self.wait_for_snapshot(snapshot_id)
        
        # Получаем данные
        posts_data = self.get_snapshot_data(snapshot_id)
        
        logger.info(
            f"Batch snapshot completed. Total posts: {len(posts_data)} "
            f"for {len(profile_urls)} profiles"
        )
        
        # Возвращаем сырые данные для обработки в сервисе
        return posts_data
    
    def wait_for_snapshot(self, snapshot_id: str, **kwargs):
        """Ожидание готовности снапшота."""
        return self.profile_client.wait_for_snapshot(snapshot_id, **kwargs)


# Функция для выбора sync/async клиента
def get_client(async_mode: bool = False):
    """
    Получение клиента BrightData.
    
    Args:
        async_mode: Если True, возвращает async версию клиента
        
    Returns:
        BrightDataClient или AsyncBrightDataClient
    """
    if async_mode:
        from .async_client import AsyncBrightDataClient
        return AsyncBrightDataClient()
    return BrightDataClient()


# Экспортируем все необходимые классы
__all__ = [
    "BrightDataAPIError",
    "BrightDataClient",
    "CommentClient",
    "HashtagClient",
    "InstagramRateLimitError",
    "PostClient",
    "PrivateProfileError",
    "ProfileClient",
    "ProfileNotFoundError",
    "RateLimitError",
    "SnapshotNotReadyError",
    "ValidationError",
    "get_client",
]