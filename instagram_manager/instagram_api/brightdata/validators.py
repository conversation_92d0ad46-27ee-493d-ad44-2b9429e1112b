"""
Data validators for BrightData API responses.

Валидация и преобразование данных от API.
"""

import logging
from typing import Any, TypeVar

from pydantic import BaseModel
from pydantic import ValidationError as PydanticValidationError

from instagram_manager.instagram_api.brightdata.exceptions import ValidationError
from instagram_manager.schemas.validation import ValidationHelpers

logger = logging.getLogger(__name__)

T = TypeVar("T", bound=BaseModel)


class DataValidator:
    """Валидатор данных от BrightData API."""
    
    @staticmethod
    def validate_single(
        data: dict, 
        model_class: type[T], 
        skip_on_error: bool = True
    ) -> T | None:
        """
        Валидация одного объекта.
        
        Args:
            data: Данные для валидации
            model_class: Класс Pydantic модели
            skip_on_error: Пропускать объект при ошибке валидации
            
        Returns:
            Валидированный объект или None при ошибке
            
        Raises:
            ValidationError: При ошибке валидации (если skip_on_error=False)
        """
        try:
            return model_class(**data)
        except PydanticValidationError as e:
            logger.warning(f"Validation error for {model_class.__name__}: {e}")
            if not skip_on_error:
                raise ValidationError(f"Validation failed: {e!s}")
            return None
    
    @staticmethod
    def validate_list(
        data_list: list[dict], 
        model_class: type[T], 
        skip_errors: bool = True
    ) -> list[T]:
        """
        Валидация списка объектов.
        
        Args:
            data_list: Список данных для валидации
            model_class: Класс Pydantic модели
            skip_errors: Пропускать объекты с ошибками валидации
            
        Returns:
            Список валидированных объектов
        """
        return ValidationHelpers.validate_response_list(
            data_list, 
            model_class, 
            skip_errors=skip_errors
        )
    
    @staticmethod
    def prepare_profile_data(data: dict) -> dict:
        """
        Подготовка данных профиля для валидации.
        
        Args:
            data: Сырые данные профиля
            
        Returns:
            Подготовленные данные
        """
        # Убедимся что есть обязательные поля
        if "username" not in data and "user_name" in data:
            data["username"] = data["user_name"]
        
        if "url" not in data and "username" in data:
            data["url"] = f"https://www.instagram.com/{data['username']}/"
        
        return data
    
    @staticmethod
    def prepare_post_data(data: dict) -> dict:
        """
        Подготовка данных поста для валидации.
        
        Args:
            data: Сырые данные поста
            
        Returns:
            Подготовленные данные
        """
        # Обеспечиваем наличие URL
        if "url" not in data and "link" in data:
            data["url"] = data["link"]
        elif "url" not in data and "shortcode" in data:
            data["url"] = f"https://www.instagram.com/p/{data['shortcode']}/"
        
        # Преобразуем типы медиа если нужно
        if "content_type" in data and data["content_type"] == "image":
            data["content_type"] = "photo"
        
        return data
    
    @staticmethod
    def prepare_comment_data(data: dict) -> dict:
        """
        Подготовка данных комментария для валидации.
        
        Args:
            data: Сырые данные комментария
            
        Returns:
            Подготовленные данные
        """
        # Логируем входящие данные для отладки
        if logger.isEnabledFor(logging.DEBUG):
            logger.debug(f"Preparing comment data with fields: {list(data.keys())}")
        
        # Маппинг ID комментария - BrightData возвращает "id"
        if "id" in data and "comment_id" not in data:
            data["comment_id"] = data["id"]
        elif "comment_id" in data and "id" not in data:
            data["id"] = data["comment_id"]
        
        # Ensure required fields have at least default values
        # Comment text
        if "comment" not in data and "text" in data:
            data["comment"] = data["text"]
        elif "comment" not in data and "comment_text" in data:
            data["comment"] = data["comment_text"]
        elif "comment" not in data:
            # If no comment text found, log warning
            logger.warning(f"No comment text found in data for comment ID: {data.get('id', 'unknown')}")
            data["comment"] = ""
            
        # Comment user
        if "comment_user" not in data:
            if "username" in data:
                data["comment_user"] = data["username"]
            elif "user" in data:
                data["comment_user"] = data["user"]
            elif "author" in data:
                data["comment_user"] = data["author"]
            else:
                logger.warning(f"No comment user found in data for comment ID: {data.get('id', 'unknown')}")
                data["comment_user"] = "unknown"
                
        # Comment date
        if "comment_date" not in data:
            if "timestamp" in data:
                data["comment_date"] = data["timestamp"]
            elif "created_at" in data:
                data["comment_date"] = data["created_at"]
            elif "date" in data:
                data["comment_date"] = data["date"]
            else:
                logger.warning(f"No comment date found in data for comment ID: {data.get('id', 'unknown')}")
                # Don't set a default - let the schema validator handle it
        
        # Преобразуем булевы значения
        if "is_verified" in data and isinstance(data["is_verified"], str):
            data["is_verified"] = data["is_verified"].lower() == "true"
        if "is_verified_user" in data and isinstance(data["is_verified_user"], str):
            data["is_verified_user"] = data["is_verified_user"].lower() == "true"
        
        # Handle numeric fields
        for field in ["likes_number", "likes", "like_count", "replies_number", "replies_count"]:
            if field in data and isinstance(data[field], str):
                try:
                    data[field] = int(data[field])
                except (ValueError, TypeError):
                    data[field] = 0
        
        # Метаданные от BrightData (url, timestamp, input) будут сохранены
        # благодаря extra="allow" в схеме
        
        return data
    
    @staticmethod
    def extract_error_info(data: dict) -> dict[str, Any] | None:
        """
        Извлечение информации об ошибке из ответа.
        
        Args:
            data: Данные ответа
            
        Returns:
            Информация об ошибке или None
        """
        if "error" in data:
            return {
                "error": data.get("error"),
                "error_code": data.get("error_code"),
                "error_message": data.get("error_message"),
                "url": data.get("url"),
            }
        return None
    
    @staticmethod
    def is_rate_limit_error(data: dict) -> bool:
        """
        Проверка является ли ошибка rate limit.
        
        Args:
            data: Данные ответа
            
        Returns:
            True если это rate limit ошибка
        """
        error_info = DataValidator.extract_error_info(data)
        if not error_info:
            return False
        
        error_message = str(error_info.get("error", "")).lower()
        return any(keyword in error_message for keyword in [
            "rate limit",
            "too many requests",
            "throttled",
            "please wait"
        ])
    
    @staticmethod
    def is_private_profile_error(data: dict) -> bool:
        """
        Проверка является ли ошибка приватным профилем.
        
        Args:
            data: Данные ответа
            
        Returns:
            True если профиль приватный
        """
        error_info = DataValidator.extract_error_info(data)
        if not error_info:
            return False
        
        error_message = str(error_info.get("error", "")).lower()
        return "private" in error_message or "is_private" in data