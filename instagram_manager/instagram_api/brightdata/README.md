# BrightData API Module Structure

Этот модуль содержит все компоненты для работы с BrightData API, организованные в модульную структуру для лучшей поддерживаемости.

## Структура файлов

### Основные компоненты
- **exceptions.py** - Все кастомные исключения API
- **client.py** - Базовый клиент с общей функциональностью
- **rate_limiter.py** - Управление rate limiting
- **cache_manager.py** - Кеширование ответов API
- **validators.py** - Валидация и подготовка данных
- **utils.py** - Вспомогательные функции

### Специализированные клиенты (clients/)
- **profile.py** - `ProfileClient` для работы с профилями
- **post.py** - `PostClient` для работы с постами
- **comment.py** - `CommentClient` для работы с комментариями
- **hashtag.py** - `HashtagClient` для работы с хэштегами

### Главный файл
- **__init__.py** - Экспортирует все классы и предоставляет фасад `BrightDataClient` для обратной совместимости

## Использование

### Обратная совместимость (рекомендуется)
```python
# Старый способ продолжает работать
from instagram_manager.instagram_api import BrightDataClient

client = BrightDataClient()
profile = client.get_profile("username")
posts = client.get_posts("username")
comments = client.get_post_comments("post_url")
```

### Новый способ (для большего контроля)
```python
# Импорт специализированных клиентов
from instagram_manager.instagram_api.brightdata import (
    ProfileClient,
    PostClient,
    CommentClient,
    HashtagClient
)

# Использование отдельных клиентов
profile_client = ProfileClient()
post_client = PostClient()

profile = profile_client.get_profile("username")
posts = post_client.get_posts("username", post_types=["photo", "video"])
```

### Работа с исключениями
```python
from instagram_manager.instagram_api.brightdata import (
    BrightDataAPIError,
    RateLimitError,
    ProfileNotFoundError,
    PrivateProfileError
)

try:
    profile = client.get_profile("username")
except ProfileNotFoundError:
    print("Profile not found")
except PrivateProfileError:
    print("Profile is private")
except RateLimitError as e:
    print(f"Rate limit exceeded: {e}")
```

## Компоненты

### RateLimiter
Управляет ограничениями на количество запросов к API:
```python
from instagram_manager.instagram_api.brightdata import RateLimiter

limiter = RateLimiter()
if limiter.check_rate_limit("endpoint"):
    # Выполнить запрос
    limiter.update_rate_limit("endpoint")
else:
    wait_time = limiter.get_wait_time("endpoint")
    print(f"Wait {wait_time} seconds")
```

### CacheManager
Кеширует ответы API для оптимизации:
```python
from instagram_manager.instagram_api.brightdata import CacheManager

cache = CacheManager()
# Получить из кеша
data = cache.get("profile", "username")
# Сохранить в кеш
cache.set("profile", "username", profile_data)
```

### DataValidator
Валидирует данные от API:
```python
from instagram_manager.instagram_api.brightdata.validators import DataValidator

# Валидация одного объекта
validated = DataValidator.validate_single(data, InstagramProfileResponse)

# Валидация списка
validated_list = DataValidator.validate_list(data_list, InstagramPostResponse)
```

## Преимущества новой структуры

1. **Модульность** - Каждый компонент в отдельном файле
2. **Переиспользование** - Общая функциональность в базовых классах
3. **Тестируемость** - Легче тестировать отдельные компоненты
4. **Расширяемость** - Легко добавлять новые типы клиентов
5. **Обратная совместимость** - Старый код продолжает работать

## Миграция со старой структуры

Не требуется никаких изменений в существующем коде. Фасад `BrightDataClient` обеспечивает полную обратную совместимость.

Старый файл `brightdata_client_old.py` можно удалить после проверки, что все работает корректно.

## Расширение функциональности

### Добавление нового типа клиента
1. Создайте файл в `clients/` (например, `stories.py`)
2. Наследуйтесь от `BaseBrightDataClient`
3. Добавьте специфичные методы
4. Экспортируйте в `clients/__init__.py`
5. Добавьте в фасад `BrightDataClient` если нужна обратная совместимость

### Добавление нового исключения
1. Добавьте класс в `exceptions.py`
2. Наследуйтесь от `BrightDataAPIError`
3. Экспортируйте в `__init__.py`