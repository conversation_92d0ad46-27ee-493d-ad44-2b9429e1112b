"""
Profile-specific BrightData client.

Клиент для работы с профилями Instagram через BrightData API.
"""

import logging

from instagram_manager.schemas.brightdata import InstagramProfileResponse

from ..client import BaseBrightDataClient
from ..exceptions import PrivateProfileError, ProfileNotFoundError
from ..validators import DataValidator

logger = logging.getLogger(__name__)


class ProfileClient(BaseBrightDataClient):
    """Клиент для работы с профилями Instagram."""
    
    def get_profile(
        self,
        username: str
    ) -> InstagramProfileResponse:
        """
        Получение данных профиля Instagram.
        
        Args:
            username: Имя пользователя Instagram
            
        Returns:
            Валидированные данные профиля
            
        Raises:
            ProfileNotFoundError: Если профиль не найден
            PrivateProfileError: Если профиль приватный
        """
        logger.info(f"Fetching profile data for {username}")
        
        # Запускаем снапшот
        profile_url = f"https://www.instagram.com/{username}/"
        snapshot_id = self.trigger_snapshot("instagram_profile", [profile_url])
        
        # Ожидаем готовности
        self.wait_for_snapshot(snapshot_id)
        
        # Получаем данные
        data_list = self.get_snapshot_data(snapshot_id)
        
        if not data_list:
            raise ProfileNotFoundError(f"Profile not found: {username}", url=profile_url)
        
        profile_data = data_list[0]
        
        # Проверяем на ошибки
        if DataValidator.extract_error_info(profile_data):
            if DataValidator.is_private_profile_error(profile_data):
                raise PrivateProfileError(f"Profile is private: {username}", url=profile_url)
            raise ProfileNotFoundError(f"Failed to fetch profile: {username}", url=profile_url)
        
        # Подготавливаем и валидируем данные
        prepared_data = DataValidator.prepare_profile_data(profile_data)
        validated_profile = DataValidator.validate_single(
            prepared_data, 
            InstagramProfileResponse,
            skip_on_error=False
        )
        
        if not validated_profile:
            raise ProfileNotFoundError(f"Failed to validate profile data: {username}")
        
        return validated_profile
    
    def get_batch_profiles(
        self,
        usernames: list[str],
        skip_errors: bool = True
    ) -> list[InstagramProfileResponse]:
        """
        Получение данных нескольких профилей.
        
        Args:
            usernames: Список имен пользователей
            skip_errors: Пропускать профили с ошибками
            
        Returns:
            Список валидированных профилей
        """
        profiles = []
        
        if usernames:
            logger.info(f"Fetching {len(usernames)} profiles from API")
            
            # Формируем URLs
            urls = [f"https://www.instagram.com/{username}/" for username in usernames]
            
            # Запускаем снапшот
            snapshot_id = self.trigger_snapshot("instagram_profile", urls)
            
            # Ожидаем готовности
            self.wait_for_snapshot(snapshot_id)
            
            # Получаем данные
            data_list = self.get_snapshot_data(snapshot_id)
            
            # Обрабатываем результаты
            for data in data_list:
                try:
                    # Проверяем на ошибки
                    if DataValidator.extract_error_info(data):
                        if not skip_errors:
                            raise ProfileNotFoundError("Failed to fetch profile", url=data.get("url"))
                        continue
                    
                    # Валидируем
                    prepared_data = DataValidator.prepare_profile_data(data)
                    profile = DataValidator.validate_single(
                        prepared_data,
                        InstagramProfileResponse,
                        skip_on_error=skip_errors
                    )
                    
                    if profile:
                        profiles.append(profile)
                        
                
                except Exception as e:
                    if not skip_errors:
                        raise
                    logger.warning(f"Failed to process profile: {e}")
        
        return profiles