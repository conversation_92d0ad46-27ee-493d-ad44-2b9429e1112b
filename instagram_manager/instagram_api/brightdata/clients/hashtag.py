"""
Hashtag-specific BrightData client.

Клиент для работы с хэштегами Instagram через BrightData API.
"""

import logging

from ..client import BaseBrightDataClient

logger = logging.getLogger(__name__)


class HashtagClient(BaseBrightDataClient):
    """Клиент для работы с хэштегами Instagram."""
    
    def get_hashtag_info(self, hashtag: str) -> dict:
        """
        Получение информации о хэштеге.
        
        Args:
            hashtag: Название хэштега (без #)
            
        Returns:
            Информация о хэштеге
        """
        logger.info(f"Fetching info for hashtag: #{hashtag}")
        
        hashtag_url = f"https://www.instagram.com/explore/tags/{hashtag}/"
        
        # Запускаем снапшот
        snapshot_id = self.trigger_snapshot("instagram_hashtag", [hashtag_url])
        
        # Ожидаем готовности
        self.wait_for_snapshot(snapshot_id)
        
        # Получаем данные
        data = self.get_snapshot_data(snapshot_id)
        
        if not data:
            return {
                "name": hashtag,
                "post_count": 0,
                "top_posts": [],
                "recent_posts": []
            }
        
        hashtag_data = data[0] if isinstance(data, list) else data
        
        return {
            "name": hashtag,
            "post_count": hashtag_data.get("post_count", 0),
            "top_posts": hashtag_data.get("top_posts", []),
            "recent_posts": hashtag_data.get("recent_posts", [])
        }
    
    def get_related_hashtags(self, hashtag: str) -> list[str]:
        """
        Получение связанных хэштегов.
        
        Args:
            hashtag: Название хэштега (без #)
            
        Returns:
            Список связанных хэштегов
        """
        hashtag_info = self.get_hashtag_info(hashtag)
        
        # Извлекаем хэштеги из постов
        related = set()
        
        for posts_list in [hashtag_info.get("top_posts", []), hashtag_info.get("recent_posts", [])]:
            for post in posts_list:
                if isinstance(post, dict):
                    # Извлекаем хэштеги из описания
                    caption = post.get("caption", "")
                    hashtags = self._extract_hashtags_from_text(caption)
                    related.update(hashtags)
        
        # Удаляем исходный хэштег
        related.discard(hashtag.lower())
        
        return list(related)
    
    def search_hashtags(self, query: str, limit: int = 10) -> list[dict]:
        """
        Поиск хэштегов по запросу.
        
        Args:
            query: Поисковый запрос
            limit: Максимальное количество результатов
            
        Returns:
            Список найденных хэштегов с информацией
        """
        # BrightData не поддерживает прямой поиск хэштегов
        # Это заглушка для возможного будущего расширения
        logger.warning("Hashtag search is not supported by BrightData API")
        return []
    
    def get_trending_hashtags(self, category: str | None = None) -> list[dict]:
        """
        Получение трендовых хэштегов.
        
        Args:
            category: Категория (опционально)
            
        Returns:
            Список трендовых хэштегов
        """
        # BrightData не предоставляет трендовые хэштеги
        # Это заглушка для возможного будущего расширения
        logger.warning("Trending hashtags are not supported by BrightData API")
        return []
    
    def _extract_hashtags_from_text(self, text: str) -> set[str]:
        """
        Извлечение хэштегов из текста.
        
        Args:
            text: Текст для анализа
            
        Returns:
            Множество найденных хэштегов (без #)
        """
        import re
        
        if not text:
            return set()
        
        # Паттерн для поиска хэштегов
        pattern = r"#(\w+)"
        matches = re.findall(pattern, text)
        
        # Возвращаем в нижнем регистре для унификации
        return {match.lower() for match in matches}
