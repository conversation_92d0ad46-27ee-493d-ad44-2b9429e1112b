"""
Post-specific BrightData client.

Клиент для работы с постами Instagram через BrightData API.
"""

import logging

from instagram_manager.schemas.brightdata import InstagramPostResponse

from ..client import BaseBrightDataClient
from ..validators import DataValidator

logger = logging.getLogger(__name__)


class PostClient(BaseBrightDataClient):
    """Клиент для работы с постами Instagram."""
    
    def get_posts(
        self,
        username: str,
        start_date: str = "",
        end_date: str = "",
        post_types: list[str] | None = None,
        limit: int | None = None,
        skip_validation_errors: bool = True,
    ) -> list[InstagramPostResponse]:
        """
        Получение постов профиля.
        
        Args:
            username: Имя пользователя Instagram
            start_date: Начальная дата (YYYY-MM-DD)
            end_date: Конечная дата (YYYY-MM-DD)
            post_types: Типы постов (photo, video, carousel, reel)
            limit: Максимальное количество постов
            skip_validation_errors: Пропускать посты с ошибками валидации
            
        Returns:
            Список валидированных постов
        """
        logger.info(f"Fetching posts for {username}")
        
        # Подготавливаем параметры
        kwargs = {}
        if start_date:
            kwargs["start_date"] = start_date
        if end_date:
            kwargs["end_date"] = end_date
        if limit:
            kwargs["num_of_posts"] = str(limit)
        
        # Обрабатываем post_types для API
        if post_types:
            # BrightData API поддерживает только post_type (singular) со значениями 'reel' или 'post'
            has_regular_posts = any(pt in ["photo", "video", "carousel"] for pt in post_types)
            has_reels = "reel" in post_types
            
            if has_reels and not has_regular_posts:
                # Только reels - используем API фильтр
                kwargs["post_type"] = "reel"
                logger.info("Filtering for reels only via API")
            else:
                # Для всех остальных случаев не фильтруем на уровне API
                # Фильтрация будет выполнена локально
                if has_regular_posts and has_reels:
                    logger.info("Mixed post types selected - fetching all posts for local filtering")
                else:
                    logger.info("Regular posts selected - fetching all posts for local filtering")
        
        # Запускаем discover для получения всех постов
        profile_url = f"https://www.instagram.com/{username}"
        snapshot_id = self.trigger_snapshot(
            "instagram_posts",
            [profile_url],
            snapshot_type="discover_new",
            discover_by="url",
            **kwargs
        )
        
        # Ожидаем готовности
        self.wait_for_snapshot(snapshot_id)
        
        # Получаем данные
        posts_data = self.get_snapshot_data(snapshot_id)
        
        if not posts_data:
            logger.warning(f"No posts found for {username}")
            return []
        
        logger.info(f"Received {len(posts_data)} raw posts for {username}")
        
        # Валидируем посты
        validated_posts = []
        
        for post_data in posts_data:
            # Подготавливаем данные
            prepared_data = DataValidator.prepare_post_data(post_data)
            
            # Валидируем
            validated_post = DataValidator.validate_single(
                prepared_data,
                InstagramPostResponse,
                skip_on_error=skip_validation_errors
            )
            
            if validated_post:
                # Фильтруем по типам если нужно
                if post_types and self._should_filter_post_type(validated_post, post_types):
                    continue
                
                validated_posts.append(validated_post)
        
        logger.info(f"Validated {len(validated_posts)} posts for {username}")
        
        return validated_posts
    
    def get_posts_by_urls(
        self,
        urls: list[str],
        skip_validation_errors: bool = True
    ) -> list[InstagramPostResponse]:
        """
        Получение постов по списку URL.
        
        Args:
            urls: Список URL постов
            skip_validation_errors: Пропускать посты с ошибками валидации
            
        Returns:
            Список валидированных постов
        """
        if not urls:
            return []
        
        logger.info(f"Fetching {len(urls)} posts by URLs")
        
        # Запускаем снапшот
        snapshot_id = self.trigger_snapshot("instagram_posts", urls)
        
        # Ожидаем готовности
        self.wait_for_snapshot(snapshot_id)
        
        # Получаем данные
        posts_data = self.get_snapshot_data(snapshot_id)
        
        # Валидируем
        validated_posts = []
        
        for post_data in posts_data:
            prepared_data = DataValidator.prepare_post_data(post_data)
            validated_post = DataValidator.validate_single(
                prepared_data,
                InstagramPostResponse,
                skip_on_error=skip_validation_errors
            )
            
            if validated_post:
                validated_posts.append(validated_post)
        
        return validated_posts
    
    def get_hashtag_posts(
        self,
        hashtag: str,
        limit: int = 20,
        skip_validation_errors: bool = True
    ) -> list[InstagramPostResponse]:
        """
        Получение постов по хэштегу.
        
        Args:
            hashtag: Название хэштега (без #)
            limit: Максимальное количество постов
            skip_validation_errors: Пропускать посты с ошибками валидации
            
        Returns:
            Список валидированных постов
        """
        logger.info(f"Fetching posts for hashtag #{hashtag}")
        
        # URL хэштега
        hashtag_url = f"https://www.instagram.com/explore/tags/{hashtag}/"
        
        # Запускаем снапшот
        snapshot_id = self.trigger_snapshot(
            "instagram_hashtag",
            [hashtag_url],
            num_of_posts=limit
        )
        
        # Ожидаем готовности
        self.wait_for_snapshot(snapshot_id)
        
        # Получаем данные
        posts_data = self.get_snapshot_data(snapshot_id)
        
        # Валидируем
        validated_posts = []
        
        for post_data in posts_data:
            prepared_data = DataValidator.prepare_post_data(post_data)
            validated_post = DataValidator.validate_single(
                prepared_data,
                InstagramPostResponse,
                skip_on_error=skip_validation_errors
            )
            
            if validated_post:
                validated_posts.append(validated_post)
        
        return validated_posts
    
    def _should_filter_post_type(
        self, 
        post: InstagramPostResponse, 
        allowed_types: list[str]
    ) -> bool:
        """
        Проверка нужно ли отфильтровать пост по типу.
        
        Args:
            post: Пост для проверки
            allowed_types: Разрешенные типы
            
        Returns:
            True если пост нужно отфильтровать
        """
        # Определяем тип поста
        post_type = self._determine_post_type(post)
        
        # Проверяем входит ли в разрешенные
        return post_type not in allowed_types
    
    def _determine_post_type(self, post: InstagramPostResponse) -> str:
        """
        Определение типа поста.
        
        Args:
            post: Пост для анализа
            
        Returns:
            Тип поста (photo, video, carousel, reel)
        """
        # 1. Проверяем URL для определения reel
        if post.url and "/reel/" in post.url:
            return "reel"
        
        # 2. Проверяем content_type из BrightData
        if post.content_type:
            content_type_lower = post.content_type.lower()
            if "reel" in content_type_lower:
                return "reel"
            elif "video" in content_type_lower:
                return "video"
            elif "carousel" in content_type_lower:
                return "carousel"
        
        # 3. Проверяем is_video флаг
        if post.is_video:
            return "video"
        
        # 4. Проверяем наличие нескольких медиа (carousel)
        if post.media and len(post.media) > 1:
            return "carousel"
        
        # 5. По умолчанию считаем фото
        return "photo"