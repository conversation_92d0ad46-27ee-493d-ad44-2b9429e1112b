"""
Comment-specific BrightData client.

Клиент для работы с комментариями Instagram через BrightData API.
"""

import logging

from instagram_manager.schemas.brightdata import InstagramCommentResponse

from ..client import BaseBrightDataClient
from ..validators import DataValidator

logger = logging.getLogger(__name__)


class CommentClient(BaseBrightDataClient):
    """Клиент для работы с комментариями Instagram."""
    
    def get_post_comments(
        self,
        post_url: str,
        skip_validation_errors: bool = True
    ) -> list[InstagramCommentResponse]:
        """
        Получение комментариев к посту.
        
        Args:
            post_url: URL поста Instagram
            skip_validation_errors: Пропускать комментарии с ошибками валидации
            
        Returns:
            Список валидированных комментариев
        """
        logger.warning(f"[BRIGHTDATA SINGLE] Fetching comments for SINGLE post: {post_url}")
        logger.warning("[BRIGHTDATA SINGLE] Using individual request instead of batch - this is inefficient!")
        
        # Запускаем снапшот
        snapshot_id = self.trigger_snapshot("instagram_comments", [post_url])
        logger.info(f"[BRIGHTDATA SINGLE] Created snapshot {snapshot_id} for single post comment request")
        
        # Ожидаем готовности
        self.wait_for_snapshot(snapshot_id)
        
        # Получаем данные
        comments_data = self.get_snapshot_data(snapshot_id)
        
        if not comments_data:
            logger.info(f"No comments found for post: {post_url}")
            return []
        
        logger.info(f"Received {len(comments_data)} raw comments")
        
        # Валидируем комментарии
        validated_comments = []
        
        for comment_data in comments_data:
            # Подготавливаем данные
            prepared_data = DataValidator.prepare_comment_data(comment_data)
            
            # Валидируем
            validated_comment = DataValidator.validate_single(
                prepared_data,
                InstagramCommentResponse,
                skip_on_error=skip_validation_errors
            )
            
            if validated_comment:
                validated_comments.append(validated_comment)
        
        logger.info(f"Validated {len(validated_comments)} comments")
        
        return validated_comments
    
    def get_batch_comments(
        self,
        post_urls: list[str],
        skip_validation_errors: bool = True
    ) -> dict[str, list[InstagramCommentResponse]]:
        """
        Получение комментариев для нескольких постов.
        
        Args:
            post_urls: Список URL постов
            skip_validation_errors: Пропускать комментарии с ошибками валидации
            
        Returns:
            Словарь {post_url: список комментариев}
        """
        if not post_urls:
            return {}
        
        logger.info(f"[BRIGHTDATA BATCH] Fetching comments for {len(post_urls)} posts in SINGLE batch request")
        logger.info(f"[BRIGHTDATA BATCH] URLs: {post_urls[:3]}... (showing first 3)")
        
        # Запускаем снапшот для всех URL
        snapshot_id = self.trigger_snapshot("instagram_comments", post_urls)
        logger.info(f"[BRIGHTDATA BATCH] Created snapshot {snapshot_id} for batch comment request")
        
        # Ожидаем готовности
        self.wait_for_snapshot(snapshot_id)
        
        # Получаем данные
        all_comments_data = self.get_snapshot_data(snapshot_id)
        
        # Группируем комментарии по постам
        comments_by_post: dict[str, list[InstagramCommentResponse]] = {}
        
        for comment_data in all_comments_data:
            # Получаем URL поста из комментария
            post_url = comment_data.get("post_url") or comment_data.get("post_link")
            if not post_url:
                logger.warning("Comment without post URL, skipping")
                continue
            
            # Инициализируем список если нужно
            if post_url not in comments_by_post:
                comments_by_post[post_url] = []
            
            # Валидируем комментарий
            prepared_data = DataValidator.prepare_comment_data(comment_data)
            validated_comment = DataValidator.validate_single(
                prepared_data,
                InstagramCommentResponse,
                skip_on_error=skip_validation_errors
            )
            
            if validated_comment:
                comments_by_post[post_url].append(validated_comment)
        
        # Логируем статистику
        for post_url, comments in comments_by_post.items():
            logger.info(f"Found {len(comments)} comments for {post_url}")
        
        return comments_by_post
    
    def get_comment_replies(
        self,
        comment_id: str,
        post_url: str,
        skip_validation_errors: bool = True
    ) -> list[InstagramCommentResponse]:
        """
        Получение ответов на комментарий.
        
        Args:
            comment_id: ID комментария
            post_url: URL поста
            skip_validation_errors: Пропускать ошибки валидации
            
        Returns:
            Список ответов на комментарий
        """
        # Получаем все комментарии поста
        all_comments = self.get_post_comments(
            post_url,
            skip_validation_errors=skip_validation_errors
        )
        
        # Фильтруем ответы на нужный комментарий
        replies = [
            comment for comment in all_comments
            if comment.parent_comment_id == comment_id
        ]
        
        return replies
    
    def _extract_shortcode_from_url(self, url: str) -> str | None:
        """
        Извлечение shortcode из URL поста.
        
        Args:
            url: URL поста
            
        Returns:
            Shortcode или None
        """
        import re
        
        # Паттерны для извлечения shortcode
        patterns = [
            r"/p/([A-Za-z0-9_-]+)/",
            r"/reel/([A-Za-z0-9_-]+)/",
            r"/tv/([A-Za-z0-9_-]+)/",
        ]
        
        for pattern in patterns:
            match = re.search(pattern, url)
            if match:
                return match.group(1)
        
        return None
