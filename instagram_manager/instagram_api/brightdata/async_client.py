"""
Async version of BrightData API client.

Асинхронный клиент для работы с BrightData API используя httpx.
"""

import json
import logging
from typing import Any, cast

import httpx
from django.conf import settings

from .client import BaseBrightDataClient
from .exceptions import BrightDataAPIError, RateLimitError, SnapshotNotReadyError

logger = logging.getLogger(__name__)


class AsyncBrightDataClient(BaseBrightDataClient):
    """Асинхронный клиент для работы с BrightData API."""
    
    def __init__(self):
        """Инициализация асинхронного клиента."""
        # Инициализируем базовые параметры без создания requests.Session
        self.api_token = settings.BRIGHTDATA_API_TOKEN
        self.base_url = settings.BRIGHTDATA_API_BASE_URL
        self.datasets = settings.BRIGHTDATA_DATASETS
        self.timeout = getattr(settings, "BRIGHTDATA_TIMEOUT", 600)
        self.rate_limits = getattr(settings, "BRIGHTDATA_RATE_LIMIT", {})
        
        
        # Создаем httpx клиент для async запросов
        self.async_session = httpx.AsyncClient(
            timeout=httpx.Timeout(self.timeout),
            headers={
                "Authorization": f"Bearer {self.api_token}",
                "Content-Type": "application/json",
            }
        )
        
        logger.info(f"Initialized AsyncBrightData client with token: {self.api_token[:10]}...")
    
    
    async def _make_request_async(
        self,
        method: str,
        endpoint: str,
        data: dict[Any, Any] | list[dict[str, Any]] | None = None,
        params: dict | None = None,
    ) -> Any:
        """
        Асинхронный метод для выполнения запросов к API.
        
        Args:
            method: HTTP метод
            endpoint: Эндпоинт API
            data: Данные для отправки
            params: Query параметры
            
        Returns:
            Ответ от API
            
        Raises:
            RateLimitError: При превышении лимита запросов
            BrightDataAPIError: При других ошибках API
        """
        url = f"{self.base_url}/{endpoint}"
        
        if params is None:
            params = {}
        params["format"] = "json"
        
        # Логирование запроса
        logger.info(f"Making async {method} request to {url}")
        if data:
            logger.debug(f"Request data: {json.dumps(data, indent=2)}")
        
        try:
            response = await self.async_session.request(
                method=method,
                url=url,
                json=data,
                params=params,
            )
            
            # Обработка ответа
            if response.status_code == 429:
                retry_after = int(response.headers.get("Retry-After", 60))
                raise RateLimitError(f"Rate limit exceeded. Retry after {retry_after} seconds", url=url)
            
            response.raise_for_status()
            
            # Парсим JSON ответ
            result = response.json()
            logger.debug(f"Response data: {json.dumps(result, indent=2)}")
            
            return result
            
        except httpx.HTTPStatusError as e:
            logger.error(f"HTTP error: {e}")
            error_detail = e.response.text if e.response else "No response body"
            raise BrightDataAPIError(
                f"HTTP {e.response.status_code}: {error_detail}",
                url=url
            )
        except httpx.RequestError as e:
            logger.error(f"Request error: {e}")
            raise BrightDataAPIError(f"Request failed: {e!s}", url=url)
        except json.JSONDecodeError as e:
            logger.error(f"JSON decode error: {e}")
            raise BrightDataAPIError(f"Invalid JSON response: {e!s}", url=url)
    
    async def trigger_dataset_async(
        self,
        dataset_type: str,
        params: dict[str, Any],
        webhook_url: str | None = None,
    ) -> dict[str, Any]:
        """
        Асинхронный метод для запуска сбора данных.
        
        Args:
            dataset_type: Тип датасета (например, 'instagram_profile')
            params: Параметры запроса
            webhook_url: URL для webhook уведомлений
            
        Returns:
            Ответ от API с snapshot_id
        """
        dataset_id = self.datasets.get(dataset_type)
        if not dataset_id:
            raise ValueError(f"Unknown dataset type: {dataset_type}")
        
        endpoint = f"trigger/{dataset_id}"
        
        # Подготовка данных запроса
        request_data = []
        if isinstance(params, list):
            request_data = params
        else:
            request_data = [params]
        
        # Добавляем webhook если указан
        if webhook_url:
            for item in request_data:
                item["webhook_url"] = webhook_url
        
        result = await self._make_request_async("POST", endpoint, data=request_data)
        return cast(dict[str, Any], result)
    
    async def get_snapshot_async(self, snapshot_id: str) -> dict[str, Any]:
        """
        Асинхронный метод для получения результатов снимка.
        
        Args:
            snapshot_id: ID снимка
            
        Returns:
            Данные снимка
            
        Raises:
            SnapshotNotReadyError: Если снимок еще не готов
        """
        endpoint = f"snapshot/{snapshot_id}"
        response = await self._make_request_async("GET", endpoint)
        
        # Проверяем статус снимка
        status = response.get("status")
        if status == "pending":
            progress = response.get("progress", 0)
            raise SnapshotNotReadyError(
                f"Snapshot {snapshot_id} is still processing ({progress}% complete)",
                snapshot_id=snapshot_id
            )
        elif status == "failed":
            error_message = response.get("error", "Unknown error")
            raise BrightDataAPIError(
                f"Snapshot {snapshot_id} failed: {error_message}",
                snapshot_id=snapshot_id,
            )
        
        return cast(dict[str, Any], response)
    
    async def close(self):
        """Закрытие асинхронного клиента."""
        await self.async_session.aclose()
    
    async def __aenter__(self):
        """Async context manager entry."""
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        await self.close()