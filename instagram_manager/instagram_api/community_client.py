import logging
from typing import Any, cast

import requests

logger = logging.getLogger(__name__)


class CommunityApiClient:
    """Клиент для работы с Community API сервиса Majila"""

    API_BASE_URL = "https://community-api.maji.la/api"
    USER_ENDPOINT = "/auth/user/"
    POSTS_ENDPOINT = "/posts/"

    def __init__(self, access_token: str | None = None):
        self.session = requests.Session()
        self.session.headers.update(
            {
                "Content-Type": "application/json",
                "Accept": "application/json",
            }
        )
        if access_token:
            self.set_auth_token(access_token)

    def set_auth_token(self, access_token: str):
        """Установка токена авторизации в заголовки"""
        self.session.headers["Authorization"] = f"Bearer {access_token}"
        logger.debug(
            f"Set Authorization header with token: Bearer {access_token[:10]}..."
        )

    def get_user(self) -> dict[str, Any]:
        """
        Получение информации о текущем пользователе

        Returns:
            Dict с информацией о пользователе (uuid, username, email и т.д.)

        Raises:
            requests.RequestException: При ошибке запроса
            ValueError: При некорректном ответе от API
        """
        url = f"{self.API_BASE_URL}{self.USER_ENDPOINT}"

        try:
            logger.info(f"Fetching user information from Community API: {url}")
            logger.debug(f"Headers: {dict(self.session.headers)}")
            response = self.session.get(url)
            response.raise_for_status()

            user_data: dict[str, Any] = response.json()
            logger.info(
                f"Successfully fetched user data: {user_data.get('username', 'Unknown')}"
            )
            return user_data

        except requests.RequestException as e:
            logger.exception(f"Failed to fetch user data: {e!s}")
            if hasattr(e, "response") and e.response is not None:
                logger.exception(f"Response status: {e.response.status_code}")
                logger.exception(f"Response headers: {dict(e.response.headers)}")
                try:
                    logger.exception(f"Response content: {e.response.text}")
                except (AttributeError, UnicodeDecodeError):
                    pass
            raise

    def create_post(self, post_data: dict[str, Any]) -> dict[str, Any]:
        """
        Создание поста в сервисе Community

        Args:
            post_data: Данные поста включающие:
                - post_uuid: UUID поста
                - content: Контент поста (текст, изображения, социальные медиа данные)
                - post_type: Тип поста
                - original_platform: Оригинальная платформа

        Returns:
            Dict с результатом создания поста

        Raises:
            requests.RequestException: При ошибке запроса
            ValueError: При некорректном ответе от API
        """
        url = f"{self.API_BASE_URL}{self.POSTS_ENDPOINT}"

        try:
            logger.info(f"Creating post with UUID: {post_data.get('post_uuid')}")
            response = self.session.post(url, json=post_data)
            response.raise_for_status()

            result: dict[str, Any] = response.json()
            logger.info(f"Successfully created post: {result.get('post_uuid')}")
            return result

        except requests.RequestException as e:
            logger.exception(f"Failed to create post: {e!s}")
            if hasattr(e, "response") and e.response is not None:
                logger.exception(f"Response content: {e.response.text}")
            raise

    def get_posts(self, limit: int = 20, offset: int = 0) -> dict[str, Any]:
        """
        Получение списка постов пользователя

        Args:
            limit: Количество постов для получения
            offset: Смещение для пагинации

        Returns:
            Dict со списком постов и метаинформацией
        """
        url = f"{self.API_BASE_URL}{self.POSTS_ENDPOINT}"
        params = {"limit": limit, "offset": offset}

        try:
            logger.info(f"Fetching posts with limit={limit}, offset={offset}")
            response = self.session.get(url, params=params)
            response.raise_for_status()

            return cast(dict[str, Any], response.json())

        except requests.RequestException as e:
            logger.exception(f"Failed to fetch posts: {e!s}")
            raise

    def check_post_exists(self, external_id: str) -> bool:
        """
        Проверка существования поста по external_id

        Args:
            external_id: Внешний ID поста (Instagram post ID)

        Returns:
            True если пост существует, False в противном случае
        """
        # TODO: Реализовать когда будет доступен соответствующий endpoint
        # Пока возвращаем False чтобы всегда пытаться создать пост
        return False
