"""Instagram API module"""

# Импортируем из новой модульной структуры brightdata
from .brightdata import (
    # Исключения
    BrightDataAPIError,
    BrightDataClient,
    CommentClient,
    HashtagClient,
    InstagramRateLimitError,
    PostClient,
    PrivateProfileError,
    # Специализированные клиенты
    ProfileClient,
    ProfileNotFoundError,
    RateLimitError,
)

# Импортируем data handlers
from .data_handlers.post_handler import PostHandler
from .data_handlers.profile_handler import ProfileHandler

__all__ = [
    # Основной клиент (для обратной совместимости)
    "BrightDataClient",
    # Специализированные клиенты
    "ProfileClient",
    "PostClient",
    "CommentClient", 
    "HashtagClient",
    # Исключения
    "BrightDataAPIError",
    "RateLimitError",
    "ProfileNotFoundError",
    "PrivateProfileError",
    "InstagramRateLimitError",
    # Data handlers
    "PostHandler",
    "ProfileHandler",
]