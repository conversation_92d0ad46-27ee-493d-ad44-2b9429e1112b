"""
Media handler for downloading and processing Instagram media files.
Supports optional Google Cloud Storage integration.
"""
import logging
import os

import requests
from django.conf import settings
from django.core.files.base import ContentFile

from instagram_manager.models import InstagramMedia

logger = logging.getLogger(__name__)

# Import GCSService only if available
try:
    from core.storage.gcs_service import GCSService
    logger.info("[GCS IMPORT] GCSService imported successfully")
except ImportError as e:
    import sys
    logger.error(
        f"[GCS IMPORT] Failed to import GCSService: {e}\n"
        f"Python Path: {sys.path}\n"
        f"Working Directory: {os.getcwd()}",
        exc_info=True
    )
    GCSService = None
except Exception as e:
    logger.error(
        f"[GCS IMPORT] Unexpected error importing GCSService: {type(e).__name__}: {e}",
        exc_info=True
    )
    GCSService = None


class MediaHandler:
    """Handler for downloading and processing Instagram media."""
    
    def __init__(self, save_to_gcs: bool = False, skip_local_save: bool = False):
        """
        Initialize media handler.
        
        Args:
            save_to_gcs: If True, save media to Google Cloud Storage
            skip_local_save: If True, skip local save and only save to GCS
        """
        self.save_to_gcs = save_to_gcs
        self.skip_local_save = skip_local_save
        self._gcs_service: GCSService | None = None
        
        # Initialize GCS service if needed
        logger.debug(
            f"GCS init check: save_to_gcs={self.save_to_gcs}, "
            f"GCS_BUCKET_NAME={getattr(settings, 'GCS_BUCKET_NAME', 'NOT SET')}, "
            f"GCSService available={GCSService is not None}"
        )
        
        if self.save_to_gcs:
            logger.info("[GCS INIT] Attempting to initialize GCS service")
            if not GCSService:
                logger.error("[GCS INIT] GCS service requested but GCSService module not available (import failed)")
            elif not getattr(settings, "GCS_BUCKET_NAME", None):
                logger.error("[GCS INIT] GCS service requested but GCS_BUCKET_NAME not configured")
            else:
                try:
                    logger.info("[GCS INIT] Creating GCSService instance...")
                    self._gcs_service = GCSService()
                    logger.info("[GCS INIT] GCS service initialized successfully for media uploads")
                except Exception as e:
                    logger.error(
                        f"[GCS INIT] Failed to initialize GCS service, "
                        f"will fallback to local storage: {e}",
                        exc_info=True
                    )
                    self._gcs_service = None
        else:
            logger.info("[GCS INIT] save_to_gcs=False, skipping GCS initialization")
    
    def download_media(self, media: InstagramMedia) -> bool:
        """
        Download media file and save to GCS or locally.
        
        Args:
            media: InstagramMedia instance to download
            
        Returns:
            True if successful, False otherwise
        """
        if not media.media_url:
            logger.warning(f"No media URL for {media.external_id}")
            return False
        
        # Check if already downloaded
        if media.is_downloaded and media.local_path:
            # If we need GCS upload but don't have GCS URL, try to upload existing file
            if self.save_to_gcs and not media.gcs_url:
                logger.info(f"Media {media.external_id} already downloaded locally, uploading to GCS")
                return self._upload_existing_to_gcs(media)
            else:
                logger.info(f"Media {media.external_id} already downloaded")
                return True
        
        logger.info(
            f"[MEDIA DEBUG] Starting download for {media.external_id}: "
            f"save_to_gcs={self.save_to_gcs}, gcs_service={self._gcs_service is not None}"
        )
        
        try:
            # Download file content
            logger.info(f"Downloading media from: {media.media_url}")
            response = requests.get(
                media.media_url,
                timeout=30,
                headers={
                    "User-Agent": "Mozilla/5.0 (compatible; SocialManager/1.0)"
                },
                stream=True
            )
            response.raise_for_status()
            
            # Get file info
            content = response.content
            content_type = response.headers.get("content-type", "image/jpeg")
            
            # Generate filename
            filename = self._generate_filename(media, content_type)
            
            # Try GCS upload first if enabled
            logger.info(
                f"[MEDIA DEBUG] GCS check for {media.external_id}: "
                f"save_to_gcs={self.save_to_gcs}, "
                f"_gcs_service is None={self._gcs_service is None}, "
                f"condition result={self.save_to_gcs and self._gcs_service}"
            )
            
            if self.save_to_gcs and self._gcs_service:
                logger.info(f"[MEDIA DEBUG] Attempting GCS upload for media {media.external_id}")
                gcs_url = self._try_gcs_upload(
                    content, 
                    filename, 
                    content_type, 
                    media
                )
                
                if gcs_url:
                    # Success - save GCS URL and mark as downloaded
                    media.gcs_url = gcs_url
                    media.is_downloaded = True
                    media.file_size = len(content)
                    media.save()
                    
                    logger.info(
                        f"Successfully uploaded to GCS: {media.external_id} "
                        f"-> {gcs_url}"
                    )
                    return True
                else:
                    # GCS failed, continue to local storage
                    logger.warning(
                        f"GCS upload failed for {media.external_id}, "
                        f"falling back to local storage"
                    )
            else:
                logger.warning(
                    f"[MEDIA DEBUG] Not attempting GCS upload for {media.external_id}: "
                    f"save_to_gcs={self.save_to_gcs}, gcs_service={self._gcs_service is not None}"
                )
            
            # Save locally only if not skipping local save or if GCS upload failed
            if not self.skip_local_save or not gcs_url:
                logger.debug(f"Saving media {media.external_id} locally")
                return self._save_locally(content, filename, media)
            else:
                # Mark as downloaded even without local save (GCS only)
                media.is_downloaded = True
                media.file_size = len(content)
                media.save()
                return True
            
        except requests.RequestException as e:
            logger.error(f"Failed to download media {media.external_id}: {e}")
            media.download_error = str(e)
            media.save()
            return False
        except Exception as e:
            logger.error(
                f"Unexpected error downloading media {media.external_id}: {e}"
            )
            media.download_error = str(e)
            media.save()
            return False
    
    def download_thumbnail(self, media: InstagramMedia) -> bool:
        """
        Download thumbnail for video media.
        
        Args:
            media: InstagramMedia instance
            
        Returns:
            True if successful, False otherwise
        """
        if not media.thumbnail_url or media.media_type != "video":
            return False
            
        # Check if already downloaded
        if media.local_thumbnail_path:
            return True
            
        try:
            response = requests.get(
                media.thumbnail_url, 
                timeout=30, 
                headers={
                    "User-Agent": "Mozilla/5.0 (compatible; SocialManager/1.0)"
                },
                stream=True
            )
            response.raise_for_status()
            
            # Get content
            content = response.content
            content_type = response.headers.get("content-type", "image/jpeg")
            
            # Generate filename
            filename = f"{media.external_id}_thumb.jpg"
            
            # Try GCS upload first if enabled
            if self.save_to_gcs and self._gcs_service:
                gcs_url = self._try_gcs_upload(
                    content,
                    filename,
                    content_type,
                    media,
                    is_thumbnail=True
                )
                
                if gcs_url:
                    logger.info(
                        f"Successfully uploaded thumbnail to GCS: {media.external_id} "
                        f"-> {gcs_url}"
                    )
                    # Save GCS URL and skip local save
                    media.gcs_thumbnail_url = gcs_url
                    media.save()
                    return True
                else:
                    # GCS failed, continue to local storage
                    logger.warning(
                        f"GCS thumbnail upload failed for {media.external_id}, "
                        f"falling back to local storage"
                    )
            
            # Save thumbnail locally only if GCS is not enabled or failed
            content_file = ContentFile(content)
            media.local_thumbnail_path.save(filename, content_file, save=False)
            media.save()
            
            logger.info(f"Downloaded thumbnail locally for: {media.external_id}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to download thumbnail for {media.external_id}: {e}")
            return False
    
    def _try_gcs_upload(
        self, 
        content: bytes, 
        filename: str, 
        content_type: str,
        media: InstagramMedia,
        is_thumbnail: bool = False
    ) -> str | None:
        """
        Try to upload file to GCS.
        
        Returns:
            GCS URL if successful, None if failed
        """
        try:
            # Determine folder based on media type
            if not GCSService:
                logger.error("GCSService not available, cannot determine folder")
                return None
                
            folder = GCSService.get_folder_by_media_type(
                media.media_type,
                is_thumbnail
            )
            
            # Use external_id as part of the filename for uniqueness
            gcs_filename = f"{media.external_id}_{filename}"
            
            # Upload to GCS with correct folder
            gcs_url = self._gcs_service.upload_file(
                file_content=content,
                original_filename=gcs_filename,
                content_type=content_type,
                folder=folder
            )
            
            return gcs_url
            
        except Exception as e:
            logger.error(
                f"GCS upload failed for media {media.external_id}: {e}",
                exc_info=True
            )
            return None
    
    def _save_locally(
        self, 
        content: bytes, 
        filename: str, 
        media: InstagramMedia
    ) -> bool:
        """Save file locally."""
        try:
            file_content = ContentFile(content)
            media.local_path.save(filename, file_content, save=False)
            media.is_downloaded = True
            media.file_size = len(content)
            media.save()
            
            # Verify file was actually saved
            if media.local_path and media.local_path.name:
                import os
                full_path = media.local_path.path
                if os.path.exists(full_path):
                    actual_size = os.path.getsize(full_path)
                    logger.info(
                        f"Successfully saved locally: {media.external_id} "
                        f"-> {media.local_path.name} (size: {actual_size} bytes)"
                    )
                    return True
                else:
                    logger.error(
                        f"File was saved but doesn't exist on disk: {media.external_id} "
                        f"-> {full_path}"
                    )
                    media.download_error = "File saved but not found on disk"
                    media.is_downloaded = False
                    media.save()
                    return False
            else:
                logger.error(
                    f"No local_path after save for media {media.external_id}"
                )
                return False
            
        except Exception as e:
            logger.error(
                f"Failed to save media locally {media.external_id}: {e}",
                exc_info=True
            )
            media.download_error = str(e)
            media.save()
            return False
    
    def _upload_existing_to_gcs(self, media: InstagramMedia) -> bool:
        """
        Upload existing local file to GCS.
        
        Args:
            media: InstagramMedia instance with local file
            
        Returns:
            True if successful, False otherwise
        """
        if not media.local_path or not self._gcs_service:
            return False
            
        try:
            # Read local file content
            with media.local_path.open('rb') as f:
                content = f.read()
                
            # Determine content type
            file_name = media.local_path.name
            if file_name.endswith(('.jpg', '.jpeg')):
                content_type = "image/jpeg"
            elif file_name.endswith('.png'):
                content_type = "image/png"
            elif file_name.endswith('.mp4'):
                content_type = "video/mp4"
            else:
                content_type = "application/octet-stream"
                
            # Generate filename
            filename = self._generate_filename(media, content_type)
            
            # Upload to GCS
            gcs_url = self._try_gcs_upload(
                content,
                filename,
                content_type,
                media
            )
            
            if gcs_url:
                media.gcs_url = gcs_url
                media.save()
                logger.info(
                    f"Successfully uploaded existing file to GCS: {media.external_id} "
                    f"-> {gcs_url}"
                )
                return True
            else:
                logger.error(f"Failed to upload existing file to GCS: {media.external_id}")
                return False
                
        except Exception as e:
            logger.error(
                f"Error uploading existing file to GCS for {media.external_id}: {e}",
                exc_info=True
            )
            return False
    
    def _generate_filename(
        self, 
        media: InstagramMedia, 
        content_type: str
    ) -> str:
        """Generate filename based on media type and external_id."""
        # Map content type to extension
        extension_map = {
            "image/jpeg": ".jpg",
            "image/png": ".png",
            "image/gif": ".gif",
            "image/webp": ".webp",
            "video/mp4": ".mp4",
            "video/quicktime": ".mov",
        }
        
        # Get extension from content type or default to jpg
        ext = extension_map.get(content_type.lower(), ".jpg")
        
        # Special handling for video content type
        if "video" in content_type.lower() and ext == ".jpg":
            ext = ".mp4"
        
        # Use external_id for unique filename
        return f"{media.external_id}{ext}"
    