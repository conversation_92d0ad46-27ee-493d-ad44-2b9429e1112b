import logging
from datetime import UTC, datetime

from django.utils import timezone

from instagram_manager.models import InstagramComment, InstagramPost
from instagram_manager.schemas.brightdata import InstagramCommentResponse

logger = logging.getLogger(__name__)


class CommentHandler:
    """Обработчик данных комментариев из BrightData"""
    
    def process_comment_data(self, comment_response: InstagramCommentResponse, 
                           post: InstagramPost,
                           parent_comment: InstagramComment | None = None) -> InstagramComment:
        """Обработка и сохранение данных комментария"""
        
        # Get comment_id from the InstagramCommentResponse object
        comment_id = getattr(comment_response, "comment_id", None)
        logger.debug(f"Processing comment data: type={type(comment_response)}, comment_id={comment_id}")
        
        # Подготавливаем данные комментария
        comment_data = {
            "external_id": comment_id,  # Use external_id field
            "comment_id": comment_id,
            "post": post,
            "author_username": comment_response.author_username,
            "author_external_id": comment_response.user_id or f"user_{comment_response.author_username}",
            "author_profile_pic": comment_response.user_profile_pic_url,
            "author_is_verified": comment_response.is_verified_author,
            "text": comment_response.text,
            "like_count": comment_response.like_count,
            "commented_at": comment_response.date_created,
            "is_pinned": comment_response.is_pinned,
            "is_hidden": comment_response.is_hidden,
        }
        
        logger.debug(f"Prepared comment data: {comment_data}")
        
        # Обрабатываем иерархию комментариев
        if parent_comment:
            comment_data["reply_to"] = parent_comment
        elif comment_response.parent_comment_id:
            # Пытаемся найти родительский комментарий
            try:
                reply_to = InstagramComment.objects.get(external_id=comment_response.parent_comment_id)
                comment_data["reply_to"] = reply_to
            except InstagramComment.DoesNotExist:
                logger.warning(f"Reply to comment {comment_response.parent_comment_id} not found")
        
        # Создаем или обновляем комментарий
        logger.info(f"Creating/updating comment with external_id={comment_data['external_id']}")
        try:
            comment, created = InstagramComment.objects.update_or_create(
                external_id=comment_data["external_id"],
                defaults=comment_data
            )
            
            if created:
                logger.info(f"Created new comment: {comment.comment_id}")
            else:
                logger.info(f"Updated comment: {comment.comment_id}")
        except Exception as e:
            logger.exception(f"Error creating/updating comment: {e}")
            raise
            
        return comment
        
    @staticmethod
    def _parse_timestamp(timestamp) -> datetime:
        """Парсинг временной метки"""
        if isinstance(timestamp, int):
            return datetime.fromtimestamp(timestamp, tz=UTC)
        elif isinstance(timestamp, str):
            try:
                return datetime.fromisoformat(timestamp.replace("Z", "+00:00"))
            except Exception:
                return timezone.now()
        return timezone.now()
        
    def process_comment_tree(self, comments_data: list[InstagramCommentResponse],
                           post: InstagramPost) -> list[InstagramComment]:
        """
        Обработать дерево комментариев с учетом иерархии.
        
        Args:
            comments_data: Список данных комментариев от API
            post: Пост
            
        Returns:
            Список обработанных комментариев
        """
        processed_comments = []
        comment_map = {}
        
        # Сначала обрабатываем комментарии верхнего уровня
        for comment_response in comments_data:
            if not comment_response.parent_comment_id:
                comment = self.process_comment_data(comment_response, post)
                processed_comments.append(comment)
                comment_map[comment.external_id] = comment
        
        # Затем обрабатываем ответы
        for comment_response in comments_data:
            if comment_response.parent_comment_id:
                parent = comment_map.get(comment_response.parent_comment_id)
                comment = self.process_comment_data(comment_response, post, parent)
                processed_comments.append(comment)
                comment_map[comment.external_id] = comment
                
        return processed_comments
    
    def batch_process_comments(self, comments_data: list[dict],
                             post: InstagramPost) -> tuple[int, int]:
        """
        Массовая обработка комментариев.
        
        Args:
            comments_data: Список сырых данных комментариев
            post: Пост
            
        Returns:
            Кортеж (количество созданных, количество обновленных)
        """
        created_count = 0
        updated_count = 0
        
        for comment_dict in comments_data:
            try:
                # Преобразуем в Pydantic модель
                comment_response = InstagramCommentResponse(**comment_dict)
                
                # Проверяем существование
                existing = InstagramComment.objects.filter(
                    external_id=comment_response.comment_id
                ).first()
                
                # Обрабатываем комментарий
                self.process_comment_data(comment_response, post)
                
                if existing:
                    updated_count += 1
                else:
                    created_count += 1
                    
            except Exception as e:
                logger.exception(f"Error processing comment: {e}")
                continue
                
        return created_count, updated_count