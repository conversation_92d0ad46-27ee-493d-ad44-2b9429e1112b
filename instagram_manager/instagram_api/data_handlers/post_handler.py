"""Enhanced post handler with Pydantic validation."""

import logging

from instagram_manager.instagram_api.data_handlers.media_handler import MediaHandler
from instagram_manager.models import (
    InstagramHashtag,
    InstagramMedia,
    InstagramPost,
    InstagramProfile,
)
from instagram_manager.schemas.brightdata import InstagramPostResponse, MediaItem

logger = logging.getLogger(__name__)


class PostHandler:
    """Post data handler with Pydantic validation."""
    
    def __init__(self, save_to_gcs: bool = False, skip_local_save: bool = False):
        """Initialize PostHandler with configuration.
        
        Args:
            save_to_gcs: Whether to save media files to Google Cloud Storage
            skip_local_save: Whether to skip local save when saving to GCS
        """
        self.save_to_gcs = save_to_gcs
        self.skip_local_save = skip_local_save
        logger.info(f"[POST HANDLER INIT] Creating PostHandler with save_to_gcs={save_to_gcs}, skip_local_save={skip_local_save}")
        self.media_handler = MediaHandler(save_to_gcs=save_to_gcs, skip_local_save=skip_local_save)
        logger.info(
            f"[POST HANDLER INIT] PostHandler initialized with save_to_gcs={save_to_gcs}, "
            f"skip_local_save={skip_local_save}, "
            f"media_handler._gcs_service={self.media_handler._gcs_service is not None}"
        )
    
    def process_post_data(
        self,
        post_response: InstagramPostResponse, 
        profile: InstagramProfile,
        skip_media_download: bool = False
    ) -> InstagramPost:
        """Process validated post data and save to database.
        
        Args:
            post_response: Validated Pydantic model from BrightData
            profile: InstagramProfile instance
            
        Returns:
            InstagramPost model instance
        """
        # Log the validated data
        logger.info(f"Processing validated post: {post_response.shortcode}")
        
        # Determine post type based on validated data
        post_type = self._determine_post_type(post_response)
        
        # Convert Pydantic model to dict for ORM
        post_data = {
            "external_id": post_response.post_id,
            "profile": profile,
            "shortcode": post_response.shortcode,
            "post_type": post_type,
            "caption": post_response.description,
            "like_count": post_response.likes,
            "comment_count": post_response.num_comments,
            "view_count": post_response.view_count or 0,
            "video_play_count": post_response.view_count or 0,  # Use same as views
            "posted_at": post_response.date_posted,
            "location": post_response.location,
            "location_id": None,  # BrightData doesn't provide location_id
            "is_sponsored": False,  # BrightData doesn't provide this
            "is_comments_disabled": False,  # BrightData doesn't provide this
            "post_url": post_response.url,
            "accessibility_caption": "",  # Not provided by BrightData
        }
        
        # Create or update post
        post, created = InstagramPost.objects.update_or_create(
            external_id=post_data["external_id"],
            defaults=post_data
        )
        
        # Process media files with validated data
        self._process_media(post_response, post)
        
        # Process hashtags from validated data
        self._process_hashtags(post_response, post)
        
        # Auto-download media if enabled and not skipped
        if not skip_media_download:
            self._auto_download_media(post)
        
        if created:
            logger.info(f"Created new post: {post.shortcode}")
        else:
            logger.info(f"Updated post: {post.shortcode}")
            
        return post
    
    def _determine_post_type(self, post_response: InstagramPostResponse) -> str:
        """Determine post type from validated data."""
        content_type = post_response.content_type.lower()
        
        if "video" in content_type or post_response.is_video:
            return "video"
        elif "reel" in content_type:
            return "reel"
        elif len(post_response.post_content) > 1:
            return "carousel"
        else:
            return "photo"
    
    def _process_media(self, post_response: InstagramPostResponse, post: InstagramPost):
        """Process media files from validated data."""
        # Get existing media to check what needs to be updated
        existing_media = {m.media_id: m for m in post.media.all()}
        processed_media_ids = set()
        
        # Process each media item
        for index, media_item in enumerate(post_response.post_content):
            media_id = media_item.media_id or f"{post.external_id}_{index}"
            processed_media_ids.add(media_id)
            
            # For video items without thumbnail, use top-level thumbnail
            thumbnail_url = media_item.thumbnail_url or ""
            if not thumbnail_url and media_item.media_type.lower() == "video" and post_response.thumbnail:
                thumbnail_url = post_response.thumbnail
                logger.debug(f"Using top-level thumbnail for video {media_id}")
            
            # Update existing or create new
            if media_id in existing_media:
                # Update existing media (preserve local_path if downloaded)
                media = existing_media[media_id]
                media.media_url = media_item.url or media_item.media_url
                media.thumbnail_url = thumbnail_url
                media.duration = media_item.duration or 0
                media.width = media_item.width or 0
                media.height = media_item.height or 0
                media.order_index = index
                media.save()
                logger.debug(f"Updated existing media {media_id}")
            else:
                # Create new media with thumbnail
                self._create_media_from_item(media_item, post, index, thumbnail_url, post_response.thumbnail)
                logger.debug(f"Created new media {media_id}")
        
        # Delete media that no longer exists in the post
        # This will trigger pre_delete signal to clean up files
        for media_id, media in existing_media.items():
            if media_id not in processed_media_ids:
                logger.info(f"Deleting removed media {media_id} from post {post.external_id}")
                media.delete()
        
        # If no media items but we have video duration, create a single video media
        if not post_response.post_content and post_response.video_duration:
            InstagramMedia.objects.create(
                external_id=f"{post.external_id}_video_0",
                post=post,
                media_type="video",
                media_url=post_response.url,  # Use post URL as fallback
                thumbnail_url=post_response.thumbnail or "",  # Use top-level thumbnail
                audio_url=None,
                duration=post_response.video_duration or 0,
                width=0,
                height=0,
                order_index=0,
            )
    
    def _create_media_from_item(
        self,
        media_item: MediaItem, 
        post: InstagramPost, 
        order_index: int,
        thumbnail_override: str | None = None,
        post_thumbnail: str | None = None
    ):
        """Create media record from validated MediaItem."""
        media_type = media_item.media_type.lower()
        if media_type not in ["photo", "video"]:
            media_type = "photo"  # Default to photo
        
        # Use thumbnail_override if provided, otherwise item thumbnail, otherwise post thumbnail for videos
        thumbnail_url = thumbnail_override or media_item.thumbnail_url or ""
        if not thumbnail_url and media_type == "video" and post_thumbnail:
            thumbnail_url = post_thumbnail
        
        # Get media URL
        media_url = media_item.url or media_item.media_url
        
        # Check URL lengths and log if they exceed old limit
        if len(media_url) > 1000:
            logger.warning(
                f"Media URL for {post.external_id} exceeds old 1000 char limit: {len(media_url)} chars"
            )
        if thumbnail_url and len(thumbnail_url) > 1000:
            logger.warning(
                f"Thumbnail URL for {post.external_id} exceeds old 1000 char limit: {len(thumbnail_url)} chars"
            )
        
        # Truncate URLs if they still exceed new limit (safety measure)
        max_url_length = 2048
        if len(media_url) > max_url_length:
            logger.error(
                f"Media URL for {post.external_id} exceeds {max_url_length} chars ({len(media_url)}), truncating..."
            )
            media_url = media_url[:max_url_length]
        if thumbnail_url and len(thumbnail_url) > max_url_length:
            logger.error(
                f"Thumbnail URL for {post.external_id} exceeds {max_url_length} chars ({len(thumbnail_url)}), truncating..."
            )
            thumbnail_url = thumbnail_url[:max_url_length]
        
        InstagramMedia.objects.create(
            external_id=media_item.media_id or f"{post.external_id}_{order_index}",
            post=post,
            media_type=media_type,
            media_url=media_url,
            thumbnail_url=thumbnail_url,
            audio_url=None,  # Not provided by BrightData
            duration=media_item.duration or 0,
            width=media_item.width or 0,
            height=media_item.height or 0,
            order_index=order_index,
        )
    
    def _process_hashtags(self, post_response: InstagramPostResponse, post: InstagramPost):
        """Process hashtags from validated data."""
        # Clear old hashtags
        post.hashtags.clear()
        
        # Get unique hashtags from validated data
        unique_hashtags = set()
        unique_hashtags.update(post_response.hashtags)
        
        # Create/update hashtags
        for tag_name in unique_hashtags:
            if tag_name:  # Check tag is not empty
                tag_name = tag_name.strip("#").lower()
                hashtag, created = InstagramHashtag.objects.get_or_create(
                    name=tag_name
                )
                post.hashtags.add(hashtag)
                
                if created:
                    logger.debug(f"Created new hashtag: #{tag_name}")
    
    def _auto_download_media(self, post: InstagramPost):
        """Auto-download media files after creating post."""
        from django.conf import settings
        
        if not getattr(settings, "INSTAGRAM_AUTO_DOWNLOAD_MEDIA", True):
            return
        
        # Use the instance's MediaHandler with configured GCS setting
        handler = self.media_handler
        
        for media in post.media.filter(is_downloaded=False):
            try:
                # Download main file
                if handler.download_media(media):
                    logger.info(
                        f"Auto-downloaded media {media.media_id} for post {post.shortcode}"
                    )
                    
                    # For videos also download thumbnail
                    if media.media_type == "video" and media.thumbnail_url:
                        handler.download_thumbnail(media)
                else:
                    logger.warning(f"Failed to auto-download media {media.media_id}")
                    
            except Exception as e:
                logger.exception(f"Error auto-downloading media {media.media_id}: {e!s}")
    
    def batch_process_posts(
        self,
        posts_response: list[InstagramPostResponse],
        profile: InstagramProfile,
        skip_media_download: bool = False
    ) -> list[InstagramPost]:
        """Process multiple posts efficiently.
        
        Args:
            posts_response: List of validated post responses
            profile: Profile that owns these posts
            
        Returns:
            List of created/updated InstagramPost instances
        """
        processed_posts = []
        
        for post_response in posts_response:
            try:
                post = self.process_post_data(post_response, profile, skip_media_download)
                processed_posts.append(post)
            except Exception as e:
                logger.exception(
                    f"Error processing post {post_response.shortcode}: {e!s}"
                )
        
        logger.info(
            f"Processed {len(processed_posts)} out of {len(posts_response)} posts"
        )
        
        return processed_posts