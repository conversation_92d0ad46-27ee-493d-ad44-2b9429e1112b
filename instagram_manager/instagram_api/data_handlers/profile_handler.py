"""Enhanced profile handler with Pydantic validation."""

import logging

from django.utils import timezone

from instagram_manager.models import InstagramProfile
from instagram_manager.schemas.brightdata import InstagramProfileResponse

logger = logging.getLogger(__name__)


class ProfileHandler:
    """Profile data handler with Pydantic validation."""
    
    @staticmethod
    def process_profile_data(profile_response: InstagramProfileResponse) -> InstagramProfile:
        """Process validated profile data and save to database.
        
        Args:
            profile_response: Validated Pydantic model from BrightData
            
        Returns:
            InstagramProfile model instance
        """
        # Log the validated data
        logger.info(f"Processing validated profile: {profile_response.username}")
        
        # Convert Pydantic model to dict for ORM
        profile_data = {
            "profile_id": profile_response.profile_id,
            "username": profile_response.username,
            "full_name": profile_response.full_name,
            "bio": profile_response.biography,
            "follower_count": profile_response.follower_count,  # Changed to match model field name
            "following_count": profile_response.following_count,
            "post_count": profile_response.post_count,  # Changed to match model field name
            "is_verified": profile_response.is_verified,
            "is_business": profile_response.is_business,
            "is_private": profile_response.is_private,
            "profile_pic_url": profile_response.profile_pic_url or "",
            "profile_pic_hd_url": profile_response.profile_pic_url or "",  # Use same URL
            "external_url": profile_response.external_url or "",
            "category_name": profile_response.category or "",
            "last_scraped_at": timezone.now()
        }
        
        # Create or update profile
        profile, created = InstagramProfile.objects.update_or_create(
            profile_id=profile_data["profile_id"],
            defaults=profile_data
        )
        
        if created:
            logger.info(f"Created new profile: {profile.username}")
        else:
            logger.info(f"Updated profile: {profile.username}")
            
        return profile
    
    @staticmethod
    def update_profile_stats(
        profile: InstagramProfile, 
        profile_response: InstagramProfileResponse
    ) -> InstagramProfile:
        """Update only the statistics of an existing profile.
        
        Args:
            profile: Existing InstagramProfile instance
            profile_response: New data from API
            
        Returns:
            Updated InstagramProfile instance
        """
        # Update only stats fields
        profile.follower_count = profile_response.follower_count
        profile.following_count = profile_response.following_count
        profile.post_count = profile_response.post_count
        profile.is_verified = profile_response.is_verified
        profile.is_private = profile_response.is_private
        profile.last_scraped_at = timezone.now()
        
        profile.save(update_fields=[
            "follower_count", "following_count", "post_count",
            "is_verified", "is_private", "last_scraped_at"
        ])
        
        logger.info(f"Updated stats for profile: {profile.username}")
        return profile
    
    @staticmethod
    def create_profile_from_partial_data(
        username: str,
        profile_id: str | None = None,
        **kwargs
    ) -> InstagramProfile:
        """Create a minimal profile from partial data.
        
        This is useful when we have references to profiles in posts
        but haven't fetched full profile data yet.
        
        Args:
            username: Instagram username
            profile_id: Optional profile ID
            **kwargs: Additional fields
            
        Returns:
            InstagramProfile instance
        """
        if not profile_id:
            profile_id = username  # Use username as temporary ID
        
        profile_data = {
            "profile_id": str(profile_id),
            "username": username,
            "full_name": kwargs.get("full_name", ""),
            "bio": kwargs.get("bio", ""),
            "follower_count": kwargs.get("follower_count", 0),  # Changed to match model field name
            "following_count": kwargs.get("following_count", 0),
            "post_count": kwargs.get("post_count", 0),  # Changed to match model field name
            "is_verified": kwargs.get("is_verified", False),
            "is_business": kwargs.get("is_business", False),
            "is_private": kwargs.get("is_private", False),
            "profile_pic_url": kwargs.get("profile_pic_url", ""),
            "profile_pic_hd_url": kwargs.get("profile_pic_hd_url", ""),
            "external_url": kwargs.get("external_url", ""),
            "category_name": kwargs.get("category_name", ""),
            "last_scraped_at": timezone.now()
        }
        
        profile, created = InstagramProfile.objects.update_or_create(
            profile_id=profile_data["profile_id"],
            defaults=profile_data
        )
        
        if created:
            logger.info(f"Created partial profile: {profile.username}")
        
        return profile