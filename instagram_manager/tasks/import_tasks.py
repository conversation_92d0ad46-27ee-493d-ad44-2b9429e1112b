"""
Import tasks for Instagram data.

These tasks handle importing various Instagram data types (profiles, posts, comments, etc.)
from external APIs and can be executed either synchronously or asynchronously.
"""

import logging
from typing import Any, ClassVar

from django.db import transaction
from django.utils import timezone

from core.tasks.base import BaseTask
from core.tasks.decorators import task
from core.tasks.exceptions import TaskExecutionError, TaskValidationError
from instagram_manager.instagram_api import BrightDataClient
from instagram_manager.instagram_api.data_handlers.post_handler import <PERSON><PERSON>andler
from instagram_manager.instagram_api.data_handlers.profile_handler import ProfileHandler
from instagram_manager.models import InstagramPost, InstagramProfile, InstagramScrapingTask, InstagramSubTask
from instagram_manager.services import ImportService

# Use standard Python logging instead of ContextLogger for Celery compatibility
logger = logging.getLogger(__name__)


@task(monitor=True)
class ImportBatchPostsTask(BaseTask):
    """
    Task for batch importing Instagram posts for multiple profiles.

    This task efficiently fetches posts for multiple profiles in a single
    BrightData snapshot and distributes them accordingly.
    """

    task_type: ClassVar[str] = "instagram.import_batch_posts"
    description: ClassVar[str] = "Batch import Instagram posts"
    max_retries: ClassVar[int] = 3

    def __init__(self, task_id: str | None = None):
        super().__init__(task_id)
        self.client = BrightDataClient()

    def validate_params(self, usernames: list[str], **kwargs) -> dict[str, Any]:
        """Validate task parameters."""
        if not usernames:
            raise TaskValidationError(
                self.task_type, {"usernames": "At least one username is required"}
            )

        # Normalize usernames
        usernames = [u.lower().strip().lstrip("@") for u in usernames]

        params = {
            "usernames": usernames,
            "batch_size": kwargs.get("batch_size", 10),
            "limit": kwargs.get("limit"),
            "post_types": kwargs.get("post_types", []),
            "import_comments": kwargs.get("import_comments", False),
            "skip_media_download": kwargs.get("skip_media_download", False),
            "save_media_to_gcs": kwargs.get("save_media_to_gcs", False),
            "import_all_users": kwargs.get("import_all_users", False),
            "scraping_task_id": kwargs.get("scraping_task_id"),
        }
        
        # Only add date parameters if they are provided and not empty
        if kwargs.get("start_date"):
            params["start_date"] = kwargs["start_date"]
        if kwargs.get("end_date"):
            params["end_date"] = kwargs["end_date"]
            
        return params

    def execute_task(
        self,
        usernames: list[str],
        batch_size: int = 10,
        limit: int | None = None,
        start_date: str | None = None,
        end_date: str | None = None,
        post_types: list[str] | None = None,
        import_comments: bool = False,
        skip_media_download: bool = False,
        save_media_to_gcs: bool = False,
        import_all_users: bool = False,
        scraping_task_id: int | None = None,
        subtask_id: int | None = None,
        **kwargs,
    ) -> dict[str, Any]:
        """Execute the batch posts import task."""
        # Логируем входящие параметры
        logger.info(
            f"[IMPORT DEBUG] ImportBatchPostsTask.execute_task called with: "
            f"start_date={start_date}, end_date={end_date}, "
            f"scraping_task_id={scraping_task_id}, subtask_id={subtask_id}, "
            f"usernames={usernames}, import_comments={import_comments}, "
            f"skip_media_download={skip_media_download}, save_media_to_gcs={save_media_to_gcs}, "
            f"kwargs={kwargs}"
        )
        
        try:
            from instagram_manager.models import InstagramScrapingTask
            from instagram_manager.services.batch_post_service import BatchPostService
            from instagram_manager.services.batch_profile_service import BatchProfileService
        except Exception as e:
            logger.error(f"[IMPORT ERROR] Failed to import required modules: {e}")
            raise

        result: dict[str, Any] = {
            "status": "success",
            "total_posts": 0,
            "profiles_processed": 0,
            "profiles_failed": [],
            "errors": [],
        }

        try:
            # Step 1: Get main task if provided
            main_task = None
            if scraping_task_id:
                try:
                    main_task = InstagramScrapingTask.objects.get(pk=scraping_task_id)
                    logger.info(
                        f"Found main task ID: {scraping_task_id}, "
                        f"task_type={main_task.task_type}, is_periodic={main_task.is_periodic}"
                    )
                    
                    # Если это периодическая задача, очищаем старые подзадачи
                    # ВАЖНО: Очищаем только если это главная задача, а не подзадача
                    if main_task.is_periodic and not subtask_id and "subtask_id" not in kwargs:
                        logger.info("Clearing old subtasks for periodic task (main task only)")
                        main_task.clear_subtasks()
                    elif main_task.is_periodic and (subtask_id or "subtask_id" in kwargs):
                        logger.info(f"Skipping clear_subtasks - this is a subtask (ID: {subtask_id or kwargs.get('subtask_id')})")
                        
                except InstagramScrapingTask.DoesNotExist:
                    logger.warning(
                        f"[TRANSACTION WARNING] Main task ID {scraping_task_id} not found in database! "
                        f"This may indicate a transaction isolation issue. Retrying..."
                    )
                    # Попробуем найти задачу еще раз через небольшую задержку
                    from time import sleep
                    sleep(0.1)  # Уменьшаем задержку
                    try:
                        main_task = InstagramScrapingTask.objects.get(pk=scraping_task_id)
                        logger.info(f"Found main task ID {scraping_task_id} on retry")
                    except InstagramScrapingTask.DoesNotExist:
                        logger.error(
                            f"[TRANSACTION ERROR] Main task ID {scraping_task_id} still not found after retry. "
                            f"This indicates the task was not visible due to transaction isolation. "
                            f"Ensure transaction.on_commit() is used when creating tasks. "
                            f"Available tasks: {list(InstagramScrapingTask.objects.values_list('id', flat=True).order_by('-id')[:10])}"
                        )
            else:
                logger.info("No main task ID provided")

            # Step 2: Validate and create profiles
            self.update_progress(10, f"Validating {len(usernames)} profiles")

            batch_profile_service = BatchProfileService(self.client)
            
            logger.info(
                f"[BATCH DEBUG] Starting profile validation for {len(usernames)} usernames: "
                f"{', '.join(usernames[:3])}{'...' if len(usernames) > 3 else ''}. "
                f"main_task={'YES' if main_task else 'NO'} (ID: {main_task.id if main_task else 'None'})"
            )
            
            profiles, failed_usernames = (
                batch_profile_service.validate_and_create_profiles(
                    usernames=usernames, 
                    parent_task=main_task  # Передаем главную задачу как родительскую
                )
            )
            
            logger.info(
                f"[BATCH DEBUG] Profile validation completed: {len(profiles)} profiles ready, "
                f"{len(failed_usernames)} failed"
            )

            if failed_usernames:
                result["errors"].append(
                    f"Failed to create profiles: {', '.join(failed_usernames)}"
                )
                result["profiles_failed"].extend(failed_usernames)

            if not profiles:
                result["status"] = "failed"
                result["errors"].append("No valid profiles to process")
                return result

            # Step 3: Create batch service
            batch_service = BatchPostService(self.client)

            # Step 4: Create single subtask for all posts if main task exists
            posts_subtask = None
            if main_task:
                # Создаем ОДНУ общую подзадачу для импорта всех постов
                all_usernames = [p.username for p in profiles]
                logger.info(
                    f"[IMPORT DEBUG] Creating single subtask for batch import of {len(profiles)} profiles: "
                    f"{', '.join(all_usernames[:3])}{'...' if len(all_usernames) > 3 else ''}"
                )
                
                posts_subtask = main_task.create_subtask(
                    subtask_type="import_posts",
                    target_identifier=f"batch_{len(profiles)}_profiles",
                    limit=limit,
                    start_date=start_date,
                    end_date=end_date,
                    post_types=post_types or [],
                    skip_media_download=skip_media_download,
                    save_media_to_gcs=save_media_to_gcs,
                    import_all_users=import_all_users,
                    usernames=all_usernames,  # Сохраняем список всех usernames для отслеживания
                )
                posts_subtask.start()  # Отмечаем начало выполнения

            # Step 5: Process profiles in batches
            total_profiles = len(profiles)
            total_posts_imported = 0
            profiles_with_posts = 0
            profiles_failed = []
            all_imported_post_urls = []  # Собираем URLs всех импортированных постов
            
            logger.info(
                f"[IMPORT DEBUG] Starting batch processing: import_comments={import_comments}, "
                f"profiles={total_profiles}, main_task={main_task.id if main_task else None}"
            )
            
            for i in range(0, total_profiles, batch_size):
                batch_profiles = profiles[i : i + batch_size]
                batch_usernames = [p.username for p in batch_profiles]

                # Update progress
                batch_num = i // batch_size + 1
                total_batches = (total_profiles + batch_size - 1) // batch_size
                progress = 20 + int((batch_num / total_batches) * 70)

                self.update_progress(
                    progress,
                    f"Processing batch {batch_num}/{total_batches}: {', '.join(batch_usernames)}",
                )

                # Если нет главной задачи, создаем временную задачу для отслеживания
                # (это для обратной совместимости)
                task = None
                if not main_task:
                    import uuid
                    
                    task = InstagramScrapingTask.objects.create(
                        task_id=f"batch_{uuid.uuid4()}",
                        task_type="posts",
                        target_identifier=f"batch_{len(batch_usernames)}_profiles",
                        status="in_progress",
                        brightdata_dataset_id=getattr(
                            self.client.datasets, "instagram_posts", ""
                        ),
                        batch_identifiers=batch_usernames,
                        skip_media_download=skip_media_download,
                        started_at=timezone.now(),
                    )

                try:
                    # Prepare parameters for batch import
                    batch_params: dict[str, Any] = {
                        "profiles": batch_profiles,
                        "post_types": post_types or [],
                        "limit": limit,
                        "task": task if not main_task else None,  # Передаем task только если нет main_task
                        "skip_media_download": skip_media_download,
                        "save_media_to_gcs": save_media_to_gcs,
                        "import_all_users": import_all_users,
                    }
                    
                    # Only add date parameters if they are provided
                    if start_date:
                        batch_params["start_date"] = start_date
                    if end_date:
                        batch_params["end_date"] = end_date
                    
                    # Execute batch import
                    batch_results = batch_service.import_posts_batch(**batch_params)
                    
                    logger.info(
                        f"[BATCH DEBUG] Batch import returned results for {len(batch_results)} keys: "
                        f"{list(batch_results.keys())}"
                    )

                    # Process results
                    batch_posts_count = 0
                    batch_profiles_success = 0
                    
                    for username, batch_result in batch_results.items():
                        if username == "_statistics":
                            continue

                        if batch_result["success"]:
                            result["profiles_processed"] += 1
                            result["total_posts"] += batch_result["post_count"]
                            batch_posts_count += batch_result["post_count"]
                            batch_profiles_success += 1
                            total_posts_imported += batch_result["post_count"]
                            profiles_with_posts += 1
                            
                            logger.info(
                                f"[BATCH DEBUG] Profile {username} imported {batch_result['post_count']} posts, "
                                f"total so far: {total_posts_imported}"
                            )

                            # Collect post URLs for comment import if requested
                            if import_comments and batch_result.get("posts"):
                                for post in batch_result["posts"]:
                                    if hasattr(post, "post_url") and post.post_url:
                                        all_imported_post_urls.append(post.post_url)
                                logger.info(
                                    f"[COMMENTS DEBUG] Collected {len(batch_result['posts'])} post URLs from {username}"
                                )
                        else:
                            result["profiles_failed"].append(username)
                            result["errors"].append(
                                f"{username}: {batch_result.get('error', 'Unknown error')}"
                            )
                            profiles_failed.append(username)
                    
                    # Обновляем общую подзадачу для постов
                    if posts_subtask:
                        # Не вызываем update_progress здесь - правильные значения будут установлены в main_task_executor
                        logger.info(
                            f"[SUBTASK DEBUG] Posts import progress: {profiles_with_posts}/{total_profiles} profiles, "
                            f"{total_posts_imported} posts imported"
                        )

                    # Обновляем временную задачу только если нет main_task
                    if not main_task:
                        if not hasattr(task, "batch_results") or not task.batch_results:
                            task.batch_results = {}

                        for username, batch_result in batch_results.items():
                            if username != "_statistics":
                                task.batch_results[username] = {
                                    "success": batch_result.get("success", False),
                                    "post_count": batch_result.get("post_count", 0),
                                    "total_found": batch_result.get("total_found", 0),
                                    "error": batch_result.get("error"),
                                }

                        current_batch_items = sum(
                            r.get("post_count", 0)
                            for r in batch_results.values()
                            if r.get("success", False)
                        )
                        task.items_scraped = current_batch_items
                        task.status = "completed"
                        task.completed_at = timezone.now()
                        task.save()

                except Exception as e:
                    error_msg = f"Batch processing error: {e!s}"
                    logger.exception(error_msg)
                    result["errors"].append(error_msg)
                    
                    # Если есть подзадачи, помечаем их как failed
                    if main_task and posts_subtask:
                        if posts_subtask.status != "completed":
                            posts_subtask.fail(str(e))
                    elif not main_task:
                        # Обратная совместимость: обновляем временную задачу
                        task.status = "failed"
                        task.error_message = str(e)
                        task.completed_at = timezone.now()
                        task.save()

            # Step 7: Final update
            logger.info("[IMPORT DEBUG] Step 7: Finalizing batch import")
            self.update_progress(100, "Batch import completed")
            
            # Завершаем подзадачу для постов
            if posts_subtask:
                if profiles_failed:
                    # Если есть неудачные профили, но есть и успешные
                    if profiles_with_posts > 0:
                        posts_subtask.complete(
                            result_data={
                                "status": "partial_success",
                                "profiles_processed": profiles_with_posts,
                                "profiles_failed": len(profiles_failed),
                                "failed_usernames": profiles_failed,
                                "total_posts": total_posts_imported,
                                "items_imported": total_posts_imported,
                                "message": f"Successfully imported {total_posts_imported} posts from {profiles_with_posts}/{total_profiles} profiles"
                            },
                            processed_items=total_posts_imported,
                            total_items=total_posts_imported
                        )
                    else:
                        # Все профили failed
                        posts_subtask.fail(f"Failed to import posts from all {len(profiles_failed)} profiles")
                else:
                    # Все успешно
                    posts_subtask.complete(
                        result_data={
                            "status": "success",
                            "profiles_processed": profiles_with_posts,
                            "total_posts": total_posts_imported,
                            "items_imported": total_posts_imported,
                            "message": f"Successfully imported {total_posts_imported} posts from {profiles_with_posts} profiles"
                        },
                        processed_items=total_posts_imported,
                        total_items=total_posts_imported
                    )
                    
                logger.info(
                    f"[SUBTASK DEBUG] Posts subtask completed: {profiles_with_posts} profiles processed, "
                    f"{total_posts_imported} posts imported, {len(profiles_failed)} profiles failed"
                )
            
            # Step 6: Create async comment import subtask if requested
            logger.info(
                f"[COMMENTS DEBUG] Checking conditions for comment import: "
                f"import_comments={import_comments}, total_posts_imported={total_posts_imported}, "
                f"main_task={'YES' if main_task else 'NO'} (ID: {main_task.id if main_task else 'None'})"
            )
            
            # Changed condition: create comment import even if no NEW posts were imported
            # We want to import comments for ALL posts that match the criteria
            if import_comments and main_task:
                logger.info(
                    f"[COMMENTS DEBUG] Creating comment import subtask (newly imported posts: {total_posts_imported})"
                )
                
                # Check if this is a periodic task and we have date parameters
                if main_task.is_periodic and (start_date or end_date):
                    logger.info(
                        f"[COMMENTS DEBUG] Periodic task detected. Fetching ALL posts from DB "
                        f"for date range: {start_date} to {end_date}"
                    )
                    
                    # For periodic tasks, get ALL posts from DB in date range
                    posts_query = InstagramPost.objects.filter(
                        profile__username__in=usernames
                    )
                    
                    # Apply date filters if provided
                    if start_date:
                        # Convert string date to datetime if needed
                        if isinstance(start_date, str):
                            from datetime import datetime
                            start_datetime = datetime.strptime(start_date, "%Y-%m-%d")
                        else:
                            start_datetime = start_date
                        posts_query = posts_query.filter(posted_at__gte=start_datetime)
                        
                    if end_date:
                        # Convert string date to datetime if needed
                        if isinstance(end_date, str):
                            from datetime import datetime
                            end_datetime = datetime.strptime(end_date, "%Y-%m-%d")
                            # Add 1 day to include the entire end date
                            from datetime import timedelta
                            end_datetime = end_datetime + timedelta(days=1)
                        else:
                            from datetime import timedelta
                            end_datetime = end_date + timedelta(days=1)
                        posts_query = posts_query.filter(posted_at__lt=end_datetime)
                    
                    # Get URLs from database posts
                    db_post_urls = list(posts_query.values_list("url", flat=True))
                    
                    # Combine with newly imported posts (remove duplicates)
                    all_post_urls = list(set(db_post_urls + all_imported_post_urls))
                    post_urls = all_post_urls
                    
                    logger.info(
                        f"[COMMENTS DEBUG] Found {len(db_post_urls)} posts in DB, "
                        f"{len(all_imported_post_urls)} newly imported posts. "
                        f"Total unique posts for comment import: {len(post_urls)}"
                    )
                else:
                    # For regular (non-periodic) tasks, use only newly imported posts
                    post_urls = list(set(all_imported_post_urls))  # Remove duplicates
                    
                    logger.info(
                        f"[COMMENTS DEBUG] Regular task: Using {len(post_urls)} newly imported post URLs "
                        f"(from {len(all_imported_post_urls)} total URLs before deduplication)"
                    )
                
                if post_urls:
                    
                    # Создаем подзадачу для импорта комментариев
                    comments_subtask = main_task.create_subtask(
                        subtask_type="import_comments",
                        target_identifier=f"batch_{len(post_urls)}_posts_comments",
                        limit_per_post=100,
                        include_replies=True,
                        post_urls=post_urls[:100],  # Сохраняем первые 100 для истории
                    )
                    comments_subtask.start()
                    
                    # Запускаем асинхронный импорт комментариев
                    from instagram_manager.tasks import import_batch_comments_async
                    
                    logger.info(
                        f"[COMMENTS DEBUG] Starting async comment import task for {len(post_urls)} posts, "
                        f"subtask_id={comments_subtask.id}"
                    )
                    
                    import_batch_comments_async.delay(
                        post_urls=post_urls,
                        limit_per_post=100,
                        include_replies=True,
                        scraping_task_id=main_task.id,
                        subtask_id=comments_subtask.id,  # Передаем ID созданной подтаски
                    )
                    
                    # Добавляем информацию о запущенной подзадаче в результат
                    result["comments_subtask_created"] = True
                    result["comments_posts_count"] = len(post_urls)
                else:
                    logger.warning(
                        f"[COMMENTS DEBUG] No post URLs found for comment import despite having {total_posts_imported} posts"
                    )
            else:
                logger.info(
                    "[COMMENTS DEBUG] Comment import skipped: one of conditions not met"
                )
            
            # Если есть главная задача, обновляем счетчики подзадач
            if main_task:
                main_task.update_subtask_counters()
                
                # Вычисляем общее количество импортированных элементов из всех подзадач
                total_items = sum(subtask.processed_items for subtask in main_task.subtasks_new.all())
                main_task.items_scraped = total_items
                
                # Определяем статус главной задачи на основе подзадач
                if main_task.subtasks_failed > 0:
                    if main_task.subtasks_completed > 0:
                        main_task.status = "partial_success"
                    else:
                        main_task.status = "failed"
                else:
                    main_task.status = "completed"
                
                main_task.completed_at = timezone.now()
                main_task.save()

            # Set final status
            if result["profiles_failed"]:
                result["status"] = "partial_success"

            return result

        except Exception as e:
            error_msg = f"Unexpected error during batch import: {e!s}"
            logger.exception(f"[IMPORT ERROR] {error_msg}")
            logger.error(f"[IMPORT ERROR] Exception type: {type(e).__name__}")
            logger.error("[IMPORT ERROR] Traceback: ", exc_info=True)
            
            # Update main task if exists
            if main_task:
                main_task.status = "failed"
                main_task.error_message = str(e)
                main_task.completed_at = timezone.now()
                main_task.save()
            
            result["status"] = "failed"
            result["errors"].append(error_msg)
            return result



@task(monitor=True, cache_result=True, cache_ttl=300)
class ImportInstagramProfileTask(BaseTask):
    """
    Task for importing Instagram profile data.

    This task fetches profile data from BrightData API and saves it to the database,
    optionally importing posts and followers in a deep scan.
    """

    task_type: ClassVar[str] = "instagram.import_profile"
    description: ClassVar[str] = "Import Instagram profile data"
    max_retries: ClassVar[int] = 5

    def __init__(self, task_id: str | None = None):
        super().__init__(task_id)
        self.client = BrightDataClient()
        self.post_handler: PostHandler | None = None  # Will be initialized when needed

    def validate_params(
        self, username: str, deep_scan: bool = False, **kwargs
    ) -> dict[str, Any]:
        """Validate task parameters."""
        if not username:
            raise TaskValidationError(
                self.task_type, {"username": "Username is required"}
            )

        # Normalize username
        username = username.lower().strip().lstrip("@")

        return {
            "username": username,
            "deep_scan": deep_scan,
            "update_existing": kwargs.get("update_existing", False),
            "import_posts": kwargs.get("import_posts", deep_scan),
            "import_followers": kwargs.get("import_followers", deep_scan),
            "posts_limit": kwargs.get("posts_limit", 50),
            "followers_limit": kwargs.get("followers_limit", 100),
        }

    def execute_task(
        self,
        username: str,
        deep_scan: bool = False,
        update_existing: bool = False,
        import_posts: bool = False,
        import_followers: bool = False,
        posts_limit: int = 50,
        followers_limit: int = 100,
        **kwargs,
    ) -> dict[str, Any]:
        """Execute the profile import task."""
        result = {
            "status": "success",
            "username": username,
            "profile_id": None,
            "posts_imported": 0,
            "followers_imported": 0,
            "errors": [],
        }

        try:
            # Step 1: Check if profile exists
            self.update_progress(10, f"Checking if profile @{username} exists")

            existing_profile = None
            if not update_existing:
                try:
                    existing_profile = InstagramProfile.objects.get(username=username)
                    logger.info(
                        f"Profile @{username} already exists",
                        extra={"username": username, "profile_id": existing_profile.id},
                    )
                    result["profile_id"] = existing_profile.id
                    result["status"] = "skipped"
                    result["message"] = "Profile already exists"

                    if not (import_posts or import_followers):
                        return result
                except InstagramProfile.DoesNotExist:
                    pass

            # Step 2: Fetch profile data from API
            if not existing_profile or update_existing:
                self.update_progress(20, f"Fetching profile data for @{username}")

                try:
                    profile_data = self.client.get_profile(username)
                except Exception as e:
                    error_msg = f"Failed to fetch profile data: {e!s}"
                    logger.error(
                        error_msg, extra={"username": username, "error": str(e)}
                    )
                    raise TaskExecutionError(self.task_id, error_msg, e)

                # Step 3: Save profile data
                self.update_progress(40, f"Saving profile @{username}")

                try:
                    with transaction.atomic():
                        logger.debug(
                            f"[TRANSACTION START] Starting atomic transaction for saving profile @{username}"
                        )
                        
                        profile = ProfileHandler.process_profile_data(profile_data)
                        result["profile_id"] = profile.id

                        logger.info(
                            f"Successfully imported profile @{username}",
                            extra={
                                "username": username,
                                "profile_id": profile.id,
                                "followers": profile.follower_count,
                                "posts": profile.post_count,
                            },
                        )
                        
                        logger.debug(
                            f"[TRANSACTION END] Successfully saved profile @{username} in transaction"
                        )
                except Exception as e:
                    error_msg = f"Failed to save profile data: {e!s}"
                    logger.error(
                        f"[TRANSACTION ERROR] Failed to save profile @{username} in transaction: {e}"
                    )
                    logger.error(
                        error_msg, extra={"username": username, "error": str(e)}
                    )
                    raise TaskExecutionError(self.task_id, error_msg, e)
            else:
                profile = existing_profile

            # Step 4: Import posts if requested
            if import_posts and profile:
                self.update_progress(60, f"Importing posts for @{username}")

                try:
                    posts_result = self._import_posts(profile, posts_limit)
                    result["posts_imported"] = posts_result["imported"]
                    if posts_result.get("errors"):
                        result["errors"].extend(posts_result["errors"])
                except Exception as e:
                    error_msg = f"Failed to import posts: {e!s}"
                    logger.error(
                        error_msg, extra={"username": username, "error": str(e)}
                    )
                    result["errors"].append(error_msg)

            # Step 5: Import followers if requested
            if import_followers and profile:
                self.update_progress(80, f"Importing followers for @{username}")

                try:
                    followers_result = self._import_followers(profile, followers_limit)
                    result["followers_imported"] = followers_result["imported"]
                    if followers_result.get("errors"):
                        result["errors"].extend(followers_result["errors"])
                except Exception as e:
                    error_msg = f"Failed to import followers: {e!s}"
                    logger.error(
                        error_msg, extra={"username": username, "error": str(e)}
                    )
                    result["errors"].append(error_msg)

            # Step 6: Complete
            self.update_progress(100, f"Completed import for @{username}")

            # Set final status
            if result["errors"]:
                result["status"] = "partial_success"

            return result

        except TaskExecutionError:
            raise
        except Exception as e:
            error_msg = f"Unexpected error during profile import: {e!s}"
            logger.exception(error_msg, extra={"username": username})
            raise TaskExecutionError(self.task_id, error_msg, e)

    def _import_posts(self, profile: InstagramProfile, limit: int) -> dict[str, Any]:
        """Import posts for a profile."""
        result: dict[str, Any] = {"imported": 0, "errors": []}

        try:
            # Get posts from API
            posts_data = self.client.get_posts(profile.username, limit=limit)
            
            # Initialize PostHandler if not already done
            if not self.post_handler:
                self.post_handler = PostHandler(save_to_gcs=False)  # Default for profile import

            # Process posts
            for i, post_data in enumerate(posts_data):
                try:
                    self.post_handler.process_post_data(post_data, profile)
                    result["imported"] += 1

                    # Update progress
                    progress = 60 + int((i / len(posts_data)) * 20)
                    self.update_progress(
                        progress, f"Imported {i + 1}/{len(posts_data)} posts"
                    )
                except Exception as e:
                    error_msg = f"Failed to import post: {e!s}"
                    logger.error(error_msg, extra={"error": str(e)})
                    result["errors"].append(error_msg)

            return result

        except Exception as e:
            raise TaskExecutionError(self.task_id, f"Failed to import posts: {e!s}", e)

    def _import_followers(
        self, profile: InstagramProfile, limit: int
    ) -> dict[str, Any]:
        """Import followers for a profile."""
        result: dict[str, Any] = {"imported": 0, "errors": []}

        # Followers import requires additional API capabilities
        # When implementing, use:
        # 1. BrightData API endpoint for followers data
        # 2. InstagramFollowerRepository for data storage
        # 3. Batch processing for large follower lists
        logger.info(
            "Followers import skipped - requires API upgrade",
            extra={
                "profile_id": profile.id,
                "username": profile.username,
                "reason": "API_NOT_AVAILABLE",
            },
        )

        return result


@task(monitor=True)
class ImportInstagramPostsTask(BaseTask):
    """Task for importing Instagram posts for a profile."""

    task_type: ClassVar[str] = "instagram.import_posts"
    description: ClassVar[str] = "Import Instagram posts"
    max_retries: ClassVar[int] = 3

    def validate_params(self, profile_id: int, **kwargs) -> dict[str, Any]:
        """Validate task parameters."""
        if not profile_id:
            raise TaskValidationError(
                self.task_type, {"profile_id": "Profile ID is required"}
            )

        return {
            "profile_id": profile_id,
            "limit": kwargs.get("limit", 50),
            "since_date": kwargs.get("since_date"),
            "import_comments": kwargs.get("import_comments", False),
            "import_media": kwargs.get("import_media", True),
            "save_media_to_gcs": kwargs.get("save_media_to_gcs", False),
            "skip_media_download": kwargs.get("skip_media_download", False),
            "start_date": kwargs.get("start_date"),
            "end_date": kwargs.get("end_date"),
        }

    def execute_task(
        self,
        profile_id: int,
        limit: int = 50,
        since_date: str | None = None,
        import_comments: bool = False,
        import_media: bool = True,
        **kwargs,
    ) -> dict[str, Any]:
        """Execute the posts import task."""
        result: dict[str, Any] = {
            "status": "success",
            "profile_id": profile_id,
            "posts_imported": 0,
            "comments_imported": 0,
            "media_downloaded": 0,
            "errors": [],
        }

        try:
            # Get profile
            self.update_progress(10, "Loading profile")
            profile = InstagramProfile.objects.get(id=profile_id)

            # Initialize client
            client = BrightDataClient()
            
            # Initialize PostHandler with GCS setting if provided
            save_media_to_gcs = kwargs.get("save_media_to_gcs", False)
            skip_media_download = kwargs.get("skip_media_download", False)
            post_handler = PostHandler(save_to_gcs=save_media_to_gcs)

            # Get posts data
            self.update_progress(20, f"Fetching posts for @{profile.username}")

            # Prepare filters
            start_date = kwargs.get("start_date", "")
            end_date = kwargs.get("end_date", "")
            post_types = kwargs.get("post_types", [])

            posts_data = client.get_posts(
                profile.username,
                start_date=start_date,
                end_date=end_date,
                post_types=post_types,
            )

            # Apply limit if specified
            if limit and len(posts_data) > limit:
                posts_data = posts_data[:limit]

            total_posts = len(posts_data)

            if total_posts == 0:
                self.update_progress(100, "No posts to import")
                return result

            # Process posts
            for i, post_data in enumerate(posts_data):
                try:
                    # Update progress
                    progress = 20 + int((i / total_posts) * 60)
                    self.update_progress(
                        progress, f"Processing post {i + 1}/{total_posts}"
                    )

                    # Process post
                    post = post_handler.process_post_data(post_data, profile, skip_media_download=skip_media_download)
                    result["posts_imported"] += 1

                    # Import comments if requested
                    if import_comments and post.comment_count > 0:
                        # Check if we're part of a batch import
                        logger.warning(
                            f"[INDIVIDUAL COMMENT IMPORT] ImportInstagramPostsTask is trying to import comments "
                            f"individually for post {post.shortcode}. This should not happen in batch mode! "
                            f"import_comments={import_comments}, profile_id={profile_id}"
                        )
                        
                        # Skip individual comment import in batch mode
                        # Comments should be imported via ImportBatchCommentsTask
                        if False:  # Disabled to prevent individual requests
                            try:
                                comments_data = client.get_post_comments(post.post_url)
                                from instagram_manager.instagram_api.data_handlers.comment_handler import (
                                    CommentHandler,
                                )

                                comment_handler = CommentHandler()

                                for comment_data in comments_data:
                                    comment_handler.process_comment_data(comment_data, post)
                                    result["comments_imported"] += 1
                            except Exception as e:
                                error_msg = f"Failed to import comments for post {post.shortcode}: {e!s}"
                                logger.error(error_msg)
                                result["errors"].append(error_msg)

                    # Download media if requested
                    if import_media and post.media.exists():
                        from instagram_manager.instagram_api.data_handlers.media_handler import MediaHandler

                        media_handler = MediaHandler(save_to_gcs=kwargs.get("save_media_to_gcs", False))

                        for media in post.media.all():
                            if not media.is_downloaded:
                                try:
                                    if media_handler.download_media(media):
                                        result["media_downloaded"] += 1
                                except Exception as e:
                                    error_msg = (
                                        f"Failed to download media {media.id}: {e!s}"
                                    )
                                    logger.error(error_msg)
                                    result["errors"].append(error_msg)

                except Exception as e:
                    error_msg = f"Failed to import post: {e!s}"
                    logger.error(error_msg, extra={"error": str(e)})
                    result["errors"].append(error_msg)

            # Final update
            self.update_progress(
                100, f"Completed import of {result['posts_imported']} posts"
            )

            # Set final status
            if result["errors"]:
                result["status"] = "partial_success"

            return result

        except InstagramProfile.DoesNotExist:
            raise TaskExecutionError(
                self.task_id, f"Profile with ID {profile_id} not found"
            )
        except Exception as e:
            error_msg = f"Unexpected error during posts import: {e!s}"
            logger.exception(error_msg)
            raise TaskExecutionError(self.task_id, error_msg, e)


@task(monitor=True)
class ImportInstagramCommentsTask(BaseTask):
    """Task for importing Instagram comments for posts."""

    task_type: ClassVar[str] = "instagram.import_comments"
    description: ClassVar[str] = "Import Instagram comments"
    max_retries: ClassVar[int] = 3

    def validate_params(self, **kwargs) -> dict[str, Any]:
        """Validate task parameters."""
        post_ids = kwargs.get("post_ids", [])
        if not post_ids:
            raise TaskValidationError(
                self.task_type, {"post_ids": "Post IDs are required"}
            )

        return {
            "post_ids": post_ids,
            "limit_per_post": kwargs.get("limit_per_post", 100),
            "import_replies": kwargs.get("import_replies", True),
        }

    def execute_task(
        self,
        post_ids: list[int],
        limit_per_post: int = 100,
        import_replies: bool = True,
        **kwargs,
    ) -> dict[str, Any]:
        """Execute the comments import task."""
        result: dict[str, Any] = {
            "status": "success",
            "posts_processed": 0,
            "comments_imported": 0,
            "replies_imported": 0,
            "errors": [],
        }

        from instagram_manager.services import CommentService

        comment_service = CommentService()

        total_posts = len(post_ids)

        for i, post_id in enumerate(post_ids):
            try:
                # Update progress
                progress = int((i / total_posts) * 100)
                self.update_progress(progress, f"Processing post {i + 1}/{total_posts}")

                # Get post
                post = InstagramPost.objects.get(id=post_id)

                # Import comments
                comments = comment_service.import_comments_for_post(
                    post=post,
                    limit=limit_per_post,
                    include_replies=import_replies,
                    min_likes=kwargs.get("min_likes"),
                    force_update=kwargs.get("update_existing", False),
                )

                result["posts_processed"] += 1
                result["comments_imported"] += len(comments)

                # Count replies
                if import_replies:
                    for comment in comments:
                        if comment.parent_comment_id:
                            result["replies_imported"] += 1

                logger.info(
                    f"Imported {len(comments)} comments for post {post.shortcode}",
                    extra={"post_id": post_id, "comments_count": len(comments)},
                )

            except InstagramPost.DoesNotExist:
                error_msg = f"Post with ID {post_id} not found"
                logger.error(error_msg)
                result["errors"].append(error_msg)
            except Exception as e:
                error_msg = f"Failed to import comments for post {post_id}: {e!s}"
                logger.error(error_msg, extra={"post_id": post_id, "error": str(e)})
                result["errors"].append(error_msg)

        # Final update
        self.update_progress(
            100, f"Completed: {result['comments_imported']} comments imported"
        )

        # Set final status
        if result["errors"]:
            result["status"] = (
                "partial_success" if result["comments_imported"] > 0 else "failed"
            )

        return result


@task(monitor=True)
class ImportInstagramFollowersTask(BaseTask):
    """Task for importing Instagram followers."""

    task_type: ClassVar[str] = "instagram.import_followers"
    description: ClassVar[str] = "Import Instagram followers"
    max_retries: ClassVar[int] = 3

    def validate_params(self, profile_id: int, **kwargs) -> dict[str, Any]:
        """Validate task parameters."""
        if not profile_id:
            raise TaskValidationError(
                self.task_type, {"profile_id": "Profile ID is required"}
            )

        return {
            "profile_id": profile_id,
            "limit": kwargs.get("limit", 1000),
            "import_profiles": kwargs.get("import_profiles", False),
        }

    def execute_task(
        self,
        profile_id: int,
        limit: int = 1000,
        import_profiles: bool = False,
        **kwargs,
    ) -> dict[str, Any]:
        """Execute the followers import task."""
        try:
            profile = InstagramProfile.objects.get(id=profile_id)
        except InstagramProfile.DoesNotExist:
            raise TaskValidationError(
                self.task_type,
                {"profile_id": f"Profile with ID {profile_id} not found"}
            )

        self.update_progress(0, f"Starting followers import for @{profile.username}")

        # Note: Full implementation requires API support
        # Structure for future implementation:
        # 1. Connect to BrightData followers endpoint
        # 2. Paginate through followers list
        # 3. Create InstagramFollower records
        # 4. Optionally import follower profiles

        logger.info(
            "Followers import not available in current API version",
            extra={
                "profile_id": profile_id,
                "username": profile.username,
                "requested_limit": limit,
            },
        )

        self.update_progress(100, "Followers import requires API upgrade")

        return {
            "status": "success",
            "profile_id": profile_id,
            "followers_imported": 0,
            "message": "Feature requires BrightData API upgrade",
        }


@task(monitor=True)
class ImportBatchCommentsTask(BaseTask):
    """
    Task for batch importing Instagram comments for multiple posts.
    
    This task efficiently fetches comments for multiple posts in a single
    BrightData snapshot request.
    """
    
    task_type: ClassVar[str] = "instagram.import_batch_comments"
    description: ClassVar[str] = "Batch import Instagram comments"
    max_retries: ClassVar[int] = 3
    
    def __init__(self, task_id: str | None = None):
        # Add print for guaranteed output
        print(f"[COMMENTS TASK PRINT] Initializing ImportBatchCommentsTask with task_id={task_id}")
        logger.info(f"[COMMENTS TASK] Initializing ImportBatchCommentsTask with task_id={task_id}")
        
        try:
            super().__init__(task_id)
            print("[COMMENTS TASK PRINT] BaseTask initialized successfully")
            logger.info("[COMMENTS TASK] BaseTask initialized successfully")
        except Exception as e:
            print(f"[COMMENTS TASK PRINT] Failed to initialize BaseTask: {e}")
            logger.error(f"[COMMENTS TASK] Failed to initialize BaseTask: {e}", exc_info=True)
            raise
        
        # Initialize BrightData client
        try:
            print("[COMMENTS TASK PRINT] Initializing BrightData client")
            logger.info("[COMMENTS TASK] Initializing BrightData client")
            self.client = BrightDataClient()
            print("[COMMENTS TASK PRINT] BrightData client initialized successfully")
            logger.info("[COMMENTS TASK] BrightData client initialized successfully")
        except Exception as e:
            print(f"[COMMENTS TASK PRINT] BrightData client initialization error: {e}")
            logger.error(f"[COMMENTS TASK] BrightData client initialization error: {e}", exc_info=True)
            raise
    
    def validate_params(self, post_urls: list[str], **kwargs) -> dict[str, Any]:
        """Validate task parameters."""
        print(f"[COMMENTS TASK PRINT] validate_params called with {len(post_urls) if post_urls else 0} post_urls")
        logger.info(
            f"[COMMENTS TASK] validate_params called with {len(post_urls) if post_urls else 0} post_urls"
        )
        logger.info(f"[COMMENTS TASK] kwargs: {list(kwargs.keys())}")
        logger.info(f"[COMMENTS TASK] subtask_id from kwargs: {kwargs.get('subtask_id')}")
        
        if not post_urls:
            print("[COMMENTS TASK PRINT] No post URLs provided - raising validation error")
            logger.error("[COMMENTS TASK] No post URLs provided")
            raise TaskValidationError(
                self.task_type, {"post_urls": "At least one post URL is required"}
            )
        
        validated_params = {
            "post_urls": post_urls,
            "limit_per_post": kwargs.get("limit_per_post", 100),
            "include_replies": kwargs.get("include_replies", True),
            "min_likes": kwargs.get("min_likes"),
            "update_existing": kwargs.get("update_existing", False),
            "scraping_task_id": kwargs.get("scraping_task_id"),
            "subtask_id": kwargs.get("subtask_id"),
        }
        
        print(f"[COMMENTS TASK PRINT] Validated params successfully: {len(validated_params)} params")
        logger.info(f"[COMMENTS TASK] Validated params: subtask_id={validated_params['subtask_id']}")
        return validated_params
    
    def execute_task(
        self,
        post_urls: list[str],
        limit_per_post: int = 100,
        include_replies: bool = True,
        min_likes: int | None = None,
        update_existing: bool = False,
        scraping_task_id: int | None = None,
        subtask_id: int | None = None,
        **kwargs,
    ) -> dict[str, Any]:
        """Execute the batch comments import task."""
        try:
            print(f"[COMMENTS TASK PRINT] execute_task started with {len(post_urls)} post URLs")
            logger.info(
                f"[COMMENTS TASK] ImportBatchCommentsTask.execute_task called with: "
                f"post_urls count={len(post_urls)}, limit_per_post={limit_per_post}, "
                f"include_replies={include_replies}, scraping_task_id={scraping_task_id}, "
                f"subtask_id={subtask_id}"
            )
            logger.info(f"[COMMENTS TASK] First 3 post URLs: {post_urls[:3]}")
            logger.info(f"[COMMENTS TASK] Task ID: {self.task_id}")
            logger.info(f"[COMMENTS TASK] All kwargs: {list(kwargs.keys())}")
        except Exception as e:
            print(f"[COMMENTS TASK PRINT] EARLY ERROR in execute_task: {e}")
            logger.error(f"[COMMENTS TASK] EARLY ERROR in execute_task: {e}", exc_info=True)
            raise
        
        try:
            print("[COMMENTS TASK PRINT] Starting imports")
            from instagram_manager.services.batch_comment_service import BatchCommentService
            print("[COMMENTS TASK PRINT] Imports completed successfully")
            logger.info("[COMMENTS TASK] Imports completed successfully")
            
            # Добавляем диагностику подключения к БД
            from django.db import connection
            print(f"[COMMENTS TASK PRINT] DB connection vendor: {connection.vendor}")
            logger.info(f"[COMMENTS TASK] DB connection vendor: {connection.vendor}")
            logger.info(f"[COMMENTS TASK] DB connection is usable: {connection.is_usable()}")
            
        except Exception as e:
            print(f"[COMMENTS TASK PRINT] Import error: {e}")
            logger.exception(f"[COMMENTS TASK] Import error: {e}")
            raise
        
        result: dict[str, Any] = {
            "status": "success",
            "total_comments": 0,
            "posts_processed": 0,
            "posts_failed": [],
            "errors": [],
        }
        
        print("[COMMENTS TASK PRINT] Result dict created successfully")
        logger.info("[COMMENTS TASK] Starting task execution")
        
        # Initialize comments_subtask to None for error handling
        comments_subtask = None
        
        try:
            # Step 1: Get main task if provided
            main_task = None
            if scraping_task_id:
                logger.info(f"[IMPORT DEBUG] Looking for main task ID: {scraping_task_id}")
                
                # Добавляем повторные попытки получения задачи с экспоненциальной задержкой
                # С transaction.on_commit() это не должно быть нужно, но оставляем для edge cases
                max_retries = 2  # Уменьшаем количество попыток
                retry_delay = 0.1  # Начинаем с меньшей задержки
                
                for attempt in range(max_retries):
                    try:
                        # Попытка получить задачу
                        main_task = InstagramScrapingTask.objects.get(pk=scraping_task_id)
                        logger.info(
                            f"[IMPORT DEBUG] Found main task ID: {scraping_task_id} on attempt {attempt + 1}, "
                            f"task_type={main_task.task_type}, "
                            f"status={main_task.status}"
                        )
                        
                        # Если это периодическая задача, очищаем старые подзадачи
                        # ВАЖНО: Очищаем только если это главная задача, а не подзадача
                        if main_task.is_periodic and not subtask_id and "subtask_id" not in kwargs:
                            logger.info("Clearing old subtasks for periodic task (main task only)")
                            main_task.clear_subtasks()
                        elif main_task.is_periodic and (subtask_id or "subtask_id" in kwargs):
                            logger.info(f"Skipping clear_subtasks - this is a subtask (ID: {subtask_id or kwargs.get('subtask_id')})")
                        
                        break  # Успешно получили задачу, выходим из цикла
                        
                    except InstagramScrapingTask.DoesNotExist:
                        if attempt < max_retries - 1:
                            logger.warning(
                                f"[TRANSACTION WARNING] Main task {scraping_task_id} not found on attempt {attempt + 1}. "
                                f"This may indicate a transaction isolation issue. "
                                f"Retrying in {retry_delay}s..."
                            )
                            
                            # Логируем доступные задачи для диагностики
                            available_tasks = list(
                                InstagramScrapingTask.objects.values_list("id", flat=True).order_by("-id")[:10]
                            )
                            logger.warning(f"[TRANSACTION DEBUG] Available task IDs in DB: {available_tasks}")
                            
                            # Проверяем настройки базы данных
                            from django.db import connection
                            with connection.cursor() as cursor:
                                cursor.execute("SHOW TRANSACTION ISOLATION LEVEL")
                                isolation_level = cursor.fetchone()
                                logger.warning(f"[TRANSACTION DEBUG] Current isolation level: {isolation_level}")
                            
                            import time
                            time.sleep(retry_delay)
                            retry_delay *= 2  # Экспоненциальная задержка
                        else:
                            logger.error(
                                f"[TRANSACTION ERROR] Main task ID {scraping_task_id} not found after {max_retries} attempts. "
                                f"This indicates the task was not visible due to transaction isolation. "
                                f"Ensure transaction.on_commit() is used when creating tasks."
                            )
                            # Финальная диагностика
                            all_tasks = list(
                                InstagramScrapingTask.objects.values_list("id", flat=True).order_by("-id")[:20]
                            )
                            logger.error(f"[TRANSACTION ERROR] All available task IDs: {all_tasks}")
                            raise
                            
                    except Exception as e:
                        logger.error(f"[IMPORT ERROR] Unexpected error retrieving main task: {e}", exc_info=True)
                        if attempt == max_retries - 1:
                            raise
            
            # Step 2: Create batch service
            logger.info("[COMMENTS TASK] Creating BatchCommentService with client")
            batch_service = BatchCommentService(self.client)
            logger.info("[COMMENTS TASK] BatchCommentService created successfully")
            
            # Step 3: Create or reuse subtask for all comments
            comments_subtask = None
            if subtask_id:
                # Reuse existing subtask created by parent task
                logger.info(
                    f"[COMMENTS TASK] Reusing existing subtask ID {subtask_id} for comment import"
                )
                try:
                    if main_task:
                        # Log query parameters for debugging
                        logger.info(
                            f"[COMMENTS TASK] Querying InstagramSubTask with pk={subtask_id}, parent_task_id={main_task.id}"
                        )
                        # Query InstagramSubTask directly instead of using related manager
                        try:
                            comments_subtask = InstagramSubTask.objects.get(pk=subtask_id, parent_task=main_task)
                        except InstagramSubTask.DoesNotExist:
                            # Try without parent_task filter as fallback
                            logger.warning(
                                f"[COMMENTS TASK] Subtask {subtask_id} not found with parent_task={main_task.id}, "
                                f"trying without parent_task filter"
                            )
                            comments_subtask = InstagramSubTask.objects.get(pk=subtask_id)
                        logger.info(
                            f"[COMMENTS TASK] Successfully retrieved subtask {subtask_id} from main task {main_task.id}"
                        )
                        logger.info(
                            f"[COMMENTS TASK] Subtask details: type={comments_subtask.subtask_type}, "
                            f"status={comments_subtask.status}, target={comments_subtask.target_identifier}, "
                            f"total_items={comments_subtask.total_items}"
                        )
                        # Update subtask status to running
                        if comments_subtask.status != "running":
                            comments_subtask.start()
                            logger.info(f"[COMMENTS TASK] Updated subtask {subtask_id} status to 'running'")
                        # Total items будет установлен при complete()
                            logger.info(
                                f"[COMMENTS TASK] Updated subtask {subtask_id} total_items to {len(post_urls)}"
                            )
                    else:
                        logger.error(
                            f"[COMMENTS TASK] Cannot retrieve subtask {subtask_id} - no main task available"
                        )
                        raise TaskExecutionError(
                            self.task_id,
                            f"Subtask {subtask_id} not found - no main task available"
                        )
                except InstagramSubTask.DoesNotExist:
                    logger.error(
                        f"[COMMENTS TASK] Subtask {subtask_id} does not exist in database"
                    )
                    
                    # Try to recover by creating a new subtask
                    if main_task:
                        logger.warning(
                            f"[COMMENTS TASK] Attempting to recover by creating a new subtask for main task {main_task.id}"
                        )
                        try:
                            comments_subtask = main_task.create_subtask(
                                subtask_type="import_comments",
                                target_identifier=f"batch_{len(post_urls)}_posts_recovery",
                                limit_per_post=limit_per_post,
                                include_replies=include_replies,
                                post_urls=post_urls[:100],  # Save first 100 for history
                            )
                            comments_subtask.start()
                            logger.info(
                                f"[COMMENTS TASK] Successfully created recovery subtask {comments_subtask.id}"
                            )
                        except Exception as recovery_error:
                            logger.error(
                                f"[COMMENTS TASK] Failed to create recovery subtask: {recovery_error}"
                            )
                            raise TaskExecutionError(
                                self.task_id,
                                f"Subtask {subtask_id} not found and recovery failed: {recovery_error}"
                            )
                    else:
                        raise TaskExecutionError(
                            self.task_id,
                            f"Subtask {subtask_id} not found in database and no main task for recovery"
                        )
                except Exception as e:
                    logger.error(
                        f"[COMMENTS TASK] Failed to retrieve subtask {subtask_id}: {e}",
                        exc_info=True
                    )
                    raise TaskExecutionError(
                        self.task_id,
                        f"Failed to retrieve subtask {subtask_id}: {e}"
                    )
            elif main_task:
                # Create new subtask only if not provided
                logger.info(
                    f"Creating single subtask for batch import of comments from {len(post_urls)} posts"
                )
                
                comments_subtask = main_task.create_subtask(
                    subtask_type="import_comments",
                    target_identifier=f"batch_{len(post_urls)}_posts",
                    limit_per_post=limit_per_post,
                    include_replies=include_replies,
                    min_likes=min_likes,
                    update_existing=update_existing,
                    post_urls=post_urls,
                )
                comments_subtask.start()
            
            # Step 4: Import comments in batch
            self.update_progress(20, f"Importing comments for {len(post_urls)} posts")
            
            import_results = batch_service.import_comments_batch(
                post_urls=post_urls,
                limit_per_post=limit_per_post,
                include_replies=include_replies,
                min_likes=min_likes,
                update_existing=update_existing,
                task=main_task,
            )
            
            # Step 5: Process results
            for post_url, comment_result in import_results.items():
                if post_url == "_statistics":
                    continue
                
                if comment_result["success"]:
                    result["posts_processed"] += 1
                    result["total_comments"] += comment_result["comment_count"]
                else:
                    result["posts_failed"].append(post_url)
                    result["errors"].append(
                        f"{post_url}: {comment_result.get('error', 'Unknown error')}"
                    )
            
            # Step 6: Update subtask
            if comments_subtask:
                # Не вызываем update_progress здесь - правильные значения будут установлены в main_task_executor
                # processed_items будет установлен из items_imported в результате
                
                if result["posts_failed"]:
                    if result["posts_processed"] > 0:
                        # Есть обработанные посты - это частичный успех
                        comments_subtask.complete(
                            result_data={
                                "status": "partial_success",
                                "posts_processed": result["posts_processed"],
                                "posts_failed": len(result["posts_failed"]),
                                "total_comments": result["total_comments"],
                                "items_imported": result["total_comments"],
                                "message": f"Imported {result['total_comments']} comments from {result['posts_processed']}/{len(post_urls)} posts"
                            },
                            processed_items=result["total_comments"],
                            total_items=result["total_comments"]
                        )
                        logger.info(
                            f"[COMMENTS TASK] Partial success: {result['posts_processed']} posts processed, "
                            f"{len(result['posts_failed'])} posts failed"
                        )
                    else:
                        # Нет обработанных постов - проверяем, есть ли посты в БД
                        missing_posts = []
                        for failed_url in result["posts_failed"]:
                            logger.warning(f"[COMMENTS TASK] Failed post URL: {failed_url}")
                            if "Post not found in database" in str(result.get("errors", [])):
                                missing_posts.append(failed_url)
                        
                        if missing_posts:
                            error_msg = (
                                f"Failed to import comments: {len(missing_posts)} posts not found in database. "
                                f"Please import posts first before importing comments."
                            )
                            logger.error(f"[COMMENTS TASK] {error_msg}")
                            comments_subtask.fail(error_msg)
                        else:
                            comments_subtask.fail(f"Failed to import comments from all {len(result['posts_failed'])} posts")
                else:
                    comments_subtask.complete(
                        result_data={
                            "status": "success",
                            "posts_processed": result["posts_processed"],
                            "total_comments": result["total_comments"],
                            "items_imported": result["total_comments"],
                            "message": f"Successfully imported {result['total_comments']} comments from {result['posts_processed']} posts"
                        },
                        processed_items=result["total_comments"],
                        total_items=result["total_comments"]
                    )
            
            # Step 7: Update main task
            if main_task:
                main_task.update_subtask_counters()
                
                # Вычисляем общее количество импортированных элементов из всех подзадач
                total_items = sum(subtask.processed_items for subtask in main_task.subtasks_new.all())
                main_task.items_scraped = total_items
                
                if main_task.subtasks_failed > 0:
                    main_task.status = "partial_success" if main_task.subtasks_completed > 0 else "failed"
                else:
                    main_task.status = "completed"
                
                main_task.completed_at = timezone.now()
                main_task.save()
            
            # Step 8: Final update
            self.update_progress(100, "Batch comment import completed")
            
            # Set final status
            if result["posts_failed"]:
                result["status"] = "partial_success"
            
            return result
            
        except Exception as e:
            error_msg = f"Unexpected error during batch comment import: {e!s}"
            print(f"[COMMENTS TASK PRINT] FATAL ERROR: {error_msg}")
            print(f"[COMMENTS TASK PRINT] Error type: {type(e).__name__}")
            print(f"[COMMENTS TASK PRINT] Error args: {e.args}")
            logger.exception(f"[COMMENTS TASK] FATAL ERROR: {error_msg}")
            
            result["status"] = "failed"
            result["errors"].append(error_msg)
            
            # Update subtask status if exists
            if comments_subtask:
                try:
                    comments_subtask.fail(error_msg)
                    logger.info(f"[COMMENTS TASK] Marked subtask {comments_subtask.id} as failed")
                except Exception as sub_e:
                    logger.error(f"[COMMENTS TASK] Failed to update subtask status: {sub_e}")
            
            return result


@task(monitor=True)
class ImportBatchProfilesTask(BaseTask):
    """
    Task for batch importing Instagram profiles.
    
    This task efficiently fetches multiple profiles in a single operation
    and creates appropriate subtasks for tracking.
    """
    
    task_type: ClassVar[str] = "instagram.import_batch_profiles"
    description: ClassVar[str] = "Batch import Instagram profiles"
    max_retries: ClassVar[int] = 3
    
    def __init__(self, task_id: str | None = None):
        super().__init__(task_id)
        self.client = BrightDataClient()
    
    def validate_params(self, usernames: list[str], **kwargs) -> dict[str, Any]:
        """Validate task parameters."""
        if not usernames:
            raise TaskValidationError(
                self.task_type, {"usernames": "At least one username is required"}
            )
        
        # Normalize usernames
        usernames = [u.lower().strip().lstrip("@") for u in usernames]
        
        params = {
            "usernames": usernames,
            "import_posts": kwargs.get("import_posts", False),
            "posts_limit": kwargs.get("posts_limit"),
            "skip_media_download": kwargs.get("skip_media_download", False),
            "scraping_task_id": kwargs.get("scraping_task_id"),
        }
        
        return params
    
    def execute_task(
        self,
        usernames: list[str],
        import_posts: bool = False,
        posts_limit: int | None = None,
        skip_media_download: bool = False,
        scraping_task_id: int | None = None,
        subtask_id: int | None = None,
        **kwargs,
    ) -> dict[str, Any]:
        """Execute the batch profiles import task."""
        from instagram_manager.models import InstagramScrapingTask
        from instagram_manager.services.batch_profile_service import BatchProfileService
        
        result: dict[str, Any] = {
            "status": "success",
            "profiles_imported": 0,
            "profiles_failed": [],
            "posts_imported": 0,
            "errors": [],
        }
        
        try:
            # Step 1: Get main task if provided
            main_task = None
            if scraping_task_id:
                try:
                    main_task = InstagramScrapingTask.objects.get(pk=scraping_task_id)
                    logger.info(f"Found main task ID: {scraping_task_id}")
                    
                    # Clear old subtasks for periodic tasks
                    # ВАЖНО: Очищаем только если это главная задача, а не подзадача
                    if main_task.is_periodic and not subtask_id and "subtask_id" not in kwargs:
                        logger.info("Clearing old subtasks for periodic task (main task only)")
                        main_task.clear_subtasks()
                    elif main_task.is_periodic and (subtask_id or "subtask_id" in kwargs):
                        logger.info(f"Skipping clear_subtasks - this is a subtask (ID: {subtask_id or kwargs.get('subtask_id')})")
                        
                except InstagramScrapingTask.DoesNotExist:
                    logger.error(f"Main task ID {scraping_task_id} not found")
            
            # Step 2: Create batch service
            batch_service = BatchProfileService(self.client)
            
            # Step 3: Import profiles with subtask creation
            self.update_progress(20, f"Importing {len(usernames)} profiles")
            
            # Create profile subtask if main task exists
            profile_subtask = None
            if main_task:
                profile_subtask = main_task.create_subtask(
                    subtask_type="import_profile",
                    target_identifier=f"batch_{len(usernames)}_profiles",
                    usernames=usernames,
                )
                profile_subtask.start()
                # Не устанавливаем total_items здесь - он будет установлен из результата импорта
            
            # Import profiles
            import_results = batch_service.import_profiles_batch(usernames, task=None)
            
            # Process results
            for username, profile_result in import_results.items():
                if profile_result["success"]:
                    result["profiles_imported"] += 1
                    
                    # If import_posts is requested, import posts for this profile
                    if import_posts and profile_result.get("profile"):
                        # Import posts using batch posts task
                        posts_result_raw = ImportService.import_batch_posts(
                            usernames=[username],
                            force_sync=True,  # Always sync within the task
                            limit=posts_limit,
                            skip_media_download=skip_media_download,
                            scraping_task_id=scraping_task_id,
                        )
                        
                        # Since force_sync=True, we know this is a dict
                        from typing import cast
                        posts_result = cast(dict[str, Any], posts_result_raw)
                        
                        if posts_result.get("status") == "success":
                            result["posts_imported"] += posts_result.get("total_posts", 0)
                        else:
                            result["errors"].extend(posts_result.get("errors", []))
                else:
                    result["profiles_failed"].append(username)
                    result["errors"].append(
                        f"{username}: {profile_result.get('error', 'Unknown error')}"
                    )
            
            # Update subtask
            if profile_subtask:
                profile_subtask.processed_items = result["profiles_imported"]
                
                if result["profiles_failed"]:
                    if result["profiles_imported"] > 0:
                        profile_subtask.complete(result_data={
                            "status": "partial_success",
                            "profiles_imported": result["profiles_imported"],
                            "profiles_failed": len(result["profiles_failed"]),
                            "items_imported": result["profiles_imported"],
                            "message": f"Imported {result['profiles_imported']} profiles, {len(result['profiles_failed'])} failed"
                        })
                    else:
                        profile_subtask.fail(f"Failed to import all {len(result['profiles_failed'])} profiles")
                else:
                    profile_subtask.complete(result_data={
                        "status": "success",
                        "profiles_imported": result["profiles_imported"],
                        "items_imported": result["profiles_imported"],
                        "message": f"Successfully imported {result['profiles_imported']} profiles"
                    })
            
            # Update main task
            if main_task:
                main_task.update_subtask_counters()
                main_task.items_scraped = int(result["profiles_imported"])
                
                if main_task.subtasks_failed > 0:
                    main_task.status = "partial_success" if main_task.subtasks_completed > 0 else "failed"
                else:
                    main_task.status = "completed"
                
                main_task.completed_at = timezone.now()
                main_task.save()
            
            # Step 4: Final update
            self.update_progress(100, "Batch profile import completed")
            
            # Set final status
            if result["profiles_failed"]:
                result["status"] = "partial_success"
            
            return result
            
        except Exception as e:
            error_msg = f"Unexpected error during batch profile import: {e!s}"
            logger.exception(error_msg)
            result["status"] = "failed"
            result["errors"].append(error_msg)
            return result
