"""
Executor for main tasks with subtasks.

This module handles the execution of main tasks that contain multiple subtasks,
ensuring proper coordination and status tracking.
"""

from celery import shared_task
from django.utils import timezone

from core.logging import ContextLogger

logger = ContextLogger(__name__)


@shared_task(name="instagram.execute_main_task")
def execute_main_task(task_id: int):
    """
    Execute a main task with all its subtasks.
    
    This task coordinates the execution of all subtasks belonging to a main task,
    tracking progress and updating status accordingly.
    
    Args:
        task_id: ID of the main InstagramScrapingTask
        
    Returns:
        dict: Execution result with statistics
    """
    from instagram_manager.models import InstagramScrapingTask
    
    try:
        main_task = InstagramScrapingTask.objects.get(pk=task_id)
    except InstagramScrapingTask.DoesNotExist:
        logger.error(f"Main task with ID {task_id} not found")
        return {"error": f"Task {task_id} not found"}
    
    logger.info(
        f"Starting execution of main task {task_id}",
        extra={
            "task_id": task_id,
            "task_type": main_task.task_type,
            "subtasks_count": main_task.subtasks_count
        }
    )
    
    # Обновляем статус главной задачи
    main_task.status = "running"
    main_task.started_at = timezone.now()
    main_task.save(update_fields=["status", "started_at"])
    
    try:
        # Получаем все подзадачи
        subtasks = main_task.subtasks_new.filter(status="pending").order_by("created_at")
        
        if not subtasks.exists():
            logger.warning(f"No pending subtasks found for task {task_id}")
            main_task.status = "completed"
            main_task.completed_at = timezone.now()
            main_task.save(update_fields=["status", "completed_at"])
            return {"status": "completed", "subtasks_processed": 0}
        
        total_items_imported = 0
        
        # Выполняем каждую подзадачу
        for subtask in subtasks:
            try:
                logger.info(
                    f"Executing subtask {subtask.id}",
                    extra={
                        "subtask_id": subtask.id,
                        "subtask_type": subtask.subtask_type,
                        "target": subtask.target_identifier
                    }
                )
                
                # Отмечаем начало выполнения
                subtask.start()
                
                # Выполняем подзадачу в зависимости от типа
                if subtask.subtask_type == "import_posts":
                    result = _execute_posts_import(subtask)
                elif subtask.subtask_type == "import_profile":
                    result = _execute_profile_import(subtask)
                elif subtask.subtask_type == "import_comments":
                    result = _execute_comments_import(subtask)
                else:
                    raise ValueError(f"Unknown subtask type: {subtask.subtask_type}")
                
                # Обновляем результаты подзадачи
                if result.get("success"):
                    # Получаем количество импортированных элементов
                    items_imported = result.get("items_imported", 0)
                    
                    # Сохраняем информацию об источниках в result_data для отображения в админке
                    if subtask.subtask_type == "import_comments":
                        posts_processed = result.get("posts_processed", 0)
                        result["sources_info"] = f"{posts_processed} posts"
                    elif subtask.subtask_type == "import_posts":
                        # Получаем количество профилей из параметров выполнения
                        usernames = subtask.execution_params.get("usernames", [])
                        if isinstance(usernames, list):
                            result["sources_info"] = f"{len(usernames)} profile{'s' if len(usernames) != 1 else ''}"
                    elif subtask.subtask_type == "import_profile":
                        # Для импорта профилей показываем количество профилей
                        total_profiles = result.get("total_profiles", 1)
                        result["sources_info"] = f"{total_profiles} profile{'s' if total_profiles != 1 else ''}"
                    
                    # Вызываем complete() с передачей всех необходимых данных
                    subtask.complete(
                        result_data=result,
                        processed_items=items_imported,
                        total_items=items_imported
                    )
                    
                    # Накапливаем общее количество
                    total_items_imported += subtask.processed_items
                else:
                    subtask.fail(result.get("error", "Unknown error"))
                
            except Exception as e:
                logger.error(
                    f"Failed to execute subtask {subtask.id}",
                    extra={"subtask_id": subtask.id, "error": str(e)},
                    exc_info=True
                )
                subtask.fail(str(e))
        
        # Обновляем счетчики главной задачи
        main_task.update_subtask_counters()
        main_task.items_scraped = total_items_imported
        
        # Определяем финальный статус
        if main_task.subtasks_failed > 0:
            if main_task.subtasks_completed > 0:
                main_task.status = "partial_success"
            else:
                main_task.status = "failed"
        else:
            main_task.status = "completed"
        
        main_task.completed_at = timezone.now()
        main_task.save(update_fields=["status", "completed_at", "items_scraped"])
        
        logger.info(
            f"Main task {task_id} completed",
            extra={
                "task_id": task_id,
                "status": main_task.status,
                "subtasks_completed": main_task.subtasks_completed,
                "subtasks_failed": main_task.subtasks_failed,
                "items_imported": total_items_imported
            }
        )
        
        return {
            "status": main_task.status,
            "subtasks_completed": main_task.subtasks_completed,
            "subtasks_failed": main_task.subtasks_failed,
            "items_imported": total_items_imported
        }
        
    except Exception as e:
        logger.error(
            f"Failed to execute main task {task_id}",
            extra={"task_id": task_id, "error": str(e)},
            exc_info=True
        )
        
        main_task.status = "failed"
        main_task.error_message = str(e)
        main_task.completed_at = timezone.now()
        main_task.save(update_fields=["status", "error_message", "completed_at"])
        
        return {
            "status": "failed",
            "error": str(e)
        }


def _execute_posts_import(subtask):
    """Execute posts import for a specific profile."""
    from instagram_manager.models import InstagramProfile
    from instagram_manager.services import PostService
    
    try:
        # Получаем профиль
        profile = InstagramProfile.objects.get(username=subtask.target_identifier)
        
        # Извлекаем параметры
        params = subtask.execution_params or {}
        
        # Создаем сервис и импортируем посты
        service = PostService()
        posts = service.import_posts(
            profile=profile,
            limit=params.get("limit"),
            start_date=params.get("start_date"),
            end_date=params.get("end_date"),
            post_types=params.get("post_types", []),
            skip_media_download=params.get("skip_media_download", False),
        )
        
        return {
            "success": True,
            "items_imported": len(posts),
            "profile_id": profile.id
        }
        
    except Exception as e:
        return {
            "success": False,
            "error": str(e)
        }


def _execute_profile_import(subtask):
    """Execute profile import."""
    from instagram_manager.instagram_api import BrightDataClient
    from instagram_manager.services.batch_profile_service import BatchProfileService
    
    try:
        # Извлекаем параметры
        params = subtask.execution_params or {}
        usernames = params.get("usernames", [])
        missing_usernames = params.get("missing_usernames", [])
        existing_usernames = params.get("existing_usernames", [])
        
        # Если usernames не указаны, используем target_identifier
        if not usernames:
            usernames = [subtask.target_identifier]
        
        client = BrightDataClient()
        service = BatchProfileService(client)
        
        # Результаты
        created_count = 0
        updated_count = 0
        failed_count = 0
        
        # Импортируем только отсутствующие профили
        if missing_usernames:
            results = service.import_profiles_batch(missing_usernames)
            
            # Подсчитываем результаты
            for username, result in results.items():
                if result.get("success"):
                    created_count += 1
                else:
                    failed_count += 1
        
        # Считаем существующие профили как успешно "обновленные"
        # (даже если мы их фактически не обновляли)
        updated_count = len(existing_usernames)
        
        # Общее количество успешно обработанных профилей
        successful_count = created_count + updated_count
        
        return {
            "success": successful_count > 0,
            "items_imported": successful_count,
            "total_profiles": len(usernames),
            "created": created_count,
            "updated": updated_count,
            "failed": failed_count
        }
        
    except Exception as e:
        return {
            "success": False,
            "error": str(e)
        }


def _execute_comments_import(subtask):
    """Execute comments import for posts."""
    from instagram_manager.instagram_api import BrightDataClient
    from instagram_manager.models import InstagramPost
    from instagram_manager.services import CommentService
    from instagram_manager.services.batch_comment_service import BatchCommentService
    
    try:
        # Извлекаем параметры
        params = subtask.execution_params or {}
        post_urls = params.get("post_urls", [])
        post_ids = params.get("post_ids", [])
        
        logger.info(
            f"[COMMENTS EXECUTOR] _execute_comments_import called with: "
            f"post_urls={len(post_urls) if post_urls else 0}, "
            f"post_ids={len(post_ids) if post_ids else 0}, "
            f"params={list(params.keys())}"
        )
        
        # Добавляем детальное логирование URL
        if post_urls:
            logger.info(f"[COMMENTS EXECUTOR] First 3 post URLs: {post_urls[:3]}")
            logger.info(f"[COMMENTS EXECUTOR] Total URLs to process: {len(post_urls)}")
        
        # Если есть post_urls, используем батч-импорт
        if post_urls:
            logger.info(f"[COMMENTS EXECUTOR] Using BatchCommentService for {len(post_urls)} posts")
            
            # Используем BatchCommentService для батч-импорта
            client = BrightDataClient()
            service = BatchCommentService(client)
            
            results = service.import_comments_batch(
                post_urls=post_urls,
                limit_per_post=params.get("limit_per_post", 100),
                include_replies=params.get("include_replies", True),
                min_likes=params.get("min_likes"),
                update_existing=params.get("update_existing", False),
                task=subtask.parent_task,
            )
            
            # Подсчитываем результаты
            total_comments = 0
            successful_posts = 0
            failed_posts = 0
            missing_posts = 0
            
            for post_url, result in results.items():
                if post_url == "_statistics":
                    # Пропускаем статистику
                    continue
                if isinstance(result, dict):
                    if result.get("success"):
                        total_comments += result.get("comment_count", 0)
                        successful_posts += 1
                    else:
                        failed_posts += 1
                        error = result.get("error", "")
                        if "not found in database" in error:
                            missing_posts += 1
                        logger.warning(
                            f"[COMMENTS EXECUTOR] Failed to import comments for {post_url}: {error}"
                        )
            
            # Извлекаем статистику если есть
            statistics = results.get("_statistics", {})
            logger.info(
                f"[COMMENTS EXECUTOR] Batch import completed: "
                f"{total_comments} comments from {successful_posts} posts, "
                f"{failed_posts} posts failed, {missing_posts} posts missing in DB"
            )
            if statistics:
                logger.info(
                    f"[COMMENTS EXECUTOR] Statistics: "
                    f"total_received={statistics.get('total_comments_received', 0)}, "
                    f"posts_found={statistics.get('posts_found', 0)}, "
                    f"posts_missing={statistics.get('posts_missing', 0)}"
                )
            
            # Определяем успешность операции
            success = successful_posts > 0 or (failed_posts > 0 and missing_posts == failed_posts)
            
            return {
                "success": success,
                "items_imported": total_comments,
                "posts_processed": successful_posts,
                "posts_failed": failed_posts,
                "posts_missing": missing_posts
            }
        
        # Fallback на старый подход если нет post_urls
        elif post_ids or subtask.target_identifier:
            logger.warning(
                "[COMMENTS EXECUTOR] Using old CommentService approach (individual requests per post)"
            )
            
            if not post_ids:
                # Если не указаны ID постов, пытаемся найти по shortcode
                post = InstagramPost.objects.get(shortcode=subtask.target_identifier)
                post_ids = [post.id]
            
            # Создаем сервис и импортируем комментарии
            comment_service = CommentService()
            total_comments = 0
            
            for post_id in post_ids:
                post = InstagramPost.objects.get(id=post_id)
                comments = comment_service.import_comments_for_post(
                    post=post,
                    limit=params.get("limit", 100)
                )
                total_comments += len(comments)
            
            return {
                "success": True,
                "items_imported": total_comments
            }
        else:
            logger.error("[COMMENTS EXECUTOR] No post URLs or IDs provided")
            return {
                "success": False,
                "error": "No post URLs or IDs provided"
            }
        
    except Exception as e:
        logger.error(f"[COMMENTS EXECUTOR] Error: {e}", exc_info=True)
        return {
            "success": False,
            "error": str(e)
        }