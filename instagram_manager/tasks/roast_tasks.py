"""
Roast tasks for Instagram profile analysis.

This task handles analyzing Instagram profiles by:
1. Fetching profile and posts data
2. Downloading media (photos only) to Google Cloud Storage
3. Returning profile URL and array of GCS photo URLs
"""

import logging
from typing import Any, ClassVar

from django.db import transaction
from django.utils import timezone

from core.tasks.base import BaseTask
from core.tasks.decorators import task
from core.tasks.exceptions import TaskExecutionError, TaskValidationError

# Use standard Python logging instead of ContextLogger for Celery compatibility
logger = logging.getLogger(__name__)


@task(monitor=True)
class RoastProfileTask(BaseTask):
    """
    Task for analyzing Instagram profile and uploading photos to GCS.
    
    This task:
    1. Checks if profile exists in DB, imports from BrightData if needed
    2. Fetches required number of posts with photos
    3. Downloads photos and uploads to Google Cloud Storage
    4. Updates posts with GCS URLs
    5. Returns profile URL and array of photo URLs
    """
    
    task_type: ClassVar[str] = "instagram.roast_profile"
    description: ClassVar[str] = "Analyze Instagram profile and upload photos to GCS"
    max_retries: ClassVar[int] = 3
    
    def validate_params(self, **kwargs) -> dict[str, Any]:
        """Validate task parameters."""
        username = kwargs.get("username", "").strip().lower()
        if not username:
            raise TaskValidationError(
                self.task_type, {"username": "Username is required"}
            )
        
        # Remove @ if present
        if username.startswith("@"):
            username = username[1:]
            
        post_count = kwargs.get("post_count", 10)
        if not isinstance(post_count, int) or post_count < 1 or post_count > 100:
            raise TaskValidationError(
                self.task_type, {"post_count": "Post count must be between 1 and 100"}
            )
            
        return {
            "username": username,
            "post_count": post_count,
            "profile_url": kwargs.get("profile_url"),  # Optional, can be derived
        }
    
    def execute_task(
        self,
        username: str,
        post_count: int,
        profile_url: str | None = None,
        **kwargs
    ) -> dict[str, Any]:
        """Execute the roast profile analysis task."""
        logger.info(
            f"Starting roast analysis for @{username} with {post_count} posts"
        )
        
        # Create InstagramScrapingTask record
        from instagram_manager.models import InstagramScrapingTask
        scraping_task = InstagramScrapingTask.objects.create(
            task_type="roast",
            target_identifier=username,
            status="running",
            started_at=self.task_result.started_at,
            brightdata_dataset_id="",  # Not using BrightData directly for roast
            total_items=post_count,
            celery_task_id=self.task_id,
            error_message=""
        )
        logger.info(f"Created InstagramScrapingTask {scraping_task.id} for roast analysis")
        
        try:
            from instagram_manager.services.roast_service import RoastService
        except ImportError:
            # RoastService not implemented yet, create inline for now
            logger.warning("RoastService not found, using inline implementation")
            return self._execute_inline(username, post_count, profile_url, scraping_task)
            
        # Use RoastService when available
        service = RoastService()
        
        # Process profile with progress callback
        def progress_callback(current: int, total: int, message: str):
            progress_pct = int((current / total) * 100) if total > 0 else 0
            self.update_progress(progress_pct, message)
            self.task_result.processed_items = current
            self.task_result.total_items = total
            self.task_result.save(update_fields=["processed_items", "total_items"])
        
        try:
            result = service.process_profile(
                username=username,
                post_count=post_count,
                profile_url=profile_url,
                progress_callback=progress_callback
            )
            
            logger.info(
                f"Roast analysis completed for @{username}: "
                f"{result['statistics']['photos_uploaded']} photos uploaded"
            )
            
            # Update scraping task with results
            scraping_task.status = "completed"
            scraping_task.completed_at = timezone.now()
            scraping_task.items_scraped = result.get('statistics', {}).get('total_posts_analyzed', 0)
            scraping_task.total_items_received = result.get('statistics', {}).get('photos_found', 0)
            scraping_task.result_data = result
            scraping_task.save()
            
            return result
            
        except Exception as e:
            error_msg = f"Failed to process profile @{username}: {e!s}"
            logger.exception(error_msg)
            
            # Update scraping task with error
            scraping_task.status = "failed"
            scraping_task.error_message = error_msg
            scraping_task.save()
            
            raise TaskExecutionError(self.task_id, error_msg, e)
    
    def _execute_inline(
        self,
        username: str,
        post_count: int,
        profile_url: str | None = None,
        scraping_task = None
    ) -> dict[str, Any]:
        """Inline implementation until RoastService is ready."""
        from instagram_manager.instagram_api import BrightDataClient
        from instagram_manager.instagram_api.data_handlers.media_handler import MediaHandler
        from instagram_manager.repositories import PostRepository, ProfileRepository
        from instagram_manager.services.profile_service import ProfileService
        from instagram_manager.services.batch_post_service import BatchPostService
        
        # Initialize components
        profile_repo = ProfileRepository()
        post_repo = PostRepository()
        media_handler = MediaHandler(save_to_gcs=True, skip_local_save=True)
        
        # Step 1: Check/import profile
        self.update_progress(10, f"Checking profile @{username}")
        
        profile = profile_repo.get_by_username(username)
        if not profile:
            # Import profile
            self.update_progress(20, f"Importing profile @{username}")
            profile_service = ProfileService()
            profile = profile_service.import_profile(username)
            
        # Derive profile URL if not provided
        if not profile_url:
            profile_url = f"https://www.instagram.com/{username}/"
            
        # Step 2: Get posts with photos
        self.update_progress(30, f"Fetching posts for @{username}")
        
        # First, get total posts count for the profile
        total_posts_count = post_repo.filter(profile=profile).count()
        logger.info(f"Profile @{username} has {total_posts_count} total posts in DB")
        
        # Get posts with photos
        posts_with_photos = post_repo.filter(
            profile=profile,
            media__media_type="photo"
        ).distinct().order_by("-posted_at")
        
        # Count posts with photos
        photos_count = posts_with_photos.count()
        logger.info(f"Profile @{username} has {photos_count} posts with photos")
        
        # Get required posts (limited by post_count)
        posts = posts_with_photos[:post_count]
        existing_count = posts.count()
        
        # Check if we need more posts
        # Only import if we don't have enough posts with photos AND total posts is less than requested
        need_more_posts = existing_count < post_count and total_posts_count < post_count
        
        if need_more_posts:
            # Import more posts
            self.update_progress(
                40, 
                f"Importing more posts (have {existing_count} photos out of {total_posts_count} total posts, need {post_count})"
            )
            
            client = BrightDataClient()
            batch_service = BatchPostService(client)
            
            # Import posts
            batch_service.import_posts_batch(
                profiles=[profile],
                limit=post_count,
                skip_media_download=False,
                save_media_to_gcs=True
            )
            
            # Re-fetch posts with photos after import
            posts = post_repo.filter(
                profile=profile,
                media__media_type="photo"
            ).distinct().order_by("-posted_at")[:post_count]
            
            # Log the new counts
            new_total_count = post_repo.filter(profile=profile).count()
            new_photos_count = posts.count()
            logger.info(
                f"After import: Profile @{username} has {new_total_count} total posts, "
                f"{new_photos_count} with photos"
            )
        else:
            # Log why we're not importing
            if existing_count >= post_count:
                logger.info(f"Not importing: already have {existing_count} posts with photos (need {post_count})")
            else:
                logger.info(
                    f"Not importing: have {existing_count} photos out of {total_posts_count} total posts. "
                    f"Total posts >= requested ({total_posts_count} >= {post_count}), likely posts are videos."
                )
        
        # Step 3: Upload photos to GCS
        self.update_progress(50, "Uploading photos to GCS")
        
        media_urls = []
        posts_list = list(posts)
        total_posts = len(posts_list)
        
        with transaction.atomic():
            for idx, post in enumerate(posts_list):
                self.update_progress(
                    50 + int((idx / total_posts) * 40),
                    f"Processing post {idx + 1}/{total_posts}"
                )
                
                # Get photos for this post
                photos = post.media.filter(media_type="photo")
                
                for photo in photos:
                    if not photo.gcs_url:
                        # Download and upload to GCS
                        success = media_handler.download_media(photo)
                        if success and photo.gcs_url:
                            media_urls.append(photo.gcs_url)
                            logger.info(f"Uploaded photo to GCS: {photo.gcs_url}")
                    elif photo.gcs_url:
                        # Already has GCS URL
                        media_urls.append(photo.gcs_url)
                        
        # Step 4: Prepare result
        self.update_progress(100, "Analysis complete")
        
        result = {
            "profile_url": profile_url,
            "username": username,
            "post_count": total_posts,
            "media_urls": media_urls,
            "statistics": {
                "total_posts_analyzed": total_posts,
                "photos_found": len(media_urls),
                "photos_uploaded": len([url for url in media_urls if url])
            }
        }
        
        # Update scraping task if provided
        if scraping_task:
            scraping_task.status = "completed"
            scraping_task.completed_at = timezone.now()
            scraping_task.items_scraped = result.get('statistics', {}).get('total_posts_analyzed', 0)
            scraping_task.total_items_received = result.get('statistics', {}).get('photos_found', 0)
            scraping_task.result_data = result
            scraping_task.save()
        
        return result