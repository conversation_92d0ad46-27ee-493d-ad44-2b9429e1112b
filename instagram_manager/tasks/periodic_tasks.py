"""
Periodic tasks for Instagram data imports.

These tasks are designed to be run by Celery Beat scheduler
for recurring imports of Instagram data.
"""

from datetime import timedelta

from celery import shared_task
from django.utils import timezone

from core.logging import ContextLogger

logger = ContextLogger(__name__)


@shared_task(name="instagram.execute_periodic_import")
def execute_periodic_import(task_id: int):
    """
    Execute a periodic import for an existing InstagramScrapingTask.
    
    This task:
    1. Updates the task's run counters
    2. Determines the date range based on last run
    3. Executes the import using saved parameters
    4. Updates statistics and schedules next run
    
    Args:
        task_id: ID of the InstagramScrapingTask to execute
        
    Returns:
        dict: Result of the import operation
    """
    from instagram_manager.models import InstagramScrapingTask
    from instagram_manager.services import ImportService
    
    try:
        task = InstagramScrapingTask.objects.get(pk=task_id)
    except InstagramScrapingTask.DoesNotExist:
        logger.error(f"Periodic task with ID {task_id} not found")
        return {"error": f"Task {task_id} not found"}
    
    if not task.is_periodic:
        logger.warning(f"Task {task_id} is not marked as periodic")
        return {"error": "Task is not periodic"}
    
    logger.info(
        f"Starting periodic import for task {task_id}",
        extra={
            "task_id": task_id,
            "run_count": task.periodic_runs_count + 1,
            "last_run": task.last_periodic_run
        }
    )
    
    # Увеличиваем счетчик запусков
    task.periodic_runs_count += 1
    task.status = "running"
    task.started_at = timezone.now()
    task.save(update_fields=["periodic_runs_count", "status", "started_at"])
    
    try:
        # Получаем параметры из batch_identifiers и periodic_parameters
        usernames = task.batch_identifiers or []
        params = dict(task.periodic_parameters or {})
        
        # Логируем начальное состояние
        logger.info(
            f"[PERIODIC DEBUG] Starting periodic task {task_id}",
            extra={
                "task_id": task_id,
                "initial_start_date": task.initial_start_date.isoformat() if task.initial_start_date else None,
                "last_periodic_run": task.last_periodic_run.isoformat() if task.last_periodic_run else None,
                "periodic_runs_count": task.periodic_runs_count,
                "periodic_parameters": task.periodic_parameters,
            }
        )
        
        # Определяем, является ли это первым запуском
        is_first_run = task.last_periodic_run is None
        
        # Получаем информацию о том, какие даты были заданы пользователем
        has_start_date = params.get("has_start_date", False)
        has_end_date = params.get("has_end_date", False)
        
        logger.info(
            f"[PERIODIC DEBUG] is_first_run={is_first_run}, "
            f"has_start_date={has_start_date}, has_end_date={has_end_date}, "
            f"params before date setup: {params}"
        )
        
        # Определяем диапазон дат
        if is_first_run:
            # При первом запуске используем initial_start_date только если оно задано
            if task.initial_start_date:
                logger.info(
                    f"[PERIODIC DEBUG] First run WITH initial_start_date: {task.initial_start_date}"
                )
                params["start_date"] = task.initial_start_date.strftime("%Y-%m-%d")
                # end_date устанавливаем только если она была задана в форме
                if has_end_date:
                    params["end_date"] = timezone.now().date().strftime("%Y-%m-%d")
            else:
                logger.info("[PERIODIC DEBUG] First run WITHOUT initial_start_date - no dates will be set")
            # Если initial_start_date не задано, не добавляем даты вообще
        else:
            # Для последующих запусков ВСЕГДА устанавливаем start_date для инкрементального импорта
            date_from = task.last_periodic_run.date()
            params["start_date"] = date_from.strftime("%Y-%m-%d")
            logger.info(
                f"[PERIODIC DEBUG] Subsequent run - using last_periodic_run as start_date: {date_from}"
            )
            
            # end_date устанавливаем ТОЛЬКО если она была изначально задана пользователем
            if has_end_date:
                params["end_date"] = timezone.now().date().strftime("%Y-%m-%d")
                logger.info("[PERIODIC DEBUG] end_date was set by user, using today as end_date")
            else:
                logger.info("[PERIODIC DEBUG] end_date was NOT set by user, not setting it")
        
        # Удаляем служебные поля перед передачей в ImportService
        params.pop("has_start_date", None)
        params.pop("has_end_date", None)
        
        logger.info(f"[PERIODIC DEBUG] params after date setup: {params}")
        
        logger.info(
            "Executing periodic import with parameters",
            extra={
                "task_id": task_id,
                "usernames": usernames,
                "date_from": params.get("start_date", "Not set"),
                "date_to": params.get("end_date", "Not set"),
                "is_first_run": is_first_run
            }
        )
        
        # Запускаем импорт синхронно (мы уже в Celery задаче)
        ImportService.import_batch_posts(
            usernames=usernames,
            force_sync=True,  # Выполняем синхронно внутри Celery задачи
            scraping_task_id=task.id,  # Передаем ID периодической задачи
            **params
        )
        
        # Обновляем статистику при успехе
        task.status = "completed"
        task.periodic_success_count += 1
        
        # Агрегируем результаты из подзадач (используем новую модель)
        subtasks = task.subtasks_new.all()
        total_posts = 0
        for subtask in subtasks:
            if subtask.processed_items:
                total_posts += subtask.processed_items
        
        task.items_scraped = total_posts  # Последний запуск
        task.total_periodic_items += total_posts  # Общий счетчик
        
        # Сохраняем сводку результатов
        task.result = {
            "status": "success",
            "total_posts": total_posts,
            "subtasks_count": subtasks.count(),
            "subtasks_completed": subtasks.filter(status="completed").count(),
            "subtasks_failed": subtasks.filter(status="failed").count()
        }
        task.error_message = ""
        
        logger.info(
            "Periodic import completed successfully",
            extra={
                "task_id": task_id,
                "posts_imported": task.items_scraped,
                "total_items": task.total_periodic_items
            }
        )
        
    except Exception as e:
        # Обработка ошибок
        logger.error(
            "Periodic import failed",
            extra={
                "task_id": task_id,
                "error": str(e)
            },
            exc_info=True
        )
        
        task.status = "failed"
        task.periodic_fail_count += 1
        task.error_message = str(e)
        task.result = {"error": str(e)}
    
    finally:
        # Обновляем временные метки
        task.completed_at = timezone.now()
        task.last_periodic_run = timezone.now()
        task.next_periodic_run = timezone.now() + timedelta(seconds=task.interval_seconds)
        
        # Сохраняем все изменения
        task.save(update_fields=[
            "status", "periodic_success_count", "periodic_fail_count",
            "items_scraped", "total_periodic_items", "result", "error_message",
            "completed_at", "last_periodic_run", "next_periodic_run"
        ])
    
    return {
        "task_id": task.id,
        "status": task.status,
        "items_imported": task.items_scraped,
        "total_items": task.total_periodic_items,
        "next_run": task.next_periodic_run.isoformat() if task.next_periodic_run else None
    }