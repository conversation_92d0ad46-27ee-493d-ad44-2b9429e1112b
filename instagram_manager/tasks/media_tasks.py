"""
Media-related tasks for Instagram.

These tasks handle downloading, processing, and managing Instagram media files.
"""

from typing import Any, ClassVar

from core.logging import ContextLogger
from core.tasks.base import BaseTask
from core.tasks.decorators import task
from core.tasks.exceptions import TaskValidationError
from instagram_manager.models import InstagramMedia
from instagram_manager.services.media_service import MediaService

logger = ContextLogger(__name__)


@task(monitor=True)
class DownloadMediaTask(BaseTask):
    """Task for downloading Instagram media files."""
    
    task_type: ClassVar[str] = "instagram.download_media"
    description: ClassVar[str] = "Download Instagram media files"
    max_retries: ClassVar[int] = 5
    
    def __init__(self, task_id: str | None = None):
        super().__init__(task_id)
        self.media_service = MediaService()
    
    def validate_params(self, media_ids: list[int], **kwargs) -> dict[str, Any]:
        """Validate task parameters."""
        if not media_ids:
            raise TaskValidationError(self.task_type, {"media_ids": "Media IDs are required"})
        
        return {
            "media_ids": media_ids,
            "download_thumbnails": kwargs.get("download_thumbnails", True),
            "force_redownload": kwargs.get("force_redownload", False),
            "batch_size": kwargs.get("batch_size", 10),
        }
    
    def execute_task(
        self,
        media_ids: list[int],
        download_thumbnails: bool = True,
        force_redownload: bool = False,
        batch_size: int = 10,
        **kwargs
    ) -> dict[str, Any]:
        """Execute the media download task."""
        result: dict[str, Any] = {
            "status": "success",
            "total_media": len(media_ids),
            "downloaded": 0,
            "skipped": 0,
            "failed": 0,
            "errors": []
        }
        
        # Process media in batches
        for i in range(0, len(media_ids), batch_size):
            batch_ids = media_ids[i:i + batch_size]
            batch_num = (i // batch_size) + 1
            total_batches = (len(media_ids) + batch_size - 1) // batch_size
            
            self.update_progress(
                int((i / len(media_ids)) * 100),
                f"Processing batch {batch_num}/{total_batches}"
            )
            
            # Get media objects
            media_objects = InstagramMedia.objects.filter(id__in=batch_ids)
            
            for media in media_objects:
                try:
                    # Skip if already downloaded and not forcing
                    if media.is_downloaded and not force_redownload:
                        result["skipped"] += 1
                        continue
                    
                    # Download media
                    from instagram_manager.instagram_api.data_handlers.media_handler import MediaHandler
                    media_handler = MediaHandler(save_to_gcs=False)
                    success = media_handler.download_media(media)
                    
                    if success:
                        result["downloaded"] += 1
                        
                        # Download thumbnail for videos if requested
                        if download_thumbnails and media.media_type == "video":
                            media_handler.download_thumbnail(media)
                    else:
                        result["failed"] += 1
                        
                except Exception as e:
                    error_msg = f"Failed to download media {media.id}: {e!s}"
                    logger.error(error_msg, extra={"media_id": media.id, "error": str(e)})
                    result["failed"] += 1
                    result["errors"].append(error_msg)
        
        # Final status
        if result["failed"] > 0:
            result["status"] = "partial_success" if result["downloaded"] > 0 else "failed"
        
        return result


@task(monitor=True)
class ProcessMediaTask(BaseTask):
    """Task for processing Instagram media (resize, optimize, etc.)."""
    
    task_type: ClassVar[str] = "instagram.process_media"
    description: ClassVar[str] = "Process Instagram media files"
    max_retries: ClassVar[int] = 3
    
    def validate_params(self, media_ids: list[int], **kwargs) -> dict[str, Any]:
        """Validate task parameters."""
        if not media_ids:
            raise TaskValidationError(self.task_type, {"media_ids": "Media IDs are required"})
        
        valid_operations = ["resize", "optimize", "thumbnail", "watermark"]
        operations = kwargs.get("operations", ["thumbnail"])
        
        for op in operations:
            if op not in valid_operations:
                raise TaskValidationError(
                    self.task_type,
                    {"operations": f"Invalid operation '{op}'. Valid: {valid_operations}"}
                )
        
        return {
            "media_ids": media_ids,
            "operations": operations,
            "resize_width": kwargs.get("resize_width", 1080),
            "resize_height": kwargs.get("resize_height", 1080),
            "quality": kwargs.get("quality", 85),
            "watermark_text": kwargs.get("watermark_text", ""),
        }
    
    def execute_task(
        self,
        media_ids: list[int],
        operations: list[str],
        resize_width: int = 1080,
        resize_height: int = 1080,
        quality: int = 85,
        watermark_text: str = "",
        **kwargs
    ) -> dict[str, Any]:
        """Execute the media processing task."""
        # Media processing implementation structure:
        # 1. Load media using MediaRepository
        # 2. Use Pillow for image operations
        # 3. Use moviepy for video operations
        # 4. Save processed files to MEDIA_ROOT
        # 5. Update InstagramMedia records
        
        # Currently a placeholder for future implementation
        logger.info(
            "Media processing task queued",
            extra={
                "media_count": len(media_ids),
                "operations": operations,
                "quality": quality
            }
        )
        
        return {
            "status": "success",
            "total_media": len(media_ids),
            "processed": 0,
            "operations_applied": operations
        }