"""
Asynchronous tasks for Instagram Manager.

This module contains task implementations for various Instagram operations
that can be executed either synchronously or asynchronously.
"""

import logging

from celery import shared_task

from .export_tasks import ExportInstagramDataTask
from .import_tasks import (
    ImportBatchCommentsTask,
    ImportBatchPostsTask,
    ImportBatchProfilesTask,
    ImportInstagramCommentsTask,
    ImportInstagramFollowersTask,
    ImportInstagramPostsTask,
    ImportInstagramProfileTask,
)
from .main_task_executor import execute_main_task
from .media_tasks import DownloadMediaTask, ProcessMediaTask
from .periodic_tasks import execute_periodic_import
from .roast_tasks import RoastProfileTask

logger = logging.getLogger(__name__)

# Эти задачи теперь используют единую систему через BaseTask.run_async()
# Оставляем их для обратной совместимости и прямого вызова из кода

@shared_task(name="instagram.import_profile")
def import_profile_async(username, **kwargs):
    """Импорт Instagram профиля через Celery"""
    task = ImportInstagramProfileTask()
    return task.execute(username=username, **kwargs)

@shared_task(name="instagram.import_posts")
def import_posts_async(profile_id, **kwargs):
    """Импорт постов Instagram через Celery"""
    task = ImportInstagramPostsTask()
    return task.execute(profile_id=profile_id, **kwargs)

@shared_task(name="instagram.import_comments")
def import_comments_async(post_ids, **kwargs):
    """Импорт комментариев Instagram через Celery"""
    task = ImportInstagramCommentsTask()
    return task.execute(post_ids=post_ids, **kwargs)

@shared_task(name="instagram.download_media")
def download_media_async(media_id, **kwargs):
    """Загрузка медиа файлов через Celery"""
    task = DownloadMediaTask()
    return task.execute(media_id=media_id, **kwargs)

@shared_task(name="instagram.import_batch_posts")
def import_batch_posts_async(usernames, **kwargs):
    """Batch импорт постов Instagram через Celery"""
    logger.info(
        f"[ASYNC DEBUG] import_batch_posts_async called with usernames={usernames}, "
        f"scraping_task_id={kwargs.get('scraping_task_id')}, all kwargs={list(kwargs.keys())}"
    )
    task = ImportBatchPostsTask()
    return task.execute(usernames=usernames, **kwargs)

@shared_task(name="instagram.import_batch_profiles")
def import_batch_profiles_async(usernames, **kwargs):
    """Batch импорт профилей Instagram через Celery"""
    logger.info(
        f"[ASYNC DEBUG] import_batch_profiles_async called with usernames={usernames}, "
        f"scraping_task_id={kwargs.get('scraping_task_id')}, all kwargs={list(kwargs.keys())}"
    )
    task = ImportBatchProfilesTask()
    return task.execute(usernames=usernames, **kwargs)

@shared_task(name="instagram.update_active_profiles")
def update_active_profiles():
    """Обновление активных профилей (периодическая задача)"""
    from instagram_manager.models import InstagramProfile
    
    profiles = InstagramProfile.objects.filter(
        is_deleted=False,
        is_active=True
    ).order_by("-follower_count")[:50]
    
    for profile in profiles:
        import_profile_async.delay(
            username=profile.username,
            update_existing=True,
            import_posts=True,
            posts_limit=10
        )
    
    return f"Scheduled update for {profiles.count()} profiles"

@shared_task(name="instagram.import_batch_comments", time_limit=300, soft_time_limit=240)
def import_batch_comments_async(post_urls, **kwargs):
    """Batch импорт комментариев Instagram через Celery"""
    logger.info(
        f"[ASYNC DEBUG] import_batch_comments_async called with post_urls={len(post_urls)} posts, "
        f"scraping_task_id={kwargs.get('scraping_task_id')}, subtask_id={kwargs.get('subtask_id')}, "
        f"all kwargs={list(kwargs.keys())}"
    )
    task = ImportBatchCommentsTask()
    return task.execute(post_urls=post_urls, **kwargs)

__all__ = [
    "DownloadMediaTask",
    "ExportInstagramDataTask",
    "ImportBatchCommentsTask",
    "ImportBatchPostsTask",
    "ImportBatchProfilesTask",
    "ImportInstagramCommentsTask",
    "ImportInstagramFollowersTask",
    "ImportInstagramPostsTask",
    "ImportInstagramProfileTask",
    "ProcessMediaTask",
    "RoastProfileTask",
    # Celery tasks
    "download_media_async",
    "execute_main_task",
    "execute_periodic_import",
    "import_batch_comments_async",
    "import_batch_posts_async",
    "import_batch_profiles_async",
    "import_comments_async",
    "import_posts_async",
    "import_profile_async",
    "update_active_profiles",
]