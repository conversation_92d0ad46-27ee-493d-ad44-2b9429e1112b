# Django Environment Configuration
# 
# This project uses environment-specific configuration files:
# - .env.dev - Development environment settings
# - .env.test - Test environment settings  
# - .env.prod - Production environment settings
#
# Copy the appropriate file to .env based on your environment:
# cp .env.dev .env    # For development
# cp .env.test .env   # For testing
# cp .env.prod .env   # For production
#
# Then update the values with your actual configuration.

# Django Settings
SECRET_KEY=your-secret-key-here-generate-new-one-for-production
DEBUG=False
ALLOWED_HOSTS=localhost,127.0.0.1,yourdomain.com

# Environment: development, testing, production
DJANGO_ENV=development

# Database Configuration
# For local development with Docker Compose:
POSTGRES_DB=socialmanager
POSTGRES_USER=postgres
POSTGRES_PASSWORD=postgres
POSTGRES_HOST=localhost  # Use 'db' for Docker containers
POSTGRES_PORT=5432

# For production, use strong password:
DB_PASSWORD=secure-password-here
DATABASE_URL=************************************************/socialmanager

# Redis Configuration
# For development:
REDIS_URL=redis://localhost:6379/0

# For production with password:
REDIS_PASSWORD=redis-password-here
# REDIS_URL=redis://:redis-password-here@redis:6379/0

# Celery Configuration
CELERY_BROKER_URL=redis://localhost:6379/1
CELERY_RESULT_BACKEND=redis://localhost:6379/2
CELERY_TASK_ALWAYS_EAGER=False  # Set to True for synchronous execution in tests

# Instagram/BrightData
BRIGHTDATA_API_KEY=your-api-key
BRIGHTDATA_DATASET_ID=your-dataset-id
BRIGHTDATA_TIMEOUT=120
BRIGHTDATA_API_TOKEN=your-brightdata-api-token
BRIGHTDATA_BROWSER_URL=wss://your-brightdata-browser-url
FIELD_ENCRYPTION_KEY=your-32-character-encryption-key

# Telegram
TELEGRAM_API_ID=your-api-id
TELEGRAM_API_HASH=your-api-hash
SESSION_NAME=telegram_manager
OUTPUT_DIR=/app/telegram_exports

# Media Storage
MEDIA_ROOT=/app/media
STATIC_ROOT=/app/static

# Google Cloud Storage (Optional)
USE_GCS=False
GCS_BUCKET_NAME=your-bucket
GOOGLE_APPLICATION_CREDENTIALS=/app/credentials/gcs-key.json

# Instagram Rate Limits
INSTAGRAM_RATE_LIMITS_ENABLED=false
INSTAGRAM_COMMENTS_PER_HOUR=60
INSTAGRAM_COMMENTS_PER_DAY=200
INSTAGRAM_COMMENT_DELAY=30

# Instagram Test Account
INSTAGRAM_TEST_USERNAME=testuser

# Majila Integration (optional)
# MAJILA_BACKWARDS_URL=https://your-majila-url

# Development Options
ENABLE_DEBUG_TOOLBAR=false
SHOW_SQL_QUERIES=false

# Sentry Error Tracking (Optional)
# SENTRY_DSN=your-sentry-dsn

# MCP Server Settings
MCP_SERVER_PORT=8001
MCP_SERVER_HOST=0.0.0.0