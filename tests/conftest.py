"""
Pytest configuration and fixtures for MCP server tests.
Enhanced with MCP-specific fixtures and async support.
"""

import asyncio
import logging
import os
import urllib.error
import urllib.request
from contextlib import ExitStack
from datetime import datetime
from unittest import mock
from unittest.mock import Mock, patch

import django
import pytest
import pytest_asyncio
from asgiref.sync import sync_to_async

# Set testing environment variable
os.environ["TESTING"] = "1"
os.environ["DJANGO_SETTINGS_MODULE"] = "tests.settings"

# Setup Django before importing models
django.setup()

from django.contrib.auth import get_user_model  # noqa: E402

# Disable all logging during tests
logging.disable(logging.CRITICAL)

# Disable structlog completely during tests
import structlog  # noqa: E402

structlog.configure(
    processors=[],
    context_class=dict,
    logger_factory=structlog.ReturnLoggerFactory(),
    cache_logger_on_first_use=False,
)

User = get_user_model()

# ===== Event Loop Configuration =====

@pytest.fixture(scope="session")
def event_loop():
    """Create event loop for async tests."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()

# ===== Existing Fixtures (kept intact) =====

@pytest.fixture(autouse=True)
def disable_structlog():
    """Disable structlog for all tests."""
    with mock.patch("structlog.get_logger") as mock_logger:
        mock_logger.return_value = mock.Mock()
        yield

@pytest.fixture(autouse=True)
def no_print(monkeypatch):
    """Disable print statements during tests."""
    def mock_print(*args, **kwargs):
        pass
    monkeypatch.setattr("builtins.print", mock_print)

@pytest.fixture(autouse=True)
def enable_db_access_for_all_tests(db):
    """
    Give all tests access to the database.
    This makes @pytest.mark.django_db unnecessary.
    """
    pass

@pytest.fixture
def test_user(django_user_model):
    """Create a test user."""
    return django_user_model.objects.create_user(
        username="testuser",
        email="<EMAIL>",
        password="testpass123"
    )

@pytest.fixture
def admin_user(django_user_model):
    """Create an admin user."""
    return django_user_model.objects.create_superuser(
        username="admin",
        email="<EMAIL>",
        password="adminpass123"
    )

@pytest.fixture
def captured_logs():
    """Capture logs for debugging specific tests."""
    import io
    
    log_capture_string = io.StringIO()
    ch = logging.StreamHandler(log_capture_string)
    ch.setLevel(logging.DEBUG)
    
    # Get all loggers
    loggers = [
        logging.getLogger("instagram_manager"),
        logging.getLogger("telegram_manager"),
        logging.getLogger("core"),
        logging.getLogger("django"),
        logging.getLogger("mcp_server"),
    ]
    
    # Add handler to all loggers
    for logger in loggers:
        logger.addHandler(ch)
        logger.setLevel(logging.DEBUG)
    
    yield log_capture_string
    
    # Remove handler
    for logger in loggers:
        logger.removeHandler(ch)

# ===== MCP-Specific Database Fixtures =====

@pytest_asyncio.fixture
async def async_db(db):
    """Database fixture for async tests."""
    # The db fixture already handles transaction management
    # We just need to ensure async context is available
    yield db

@pytest_asyncio.fixture
async def async_user(async_db):
    """Create test user for async tests."""
    return await sync_to_async(User.objects.create_user)(
        username="asynctestuser",
        email="<EMAIL>",
        password="asynctestpass123"
    )

# ===== MCP Server Fixtures =====

def is_mcp_server_running():
    """Check if MCP server is running"""
    from django.conf import settings
    
    try:
        # Try to connect to the MCP endpoint
        # MCP server uses JSON-RPC over SSE, so any HTTP response means it's running
        mcp_url = getattr(settings, "MCP_URL", "http://localhost:8001/mcp")
        req = urllib.request.Request(mcp_url)
        
        with urllib.request.urlopen(req, timeout=2):
            # Any response means server is running
            return True
    except urllib.error.HTTPError as e:
        # HTTP errors (400, 406, etc) still mean server is running
        if e.code in [400, 406]:
            return True
        else:
            return False
    except (urllib.error.URLError, OSError):
        return False

@pytest.fixture
def mcp_client():
    """MCP test client fixture."""
    # Import here to avoid circular imports
    from mcp_server.main import mcp
    
    # Create a mock client that simulates MCP client behavior
    class MockMCPClient:
        def __init__(self, server):
            self.server = server
            
        async def call_tool(self, tool_name: str, arguments: dict):
            """Call a tool on the MCP server."""
            # Dynamically import and call the tool function
            try:
                # Import all tool modules to ensure functions are available
                from mcp_server.tools import (
                    health,
                    instagram_comments,
                    instagram_media,
                    instagram_posts,
                    instagram_profiles,
                    task_aggregation,
                    task_management,
                    task_results,
                )
                
                # Map tool names to modules
                tool_modules = {
                    "task_create_import": task_management,
                    "task_get_status": task_management,
                    "task_list": task_management,
                    "task_cancel": task_management,
                    "task_retry": task_management,
                    "task_get_queue_stats": task_management,
                    "task_get_result": task_results,
                    "task_get_batch_results": task_results,
                    "task_export_results": task_results,
                    "task_search_results": task_results,
                    "task_aggregate_results": task_aggregation,
                    "instagram_get_profile": instagram_profiles,
                    "instagram_list_profiles": instagram_profiles,
                    "instagram_search_profiles": instagram_profiles,
                    "instagram_get_profile_stats": instagram_profiles,
                    "instagram_bulk_check_profiles": instagram_profiles,
                    "instagram_get_posts": instagram_posts,
                    "instagram_get_post_details": instagram_posts,
                    "instagram_search_posts": instagram_posts,
                    "instagram_get_trending_posts": instagram_posts,
                    "instagram_download_media": instagram_media,
                    "instagram_bulk_download_media": instagram_media,
                    "instagram_get_post_media": instagram_media,
                    "instagram_get_media_analytics": instagram_media,
                    "instagram_get_comments": instagram_comments,
                    "instagram_post_comment": instagram_comments,
                    "instagram_bulk_post_comments": instagram_comments,
                    "instagram_search_comments": instagram_comments,
                    "instagram_get_comment_stats": instagram_comments,
                    "health_check": health,
                }
                
                # Get the module for this tool
                module = tool_modules.get(tool_name)
                if not module:
                    raise ValueError(f"Tool {tool_name} not found")
                
                # Get the function from the module
                tool_func = getattr(module, tool_name, None)
                if not tool_func:
                    raise ValueError(f"Tool {tool_name} not found in module")
                
                # The function might be wrapped by @mcp.tool() decorator
                # Check if it's a FunctionTool object
                if hasattr(tool_func, "fn"):
                    # It's a FunctionTool, get the actual function and call it
                    actual_func = tool_func.fn
                    result = await actual_func(**arguments)
                elif callable(tool_func):
                    # It's a regular callable
                    result = await tool_func(**arguments)
                else:
                    raise ValueError(f"Tool {tool_name} is not callable")
                
                return result
                
            except Exception as e:
                # If it's already a ValueError about tool not found, re-raise it
                if "not found" in str(e):
                    raise
                # Otherwise, wrap it
                raise ValueError(f"Error calling tool {tool_name}: {e}")
            
        async def __aenter__(self):
            return self
            
        async def __aexit__(self, exc_type, exc_val, exc_tb):
            pass
    
    return MockMCPClient(mcp)

# ===== Mock Fixtures =====

@pytest.fixture
def mock_celery():
    """Mock Celery for testing."""
    import uuid
    with patch("celery.current_app.send_task") as mock_send:
        # Create a mock result with unique ID
        mock_result = Mock()
        mock_result.id = f"test-task-{uuid.uuid4()}"
        mock_result.state = "PENDING"
        mock_result.get = Mock(return_value={"success": True})
        
        mock_send.return_value = mock_result
        yield mock_send

@pytest.fixture(autouse=True)
def mock_brightdata():
    """Mock BrightData API for all tests automatically."""
    # List of all possible import paths for BrightDataClient
    patches = [
        "instagram_manager.instagram_api.brightdata.BrightDataClient",
        "instagram_manager.services.base_import_service.BrightDataClient",
        "instagram_manager.services.import_service.BrightDataClient",
        "instagram_manager.services.BrightDataClient",
        "instagram_manager.management.commands.base_instagram_command.BrightDataClient",
    ]
    
    with ExitStack() as stack:
        mocked_clients = []
        
        for patch_path in patches:
            try:
                mock_client = stack.enter_context(patch(patch_path))
                instance = Mock()
                
                # Mock profile data
                instance.get_profile.return_value = {
                    "username": "testuser",
                    "full_name": "Test User",
                    "bio": "Test bio",
                    "follower_count": 1000,
                    "following_count": 500,
                    "post_count": 50,
                    "is_verified": True,
                    "is_private": False,
                    "is_business": True,
                    "profile_pic_url": "https://example.com/pic.jpg",
                    "external_id": "12345678"
                }
                
                # Mock posts data
                instance.get_posts.return_value = {
                    "posts": [
                        {
                            "external_id": "post123",
                            "content": "Test post",
                            "post_type": "photo",
                            "post_url": "https://instagram.com/p/test",
                            "like_count": 100,
                            "comment_count": 10,
                            "posted_at": "2024-01-01T00:00:00Z"
                        }
                    ]
                }
                
                # Mock comments data
                instance.get_comments.return_value = {
                    "comments": []
                }
                
                # Mock any other methods that might be called
                instance.api_key = "test_api_key"
                instance.dataset_id = "test_dataset_id"
                
                mock_client.return_value = instance
                mocked_clients.append(instance)
            except Exception:
                # Skip if import path doesn't exist
                pass
        
        # If at least one mock was successful, yield the first instance
        if mocked_clients:
            yield mocked_clients[0]
        else:
            yield None

@pytest.fixture
def mock_redis():
    """Mock Redis for testing."""
    with patch("django.core.cache.cache") as mock_cache:
        storage = {}
        
        def get(key, default=None):
            return storage.get(key, default)
        
        def set(key, value, timeout=None):
            storage[key] = value
            return True
        
        def delete(key):
            return storage.pop(key, None) is not None
        
        def clear():
            storage.clear()
        
        mock_cache.get = Mock(side_effect=get)
        mock_cache.set = Mock(side_effect=set)
        mock_cache.delete = Mock(side_effect=delete)
        mock_cache.clear = Mock(side_effect=clear)
        
        yield mock_cache

# ===== Test Data Fixtures =====

@pytest.fixture
def instagram_profile_data():
    """Sample Instagram profile data."""
    return {
        "id": 1,
        "username": "testprofile",
        "full_name": "Test Profile",
        "bio": "Test bio",
        "follower_count": 10000,
        "following_count": 500,
        "post_count": 100,
        "is_verified": True,
        "is_private": False,
        "is_business": True,
        "profile_pic_url": "https://example.com/pic.jpg",
        "external_id": "123456789",
        "website": "https://testprofile.com",
        "created_at": datetime.now()
    }

@pytest.fixture
def instagram_post_data():
    """Sample Instagram post data."""
    return {
        "id": 1,
        "external_id": "123456789",
        "content": "Test post content #test #instagram",
        "post_type": "photo",
        "post_url": "https://instagram.com/p/test123",
        "like_count": 1000,
        "comment_count": 50,
        "share_count": 25,
        "view_count": 5000,
        "posted_at": "2024-01-01T00:00:00Z",
        "is_video": False,
        "video_duration": None,
        "location": "Test Location"
    }

@pytest.fixture
def task_result_data():
    """Sample task result data."""
    return {
        "task_id": "test-task-123",
        "task_type": "instagram.profile",
        "task_name": "Import Instagram Profile",
        "status": "completed",
        "progress": 100,
        "result": {
            "imported": True,
            "profile_id": 123,
            "posts_imported": 50
        },
        "created_at": datetime.now(),
        "started_at": datetime.now(),
        "completed_at": datetime.now()
    }

@pytest.fixture
def telegram_chat_data():
    """Sample Telegram chat data."""
    return {
        "id": 1,
        "chat_id": -1001234567890,
        "title": "Test Chat",
        "username": "testchat",
        "chat_type": "channel",
        "description": "Test chat description",
        "member_count": 1000,
        "is_verified": True,
        "is_restricted": False,
        "is_scam": False,
        "is_fake": False
    }

# ===== Performance Testing Fixtures =====

@pytest.fixture
def performance_monitor():
    """Performance measurement for tests."""
    import os
    import time

    import psutil
    
    class PerformanceMonitor:
        def __init__(self):
            self.measurements = {}
            self.process = psutil.Process(os.getpid())
        
        def start(self, name: str):
            self.measurements[name] = {
                "start": time.time(),
                "memory_start": self._get_memory_usage()
            }
        
        def stop(self, name: str):
            if name in self.measurements:
                self.measurements[name]["end"] = time.time()
                self.measurements[name]["memory_end"] = self._get_memory_usage()
                self.measurements[name]["duration"] = (
                    self.measurements[name]["end"] - 
                    self.measurements[name]["start"]
                )
                self.measurements[name]["memory_delta"] = (
                    self.measurements[name]["memory_end"] - 
                    self.measurements[name]["memory_start"]
                )
        
        def _get_memory_usage(self):
            return self.process.memory_info().rss / 1024 / 1024  # MB
        
        def get_report(self):
            return self.measurements
        
        def print_report(self):
            for name, data in self.measurements.items():
                if "duration" in data:
                    print(f"\n{name}:")
                    print(f"  Duration: {data['duration']:.3f}s")
                    print(f"  Memory Delta: {data['memory_delta']:.2f} MB")
    
    return PerformanceMonitor()

# ===== Cleanup and Test Settings =====

@pytest_asyncio.fixture(autouse=True)
async def cleanup_after_test():
    """Cleanup after each test."""
    yield
    # Add any cleanup logic here if needed

@pytest.fixture(autouse=True)
def test_settings():
    """Apply test settings."""
    from django.conf import settings
    
    # Store original values
    original_celery_eager = getattr(settings, "CELERY_TASK_ALWAYS_EAGER", None)
    original_debug = settings.DEBUG
    
    # Apply test settings
    settings.CELERY_TASK_ALWAYS_EAGER = True
    settings.DEBUG = True
    settings.TESTING = True
    
    yield
    
    # Restore original values
    if original_celery_eager is not None:
        settings.CELERY_TASK_ALWAYS_EAGER = original_celery_eager
    settings.DEBUG = original_debug

# ===== Pytest Configuration =====

def pytest_configure(config):
    """Configure pytest with clean output settings."""
    # Disable Django's test database creation messages
    os.environ["DJANGO_TEST_SILENT"] = "1"
    
    # Set minimal verbosity
    if hasattr(config.option, "verbose"):
        config.option.verbose = 0
    
    # Add custom markers
    config.addinivalue_line("markers", "e2e: mark test as end-to-end test")
    config.addinivalue_line("markers", "performance: mark test as performance test")

def pytest_addoption(parser):
    """Add custom command line options."""
    parser.addoption(
        "--no-cleanup",
        action="store_true",
        default=False,
        help="Skip automatic cleanup of test_media and htmlcov directories after tests"
    )

def pytest_sessionfinish(session, exitstatus):
    """Hook called after whole test run finished, right before returning the exit status."""
    # Import cleanup functions from our plugin
    from pathlib import Path

    from .pytest_cleanup_plugin import perform_cleanup
    
    # Get base directory (parent of tests directory)
    base_dir = Path(__file__).parent.parent
    
    # Check if cleanup is disabled
    skip_cleanup = session.config.getoption("--no-cleanup", default=False)
    
    # Perform cleanup
    perform_cleanup(base_dir, skip_cleanup)

# ===== Async Utilities =====

@pytest.fixture
def async_to_sync():
    """Convert async function to sync for testing."""
    return sync_to_async

@pytest_asyncio.fixture
async def create_test_data():
    """Helper to create test data in database."""
    from instagram_manager.models import InstagramPost, InstagramProfile
    from telegram_manager.models import TelegramChat
    
    async def _create_instagram_profile(**kwargs):
        defaults = {
            "username": "testprofile",
            "full_name": "Test Profile",
            "external_id": "12345",
            "follower_count": 1000,
            "following_count": 500,
            "post_count": 50
        }
        defaults.update(kwargs)
        return await sync_to_async(InstagramProfile.objects.create)(**defaults)
    
    async def _create_instagram_post(profile=None, **kwargs):
        if not profile:
            profile = await _create_instagram_profile()
        defaults = {
            "profile": profile,
            "external_id": "post123",
            "content": "Test post",
            "post_type": "photo",
            "post_url": "https://instagram.com/p/test",
            "like_count": 100,
            "comment_count": 10
        }
        defaults.update(kwargs)
        return await sync_to_async(InstagramPost.objects.create)(**defaults)
    
    async def _create_telegram_chat(**kwargs):
        defaults = {
            "chat_id": -1001234567890,
            "title": "Test Chat",
            "chat_type": "channel"
        }
        defaults.update(kwargs)
        return await sync_to_async(TelegramChat.objects.create)(**defaults)
    
    return {
        "instagram_profile": _create_instagram_profile,
        "instagram_post": _create_instagram_post,
        "telegram_chat": _create_telegram_chat
    }