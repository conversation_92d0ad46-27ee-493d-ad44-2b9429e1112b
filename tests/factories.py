import factory
from django.contrib.auth import get_user_model
from django.utils import timezone
from factory.django import DjangoModelFactory
from faker import Faker

from instagram_manager.models import (
    InstagramAccount,
    InstagramComment,
    InstagramFollower,
    InstagramHashtag,
    InstagramMedia,
    InstagramPost,
    InstagramProfile,
)
from telegram_manager.models import TelegramChat, TelegramMessage, TelegramUser

User = get_user_model()
fake = Faker()


class UserFactory(DjangoModelFactory):
    """Фабрика для создания пользователей Django"""
    
    class Meta:
        model = User
    
    username = factory.Sequence(lambda n: f"user{n}")
    email = factory.LazyAttribute(lambda obj: f"{obj.username}@example.com")
    first_name = factory.Faker("first_name")
    last_name = factory.Faker("last_name")
    is_active = True
    is_staff = False
    is_superuser = False


class InstagramProfileFactory(DjangoModelFactory):
    """Фабрика для Instagram профилей"""
    
    class Meta:
        model = InstagramProfile
    
    profile_id = factory.LazyAttribute(lambda _: str(fake.random_int(********, ********)))
    username = factory.LazyAttribute(lambda _: fake.user_name())
    full_name = factory.LazyAttribute(lambda _: fake.name())
    bio = factory.LazyAttribute(lambda _: fake.text(max_nb_chars=150))
    
    follower_count = factory.LazyAttribute(lambda _: fake.random_int(0, 100000))
    following_count = factory.LazyAttribute(lambda _: fake.random_int(0, 10000))
    post_count = factory.LazyAttribute(lambda _: fake.random_int(0, 1000))
    
    is_verified = factory.Faker("boolean", chance_of_getting_true=10)
    is_private = factory.Faker("boolean", chance_of_getting_true=20)
    is_business = factory.Faker("boolean", chance_of_getting_true=30)
    
    profile_pic_url = factory.LazyAttribute(lambda _: fake.image_url())
    profile_pic_hd_url = factory.LazyAttribute(lambda _: fake.image_url())
    external_url = factory.LazyAttribute(lambda _: fake.url() if fake.boolean(chance_of_getting_true=30) else "")
    category_name = factory.LazyAttribute(lambda _: fake.word())
    
    created_at = factory.LazyFunction(timezone.now)
    updated_at = factory.LazyFunction(timezone.now)


class InstagramPostFactory(DjangoModelFactory):
    """Фабрика для Instagram постов"""
    
    class Meta:
        model = InstagramPost
    
    profile = factory.SubFactory(InstagramProfileFactory)
    external_id = factory.LazyAttribute(lambda _: str(fake.random_int(*********, ********9)))
    shortcode = factory.LazyAttribute(lambda _: fake.lexify("?" * 11, letters="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789_-"))
    caption = factory.LazyAttribute(lambda _: fake.text(max_nb_chars=500))
    
    like_count = factory.LazyAttribute(lambda _: fake.random_int(0, 50000))
    comment_count = factory.LazyAttribute(lambda _: fake.random_int(0, 1000))
    view_count = factory.LazyAttribute(lambda obj: fake.random_int(0, 100000))
    video_play_count = factory.LazyAttribute(lambda obj: fake.random_int(0, 100000) if obj.post_type == "video" else None)
    
    post_type = factory.Iterator(["photo", "video", "carousel", "reel"])
    post_url = factory.LazyAttribute(lambda obj: f"https://www.instagram.com/p/{obj.shortcode}/")
    
    location = factory.LazyAttribute(lambda _: fake.city() if fake.boolean(chance_of_getting_true=40) else "")
    location_id = factory.LazyAttribute(lambda obj: str(fake.random_int(1000, 9999)) if obj.location else None)
    
    is_sponsored = factory.Faker("boolean", chance_of_getting_true=5)
    is_comments_disabled = factory.Faker("boolean", chance_of_getting_true=10)
    
    accessibility_caption = factory.LazyAttribute(lambda _: fake.text(max_nb_chars=200) if fake.boolean(chance_of_getting_true=50) else "")
    
    posted_at = factory.LazyFunction(timezone.now)
    created_at = factory.LazyFunction(timezone.now)
    updated_at = factory.LazyFunction(timezone.now)


class InstagramMediaFactory(DjangoModelFactory):
    """Фабрика для медиа файлов Instagram"""
    
    class Meta:
        model = InstagramMedia
    
    post = factory.SubFactory(InstagramPostFactory)
    external_id = factory.LazyAttribute(lambda _: str(fake.random_int(*********, ********9)))
    media_type = factory.Iterator(["photo", "video"])
    media_url = factory.LazyAttribute(lambda _: fake.image_url())
    thumbnail_url = factory.LazyAttribute(lambda _: fake.image_url())
    
    width = factory.LazyAttribute(lambda _: fake.random_int(640, 1920))
    height = factory.LazyAttribute(lambda _: fake.random_int(480, 1080))
    duration = factory.LazyAttribute(lambda obj: fake.random_int(5, 300) if obj.media_type == "video" else None)
    
    order_index = factory.Sequence(lambda n: n)
    is_downloaded = False
    
    created_at = factory.LazyFunction(timezone.now)


class InstagramCommentFactory(DjangoModelFactory):
    """Фабрика для комментариев Instagram"""
    
    class Meta:
        model = InstagramComment
    
    post = factory.SubFactory(InstagramPostFactory)
    external_id = factory.LazyAttribute(lambda _: str(fake.random_int(*********, ********9)))
    text = factory.LazyAttribute(lambda _: fake.text(max_nb_chars=200))
    
    author_username = factory.LazyAttribute(lambda _: fake.user_name())
    author_external_id = factory.LazyAttribute(lambda _: str(fake.random_int(********, ********)))
    author_profile_pic = factory.LazyAttribute(lambda _: fake.image_url())
    
    like_count = factory.LazyAttribute(lambda _: fake.random_int(0, 1000))
    
    author_is_verified = factory.Faker("boolean", chance_of_getting_true=5)
    is_hidden = False
    is_pinned = factory.Faker("boolean", chance_of_getting_true=5)
    
    commented_at = factory.LazyFunction(timezone.now)
    created_at = factory.LazyFunction(timezone.now)


class InstagramHashtagFactory(DjangoModelFactory):
    """Фабрика для хештегов Instagram"""
    
    class Meta:
        model = InstagramHashtag
    
    name = factory.LazyAttribute(lambda _: fake.word().lower())
    post_count = factory.LazyAttribute(lambda _: fake.random_int(100, 1000000))
    
    created_at = factory.LazyFunction(timezone.now)
    updated_at = factory.LazyFunction(timezone.now)


class InstagramFollowerFactory(DjangoModelFactory):
    """Фабрика для подписчиков Instagram"""
    
    class Meta:
        model = InstagramFollower
    
    profile = factory.SubFactory(InstagramProfileFactory)
    follower_username = factory.LazyAttribute(lambda _: fake.user_name())
    follower_id = factory.LazyAttribute(lambda _: str(fake.random_int(********, ********)))
    follower_full_name = factory.LazyAttribute(lambda _: fake.name())
    is_verified = factory.Faker("boolean", chance_of_getting_true=5)
    profile_pic_url = factory.LazyAttribute(lambda _: fake.image_url())
    
    created_at = factory.LazyFunction(timezone.now)


class InstagramAccountFactory(DjangoModelFactory):
    """Фабрика для аккаунтов Instagram для автоматизации"""
    
    class Meta:
        model = InstagramAccount
    
    username = factory.LazyAttribute(lambda _: fake.user_name())
    
    is_active = True
    is_logged_in = False
    
    session_data = factory.LazyAttribute(lambda _: {})
    cookies = factory.LazyAttribute(lambda _: {})
    
    daily_comment_count = 0
    last_comment_at = None
    last_used_at = None
    
    created_at = factory.LazyFunction(timezone.now)
    updated_at = factory.LazyFunction(timezone.now)


class TelegramChatFactory(DjangoModelFactory):
    """Фабрика для Telegram чатов"""
    
    class Meta:
        model = TelegramChat
    
    chat_id = factory.Sequence(lambda n: -100*********0 - n)
    title = factory.LazyAttribute(lambda _: fake.company())
    username = factory.LazyAttribute(lambda _: fake.user_name())
    
    participants_count = factory.LazyAttribute(lambda _: fake.random_int(10, 10000))
    verified = factory.Faker("boolean", chance_of_getting_true=10)
    scam = False
    broadcast = factory.Faker("boolean", chance_of_getting_true=30)
    megagroup = factory.Faker("boolean", chance_of_getting_true=20)


class TelegramUserFactory(DjangoModelFactory):
    """Фабрика для Telegram пользователей"""
    
    class Meta:
        model = TelegramUser
    
    user_id = factory.Sequence(lambda n: ********* + n)
    username = factory.LazyAttribute(lambda _: fake.user_name())
    first_name = factory.LazyAttribute(lambda _: fake.first_name())
    last_name = factory.LazyAttribute(lambda _: fake.last_name())
    phone = factory.LazyAttribute(lambda _: fake.phone_number())
    
    is_bot = False
    is_verified = factory.Faker("boolean", chance_of_getting_true=5)
    is_premium = factory.Faker("boolean", chance_of_getting_true=10)
    is_deleted = False


class TelegramMessageFactory(DjangoModelFactory):
    """Фабрика для Telegram сообщений"""
    
    class Meta:
        model = TelegramMessage
    
    chat = factory.SubFactory(TelegramChatFactory)
    from_user = factory.SubFactory(TelegramUserFactory)
    
    message_id = factory.Sequence(lambda n: n + 1)
    text = factory.LazyAttribute(lambda _: fake.text(max_nb_chars=1000))
    
    date = factory.LazyFunction(timezone.now)
    edit_date = None
    
    views = factory.LazyAttribute(lambda _: fake.random_int(0, 10000))
    forwards = factory.LazyAttribute(lambda _: fake.random_int(0, 100))
    
    is_forward = factory.Faker("boolean", chance_of_getting_true=20)
    is_reply = factory.Faker("boolean", chance_of_getting_true=30)
    
    media_type = factory.LazyAttribute(
        lambda obj: fake.random_element(["photo", "video", "document"]) if fake.boolean(chance_of_getting_true=25) else ""
    )