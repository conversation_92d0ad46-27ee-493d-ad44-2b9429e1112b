"""
Примеры использования улучшенных фабрик в тестах
"""
from django.test import TestCase
from tests.factories_enhanced import (
    InstagramProfileFactory, InstagramPostFactory, InstagramCommentFactory,
    TelegramChatFactory, TelegramMessageFactory, TelegramUserFactory,
    create_instagram_test_data, create_telegram_test_data
)


class TestInstagramFactoriesUsage(TestCase):
    """Примеры использования Instagram фабрик"""
    
    def test_create_popular_profile_with_traits(self):
        """Создание популярного профиля используя trait"""
        # Создаем популярный профиль одной строкой
        profile = InstagramProfileFactory(popular=True)
        
        assert profile.is_verified
        assert profile.follower_count > 100000
        assert profile.post_count > 500
    
    def test_create_profile_with_posts_and_followers(self):
        """Создание профиля с автоматическим созданием постов и подписчиков"""
        profile = InstagramProfileFactory(
            username='test_blogger',
            posts__create_posts=True,
            posts__num_posts=5,
            followers__create_followers=True,
            followers__num_followers=10
        )
        
        # Проверяем что посты созданы
        assert profile.posts.count() == 5
        
        # Проверяем что подписчики созданы
        from instagram_manager.models import InstagramFollower
        follower_count = InstagramFollower.objects.filter(profile=profile).count()
        assert follower_count == 10
    
    def test_create_viral_post_with_media_and_comments(self):
        """Создание вирусного поста с медиа и комментариями"""
        post = InstagramPostFactory(
            viral=True,  # Использует trait для популярного поста
            post_type='carousel',
            media__num_media=5,  # Создаст 5 медиа файлов
            comments__create_comments=True,
            comments__num_comments=20,  # Создаст 20 комментариев
            hashtags__create_hashtags=True,
            hashtags__num_hashtags=10  # Создаст 10 хештегов
        )
        
        assert post.like_count > 50000
        assert post.media.count() == 5
        assert post.comments.count() == 20
        assert post.hashtags.count() == 10
    
    def test_create_business_profile_with_sponsored_posts(self):
        """Создание бизнес профиля со спонсированными постами"""
        # Создаем бизнес профиль
        business = InstagramProfileFactory(business=True)
        
        # Создаем спонсированные посты
        sponsored_posts = InstagramPostFactory.create_batch(
            3,
            profile=business,
            sponsored=True  # Использует trait для спонсированных постов
        )
        
        assert business.is_business
        assert business.is_verified
        
        for post in sponsored_posts:
            assert post.is_sponsored
            assert '#ad' in post.caption
            assert '#sponsored' in post.caption
    
    def test_create_comment_thread(self):
        """Создание цепочки комментариев с ответами"""
        post = InstagramPostFactory()
        
        # Создаем главный комментарий с ответами
        main_comment = InstagramCommentFactory(
            post=post,
            popular=True,  # Использует trait для популярного комментария
            replies=5  # Автоматически создаст 5 ответов
        )
        
        assert main_comment.is_pinned
        assert main_comment.like_count > 100
        
        # Проверяем что ответы созданы
        replies = post.comments.filter(reply_to=main_comment)
        assert replies.count() == 5
    
    def test_batch_create_different_profile_types(self):
        """Создание разных типов профилей одним batch"""
        # Создаем по 2 профиля каждого типа
        profiles = [
            *InstagramProfileFactory.create_batch(2, popular=True),
            *InstagramProfileFactory.create_batch(2, business=True),
            *InstagramProfileFactory.create_batch(2, private=True),
            *InstagramProfileFactory.create_batch(2, new_profile=True)
        ]
        
        assert len(profiles) == 8
        
        # Проверяем что traits применились правильно
        popular_profiles = [p for p in profiles if p.follower_count > 100000]
        assert len(popular_profiles) == 2
        
        business_profiles = [p for p in profiles if p.is_business]
        assert len(business_profiles) == 2
        
        private_profiles = [p for p in profiles if p.is_private]
        assert len(private_profiles) == 2
    
    def test_create_complex_test_data(self):
        """Использование функции создания комплексных данных"""
        data = create_instagram_test_data()
        
        # Проверяем influencer
        assert data['influencer'].is_verified
        assert data['influencer'].posts.count() == 10
        
        # Проверяем viral posts
        assert len(data['viral_posts']) == 3
        for post in data['viral_posts']:
            assert post.like_count > 50000
            assert post.comments.count() > 0
        
        # Проверяем business
        assert data['business'].is_business
        
        # Проверяем sponsored posts
        assert len(data['sponsored_posts']) == 5
        for post in data['sponsored_posts']:
            assert post.is_sponsored


class TestTelegramFactoriesUsage(TestCase):
    """Примеры использования Telegram фабрик"""
    
    def test_create_channel_with_messages(self):
        """Создание канала с сообщениями"""
        # Создаем канал используя trait
        channel = TelegramChatFactory(
            channel=True,
            title='Tech News',
            participants_count=25000
        )
        
        # Создаем автора канала
        author = TelegramUserFactory(premium=True)
        
        # Создаем сообщения разных типов
        messages = [
            TelegramMessageFactory(chat=channel, from_user=author),
            TelegramMessageFactory(chat=channel, from_user=author, with_photo=True),
            TelegramMessageFactory(chat=channel, from_user=author, forwarded=True),
        ]
        
        assert channel.broadcast
        assert len(messages) == 3
        
        # Проверяем что у сообщений в канале есть просмотры
        for message in messages:
            if not message.is_forward:
                assert message.views is not None
    
    def test_create_discussion_thread(self):
        """Создание дискуссии в супергруппе"""
        # Создаем большую группу
        group = TelegramChatFactory(large_group=True)
        
        # Создаем участников
        members = TelegramUserFactory.create_batch(5)
        
        # Создаем основное сообщение
        main_message = TelegramMessageFactory(
            chat=group,
            from_user=members[0],
            text="What do you think about the new feature?"
        )
        
        # Создаем ответы
        replies = []
        for i in range(1, 5):
            reply = TelegramMessageFactory(
                chat=group,
                from_user=members[i],
                reply=True,
                reply_to=main_message
            )
            replies.append(reply)
        
        assert group.megagroup
        assert group.participants_count > 1000
        assert len(replies) == 4
        
        for reply in replies:
            assert reply.is_reply
            assert reply.reply_to == main_message
    
    def test_create_bot_messages(self):
        """Создание сообщений от бота"""
        chat = TelegramChatFactory()
        
        # Создаем бота используя trait
        bot = TelegramUserFactory(bot=True)
        
        # Создаем сообщения от бота
        bot_texts = [
            "Welcome to the group!",
            "Please read the rules in pinned message.",
            "Type /help for available commands."
        ]
        bot_messages = []
        for text in bot_texts:
            bot_messages.append(TelegramMessageFactory(
                chat=chat,
                from_user=bot,
                text=text
            ))
        
        assert bot.is_bot
        assert '_bot' in bot.username
        assert len(bot_messages) == 3
    
    def test_create_scam_chat_detection(self):
        """Создание скам чата для тестирования детекции"""
        # Создаем скам чат используя trait
        scam_chat = TelegramChatFactory(
            scam_chat=True,
            title="FREE CRYPTO GIVEAWAY 💰💰💰"
        )
        
        # Создаем скам сообщения
        scammer = TelegramUserFactory()
        scam_texts = [
            "Send 0.1 BTC and get 1 BTC back!",
            "Limited time offer! Act now!",
            "Already 1000 people got their profits!",
            "Don't miss this opportunity!",
            "Send to: **********************************"
        ]
        scam_messages = []
        for text in scam_texts:
            scam_messages.append(TelegramMessageFactory(
                chat=scam_chat,
                from_user=scammer,
                text=text
            ))
        
        assert scam_chat.scam
        assert not scam_chat.verified
        assert len(scam_messages) == 5
    
    def test_create_complex_telegram_data(self):
        """Использование функции создания комплексных данных"""
        data = create_telegram_test_data()
        
        # Проверяем канал
        assert data['channel'].broadcast
        assert len(data['messages']) == 20
        
        # Проверяем что некоторые сообщения с фото
        messages_with_photo = [m for m in data['messages'] if m.media_type == 'photo']
        assert len(messages_with_photo) > 0
        
        # Проверяем супергруппу
        assert data['supergroup'].megagroup
        assert data['supergroup'].verified
        
        # Проверяем дискуссию
        assert len(data['discussion']) == 30
        reply_messages = [m for m in data['discussion'] if m.is_reply]
        assert len(reply_messages) > 0
        
        # Проверяем участников
        assert len(data['members']) == 10


class TestFactoryPerformance(TestCase):
    """Тесты производительности фабрик"""
    
    def test_batch_create_performance(self):
        """Тест производительности batch создания"""
        import time
        from instagram_manager.models import InstagramPost, InstagramProfile
        
        # Clear existing data to ensure exact counts
        InstagramPost.objects.all().delete()
        InstagramProfile.objects.all().delete()
        
        # Замеряем время создания 100 профилей с постами
        start_time = time.time()
        
        profiles = InstagramProfileFactory.create_batch(
            100,
            posts__create_posts=True,
            posts__num_posts=5
        )
        
        end_time = time.time()
        duration = end_time - start_time
        
        # Проверяем что все создано
        assert len(profiles) == 100
        
        # Проверяем что это заняло разумное время (менее 10 секунд)
        assert duration < 10.0, f"Batch creation took {duration:.2f} seconds"
        
        # Проверяем что посты созданы
        from instagram_manager.models import InstagramPost
        total_posts = InstagramPost.objects.count()
        assert total_posts == 500  # 100 профилей * 5 постов
    
    def test_complex_data_creation_performance(self):
        """Тест производительности создания комплексных данных"""
        import time
        
        start_time = time.time()
        
        # Создаем комплексные данные
        instagram_data = create_instagram_test_data()
        telegram_data = create_telegram_test_data()
        
        end_time = time.time()
        duration = end_time - start_time
        
        # Проверяем что данные созданы
        assert instagram_data['influencer'] is not None
        assert telegram_data['channel'] is not None
        
        # Проверяем производительность
        assert duration < 5.0, f"Complex data creation took {duration:.2f} seconds"


if __name__ == '__main__':
    import unittest
    unittest.main()