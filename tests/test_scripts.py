"""Test wrapper for scripts to run them in test environment."""

import io
from contextlib import redirect_stdout, redirect_stderr
from unittest.mock import patch
from django.test import TestCase
from django.conf import settings
from instagram_manager.models import InstagramProfile


class ScriptsTestCase(TestCase):
    """Test case for running scripts in test environment."""

    def setUp(self):
        """Create test data for scripts."""
        # Create a test profile
        self.profile = InstagramProfile.objects.create(
            username=settings.INSTAGRAM_TEST_USERNAME, profile_id="test123", full_name="Test User"
        )

    @patch(
        "instagram_manager.instagram_api.brightdata.BrightDataClient.get_posts"
    )
    def test_scripts_run_without_api_calls(self, mock_get_posts):
        """Test that scripts can run without making actual API calls."""
        # Mock the API response
        mock_get_posts.return_value = []

        # Import and run scripts
        # Suppress output from the script
        with redirect_stdout(io.StringIO()), redirect_stderr(io.StringIO()):
            try:
                from scripts import test_auto_download

                test_auto_download.main()
            except SystemExit:
                pass  # Scripts may call exit()

        # Verify no real API calls were made
        assert True, "Script ran without errors"

    def test_check_posts_script(self):
        """Test check_posts script runs correctly."""
        # Suppress output from the script
        with redirect_stdout(io.StringIO()), redirect_stderr(io.StringIO()):
            try:
                from scripts import check_posts

                check_posts.main()
            except SystemExit:
                pass

        assert True, "check_posts script ran"

    def test_photo_display_script(self):
        """Test photo display script runs correctly."""
        # Suppress output from the script
        with redirect_stdout(io.StringIO()), redirect_stderr(io.StringIO()):
            try:
                from scripts import test_photo_display

                test_photo_display.main()
            except SystemExit:
                pass

        assert True, "test_photo_display script ran"

    @patch(
        "instagram_manager.instagram_api.brightdata.BrightDataClient.get_posts"
    )
    def test_import_fix_script(self, mock_get_posts):
        """Test import fix script runs correctly."""
        mock_get_posts.return_value = []
        # Suppress output from the script
        with redirect_stdout(io.StringIO()), redirect_stderr(io.StringIO()):
            try:
                from scripts import test_import_fix

                test_import_fix.main()
            except SystemExit:
                pass

        assert True, "test_import_fix script ran"

    @patch(
        "instagram_manager.services.post_service.PostService.import_posts_for_profile"
    )
    def test_post_import_issue_script(self, mock_import):
        """Test post import issue script runs correctly."""
        mock_import.return_value = []
        # Suppress output from the script
        with redirect_stdout(io.StringIO()), redirect_stderr(io.StringIO()):
            try:
                from scripts import test_post_import_issue

                test_post_import_issue.main()
            except SystemExit:
                pass

        assert True, "test_post_import_issue script ran"
