# MCP Server Test Suite

## Overview

Comprehensive test suite for the SocialManager MCP server including:
- Unit tests for individual components
- Integration tests for workflows
- End-to-end tests with MCP client
- Performance and load tests
- Automated CI/CD pipeline

## Test Structure

```
tests/
├── conftest.py           # Pytest configuration and fixtures
├── unit/                 # Unit tests for individual functions
├── integration/          # Integration tests for workflows
├── e2e/                  # End-to-end tests with MCP client
├── performance/          # Performance and load tests
├── utils/                # Test utilities and helpers
├── core/                 # Core app tests
├── instagram_manager/    # Instagram app tests
├── telegram_manager/     # Telegram app tests
└── mcp_server/          # MCP server specific tests
```

## Running Tests

### Prerequisites

```bash
# Install test dependencies
uv pip install -e ".[test]"

# Install performance monitoring
uv pip install psutil
```

### Local Development

```bash
# Run all tests (690+ tests)
uv run pytest

# Run with enhanced readable output
uv run pytest -c pytest-readable.ini

# Run with ultra-clean output
uv run pytest -c pytest-clean.ini

# Run specific test category
uv run pytest tests/unit
uv run pytest tests/integration
uv run pytest tests/e2e
uv run pytest tests/performance

# Run with coverage
uv run pytest --cov=mcp_server --cov-report=html

# Run specific test file
uv run pytest tests/unit/test_instagram_tools.py

# Run specific test class
uv run pytest tests/unit/test_instagram_tools.py::TestInstagramProfileTools

# Run specific test method
uv run pytest tests/unit/test_instagram_tools.py::TestInstagramProfileTools::test_get_profile_success

# Run tests matching pattern
uv run pytest -k "profile"

# Run only failed tests from last run
uv run pytest --lf

# Run tests in parallel (requires pytest-xdist)
uv run pytest -n auto

# Run with specific markers
uv run pytest -m "e2e"
uv run pytest -m "performance"

# Skip automatic cleanup of test artifacts
uv run pytest --no-cleanup
```

### Automatic Cleanup

После завершения тестов автоматически выполняется очистка:
- Удаляются все файлы из подпапок `test_media/`
- Удаляется и пересоздается папка `htmlcov/`

Чтобы отключить автоматическую очистку (например, для отладки):
```bash
uv run pytest --no-cleanup
```

### Docker

```bash
# Run tests in Docker
docker-compose -f docker-compose.test.yml run --rm test

# Run specific test suite
docker-compose -f docker-compose.test.yml run --rm test pytest tests/unit
```

## Test Categories

### Unit Tests (`tests/unit/`)
- Test individual functions and methods
- Mock all external dependencies
- Fast execution (< 100ms per test)
- Focus on business logic validation

Example:
```python
@pytest.mark.asyncio
async def test_get_profile_success(mock_brightdata):
    result = await instagram_get_profile("testuser")
    assert result["username"] == "testuser"
```

### Integration Tests (`tests/integration/`)
- Test multiple components working together
- Use real database (test database)
- Mock external APIs (BrightData, Telegram)
- Test complete workflows

Example:
```python
@pytest.mark.asyncio
async def test_instagram_profile_import_workflow(db, mock_celery):
    # Create task
    task = await task_create_import(...)
    # Verify workflow completion
    assert task["status"] == "completed"
```

### End-to-End Tests (`tests/e2e/`)
- Test complete user scenarios
- Use MCP client interface
- Minimal mocking
- Validate API contracts

Example:
```python
@pytest.mark.e2e
async def test_complete_instagram_workflow(mcp_client):
    profile = await mcp_client.call_tool("instagram_get_profile", {...})
    assert profile["follower_count"] > 0
```

### Performance Tests (`tests/performance/`)
- Load testing and benchmarking
- Response time analysis
- Memory usage tracking
- Concurrency testing

Example:
```python
@pytest.mark.performance
async def test_concurrent_profile_requests(mcp_client, performance_monitor):
    # Test 100 concurrent requests
    assert p50 < 100  # Median under 100ms
```

## Test Fixtures

### Core Fixtures

```python
# Database fixtures
@pytest.fixture
async def async_db():
    """Database for async tests"""

@pytest.fixture
async def async_user(async_db):
    """Test user for async tests"""

# MCP fixtures
@pytest.fixture
async def mcp_client():
    """MCP test client"""

# Mock fixtures
@pytest.fixture
def mock_celery():
    """Mock Celery tasks"""

@pytest.fixture
def mock_brightdata():
    """Mock BrightData API"""

@pytest.fixture
def mock_redis():
    """Mock Redis cache"""
```

### Test Data Fixtures

```python
@pytest.fixture
def instagram_profile_data():
    """Sample Instagram profile data"""

@pytest.fixture
def task_result_data():
    """Sample task result data"""

@pytest.fixture
def performance_monitor():
    """Performance measurement tool"""
```

## Writing Tests

### Test Structure

```python
import pytest
from unittest.mock import Mock, patch

class TestFeatureName:
    """Test suite for feature"""
    
    @pytest.fixture
    def setup_data(self):
        """Setup test data"""
        return {...}
    
    @pytest.mark.asyncio
    async def test_specific_behavior(self, setup_data, mock_brightdata):
        """Test specific behavior"""
        # Arrange
        mock_brightdata.get_profile.return_value = {...}
        
        # Act
        result = await function_under_test()
        
        # Assert
        assert result["success"] is True
        assert_valid_response(result, ["id", "username"])
```

### Best Practices

1. **Test Naming**: Use descriptive names that explain what is being tested
   - ✅ `test_get_profile_with_invalid_username_returns_error`
   - ❌ `test_profile_1`

2. **Isolation**: Each test should be independent
   - Use fixtures for setup
   - Clean up after tests
   - Don't rely on test execution order

3. **Mocking**: Mock external dependencies appropriately
   - Mock at the boundary (APIs, databases)
   - Use real implementations where possible
   - Verify mock calls

4. **Assertions**: Use specific assertions
   - Assert on exact values, not just truthy/falsy
   - Use helper functions for complex assertions
   - Include error messages in assertions

5. **Performance**: Keep tests fast
   - Unit tests < 100ms
   - Integration tests < 1s
   - Use markers for slow tests

## Test Utilities

### Mock Data Generator

```python
from tests.utils.test_helpers import MockDataGenerator

# Generate test data
generator = MockDataGenerator()
profile = generator.instagram_profile(verified=True)
posts = generator.instagram_posts(count=10)
```

### Test Data Builder

```python
from tests.utils.test_helpers import TestDataBuilder

# Build complex test scenarios
builder = TestDataBuilder()
data = await builder.create_profile_hierarchy(
    profile_count=5,
    posts_per_profile=10
)
```

### Assertion Helpers

```python
from tests.utils.test_helpers import (
    assert_valid_response,
    assert_error_response,
    assert_pagination_response
)

# Use in tests
assert_valid_response(result, ["id", "username"])
assert_error_response(result, "validation_error")
assert_pagination_response(result)
```

## Continuous Integration

### GitHub Actions Workflow

The project uses GitHub Actions for CI/CD with the following jobs:

1. **Lint**: Code quality checks (ruff, black, mypy)
2. **Test**: Parallel test execution by category
3. **Performance**: Performance benchmarking (on main branch)
4. **Docker**: Build verification
5. **E2E**: End-to-end tests (on PRs)
6. **Security**: Vulnerability scanning

### Running CI Locally

```bash
# Install act (GitHub Actions locally)
brew install act

# Run CI workflow
act -j test

# Run specific job
act -j lint
```

## Performance Benchmarks

Target performance metrics:

| Metric | Target | Actual |
|--------|--------|--------|
| P50 Response Time | < 100ms | TBD |
| P95 Response Time | < 200ms | TBD |
| P99 Response Time | < 500ms | TBD |
| Task Creation | > 10/sec | TBD |
| Memory Growth | < 100MB | TBD |
| Concurrent Requests | > 100 | TBD |

## Test Coverage

Current coverage goals:

- Overall: > 80%
- Unit tests: > 90%
- Integration tests: > 70%
- Critical paths: 100%

View coverage report:
```bash
# Generate HTML report
uv run pytest --cov=. --cov-report=html

# Open report
open htmlcov/index.html
```

## Debugging Tests

### Verbose Output

```bash
# Show all test output
uv run pytest -vv -s

# Show test durations
uv run pytest --durations=10

# Drop into debugger on failure
uv run pytest --pdb

# Show local variables on failure
uv run pytest -l
```

### Specific Test Debugging

```python
# Add breakpoint in test
import pdb; pdb.set_trace()

# Or use pytest debugging
pytest.set_trace()

# Print debug info
print(f"Debug: {variable}")
```

## Common Issues

### Database Access Errors
- Ensure test database exists
- Check DATABASE_URL in environment
- Run migrations: `python manage.py migrate`

### Async Test Errors
- Use `@pytest.mark.asyncio` decorator
- Use async fixtures properly
- Check event loop configuration

### Mock Not Working
- Verify patch path is correct
- Use `spec=True` for better mocking
- Check mock is applied before import

### Performance Test Failures
- Increase timeout values for CI
- Check system resources
- Use performance markers appropriately

### Test Artifacts Not Cleaned
- Check if `--no-cleanup` flag was used
- Verify pytest_cleanup_plugin.py exists in tests/
- Check write permissions on test_media and htmlcov directories
- Look for cleanup errors in test output

## Contributing

When adding new tests:

1. Choose appropriate test category
2. Use existing fixtures and utilities
3. Follow naming conventions
4. Add docstrings to test classes/methods
5. Update this README if needed
6. Ensure tests pass locally before PR
7. Check coverage doesn't decrease

## Test Markers

Available pytest markers:

- `@pytest.mark.asyncio`: Async test
- `@pytest.mark.django_db`: Database access
- `@pytest.mark.e2e`: End-to-end test
- `@pytest.mark.performance`: Performance test
- `@pytest.mark.slow`: Slow test (> 1s)
- `@pytest.mark.unit`: Unit test
- `@pytest.mark.integration`: Integration test

## Environment Variables

Required for tests:

```bash
# Django
DJANGO_SETTINGS_MODULE=tests.settings

# Database
DATABASE_URL=postgresql://user:pass@localhost/test_db

# Redis
REDIS_URL=redis://localhost:6379/0

# APIs (use test keys)
BRIGHTDATA_API_KEY=test_key
FIELD_ENCRYPTION_KEY=test_encryption_key_32_characters!!
```

---

For more information, see the main project README or contact the development team.