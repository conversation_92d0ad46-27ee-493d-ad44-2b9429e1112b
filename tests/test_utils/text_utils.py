"""Утилиты для работы с текстом в тестах"""

import html
import re
import unicodedata


class TextUtils:
    """Утилиты для работы с текстом"""
    
    HASHTAG_PATTERN = re.compile(r"#[A-Za-z0-9А-Яа-я_]+", re.UNICODE)
    MENTION_PATTERN = re.compile(r"@[A-Za-z0-9А-Яа-я_.]+", re.UNICODE)
    URL_PATTERN = re.compile(
        r"http[s]?://(?:[a-zA-Z]|[0-9]|[$-_@.&+]|[!*\(\),]|(?:%[0-9a-fA-F][0-9a-fA-F]))+"
    )
    EMOJI_PATTERN = re.compile(
        "["
        "\U0001F600-\U0001F64F"  # emoticons
        "\U0001F300-\U0001F5FF"  # symbols & pictographs
        "\U0001F680-\U0001F6FF"  # transport & map symbols
        "\U0001F1E0-\U0001F1FF"  # flags (iOS)
        "\U00002702-\U000027B0"
        "\U000024C2-\U0001F251"
        "]+", flags=re.UNICODE)
    
    @staticmethod
    def extract_hashtags(text: str) -> list[str]:
        """Извлечение хештегов из текста"""
        if not text:
            return []
        
        hashtags = TextUtils.HASHTAG_PATTERN.findall(text)
        # Нормализация: удаляем #, приводим к нижнему регистру
        return list(set(tag[1:].lower() for tag in hashtags))
    
    @staticmethod
    def extract_mentions(text: str) -> list[str]:
        """Извлечение @mentions из текста"""
        if not text:
            return []
        
        mentions = TextUtils.MENTION_PATTERN.findall(text)
        # Нормализация: удаляем @, приводим к нижнему регистру
        return list(set(mention[1:].lower() for mention in mentions))
    
    @staticmethod
    def clean_text(text: str, preserve_newlines: bool = False) -> str:
        """Очистка текста от лишних символов"""
        if not text:
            return ""
        
        # Декодируем HTML entities
        text = html.unescape(text)
        
        # Удаляем управляющие символы, но сохраняем табуляцию, перевод строки и возврат каретки
        text = re.sub(r"[\x00-\x08\x0B\x0C\x0E-\x1F\x7F-\x9F]", "", text)
        
        # Нормализация уникода
        text = unicodedata.normalize("NFKC", text)
        
        if not preserve_newlines:
            # Заменяем множественные пробелы
            text = re.sub(r"\s+", " ", text)
            return text.strip()
        else:
            # Сохраняем переводы строк, но убираем лишние пробелы
            lines = text.split("\n")
            lines = [re.sub(r"\s+", " ", line).strip() if line.strip() else "" for line in lines]
            return "\n".join(lines)
    
    @staticmethod
    def truncate_text(text: str, max_length: int, suffix: str = "...") -> str:
        """Обрезка текста с сохранением целостности слов"""
        if len(text) <= max_length:
            return text
        
        # Обрезаем до максимальной длины минус suffix
        truncated = text[:max_length - len(suffix)]
        
        # Находим последний пробел, чтобы не обрезать слово
        last_space = truncated.rfind(" ")
        if last_space > 0:
            truncated = truncated[:last_space]
        
        return truncated + suffix
    
    @staticmethod
    def extract_urls(text: str) -> list[str]:
        """Извлечение URL из текста"""
        if not text:
            return []
        
        return TextUtils.URL_PATTERN.findall(text)