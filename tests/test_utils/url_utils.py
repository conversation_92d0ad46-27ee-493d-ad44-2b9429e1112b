"""Утилиты для работы с URL в тестах"""

import re
from urllib.parse import urlparse


class URLUtils:
    """Утилиты для работы с URL"""
    
    INSTAGRAM_DOMAINS = {"instagram.com", "www.instagram.com", "instagr.am"}
    TELEGRAM_DOMAINS = {"t.me", "telegram.me", "telegram.org"}
    
    @staticmethod
    def extract_instagram_username(url: str) -> str | None:
        """Извлечение username из Instagram URL"""
        try:
            parsed = urlparse(url)
            
            # Проверяем домен
            if parsed.netloc not in URLUtils.INSTAGRAM_DOMAINS:
                return None
            
            # Удаляем начальный и конечный слеш
            path = parsed.path.strip("/")
            
            # Проверяем разные форматы URL
            # Профиль: instagram.com/username
            if path and "/" not in path:
                return path
            
            # Посты, reels, IGTV: instagram.com/p/xxx, instagram.com/reel/xxx
            parts = path.split("/")
            if len(parts) >= 2 and parts[0] in ["p", "reel", "tv"]:
                # Не username, а ID поста
                return None
            
            # Stories: instagram.com/stories/username/xxx
            if len(parts) >= 2 and parts[0] == "stories":
                return parts[1]
            
            return None
            
        except Exception:
            return None
    
    @staticmethod
    def is_valid_instagram_url(url: str) -> bool:
        """Проверка валидности Instagram URL"""
        try:
            parsed = urlparse(url)
            return parsed.netloc in URLUtils.INSTAGRAM_DOMAINS
        except Exception:
            return False
    
    @staticmethod
    def extract_instagram_post_id(url: str) -> str | None:
        """Извлечение ID поста из Instagram URL"""
        try:
            parsed = urlparse(url)
            
            if parsed.netloc not in URLUtils.INSTAGRAM_DOMAINS:
                return None
            
            # Проверяем формат URL поста
            match = re.match(r"^/(p|reel|tv)/([A-Za-z0-9_-]+)", parsed.path)
            if match:
                return match.group(2)
            
            return None
            
        except Exception:
            return None
    
    @staticmethod
    def clean_url(url: str) -> str:
        """Очистка URL от лишних параметров"""
        try:
            parsed = urlparse(url)
            # Проверяем, что это валидный URL со схемой
            if parsed.scheme and parsed.netloc:
                # Удаляем query parameters
                return f"{parsed.scheme}://{parsed.netloc}{parsed.path}"
            else:
                # Возвращаем исходный URL, если он невалидный
                return url
        except Exception:
            return url