"""
Pytest plugin for automatic cleanup of test artifacts.

This plugin automatically cleans up test_media subdirectories and htmlcov
directory after test session completion.
"""

import logging
import shutil
from pathlib import Path

logger = logging.getLogger(__name__)


def cleanup_test_media(base_dir: Path) -> None:
    """
    Clean up contents of test_media subdirectories.
    
    Preserves directory structure but removes all files and subdirectories
    within each subdirectory of test_media.
    
    Args:
        base_dir: Base directory of the project
    """
    test_media_dir = base_dir / "test_media"
    
    if not test_media_dir.exists():
        logger.debug(f"test_media directory not found at {test_media_dir}")
        return
    
    cleaned_count = 0
    
    # Iterate through all subdirectories in test_media
    for subdir in test_media_dir.iterdir():
        if subdir.is_dir():
            # Clean all contents within the subdirectory
            for item in subdir.iterdir():
                try:
                    if item.is_file():
                        item.unlink()
                        cleaned_count += 1
                    elif item.is_dir():
                        shutil.rmtree(item)
                        cleaned_count += 1
                except Exception as e:
                    logger.warning(f"Failed to remove {item}: {e}")
    
    logger.info(f"Cleaned {cleaned_count} items from test_media subdirectories")


def cleanup_htmlcov(base_dir: Path) -> None:
    """
    Remove and recreate htmlcov directory.
    
    Completely removes the htmlcov directory and all its contents,
    then creates an empty directory in its place.
    
    Args:
        base_dir: Base directory of the project
    """
    htmlcov_dir = base_dir / "htmlcov"
    
    if htmlcov_dir.exists():
        try:
            shutil.rmtree(htmlcov_dir)
            logger.info(f"Removed htmlcov directory at {htmlcov_dir}")
        except Exception as e:
            logger.warning(f"Failed to remove htmlcov directory: {e}")
            return
    
    # Recreate empty directory
    try:
        htmlcov_dir.mkdir(exist_ok=True)
        logger.info(f"Created empty htmlcov directory at {htmlcov_dir}")
    except Exception as e:
        logger.warning(f"Failed to create htmlcov directory: {e}")


def perform_cleanup(base_dir: Path, skip_cleanup: bool = False) -> None:
    """
    Perform full cleanup of test artifacts.
    
    Args:
        base_dir: Base directory of the project
        skip_cleanup: If True, skip cleanup (for --no-cleanup option)
    """
    if skip_cleanup:
        logger.info("Skipping test cleanup (--no-cleanup option)")
        return
    
    logger.info("Starting post-test cleanup...")
    
    cleanup_test_media(base_dir)
    cleanup_htmlcov(base_dir)
    
    logger.info("Post-test cleanup completed")