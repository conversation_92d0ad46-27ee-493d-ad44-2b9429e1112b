"""
Улучшенные фабрики для тестовых данных с traits и batch создание
"""
import random
from datetime import timedelta

import factory
from django.contrib.auth import get_user_model
from django.utils import timezone
from factory import fuzzy
from factory.django import DjangoModelFactory
from faker import Faker

from instagram_manager.models import (
    InstagramComment,
    InstagramFollower,
    InstagramHashtag,
    InstagramMedia,
    InstagramPost,
    InstagramProfile,
)
from telegram_manager.models import TelegramChat, TelegramMessage, TelegramUser

User = get_user_model()
fake = Faker()


class UserFactory(DjangoModelFactory):
    """Фабрика для создания пользователей Django"""
    
    class Meta:
        model = User
        django_get_or_create = ("username",)
    
    username = factory.Sequence(lambda n: f"user{n}")
    email = factory.LazyAttribute(lambda obj: f"{obj.username}@example.com")
    first_name = factory.Faker("first_name")
    last_name = factory.Faker("last_name")
    is_active = True
    is_staff = False
    is_superuser = False
    
    class Params:
        # Trait для создания админа
        admin = factory.Trait(
            is_staff=True,
            is_superuser=True,
            username=factory.Sequence(lambda n: f"admin{n}")
        )
        # Trait для создания заблокированного пользователя
        blocked = factory.Trait(
            is_active=False
        )


class InstagramProfileFactory(DjangoModelFactory):
    """Фабрика для Instagram профилей с traits"""
    
    class Meta:
        model = InstagramProfile
        django_get_or_create = ("username",)
    
    profile_id = factory.LazyAttribute(lambda _: str(fake.random_int(10000000, 99999999)))
    username = factory.LazyAttribute(lambda _: fake.user_name())
    full_name = factory.LazyAttribute(lambda _: fake.name())
    bio = factory.LazyAttribute(lambda _: fake.text(max_nb_chars=150))
    
    follower_count = factory.Faker("random_int", min=100, max=100000)
    following_count = factory.Faker("random_int", min=50, max=50000)
    post_count = factory.Faker("random_int", min=0, max=1000)
    
    is_verified = factory.Faker("boolean", chance_of_getting_true=10)
    is_private = factory.Faker("boolean", chance_of_getting_true=20)
    is_business = factory.Faker("boolean", chance_of_getting_true=30)
    
    profile_pic_url = factory.Faker("image_url")
    profile_pic_hd_url = factory.LazyAttribute(lambda obj: obj.profile_pic_url)
    external_url = factory.Faker("url")
    category_name = factory.Faker("word")
    
    last_scraped_at = fuzzy.FuzzyDateTime(
        timezone.now() - timedelta(days=30),
        timezone.now()
    )
    
    class Params:
        # Trait для популярного профиля
        popular = factory.Trait(
            follower_count=factory.Faker("random_int", min=100000, max=1000000),
            is_verified=True,
            is_private=False,  # Явно устанавливаем публичный
            is_business=False,  # Популярные профили не обязательно бизнес
            post_count=factory.Faker("random_int", min=500, max=2000)
        )
        # Trait для бизнес профиля
        business = factory.Trait(
            is_business=True,
            is_verified=True,
            is_private=False,  # Бизнес профили обычно публичные
            category_name=factory.Faker("company_suffix"),
            external_url=factory.Faker("url")
        )
        # Trait для приватного профиля
        private = factory.Trait(
            is_private=True,
            is_verified=False,
            is_business=False  # Приватные профили обычно не бизнес
        )
        # Trait для нового профиля
        new_profile = factory.Trait(
            follower_count=factory.Faker("random_int", min=0, max=100),
            following_count=factory.Faker("random_int", min=0, max=200),
            post_count=factory.Faker("random_int", min=0, max=10),
            is_verified=False,
            is_private=False,  # Новые профили обычно публичные
            is_business=False  # Новые профили обычно не бизнес
        )
    
    @factory.post_generation
    def posts(self, create, extracted, **kwargs):
        """Создание связанных постов"""
        if not create:
            return
            
        if extracted:
            # Используем переданные посты
            for post in extracted:
                self.posts.add(post)
        elif kwargs.get("create_posts"):
            # Автоматически создаем посты
            num_posts = kwargs.get("num_posts", 5)
            InstagramPostFactory.create_batch(num_posts, profile=self)
    
    @factory.post_generation
    def followers(self, create, extracted, **kwargs):
        """Создание подписчиков"""
        if not create:
            return
            
        if extracted:
            for follower in extracted:
                InstagramFollowerFactory(profile=self, follower_username=follower.username, follower_id=follower.profile_id)
        elif kwargs.get("create_followers"):
            num_followers = kwargs.get("num_followers", 10)
            for _ in range(num_followers):
                InstagramFollowerFactory(profile=self)


class InstagramPostFactory(DjangoModelFactory):
    """Фабрика для Instagram постов с расширенными возможностями"""
    
    class Meta:
        model = InstagramPost
    
    profile = factory.SubFactory(InstagramProfileFactory)
    external_id = factory.LazyAttribute(lambda _: str(fake.random_int(100000000, 999999999)))
    shortcode = factory.LazyAttribute(lambda _: fake.lexify("?" * 11, letters="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789_-"))
    caption = factory.Faker("text", max_nb_chars=500)
    
    like_count = factory.Faker("random_int", min=10, max=50000)
    comment_count = factory.Faker("random_int", min=0, max=1000)
    view_count = factory.Faker("random_int", min=0, max=100000)
    
    post_type = factory.Iterator(["photo", "video", "carousel", "reel"])
    post_url = factory.LazyAttribute(lambda obj: f"https://www.instagram.com/p/{obj.shortcode}/")
    
    location = factory.Faker("city")
    location_id = factory.LazyAttribute(lambda obj: str(fake.random_int(1000, 9999)) if obj.location else None)
    
    is_sponsored = factory.Faker("boolean", chance_of_getting_true=5)
    is_comments_disabled = factory.Faker("boolean", chance_of_getting_true=10)
    
    posted_at = fuzzy.FuzzyDateTime(
        timezone.now() - timedelta(days=365),
        timezone.now()
    )
    
    class Params:
        # Trait для видео поста
        video = factory.Trait(
            post_type="video",
            view_count=factory.Faker("random_int", min=100, max=100000),
            video_play_count=factory.Faker("random_int", min=100, max=100000)
        )
        # Trait для популярного поста
        viral = factory.Trait(
            like_count=factory.Faker("random_int", min=50000, max=500000),
            comment_count=factory.Faker("random_int", min=1000, max=10000),
            is_comments_disabled=False
        )
        # Trait для спонсированного поста
        sponsored = factory.Trait(
            is_sponsored=True,
            caption=factory.LazyAttribute(lambda _: f"#ad #sponsored {fake.text(max_nb_chars=400)}")
        )
        # Trait для поста без вовлеченности
        no_engagement = factory.Trait(
            like_count=0,
            comment_count=0,
            is_comments_disabled=True
        )
    
    @factory.post_generation
    def media(self, create, extracted, **kwargs):
        """Создание связанных медиа файлов"""
        if not create:
            return
            
        if extracted:
            for media in extracted:
                self.media.add(media)
        else:
            # Автоматически создаем медиа в зависимости от типа поста
            if self.post_type == "carousel":
                num_media = kwargs.get("num_media", random.randint(2, 10))
                for i in range(num_media):
                    InstagramMediaFactory(
                        post=self,
                        order_index=i
                    )
            else:
                # Для обычных постов создаем один медиа файл
                InstagramMediaFactory(
                    post=self,
                    media_type="video" if self.post_type in ["video", "reel"] else "photo"
                )
    
    @factory.post_generation
    def comments(self, create, extracted, **kwargs):
        """Создание комментариев"""
        if not create or self.is_comments_disabled:
            return
            
        if extracted:
            for comment in extracted:
                self.comments.add(comment)
        elif kwargs.get("create_comments"):
            num_comments = kwargs.get("num_comments", random.randint(1, 10))
            InstagramCommentFactory.create_batch(num_comments, post=self)
    
    @factory.post_generation
    def hashtags(self, create, extracted, **kwargs):
        """Добавление хештегов"""
        if not create:
            return
            
        if extracted:
            for hashtag in extracted:
                self.hashtags.add(hashtag)
        elif kwargs.get("create_hashtags"):
            num_hashtags = kwargs.get("num_hashtags", random.randint(3, 10))
            hashtags = InstagramHashtagFactory.create_batch(num_hashtags)
            self.hashtags.set(hashtags)


class InstagramMediaFactory(DjangoModelFactory):
    """Фабрика для медиа файлов Instagram"""
    
    class Meta:
        model = InstagramMedia
    
    post = factory.SubFactory(InstagramPostFactory)
    external_id = factory.LazyAttribute(lambda _: str(fake.random_int(100000000, 999999999)))
    media_type = factory.Iterator(["photo", "video"])
    media_url = factory.Faker("image_url")
    thumbnail_url = factory.LazyAttribute(
        lambda obj: fake.image_url() if obj.media_type == "video" else obj.media_url
    )
    
    width = factory.Faker("random_int", min=640, max=1920)
    height = factory.Faker("random_int", min=480, max=1080)
    duration = factory.LazyAttribute(
        lambda obj: fake.random_int(5, 300) if obj.media_type == "video" else None
    )
    
    order_index = 0
    
    local_path = None
    local_thumbnail_path = None
    
    class Params:
        # Trait для высокого разрешения
        high_quality = factory.Trait(
            width=1920,
            height=1080
        )
        # Trait для квадратного формата
        square = factory.Trait(
            width=1080,
            height=1080
        )
        # Trait для загруженного медиа
        downloaded = factory.Trait(
            local_path=factory.LazyAttribute(lambda obj: f"/media/instagram/{obj.post.shortcode}_{obj.order_index}.jpg"),
            local_thumbnail_path=factory.LazyAttribute(
                lambda obj: f"/media/instagram/thumbs/{obj.post.shortcode}_{obj.order_index}.jpg" if obj.media_type == "video" else None
            )
        )


class InstagramCommentFactory(DjangoModelFactory):
    """Фабрика для комментариев Instagram"""
    
    class Meta:
        model = InstagramComment
    
    post = factory.SubFactory(InstagramPostFactory)
    external_id = factory.LazyAttribute(lambda _: str(fake.random_int(100000000, 999999999)))
    text = factory.Faker("text", max_nb_chars=200)
    
    author_username = factory.LazyAttribute(lambda _: fake.user_name())
    author_external_id = factory.LazyAttribute(lambda _: str(fake.random_int(10000000, 99999999)))
    author_profile_pic = factory.Faker("image_url")
    author_is_verified = factory.Faker("boolean", chance_of_getting_true=5)
    
    like_count = factory.Faker("random_int", min=0, max=1000)
    
    is_hidden = False
    is_pinned = factory.Faker("boolean", chance_of_getting_true=5)
    
    reply_to = None
    
    commented_at = fuzzy.FuzzyDateTime(
        timezone.now() - timedelta(days=30),
        timezone.now()
    )
    
    class Params:
        # Trait для популярного комментария
        popular = factory.Trait(
            like_count=factory.Faker("random_int", min=100, max=5000),
            is_pinned=True
        )
        # Trait для бизнес ответа
        business_reply = factory.Trait(
            author_is_verified=True,
            text=factory.LazyAttribute(lambda _: f"Thank you for your feedback! {fake.text(max_nb_chars=150)}")
        )
        # Trait для спама
        spam = factory.Trait(
            is_hidden=True,
            text=factory.LazyAttribute(lambda _: f"Check out my profile! {fake.url()}")
        )
    
    @factory.post_generation
    def replies(self, create, extracted, **kwargs):
        """Создание ответов на комментарий"""
        if not create:
            return
            
        if isinstance(extracted, int):
            # Если передано количество ответов
            for _ in range(extracted):
                InstagramCommentFactory(
                    post=self.post,
                    reply_to=self
                )
        elif extracted:
            # Если переданы конкретные ответы
            for reply in extracted:
                reply.reply_to = self
                reply.save()


class InstagramHashtagFactory(DjangoModelFactory):
    """Фабрика для хештегов Instagram"""
    
    class Meta:
        model = InstagramHashtag
        django_get_or_create = ("name",)
    
    name = factory.Sequence(lambda n: f"{fake.word().lower()}{n}")
    post_count = factory.Faker("random_int", min=100, max=1000000)
    
    class Params:
        # Trait для популярного хештега
        trending = factory.Trait(
            post_count=factory.Faker("random_int", min=1000000, max=10000000),
            name=factory.LazyAttribute(lambda _: f"trending{fake.word().lower()}")
        )
        # Trait для нишевого хештега
        niche = factory.Trait(
            post_count=factory.Faker("random_int", min=100, max=10000),
            name=factory.LazyAttribute(lambda _: f"{fake.word().lower()}_{fake.word().lower()}")
        )


class InstagramFollowerFactory(DjangoModelFactory):
    """Фабрика для связей подписчиков Instagram"""
    
    class Meta:
        model = InstagramFollower
    
    profile = factory.SubFactory(InstagramProfileFactory)
    follower_username = factory.LazyAttribute(lambda _: fake.user_name())
    follower_id = factory.LazyAttribute(lambda _: str(fake.random_int(10000000, 99999999)))
    follower_full_name = factory.LazyAttribute(lambda _: fake.name())
    is_verified = factory.Faker("boolean", chance_of_getting_true=5)
    profile_pic_url = factory.Faker("image_url")
    followed_at = fuzzy.FuzzyDateTime(
        timezone.now() - timedelta(days=365),
        timezone.now()
    )


class TelegramChatFactory(DjangoModelFactory):
    """Фабрика для Telegram чатов"""
    
    class Meta:
        model = TelegramChat
        django_get_or_create = ("chat_id",)
    
    chat_id = factory.Sequence(lambda n: -1001000000000 - n)
    title = factory.Faker("company")
    username = factory.LazyAttribute(lambda _: fake.user_name() if fake.boolean(chance_of_getting_true=70) else None)
    
    broadcast = factory.Faker("boolean", chance_of_getting_true=30)
    megagroup = factory.Faker("boolean", chance_of_getting_true=20) 
    
    participants_count = factory.Faker("random_int", min=10, max=10000)
    verified = factory.Faker("boolean", chance_of_getting_true=10)
    scam = False
    
    class Params:
        # Trait для канала
        channel = factory.Trait(
            broadcast=True,
            megagroup=False,
            participants_count=factory.Faker("random_int", min=100, max=100000)
        )
        # Trait для большой группы
        large_group = factory.Trait(
            broadcast=False,
            megagroup=True,
            participants_count=factory.Faker("random_int", min=1000, max=200000),
            verified=True
        )
        # Trait для скам чата
        scam_chat = factory.Trait(
            scam=True,
            verified=False
        )


class TelegramUserFactory(DjangoModelFactory):
    """Фабрика для Telegram пользователей"""
    
    class Meta:
        model = TelegramUser
        django_get_or_create = ("user_id",)
    
    user_id = factory.Sequence(lambda n: 100000000 + n)
    username = factory.LazyAttribute(lambda _: fake.user_name() if fake.boolean(chance_of_getting_true=80) else None)
    first_name = factory.Faker("first_name")
    last_name = factory.LazyAttribute(lambda _: fake.last_name() if fake.boolean(chance_of_getting_true=60) else None)
    phone = factory.LazyAttribute(lambda _: fake.phone_number() if fake.boolean(chance_of_getting_true=30) else None)
    
    is_bot = False
    is_verified = factory.Faker("boolean", chance_of_getting_true=5)
    is_premium = factory.Faker("boolean", chance_of_getting_true=10)
    is_deleted = False
    
    class Params:
        # Trait для бота
        bot = factory.Trait(
            is_bot=True,
            username=factory.LazyAttribute(lambda _: f"{fake.word().lower()}_bot"),
            last_name=None,
            phone=None
        )
        # Trait для премиум пользователя
        premium = factory.Trait(
            is_premium=True,
            is_verified=True
        )
        # Trait для удаленного аккаунта
        deleted = factory.Trait(
            is_deleted=True,
            username=None,
            first_name="Deleted",
            last_name="Account"
        )


class TelegramMessageFactory(DjangoModelFactory):
    """Фабрика для Telegram сообщений"""
    
    class Meta:
        model = TelegramMessage
    
    chat = factory.SubFactory(TelegramChatFactory)
    from_user = factory.SubFactory(TelegramUserFactory)
    
    message_id = factory.Sequence(lambda n: n + 1)
    text = factory.Faker("text", max_nb_chars=1000)
    
    date = fuzzy.FuzzyDateTime(
        timezone.now() - timedelta(days=30),
        timezone.now()
    )
    edit_date = None
    
    views = factory.LazyAttribute(
        lambda obj: fake.random_int(0, 10000) if obj.chat.broadcast else None
    )
    forwards = factory.LazyAttribute(
        lambda obj: fake.random_int(0, 100) if obj.chat.broadcast else 0
    )
    
    is_forward = factory.Faker("boolean", chance_of_getting_true=20)
    is_reply = factory.Faker("boolean", chance_of_getting_true=30)
    
    media_type = factory.Faker("random_element", elements=["", "photo", "video", "document"])
    
    class Params:
        # Trait для сообщения с медиа
        with_photo = factory.Trait(
            media_type="photo",
            photo=factory.LazyAttribute(lambda _: {"file_id": fake.uuid4(), "file_size": fake.random_int(1000, 50000)}),
            text=factory.LazyAttribute(lambda _: fake.text(max_nb_chars=200) if fake.boolean() else "")
        )
        # Trait для форварда
        forwarded = factory.Trait(
            is_forward=True,
            fwd_from_chat=factory.SubFactory(TelegramChatFactory),
            fwd_date=factory.LazyAttribute(lambda obj: obj.date - timedelta(hours=random.randint(1, 48)))
        )
        # Trait для ответа
        reply = factory.Trait(
            is_reply=True,
            reply_to=factory.SubFactory("tests.factories_enhanced.TelegramMessageFactory")
        )


# Примеры использования batch создания
def create_instagram_test_data():
    """Создание комплексных тестовых данных для Instagram"""
    
    # Создаем популярного блогера только с базовыми настройками
    influencer = InstagramProfileFactory(
        popular=True,
        username="test_influencer",
        followers__create_followers=True,
        followers__num_followers=20
    )
    
    # Создаем 10 постов (7 обычных + 3 вирусных) для точного количества
    InstagramPostFactory.create_batch(
        7,
        profile=influencer
    )
    
    # Создаем 3 вирусных поста
    viral_posts = InstagramPostFactory.create_batch(
        3,
        profile=influencer,
        viral=True,
        media__num_media=5,  # Для каруселей
        comments__create_comments=True,
        comments__num_comments=50,
        hashtags__create_hashtags=True,
        hashtags__num_hashtags=15
    )
    
    # Создаем бизнес профиль со спонсированными постами
    business = InstagramProfileFactory(
        business=True,
        username="test_business"
    )
    
    sponsored_posts = InstagramPostFactory.create_batch(
        5,
        profile=business,
        sponsored=True
    )
    
    return {
        "influencer": influencer,
        "viral_posts": viral_posts,
        "business": business,
        "sponsored_posts": sponsored_posts
    }


def create_telegram_test_data():
    """Создание комплексных тестовых данных для Telegram"""
    
    # Создаем канал с сообщениями
    channel = TelegramChatFactory(
        channel=True,
        title="Test Channel",
        participants_count=50000
    )
    
    # Создаем авторов
    authors = TelegramUserFactory.create_batch(3)
    
    # Создаем сообщения в канале
    messages = []
    for i in range(20):
        message = TelegramMessageFactory(
            chat=channel,
            from_user=random.choice(authors),
            with_photo=i % 3 == 0,  # Каждое третье с фото
            views=random.randint(1000, 20000)
        )
        messages.append(message)
    
    # Создаем супергруппу с дискуссией
    supergroup = TelegramChatFactory(
        large_group=True,
        title="Test Community"
    )
    
    # Создаем участников
    members = TelegramUserFactory.create_batch(10)
    
    # Создаем дискуссию (сообщения с ответами)
    discussion = []
    for i in range(30):
        if i % 5 == 0:
            # Новая тема
            message = TelegramMessageFactory(
                chat=supergroup,
                from_user=random.choice(members)
            )
        else:
            # Ответ на предыдущее
            message = TelegramMessageFactory(
                chat=supergroup,
                from_user=random.choice(members),
                reply=True
            )
        discussion.append(message)
    
    return {
        "channel": channel,
        "messages": messages,
        "supergroup": supergroup,
        "discussion": discussion,
        "members": members
    }