"""
Test helper utilities
"""

from typing import Dict, Any, List
import random
import string
from datetime import datetime, timedelta
from asgiref.sync import sync_to_async

from instagram_manager.models import InstagramProfile, InstagramPost, InstagramComment, InstagramMedia
from telegram_manager.models import TelegramChat, TelegramUser, TelegramMessage
from core.models import TaskResult


class MockDataGenerator:
    """Generate mock data for tests"""
    
    @staticmethod
    def instagram_profile(
        username: str = None,
        verified: bool = False,
        business: bool = False,
        private: bool = False
    ) -> Dict[str, Any]:
        """Generate mock Instagram profile"""
        username = username or f"user_{random.randint(1000, 9999)}"
        
        return {
            "id": random.randint(1, 1000000),
            "username": username,
            "full_name": f"{username.replace('_', ' ').title()}",
            "bio": f"Bio for {username}. " + " ".join([
                random.choice(["Photography", "Travel", "Food", "Fashion", "Art"]),
                "enthusiast.",
                f"📍 {random.choice(['NYC', 'LA', 'London', 'Paris', 'Tokyo'])}"
            ]),
            "external_id": str(random.randint(10000000, 99999999)),
            "follower_count": random.randint(100, 1000000),
            "following_count": random.randint(50, 5000),
            "post_count": random.randint(10, 1000),
            "is_verified": verified,
            "is_private": private,
            "is_business": business,
            "profile_pic_url": f"https://example.com/profiles/{username}.jpg",
            "website": f"https://{username}.com" if business else None,
            "category": random.choice(["Personal", "Creator", "Business"]) if business else None,
            "created_at": datetime.now() - timedelta(days=random.randint(1, 365))
        }
    
    @staticmethod
    def instagram_posts(
        count: int = 10,
        profile_id: int = None,
        post_type: str = None
    ) -> List[Dict[str, Any]]:
        """Generate mock Instagram posts"""
        posts = []
        post_types = ["photo", "video", "album", "reel"] if not post_type else [post_type]
        
        hashtags = ["#instagram", "#photography", "#travel", "#food", "#fashion", 
                   "#art", "#nature", "#sunset", "#love", "#instagood"]
        
        for i in range(count):
            selected_type = random.choice(post_types)
            post = {
                "id": random.randint(1, 1000000),
                "profile_id": profile_id or random.randint(1, 1000),
                "external_id": str(random.randint(10000000, 99999999)),
                "content": f"Post content {i}. " + " ".join(
                    random.sample(hashtags, random.randint(2, 5))
                ),
                "post_type": selected_type,
                "post_url": f"https://instagram.com/p/{MockDataGenerator._random_string(11)}",
                "like_count": random.randint(10, 100000),
                "comment_count": random.randint(0, 5000),
                "share_count": random.randint(0, 1000),
                "view_count": random.randint(100, 1000000) if selected_type in ["video", "reel"] else None,
                "is_video": selected_type in ["video", "reel"],
                "video_duration": random.randint(5, 60) if selected_type in ["video", "reel"] else None,
                "location": random.choice([None, "New York, NY", "Los Angeles, CA", "London, UK"]),
                "posted_at": datetime.now() - timedelta(
                    days=random.randint(1, 30),
                    hours=random.randint(0, 23),
                    minutes=random.randint(0, 59)
                )
            }
            posts.append(post)
        
        # Sort by posted_at descending
        posts.sort(key=lambda x: x["posted_at"], reverse=True)
        return posts
    
    @staticmethod
    def instagram_comments(
        count: int = 5,
        post_id: int = None,
        include_replies: bool = True
    ) -> List[Dict[str, Any]]:
        """Generate mock Instagram comments"""
        comments = []
        
        comment_templates = [
            "Great post! 👍",
            "Love this! ❤️",
            "Amazing content!",
            "So beautiful! 😍",
            "Wow! 🔥",
            "Nice shot! 📸",
            "This is incredible!",
            "Keep up the great work!",
            "Inspiring! ✨",
            "Perfect! 💯"
        ]
        
        for i in range(count):
            comment = {
                "id": random.randint(1, 1000000),
                "post_id": post_id or random.randint(1, 1000),
                "external_id": str(random.randint(10000000, 99999999)),
                "user_id": random.randint(1, 10000),
                "username": f"commenter_{random.randint(100, 999)}",
                "text": random.choice(comment_templates),
                "like_count": random.randint(0, 100),
                "reply_count": random.randint(0, 10) if include_replies else 0,
                "is_reply": False,
                "parent_comment_id": None,
                "created_at": datetime.now() - timedelta(
                    hours=random.randint(1, 48),
                    minutes=random.randint(0, 59)
                )
            }
            comments.append(comment)
            
            # Add some replies
            if include_replies and comment["reply_count"] > 0:
                for j in range(min(comment["reply_count"], 3)):
                    reply = {
                        "id": random.randint(1, 1000000),
                        "post_id": post_id or random.randint(1, 1000),
                        "external_id": str(random.randint(10000000, 99999999)),
                        "user_id": random.randint(1, 10000),
                        "username": f"replier_{random.randint(100, 999)}",
                        "text": f"@{comment['username']} I agree!",
                        "like_count": random.randint(0, 50),
                        "reply_count": 0,
                        "is_reply": True,
                        "parent_comment_id": comment["id"],
                        "created_at": comment["created_at"] + timedelta(minutes=random.randint(5, 60))
                    }
                    comments.append(reply)
        
        return comments
    
    @staticmethod
    def instagram_media(
        count: int = 1,
        post_id: int = None,
        media_type: str = None
    ) -> List[Dict[str, Any]]:
        """Generate mock Instagram media"""
        media_list = []
        media_types = ["image", "video"] if not media_type else [media_type]
        
        for i in range(count):
            selected_type = random.choice(media_types)
            media = {
                "id": random.randint(1, 1000000),
                "post_id": post_id or random.randint(1, 1000),
                "media_type": selected_type,
                "media_url": f"https://example.com/media/{MockDataGenerator._random_string(20)}.{'jpg' if selected_type == 'image' else 'mp4'}",
                "thumbnail_url": f"https://example.com/thumbs/{MockDataGenerator._random_string(20)}.jpg",
                "width": random.choice([1080, 1920, 720]),
                "height": random.choice([1080, 1920, 1350]),
                "file_size": random.randint(100000, 5000000),  # 100KB to 5MB
                "duration": random.randint(5, 60) if selected_type == "video" else None,
                "display_order": i,
                "local_path": None,
                "gcs_path": None
            }
            media_list.append(media)
        
        return media_list
    
    @staticmethod
    def task_result(
        task_type: str = "instagram.profile",
        status: str = "completed",
        task_id: str = None
    ) -> Dict[str, Any]:
        """Generate mock task result"""
        task_id = task_id or f"task_{MockDataGenerator._random_string(12)}"
        
        result = {
            "task_id": task_id,
            "task_type": task_type,
            "task_name": MockDataGenerator._get_task_name(task_type),
            "status": status,
            "progress": 100 if status == "completed" else random.randint(0, 99),
            "created_at": datetime.now() - timedelta(minutes=10),
            "started_at": datetime.now() - timedelta(minutes=9),
            "updated_at": datetime.now() - timedelta(minutes=1)
        }
        
        if status == "completed":
            result["completed_at"] = datetime.now()
            result["result"] = MockDataGenerator._get_task_result(task_type)
        elif status == "failed":
            result["completed_at"] = datetime.now()
            result["error"] = random.choice([
                "API rate limit exceeded",
                "Network connection error",
                "Invalid credentials",
                "Resource not found"
            ])
        
        return result
    
    @staticmethod
    def telegram_chat(
        chat_type: str = "channel",
        title: str = None
    ) -> Dict[str, Any]:
        """Generate mock Telegram chat"""
        title = title or f"Test {chat_type.title()} {random.randint(100, 999)}"
        
        return {
            "id": random.randint(1, 1000000),
            "chat_id": -random.randint(1000000000, 9999999999),
            "title": title,
            "username": f"test_{chat_type}_{random.randint(100, 999)}",
            "chat_type": chat_type,
            "description": f"Description for {title}",
            "member_count": random.randint(10, 10000),
            "is_verified": random.choice([True, False]),
            "is_restricted": False,
            "is_scam": False,
            "is_fake": False,
            "created_at": datetime.now() - timedelta(days=random.randint(30, 365))
        }
    
    @staticmethod
    def telegram_message(
        chat_id: int = None,
        user_id: int = None,
        message_type: str = "text"
    ) -> Dict[str, Any]:
        """Generate mock Telegram message"""
        content_by_type = {
            "text": f"Test message {random.randint(1, 1000)}",
            "photo": "Photo message",
            "video": "Video message",
            "document": "Document message",
            "sticker": "Sticker message"
        }
        
        return {
            "id": random.randint(1, 1000000),
            "message_id": random.randint(1, 100000),
            "chat_id": chat_id or -random.randint(1000000000, 9999999999),
            "user_id": user_id or random.randint(1, 1000000),
            "message_type": message_type,
            "content": content_by_type.get(message_type, "Unknown message"),
            "date": datetime.now() - timedelta(
                days=random.randint(0, 7),
                hours=random.randint(0, 23),
                minutes=random.randint(0, 59)
            ),
            "edit_date": None,
            "views": random.randint(0, 10000) if message_type == "channel" else None,
            "forwards": random.randint(0, 100),
            "replies": random.randint(0, 50)
        }
    
    @staticmethod
    def _random_string(length: int) -> str:
        """Generate random string"""
        return ''.join(random.choices(string.ascii_letters + string.digits, k=length))
    
    @staticmethod
    def _get_task_name(task_type: str) -> str:
        """Get task name from type"""
        task_names = {
            "instagram.profile": "Import Instagram Profile",
            "instagram.posts": "Import Instagram Posts",
            "instagram.comments": "Import Instagram Comments",
            "instagram.batch_posts": "Batch Import Instagram Posts",
            "telegram.messages": "Import Telegram Messages",
            "telegram.users": "Import Telegram Users"
        }
        return task_names.get(task_type, "Unknown Task")
    
    @staticmethod
    def _get_task_result(task_type: str) -> Dict[str, Any]:
        """Get mock task result based on type"""
        if task_type == "instagram.profile":
            return {
                "success": True,
                "profile_id": random.randint(1, 1000),
                "username": f"imported_user_{random.randint(100, 999)}",
                "imported": True,
                "follower_count": random.randint(1000, 10000)
            }
        elif task_type == "instagram.posts":
            return {
                "success": True,
                "posts_imported": random.randint(10, 100),
                "profile_id": random.randint(1, 1000),
                "post_ids": list(range(1, random.randint(11, 50)))
            }
        elif task_type == "instagram.comments":
            return {
                "success": True,
                "comments_imported": random.randint(20, 200),
                "post_id": random.randint(1, 1000)
            }
        elif task_type == "telegram.messages":
            return {
                "success": True,
                "messages_imported": random.randint(100, 1000),
                "chat_id": -random.randint(1000000000, 9999999999)
            }
        else:
            return {"success": True, "items_processed": random.randint(1, 100)}


class DataBuilder:
    """Build complex test scenarios"""
    
    def __init__(self):
        self.generator = MockDataGenerator()
    
    async def create_profile_hierarchy(
        self,
        profile_count: int = 5,
        posts_per_profile: int = 10,
        comments_per_post: int = 5,
        media_per_post: int = 1
    ) -> Dict[str, Any]:
        """Create profiles with posts, comments, and media"""
        data = {
            "profiles": [],
            "posts": [],
            "comments": [],
            "media": []
        }
        
        for i in range(profile_count):
            # Create profile
            profile_data = self.generator.instagram_profile(
                username=f"test_profile_{i}",
                verified=i == 0,  # First profile is verified
                business=i < 2   # First two are business accounts
            )
            
            profile = await sync_to_async(InstagramProfile.objects.create)(**{
                k: v for k, v in profile_data.items() 
                if k not in ["id", "created_at"]
            })
            data["profiles"].append(profile)
            
            # Generate posts for profile
            posts_data = self.generator.instagram_posts(
                count=posts_per_profile,
                profile_id=profile.id
            )
            
            for post_data in posts_data:
                post = await sync_to_async(InstagramPost.objects.create)(
                    profile=profile,
                    **{k: v for k, v in post_data.items() 
                       if k not in ["id", "profile_id", "posted_at"]}
                )
                data["posts"].append(post)
                
                # Generate comments for post
                comments_data = self.generator.instagram_comments(
                    count=comments_per_post,
                    post_id=post.id
                )
                
                for comment_data in comments_data:
                    comment = await sync_to_async(InstagramComment.objects.create)(
                        post=post,
                        **{k: v for k, v in comment_data.items() 
                           if k not in ["id", "post_id", "created_at"]}
                    )
                    data["comments"].append(comment)
                
                # Generate media for post
                media_data = self.generator.instagram_media(
                    count=media_per_post,
                    post_id=post.id
                )
                
                for media_item in media_data:
                    media = await sync_to_async(InstagramMedia.objects.create)(
                        post=post,
                        **{k: v for k, v in media_item.items() 
                           if k not in ["id", "post_id"]}
                    )
                    data["media"].append(media)
        
        return data
    
    async def create_task_history(
        self,
        task_count: int = 20,
        include_failures: bool = True
    ) -> List[TaskResult]:
        """Create task execution history"""
        tasks = []
        
        # Mix of task types and statuses
        task_types = [
            "instagram.profile",
            "instagram.posts",
            "instagram.batch_posts",
            "telegram.messages"
        ]
        
        statuses = ["completed", "completed", "completed", "failed", "running"] if include_failures else ["completed"]
        
        for i in range(task_count):
            task_data = self.generator.task_result(
                task_type=random.choice(task_types),
                status=random.choice(statuses)
            )
            
            task = await sync_to_async(TaskResult.objects.create)(**task_data)
            tasks.append(task)
        
        return tasks
    
    async def create_telegram_data(
        self,
        chat_count: int = 3,
        messages_per_chat: int = 50
    ) -> Dict[str, Any]:
        """Create Telegram chats with messages"""
        data = {
            "chats": [],
            "users": [],
            "messages": []
        }
        
        # Create chats
        for i in range(chat_count):
            chat_data = self.generator.telegram_chat(
                chat_type=random.choice(["channel", "group", "supergroup"]),
                title=f"Test Chat {i}"
            )
            
            chat = await sync_to_async(TelegramChat.objects.create)(**{
                k: v for k, v in chat_data.items() 
                if k not in ["id", "created_at"]
            })
            data["chats"].append(chat)
            
            # Create users for the chat
            user_count = min(10, chat.member_count or 10)
            for j in range(user_count):
                user = await sync_to_async(TelegramUser.objects.create)(
                    user_id=random.randint(1000000, 9999999),
                    username=f"user_{i}_{j}",
                    first_name=f"User{j}",
                    last_name=f"Test{i}",
                    is_bot=False
                )
                data["users"].append(user)
            
            # Create messages
            for k in range(messages_per_chat):
                message_data = self.generator.telegram_message(
                    chat_id=chat.chat_id,
                    user_id=random.choice(data["users"]).user_id
                )
                
                message = await sync_to_async(TelegramMessage.objects.create)(
                    chat=chat,
                    user=random.choice(data["users"]),
                    **{k: v for k, v in message_data.items() 
                       if k not in ["id", "chat_id", "user_id", "date"]}
                )
                data["messages"].append(message)
        
        return data


def assert_valid_response(response: Dict[str, Any], required_fields: List[str]):
    """Assert response has required fields"""
    for field in required_fields:
        assert field in response, f"Missing required field: {field}"
        
    # Check for common response fields
    if "error_type" not in response:
        # Success response should have standard fields
        assert response.get("success", True), "Response indicates failure"


def assert_error_response(response: Dict[str, Any], error_type: str = None):
    """Assert response is an error"""
    assert "error_type" in response or "error" in response, "Response is not an error"
    
    if error_type:
        assert response.get("error_type") == error_type, f"Expected error type {error_type}, got {response.get('error_type')}"
    
    # Error response should have message
    assert "message" in response or "error" in response, "Error response missing message"


def assert_pagination_response(response: Dict[str, Any]):
    """Assert response has valid pagination fields"""
    assert "total" in response, "Missing total count"
    assert "limit" in response, "Missing limit"
    assert "offset" in response, "Missing offset"
    
    # Validate pagination logic
    if "has_more" in response:
        total = response["total"]
        limit = response["limit"]
        offset = response["offset"]
        expected_has_more = (offset + limit) < total
        assert response["has_more"] == expected_has_more, "Invalid has_more calculation"


def generate_test_username(prefix: str = "test") -> str:
    """Generate unique test username"""
    timestamp = int(datetime.now().timestamp())
    random_suffix = MockDataGenerator._random_string(4)
    return f"{prefix}_{timestamp}_{random_suffix}"


def compare_datetime_strings(dt1: str, dt2: str, tolerance_seconds: int = 60) -> bool:
    """Compare two datetime strings with tolerance"""
    try:
        datetime1 = datetime.fromisoformat(dt1.replace('Z', '+00:00'))
        datetime2 = datetime.fromisoformat(dt2.replace('Z', '+00:00'))
        diff = abs((datetime1 - datetime2).total_seconds())
        return diff <= tolerance_seconds
    except Exception:
        return False