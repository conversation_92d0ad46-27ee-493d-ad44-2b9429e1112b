import unittest
from tests.test_utils.url_utils import URLUtils


class TestURLUtils(unittest.TestCase):
    """Тесты для утилит работы с URL"""
    
    def test_extract_instagram_username(self):
        """Тест извлечения username из Instagram URL"""
        # Профиль
        assert URLUtils.extract_instagram_username("https://instagram.com/testuser") == "testuser"
        assert URLUtils.extract_instagram_username("https://www.instagram.com/test_user/") == "test_user"
        
        # Stories
        assert URLUtils.extract_instagram_username("https://instagram.com/stories/testuser/123456") == "testuser"
        
        # Посты - не должны возвращать username
        assert URLUtils.extract_instagram_username("https://instagram.com/p/ABC123") is None
        assert URLUtils.extract_instagram_username("https://instagram.com/reel/XYZ789") is None
        
        # Невалидные URL
        assert URLUtils.extract_instagram_username("https://example.com/testuser") is None
        
    def test_is_valid_instagram_url(self):
        """Тест проверки валидности Instagram URL"""
        # Валидные
        assert URLUtils.is_valid_instagram_url("https://instagram.com/test")
        assert URLUtils.is_valid_instagram_url("https://www.instagram.com/p/123")
        assert URLUtils.is_valid_instagram_url("https://instagr.am/test")
        
        # Невалидные
        assert not URLUtils.is_valid_instagram_url("https://example.com/test")
        assert not URLUtils.is_valid_instagram_url("not_a_url")
        
    def test_extract_instagram_post_id(self):
        """Тест извлечения ID поста из Instagram URL"""
        # Посты
        assert URLUtils.extract_instagram_post_id("https://instagram.com/p/ABC123xyz_-") == "ABC123xyz_-"
        
        # Reels
        assert URLUtils.extract_instagram_post_id("https://instagram.com/reel/XYZ789") == "XYZ789"
        
        # TV
        assert URLUtils.extract_instagram_post_id("https://instagram.com/tv/VIDEO123") == "VIDEO123"
        
        # Невалидные
        assert URLUtils.extract_instagram_post_id("https://instagram.com/testuser") is None
        assert URLUtils.extract_instagram_post_id("https://example.com/p/123") is None
        
    def test_clean_url(self):
        """Тест очистки URL от параметров"""
        # С параметрами
        cleaned = URLUtils.clean_url("https://example.com/path?param1=value1&param2=value2")
        assert cleaned == "https://example.com/path"
        
        # Без параметров
        url = "https://example.com/path"
        assert URLUtils.clean_url(url) == url
        
        # Невалидный URL
        assert URLUtils.clean_url("not_a_url") == "not_a_url"
        


if __name__ == '__main__':
    unittest.main()