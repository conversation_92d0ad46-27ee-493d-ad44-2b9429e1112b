import unittest
from tests.test_utils.text_utils import TextUtils


class TestTextUtils(unittest.TestCase):
    """Тесты для утилит работы с текстом"""
    
    def test_extract_hashtags(self):
        """Тест извлечения хештегов"""
        text = "Hello #world! This is a #Test_Tag and #привет на русском #123numbers"
        hashtags = TextUtils.extract_hashtags(text)
        
        assert 'world' in hashtags
        assert 'test_tag' in hashtags
        assert 'привет' in hashtags
        assert '123numbers' in hashtags
        assert len(hashtags) == 4
        
        # Проверка пустого текста
        assert TextUtils.extract_hashtags("") == []
        assert TextUtils.extract_hashtags(None) == []
        
    def test_extract_mentions(self):
        """Тест извлечения упоминаний"""
        text = "@user1 mentioned @test.user and @русский_пользователь"
        mentions = TextUtils.extract_mentions(text)
        
        assert 'user1' in mentions
        assert 'test.user' in mentions
        assert 'русский_пользователь' in mentions
        assert len(mentions) == 3
        
    def test_clean_text(self):
        """Тест очистки текста"""
        # HTML entities
        text = "Test &amp; &lt;tag&gt; &quot;quote&quot;"
        cleaned = TextUtils.clean_text(text)
        assert cleaned == 'Test & <tag> "quote"'
        
        # Множественные пробелы
        text = "Test   multiple    spaces"
        cleaned = TextUtils.clean_text(text)
        assert cleaned == "Test multiple spaces"
        
        # С сохранением переводов строк
        text = "Line 1\n\nLine 2   with   spaces"
        cleaned = TextUtils.clean_text(text, preserve_newlines=True)
        assert cleaned == "Line 1\n\nLine 2 with spaces"
        
    def test_truncate_text(self):
        """Тест обрезки текста"""
        text = "This is a very long text that needs to be truncated"
        
        # Обрезка по словам
        truncated = TextUtils.truncate_text(text, 20)
        assert truncated == "This is a very..."
        assert len(truncated) <= 20
        
        # Короткий текст не обрезается
        short_text = "Short"
        assert TextUtils.truncate_text(short_text, 20) == short_text
        
    def test_extract_urls(self):
        """Тест извлечения URL"""
        text = "Check https://example.com and http://test.org/path?param=1"
        urls = TextUtils.extract_urls(text)
        
        assert "https://example.com" in urls
        assert "http://test.org/path?param=1" in urls
        assert len(urls) == 2
        


if __name__ == '__main__':
    unittest.main()