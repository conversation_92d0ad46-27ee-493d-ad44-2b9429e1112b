"""
Tests for task management system.
"""

from unittest.mock import patch, MagicMock
from django.test import TestCase

from core.models import TaskResult
from core.tasks.base import BaseTask
from core.tasks.decorators import make_async, task
from core.tasks.queue import task_queue


class SimpleTestTask(BaseTask):
    """Simple test task."""
    
    task_type = "test.simple"
    description = "Simple test task"
    
    def validate_params(self, value: int, **kwargs) -> dict:
        if value < 0:
            raise ValueError("Value must be positive")
        return {"value": value}
    
    def execute_task(self, value: int, **kwargs) -> dict:
        # Simulate some work
        self.update_progress(50, "Processing...")
        result = value * 2
        self.update_progress(100, "Complete")
        return {"result": result}


@task(monitor=True)
class DecoratedTestTask(BaseTask):
    """Test task with decorator."""
    
    task_type = "test.decorated"
    description = "Decorated test task"
    
    def validate_params(self, message: str, **kwargs) -> dict:
        return {"message": message}
    
    def execute_task(self, message: str, **kwargs) -> dict:
        return {"processed_message": message.upper()}


class TestTaskManagement(TestCase):
    """Test cases for task management system."""
    
    def setUp(self):
        """Set up test environment."""
        # Clear any existing tasks
        task_queue.backend.clear_queue()
        TaskResult.objects.all().delete()
        
        # Register test tasks
        task_queue.register_task(SimpleTestTask)
        
    def test_task_creation(self):
        """Test creating a task."""
        task = SimpleTestTask()
        self.assertIsNotNone(task.task_id)
        self.assertEqual(task.task_type, "test.simple")
        
    def test_task_execution(self):
        """Test executing a task."""
        task = SimpleTestTask()
        result = task.execute(value=5)
        
        self.assertEqual(result["result"], 10)
        
        # Check task result was saved
        task_result = TaskResult.objects.get(task_id=task.task_id)
        self.assertEqual(task_result.status, "completed")
        self.assertEqual(task_result.result["result"], 10)
        
    def test_task_validation_error(self):
        """Test task validation error."""
        task = SimpleTestTask()
        
        with self.assertRaises(Exception):
            task.execute(value=-1)
        
        # Check task result shows failure
        task_result = TaskResult.objects.get(task_id=task.task_id)
        self.assertEqual(task_result.status, "failed")
        self.assertIn("Validation error", task_result.error_message)
        
    @patch('core.tasks.celery_integration.execute_base_task.delay')
    def test_task_enqueue(self, mock_delay):
        """Test enqueueing a task."""
        # Mock Celery to not execute immediately
        mock_result = MagicMock()
        mock_result.id = 'test-celery-id'
        mock_result.status = 'PENDING'
        mock_delay.return_value = mock_result
        
        task_id = SimpleTestTask.create_and_run_async(value=7)
        
        self.assertIsNotNone(task_id)
        
        # Check task was created
        task_result = TaskResult.objects.get(task_id=task_id)
        self.assertEqual(task_result.status, "pending")
        self.assertEqual(task_result.parameters["value"], 7)
        
    def test_task_queue_processing(self):
        """Test processing tasks from queue."""
        # Create tasks directly and execute them
        task1 = SimpleTestTask()
        task1.execute(value=3)
        
        task2 = SimpleTestTask()
        task2.execute(value=5)
        
        # Check tasks were processed
        task_result1 = TaskResult.objects.get(task_id=task1.task_id)
        task_result2 = TaskResult.objects.get(task_id=task2.task_id)
        
        self.assertEqual(task_result1.status, "completed")
        self.assertEqual(task_result1.result["result"], 6)
        
        self.assertEqual(task_result2.status, "completed")
        self.assertEqual(task_result2.result["result"], 10)
        
    @patch('core.tasks.celery_integration.execute_base_task.delay')
    def test_make_async_decorator(self, mock_delay):
        """Test @make_async decorator."""
        # Mock Celery to not execute immediately
        mock_result = MagicMock()
        mock_result.id = 'test-celery-id'
        mock_result.status = 'PENDING'
        mock_delay.return_value = mock_result
        
        @make_async(task_type="test.multiply")
        def multiply_numbers(x: int, y: int) -> dict:
            return {"result": x * y}
        
        # Test synchronous execution
        result = multiply_numbers(3, 4)
        self.assertEqual(result["result"], 12)
        
        # Test async enqueue
        task_id = multiply_numbers.enqueue_async(5, 6)
        self.assertIsNotNone(task_id)
        
        # Check task was created
        task_result = TaskResult.objects.get(task_id=task_id)
        self.assertEqual(task_result.status, "pending")
        self.assertEqual(task_result.task_type, "test.multiply")
        
    def test_task_progress_tracking(self):
        """Test task progress tracking."""
        task = SimpleTestTask()
        task.execute(value=10)
        
        # Get task result
        task_result = TaskResult.objects.get(task_id=task.task_id)
        
        # Progress should be tracked
        self.assertEqual(task_result.progress_message, "Complete")
        
    def test_task_status_query(self):
        """Test querying task status."""
        # Create and execute task directly
        task = SimpleTestTask()
        task.execute(value=8)
        
        # Get status from database
        task_result = TaskResult.objects.get(task_id=task.task_id)
        self.assertEqual(task_result.status, "completed")
        self.assertEqual(task_result.result["result"], 16)