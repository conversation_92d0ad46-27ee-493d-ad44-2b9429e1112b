"""
Tests for logging context management.

This module tests:
- Context variable management
- Request and task context
- LoggingContextFilter
- Context propagation across async operations
"""

from unittest.mock import Mock
from django.test import TestCase

from core.logging.context import (
    request_id_var,
    user_id_var,
    task_id_var,
    task_name_var,
    extra_context_var,
    set_request_context,
    set_task_context,
    clear_request_context,
    clear_task_context,
    add_context,
    clear_extra_context,
    get_logging_context,
    LoggingContextFilter,
)


class TestLoggingContext(TestCase):
    """Test logging context management."""
    
    def setUp(self):
        """Clear all context before each test."""
        clear_request_context()
        clear_task_context()
        clear_extra_context()
    
    def test_set_request_context(self):
        """Test setting request context."""
        # Set context without user_id
        request_id = set_request_context()
        self.assertIsNotNone(request_id)
        self.assertEqual(request_id_var.get(), request_id)
        self.assertIsNone(user_id_var.get())
        
        # Set context with user_id
        request_id = set_request_context(user_id=123)
        self.assertIsNotNone(request_id)
        self.assertEqual(request_id_var.get(), request_id)
        self.assertEqual(user_id_var.get(), 123)
        
        # Set context with specific request_id
        specific_id = 'test-request-123'
        request_id = set_request_context(request_id=specific_id, user_id=456)
        self.assertEqual(request_id, specific_id)
        self.assertEqual(request_id_var.get(), specific_id)
        self.assertEqual(user_id_var.get(), 456)
    
    def test_set_task_context(self):
        """Test setting task context."""
        # Set task context without name
        set_task_context('task-123')
        self.assertEqual(task_id_var.get(), 'task-123')
        self.assertIsNone(task_name_var.get())
        
        # Set task context with name
        set_task_context('task-456', 'test_task')
        self.assertEqual(task_id_var.get(), 'task-456')
        self.assertEqual(task_name_var.get(), 'test_task')
    
    def test_clear_contexts(self):
        """Test clearing different contexts."""
        # Set all contexts
        set_request_context(request_id='req-123', user_id=789)
        set_task_context('task-123', 'test_task')
        add_context(custom_field='value')
        
        # Verify all are set
        self.assertEqual(request_id_var.get(), 'req-123')
        self.assertEqual(user_id_var.get(), 789)
        self.assertEqual(task_id_var.get(), 'task-123')
        self.assertEqual(task_name_var.get(), 'test_task')
        self.assertEqual(extra_context_var.get(), {'custom_field': 'value'})
        
        # Clear request context
        clear_request_context()
        self.assertIsNone(request_id_var.get())
        self.assertIsNone(user_id_var.get())
        # Task context should remain
        self.assertEqual(task_id_var.get(), 'task-123')
        self.assertEqual(extra_context_var.get(), {'custom_field': 'value'})
        
        # Clear task context
        clear_task_context()
        self.assertIsNone(task_id_var.get())
        self.assertIsNone(task_name_var.get())
        # Extra context should remain
        self.assertEqual(extra_context_var.get(), {'custom_field': 'value'})
        
        # Clear extra context
        clear_extra_context()
        self.assertEqual(extra_context_var.get(), {})
    
    def test_add_context(self):
        """Test adding custom context."""
        # Add initial context
        add_context(field1='value1', field2='value2')
        context = extra_context_var.get()
        self.assertEqual(context, {'field1': 'value1', 'field2': 'value2'})
        
        # Add more context (should merge)
        add_context(field3='value3', field1='updated')
        context = extra_context_var.get()
        self.assertEqual(context, {
            'field1': 'updated',  # Updated value
            'field2': 'value2',   # Preserved
            'field3': 'value3'    # New field
        })
    
    def test_get_logging_context(self):
        """Test getting complete logging context."""
        # Empty context
        context = get_logging_context()
        self.assertEqual(context, {})
        
        # Set various contexts
        set_request_context(request_id='req-123', user_id=456)
        set_task_context('task-789', 'import_task')
        add_context(environment='test', version='1.0.0')
        
        # Get complete context
        context = get_logging_context()
        self.assertEqual(context, {
            'request_id': 'req-123',
            'user_id': 456,
            'task_id': 'task-789',
            'task_name': 'import_task',
            'environment': 'test',
            'version': '1.0.0'
        })
    
    def test_logging_context_filter(self):
        """Test LoggingContextFilter."""
        filter_instance = LoggingContextFilter()
        
        # Create a mock log record
        record = Mock()
        record.request_id = None
        record.user_id = None
        record.task_id = None
        record.task_name = None
        
        # Test with empty context
        result = filter_instance.filter(record)
        self.assertTrue(result)  # Filter should always return True
        self.assertIsNone(record.request_id)
        self.assertIsNone(record.user_id)
        self.assertIsNone(record.task_id)
        self.assertIsNone(record.task_name)
        
        # Set context and test again
        set_request_context(request_id='req-123', user_id=456)
        set_task_context('task-789', 'test_task')
        add_context(custom='value')
        
        # Create new record without attributes
        record2 = Mock(spec=[])
        result = filter_instance.filter(record2)
        self.assertTrue(result)
        
        # Check attributes were added
        self.assertEqual(record2.request_id, 'req-123')
        self.assertEqual(record2.user_id, 456)
        self.assertEqual(record2.task_id, 'task-789')
        self.assertEqual(record2.task_name, 'test_task')
        self.assertEqual(record2.custom, 'value')
    
    def test_context_isolation(self):
        """Test that contexts are isolated between different flows."""
        # Simulate multiple concurrent requests/tasks
        contexts = []
        
        # First context
        set_request_context(request_id='req-1', user_id=1)
        contexts.append(get_logging_context())
        
        # Second context (should override)
        set_request_context(request_id='req-2', user_id=2)
        contexts.append(get_logging_context())
        
        # Verify second context is active
        self.assertEqual(get_logging_context(), {
            'request_id': 'req-2',
            'user_id': 2
        })
        
        # Verify contexts were different
        self.assertNotEqual(contexts[0], contexts[1])
    
    def test_uuid_generation_in_request_context(self):
        """Test that request IDs are valid UUIDs when auto-generated."""
        import uuid
        
        # Generate multiple request IDs
        request_ids = []
        for _ in range(5):
            request_id = set_request_context()
            request_ids.append(request_id)
            clear_request_context()
        
        # Verify all are valid UUIDs
        for request_id in request_ids:
            # Should not raise ValueError
            uuid_obj = uuid.UUID(request_id)
            self.assertEqual(str(uuid_obj), request_id)
        
        # Verify all are unique
        self.assertEqual(len(set(request_ids)), 5)
    
    def test_filter_preserves_existing_attributes(self):
        """Test that filter doesn't override existing record attributes."""
        filter_instance = LoggingContextFilter()
        
        # Set context
        set_request_context(request_id='context-req-123', user_id=456)
        
        # Create record with existing attributes
        record = Mock()
        record.request_id = 'existing-req-123'
        record.user_id = 789
        record.custom = 'existing'
        
        # Apply filter
        filter_instance.filter(record)
        
        # Existing attributes should be preserved
        self.assertEqual(record.request_id, 'existing-req-123')
        self.assertEqual(record.user_id, 789)
        self.assertEqual(record.custom, 'existing')
    
    def test_context_with_none_values(self):
        """Test handling of None values in context."""
        # Set context with None values
        add_context(field1=None, field2='value', field3=None)
        
        context = get_logging_context()
        # None values should be included
        self.assertIn('field1', context)
        self.assertIsNone(context['field1'])
        self.assertEqual(context['field2'], 'value')
        self.assertIn('field3', context)
        self.assertIsNone(context['field3'])