"""
Unit tests for GCS Service functionality.
"""
import pytest
from unittest.mock import Mock, patch
from django.test import override_settings
from google.cloud.exceptions import GoogleCloudError

from core.storage.gcs_service import GCSService


class TestGCSService:
    """Test GCS Service functionality."""
    
    @override_settings(GCS_BUCKET_NAME='test-bucket')
    @patch('core.storage.gcs_service.storage.Client')
    def test_init_success(self, mock_client):
        """Test successful initialization."""
        # Setup mock
        mock_bucket = Mock()
        mock_client.return_value.bucket.return_value = mock_bucket
        
        # Initialize service
        service = GCSService()
        
        # Assertions
        assert service.bucket_name == 'test-bucket'
        mock_client.assert_called_once()
        mock_client.return_value.bucket.assert_called_once_with('test-bucket')
    
    def test_init_no_bucket_name(self):
        """Test initialization fails without bucket name."""
        with override_settings(GCS_BUCKET_NAME=''):
            with pytest.raises(ValueError, match="GCS_BUCKET_NAME not configured"):
                GCSService()
    
    @override_settings(GCS_BUCKET_NAME='test-bucket')
    @patch('core.storage.gcs_service.storage.Client')
    def test_init_with_custom_bucket(self, mock_client):
        """Test initialization with custom bucket name."""
        # Setup mock
        mock_bucket = Mock()
        mock_client.return_value.bucket.return_value = mock_bucket
        
        # Initialize with custom bucket
        service = GCSService(bucket_name='custom-bucket')
        
        # Assertions
        assert service.bucket_name == 'custom-bucket'
        mock_client.return_value.bucket.assert_called_once_with('custom-bucket')
    
    @override_settings(GCS_BUCKET_NAME='test-bucket')
    @patch('core.storage.gcs_service.storage.Client')
    def test_file_validation_valid(self, mock_client):
        """Test file validation with valid files."""
        service = GCSService()
        
        # Test valid image
        service._validate_file(
            b'content', 
            'test.jpg', 
            'image/jpeg'
        )
        
        # Test valid video
        service._validate_file(
            b'video content',
            'video.mp4',
            'video/mp4'
        )
        
        # Test different image formats
        for ext, content_type in [
            ('test.png', 'image/png'),
            ('test.gif', 'image/gif'),
            ('test.webp', 'image/webp'),
            ('video.mov', 'video/quicktime')
        ]:
            service._validate_file(b'content', ext, content_type)
    
    @override_settings(GCS_BUCKET_NAME='test-bucket')
    @patch('core.storage.gcs_service.storage.Client')
    def test_file_validation_size(self, mock_client):
        """Test file size validation."""
        service = GCSService()
        
        # File too large
        with pytest.raises(ValueError, match="File too large"):
            service._validate_file(
                b'x' * (101 * 1024 * 1024),  # 101MB
                'big.jpg',
                'image/jpeg'
            )
        
        # File at limit (should pass)
        service._validate_file(
            b'x' * (100 * 1024 * 1024),  # 100MB
            'max.jpg',
            'image/jpeg'
        )
    
    @override_settings(GCS_BUCKET_NAME='test-bucket')
    @patch('core.storage.gcs_service.storage.Client')
    def test_file_validation_content_type(self, mock_client):
        """Test content type validation."""
        service = GCSService()
        
        # Invalid content types
        invalid_types = [
            ('test.exe', 'application/exe'),
            ('test.pdf', 'application/pdf'),
            ('test.doc', 'application/msword'),
            ('test.txt', 'text/plain'),
        ]
        
        for filename, content_type in invalid_types:
            with pytest.raises(ValueError, match="Content type not allowed"):
                service._validate_file(
                    b'content',
                    filename,
                    content_type
                )
    
    @override_settings(GCS_BUCKET_NAME='test-bucket')
    @patch('core.storage.gcs_service.storage.Client')
    def test_file_validation_extension(self, mock_client):
        """Test file extension validation."""
        service = GCSService()
        
        # Invalid extensions
        invalid_extensions = [
            'test.xyz',
            'test.exe',
            'test.bat',
            'test',  # no extension
            'test.JPG.exe',  # double extension trick
        ]
        
        for filename in invalid_extensions:
            with pytest.raises(ValueError, match="File extension not allowed"):
                service._validate_file(
                    b'content',
                    filename,
                    'image/jpeg'  # Even with valid content type
                )
    
    @override_settings(GCS_BUCKET_NAME='test-bucket')
    @patch('core.storage.gcs_service.storage.Client')
    def test_blob_name_generation(self, mock_client):
        """Test blob name generation."""
        service = GCSService()
        
        # Test normal filename
        blob_name = service._generate_blob_name('photo.jpg', 'instagram')
        assert blob_name.startswith('instagram/')
        assert blob_name.endswith('_photo.jpg')
        assert len(blob_name.split('_')) >= 3  # folder/timestamp_uuid_filename
        
        # Test filename with spaces
        blob_name = service._generate_blob_name('my photo.jpg', 'instagram')
        assert 'my_photo.jpg' in blob_name
        assert ' ' not in blob_name
        
        # Test filename with special characters
        blob_name = service._generate_blob_name('photo@#$.jpg', 'test')
        assert blob_name.startswith('test/')
        assert '@' not in blob_name
        assert '#' not in blob_name
        assert '$' not in blob_name
        
        # Test uniqueness
        blob1 = service._generate_blob_name('photo.jpg', 'instagram')
        blob2 = service._generate_blob_name('photo.jpg', 'instagram')
        assert blob1 != blob2  # Should be unique due to timestamp/uuid
    
    @override_settings(GCS_BUCKET_NAME='test-bucket')
    @patch('core.storage.gcs_service.storage.Client')
    def test_upload_success(self, mock_client):
        """Test successful file upload."""
        # Setup mocks
        mock_blob = Mock()
        mock_blob.public_url = 'https://storage.googleapis.com/test-bucket/images/test.jpg'
        
        mock_bucket = Mock()
        mock_bucket.blob.return_value = mock_blob
        
        mock_client.return_value.bucket.return_value = mock_bucket
        
        # Test upload
        service = GCSService()
        url = service.upload_file(
            b'test content',
            'photo.jpg',
            'image/jpeg'
        )
        
        # Assertions
        assert url == 'https://storage.googleapis.com/test-bucket/images/test.jpg'
        assert mock_bucket.blob.called
        mock_blob.upload_from_string.assert_called_once_with(
            b'test content',
            content_type='image/jpeg'
        )
        # Note: make_public is not called as bucket is already public
    
    @override_settings(GCS_BUCKET_NAME='test-bucket')
    @patch('core.storage.gcs_service.storage.Client')
    def test_upload_custom_folder(self, mock_client):
        """Test upload with custom folder."""
        # Setup mocks
        mock_blob = Mock()
        mock_blob.public_url = 'https://storage.googleapis.com/test-bucket/custom/test.jpg'
        
        mock_bucket = Mock()
        mock_bucket.blob.return_value = mock_blob
        
        mock_client.return_value.bucket.return_value = mock_bucket
        
        # Test upload with custom folder
        service = GCSService()
        service.upload_file(
            b'test content',
            'photo.jpg',
            'image/jpeg',
            folder='custom'
        )
        
        # Check that blob name includes custom folder
        blob_call_args = mock_bucket.blob.call_args[0][0]
        assert blob_call_args.startswith('custom/')
    
    @override_settings(GCS_BUCKET_NAME='test-bucket')
    @patch('core.storage.gcs_service.storage.Client')
    def test_upload_gcs_error(self, mock_client):
        """Test upload with GCS error."""
        # Setup mocks
        mock_blob = Mock()
        mock_blob.upload_from_string.side_effect = GoogleCloudError("Upload failed")
        
        mock_bucket = Mock()
        mock_bucket.blob.return_value = mock_blob
        
        mock_client.return_value.bucket.return_value = mock_bucket
        
        # Test upload failure
        service = GCSService()
        with pytest.raises(GoogleCloudError):
            service.upload_file(
                b'test content',
                'photo.jpg',
                'image/jpeg'
            )
    
    @override_settings(GCS_BUCKET_NAME='test-bucket')
    @patch('core.storage.gcs_service.storage.Client')
    def test_is_available_success(self, mock_client):
        """Test service availability check - success."""
        # Setup mocks
        mock_bucket = Mock()
        mock_bucket.reload.return_value = None
        
        mock_client.return_value.bucket.return_value = mock_bucket
        
        # Test availability
        service = GCSService()
        assert service.is_available() is True
        mock_bucket.reload.assert_called_once()
    
    @override_settings(GCS_BUCKET_NAME='test-bucket')
    @patch('core.storage.gcs_service.storage.Client')
    def test_is_available_failure(self, mock_client):
        """Test service availability check - failure."""
        # Setup mocks
        mock_bucket = Mock()
        mock_bucket.reload.side_effect = Exception("Connection failed")
        
        mock_client.return_value.bucket.return_value = mock_bucket
        
        # Test availability
        service = GCSService()
        assert service.is_available() is False
    
    @override_settings(GCS_BUCKET_NAME='test-bucket')
    @patch('core.storage.gcs_service.storage.Client')
    def test_delete_file_success(self, mock_client):
        """Test successful file deletion."""
        # Setup mocks
        mock_blob = Mock()
        mock_blob.delete.return_value = None
        
        mock_bucket = Mock()
        mock_bucket.blob.return_value = mock_blob
        
        mock_client.return_value.bucket.return_value = mock_bucket
        
        # Test deletion
        service = GCSService()
        result = service.delete_file('instagram/test.jpg')
        
        # Assertions
        assert result is True
        mock_bucket.blob.assert_called_once_with('instagram/test.jpg')
        mock_blob.delete.assert_called_once()
    
    @override_settings(GCS_BUCKET_NAME='test-bucket')
    @patch('core.storage.gcs_service.storage.Client')
    def test_delete_file_failure(self, mock_client):
        """Test file deletion failure."""
        # Setup mocks
        mock_blob = Mock()
        mock_blob.delete.side_effect = Exception("Delete failed")
        
        mock_bucket = Mock()
        mock_bucket.blob.return_value = mock_blob
        
        mock_client.return_value.bucket.return_value = mock_bucket
        
        # Test deletion failure
        service = GCSService()
        result = service.delete_file('instagram/test.jpg')
        
        # Assertions
        assert result is False