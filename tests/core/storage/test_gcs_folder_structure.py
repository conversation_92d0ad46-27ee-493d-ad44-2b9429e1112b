"""
Tests for GCS folder structure functionality.
Following TDD approach - tests written before implementation.
"""
from core.storage.gcs_service import GCSService


class TestGCSFolderStructure:
    """Test GCS folder structure based on media type."""
    
    def test_get_folder_by_media_type_for_images(self):
        """Test that images are placed in 'images' folder."""
        folder = GCSService.get_folder_by_media_type("image")
        assert folder == "images"
    
    def test_get_folder_by_media_type_for_videos(self):
        """Test that videos are placed in 'videos' folder."""
        folder = GCSService.get_folder_by_media_type("video")
        assert folder == "videos"
    
    def test_get_folder_by_media_type_for_thumbnails(self):
        """Test that thumbnails are placed in 'previews' folder."""
        folder = GCSService.get_folder_by_media_type("image", is_thumbnail=True)
        assert folder == "previews"
        
        # Video thumbnails should also go to previews
        folder = GCSService.get_folder_by_media_type("video", is_thumbnail=True)
        assert folder == "previews"
    
    def test_get_folder_by_media_type_defaults_to_images(self):
        """Test that unknown media types default to 'images' folder."""
        folder = GCSService.get_folder_by_media_type("unknown")
        assert folder == "images"
        
        folder = GCSService.get_folder_by_media_type("")
        assert folder == "images"
        
        folder = GCSService.get_folder_by_media_type(None)
        assert folder == "images"
    
    def test_get_folder_by_media_type_case_insensitive(self):
        """Test that media type is case insensitive."""
        folder = GCSService.get_folder_by_media_type("IMAGE")
        assert folder == "images"
        
        folder = GCSService.get_folder_by_media_type("Video")
        assert folder == "videos"
        
        folder = GCSService.get_folder_by_media_type("VIDEO")
        assert folder == "videos"