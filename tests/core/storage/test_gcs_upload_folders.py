"""
Tests for GCS upload with correct folder structure.
Testing that files are uploaded to correct folders based on type.
"""
from unittest.mock import MagicMock, patch
import pytest
from core.storage.gcs_service import GCSService


class TestGCSUploadWithFolders:
    """Test GCS upload places files in correct folders."""
    
    @pytest.fixture
    def mock_gcs_service(self):
        """Create a mock GCS service with mocked Google Cloud Storage."""
        with patch('core.storage.gcs_service.storage.Client'):
            service = GCSService(bucket_name="test-bucket")
            service.bucket = MagicMock()
            return service
    
    def test_upload_file_uses_images_folder_by_default(self, mock_gcs_service):
        """Test that upload_file uses 'images' folder by default."""
        mock_blob = MagicMock()
        mock_gcs_service.bucket.blob.return_value = mock_blob
        mock_blob.public_url = "https://storage.googleapis.com/test-bucket/images/test_file.jpg"
        
        mock_gcs_service.upload_file(
            file_content=b"test content",
            original_filename="test.jpg",
            content_type="image/jpeg"
        )
        
        # Check that blob was created with correct path
        args = mock_gcs_service.bucket.blob.call_args[0]
        blob_name = args[0]
        assert blob_name.startswith("images/")
        assert "test.jpg" in blob_name
    
    def test_upload_file_respects_custom_folder(self, mock_gcs_service):
        """Test that upload_file respects custom folder parameter."""
        mock_blob = MagicMock()
        mock_gcs_service.bucket.blob.return_value = mock_blob
        mock_blob.public_url = "https://storage.googleapis.com/test-bucket/videos/test_video.mp4"
        
        mock_gcs_service.upload_file(
            file_content=b"test video content",
            original_filename="test.mp4",
            content_type="video/mp4",
            folder="videos"
        )
        
        # Check that blob was created with correct path
        args = mock_gcs_service.bucket.blob.call_args[0]
        blob_name = args[0]
        assert blob_name.startswith("videos/")
        assert "test.mp4" in blob_name
    
    def test_upload_file_with_previews_folder(self, mock_gcs_service):
        """Test upload to previews folder for thumbnails."""
        mock_blob = MagicMock()
        mock_gcs_service.bucket.blob.return_value = mock_blob
        mock_blob.public_url = "https://storage.googleapis.com/test-bucket/previews/thumbnail.jpg"
        
        mock_gcs_service.upload_file(
            file_content=b"thumbnail content",
            original_filename="thumbnail.jpg",
            content_type="image/jpeg",
            folder="previews"
        )
        
        # Check that blob was created with correct path
        args = mock_gcs_service.bucket.blob.call_args[0]
        blob_name = args[0]
        assert blob_name.startswith("previews/")
        assert "thumbnail.jpg" in blob_name
    
    def test_blob_name_format_with_folder(self, mock_gcs_service):
        """Test that blob name maintains correct format with folder."""
        mock_blob = MagicMock()
        mock_gcs_service.bucket.blob.return_value = mock_blob
        
        mock_gcs_service.upload_file(
            file_content=b"content",
            original_filename="test_file.jpg",
            content_type="image/jpeg",
            folder="images"
        )
        
        # Get the blob name that was used
        args = mock_gcs_service.bucket.blob.call_args[0]
        blob_name = args[0]
        
        # Check format: folder/timestamp_uuid_filename
        parts = blob_name.split("/")
        assert len(parts) == 2
        assert parts[0] == "images"
        
        # Check filename format
        filename_parts = parts[1].split("_")
        assert len(filename_parts) >= 3  # timestamp_uuid_filename
        # The filename might have underscores converted or removed
        assert "test" in parts[1] and "file.jpg" in parts[1]