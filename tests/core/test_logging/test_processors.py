"""
Tests for structlog processors.

These tests follow TDD approach - they will fail until implementation is complete.
"""

from unittest.mock import Mock, patch

from core.logging.processors import add_app_context, filter_sensitive_keys


class TestAddAppContext:
    """Test cases for add_app_context processor."""
    
    def test_adds_app_name_to_event_dict(self):
        """Test that app name is added to event dict."""
        logger = Mock()
        method_name = "info"
        event_dict = {"event": "test_event", "level": "info"}
        
        # Process event
        result = add_app_context(logger, method_name, event_dict)
        
        # Should add app_name
        assert "app_name" in result
        assert result["app_name"] == "socialmanager"  # Or from settings
        
    def test_adds_environment_to_event_dict(self):
        """Test that environment is added to event dict."""
        logger = Mock()
        method_name = "info"
        event_dict = {"event": "test_event", "level": "info"}
        
        # Process event
        result = add_app_context(logger, method_name, event_dict)
        
        # Should add environment
        assert "environment" in result
        # Should be from Django settings.DEBUG or settings.ENVIRONMENT
        
    def test_adds_request_context_if_available(self):
        """Test that request context is added when available."""
        logger = Mock()
        method_name = "info"
        event_dict = {"event": "test_event", "level": "info"}
        
        # Mock request context
        with patch('core.logging.get_context') as mock_get_context:
            mock_get_context.return_value = {
                "request_id": "req-123",
                "user_id": 456,
                "path": "/api/users/"
            }
            
            result = add_app_context(logger, method_name, event_dict)
            
            # Should include request context
            assert "request_id" in result
            assert result["request_id"] == "req-123"
            assert "user_id" in result
            assert result["user_id"] == 456
            assert "path" in result
            assert result["path"] == "/api/users/"
            
    def test_adds_task_context_if_available(self):
        """Test that Celery task context is added when available."""
        logger = Mock()
        method_name = "info"
        event_dict = {"event": "test_event", "level": "info"}
        
        # Mock being in a Celery task
        with patch('celery.current_task') as mock_task:
            mock_task.request.id = "task-789"
            mock_task.name = "process_instagram_import"
            
            result = add_app_context(logger, method_name, event_dict)
            
            # Should include task context
            assert "task_id" in result
            assert result["task_id"] == "task-789"
            assert "task_name" in result
            assert result["task_name"] == "process_instagram_import"
            
    def test_adds_timestamp_if_not_present(self):
        """Test that timestamp is added if not already present."""
        logger = Mock()
        method_name = "info"
        event_dict = {"event": "test_event", "level": "info"}
        
        result = add_app_context(logger, method_name, event_dict)
        
        # Should add timestamp
        assert "timestamp" in result
        
    def test_preserves_existing_timestamp(self):
        """Test that existing timestamp is preserved."""
        logger = Mock()
        method_name = "info"
        existing_timestamp = "2024-01-01T12:00:00Z"
        event_dict = {
            "event": "test_event",
            "level": "info",
            "timestamp": existing_timestamp
        }
        
        result = add_app_context(logger, method_name, event_dict)
        
        # Should preserve existing timestamp
        assert result["timestamp"] == existing_timestamp
        
    def test_does_not_override_existing_fields(self):
        """Test that processor doesn't override existing fields."""
        logger = Mock()
        method_name = "info"
        event_dict = {
            "event": "test_event",
            "level": "info",
            "app_name": "custom_app",
            "environment": "staging"
        }
        
        result = add_app_context(logger, method_name, event_dict)
        
        # Should not override existing values
        assert result["app_name"] == "custom_app"
        assert result["environment"] == "staging"


class TestFilterSensitiveKeys:
    """Test cases for filter_sensitive_keys processor."""
    
    def test_filters_password_fields(self):
        """Test that password fields are filtered."""
        logger = Mock()
        method_name = "info"
        event_dict = {
            "event": "user_login",
            "password": "secret123",
            "user": "john"
        }
        
        result = filter_sensitive_keys(logger, method_name, event_dict)
        
        # Password should be filtered
        assert result["password"] != "secret123"
        assert result["password"] in ["[REDACTED]", "***", "[FILTERED]"]
        # Other fields preserved
        assert result["user"] == "john"
        
    def test_filters_token_fields(self):
        """Test that token fields are filtered."""
        logger = Mock()
        method_name = "info"
        event_dict = {
            "event": "api_call",
            "token": "abc123xyz",
            "access_token": "bearer-xyz",
            "refresh_token": "refresh-123",
            "endpoint": "/api/users"
        }
        
        result = filter_sensitive_keys(logger, method_name, event_dict)
        
        # All token fields should be filtered
        assert result["token"] not in ["abc123xyz", "abc123xyz"]
        assert result["access_token"] != "bearer-xyz"
        assert result["refresh_token"] != "refresh-123"
        # Other fields preserved
        assert result["endpoint"] == "/api/users"
        
    def test_filters_api_key_fields(self):
        """Test that API key fields are filtered."""
        logger = Mock()
        method_name = "info"
        event_dict = {
            "event": "external_api_call",
            "api_key": "sk-1234567890",
            "apikey": "key-abc123",
            "api_secret": "secret-xyz",
            "service": "brightdata"
        }
        
        result = filter_sensitive_keys(logger, method_name, event_dict)
        
        # All API key fields should be filtered
        assert result["api_key"] != "sk-1234567890"
        assert result["apikey"] != "key-abc123"
        assert result["api_secret"] != "secret-xyz"
        # Other fields preserved
        assert result["service"] == "brightdata"
        
    def test_filters_nested_sensitive_data(self):
        """Test that nested sensitive data is filtered."""
        logger = Mock()
        method_name = "info"
        event_dict = {
            "event": "request_processed",
            "request_data": {
                "username": "john",
                "password": "secret123",
                "profile": {
                    "email": "<EMAIL>",
                    "api_token": "token-xyz"
                }
            }
        }
        
        result = filter_sensitive_keys(logger, method_name, event_dict)
        
        # Nested sensitive data should be filtered
        assert result["request_data"]["password"] != "secret123"
        assert result["request_data"]["profile"]["api_token"] != "token-xyz"
        # Other data preserved
        assert result["request_data"]["username"] == "john"
        assert result["request_data"]["profile"]["email"] == "<EMAIL>"
        
    def test_case_insensitive_filtering(self):
        """Test that filtering is case-insensitive."""
        logger = Mock()
        method_name = "info"
        event_dict = {
            "event": "test",
            "PASSWORD": "secret123",
            "Token": "abc123",
            "API_KEY": "key-xyz"
        }
        
        result = filter_sensitive_keys(logger, method_name, event_dict)
        
        # Case variations should all be filtered
        assert result["PASSWORD"] != "secret123"
        assert result["Token"] != "abc123"
        assert result["API_KEY"] != "key-xyz"
        
    def test_filters_sensitive_patterns_in_keys(self):
        """Test that keys with sensitive patterns are filtered."""
        logger = Mock()
        method_name = "info"
        event_dict = {
            "event": "test",
            "user_password": "secret123",
            "password_hash": "hash123",
            "old_password": "oldsecret",
            "token_value": "token123",
            "api_key_primary": "key123"
        }
        
        result = filter_sensitive_keys(logger, method_name, event_dict)
        
        # All keys containing sensitive patterns should be filtered
        assert result["user_password"] != "secret123"
        assert result["password_hash"] != "hash123"
        assert result["old_password"] != "oldsecret"
        assert result["token_value"] != "token123"
        assert result["api_key_primary"] != "key123"
        
    def test_handles_non_dict_values_gracefully(self):
        """Test that non-dict values are handled properly."""
        logger = Mock()
        method_name = "info"
        event_dict = {
            "event": "test",
            "password": "secret123",
            "data": ["item1", "item2"],  # List
            "count": 42,  # Integer
            "flag": True,  # Boolean
            "nested": {
                "password": "nested_secret",
                "items": [1, 2, 3]
            }
        }
        
        result = filter_sensitive_keys(logger, method_name, event_dict)
        
        # Should filter sensitive data
        assert result["password"] != "secret123"
        assert result["nested"]["password"] != "nested_secret"
        # Should preserve non-dict values
        assert result["data"] == ["item1", "item2"]
        assert result["count"] == 42
        assert result["flag"] is True
        assert result["nested"]["items"] == [1, 2, 3]
        
    def test_preserves_event_dict_structure(self):
        """Test that the overall structure is preserved."""
        logger = Mock()
        method_name = "info"
        event_dict = {
            "event": "complex_event",
            "level": "info",
            "timestamp": "2024-01-01T12:00:00Z",
            "data": {
                "action": "user_login",
                "password": "secret123"
            },
            "metadata": {
                "source": "web",
                "ip": "***********"
            }
        }
        
        result = filter_sensitive_keys(logger, method_name, event_dict)
        
        # Structure should be preserved
        assert "event" in result
        assert "level" in result
        assert "timestamp" in result
        assert "data" in result
        assert "metadata" in result
        # Only sensitive data filtered
        assert result["data"]["password"] != "secret123"
        assert result["data"]["action"] == "user_login"
        assert result["metadata"] == event_dict["metadata"]