"""
Helper utilities for integration tests.

This module provides utilities for setting up test environments,
mocking external services, and common test scenarios.
"""

import logging
import time
from contextlib import contextmanager
from typing import Any, Dict, List
from unittest.mock import Mock, patch

import sentry_sdk



def create_test_settings(**overrides) -> Dict[str, Any]:
    """
    Create test settings with common defaults.
    
    Args:
        **overrides: Settings to override
        
    Returns:
        Settings dictionary
    """
    base_settings = {
        'DEBUG': True,
        'TESTING': True,
        'CELERY_TASK_ALWAYS_EAGER': True,
        'CELERY_TASK_EAGER_PROPAGATES': True,
        'SENTRY_DSN': None,  # Disable Sentry by default in tests
        'LOGGING': {
            'version': 1,
            'disable_existing_loggers': False,
            'formatters': {
                'test': {
                    'format': '[%(levelname)s] %(name)s: %(message)s'
                },
            },
            'handlers': {
                'console': {
                    'class': 'logging.StreamHandler',
                    'formatter': 'test',
                },
            },
            'root': {
                'handlers': ['console'],
                'level': 'DEBUG',
            },
            'loggers': {
                'django.db.backends': {
                    'level': 'WARNING',
                },
            },
        },
    }
    
    # Apply overrides
    base_settings.update(overrides)
    
    return base_settings


@contextmanager
def capture_logs(logger_name: str = None, level: int = logging.DEBUG):
    """
    Context manager to capture log messages.
    
    Args:
        logger_name: Logger name to capture (None for root)
        level: Minimum level to capture
        
    Yields:
        List of captured log records
    """
    records = []
    
    class TestHandler(logging.Handler):
        def emit(self, record):
            records.append(record)
    
    handler = TestHandler()
    handler.setLevel(level)
    
    logger = logging.getLogger(logger_name)
    logger.addHandler(handler)
    original_level = logger.level
    logger.setLevel(level)
    
    try:
        yield records
    finally:
        logger.removeHandler(handler)
        logger.setLevel(original_level)


@contextmanager
def mock_sentry_capture():
    """
    Context manager to mock Sentry event capture.
    
    Yields:
        Tuple of (events, breadcrumbs) lists
    """
    events = []
    breadcrumbs = []
    
    def capture_event(event, hint=None):
        events.append(event)
        return event.get('event_id', 'test-event-id')
    
    def add_breadcrumb(breadcrumb, hint=None):
        breadcrumbs.append(breadcrumb)
    
    with patch('sentry_sdk.capture_event', side_effect=capture_event):
        with patch('sentry_sdk.add_breadcrumb', side_effect=add_breadcrumb):
            yield events, breadcrumbs


def setup_test_sentry(dsn: str = 'https://<EMAIL>/123'):
    """
    Set up Sentry SDK for testing.
    
    Args:
        dsn: Test DSN to use
    """
    sentry_sdk.init(
        dsn=dsn,
        environment='test',
        debug=True,
        integrations=[],  # No integrations for cleaner tests
        before_send=lambda event, hint: event,  # Don't actually send
        traces_sample_rate=1.0,
    )


def create_test_request(path: str = '/test/', 
                       method: str = 'GET',
                       user=None,
                       **kwargs) -> Mock:
    """
    Create a mock request for testing.
    
    Args:
        path: Request path
        method: HTTP method
        user: User object
        **kwargs: Additional request attributes
        
    Returns:
        Mock request object
    """
    request = Mock()
    request.path = path
    request.method = method
    request.user = user or Mock(is_authenticated=False)
    request.META = {
        'REMOTE_ADDR': '127.0.0.1',
        'HTTP_USER_AGENT': 'TestClient/1.0',
        'HTTP_HOST': 'testserver',
    }
    request.META.update(kwargs.get('META', {}))
    
    # Set additional attributes
    for key, value in kwargs.items():
        if key != 'META':
            setattr(request, key, value)
    
    return request


def assert_log_contains(records: List[logging.LogRecord],
                       message: str = None,
                       level: int = None,
                       **kwargs) -> bool:
    """
    Assert that log records contain expected content.
    
    Args:
        records: Log records to check
        message: Expected message substring
        level: Expected log level
        **kwargs: Expected extra fields
        
    Returns:
        True if matching record found
        
    Raises:
        AssertionError: If no matching record found
    """
    for record in records:
        # Check message
        if message and message not in record.getMessage():
            continue
            
        # Check level
        if level is not None and record.levelno != level:
            continue
            
        # Check extra fields
        matches = True
        for key, expected in kwargs.items():
            actual = getattr(record, key, None)
            if actual != expected:
                matches = False
                break
        
        if matches:
            return True
    
    # Build error message
    conditions = []
    if message:
        conditions.append(f"message containing '{message}'")
    if level is not None:
        conditions.append(f"level={logging.getLevelName(level)}")
    for key, value in kwargs.items():
        conditions.append(f"{key}={value}")
    
    raise AssertionError(
        f"No log record found with {', '.join(conditions)}. "
        f"Found {len(records)} records."
    )


def simulate_concurrent_requests(view_func, 
                               num_requests: int = 10,
                               delay: float = 0.01) -> List[Any]:
    """
    Simulate concurrent requests to test context isolation.
    
    Args:
        view_func: View function to call
        num_requests: Number of concurrent requests
        delay: Delay between requests (seconds)
        
    Returns:
        List of responses
    """
    import threading
    
    responses = []
    errors = []
    
    def make_request(request_id):
        try:
            request = create_test_request(
                path=f'/test/{request_id}/',
                id=f'req-{request_id}'
            )
            response = view_func(request)
            responses.append((request_id, response))
        except Exception as e:
            errors.append((request_id, e))
    
    threads = []
    for i in range(num_requests):
        thread = threading.Thread(target=make_request, args=(i,))
        threads.append(thread)
        thread.start()
        if delay:
            time.sleep(delay)
    
    # Wait for all threads
    for thread in threads:
        thread.join()
    
    if errors:
        raise Exception(f"Errors in concurrent requests: {errors}")
    
    # Sort by request ID
    responses.sort(key=lambda x: x[0])
    return [r[1] for r in responses]


class SentryEventMatcher:
    """Helper class for matching Sentry events in tests."""
    
    def __init__(self, event: Dict[str, Any]):
        self.event = event
    
    def has_tag(self, key: str, value: Any = None) -> bool:
        """Check if event has a tag."""
        tags = self.event.get('tags', {})
        if value is None:
            return key in tags
        return tags.get(key) == value
    
    def has_context(self, key: str, **kwargs) -> bool:
        """Check if event has context with specific values."""
        contexts = self.event.get('contexts', {})
        context = contexts.get(key, {})
        
        for field, expected in kwargs.items():
            if context.get(field) != expected:
                return False
        
        return True
    
    def has_extra(self, key: str, value: Any = None) -> bool:
        """Check if event has extra data."""
        extra = self.event.get('extra', {})
        if value is None:
            return key in extra
        return extra.get(key) == value
    
    def has_breadcrumb(self, message: str = None, 
                      category: str = None,
                      type: str = None) -> bool:
        """Check if event has matching breadcrumb."""
        breadcrumbs = self.event.get('breadcrumbs', {}).get('values', [])
        
        for crumb in breadcrumbs:
            if message and crumb.get('message') != message:
                continue
            if category and crumb.get('category') != category:
                continue
            if type and crumb.get('type') != type:
                continue
            return True
        
        return False


def wait_for_condition(condition_func, timeout: float = 5.0, 
                      interval: float = 0.1) -> bool:
    """
    Wait for a condition to become true.
    
    Args:
        condition_func: Function that returns True when condition is met
        timeout: Maximum time to wait (seconds)
        interval: Check interval (seconds)
        
    Returns:
        True if condition was met, False if timeout
    """
    start_time = time.time()
    
    while time.time() - start_time < timeout:
        if condition_func():
            return True
        time.sleep(interval)
    
    return False