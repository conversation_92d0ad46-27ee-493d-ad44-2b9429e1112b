"""
Comprehensive unit tests for RateLimitFilter family.

Tests cover all filter variants: basic, burst, sampling, logger-specific,
factory functions, edge cases, performance, and thread safety.
"""

import pytest
import logging
import time
from unittest.mock import patch
from concurrent.futures import ThreadPoolExecutor, as_completed
from collections import deque

from core.logging.filters import (
    RateLimitFilter,
    BurstRateLimitFilter,
    SamplingRateLimitFilter,
    LoggerSpecificRateLimitFilter,
    create_rate_limit_filter,
    create_strict_rate_limiter,
    create_lenient_rate_limiter,
    create_burst_limiter,
    create_sampling_limiter,
    LogMessage,
    MessageStats
)


@pytest.fixture
def log_record():
    """Create a basic log record for testing."""
    def _create_record(
        msg="Test message", 
        level=logging.INFO, 
        logger_name="test.logger",
        **kwargs
    ):
        record = logging.LogRecord(
            name=logger_name,
            level=level,
            pathname="test.py",
            lineno=10,
            msg=msg,
            args=(),
            exc_info=None
        )
        # Add any extra attributes
        for key, value in kwargs.items():
            setattr(record, key, value)
        return record
    return _create_record


@pytest.fixture
def mock_time():
    """Mock time.time() for deterministic testing."""
    with patch('core.logging.filters.time.time') as mock:
        mock.return_value = 0.0
        yield mock


class TestLogMessage:
    """Test LogMessage class."""
    
    def test_from_record_basic(self, log_record):
        """Test creating LogMessage from record."""
        record = log_record("Test message", logging.INFO, "test.logger")
        log_msg = LogMessage.from_record(record)
        
        assert log_msg.logger_name == "test.logger"
        assert log_msg.level == logging.INFO
        assert log_msg.message == "Test message"
    
    def test_from_record_with_args(self, log_record):
        """Test LogMessage with formatted message."""
        record = logging.LogRecord(
            name="test.logger",
            level=logging.ERROR,
            pathname="test.py",
            lineno=10,
            msg="Error %s: %d",
            args=("DB", 500),
            exc_info=None
        )
        log_msg = LogMessage.from_record(record)
        
        assert log_msg.message == "Error DB: 500"
    
    def test_get_hash_consistency(self, log_record):
        """Test that same messages produce same hash."""
        record1 = log_record("Test message", logging.INFO, "test.logger")
        record2 = log_record("Test message", logging.INFO, "test.logger")
        
        msg1 = LogMessage.from_record(record1)
        msg2 = LogMessage.from_record(record2)
        
        assert msg1.get_hash() == msg2.get_hash()
    
    def test_get_hash_different_messages(self, log_record):
        """Test that different messages produce different hashes."""
        record1 = log_record("Message 1", logging.INFO)
        record2 = log_record("Message 2", logging.INFO)
        
        msg1 = LogMessage.from_record(record1)
        msg2 = LogMessage.from_record(record2)
        
        assert msg1.get_hash() != msg2.get_hash()
    
    def test_get_hash_different_levels(self, log_record):
        """Test that same message with different levels have different hashes."""
        record1 = log_record("Test message", logging.INFO)
        record2 = log_record("Test message", logging.ERROR)
        
        msg1 = LogMessage.from_record(record1)
        msg2 = LogMessage.from_record(record2)
        
        assert msg1.get_hash() != msg2.get_hash()


class TestMessageStats:
    """Test MessageStats class."""
    
    def test_init_defaults(self):
        """Test MessageStats initialization."""
        stats = MessageStats()
        
        assert stats.count == 0
        assert stats.suppressed_count == 0
        assert stats.first_seen > 0
        assert stats.last_seen > 0
    
    def test_update_allowed(self):
        """Test updating stats for allowed message."""
        stats = MessageStats()
        initial_time = stats.last_seen
        
        time.sleep(0.01)  # Small delay
        stats.update(suppressed=False)
        
        assert stats.count == 1
        assert stats.suppressed_count == 0
        assert stats.last_seen > initial_time
    
    def test_update_suppressed(self):
        """Test updating stats for suppressed message."""
        stats = MessageStats()
        stats.update(suppressed=True)
        
        assert stats.count == 0
        assert stats.suppressed_count == 1
    
    def test_get_summary_no_suppression(self):
        """Test summary when no messages suppressed."""
        stats = MessageStats()
        summary = stats.get_summary()
        
        assert summary == ""
    
    def test_get_summary_with_suppression(self):
        """Test summary with suppressed messages."""
        stats = MessageStats()
        stats.update(suppressed=True)
        stats.update(suppressed=True)
        
        summary = stats.get_summary()
        assert "Suppressed 2 similar messages" in summary
        assert "in the last" in summary


class TestRateLimitFilter:
    """Test basic RateLimitFilter functionality."""
    
    def test_init_default_params(self):
        """Test filter initialization with default parameters."""
        filter_obj = RateLimitFilter()
        
        assert filter_obj.rate == 1
        assert filter_obj.per == 60.0
        assert filter_obj.burst == 1
        assert filter_obj.max_cache_size == 10000
        assert filter_obj.cleanup_interval == 300
        assert filter_obj.rate_limit_seconds == 60.0  # Backward compatibility
    
    def test_init_custom_params(self):
        """Test filter initialization with custom parameters."""
        filter_obj = RateLimitFilter(
            rate=5,
            per=30.0,
            burst=3,
            max_cache_size=5000,
            cleanup_interval=600
        )
        
        assert filter_obj.rate == 5
        assert filter_obj.per == 30.0
        assert filter_obj.burst == 3
        assert filter_obj.max_cache_size == 5000
        assert filter_obj.cleanup_interval == 600
    
    def test_backward_compatibility_rate_limit_seconds(self):
        """Test backward compatibility with rate_limit_seconds parameter."""
        filter_obj = RateLimitFilter(rate_limit_seconds=120)
        
        assert filter_obj.per == 120
        assert filter_obj.rate == 1  # Default for backward compatibility
        assert filter_obj.rate_limit_seconds == 120
    
    def test_first_message_allowed(self, log_record):
        """Test that first message is always allowed."""
        filter_obj = RateLimitFilter(rate=1, per=60)
        record = log_record("Test message")
        
        assert filter_obj.filter(record) is True
    
    def test_duplicate_message_blocked(self, log_record):
        """Test that duplicate messages are blocked within rate limit."""
        filter_obj = RateLimitFilter(rate=1, per=60)
        
        # First message allowed
        record1 = log_record("Test message")
        assert filter_obj.filter(record1) is True
        
        # Duplicate message blocked
        record2 = log_record("Test message")
        assert filter_obj.filter(record2) is False
    
    def test_multiple_rate_within_burst(self, log_record):
        """Test that multiple identical messages are allowed within burst limit."""
        filter_obj = RateLimitFilter(rate=2, per=60, burst=3)
        
        # Should allow rate + burst - 1 = 4 identical messages
        for i in range(4):
            record = log_record("Same message")
            assert filter_obj.filter(record) is True
        
        # Fifth identical message should be blocked
        record = log_record("Same message")
        assert filter_obj.filter(record) is False
    
    @patch('core.logging.filters.time.time')
    def test_message_allowed_after_time_window(self, mock_time, log_record):
        """Test that messages are allowed after time window expires."""
        mock_time.return_value = 0
        filter_obj = RateLimitFilter(rate=1, per=60)
        
        # First message at time 0
        record1 = log_record("Test message")
        assert filter_obj.filter(record1) is True
        
        # Same message at time 0 should be blocked
        record2 = log_record("Test message") 
        assert filter_obj.filter(record2) is False
        
        # Move time forward past the window
        mock_time.return_value = 61
        record3 = log_record("Test message")
        assert filter_obj.filter(record3) is True
    
    def test_different_messages_not_rate_limited(self, log_record):
        """Test that different messages are not rate limited."""
        filter_obj = RateLimitFilter(rate=1, per=60)
        
        record1 = log_record("Message 1")
        assert filter_obj.filter(record1) is True
        
        record2 = log_record("Message 2")
        assert filter_obj.filter(record2) is True
    
    def test_different_log_levels_treated_separately(self, log_record):
        """Test that same message with different levels treated separately."""
        filter_obj = RateLimitFilter(rate=1, per=60)
        
        record1 = log_record("Test message", logging.INFO)
        assert filter_obj.filter(record1) is True
        
        record2 = log_record("Test message", logging.ERROR)
        assert filter_obj.filter(record2) is True
    
    def test_different_loggers_treated_separately(self, log_record):
        """Test that same message from different loggers treated separately."""
        filter_obj = RateLimitFilter(rate=1, per=60)
        
        record1 = log_record("Test message", logger_name="logger1")
        assert filter_obj.filter(record1) is True
        
        record2 = log_record("Test message", logger_name="logger2")
        assert filter_obj.filter(record2) is True
    
    @patch('core.logging.filters.time.time')
    def test_cleanup_old_entries(self, mock_time, log_record):
        """Test that old entries are cleaned up."""
        mock_time.return_value = 0
        filter_obj = RateLimitFilter(rate=1, per=60, cleanup_interval=300)
        
        # Add multiple unique messages
        for i in range(10):
            mock_time.return_value = i
            record = log_record(f"Message {i}")
            filter_obj.filter(record)
        
        # Move time forward significantly to trigger cleanup
        mock_time.return_value = 400
        record = log_record("New message")
        filter_obj.filter(record)
        
        # Old messages should be cleaned up
        assert len(filter_obj._messages) < 10
    
    def test_cache_size_limit_eviction(self, log_record):
        """Test LRU eviction when cache size limit is reached."""
        filter_obj = RateLimitFilter(max_cache_size=5)
        
        # Add messages up to cache limit
        for i in range(6):  # One more than limit
            record = log_record(f"Message {i}")
            filter_obj.filter(record)
        
        # Cache should not exceed max size
        assert len(filter_obj._messages) <= 5
    
    def test_get_summary(self, log_record):
        """Test getting rate limiting summary."""
        filter_obj = RateLimitFilter(rate=1, per=60)
        
        # Allow one message
        record1 = log_record("Test message")
        filter_obj.filter(record1)
        
        # Block one message
        record2 = log_record("Test message")
        filter_obj.filter(record2)
        
        summary = filter_obj.get_summary()
        assert summary["total_messages"] == 1
        assert summary["total_suppressed"] == 1
        assert summary["unique_messages"] == 1
        assert summary["suppression_rate"] == 0.5


class TestBurstRateLimitFilter:
    """Test BurstRateLimitFilter with token bucket algorithm."""
    
    def test_init_default_params(self):
        """Test initialization with default parameters."""
        filter_obj = BurstRateLimitFilter()
        
        assert filter_obj.rate == 10
        assert filter_obj.per == 60.0
        assert filter_obj.burst == 20
    
    def test_token_bucket_initialization(self, log_record):
        """Test that new messages get full token bucket."""
        filter_obj = BurstRateLimitFilter(rate=10, per=60, burst=20)
        
        record = log_record("New message")
        result = filter_obj.filter(record)
        
        assert result is True
        # Check that bucket was initialized
        message_key = filter_obj._get_message_key(record)
        assert message_key in filter_obj._tokens
        assert filter_obj._tokens[message_key] == 19.0  # 20 - 1 consumed
    
    def test_burst_capacity(self, log_record):
        """Test that burst capacity allows multiple rapid messages."""
        filter_obj = BurstRateLimitFilter(rate=1, per=60, burst=5)
        
        # Should allow burst number of messages rapidly
        for i in range(5):
            record = log_record("Burst message")
            assert filter_obj.filter(record) is True
        
        # Next message should be blocked (no tokens left)
        record = log_record("Burst message")
        assert filter_obj.filter(record) is False
    
    @patch('core.logging.filters.time.time')
    def test_token_regeneration(self, mock_time, log_record):
        """Test that tokens are regenerated over time."""
        mock_time.return_value = 0
        filter_obj = BurstRateLimitFilter(rate=60, per=60, burst=5)  # 1 token/second
        
        # Consume all tokens
        for i in range(5):
            record = log_record("Token test")
            filter_obj.filter(record)
        
        # Should be blocked
        record = log_record("Token test")
        assert filter_obj.filter(record) is False
        
        # Move time forward by 1 second to regenerate 1 token
        mock_time.return_value = 1
        record = log_record("Token test")
        assert filter_obj.filter(record) is True
        
        # Should be blocked again
        record = log_record("Token test")
        assert filter_obj.filter(record) is False
    
    @patch('core.logging.filters.time.time')
    def test_token_cap_at_burst_size(self, mock_time, log_record):
        """Test that tokens don't exceed burst size."""
        mock_time.return_value = 0
        filter_obj = BurstRateLimitFilter(rate=60, per=60, burst=3)
        
        # Let a lot of time pass without using tokens
        mock_time.return_value = 1000
        
        # Should still only allow burst number of rapid messages
        for i in range(3):
            record = log_record("Cap test")
            assert filter_obj.filter(record) is True
        
        # Fourth message should be blocked
        record = log_record("Cap test")
        assert filter_obj.filter(record) is False
    
    def test_cleanup_token_bucket_state(self, log_record):
        """Test that token bucket state is cleaned up."""
        filter_obj = BurstRateLimitFilter(cleanup_interval=1)  # Short cleanup interval
        
        # Start at time 0
        with patch('core.logging.filters.time.time', return_value=0):
            record = log_record("Test message")
            result = filter_obj.filter(record)
            assert result is True  # First message should pass
        
        # Tokens should be initialized after filtering
        message_key = filter_obj._get_message_key(record)
        assert message_key in filter_obj._tokens
        assert message_key in filter_obj._last_update
        
        # Simulate time passage beyond cleanup interval (1 second) and trigger cleanup
        with patch('core.logging.filters.time.time', return_value=2):
            filter_obj._cleanup()
        
        # Old token state should be cleaned up after cleanup interval
        assert message_key not in filter_obj._tokens
        assert message_key not in filter_obj._last_update


class TestSamplingRateLimitFilter:
    """Test SamplingRateLimitFilter functionality."""
    
    def test_init_default_params(self):
        """Test initialization with default parameters."""
        filter_obj = SamplingRateLimitFilter()
        
        assert filter_obj.rate == 10
        assert filter_obj.per == 60.0
        assert filter_obj.sample_rate == 0.1
    
    def test_init_sample_rate_clamping(self):
        """Test that sample rate is clamped to valid range."""
        filter_obj1 = SamplingRateLimitFilter(sample_rate=-0.5)
        assert filter_obj1.sample_rate == 0.0
        
        filter_obj2 = SamplingRateLimitFilter(sample_rate=1.5)
        assert filter_obj2.sample_rate == 1.0
    
    def test_messages_under_limit_not_sampled(self, log_record):
        """Test that messages under rate limit are not sampled."""
        filter_obj = SamplingRateLimitFilter(rate=3, per=60, sample_rate=0.5)
        
        # First few messages should pass without sampling
        for i in range(3):
            record = log_record(f"Message {i}")
            result = filter_obj.filter(record)
            assert result is True
            # Should not contain sampling marker
            assert "[SAMPLED:" not in record.getMessage()
    
    def test_sampling_when_rate_exceeded(self, log_record):
        """Test sampling behavior when rate limit is exceeded."""
        filter_obj = SamplingRateLimitFilter(rate=1, per=60, sample_rate=0.5)  # Sample 1 in 2
        
        # First message passes normally
        record1 = log_record("Test message")
        assert filter_obj.filter(record1) is True
        
        # Subsequent messages should be sampled
        sampled_count = 0
        blocked_count = 0
        
        for i in range(10):
            record = log_record("Test message")
            if filter_obj.filter(record):
                sampled_count += 1
                assert "[SAMPLED:" in record.getMessage()
            else:
                blocked_count += 1
        
        # Should have roughly 50% sampling (some variance expected)
        assert sampled_count > 0
        assert blocked_count > 0
    
    def test_zero_sample_rate_blocks_all(self, log_record):
        """Test that zero sample rate blocks all messages after limit."""
        filter_obj = SamplingRateLimitFilter(rate=1, per=60, sample_rate=0.0)
        
        # First message passes
        record1 = log_record("Test message")
        assert filter_obj.filter(record1) is True
        
        # All subsequent messages should be blocked
        for i in range(5):
            record = log_record("Test message")
            assert filter_obj.filter(record) is False
    
    def test_sample_counter_reset_under_limit(self, log_record, mock_time):
        """Test that sample counter resets when back under limit."""
        mock_time.return_value = 0
        filter_obj = SamplingRateLimitFilter(rate=1, per=60, sample_rate=0.5)
        
        # Exceed rate limit
        record1 = log_record("Test message")
        filter_obj.filter(record1)
        
        # Build up sample counter
        for i in range(5):
            record = log_record("Test message")
            filter_obj.filter(record)
        
        # Move time forward to reset rate limit
        mock_time.return_value = 61
        record = log_record("Test message")
        filter_obj.filter(record)
        
        # Sample counter should be reset
        message_key = filter_obj._get_message_key(record)
        assert filter_obj._sample_counters[message_key] == 0
    
    def test_cleanup_sample_counters(self, log_record):
        """Test that sample counters are cleaned up with old messages."""
        filter_obj = SamplingRateLimitFilter()
        
        record = log_record("Test message")
        filter_obj.filter(record)
        
        message_key = filter_obj._get_message_key(record)
        # Force some sampling state
        filter_obj._sample_counters[message_key] = 5
        
        # Manually trigger cleanup
        with patch('core.logging.filters.time.time', return_value=1000):
            filter_obj._cleanup()
        
        # Sample counter should be cleaned up if message was removed
        # (The exact cleanup logic depends on implementation)


class TestLoggerSpecificRateLimitFilter:
    """Test LoggerSpecificRateLimitFilter functionality."""
    
    def test_init_default_params(self):
        """Test initialization with default parameters."""
        filter_obj = LoggerSpecificRateLimitFilter()
        
        assert filter_obj.default_rate == 10
        assert filter_obj.default_per == 60.0
        assert filter_obj.logger_configs == {}
    
    def test_init_with_logger_configs(self):
        """Test initialization with logger configurations."""
        configs = {
            'requests': {'rate': 5, 'per': 30.0},
            'urllib3': {'rate': 1, 'per': 300.0, 'burst': 2}
        }
        filter_obj = LoggerSpecificRateLimitFilter(logger_configs=configs)
        
        assert filter_obj.logger_configs == configs
    
    def test_default_config_for_unknown_logger(self, log_record):
        """Test that unknown loggers use default configuration."""
        filter_obj = LoggerSpecificRateLimitFilter(default_rate=5, default_per=30)
        
        config = filter_obj._get_logger_config("unknown.logger")
        assert config['rate'] == 5
        assert config['per'] == 30
    
    def test_exact_logger_match(self, log_record):
        """Test exact logger name matching."""
        configs = {'test.logger': {'rate': 2, 'per': 120}}
        filter_obj = LoggerSpecificRateLimitFilter(logger_configs=configs)
        
        config = filter_obj._get_logger_config("test.logger")
        assert config['rate'] == 2
        assert config['per'] == 120
    
    def test_parent_logger_match(self, log_record):
        """Test parent logger matching."""
        configs = {'parent': {'rate': 3, 'per': 90}}
        filter_obj = LoggerSpecificRateLimitFilter(logger_configs=configs)
        
        config = filter_obj._get_logger_config("parent.child.grandchild")
        assert config['rate'] == 3
        assert config['per'] == 90
    
    def test_most_specific_parent_match(self, log_record):
        """Test that most specific parent logger config is used."""
        configs = {
            'parent': {'rate': 5, 'per': 60},
            'parent.child': {'rate': 2, 'per': 30}
        }
        filter_obj = LoggerSpecificRateLimitFilter(logger_configs=configs)
        
        config = filter_obj._get_logger_config("parent.child.grandchild")
        assert config['rate'] == 2  # More specific config
        assert config['per'] == 30
    
    def test_different_loggers_separate_limits(self, log_record):
        """Test that different loggers have separate rate limits."""
        configs = {
            'fast': {'rate': 10, 'per': 60},
            'slow': {'rate': 1, 'per': 60}
        }
        filter_obj = LoggerSpecificRateLimitFilter(logger_configs=configs)
        
        # Fast logger should allow multiple identical messages (up to rate=10)
        for i in range(10):
            record = log_record("Same message", logger_name="fast")
            assert filter_obj.filter(record) is True
        
        # 11th message should be blocked for fast logger, but first blocked message gets annotation
        record = log_record("Same message", logger_name="fast")
        assert filter_obj.filter(record) is True  # First blocked message passes with annotation
        assert "[Rate limit reached for fast]" in record.msg
        
        # 12th message should be actually blocked
        record = log_record("Same message", logger_name="fast")
        assert filter_obj.filter(record) is False
        
        # Slow logger should only allow one identical message
        record1 = log_record("Same message", logger_name="slow")
        assert filter_obj.filter(record1) is True
        
        # Second message should be blocked but first blocked message gets annotation
        record2 = log_record("Same message", logger_name="slow")
        assert filter_obj.filter(record2) is True  # First blocked message passes with annotation
        assert "[Rate limit reached for slow]" in record2.msg
        
        # Third message should be actually blocked
        record3 = log_record("Same message", logger_name="slow")
        assert filter_obj.filter(record3) is False
    
    def test_rate_limit_message_annotation(self, log_record):
        """Test that rate limit messages are annotated."""
        configs = {'test': {'rate': 1, 'per': 60}}
        filter_obj = LoggerSpecificRateLimitFilter(logger_configs=configs)
        
        # First message passes
        record1 = log_record("Test message", logger_name="test")
        assert filter_obj.filter(record1) is True
        
        # Second message should be blocked, but first blocked message gets annotation
        record2 = log_record("Test message", logger_name="test")
        result = filter_obj.filter(record2)
        
        # Implementation may return True for first blocked message with annotation
        if result:
            assert "Rate limit reached for test" in record2.getMessage()
    
    def test_cleanup_per_logger_data(self, log_record):
        """Test that per-logger data is cleaned up properly."""
        filter_obj = LoggerSpecificRateLimitFilter()
        
        # Add messages for different loggers
        record1 = log_record("Message", logger_name="logger1")
        record2 = log_record("Message", logger_name="logger2")
        
        filter_obj.filter(record1)
        filter_obj.filter(record2)
        
        assert "logger1" in filter_obj._logger_messages
        assert "logger2" in filter_obj._logger_messages
        
        # Manually trigger cleanup
        with patch('core.logging.filters.time.time', return_value=1000):
            filter_obj._cleanup()
        
        # Empty logger entries should be cleaned up


class TestFactoryFunctions:
    """Test factory functions and convenience functions."""
    
    def test_create_rate_limit_filter_default(self):
        """Test creating default rate limit filter."""
        filter_obj = create_rate_limit_filter()
        
        assert isinstance(filter_obj, RateLimitFilter)
        assert not isinstance(filter_obj, BurstRateLimitFilter)
    
    def test_create_rate_limit_filter_burst(self):
        """Test creating burst rate limit filter."""
        filter_obj = create_rate_limit_filter(filter_type='burst')
        
        assert isinstance(filter_obj, BurstRateLimitFilter)
    
    def test_create_rate_limit_filter_sampling(self):
        """Test creating sampling rate limit filter."""
        filter_obj = create_rate_limit_filter(filter_type='sampling')
        
        assert isinstance(filter_obj, SamplingRateLimitFilter)
        assert filter_obj.sample_rate == 0.1  # Default sample rate
    
    def test_create_rate_limit_filter_logger_specific(self):
        """Test creating logger-specific rate limit filter."""
        filter_obj = create_rate_limit_filter(filter_type='logger_specific')
        
        assert isinstance(filter_obj, LoggerSpecificRateLimitFilter)
    
    def test_create_rate_limit_filter_with_preset_strict(self):
        """Test creating filter with strict preset."""
        filter_obj = create_rate_limit_filter(preset='strict')
        
        assert filter_obj.rate == 1
        assert filter_obj.per == 60.0
        assert filter_obj.burst == 3
        assert filter_obj.max_cache_size == 1000
    
    def test_create_rate_limit_filter_with_preset_lenient(self):
        """Test creating filter with lenient preset."""
        filter_obj = create_rate_limit_filter(preset='lenient')
        
        assert filter_obj.rate == 10
        assert filter_obj.per == 60.0
        assert filter_obj.burst == 20
        assert filter_obj.max_cache_size == 10000
    
    def test_create_rate_limit_filter_with_preset_performance(self):
        """Test creating filter with performance preset."""
        filter_obj = create_rate_limit_filter(preset='performance')
        
        assert filter_obj.rate == 100
        assert filter_obj.per == 60.0
        assert filter_obj.burst == 200
        assert filter_obj.max_cache_size == 50000
        assert filter_obj.cleanup_interval == 600
    
    def test_preset_override_with_custom_params(self):
        """Test that custom parameters override preset values."""
        filter_obj = create_rate_limit_filter(preset='strict', rate=10)
        
        assert filter_obj.rate == 10  # Custom value
        assert filter_obj.per == 60.0  # From preset
    
    def test_create_strict_rate_limiter(self):
        """Test convenience function for strict rate limiter."""
        filter_obj = create_strict_rate_limiter()
        
        assert isinstance(filter_obj, RateLimitFilter)
        assert filter_obj.rate == 1
        assert filter_obj.max_cache_size == 1000
    
    def test_create_lenient_rate_limiter(self):
        """Test convenience function for lenient rate limiter."""
        filter_obj = create_lenient_rate_limiter()
        
        assert isinstance(filter_obj, RateLimitFilter)
        assert filter_obj.rate == 10
        assert filter_obj.max_cache_size == 10000
    
    def test_create_burst_limiter(self):
        """Test convenience function for burst limiter."""
        filter_obj = create_burst_limiter()
        
        assert isinstance(filter_obj, BurstRateLimitFilter)
        assert filter_obj.rate == 100
        assert filter_obj.burst == 200
    
    def test_create_burst_limiter_custom_params(self):
        """Test burst limiter with custom parameters."""
        filter_obj = create_burst_limiter(rate=50, burst=100, per=30)
        
        assert filter_obj.rate == 50
        assert filter_obj.burst == 100
        assert filter_obj.per == 30
    
    def test_create_sampling_limiter(self):
        """Test convenience function for sampling limiter."""
        filter_obj = create_sampling_limiter()
        
        assert isinstance(filter_obj, SamplingRateLimitFilter)
        assert filter_obj.rate == 10
        assert filter_obj.sample_rate == 0.1
    
    def test_create_sampling_limiter_custom_params(self):
        """Test sampling limiter with custom parameters."""
        filter_obj = create_sampling_limiter(rate=5, sample_rate=0.2, per=30)
        
        assert filter_obj.rate == 5
        assert filter_obj.sample_rate == 0.2
        assert filter_obj.per == 30


class TestEdgeCases:
    """Test edge cases and error conditions."""
    
    def test_malformed_log_record(self):
        """Test handling of malformed log records."""
        filter_obj = RateLimitFilter()
        
        # Record with broken getMessage()
        record = logging.LogRecord(
            name="test",
            level=logging.INFO,
            pathname="test.py",
            lineno=1,
            msg=None,  # This might cause issues
            args=(),
            exc_info=None
        )
        
        # Should not crash
        result = filter_obj.filter(record)
        assert isinstance(result, bool)
    
    def test_very_long_message(self, log_record):
        """Test handling of very long log messages."""
        filter_obj = RateLimitFilter()
        
        # Create very long message
        long_message = "A" * 10000
        record = log_record(long_message)
        
        # Should handle without issues
        result = filter_obj.filter(record)
        assert result is True
    
    def test_unicode_message(self, log_record):
        """Test handling of unicode messages."""
        filter_obj = RateLimitFilter()
        
        unicode_message = "Test message with unicode: 🔥 ñáéíóú 中文"
        record = log_record(unicode_message)
        
        result = filter_obj.filter(record)
        assert result is True
    
    def test_empty_message(self, log_record):
        """Test handling of empty messages."""
        filter_obj = RateLimitFilter()
        
        record = log_record("")
        result = filter_obj.filter(record)
        assert result is True
    
    def test_zero_rate_limit(self):
        """Test behavior with zero rate limit."""
        filter_obj = RateLimitFilter(rate=0, per=60)
        
        # Should work but block everything after burst
        record = logging.LogRecord(
            name="test",
            level=logging.INFO,
            pathname="test.py",
            lineno=1,
            msg="Test",
            args=(),
            exc_info=None
        )
        
        # First message might pass due to burst
        result = filter_obj.filter(record)
        assert isinstance(result, bool)
    
    def test_negative_time_values(self):
        """Test handling of negative time values."""
        with patch('core.logging.filters.time.time', return_value=-100):
            filter_obj = RateLimitFilter()
            
            record = logging.LogRecord(
                name="test",
                level=logging.INFO,
                pathname="test.py", 
                lineno=1,
                msg="Test",
                args=(),
                exc_info=None
            )
            
            # Should handle gracefully
            result = filter_obj.filter(record)
            assert isinstance(result, bool)
    
    def test_stats_access_when_not_in_stats(self):
        """Test accessing stats when message_key not in _stats."""
        filter_obj = RateLimitFilter()
        
        # Create a record
        record = logging.LogRecord(
            name="test",
            level=logging.INFO,
            pathname="test.py",
            lineno=1,
            msg="Test message",
            args=(),
            exc_info=None
        )
        
        # First call - message allowed and stats created
        assert filter_obj.filter(record) is True
        
        # Get the message key directly from the record
        message_key = filter_obj._get_message_key(record)
        
        # Manually remove stats but keep message
        if message_key in filter_obj._stats:
            del filter_obj._stats[message_key]
        
        # Second call - message blocked but stats don't exist
        assert filter_obj.filter(record) is False
        # Should not crash when accessing missing stats
    
    def test_cleanup_with_stats_but_no_access_order(self):
        """Test cleanup when key in stats but not in access_order."""
        filter_obj = RateLimitFilter(per=60.0)
        
        # Add entry to messages and stats
        key = "test_key"
        current_time = time.time()
        filter_obj._messages[key] = deque([current_time - 120])  # Old message
        filter_obj._stats[key] = MessageStats()
        filter_obj._stats[key].last_seen = current_time - 120
        # Don't add to access_order
        
        # Trigger cleanup
        filter_obj._cleanup()
        
        # Should handle missing key in access_order gracefully
        assert key not in filter_obj._messages
    
    def test_cleanup_access_order_remove_missing_key(self):
        """Test cleanup trying to remove key that's not in access_order."""
        filter_obj = RateLimitFilter()
        
        # Setup messages that will be removed
        key1 = "key1"
        key2 = "key2"
        current_time = time.time()
        
        # Add old messages
        filter_obj._messages[key1] = deque([current_time - 120])
        filter_obj._messages[key2] = deque([current_time - 120])
        
        # Only add key1 to access_order
        filter_obj._access_order.append(key1)
        # key2 is NOT in access_order
        
        # Run cleanup
        filter_obj._cleanup()
        
        # Should not crash when trying to remove key2 from access_order
        assert key1 not in filter_obj._messages
        assert key2 not in filter_obj._messages


class TestPerformance:
    """Test performance characteristics."""
    
    def test_filter_performance_many_messages(self, log_record):
        """Test filter performance with many unique messages."""
        filter_obj = RateLimitFilter(max_cache_size=1000)
        
        start_time = time.time()
        
        # Process many unique messages
        for i in range(500):
            record = log_record(f"Message {i}")
            filter_obj.filter(record)
        
        end_time = time.time()
        duration = end_time - start_time
        
        # Should complete reasonably quickly (less than 1 second)
        assert duration < 1.0
    
    def test_filter_performance_duplicate_messages(self, log_record):
        """Test filter performance with many duplicate messages."""
        filter_obj = RateLimitFilter()
        
        start_time = time.time()
        
        # Process many duplicate messages (should be fast)
        for i in range(1000):
            record = log_record("Duplicate message")
            filter_obj.filter(record)
        
        end_time = time.time()
        duration = end_time - start_time
        
        # Should be very fast since most are duplicates
        assert duration < 0.5
    
    def test_memory_usage_with_many_unique_messages(self, log_record):
        """Test that memory usage is bounded with many unique messages."""
        filter_obj = RateLimitFilter(max_cache_size=100)
        
        # Add many unique messages
        for i in range(200):
            record = log_record(f"Unique message {i}")
            filter_obj.filter(record)
        
        # Cache should be bounded
        assert len(filter_obj._messages) <= 100
        assert len(filter_obj._stats) <= 150  # Stats kept a bit longer


class TestThreadSafety:
    """Test thread safety of rate limit filters."""
    
    def test_concurrent_filtering(self, log_record):
        """Test that concurrent filtering works correctly."""
        filter_obj = RateLimitFilter(rate=10, per=60)
        results = []
        
        def worker():
            local_results = []
            for i in range(20):
                # Send same message to test rate limiting
                record = log_record("Same concurrent message")
                result = filter_obj.filter(record)
                local_results.append(result)
            return local_results
        
        # Run multiple threads concurrently
        with ThreadPoolExecutor(max_workers=5) as executor:
            futures = [executor.submit(worker) for _ in range(5)]
            
            for future in as_completed(futures):
                results.extend(future.result())
        
        # Should have some True and some False results
        # With rate=10, only first 10 identical messages should pass
        true_count = sum(1 for r in results if r)
        false_count = sum(1 for r in results if not r)
        
        assert true_count > 0
        assert false_count > 0
        # With 5 threads * 20 messages = 100 total messages, but rate=10
        # So we expect approximately 10 true and 90 false
        # Total should be all results
        assert true_count + false_count == len(results)
    
    def test_concurrent_same_message(self, log_record):
        """Test concurrent filtering of the same message."""
        filter_obj = RateLimitFilter(rate=5, per=60)
        results = []
        
        def worker():
            local_results = []
            for i in range(10):
                record = log_record("Same message")
                result = filter_obj.filter(record)
                local_results.append(result)
            return local_results
        
        # Run multiple threads with same message
        with ThreadPoolExecutor(max_workers=3) as executor:
            futures = [executor.submit(worker) for _ in range(3)]
            
            for future in as_completed(futures):
                results.extend(future.result())
        
        # Should have limited number of True results due to rate limiting
        true_count = sum(1 for r in results if r)
        assert true_count <= 10  # Rate + some burst allowance
    
    def test_cleanup_thread_safety(self, log_record):
        """Test that cleanup operations are thread-safe."""
        filter_obj = RateLimitFilter(cleanup_interval=0.1)  # Frequent cleanup
        
        def worker():
            for i in range(50):
                record = log_record(f"Cleanup test {i}")
                filter_obj.filter(record)
                time.sleep(0.01)  # Small delay to trigger cleanups
        
        # Run multiple threads to stress test cleanup
        with ThreadPoolExecutor(max_workers=3) as executor:
            futures = [executor.submit(worker) for _ in range(3)]
            
            # Should complete without errors
            for future in as_completed(futures):
                future.result()  # Will raise if there was an exception
    
    def test_burst_filter_thread_safety(self, log_record):
        """Test thread safety of BurstRateLimitFilter."""
        filter_obj = BurstRateLimitFilter(rate=10, per=60, burst=20)
        
        def worker():
            results = []
            for i in range(30):
                record = log_record("Burst test")
                result = filter_obj.filter(record)
                results.append(result)
            return results
        
        # Run concurrent burst tests
        with ThreadPoolExecutor(max_workers=4) as executor:
            futures = [executor.submit(worker) for _ in range(4)]
            
            all_results = []
            for future in as_completed(futures):
                all_results.extend(future.result())
        
        # Should have some allowed messages (burst capacity)
        true_count = sum(1 for r in all_results if r)
        assert true_count > 0
        assert true_count <= 25  # Some burst allowance per thread


class TestIntegration:
    """Test integration with actual logging system."""
    
    def test_integration_with_logger(self):
        """Test filter integration with Python logger."""
        # Create logger with our filter
        logger = logging.getLogger("test.integration")
        logger.setLevel(logging.DEBUG)
        
        # Create handler with filter
        handler = logging.StreamHandler()
        rate_filter = RateLimitFilter(rate=2, per=60)  # Allow 2 identical messages per 60s
        handler.addFilter(rate_filter)
        logger.addHandler(handler)
        
        try:
            # Log messages - need to exceed rate limit for suppression
            logger.info("First message")   # 1st: Should pass 
            logger.info("Second message")  # Different: Should pass
            logger.info("First message")   # 2nd: Should pass (rate=2)
            logger.info("First message")   # 3rd: Should be blocked (exceeds rate=2)
            logger.info("Third message")   # Different: Should pass
            logger.info("Third message")   # 2nd: Should pass (rate=2)
            logger.info("Third message")   # 3rd: Should be blocked (exceeds rate=2)
            
            # Check filter statistics
            summary = rate_filter.get_summary()
            assert summary["total_messages"] >= 3  # At least some passed
            assert summary["total_suppressed"] >= 2  # At least 2 blocked
            
        finally:
            # Clean up
            logger.removeHandler(handler)
            handler.close()
    
    def test_multiple_filters_on_handler(self):
        """Test multiple rate limit filters on same handler."""
        logger = logging.getLogger("test.multiple")
        logger.setLevel(logging.DEBUG)
        
        handler = logging.StreamHandler()
        
        # Add multiple filters
        filter1 = RateLimitFilter(rate=5, per=60)
        filter2 = SamplingRateLimitFilter(rate=2, per=60, sample_rate=0.5)
        
        handler.addFilter(filter1)
        handler.addFilter(filter2)
        logger.addHandler(handler)
        
        try:
            # Log many messages
            for i in range(10):
                logger.info(f"Multi-filter test {i}")
            
            # Both filters should have statistics
            summary1 = filter1.get_summary()
            summary2 = filter2.get_summary()
            
            assert summary1["total_messages"] > 0
            assert summary2["total_messages"] > 0
            
        finally:
            logger.removeHandler(handler)
            handler.close()