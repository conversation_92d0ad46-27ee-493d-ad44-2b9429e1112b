"""
Tests for custom logging handlers.

These tests follow TDD approach - they will fail until implementation is complete.
"""

import logging
from unittest.mock import Mock, patch

from core.logging.handlers import SentryHandler


class TestSentryHandler:
    """Test cases for SentryHandler."""
    
    def test_init_default_level(self):
        """Test handler initialization with default ERROR level."""
        handler = SentryHandler()
        assert handler.level == logging.ERROR
        
    def test_init_custom_level(self):
        """Test handler initialization with custom level."""
        handler = SentryHandler(level=logging.WARNING)
        assert handler.level == logging.WARNING
    
    def test_simple_handler_creation(self):
        """Test that we can create a handler and check imports."""
        # This test checks if the imports are working correctly
        handler = <PERSON>tryHandler()
        
        # Check if _capture_message method exists
        assert hasattr(handler, '_capture_message')
        
        # Check if cast import works
        from typing import cast, Literal
        test_value = cast(Literal['error'], 'error')
        assert test_value == 'error'
        
    @patch('sentry_sdk.push_scope')
    @patch('sentry_sdk.capture_message')
    @patch('sentry_sdk.Hub')
    def test_emit_error_record(self, mock_hub, mock_capture_message, mock_push_scope):
        """Test that error records are sent to Sentry."""
        # Mock Sentry as initialized
        mock_hub.current.client = Mock()
        
        # Mock scope
        mock_scope = Mock()
        mock_push_scope.return_value.__enter__.return_value = mock_scope
        
        # Create handler with explicit mock tracking
        handler = SentryHandler()
        
        # Debug: print handler levels
        print(f"Handler level: {handler.level}")
        print(f"Handler capture_message_level: {handler.capture_message_level}")
        
        # Track _capture_message calls
        original_capture_message = handler._capture_message
        capture_message_calls = []
        
        def track_capture_message(record):
            print(f"_capture_message called with record level: {record.levelno}")
            capture_message_calls.append(record)
            return original_capture_message(record)
        
        handler._capture_message = track_capture_message
        
        # Create a log record
        record = logging.LogRecord(
            name="test_logger",
            level=logging.ERROR,
            pathname="test.py",
            lineno=42,
            msg="Test error message",
            args=(),
            exc_info=None
        )
        
        # Debug: print record level
        print(f"Record level: {record.levelno}")
        print(f"Record level name: {record.levelname}")
        print(f"Record has exc_info: {record.exc_info}")
        
        # Debug emit method
        print("Calling handler.emit()")
        
        # Track emit execution
        original_emit = handler.emit
        emit_exception = None
        
        def debug_emit(rec):
            nonlocal emit_exception
            print("Inside emit")
            try:
                # Check Sentry SDK
                import sentry_sdk
                print(f"sentry_sdk module: {sentry_sdk}")
                print(f"sentry_sdk.Hub: {sentry_sdk.Hub}")
                print(f"sentry_sdk.Hub.current: {sentry_sdk.Hub.current}")
                print(f"sentry_sdk.Hub.current.client: {sentry_sdk.Hub.current.client}")
                
                # Call original
                return original_emit(rec)
            except Exception as e:
                print(f"Exception in emit: {type(e).__name__}: {e}")
                emit_exception = e
                raise
        
        handler.emit = debug_emit
        handler.emit(record)
        
        if emit_exception:
            print(f"Emit raised exception: {emit_exception}")
        
        print(f"_capture_message was called {len(capture_message_calls)} times")
        
        # Verify Sentry was called
        print(f"mock_capture_message call count: {mock_capture_message.call_count}")
        mock_capture_message.assert_called_once()
        
    @patch('sentry_sdk.push_scope')
    @patch('sentry_sdk.capture_message')
    @patch('sentry_sdk.Hub')
    def test_emit_with_extra_data(self, mock_hub, mock_capture_message, mock_push_scope):
        """Test that extra data from record is included in Sentry scope."""
        # Mock Sentry as initialized
        mock_hub.current.client = Mock()
        
        # Mock scope
        mock_scope = Mock()
        mock_push_scope.return_value.__enter__.return_value = mock_scope
        
        handler = SentryHandler()
        
        # Create record with extra data
        record = logging.LogRecord(
            name="test_logger",
            level=logging.ERROR,
            pathname="test.py",
            lineno=42,
            msg="Test error",
            args=(),
            exc_info=None
        )
        record.user_id = 123
        record.request_id = "abc-123"
        
        handler.emit(record)
        
        # Verify message was captured
        mock_capture_message.assert_called_once()
        
        # Verify extra data was added to scope
        mock_scope.set_extra.assert_any_call('user_id', 123)
        mock_scope.set_extra.assert_any_call('request_id', "abc-123")
        
    @patch('sentry_sdk.push_scope')
    @patch('sentry_sdk.capture_exception')
    @patch('sentry_sdk.Hub')
    def test_emit_with_exception(self, mock_hub, mock_capture_exception, mock_push_scope):
        """Test that exceptions are properly captured."""
        # Mock Sentry as initialized
        mock_hub.current.client = Mock()
        
        # Mock scope
        mock_scope = Mock()
        mock_push_scope.return_value.__enter__.return_value = mock_scope
        
        handler = SentryHandler()
        
        # Create exception info
        try:
            raise ValueError("Test exception")
        except ValueError:
            import sys
            exc_info = sys.exc_info()
        
        # Create record with exception
        record = logging.LogRecord(
            name="test_logger",
            level=logging.ERROR,
            pathname="test.py",
            lineno=42,
            msg="Test error with exception",
            args=(),
            exc_info=exc_info
        )
        
        handler.emit(record)
        
        # Verify exception was captured
        mock_capture_exception.assert_called_once()
        
    @patch('sentry_sdk.push_scope')
    @patch('sentry_sdk.capture_message')  
    @patch('sentry_sdk.Hub')
    def test_emit_below_threshold(self, mock_hub, mock_capture_message, mock_push_scope):
        """Test that records below threshold are not sent."""
        # Mock Sentry as initialized
        mock_hub.current.client = Mock()
        
        handler = SentryHandler(level=logging.ERROR)
        
        # Create INFO level record
        record = logging.LogRecord(
            name="test_logger",
            level=logging.INFO,
            pathname="test.py",
            lineno=42,
            msg="Info message",
            args=(),
            exc_info=None
        )
        
        handler.emit(record)
        # Should not be called for INFO when threshold is ERROR
        mock_capture_message.assert_not_called()
        mock_push_scope.assert_not_called()
            
    @patch('sentry_sdk.push_scope')
    @patch('sentry_sdk.capture_message')
    @patch('sentry_sdk.Hub')
    def test_emit_handles_errors_gracefully(self, mock_hub, mock_capture_message, mock_push_scope):
        """Test that handler errors don't break logging."""
        # Mock Sentry as initialized
        mock_hub.current.client = Mock()
        
        # Mock scope that raises exception
        mock_scope = Mock()
        mock_push_scope.return_value.__enter__.return_value = mock_scope
        mock_scope.set_tag.side_effect = Exception("Sentry error")
        
        handler = SentryHandler()
        
        # Create record
        record = logging.LogRecord(
            name="test_logger",
            level=logging.ERROR,
            pathname="test.py",
            lineno=42,
            msg="Test message",
            args=(),
            exc_info=None
        )
        
        # Should not raise exception
        handler.emit(record)  # Should handle error gracefully
        
    def test_simple_logging(self):
        """Test simple logging without mocks."""
        # Create a fresh logger
        import logging
        logger_name = f"test_simple_{id(self)}"
        logger = logging.getLogger(logger_name)
        logger.handlers = []
        logger.propagate = False
        logger.setLevel(logging.DEBUG)
        
        # Track emit calls
        emit_calls = []
        
        class TrackingHandler(logging.Handler):
            def __init__(self, level=logging.NOTSET):
                super().__init__(level)
                
            def emit(self, record):
                emit_calls.append(record)
        
        handler = TrackingHandler(level=logging.ERROR)
        logger.addHandler(handler)
        
        # Log messages
        logger.debug("Debug")
        logger.info("Info")
        logger.warning("Warning")
        logger.error("Error")
        
        # Check only ERROR was handled
        assert len(emit_calls) == 1
        assert emit_calls[0].levelno == logging.ERROR
        
    @patch('sentry_sdk.push_scope')
    @patch('sentry_sdk.capture_message')
    @patch('sentry_sdk.Hub')
    def test_integration_with_logging_module(self, mock_hub, mock_capture_message, mock_push_scope):
        """Test integration with Python logging module."""
        # Mock Sentry as initialized
        mock_hub.current.client = Mock()
        
        # Mock scope
        mock_scope = Mock()
        mock_push_scope.return_value.__enter__.return_value = mock_scope
        
        # Create logger with our handler - use unique name
        logger_name = f"test_sentry_integration_{id(self)}"
        logger = logging.getLogger(logger_name)
        logger.handlers = []
        logger.propagate = False  # Ensure no parent handlers interfere
        
        handler = SentryHandler(level=logging.ERROR)
        
        # Mock the emit method to track if it's called
        emit_called = []
        original_emit = handler.emit
        
        def mock_emit(record):
            emit_called.append(record)
            return original_emit(record)
        
        # Use Mock to properly replace the method
        handler.emit = Mock(side_effect=mock_emit)
        
        logger.addHandler(handler)
        logger.setLevel(logging.DEBUG)
        
        # Verify handler is added
        assert handler in logger.handlers
        assert logger.level == logging.DEBUG
        assert handler.level == logging.ERROR
        
        # Log at different levels
        logger.debug("Debug message")  # Should not be sent
        logger.info("Info message")    # Should not be sent
        logger.warning("Warning")      # Should not be sent
        logger.error("Error message")  # Should be sent
        
        # Check that emit was called
        assert len(emit_called) == 1, f"emit was called {len(emit_called)} times, expected 1"
        assert emit_called[0].levelno == logging.ERROR
        
        # Only ERROR should be captured
        assert mock_capture_message.call_count == 1
            
    @patch('sentry_sdk.push_scope')
    @patch('sentry_sdk.capture_message')
    @patch('sentry_sdk.Hub')
    def test_context_enrichment(self, mock_hub, mock_capture_message, mock_push_scope):
        """Test that handler enriches Sentry context with app data."""
        # Mock Sentry as initialized
        mock_hub.current.client = Mock()
        
        # Mock scope
        mock_scope = Mock()
        mock_push_scope.return_value.__enter__.return_value = mock_scope
        
        handler = SentryHandler()
        
        # Create record with context
        record = logging.LogRecord(
            name="test_logger",
            level=logging.ERROR,
            pathname="test.py",
            lineno=42,
            msg="Test error",
            args=(),
            exc_info=None
        )
        
        # Add context variables
        record.request_id = "req-123"
        record.user_id = 456
        record.task_id = "task-789"
        
        handler.emit(record)
        
        # Verify context was set
        mock_scope.set_context.assert_called()
        mock_scope.set_tag.assert_called()
        mock_scope.set_extra.assert_called()