"""
Tests for logging middleware functionality.
"""

import time
import uuid
from unittest.mock import Mock, patch

import pytest
from django.http import HttpResponse
from django.test import RequestFactory, TestCase
from django.contrib.auth import get_user_model
from django.contrib.auth.models import AnonymousUser

from core.logging import get_context, clear_context
from core.logging.middleware import (
    LoggingContextMiddleware,
    RequestIDMiddleware,
    APILoggingMiddleware,
)

User = get_user_model()


class TestLoggingContextMiddleware(TestCase):
    """Test LoggingContextMiddleware functionality."""
    
    def setUp(self):
        """Set up test environment."""
        self.factory = RequestFactory()
        self.middleware = LoggingContextMiddleware(get_response=lambda r: HttpResponse())
        clear_context()
    
    def tearDown(self):
        """Clean up after tests."""
        clear_context()
    
    def test_process_request_generates_request_id(self):
        """Test that middleware generates request_id."""
        request = self.factory.get('/')
        request.user = AnonymousUser()
        
        self.middleware.process_request(request)
        
        # Check request has request_id
        self.assertTrue(hasattr(request, 'request_id'))
        self.assertIsNotNone(request.request_id)
        
        # Check context has request_id
        context = get_context()
        self.assertEqual(context['request_id'], request.request_id)
    
    def test_process_request_uses_existing_request_id(self):
        """Test that middleware uses X-Request-ID header if present."""
        existing_id = str(uuid.uuid4())
        request = self.factory.get('/', HTTP_X_REQUEST_ID=existing_id)
        request.user = AnonymousUser()
        
        self.middleware.process_request(request)
        
        self.assertEqual(request.request_id, existing_id)
        self.assertEqual(get_context()['request_id'], existing_id)
    
    def test_process_request_sets_user_context(self):
        """Test that middleware sets user context for authenticated users."""
        user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass'
        )
        
        request = self.factory.get('/')
        request.user = user
        
        self.middleware.process_request(request)
        
        context = get_context()
        self.assertEqual(context['user_id'], user.id)
        self.assertEqual(context['username'], 'testuser')
        self.assertEqual(context['user_email'], '<EMAIL>')
    
    def test_process_request_anonymous_user(self):
        """Test that middleware handles anonymous users correctly."""
        request = self.factory.get('/')
        request.user = AnonymousUser()
        
        self.middleware.process_request(request)
        
        context = get_context()
        self.assertNotIn('user_id', context)
        self.assertNotIn('username', context)
    
    def test_process_request_adds_request_metadata(self):
        """Test that middleware adds request metadata to context."""
        request = self.factory.post(
            '/api/test',
            data={'key': 'value'},
            HTTP_USER_AGENT='TestAgent/1.0',
            REMOTE_ADDR='1**********'
        )
        request.user = AnonymousUser()
        
        self.middleware.process_request(request)
        
        context = get_context()
        self.assertEqual(context['method'], 'POST')
        self.assertEqual(context['path'], '/api/test')
        self.assertEqual(context['user_agent'], 'TestAgent/1.0')
        self.assertEqual(context['remote_addr'], '1**********')
    
    def test_process_request_handles_x_forwarded_for(self):
        """Test that middleware correctly extracts IP from X-Forwarded-For."""
        request = self.factory.get(
            '/',
            HTTP_X_FORWARDED_FOR='***********, ************',
            REMOTE_ADDR='1**********'
        )
        request.user = AnonymousUser()
        
        self.middleware.process_request(request)
        
        context = get_context()
        self.assertEqual(context['remote_addr'], '***********')
    
    def test_process_request_handles_x_real_ip(self):
        """Test that middleware correctly extracts IP from X-Real-IP."""
        request = self.factory.get(
            '/',
            HTTP_X_REAL_IP='***********',
            REMOTE_ADDR='1**********'
        )
        request.user = AnonymousUser()
        
        self.middleware.process_request(request)
        
        context = get_context()
        self.assertEqual(context['remote_addr'], '***********')
    
    def test_process_request_adds_custom_headers(self):
        """Test that middleware adds custom headers to context."""
        request = self.factory.get(
            '/',
            HTTP_X_CORRELATION_ID='corr-123',
            HTTP_X_API_VERSION='v2'
        )
        request.user = AnonymousUser()
        request.session = Mock(session_key='session-123')
        
        self.middleware.process_request(request)
        
        context = get_context()
        self.assertEqual(context['correlation_id'], 'corr-123')
        self.assertEqual(context['api_version'], 'v2')
        self.assertEqual(context['session_id'], 'session-123')
    
    def test_process_response_adds_timing(self):
        """Test that middleware adds timing information."""
        request = self.factory.get('/')
        request.user = AnonymousUser()
        request._logging_start_time = time.time() - 0.5  # 500ms ago
        
        response = HttpResponse(b'Test response')
        result = self.middleware.process_response(request, response)
        
        # Should have timing in context before it's cleared
        # Note: Context is cleared at the end, so we can't check it directly
        self.assertEqual(result, response)
    
    def test_process_response_adds_request_id_header(self):
        """Test that middleware adds X-Request-ID to response."""
        request = self.factory.get('/')
        request.user = AnonymousUser()
        request.request_id = 'test-request-id'
        
        response = HttpResponse()
        result = self.middleware.process_response(request, response)
        
        self.assertEqual(result['X-Request-ID'], 'test-request-id')
    
    def test_process_response_clears_context(self):
        """Test that middleware clears context after response."""
        request = self.factory.get('/')
        request.user = AnonymousUser()
        
        # Set some context
        self.middleware.process_request(request)
        self.assertTrue(get_context())  # Context should exist
        
        # Process response
        response = HttpResponse()
        self.middleware.process_response(request, response)
        
        # Context should be cleared
        self.assertFalse(get_context())
    
    def test_process_exception_logs_exception(self):
        """Test that middleware logs exceptions with context."""
        request = self.factory.get('/')
        request.user = AnonymousUser()
        request._logging_start_time = time.time()
        
        # Set up request context
        self.middleware.process_request(request)
        
        # Create exception
        exception = ValueError("Test exception")
        
        with patch.object(self.middleware.logger, 'exception') as mock_log:
            result = self.middleware.process_exception(request, exception)
            
            # Should return None to continue with default handling
            self.assertIsNone(result)
            
            # Should have logged the exception
            mock_log.assert_called_once()
            args, kwargs = mock_log.call_args
            self.assertIn("Unhandled exception", args[0])
            self.assertEqual(kwargs['exception_type'], 'ValueError')
            self.assertEqual(kwargs['exception_message'], 'Test exception')
    
    @patch('core.logging.middleware.time.time')
    def test_slow_request_warning(self, mock_time):
        """Test that slow requests generate warnings."""
        # Set up time mocking
        start_time = 1000.0
        end_time = 1002.0  # 2 seconds later
        mock_time.side_effect = [start_time, end_time, end_time]
        
        request = self.factory.get('/')
        request.user = AnonymousUser()
        
        # Process request
        self.middleware.process_request(request)
        
        # Mock logger
        with patch.object(self.middleware.logger, 'warning') as mock_warning:
            response = HttpResponse()
            self.middleware.process_response(request, response)
            
            # Should have logged warning about slow request
            mock_warning.assert_called_once()
            args, kwargs = mock_warning.call_args
            self.assertIn("Slow request detected", args[0])
            self.assertEqual(kwargs['duration_seconds'], 2.0)


class TestRequestIDMiddleware(TestCase):
    """Test RequestIDMiddleware functionality."""
    
    def setUp(self):
        """Set up test environment."""
        self.factory = RequestFactory()
        self.middleware = RequestIDMiddleware(get_response=lambda r: HttpResponse())
        clear_context()
    
    def tearDown(self):
        """Clean up after tests."""
        clear_context()
    
    def test_only_sets_request_id(self):
        """Test that middleware only sets request_id without logging."""
        request = self.factory.get('/')
        
        with patch.object(self.middleware.logger, 'info') as mock_info:
            self.middleware.process_request(request)
            
            # Should not log
            mock_info.assert_not_called()
            
            # Should set request_id
            self.assertTrue(hasattr(request, 'request_id'))
            self.assertIsNotNone(request.request_id)
            self.assertEqual(get_context()['request_id'], request.request_id)
    
    def test_clears_context_without_logging(self):
        """Test that middleware clears context without logging."""
        request = self.factory.get('/')
        self.middleware.process_request(request)
        
        response = HttpResponse()
        
        with patch.object(self.middleware.logger, 'info') as mock_info:
            self.middleware.process_response(request, response)
            
            # Should not log
            mock_info.assert_not_called()
            
            # Should clear context
            self.assertFalse(get_context())


class TestAPILoggingMiddleware(TestCase):
    """Test APILoggingMiddleware functionality."""
    
    def setUp(self):
        """Set up test environment."""
        self.factory = RequestFactory()
        self.middleware = APILoggingMiddleware(get_response=lambda r: HttpResponse())
        clear_context()
    
    def tearDown(self):
        """Clean up after tests."""
        clear_context()
    
    def test_logs_api_request_body(self):
        """Test that middleware logs API request body."""
        request = self.factory.post(
            '/api/test',
            data='{"key": "value"}',
            content_type='application/json'
        )
        request.user = AnonymousUser()
        
        with patch.object(self.middleware.logger, 'debug') as mock_debug:
            self.middleware.process_request(request)
            
            # Should log request body
            mock_debug.assert_called_once()
            args, kwargs = mock_debug.call_args
            self.assertIn("API request body", args[0])
            self.assertEqual(kwargs['body'], '{"key": "value"}')
    
    def test_truncates_large_request_body(self):
        """Test that middleware truncates large request bodies."""
        large_body = 'x' * 2000
        request = self.factory.post(
            '/api/test',
            data=large_body,
            content_type='text/plain'
        )
        request.user = AnonymousUser()
        
        with patch.object(self.middleware.logger, 'debug') as mock_debug:
            self.middleware.process_request(request)
            
            # Should truncate body
            args, kwargs = mock_debug.call_args
            self.assertTrue(kwargs['body'].endswith('...(truncated)'))
            self.assertEqual(len(kwargs['body']), 1000 + len('...(truncated)'))
    
    def test_handles_binary_request_body(self):
        """Test that middleware handles binary request bodies."""
        # Use actual non-UTF8 binary data
        request = self.factory.post(
            '/api/test',
            data=b'\xff\xfe\xfd\xfc',  # Invalid UTF-8 sequence
            content_type='application/octet-stream'
        )
        request.user = AnonymousUser()
        
        with patch.object(self.middleware.logger, 'debug') as mock_debug:
            self.middleware.process_request(request)
            
            # Should handle binary data
            args, kwargs = mock_debug.call_args
            self.assertEqual(kwargs['body'], '<binary or unparseable>')
    
    def test_logs_api_key(self):
        """Test that middleware logs masked API key."""
        request = self.factory.get(
            '/api/test',
            HTTP_X_API_KEY='sk_test_1234567890abcdef'
        )
        request.user = AnonymousUser()
        
        self.middleware.process_request(request)
        
        context = get_context()
        self.assertEqual(context['api_key'], 'sk_t...cdef')
    
    def test_logs_short_api_key(self):
        """Test that middleware handles short API keys."""
        request = self.factory.get(
            '/api/test',
            HTTP_X_API_KEY='short'
        )
        request.user = AnonymousUser()
        
        self.middleware.process_request(request)
        
        context = get_context()
        self.assertEqual(context['api_key'], '***')
    
    @patch('core.logging.middleware.time.time')
    def test_api_sla_warning(self, mock_time):
        """Test that API SLA violations generate warnings."""
        # Set up time mocking
        start_time = 1000.0
        end_time = 1000.3  # 300ms later (exceeds 200ms SLA)
        mock_time.side_effect = [start_time, end_time, end_time]
        
        request = self.factory.get('/api/test')
        request.user = AnonymousUser()
        
        # Process request
        self.middleware.process_request(request)
        
        # Mock logger
        with patch.object(self.middleware.logger, 'warning') as mock_warning:
            response = HttpResponse()
            self.middleware.process_response(request, response)
            
            # Should have logged SLA warning
            mock_warning.assert_called()
            args, kwargs = mock_warning.call_args
            self.assertIn("API SLA exceeded", args[0])
            # Allow for floating point precision issues
            self.assertGreaterEqual(kwargs['duration_ms'], 299)
            self.assertLessEqual(kwargs['duration_ms'], 301)
            self.assertEqual(kwargs['sla_ms'], 200)
    
    def test_only_logs_api_paths(self):
        """Test that middleware only logs API paths."""
        request = self.factory.post(
            '/non-api/test',
            data='{"key": "value"}',
            content_type='application/json'
        )
        request.user = AnonymousUser()
        
        with patch.object(self.middleware.logger, 'debug') as mock_debug:
            self.middleware.process_request(request)
            
            # Should not log non-API request body
            mock_debug.assert_not_called()


@pytest.mark.django_db
class TestMiddlewareIntegration:
    """Integration tests for middleware."""
    
    def test_middleware_chain(self, client):
        """Test that middleware works in Django's middleware chain."""
        response = client.get('/')
        
        # Should have X-Request-ID header
        assert 'X-Request-ID' in response
        assert response['X-Request-ID']
    
    def test_middleware_with_authenticated_user(self, client, django_user_model):
        """Test middleware with authenticated user."""
        user = django_user_model.objects.create_user(
            username='testuser',
            password='testpass'
        )
        
        client.force_login(user)
        
        # Make request with patched logger to capture context
        with patch('core.logging.middleware.ContextLogger') as mock_logger_class:
            mock_logger = Mock()
            mock_logger_class.return_value = mock_logger
            mock_logger_class.get_logger.return_value = mock_logger
            
            client.get('/')
            
            # Should have logged with user context
            mock_logger.info.assert_called()
            call_args = mock_logger.info.call_args_list[0]
            _, kwargs = call_args
            
            # The context is set via set_context, not passed to logger
            # So we can't check it directly in the logger call
    
    def test_middleware_exception_handling(self, client):
        """Test middleware handles view exceptions properly."""
        # This would require a view that raises an exception
        # For now, we'll just ensure the middleware doesn't break
        # normal request flow
        response = client.get('/admin/')  # Should redirect to login
        assert response.status_code in [301, 302]