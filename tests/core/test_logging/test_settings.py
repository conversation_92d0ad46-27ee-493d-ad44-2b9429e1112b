"""
Test settings for logging integration tests.
"""

import os

# Import base test settings
from SocialManager.settings.test import *  # noqa: F403, F405

# Add test_app to INSTALLED_APPS for integration tests
INSTALLED_APPS.append('tests.core.test_logging.test_app')  # noqa: F405

# Update ROOT_URLCONF to include test URLs
ROOT_URLCONF = 'tests.core.test_logging.test_urls'

# Enable Sentry for integration tests if needed
SENTRY_DSN = os.environ.get('TEST_SENTRY_DSN', '')

# Configure logging for tests
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'verbose': {
            'format': '%(levelname)s %(asctime)s %(name)s %(message)s'
        },
    },
    'filters': {
        'sensitive_data': {
            '()': 'core.logging.filters.SensitiveDataFilter',
        },
        'rate_limit': {
            '()': 'core.logging.filters.RateLimitFilter',
        },
    },
    'handlers': {
        'console': {
            'class': 'logging.StreamHandler',
            'formatter': 'verbose',
            'filters': ['sensitive_data'],
        },
        'sentry': {
            'class': 'core.logging.handlers.SentryHandler',
            'level': 'ERROR',
            'filters': ['sensitive_data', 'rate_limit'],
        },
    },
    'root': {
        'handlers': ['console'],
        'level': 'DEBUG',
    },
    'loggers': {
        'django.db.backends': {
            'level': 'WARNING',
        },
        'test': {
            'handlers': ['console', 'sentry'],
            'level': 'DEBUG',
            'propagate': False,
        },
    },
}

# Ensure middleware is properly configured
# Remove existing middleware first to control order
MIDDLEWARE = [m for m in MIDDLEWARE if not any(  # noqa: F405
    mw in m for mw in [
        'LoggingContextMiddleware',
        'RequestIDMiddleware', 
        'APILoggingMiddleware',
        'SentryContextMiddleware'
    ]
)]

# Add our middleware in the correct order
MIDDLEWARE = [
    'core.logging.middleware.RequestIDMiddleware',
    'core.logging.middleware.LoggingContextMiddleware',
] + MIDDLEWARE + [
    'core.logging.middleware.APILoggingMiddleware',
    'core.middleware.sentry_middleware.SentryContextMiddleware',
]