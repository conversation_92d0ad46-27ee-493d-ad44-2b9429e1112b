"""
Tests for logging testing utilities.

These tests verify the LogCapture utility works correctly.
"""

import logging
import pytest

from core.logging.testing import LogCapture


class TestLogCapture:
    """Test cases for LogCapture utility."""
    
    def test_init_creates_empty_collections(self):
        """Test that LogCapture initializes with empty collections."""
        capture = LogCapture()
        
        assert isinstance(capture.records, list)
        assert isinstance(capture.messages, list)
        assert len(capture.records) == 0
        assert len(capture.messages) == 0
        
    def test_capture_stores_record(self):
        """Test that capture() stores the log record."""
        capture = LogCapture()
        
        # Create a log record
        record = logging.LogRecord(
            name="test",
            level=logging.INFO,
            pathname="test.py",
            lineno=1,
            msg="Test message",
            args=(),
            exc_info=None
        )
        
        # Capture it
        capture.capture(record)
        
        # Verify it was stored
        assert len(capture.records) == 1
        assert capture.records[0] is record
        
    def test_capture_stores_message(self):
        """Test that capture() stores the formatted message."""
        capture = LogCapture()
        
        # Create a log record with formatting
        record = logging.LogRecord(
            name="test",
            level=logging.INFO,
            pathname="test.py",
            lineno=1,
            msg="Hello %s",
            args=("World",),
            exc_info=None
        )
        
        # Capture it
        capture.capture(record)
        
        # Verify message was formatted and stored
        assert len(capture.messages) == 1
        assert capture.messages[0] == "Hello World"
        
    def test_capture_multiple_records(self):
        """Test capturing multiple records."""
        capture = LogCapture()
        
        # Create multiple records
        for i in range(5):
            record = logging.LogRecord(
                name="test",
                level=logging.INFO,
                pathname="test.py",
                lineno=i,
                msg=f"Message {i}",
                args=(),
                exc_info=None
            )
            capture.capture(record)
            
        # Verify all were captured
        assert len(capture.records) == 5
        assert len(capture.messages) == 5
        assert capture.messages == [f"Message {i}" for i in range(5)]
        
    def test_reset_clears_collections(self):
        """Test that reset() clears all captured data."""
        capture = LogCapture()
        
        # Add some records
        for i in range(3):
            record = logging.LogRecord(
                name="test",
                level=logging.INFO,
                pathname="test.py",
                lineno=i,
                msg=f"Message {i}",
                args=(),
                exc_info=None
            )
            capture.capture(record)
            
        # Verify they were captured
        assert len(capture.records) == 3
        assert len(capture.messages) == 3
        
        # Reset
        capture.reset()
        
        # Verify everything was cleared
        assert len(capture.records) == 0
        assert len(capture.messages) == 0
        
    def test_assert_logged_success(self):
        """Test assert_logged() when message exists."""
        capture = LogCapture()
        
        # Capture a message
        record = logging.LogRecord(
            name="test",
            level=logging.INFO,
            pathname="test.py",
            lineno=1,
            msg="Expected message",
            args=(),
            exc_info=None
        )
        capture.capture(record)
        
        # Should not raise
        capture.assert_logged("Expected message")
        
    def test_assert_logged_failure(self):
        """Test assert_logged() when message doesn't exist."""
        capture = LogCapture()
        
        # Capture a different message
        record = logging.LogRecord(
            name="test",
            level=logging.INFO,
            pathname="test.py",
            lineno=1,
            msg="Different message",
            args=(),
            exc_info=None
        )
        capture.capture(record)
        
        # Should raise AssertionError
        with pytest.raises(AssertionError) as exc_info:
            capture.assert_logged("Expected message")
            
        assert "Message 'Expected message' not found in logs" in str(exc_info.value)
        
    def test_assert_logged_with_level_success(self):
        """Test assert_logged() with correct level."""
        capture = LogCapture()
        
        # Capture an error message
        record = logging.LogRecord(
            name="test",
            level=logging.ERROR,
            pathname="test.py",
            lineno=1,
            msg="Error message",
            args=(),
            exc_info=None
        )
        capture.capture(record)
        
        # Should not raise when level matches
        capture.assert_logged("Error message", level=logging.ERROR)
        
    def test_assert_logged_with_level_failure(self):
        """Test assert_logged() with incorrect level."""
        capture = LogCapture()
        
        # Capture an info message
        record = logging.LogRecord(
            name="test",
            level=logging.INFO,
            pathname="test.py",
            lineno=1,
            msg="Info message",
            args=(),
            exc_info=None
        )
        capture.capture(record)
        
        # Should raise when level doesn't match
        with pytest.raises(AssertionError) as exc_info:
            capture.assert_logged("Info message", level=logging.ERROR)
            
        assert "logged with level INFO, expected ERROR" in str(exc_info.value)
        
    def test_assert_logged_finds_among_multiple(self):
        """Test assert_logged() finds message among multiple logs."""
        capture = LogCapture()
        
        # Capture multiple messages
        messages = ["First", "Second", "Target message", "Fourth"]
        for msg in messages:
            record = logging.LogRecord(
                name="test",
                level=logging.INFO,
                pathname="test.py",
                lineno=1,
                msg=msg,
                args=(),
                exc_info=None
            )
            capture.capture(record)
            
        # Should find the target message
        capture.assert_logged("Target message")
        
    def test_integration_with_logger(self):
        """Test integration with actual logger."""
        capture = LogCapture()
        
        # Create a custom handler that uses our capture
        class CaptureHandler(logging.Handler):
            def emit(self, record):
                capture.capture(record)
                
        # Set up logger
        logger = logging.getLogger("test_logger")
        logger.setLevel(logging.DEBUG)
        logger.handlers.clear()
        handler = CaptureHandler()
        logger.addHandler(handler)
        
        # Log some messages
        logger.debug("Debug message")
        logger.info("Info message")
        logger.warning("Warning message")
        logger.error("Error message")
        
        # Verify all were captured
        assert len(capture.records) == 4
        assert len(capture.messages) == 4
        
        # Verify assertions work
        capture.assert_logged("Debug message", level=logging.DEBUG)
        capture.assert_logged("Info message", level=logging.INFO)
        capture.assert_logged("Warning message", level=logging.WARNING)
        capture.assert_logged("Error message", level=logging.ERROR)
        
    def test_formatted_messages(self):
        """Test capturing formatted log messages."""
        capture = LogCapture()
        
        # Various formatting styles
        records = [
            logging.LogRecord(
                name="test", level=logging.INFO, pathname="test.py", lineno=1,
                msg="User %s logged in", args=("john",), exc_info=None
            ),
            logging.LogRecord(
                name="test", level=logging.INFO, pathname="test.py", lineno=1,
                msg="Processing %d items", args=(42,), exc_info=None
            ),
            logging.LogRecord(
                name="test", level=logging.INFO, pathname="test.py", lineno=1,
                msg="Status: %(status)s, Count: %(count)d", 
                args={"status": "OK", "count": 10}, exc_info=None
            ),
        ]
        
        for record in records:
            capture.capture(record)
            
        # Verify formatted messages
        assert "User john logged in" in capture.messages
        assert "Processing 42 items" in capture.messages
        assert "Status: OK, Count: 10" in capture.messages