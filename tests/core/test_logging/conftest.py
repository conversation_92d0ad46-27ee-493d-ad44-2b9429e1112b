"""
Configuration for logging tests.

This file re-enables logging for tests in this directory,
since the main conftest.py disables it globally.
It also configures the URL configuration for integration tests.
"""

import logging
import pytest
from django.conf import settings

# Re-enable logging for logging tests
logging.disable(logging.NOTSET)


@pytest.fixture(autouse=True)
def configure_test_urls():
    """
    Configure Django to use test URLs for integration tests.
    
    This fixture automatically runs for all tests in this directory
    and ensures that Django uses the correct URL configuration
    that includes the test app URLs.
    """
    # Store original URL config
    original_urlconf = getattr(settings, 'ROOT_URLCONF', None)
    
    # Set test URL config
    settings.ROOT_URLCONF = 'tests.core.test_logging.test_urls'
    
    # Run the test
    yield
    
    # Restore original URL config
    if original_urlconf:
        settings.ROOT_URLCONF = original_urlconf