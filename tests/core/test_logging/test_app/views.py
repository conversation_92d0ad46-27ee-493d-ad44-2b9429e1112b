"""
Test views for integration testing.
"""

import time

from django.http import HttpRequest, HttpResponse, JsonResponse
from django.views import View
from django.views.decorators.http import require_http_methods

from core.logging import (
    ContextLogger,
    add_context,
    set_request_id,
)
from core.logging.logger import context_logging


logger = ContextLogger.get_logger(__name__)


def test_view(request: HttpRequest) -> HttpResponse:
    """Simple test view that logs at different levels."""
    logger.debug("Test debug message")
    logger.info("Test info message", user_agent=request.META.get("HTTP_USER_AGENT"))
    logger.warning("Test warning message")
    
    return HttpResponse("OK")


def error_view(request: HttpRequest) -> HttpResponse:
    """View that raises an exception."""
    logger.info("About to raise an exception")
    raise ValueError("Test exception from view")


def context_view(request: HttpRequest) -> HttpResponse:
    """View that tests context propagation."""
    # Add custom context
    add_context(
        view_name="context_view",
        custom_field="test_value",
        user_ip=request.META.get("REMOTE_ADDR"),
    )
    
    logger.info("View with custom context")
    
    # Nested context
    with context_logging(nested=True, level=1):
        logger.info("Nested context level 1")
        
        with context_logging(nested=True, level=2):
            logger.info("Nested context level 2")
    
    logger.info("Back to original context")
    
    return JsonResponse({"status": "ok", "context": "tested"})


def sensitive_data_view(request: HttpRequest) -> HttpResponse:
    """View that logs sensitive data to test filtering."""
    # Log with various sensitive patterns
    logger.info(
        "User login attempt",
        username="testuser",
        password="secretpass123",  # Should be filtered
        api_key="sk-1234567890abcdef",  # Should be filtered
        credit_card="****************",  # Should be filtered
        email="<EMAIL>",
        ssn="***********",  # Should be filtered
    )
    
    # Test nested sensitive data
    user_data = {
        "name": "John Doe",
        "credentials": {
            "password": "another_secret",  # Should be filtered
            "token": "Bearer eyJ0eXAiOiJKV1Q...",  # Should be filtered
        },
        "profile": {
            "age": 30,
            "address": "123 Main St",
        },
    }
    
    logger.warning("User data processed", user_data=user_data)
    
    return HttpResponse("Sensitive data logged")


def performance_view(request: HttpRequest) -> HttpResponse:
    """View with simulated performance issues."""
    start_time = time.time()
    
    # Simulate some work
    result = 0
    for i in range(1000000):
        result += i
    
    # Simulate slow database query
    time.sleep(0.1)
    
    duration = time.time() - start_time
    logger.info(
        "Performance view completed",
        duration_seconds=duration,
        computation_result=result,
    )
    
    # Log error if too slow
    if duration > 0.5:
        logger.error("Performance view too slow", duration=duration)
    
    return JsonResponse({
        "duration": duration,
        "result": result,
    })


class AsyncSimulationView(View):
    """View that simulates async behavior for context isolation testing."""
    
    def get(self, request: HttpRequest) -> HttpResponse:
        """Handle GET request with context simulation."""
        request_id = request.GET.get("request_id", "default")
        set_request_id(request_id)
        
        # Log with specific context
        logger.info(
            "Async simulation started",
            request_id=request_id,
            method="GET",
        )
        
        # Simulate some processing
        time.sleep(0.05)
        
        logger.info(
            "Async simulation completed",
            request_id=request_id,
        )
        
        return JsonResponse({
            "request_id": request_id,
            "status": "completed",
        })


@require_http_methods(["POST"])
def webhook_view(request: HttpRequest) -> HttpResponse:
    """Webhook endpoint for testing request logging."""
    # Parse body
    try:
        import json
        body = json.loads(request.body) if request.body else {}
    except Exception as e:
        logger.error("Failed to parse webhook body", error=str(e))
        body = {}
    
    # Log webhook data
    logger.info(
        "Webhook received",
        webhook_type=body.get("type"),
        event_id=body.get("event_id"),
        headers={
            k: v for k, v in request.META.items() 
            if k.startswith("HTTP_")
        },
    )
    
    # Process webhook
    if body.get("type") == "error":
        logger.error("Webhook error event", details=body.get("details"))
        return JsonResponse({"status": "error"}, status=400)
    
    return JsonResponse({"status": "processed", "event_id": body.get("event_id")})


def rate_limit_test_view(request: HttpRequest) -> HttpResponse:
    """View to test rate limiting of logs."""
    # Generate many log messages quickly
    message_id = request.GET.get("id", "test")
    
    for i in range(20):
        logger.warning(
            "Rate limit test message",
            message_id=message_id,
            iteration=i,
        )
    
    return HttpResponse(f"Generated 20 log messages with id={message_id}")