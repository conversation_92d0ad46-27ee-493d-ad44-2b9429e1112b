"""
Test Celery tasks for integration testing.
"""

import time
from typing import Any, Dict

from celery import shared_task, Task

from core.logging import (
    ContextLogger,
    add_context,
    get_context,
)


logger = ContextLogger.get_logger(__name__)


@shared_task(bind=True)
def simple_task(self: Task, message: str) -> Dict[str, Any]:
    """Simple task that logs messages."""
    logger.info("Simple task started", message=message)
    
    # Simulate some work
    time.sleep(0.1)
    
    logger.info("Simple task completed", message=message)
    
    return {
        "status": "completed",
        "message": message,
        "task_id": self.request.id,
    }


@shared_task(bind=True)
def error_task(self: Task, should_retry: bool = False) -> None:
    """Task that raises an exception."""
    logger.info("Error task started", should_retry=should_retry)
    
    if should_retry and self.request.retries < 2:
        logger.warning("Task will be retried", retries=self.request.retries)
        raise self.retry(exc=Exception("Retry me"), countdown=1)
    
    logger.error("Task is failing")
    raise RuntimeError("Task failed intentionally")


@shared_task(bind=True)
def context_task(self: Task, user_id: int, operation: str) -> Dict[str, Any]:
    """Task that tests context propagation."""
    # Add task-specific context
    add_context(
        user_id=user_id,
        operation=operation,
        task_name=self.name,
    )
    
    logger.info("Context task started")
    
    # Get current context
    current_context = get_context()
    logger.info("Current context in task", context_data=current_context)
    
    # Nested operations
    result = _perform_operation(operation)
    
    logger.info("Context task completed", result=result)
    
    return {
        "status": "completed",
        "user_id": user_id,
        "operation": operation,
        "result": result,
        "context": current_context,
    }


def _perform_operation(operation: str) -> Any:
    """Helper function to test context propagation."""
    logger.info("Performing operation", operation=operation)
    
    if operation == "process":
        time.sleep(0.05)
        return {"processed": True}
    elif operation == "validate":
        return {"valid": True}
    else:
        logger.warning("Unknown operation", operation=operation)
        return {"unknown": True}


@shared_task(bind=True, max_retries=3)
def retry_task(self: Task, attempt_number: int) -> Dict[str, Any]:
    """Task that tests retry behavior and logging."""
    logger.info(
        "Retry task attempt",
        attempt=attempt_number,
        retries=self.request.retries,
        max_retries=self.max_retries,
    )
    
    if self.request.retries < 2:
        logger.warning("Task failing, will retry")
        raise self.retry(
            exc=ValueError(f"Attempt {attempt_number} failed"),
            countdown=1,
        )
    
    logger.info("Task succeeded after retries")
    return {
        "status": "completed",
        "attempts": self.request.retries + 1,
    }


@shared_task
def chain_task_1(data: Dict[str, Any]) -> Dict[str, Any]:
    """First task in a chain."""
    logger.info("Chain task 1 started", input_data=data)
    
    # Process data
    data["step1"] = "completed"
    data["timestamp1"] = time.time()
    
    logger.info("Chain task 1 completed")
    return data


@shared_task
def chain_task_2(data: Dict[str, Any]) -> Dict[str, Any]:
    """Second task in a chain."""
    logger.info("Chain task 2 started", input_data=data)
    
    # Verify previous step
    if "step1" not in data:
        logger.error("Missing step1 data")
        raise ValueError("Invalid chain state")
    
    # Process data
    data["step2"] = "completed"
    data["timestamp2"] = time.time()
    
    logger.info("Chain task 2 completed")
    return data


@shared_task(bind=True)
def performance_task(self: Task, iterations: int = 10000) -> Dict[str, Any]:
    """Task to test performance logging."""
    start_time = time.time()
    
    logger.info("Performance task started", iterations=iterations)
    
    # Simulate CPU-intensive work
    result = 0
    for i in range(iterations):
        result += i ** 2
        
        # Log progress periodically
        if i % 1000 == 0 and i > 0:
            logger.debug(
                "Performance task progress",
                completed=i,
                total=iterations,
                percentage=round(i / iterations * 100, 2),
            )
    
    duration = time.time() - start_time
    
    logger.info(
        "Performance task completed",
        duration_seconds=duration,
        iterations=iterations,
        result=result,
        operations_per_second=round(iterations / duration, 2),
    )
    
    return {
        "status": "completed",
        "duration": duration,
        "result": result,
        "task_id": self.request.id,
    }