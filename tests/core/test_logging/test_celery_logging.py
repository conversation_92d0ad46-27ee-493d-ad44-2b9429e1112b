"""
Comprehensive tests for Celery logging integration.

This module tests:
- CeleryContextPlugin for automatic context injection
- Context propagation between Celery processes
- Task logging with proper context
- Error handling and retry logic with logging
- Performance of logging in Celery tasks
- Proper context cleanup after task execution
"""

import time
import timeit
from unittest.mock import Mock, patch

import pytest
from django.test import TestCase, override_settings
from celery import chain, group, chord, Task
from celery.exceptions import Retry
from celery.signals import (
    task_prerun,
    task_postrun,
    task_failure,
    task_retry,
    task_revoked,
)

from core.logging import (
    ContextLogger,
    clear_context,
    get_context,
    add_context,
    set_task_id,
)
from core.logging.celery_handlers import (
    setup_task_logging_context,
    cleanup_task_logging_context,
    log_task_failure,
    log_task_retry,
    log_task_revoked,
)

from tests.core.test_logging.test_app.tasks import (
    simple_task,
    error_task,
    context_task,
    retry_task,
    chain_task_1,
    chain_task_2,
    performance_task,
)


class TestCeleryContextPlugin(TestCase):
    """Test CeleryContextPlugin functionality."""
    
    def setUp(self):
        """Set up test environment."""
        clear_context()
        self.logger = ContextLogger.get_logger('test.celery.plugin')
        # Store original signal receivers
        self.original_receivers = {
            task_prerun: task_prerun.receivers[:],
            task_postrun: task_postrun.receivers[:],
            task_failure: task_failure.receivers[:],
            task_retry: task_retry.receivers[:],
            task_revoked: task_revoked.receivers[:],
        }
    
    def tearDown(self):
        """Clean up after tests."""
        clear_context()
        # Restore original receivers to avoid side effects
        for signal, receivers in self.original_receivers.items():
            signal.receivers = receivers[:]
    
    def test_plugin_registration(self):
        """Test that Celery signal handlers are properly registered."""
        # Check that our handlers are connected
        prerun_handlers = [r[1]() for r in task_prerun.receivers if r[1]()]
        self.assertIn(setup_task_logging_context, prerun_handlers)
        
        postrun_handlers = [r[1]() for r in task_postrun.receivers if r[1]()]
        self.assertIn(cleanup_task_logging_context, postrun_handlers)
        
        failure_handlers = [r[1]() for r in task_failure.receivers if r[1]()]
        self.assertIn(log_task_failure, failure_handlers)
        
        retry_handlers = [r[1]() for r in task_retry.receivers if r[1]()]
        self.assertIn(log_task_retry, retry_handlers)
        
        revoked_handlers = [r[1]() for r in task_revoked.receivers if r[1]()]
        self.assertIn(log_task_revoked, revoked_handlers)
    
    def test_context_injection_on_task_start(self):
        """Test that context is injected when task starts."""
        # Mock task
        mock_task = Mock(spec=Task)
        mock_task.name = 'test.task'
        
        # Clear context first
        clear_context()
        
        # Trigger signal
        setup_task_logging_context(
            sender=mock_task,
            task_id='test-task-123',
            task=mock_task,
            args=('arg1', 'arg2'),
            kwargs={'key': 'value'}
        )
        
        # Check context was set
        context = get_context()
        self.assertEqual(context['task_id'], 'test-task-123')
        self.assertEqual(context['task_name'], 'test.task')
        self.assertEqual(context['task_args_count'], 2)
        self.assertEqual(context['task_kwargs_keys'], ['key'])
    
    def test_context_cleanup_on_task_completion(self):
        """Test that context is cleaned up after task completion."""
        # Set up context
        set_task_id('test-task-456')
        add_context(task_name='test.cleanup', custom='data')
        
        # Mock task
        mock_task = Mock(spec=Task)
        mock_task.name = 'test.cleanup'
        
        # Trigger cleanup
        cleanup_task_logging_context(
            sender=mock_task,
            task_id='test-task-456',
            task=mock_task,
            state='SUCCESS'
        )
        
        # Check context was cleared
        context = get_context()
        self.assertNotIn('task_id', context)
        self.assertNotIn('task_name', context)
        self.assertNotIn('custom', context)
    
    def test_multiple_signal_handlers(self):
        """Test that multiple handlers can coexist."""
        called_handlers = []
        
        # Add custom handler
        @task_prerun.connect
        def custom_handler(sender=None, **kwargs):
            called_handlers.append('custom')
        
        try:
            # Mock task
            mock_task = Mock(spec=Task)
            mock_task.name = 'test.multiple'
            
            # Trigger signal
            task_prerun.send(
                sender=mock_task,
                task_id='test-multiple-123',
                task=mock_task
            )
            
            # Check both handlers were called
            self.assertIn('custom', called_handlers)
            # Our handler should also have set context
            context = get_context()
            self.assertEqual(context['task_id'], 'test-multiple-123')
        
        finally:
            # Disconnect custom handler
            task_prerun.disconnect(custom_handler)
    
    @patch('core.logging.celery_handlers.logger')
    def test_logging_in_handlers(self, mock_logger):
        """Test that handlers log appropriately."""
        # Mock task
        mock_task = Mock(spec=Task)
        mock_task.name = 'test.logging'
        
        # Test prerun logging
        setup_task_logging_context(
            sender=mock_task,
            task_id='test-log-123',
            task=mock_task
        )
        
        mock_logger.info.assert_called_with(
            "Task started",
            task_name='test.logging',
            task_id='test-log-123'
        )
        
        # Test postrun logging
        cleanup_task_logging_context(
            sender=mock_task,
            task_id='test-log-123',
            task=mock_task,
            state='SUCCESS'
        )
        
        mock_logger.info.assert_called_with(
            "Task completed",
            task_name='test.logging',
            task_id='test-log-123',
            state='SUCCESS'
        )


class TestCeleryContextPropagation(TestCase):
    """Test context propagation between Celery processes."""
    
    def setUp(self):
        """Set up test environment."""
        clear_context()
        self.logger = ContextLogger.get_logger('test.celery.propagation')
    
    def tearDown(self):
        """Clean up after tests."""
        clear_context()
    
    @override_settings(CELERY_TASK_ALWAYS_EAGER=True)
    def test_context_in_simple_task(self):
        """Test context is available in simple task."""
        # Execute task
        result = simple_task.apply(args=["Test message"])
        
        # Task should succeed
        self.assertEqual(result.status, 'SUCCESS')
        self.assertEqual(result.result['status'], 'completed')
        
        # Task ID should be in result
        self.assertIn('task_id', result.result)
    
    @override_settings(CELERY_TASK_ALWAYS_EAGER=True)
    def test_context_propagation_in_task(self):
        """Test that context is properly propagated within task."""
        # Execute context task
        result = context_task.apply(args=[123, "process"])
        
        # Check result
        self.assertEqual(result.status, 'SUCCESS')
        self.assertEqual(result.result['user_id'], 123)
        self.assertEqual(result.result['operation'], 'process')
        
        # Context should have been captured
        task_context = result.result['context']
        self.assertIn('task_id', task_context)
        self.assertIn('user_id', task_context)
        self.assertIn('operation', task_context)
    
    @override_settings(CELERY_TASK_ALWAYS_EAGER=True)
    def test_context_in_chain(self):
        """Test context propagation in task chains."""
        # Create chain
        workflow = chain(
            chain_task_1.s({'initial': True}),
            chain_task_2.s()
        )
        
        # Execute chain
        result = workflow.apply()
        
        # Check result
        self.assertEqual(result.status, 'SUCCESS')
        self.assertEqual(result.result['step1'], 'completed')
        self.assertEqual(result.result['step2'], 'completed')
    
    @override_settings(CELERY_TASK_ALWAYS_EAGER=True)
    def test_context_in_group(self):
        """Test context in grouped tasks."""
        # Create group
        job = group(
            simple_task.s("Message 1"),
            simple_task.s("Message 2"),
            simple_task.s("Message 3")
        )
        
        # Execute group
        result = job.apply()
        
        # All tasks should succeed
        for task_result in result:
            self.assertEqual(task_result.status, 'SUCCESS')
            self.assertEqual(task_result.result['status'], 'completed')
            self.assertIn('task_id', task_result.result)
    
    @override_settings(CELERY_TASK_ALWAYS_EAGER=True)
    def test_context_in_chord(self):
        """Test context in chord (group with callback)."""
        # Import shared_task for callback
        from celery import shared_task
        
        # Define callback task
        @shared_task
        def chord_callback(results):
            return {
                'callback_status': 'completed',
                'results_count': len(results),
                'all_completed': all(r['status'] == 'completed' for r in results)
            }
        
        # Create chord
        header = group(
            simple_task.s("Chord 1"),
            simple_task.s("Chord 2")
        )
        callback = chord_callback.s()
        
        # Execute chord
        # In eager mode, chord returns an EagerResult
        result = chord(header)(callback)
        
        # Get the actual result
        if hasattr(result, 'result'):
            actual_result = result.result
        else:
            actual_result = result
        
        # Check callback result
        self.assertEqual(actual_result['callback_status'], 'completed')
        self.assertEqual(actual_result['results_count'], 2)
        self.assertTrue(actual_result['all_completed'])
    
    def test_context_isolation_between_tasks(self):
        """Test that context is isolated between concurrent tasks."""
        # This would require actual Celery worker to test properly
        # In eager mode, tasks run sequentially
        pass


class TestCeleryTaskLogging(TestCase):
    """Test logging within Celery tasks."""
    
    def setUp(self):
        """Set up test environment."""
        clear_context()
        self.logger = ContextLogger.get_logger('test.celery.task_logging')
    
    def tearDown(self):
        """Clean up after tests."""
        clear_context()
    
    @override_settings(CELERY_TASK_ALWAYS_EAGER=True)
    @patch('tests.core.test_logging.test_app.tasks.logger')
    def test_task_logging_includes_context(self, mock_logger):
        """Test that task logs include proper context."""
        # Execute task
        simple_task.apply(args=["Test logging"])
        
        # Check logging calls
        self.assertEqual(mock_logger.info.call_count, 2)
        
        # First call - task started
        first_call = mock_logger.info.call_args_list[0]
        self.assertEqual(first_call[0][0], "Simple task started")
        self.assertEqual(first_call[1]['message'], "Test logging")
        
        # Second call - task completed
        second_call = mock_logger.info.call_args_list[1]
        self.assertEqual(second_call[0][0], "Simple task completed")
        self.assertEqual(second_call[1]['message'], "Test logging")
    
    @override_settings(CELERY_TASK_ALWAYS_EAGER=True)
    def test_task_logger_factory(self):
        """Test creating task-specific logger."""
        # Create task logger
        task_logger = ContextLogger.get_task_logger('my_task', 'task-123')
        
        # Check logger name
        self.assertEqual(task_logger.name, 'task.my_task')
        
        # Check context was set
        context = get_context()
        self.assertEqual(context['task_id'], 'task-123')
    
    @override_settings(CELERY_TASK_ALWAYS_EAGER=True)
    @patch('tests.core.test_logging.test_app.tasks.logger')
    def test_nested_function_logging(self, mock_logger):
        """Test logging in nested functions within tasks."""
        # Execute context task
        context_task.apply(args=[456, "validate"])
        
        # Check that nested function logged
        logged_messages = [call[0][0] for call in mock_logger.info.call_args_list]
        self.assertIn("Performing operation", logged_messages)
        
        # Check operation was logged
        operation_call = next(
            call for call in mock_logger.info.call_args_list
            if call[0][0] == "Performing operation"
        )
        self.assertEqual(operation_call[1]['operation'], 'validate')
    
    @override_settings(CELERY_TASK_ALWAYS_EAGER=True)
    def test_task_result_includes_context(self):
        """Test that task results can include context info."""
        # Execute task
        result = context_task.apply(args=[789, "process"])
        
        # Check result includes context
        self.assertIn('context', result.result)
        context = result.result['context']
        
        # Should have task context
        self.assertIn('task_id', context)
        self.assertIn('user_id', context)
        self.assertEqual(context['user_id'], 789)
    
    @override_settings(CELERY_TASK_ALWAYS_EAGER=True)
    @patch('tests.core.test_logging.test_app.tasks.logger')
    def test_performance_task_logging(self, mock_logger):
        """Test logging in performance-intensive tasks."""
        # Execute performance task with small iteration count
        result = performance_task.apply(args=[100])
        
        # Check task completed
        self.assertEqual(result.status, 'SUCCESS')
        
        # Check logging
        start_call = mock_logger.info.call_args_list[0]
        self.assertEqual(start_call[0][0], "Performance task started")
        self.assertEqual(start_call[1]['iterations'], 100)
        
        # Should have completion log
        completion_logs = [
            call for call in mock_logger.info.call_args_list
            if call[0][0] == "Performance task completed"
        ]
        self.assertEqual(len(completion_logs), 1)
        
        completion_call = completion_logs[0]
        self.assertIn('duration_seconds', completion_call[1])
        self.assertIn('operations_per_second', completion_call[1])


class TestCeleryErrorHandling(TestCase):
    """Test error handling and retry logic with logging."""
    
    def setUp(self):
        """Set up test environment."""
        clear_context()
        self.logger = ContextLogger.get_logger('test.celery.errors')
    
    def tearDown(self):
        """Clean up after tests."""
        clear_context()
    
    @override_settings(CELERY_TASK_ALWAYS_EAGER=True)
    def test_task_failure_logging(self):
        """Test logging when task fails."""
        # In eager mode, we test that the task logs error messages
        # rather than testing signal handlers which may not be triggered
        
        # Mock the task logger
        with patch('tests.core.test_logging.test_app.tasks.logger') as mock_task_logger:
            # Execute failing task
            with self.assertRaises(RuntimeError):
                result = error_task.apply(args=[False])
                if result.failed():
                    result.get()
            
            # Check that error was logged by the task
            error_calls = [
                call for call in mock_task_logger.error.call_args_list
                if call[0][0] == "Task is failing"
            ]
            self.assertEqual(len(error_calls), 1)
    
    @override_settings(CELERY_TASK_ALWAYS_EAGER=True)
    def test_task_retry_logging(self):
        """Test logging when task is retried."""
        # In eager mode, we test that the task logs retry messages
        # rather than testing signal handlers which may not be triggered
        
        # Mock the task logger
        with patch('tests.core.test_logging.test_app.tasks.logger') as mock_task_logger:
            with self.assertRaises(Retry):
                result = retry_task.apply(args=[1])
                if result.failed():
                    result.get()
            
            # Check that retry was logged by the task
            warning_calls = [
                call for call in mock_task_logger.warning.call_args_list
                if call[0][0] == "Task failing, will retry"
            ]
            self.assertGreater(len(warning_calls), 0)
    
    @override_settings(CELERY_TASK_ALWAYS_EAGER=True)
    @patch('tests.core.test_logging.test_app.tasks.logger')
    def test_retry_task_context(self, mock_task_logger):
        """Test context is maintained across retries."""
        # This will fail with Retry exception in eager mode
        with self.assertRaises(Retry):
            retry_task.apply(args=[1])
        
        # Check task logged retry info
        warning_calls = [
            call for call in mock_task_logger.warning.call_args_list
            if call[0][0] == "Task failing, will retry"
        ]
        self.assertGreater(len(warning_calls), 0)
    
    @override_settings(CELERY_TASK_ALWAYS_EAGER=True)
    @patch('core.logging.celery_handlers.logger')
    def test_task_revocation_logging(self, mock_logger):
        """Test logging when task is revoked."""
        # Create mock request
        mock_request = Mock()
        mock_request.id = 'revoked-task-123'
        mock_request.task = 'test.revoked.task'
        
        # Trigger revoked signal
        log_task_revoked(
            sender=None,
            request=mock_request,
            terminated=True,
            expired=False
        )
        
        # Check revocation was logged
        mock_logger.warning.assert_called_with(
            "Task revoked",
            task_name='test.revoked.task',
            task_id='revoked-task-123',
            terminated=True,
            expired=False
        )
    
    @override_settings(CELERY_TASK_ALWAYS_EAGER=True)
    def test_exception_context_preservation(self):
        """Test that exception context is preserved in logs."""
        # Set some context before task
        add_context(request_id='req-123', user_id=456)
        
        # Execute failing task
        try:
            result = error_task.apply(args=[False])
            if result.failed():
                result.get()
        except RuntimeError:
            # Expected
            pass
        
        # Context should still be available after exception
        # (In real Celery worker, this would be in the error handler)
        context = get_context()
        # Context is cleared by postrun handler
        self.assertNotIn('request_id', context)


class TestCeleryPerformance(TestCase):
    """Test performance of logging in Celery tasks."""
    
    def setUp(self):
        """Set up test environment."""
        clear_context()
        self.logger = ContextLogger.get_logger('test.celery.performance')
    
    def tearDown(self):
        """Clean up after tests."""
        clear_context()
    
    @override_settings(CELERY_TASK_ALWAYS_EAGER=True)
    def test_logging_overhead_in_tasks(self):
        """Test that logging doesn't add significant overhead to tasks."""
        import timeit
        
        # Time task without logging
        def run_without_logging():
            # Directly call the task function
            simple_task.run("No logging")
        
        # Time task with logging (normal execution)
        def run_with_logging():
            result = simple_task.apply(args=["With logging"])
            return result.result
        
        # Warm up
        for _ in range(5):
            run_without_logging()
            run_with_logging()
        
        # Measure times
        time_without = timeit.timeit(run_without_logging, number=100)
        time_with = timeit.timeit(run_with_logging, number=100)
        
        # Logging overhead should be reasonable
        overhead_ratio = time_with / time_without
        # In eager mode, overhead can be higher due to signal handling
        self.assertLess(overhead_ratio, 5.0)
    
    @override_settings(CELERY_TASK_ALWAYS_EAGER=True)
    def test_concurrent_task_logging(self):
        """Test logging performance with concurrent tasks."""
        # In eager mode, tasks run sequentially
        # This test would need actual Celery workers for true concurrency
        
        start_time = time.time()
        
        # Execute multiple tasks
        results = []
        for i in range(10):
            result = simple_task.apply(args=[f"Concurrent {i}"])
            results.append(result)
        
        # All should complete
        for result in results:
            self.assertEqual(result.status, 'SUCCESS')
        
        duration = time.time() - start_time
        
        # Should complete reasonably quickly
        self.assertLess(duration, 2.0)
    
    @override_settings(CELERY_TASK_ALWAYS_EAGER=True)
    def test_context_lookup_performance(self):
        """Test performance of context lookups in tasks."""
        # Add some context
        add_context(
            app_name='test_app',
            environment='test',
            version='1.0.0',
            extra_data={'key': 'value'} 
        )
        
        # Time context lookups
        def lookup_context():
            for _ in range(1000):
                ctx = get_context()
                _ = ctx.get('task_id')
                _ = ctx.get('app_name')
        
        # Measure
        duration = timeit.timeit(lookup_context, number=10)
        
        # Should be fast (< 20ms for 10,000 lookups)
        self.assertLess(duration, 0.02)
    
    @override_settings(CELERY_TASK_ALWAYS_EAGER=True)
    def test_large_context_performance(self):
        """Test performance with large context data."""
        # Create large context
        large_data = {
            f'key_{i}': f'value_{i}' * 100
            for i in range(100)
        }
        
        # Execute task with large context
        start_time = time.time()
        
        add_context(**large_data)
        result = context_task.apply(args=[999, "process"])
        
        duration = time.time() - start_time
        
        # Should still complete quickly
        self.assertLess(duration, 1.0)
        self.assertEqual(result.status, 'SUCCESS')
    
    def test_memory_usage_in_long_running_tasks(self):
        """Test that context doesn't cause memory leaks in long tasks."""
        # This is a conceptual test - would need memory profiler
        # to properly test in production
        
        import gc
        
        # Force garbage collection
        gc.collect()
        
        # Execute many short tasks
        for i in range(100):
            clear_context()
            set_task_id(f'memory-test-{i}')
            add_context(iteration=i, data='x' * 1000)
            
            # Simulate task work
            time.sleep(0.001)
            
            # Clear context (as would happen in postrun)
            clear_context()
        
        # Force garbage collection
        gc.collect()
        
        # Context should be clean
        context = get_context()
        self.assertEqual(len(context), 0)


class TestCeleryContextCleanup(TestCase):
    """Test proper context cleanup after task execution."""
    
    def setUp(self):
        """Set up test environment."""
        clear_context()
        self.logger = ContextLogger.get_logger('test.celery.cleanup')
    
    def tearDown(self):
        """Clean up after tests."""
        clear_context()
    
    @override_settings(CELERY_TASK_ALWAYS_EAGER=True)
    def test_context_cleanup_after_success(self):
        """Test context is cleaned up after successful task."""
        # Add some pre-task context
        add_context(pre_task='data')
        
        # Execute task
        result = simple_task.apply(args=["Success"])
        
        # Task should succeed
        self.assertEqual(result.status, 'SUCCESS')
        
        # Context should be clean
        context = get_context()
        self.assertNotIn('task_id', context)
        self.assertNotIn('task_name', context)
        self.assertNotIn('pre_task', context)
    
    @override_settings(CELERY_TASK_ALWAYS_EAGER=True)
    def test_context_cleanup_after_failure(self):
        """Test context is cleaned up after task failure."""
        # Add some pre-task context
        add_context(pre_task='failure_data')
        
        # Execute failing task
        try:
            result = error_task.apply(args=[False])
            if result.failed():
                result.get()
        except RuntimeError:
            pass
        
        # Context should be clean
        context = get_context()
        self.assertNotIn('task_id', context)
        self.assertNotIn('task_name', context)
        self.assertNotIn('pre_task', context)
    
    @override_settings(CELERY_TASK_ALWAYS_EAGER=True)
    def test_context_cleanup_after_retry(self):
        """Test context is managed properly during retries."""
        # Execute task that will retry
        try:
            result = retry_task.apply(args=[1])
            if result.failed():
                result.get()
        except Retry:
            pass
        
        # Context should be clean after retry
        context = get_context()
        self.assertNotIn('task_id', context)
    
    def test_signal_handler_cleanup(self):
        """Test that signal handlers don't leak context."""
        # Set context
        set_task_id('leak-test-123')
        add_context(custom='leak_data')
        
        # Manually trigger postrun
        mock_task = Mock(spec=Task)
        mock_task.name = 'test.leak'
        
        cleanup_task_logging_context(
            sender=mock_task,
            task_id='leak-test-123',
            task=mock_task,
            state='SUCCESS'
        )
        
        # Context should be cleared
        context = get_context()
        self.assertEqual(len(context), 0)
    
    @override_settings(CELERY_TASK_ALWAYS_EAGER=True)
    def test_context_isolation_in_chains(self):
        """Test context isolation between chained tasks."""
        # Create chain using tasks designed for chaining
        initial_data = {'step': 0}
        workflow = chain(
            chain_task_1.s(initial_data),
            chain_task_2.s()
        )
        
        # Execute chain
        result = workflow.apply()
        
        # All tasks should succeed
        self.assertEqual(result.status, 'SUCCESS')
        self.assertEqual(result.result['step1'], 'completed')
        self.assertEqual(result.result['step2'], 'completed')
        
        # Final context should be clean
        context = get_context()
        self.assertEqual(len(context), 0)
    
    def test_context_vars_reset(self):
        """Test that context vars are properly reset."""
        from core.logging.logger import request_id_var, user_id_var, task_id_var
        
        # Set all context vars
        request_id_var.set('req-cleanup-123')
        user_id_var.set(999)
        task_id_var.set('task-cleanup-123')
        
        # Clear context
        clear_context()
        
        # All should be None
        self.assertIsNone(request_id_var.get())
        self.assertIsNone(user_id_var.get())
        self.assertIsNone(task_id_var.get())


# Additional test fixtures and utilities

@pytest.fixture
def celery_task_logger():
    """Fixture for task logger."""
    logger = ContextLogger.get_task_logger('test_task', 'fixture-task-123')
    yield logger
    clear_context()


@pytest.fixture
def mock_celery_app():
    """Fixture for mocked Celery app."""
    from celery import Celery
    
    app = Celery('test_app')
    app.conf.update(
        task_always_eager=True,
        task_eager_propagates=True,
        result_backend='cache+memory://',
        broker_url='memory://',
    )
    
    return app


def create_test_task(name='test.task', bind=False):
    """Helper to create test tasks."""
    from celery import Task
    
    class TestTask(Task):
        name = name
        bind = bind
        
        def run(self, *args, **kwargs):
            logger = ContextLogger.get_task_logger(self.name)
            logger.info(f"Executing {self.name}")
            return {'status': 'completed', 'args': args, 'kwargs': kwargs}
    
    return TestTask()


# Performance benchmarks

def benchmark_task_logging(iterations=1000):
    """Benchmark task logging performance."""
    import timeit
    
    def run_logged_task():
        result = simple_task.apply(args=["Benchmark"])
        return result.result
    
    # Warm up
    for _ in range(10):
        run_logged_task()
    
    # Benchmark
    duration = timeit.timeit(run_logged_task, number=iterations)
    ops_per_second = iterations / duration
    
    return {
        'iterations': iterations,
        'total_duration': duration,
        'ops_per_second': ops_per_second,
        'avg_duration_ms': (duration / iterations) * 1000
    }


# Memory profiling utilities

def profile_task_memory():
    """Profile memory usage during task execution."""
    import tracemalloc
    
    tracemalloc.start()
    
    # Execute tasks
    results = []
    for i in range(100):
        result = simple_task.apply(args=[f"Memory test {i}"])
        results.append(result)
    
    current, peak = tracemalloc.get_traced_memory()
    tracemalloc.stop()
    
    return {
        'current_mb': current / 1024 / 1024,
        'peak_mb': peak / 1024 / 1024,
        'tasks_executed': len(results)
    }