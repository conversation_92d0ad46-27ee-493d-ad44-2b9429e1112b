"""Tests for core form fields."""

import pytest
from django import forms

from core.forms.fields import (
    BulkURLsField,
    BulkUsernamesField,
    HashtagField,
    InstagramURLField,
    PostTypesField,
    SocialUsernameField,
)


class TestSocialUsernameField:
    """Test SocialUsernameField."""
    
    def test_field_initialization(self):
        """Test field initialization."""
        field = SocialUsernameField(platform="instagram")
        assert field.platform == "instagram"
        assert field.max_length == 30
        
    def test_clean_username_with_at(self):
        """Test username cleaning removes @."""
        field = SocialUsernameField(platform="instagram")
        
        # Test @ removal
        assert field.to_python("@username") == "username"
        assert field.to_python("username") == "username"
        
    def test_validation(self):
        """Test field validation."""
        field = SocialUsernameField(platform="instagram", required=True)
        
        # Valid username
        field.clean("valid_username")
        
        # Empty username with required=True
        with pytest.raises(forms.ValidationError):
            field.clean("")


class TestInstagramURLField:
    """Test InstagramURLField."""
    
    def test_valid_instagram_urls(self):
        """Test valid Instagram URLs."""
        field = InstagramURLField()
        
        # Should not raise
        field.clean("https://instagram.com/p/ABC123/")
        field.clean("https://instagram.com/reel/XYZ789/")
        
    def test_invalid_instagram_urls(self):
        """Test invalid Instagram URLs."""
        field = InstagramURLField(required=True)
        
        with pytest.raises(forms.ValidationError):
            field.clean("https://facebook.com/post/123")
            
        with pytest.raises(forms.ValidationError):
            field.clean("not a url")


class TestHashtagField:
    """Test HashtagField."""
    
    def test_hashtag_cleaning(self):
        """Test hashtag cleaning."""
        field = HashtagField()
        
        # Test # removal
        assert field.to_python("#hashtag") == "hashtag"
        assert field.to_python("hashtag") == "hashtag"
        
    def test_hashtag_validation(self):
        """Test hashtag validation."""
        field = HashtagField(required=True)
        
        # Valid hashtag
        field.clean("valid_hashtag")
        field.clean("hashtag123")
        
        # Invalid hashtag
        with pytest.raises(forms.ValidationError):
            field.clean("hash tag")  # Space not allowed


class TestBulkUsernamesField:
    """Test BulkUsernamesField."""
    
    def test_parse_usernames_list(self):
        """Test parsing usernames list."""
        field = BulkUsernamesField(platform="instagram")
        
        input_text = """username1
        @username2
        username3"""
        
        result = field.to_python(input_text)
        assert result == ["username1", "username2", "username3"]
        
    def test_empty_input(self):
        """Test empty input returns empty list."""
        field = BulkUsernamesField(platform="instagram", required=False)
        
        assert field.to_python("") == []
        assert field.to_python(None) == []
        
    def test_validation_with_invalid_username(self):
        """Test validation with invalid username."""
        field = BulkUsernamesField(platform="instagram")
        
        input_text = """username1
        user name
        username3"""
        
        # to_python should still parse it
        result = field.to_python(input_text)
        assert len(result) == 3
        
        # But clean should raise validation error
        with pytest.raises(forms.ValidationError):
            field.clean(input_text)


class TestBulkURLsField:
    """Test BulkURLsField."""
    
    def test_parse_urls_list(self):
        """Test parsing URLs list."""
        field = BulkURLsField()
        
        input_text = """https://instagram.com/p/ABC123/
        https://instagram.com/reel/DEF456/"""
        
        result = field.to_python(input_text)
        assert len(result) == 2
        
    def test_empty_input(self):
        """Test empty input returns empty list."""
        field = BulkURLsField(required=False)
        
        assert field.to_python("") == []
        assert field.to_python(None) == []


class TestPostTypesField:
    """Test PostTypesField."""
    
    def test_default_choices(self):
        """Test default post type choices."""
        field = PostTypesField()
        
        assert len(field.choices) == 4
        assert field.initial == ["photo", "video", "carousel", "reel"]
        
    def test_validation(self):
        """Test post types validation."""
        field = PostTypesField()
        
        # Valid types
        field.clean(["photo", "video"])
        
        # Invalid type
        with pytest.raises(forms.ValidationError, match="Invalid post types"):
            field.clean(["photo", "invalid_type"])
            
    def test_empty_selection_allowed(self):
        """Test empty selection is allowed by default."""
        field = PostTypesField(required=False)
        
        # Should not raise
        result = field.clean([])
        assert result == []