"""
Tests for GCS checkbox in MediaDownloadOptionsMixin.
"""

from django.test import TestCase, override_settings
from django import forms
from django.conf import settings

from core.forms.mixins import MediaDownloadOptionsMixin


class GCSTestForm(MediaDownloadOptionsMixin, forms.Form):
    """Test form with mixin."""
    pass


class TestMediaDownloadOptionsMixinGCS(TestCase):
    """Test GCS checkbox in forms."""
    
    @override_settings(GCS_BUCKET_NAME='test-bucket')
    def test_gcs_checkbox_visible_when_configured(self):
        """Test checkbox is visible when GCS is configured."""
        form = GCSTestForm()
        assert 'save_media_to_gcs' in form.fields
        assert form.fields['save_media_to_gcs'].widget.input_type != 'hidden'
        assert form.fields['save_media_to_gcs'].label == 'Save media to Google Cloud Storage'
        assert form.fields['save_media_to_gcs'].initial is False
        assert form.fields['save_media_to_gcs'].required is False
    
    @override_settings(GCS_BUCKET_NAME='')
    def test_gcs_checkbox_hidden_when_not_configured(self):
        """Test checkbox is hidden when GCS not configured."""
        form = GCSTestForm()
        assert 'save_media_to_gcs' in form.fields
        assert form.fields['save_media_to_gcs'].widget.input_type == 'hidden'
        assert form.fields['save_media_to_gcs'].initial is False
    
    @override_settings(GCS_BUCKET_NAME=None)
    def test_gcs_checkbox_hidden_when_bucket_none(self):
        """Test checkbox is hidden when GCS_BUCKET_NAME is None."""
        form = GCSTestForm()
        assert 'save_media_to_gcs' in form.fields
        assert form.fields['save_media_to_gcs'].widget.input_type == 'hidden'
    
    def test_gcs_requires_download_media(self):
        """Test that GCS requires download_media to be True."""
        form = GCSTestForm(data={
            'download_media': False,
            'save_media_to_gcs': True
        })
        assert form.is_valid()
        # GCS should be disabled when download_media is False
        assert form.cleaned_data['save_media_to_gcs'] is False
    
    @override_settings(GCS_BUCKET_NAME='test-bucket')
    def test_gcs_with_download_media_enabled(self):
        """Test that GCS works when download_media is True."""
        form = GCSTestForm(data={
            'download_media': True,
            'save_media_to_gcs': True
        })
        assert form.is_valid()
        assert form.cleaned_data['download_media'] is True
        assert form.cleaned_data['save_media_to_gcs'] is True
    
    @override_settings(GCS_BUCKET_NAME='test-bucket')
    def test_gcs_checkbox_help_text(self):
        """Test that GCS checkbox has appropriate help text."""
        form = GCSTestForm()
        field = form.fields['save_media_to_gcs']
        assert 'Store media files in GCS' in field.help_text
        assert 'requires GCS configuration' in field.help_text
    
    def test_form_without_settings(self):
        """Test form behavior when GCS_BUCKET_NAME is not in settings."""
        # Remove GCS_BUCKET_NAME from settings if it exists
        with self.settings():
            # Delete the setting to simulate it not existing
            if hasattr(settings, 'GCS_BUCKET_NAME'):
                delattr(settings, 'GCS_BUCKET_NAME')
            
            form = GCSTestForm()
            assert 'save_media_to_gcs' in form.fields
            assert form.fields['save_media_to_gcs'].widget.input_type == 'hidden'
            assert form.fields['save_media_to_gcs'].initial is False
    
    @override_settings(GCS_BUCKET_NAME='test-bucket')
    def test_gcs_field_css_classes(self):
        """Test that GCS field has appropriate CSS classes."""
        form = GCSTestForm()
        field = form.fields['save_media_to_gcs']
        widget_attrs = field.widget.attrs
        
        # Check for CSS classes and data attributes
        assert 'form-check-input' in widget_attrs.get('class', '')
        assert 'data-depends-on' in widget_attrs
        assert widget_attrs['data-depends-on'] == 'id_download_media'
        assert widget_attrs.get('data-depends-on-value') == 'true'
    
    @override_settings(GCS_BUCKET_NAME='test-bucket')
    def test_multiple_forms_independent(self):
        """Test that multiple form instances don't interfere."""
        form1 = GCSTestForm(data={
            'download_media': True,
            'save_media_to_gcs': True
        })
        
        form2 = GCSTestForm(data={
            'download_media': True,
            'save_media_to_gcs': False
        })
        
        assert form1.is_valid()
        assert form2.is_valid()
        
        assert form1.cleaned_data['save_media_to_gcs'] is True
        assert form2.cleaned_data['save_media_to_gcs'] is False
    
    @override_settings(GCS_BUCKET_NAME='production-bucket')
    def test_gcs_with_real_bucket_name(self):
        """Test form with a real-looking bucket name."""
        form = GCSTestForm()
        assert 'save_media_to_gcs' in form.fields
        assert form.fields['save_media_to_gcs'].widget.input_type != 'hidden'
        
        # Submit form with GCS enabled
        form = GCSTestForm(data={
            'download_media': True,
            'save_media_to_gcs': True
        })
        assert form.is_valid()
        assert form.cleaned_data['save_media_to_gcs'] is True