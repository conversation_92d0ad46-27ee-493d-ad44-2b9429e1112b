"""Tests for core form validators."""

import pytest
from django.core.exceptions import ValidationError

from core.forms.validators import (
    InstagramUsernameValidator,
    hashtag_validator,
    validate_instagram_username,
)


class TestInstagramUsernameValidator:
    """Test Instagram username validation."""
    
    def test_valid_usernames(self):
        """Test valid Instagram usernames."""
        validator = InstagramUsernameValidator()
        
        # Should not raise
        validator("john_doe")
        validator("user123")
        validator("test.user")
        
    def test_username_with_at_symbol(self):
        """Test username with @ symbol is cleaned."""
        validator = InstagramUsernameValidator()
        # Should not raise - @ will be cleaned
        validator("@username")
        
    def test_empty_username(self):
        """Test empty username."""
        validator = InstagramUsernameValidator()
        with pytest.raises(ValidationError, match="Username cannot be empty"):
            validator("")
            
    def test_invalid_username(self):
        """Test invalid Instagram username."""
        validator = InstagramUsernameValidator()
        
        with pytest.raises(ValidationError):
            validator("user-name")  # Hyphen not allowed
            
        with pytest.raises(ValidationError):
            validator("user name")  # Space not allowed
            
        with pytest.raises(ValidationError):
            validator("a" * 31)  # Too long


class TestHashtagValidator:
    """Test hashtag validation."""
    
    def test_valid_hashtags(self):
        """Test valid hashtags."""
        assert hashtag_validator("hashtag") == "hashtag"
        assert hashtag_validator("#hashtag") == "hashtag"
        assert hashtag_validator("hash_tag") == "hash_tag"
        assert hashtag_validator("hashtag123") == "hashtag123"
        
    def test_empty_hashtag(self):
        """Test empty hashtag."""
        assert hashtag_validator("") == ""
            
    def test_invalid_hashtags(self):
        """Test invalid hashtags."""
        # Empty hashtag after stripping #
        with pytest.raises(ValidationError, match="Hashtag cannot be empty"):
            hashtag_validator("#")
            
        with pytest.raises(ValidationError, match="only contain"):
            hashtag_validator("hash tag")  # Space not allowed
            
        with pytest.raises(ValidationError, match="only contain"):
            hashtag_validator("hash@tag")  # @ not allowed


class TestBackwardsCompatibility:
    """Test backwards compatibility aliases."""
    
    def test_validate_instagram_username(self):
        """Test validate_instagram_username alias."""
        # Should work the same as InstagramUsernameValidator
        validate_instagram_username("valid_username")
        
        with pytest.raises(ValidationError):
            validate_instagram_username("invalid-username")