"""Tests for Celery task integration."""

from unittest.mock import Mock, patch
from django.test import TestCase

from core.tasks.base import BaseTask, TaskRepository
from core.tasks.exceptions import TaskExecutionError, TaskValidationError, TaskRetryError
from core.models import TaskResult


class DummyTask(BaseTask):
    """Dummy task for testing."""
    
    task_type = "dummy_task"
    description = "Test dummy task"
    max_retries = 2
    
    def validate_params(self, **kwargs):
        """Validate parameters."""
        if "required_param" not in kwargs:
            raise ValueError("required_param is required")
        
        if kwargs.get("should_fail_validation"):
            raise ValueError("Validation failed as requested")
            
        return kwargs
        
    def execute_task(self, **kwargs):
        """Execute task."""
        if kwargs.get("should_fail"):
            raise TaskExecutionError("dummy_task", {"error": "Failed as requested"})
            
        result = {
            "status": "success",
            "processed": kwargs.get("required_param"),
            "items_count": kwargs.get("items_count", 1)
        }
        
        # Simulate progress updates
        if kwargs.get("with_progress"):
            self.update_progress(50, "Half way there")
            self.update_progress(100, "Completed")
            
        return result


class TestBaseTask(TestCase):
    """Test BaseTask functionality."""
    
    def setUp(self):
        """Set up test data."""
        self.task = DummyTask()
        
    def test_task_initialization(self):
        """Test task initialization."""
        # Without task_id
        task1 = DummyTask()
        self.assertIsNotNone(task1.task_id)
        self.assertEqual(task1.task_type, "dummy_task")
        self.assertEqual(task1.max_retries, 2)
        
        # With task_id
        task2 = DummyTask(task_id="custom-id")
        self.assertEqual(task2.task_id, "custom-id")
        
    def test_validate_params_success(self):
        """Test successful parameter validation."""
        params = {"required_param": "test_value", "optional": "data"}
        cleaned = self.task.validate_params(**params)
        self.assertEqual(cleaned, params)
        
    def test_validate_params_missing_required(self):
        """Test validation with missing required parameter."""
        with self.assertRaises(ValueError) as cm:
            self.task.validate_params(optional="data")
        self.assertIn("required_param is required", str(cm.exception))
        
    def test_validate_params_custom_failure(self):
        """Test validation with custom failure."""
        with self.assertRaises(ValueError) as cm:
            self.task.validate_params(
                required_param="test",
                should_fail_validation=True
            )
        self.assertIn("Validation failed as requested", str(cm.exception))
        
    def test_execute_success(self):
        """Test successful task execution."""
        params = {"required_param": "test_value", "items_count": 5}
        result = self.task.execute(**params)
        
        self.assertEqual(result["status"], "success")
        self.assertEqual(result["processed"], "test_value")
        self.assertEqual(result["items_count"], 5)
        
        # Check task result was created
        task_result = TaskResult.objects.get(task_id=self.task.task_id)
        self.assertEqual(task_result.status, "completed")
        self.assertEqual(task_result.result["status"], "success")
        
    def test_execute_with_progress(self):
        """Test task execution with progress updates."""
        params = {"required_param": "test", "with_progress": True}
        result = self.task.execute(**params)
        
        self.assertEqual(result["status"], "success")
        
        # Check progress was updated
        task_result = TaskResult.objects.get(task_id=self.task.task_id)
        self.assertEqual(task_result.processed_items, 100)
        self.assertEqual(task_result.progress_message, "Completed")
        
    def test_execute_validation_error(self):
        """Test task execution with validation error."""
        with self.assertRaises(TaskValidationError):
            self.task.execute(optional="data")  # Missing required_param
            
        # Check task result was marked as failed
        task_result = TaskResult.objects.get(task_id=self.task.task_id)
        self.assertEqual(task_result.status, "failed")
        self.assertIn("required_param is required", task_result.error_message)
        
    def test_execute_task_error(self):
        """Test task execution with execution error."""
        params = {"required_param": "test", "should_fail": True}
        
        # First attempt should raise TaskRetryError
        with self.assertRaises(TaskRetryError) as cm:
            self.task.execute(**params)
            
        self.assertIn("exceeded retry limit", str(cm.exception))
        
        # Check task result was marked as retry
        task_result = TaskResult.objects.get(task_id=self.task.task_id)
        self.assertEqual(task_result.status, "retry")
        self.assertEqual(task_result.retry_count, 1)
        self.assertIn("Failed as requested", task_result.error_message)
        
    @patch("core.tasks.celery_integration.execute_base_task.delay")
    def test_run_async(self, mock_delay):
        """Test async task execution."""
        # Mock Celery result
        mock_celery_result = Mock()
        mock_celery_result.id = "celery-task-123"
        mock_celery_result.status = "PENDING"
        mock_delay.return_value = mock_celery_result
        
        # Run task async
        params = {"required_param": "test", "items_count": 10}
        self.task.run_async(**params)
        
        # Check Celery task was called
        # Use actual module path that DummyTask will have
        task_path = f"{DummyTask.__module__}.{DummyTask.__name__}"
        mock_delay.assert_called_once_with(
            task_path,
            task_id=self.task.task_id,
            **params
        )
        
        # Check task result was created
        task_result = TaskResult.objects.get(task_id=self.task.task_id)
        self.assertEqual(task_result.status, "pending")
        self.assertEqual(task_result.celery_task_id, "celery-task-123")
        self.assertEqual(task_result.parameters, params)
        

class TestTaskRepository(TestCase):
    """Test TaskRepository functionality."""
    
    def setUp(self):
        """Set up test data."""
        self.repository = TaskRepository()
        
        # Create test tasks
        self.pending_task1 = TaskResult.objects.create(
            task_id="pending-1",
            task_type="test",
            status="pending",
            priority=1
        )
        self.pending_task2 = TaskResult.objects.create(
            task_id="pending-2",
            task_type="test",
            status="pending",
            priority=2
        )
        self.running_task = TaskResult.objects.create(
            task_id="running-1",
            task_type="test",
            status="running"
        )
        self.completed_task = TaskResult.objects.create(
            task_id="completed-1",
            task_type="test",
            status="completed"
        )
        
    def test_get_pending_tasks(self):
        """Test getting pending tasks."""
        pending = self.repository.get_pending_tasks(limit=10)
        
        self.assertEqual(len(pending), 2)
        # Higher priority first
        self.assertEqual(pending[0].task_id, "pending-2")
        self.assertEqual(pending[1].task_id, "pending-1")
        
    def test_get_pending_tasks_with_limit(self):
        """Test getting pending tasks with limit."""
        pending = self.repository.get_pending_tasks(limit=1)
        
        self.assertEqual(len(pending), 1)
        self.assertEqual(pending[0].task_id, "pending-2")
        
    def test_get_running_tasks(self):
        """Test getting running tasks."""
        running = self.repository.get_running_tasks()
        
        self.assertEqual(len(running), 1)
        self.assertEqual(running[0].task_id, "running-1")
        
    def test_get_tasks_by_channel(self):
        """Test getting tasks by channel."""
        # Create task with channel
        TaskResult.objects.create(
            task_id="channel-1",
            task_type="test",
            status="running",
            channel_name="test-channel"
        )
        
        tasks = self.repository.get_tasks_by_channel("test-channel")
        
        self.assertEqual(len(tasks), 1)
        self.assertEqual(tasks[0].task_id, "channel-1")
        

class TestCeleryIntegration(TestCase):
    """Test Celery integration."""
    
    def test_execute_base_task(self):
        """Test execute_base_task Celery task."""
        # Test the actual task execution
        task = DummyTask(task_id="task-123")
        result = task.execute(required_param="test_value")
        
        # Verify result
        self.assertEqual(result["status"], "success")
        self.assertEqual(result["processed"], "test_value")
        
        # Check that task was created with correct ID
        task_result = TaskResult.objects.get(task_id="task-123")
        self.assertEqual(task_result.status, "completed")
        
    def test_execute_base_task_without_task_id(self):
        """Test execute_base_task without explicit task_id."""
        # Test the actual task execution with auto-generated ID
        task = DummyTask()  # No task_id provided
        result = task.execute(required_param="test_value")
        
        # Verify result
        self.assertEqual(result["status"], "success")
        self.assertEqual(result["processed"], "test_value")
        
        # Check that task was created
        task_result = TaskResult.objects.get(task_id=task.task_id)
        self.assertEqual(task_result.status, "completed")
        # Check that a UUID was generated
        self.assertIsNotNone(task.task_id)
        self.assertTrue(len(task.task_id) > 10)  # UUIDs are longer