"""
Tests for ContextLogger migration tools.

Tests the migration script, validation script, and compatibility layer.
"""

import ast
from pathlib import Path
import warnings


# Import the modules we're testing
import sys
scripts_dir = Path(__file__).parent.parent.parent / "scripts"
sys.path.insert(0, str(scripts_dir))

# Import directly without "scripts." prefix since we added scripts to path
from migrate_to_context_logger import LoggerMigrationTransformer, process_file  # noqa: E402
from validate_migration import MigrationValidator, validate_file  # noqa: E402
from core.logging.compat import (  # noqa: E402
    enable_compatibility_mode,
    disable_compatibility_mode,
    is_compatibility_enabled,
    compatibility_context,
    with_compatibility,
    CompatibilityWarning,
    check_logger_type,
)
from core.logging import ContextLogger  # noqa: E402


class TestLoggerMigrationTransformer:
    """Test the AST transformer for migration."""
    
    def test_simple_import_transformation(self):
        """Test basic import and getLogger transformation."""
        source = """
import logging

logger = logging.getLogger(__name__)
"""
        tree = ast.parse(source)
        transformer = LoggerMigrationTransformer()
        new_tree = transformer.visit(tree)
        new_tree = transformer.transform_imports(new_tree)
        
        # Check that transformation was detected
        assert transformer.modified
        assert transformer.has_logging_import
        assert len(transformer.getLogger_calls) == 1
    
    def test_multiple_getlogger_calls(self):
        """Test transformation of multiple getLogger calls."""
        source = """
import logging

logger1 = logging.getLogger(__name__)
logger2 = logging.getLogger("custom.logger")
logger3 = logging.getLogger()
"""
        tree = ast.parse(source)
        transformer = LoggerMigrationTransformer()
        transformer.visit(tree)
        
        assert transformer.modified
        assert len(transformer.getLogger_calls) == 3
    
    def test_aliased_import(self):
        """Test transformation with aliased import."""
        source = """
import logging as log

logger = log.getLogger(__name__)
"""
        tree = ast.parse(source)
        transformer = LoggerMigrationTransformer()
        transformer.visit(tree)
        
        assert transformer.modified
        assert transformer.logging_import_alias == "log"
    
    def test_existing_get_logger_import(self):
        """Test that existing get_logger import is preserved."""
        source = """
from core.logging import get_logger

logger = get_logger(__name__)
"""
        tree = ast.parse(source)
        transformer = LoggerMigrationTransformer()
        transformer.visit(tree)
        
        assert not transformer.modified
        assert transformer.has_get_logger_import
    
    def test_mixed_logging_usage(self):
        """Test that logging import is kept if used for other purposes."""
        source = """
import logging

logger = logging.getLogger(__name__)
logging.basicConfig(level=logging.INFO)
"""
        tree = ast.parse(source)
        transformer = LoggerMigrationTransformer()
        new_tree = transformer.visit(tree)
        new_tree = transformer.transform_imports(new_tree)
        
        assert transformer.modified
        # Should not remove logging import since it's used for basicConfig
        assert not transformer.should_remove_logging_import(new_tree)


class TestProcessFile:
    """Test the file processing function."""
    
    def test_process_file_success(self, tmp_path):
        """Test successful file processing."""
        # Create a test file
        test_file = tmp_path / "test_module.py"
        test_file.write_text("""
import logging

logger = logging.getLogger(__name__)

def test_function():
    logger.info("Test message")
""")
        
        # Process the file
        result = process_file(test_file, dry_run=False, verbose=False)
        
        assert result is True
        
        # Check the file was modified
        content = test_file.read_text()
        assert "from core.logging import get_logger" in content
        assert "logger = get_logger(__name__)" in content
        assert "logging.getLogger" not in content
    
    def test_process_file_dry_run(self, tmp_path, monkeypatch):
        """Test dry run mode."""
        test_file = tmp_path / "test_module.py"
        original_content = """
import logging

logger = logging.getLogger(__name__)
"""
        test_file.write_text(original_content)
        
        # Capture print calls
        printed_lines = []
        def mock_print(*args, **kwargs):
            printed_lines.append(' '.join(str(arg) for arg in args))
        
        # Patch the print function in the module
        monkeypatch.setattr('builtins.print', mock_print)
        
        # Process in dry run mode
        result = process_file(test_file, dry_run=True, verbose=True)
        
        assert result is True
        
        # Check file was not modified
        assert test_file.read_text() == original_content
        
        # Check output
        output = '\n'.join(printed_lines)
        assert "Would modify:" in output
        assert "Found 1 logging.getLogger() calls" in output
    
    def test_process_file_already_migrated(self, tmp_path):
        """Test processing already migrated file."""
        test_file = tmp_path / "test_module.py"
        test_file.write_text("""
from core.logging import get_logger

logger = get_logger(__name__)
""")
        
        result = process_file(test_file, dry_run=False, verbose=True)
        
        assert result is False  # No modifications needed
    
    def test_process_file_syntax_error(self, tmp_path, monkeypatch):
        """Test handling of syntax errors."""
        test_file = tmp_path / "test_module.py"
        test_file.write_text("""
import logging
logger = logging.getLogger(__name__)
def broken(:  # Syntax error
    pass
""")
        
        # Capture print calls
        printed_lines = []
        def mock_print(*args, **kwargs):
            printed_lines.append(' '.join(str(arg) for arg in args))
        
        monkeypatch.setattr('builtins.print', mock_print)
        
        result = process_file(test_file, dry_run=False, verbose=False)
        
        assert result is False
        
        output = '\n'.join(printed_lines)
        assert "Syntax error" in output


class TestMigrationValidator:
    """Test the migration validator."""
    
    def test_validate_old_logging(self):
        """Test detection of old logging usage."""
        source = """
import logging

logger = logging.getLogger(__name__)
"""
        tree = ast.parse(source)
        validator = MigrationValidator()
        validator.visit(tree)
        
        assert validator.has_logging_getLogger
        assert len(validator.getLogger_calls) == 1
        assert not validator.has_get_logger_import
    
    def test_validate_new_logging(self):
        """Test validation of migrated code."""
        source = """
from core.logging import get_logger

logger = get_logger(__name__)
"""
        tree = ast.parse(source)
        validator = MigrationValidator()
        validator.visit(tree)
        
        assert not validator.has_logging_getLogger
        assert validator.has_get_logger_import
        assert validator.uses_logger
    
    def test_validate_missing_import(self):
        """Test detection of missing import."""
        source = """
# Missing import
logger = get_logger(__name__)
"""
        tree = ast.parse(source)
        validator = MigrationValidator()
        validator.visit(tree)
        
        assert not validator.has_get_logger_import
        assert validator.uses_logger


class TestValidateFile:
    """Test the file validation function."""
    
    def test_validate_file_valid(self, tmp_path):
        """Test validation of properly migrated file."""
        test_file = tmp_path / "test_module.py"
        test_file.write_text("""
from core.logging import get_logger

logger = get_logger(__name__)

def test_function():
    logger.info("Test")
""")
        
        is_valid, issues = validate_file(test_file)
        
        assert is_valid is True
        assert len(issues) == 0
    
    def test_validate_file_old_logging(self, tmp_path):
        """Test validation detects old logging usage."""
        test_file = tmp_path / "test_module.py"
        test_file.write_text("""
import logging

logger = logging.getLogger(__name__)
""")
        
        is_valid, issues = validate_file(test_file)
        
        assert is_valid is False
        # The validator returns 2 issues: old_logging and missing_import
        assert len(issues) == 2
        
        # Check that we have both expected issues
        issue_types = [issue[0] for issue in issues]
        assert 'old_logging' in issue_types
        assert 'missing_import' in issue_types
    
    def test_validate_file_missing_import(self, tmp_path):
        """Test validation detects missing import."""
        test_file = tmp_path / "test_module.py"
        test_file.write_text("""
# Using logger without proper import
logger = get_logger(__name__)
""")
        
        is_valid, issues = validate_file(test_file)
        
        assert is_valid is False
        assert any(issue[0] == 'missing_import' for issue in issues)
    
    def test_validate_allowed_file(self, tmp_path):
        """Test validation of allowed files."""
        # Create a file that matches allowed pattern
        test_file = tmp_path / "core" / "logging" / "logger.py"
        test_file.parent.mkdir(parents=True)
        test_file.write_text("""
import logging

# This file is allowed to use logging.getLogger
logger = logging.getLogger(__name__)
""")
        
        is_valid, issues = validate_file(test_file)
        
        assert is_valid is True  # Should be valid since it's an allowed file


class TestCompatibilityLayer:
    """Test the compatibility layer."""
    
    def test_enable_disable_compatibility(self):
        """Test enabling and disabling compatibility mode."""
        import logging
        
        # Store original
        original_getLogger = logging.getLogger
        
        # Enable compatibility
        enable_compatibility_mode(show_warnings=False)
        assert is_compatibility_enabled()
        assert logging.getLogger != original_getLogger
        
        # Disable compatibility
        disable_compatibility_mode()
        assert not is_compatibility_enabled()
        assert logging.getLogger == original_getLogger
    
    def test_compatibility_returns_context_logger(self):
        """Test that compatibility mode returns ContextLogger."""
        import logging
        
        enable_compatibility_mode(show_warnings=False)
        try:
            logger = logging.getLogger("test.logger")
            assert isinstance(logger, ContextLogger)
            assert check_logger_type(logger) == "context"
        finally:
            disable_compatibility_mode()
    
    def test_compatibility_warnings(self):
        """Test deprecation warnings in compatibility mode."""
        import logging
        
        enable_compatibility_mode(show_warnings=True)
        try:
            with warnings.catch_warnings(record=True) as w:
                warnings.simplefilter("always")
                logging.getLogger("test.logger")
                
                assert len(w) == 1
                assert issubclass(w[0].category, CompatibilityWarning)
                assert "deprecated" in str(w[0].message)
        finally:
            disable_compatibility_mode()
    
    def test_compatibility_context_manager(self):
        """Test compatibility context manager."""
        import logging
        
        assert not is_compatibility_enabled()
        
        with compatibility_context(show_warnings=False):
            assert is_compatibility_enabled()
            logger = logging.getLogger("test")
            assert isinstance(logger, ContextLogger)
        
        assert not is_compatibility_enabled()
    
    def test_compatibility_decorator(self):
        """Test compatibility decorator."""
        import logging
        
        @with_compatibility(show_warnings=False)
        def test_function():
            logger = logging.getLogger("test")
            return check_logger_type(logger)
        
        assert not is_compatibility_enabled()
        result = test_function()
        assert result == "context"
        assert not is_compatibility_enabled()
    
    def test_nested_compatibility_context(self):
        """Test nested compatibility contexts."""
        import logging
        
        # Start with compatibility enabled
        enable_compatibility_mode(show_warnings=True)
        
        try:
            with compatibility_context(show_warnings=False):
                # Should suppress warnings
                with warnings.catch_warnings(record=True) as w:
                    warnings.simplefilter("always")
                    logging.getLogger("test")
                    assert len(w) == 0  # No warnings
            
            # Should restore warning state
            with warnings.catch_warnings(record=True) as w:
                warnings.simplefilter("always")
                logging.getLogger("test2")
                assert len(w) == 1  # Warning restored
        finally:
            disable_compatibility_mode()


class TestEndToEnd:
    """End-to-end tests for the complete migration process."""
    
    def test_full_migration_workflow(self, tmp_path):
        """Test complete migration workflow."""
        # Create a test project structure
        src_dir = tmp_path / "src"
        src_dir.mkdir()
        
        # Create test files
        (src_dir / "module1.py").write_text("""
import logging

logger = logging.getLogger(__name__)

def function1():
    logger.info("Module 1 function")
""")
        
        (src_dir / "module2.py").write_text("""
import logging

class MyClass:
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    def method(self):
        self.logger.debug("Debug message")
""")
        
        # Already migrated file
        (src_dir / "module3.py").write_text("""
from core.logging import get_logger

logger = get_logger(__name__)

def function3():
    logger.warning("Already migrated")
""")
        
        # Run migration (would call the script in real scenario)
        for file_path in src_dir.glob("*.py"):
            process_file(file_path, dry_run=False, verbose=False)
        
        # Validate all files
        validation_results = []
        for file_path in src_dir.glob("*.py"):
            is_valid, issues = validate_file(file_path)
            validation_results.append((file_path, is_valid, issues))
        
        # Check results
        for file_path, is_valid, issues in validation_results:
            assert is_valid, f"Validation failed for {file_path}: {issues}"
        
        # Verify content
        module1_content = (src_dir / "module1.py").read_text()
        assert "from core.logging import get_logger" in module1_content
        assert "logger = get_logger(__name__)" in module1_content
        
        module2_content = (src_dir / "module2.py").read_text()
        assert "from core.logging import get_logger" in module2_content
        assert "self.logger = get_logger(__name__)" in module2_content