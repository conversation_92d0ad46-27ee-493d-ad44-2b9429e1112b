import pytest
from unittest.mock import Mock
from django.test import TestCase
from django.core.exceptions import ObjectDoesNotExist

from core.services.base import BaseService, MediaService


class MockService(BaseService):
    """Тестовая реализация BaseService для тестирования"""
    
    @property
    def entity_name(self) -> str:
        return "TestEntity"


class TestBaseService(TestCase):
    """Тесты для базового сервиса"""
    
    def setUp(self):
        self.mock_repository = Mock()
        self.service = MockService(repository=self.mock_repository)
    
    def test_get_by_id_success(self):
        """Тест успешного получения по ID"""
        # Arrange
        expected_entity = Mock(id=1, name="Test")
        self.mock_repository.get_by_id.return_value = expected_entity
        
        # Act
        result = self.service.get_by_id(1)
        
        # Assert
        assert result == expected_entity
        self.mock_repository.get_by_id.assert_called_once_with(1)
    
    def test_get_by_id_not_found(self):
        """Тест когда объект не найден"""
        # Arrange
        self.mock_repository.get_by_id.side_effect = ObjectDoesNotExist()
        
        # Act & Assert
        with pytest.raises(ObjectDoesNotExist):
            self.service.get_by_id(999)
    
    def test_create_with_logging(self):
        """Тест создания с логированием"""
        # Arrange
        entity_data = {"name": "New Entity"}
        created_entity = Mock(pk=1, **entity_data)
        self.mock_repository.create.return_value = created_entity
        
        # Мокаем логгер сервиса
        self.service.logger = Mock()
        
        # Act
        result = self.service.create(**entity_data)
        
        # Assert
        assert result == created_entity
        self.mock_repository.create.assert_called_once_with(**entity_data)
        # Проверяем, что логирование вызвано
        self.service.logger.info.assert_called()
        log_message = self.service.logger.info.call_args[0][0]
        assert "Created TestEntity with id 1" in log_message
    
    def test_update_success(self):
        """Тест успешного обновления"""
        # Arrange
        entity_id = 1
        update_data = {"name": "Updated Entity"}
        existing_entity = Mock(pk=entity_id, name="Old Entity")
        updated_entity = Mock(pk=entity_id, **update_data)
        
        self.mock_repository.get_by_id.return_value = existing_entity
        self.mock_repository.update.return_value = updated_entity
        
        # Act
        result = self.service.update(entity_id, **update_data)
        
        # Assert
        assert result == updated_entity
        self.mock_repository.get_by_id.assert_called_once_with(entity_id)
        self.mock_repository.update.assert_called_once_with(entity_id, **update_data)
    
    def test_delete_success(self):
        """Тест успешного удаления"""
        # Arrange
        entity_id = 1
        entity = Mock(pk=entity_id, name="Test Entity")
        self.mock_repository.get_by_id.return_value = entity
        self.mock_repository.delete.return_value = True
        
        # Act
        result = self.service.delete(entity_id)
        
        # Assert
        assert result
        self.mock_repository.get_by_id.assert_called_once_with(entity_id)
        self.mock_repository.delete.assert_called_once_with(entity_id)
    
    def test_bulk_create_success(self):
        """Тест массового создания"""
        # Arrange
        items = [
            {"name": "Entity 1"},
            {"name": "Entity 2"},
            {"name": "Entity 3"}
        ]
        
        # Мокаем модель
        mock_model = Mock()
        self.mock_repository.model = mock_model
        
        # Создаем моки объектов
        created_objects = [Mock(pk=i+1, **item) for i, item in enumerate(items)]
        self.mock_repository.bulk_create.return_value = created_objects
        
        # Act
        result = self.service.bulk_create(items)
        
        # Assert
        assert len(result) == 3
        self.mock_repository.bulk_create.assert_called_once()
        # Проверяем, что для каждого элемента был создан объект модели
        assert mock_model.call_count == 3
    
    def test_search_success(self):
        """Тест поиска"""
        # Arrange
        query = "test"
        search_fields = ["name", "description"]
        expected_results = [Mock(id=1), Mock(id=2)]
        self.mock_repository.search.return_value = expected_results
        
        # Act
        result = self.service.search(query, search_fields)
        
        # Assert
        assert result == expected_results
        self.mock_repository.search.assert_called_once_with(query, search_fields)
    
    def test_get_list_with_pagination(self):
        """Тест получения списка с пагинацией"""
        # Arrange
        filters = {"status": "active"}
        page = 2
        per_page = 10
        
        mock_queryset = Mock()
        self.mock_repository.filter.return_value = mock_queryset
        
        expected_result = {
            "items": [Mock(id=11), Mock(id=12)],
            "total": 25,
            "page": page,
            "per_page": per_page,
            "pages": 3
        }
        self.mock_repository.paginate.return_value = expected_result
        
        # Act
        result = self.service.get_list(filters=filters, page=page, per_page=per_page)
        
        # Assert
        assert result == expected_result
        self.mock_repository.filter.assert_called_once_with(**filters)
        self.mock_repository.paginate.assert_called_once_with(mock_queryset, page, per_page)
    
    def test_validate_create_data_called(self):
        """Тест вызова валидации при создании"""
        # Arrange
        entity_data = {"name": "Test"}
        self.service.validate_create_data = Mock()
        self.mock_repository.create.return_value = Mock(pk=1)
        
        # Act
        self.service.create(**entity_data)
        
        # Assert
        self.service.validate_create_data.assert_called_once_with(entity_data)
    
    def test_after_create_hook_called(self):
        """Тест вызова хука после создания"""
        # Arrange
        entity_data = {"name": "Test"}
        created_entity = Mock(pk=1)
        self.mock_repository.create.return_value = created_entity
        self.service.after_create = Mock()
        
        # Act
        self.service.create(**entity_data)
        
        # Assert
        self.service.after_create.assert_called_once_with(created_entity)


class TestMediaService(TestCase):
    """Тесты для сервиса работы с медиа"""
    
    def setUp(self):
        self.service = MediaService(storage_path='test/media')
    
    def test_process_media_basic(self):
        """Тест базовой обработки медиа"""
        # Arrange
        media_url = "https://example.com/image.jpg"
        media_type = "photo"
        
        # Act
        result = self.service.process_media(media_url, media_type)
        
        # Assert
        assert result['media_url'] == media_url
        assert result['media_type'] == media_type
        assert not result['is_downloaded']
    
    async def test_download_media_not_implemented(self):
        """Тест что download_media не реализован в базовом классе"""
        # Act & Assert
        with pytest.raises(NotImplementedError):
            await self.service.download_media("http://example.com/img.jpg", "test.jpg")
    
    def test_get_media_metadata_not_implemented(self):
        """Тест что get_media_metadata не реализован в базовом классе"""
        # Act & Assert
        with pytest.raises(NotImplementedError):
            self.service.get_media_metadata("/path/to/file.jpg")


if __name__ == '__main__':
    pytest.main([__file__])