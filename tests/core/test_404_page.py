"""
Test custom 404 page functionality.
"""

import pytest
from django.test import Client, override_settings


@pytest.mark.django_db
class TestCustom404Page:
    """Test custom 404 error page."""
    
    @override_settings(DEBUG=False, ROOT_URLCONF='SocialManager.urls')
    def test_404_page_renders(self, client):
        """Test that custom 404 page renders correctly."""
        # Force reload of URL configuration to ensure handler404 is loaded
        from django.urls import clear_url_caches
        clear_url_caches()
        
        response = client.get('/non-existent-page/')
        
        # Should return 404 status
        assert response.status_code == 404
        
        # Should use our custom template
        content = response.content.decode()
        assert '404' in content
        assert 'Страница не найдена' in content
        assert 'Панель управления' in content
    
    def test_favicon_returns_204(self):
        """Test that favicon.ico returns 204 No Content."""
        client = Client()
        response = client.get('/favicon.ico')
        
        # Should return 204 No Content
        assert response.status_code == 204
        
        # Should have no content
        assert len(response.content) == 0