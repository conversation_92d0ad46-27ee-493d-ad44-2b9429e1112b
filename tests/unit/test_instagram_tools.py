"""
Unit tests for Instagram MCP tools
"""

import pytest
from unittest.mock import Mock, patch
from datetime import datetime

# Import the tool modules
import mcp_server.tools.instagram_profiles as profile_tools
import mcp_server.tools.instagram_posts as post_tools
import mcp_server.tools.instagram_media as media_tools

# Get the actual functions from the FunctionTool wrappers
instagram_get_profile = profile_tools.instagram_get_profile.fn
instagram_list_profiles = profile_tools.instagram_list_profiles.fn
instagram_search_profiles = profile_tools.instagram_search_profiles.fn
instagram_get_profile_stats = profile_tools.instagram_get_profile_stats.fn

instagram_get_posts = post_tools.instagram_get_posts.fn
instagram_get_post_details = post_tools.instagram_get_post_details.fn
instagram_get_trending_posts = post_tools.instagram_get_trending_posts.fn

instagram_get_post_media = media_tools.instagram_get_post_media.fn
instagram_download_media = media_tools.instagram_download_media.fn


class TestInstagramProfileTools:
    """Test Instagram profile tools"""
    
    @pytest.mark.asyncio
    async def test_get_profile_success(self, mock_brightdata, instagram_profile_data):
        """Test successful profile retrieval"""
        # Mock database
        with patch('mcp_server.tools.instagram_profiles.get_profile_by_username') as mock_get:
            mock_profile = Mock(**instagram_profile_data)
            mock_get.return_value = mock_profile
            
            # Test
            result = await instagram_get_profile("testprofile")
            
            # Assertions
            assert result["username"] == "testprofile"
            assert result["follower_count"] == 10000
            assert result["is_verified"] is True
            assert "statistics" in result
            mock_get.assert_called_once_with("testprofile")
    
    @pytest.mark.asyncio
    async def test_get_profile_not_found(self):
        """Test profile not found"""
        with patch('mcp_server.tools.instagram_profiles.get_profile_by_username') as mock_get:
            mock_get.return_value = None
            
            result = await instagram_get_profile("nonexistent")
            
            assert result["error_type"] == "not_found"
            assert "not found" in result["message"].lower()
    
    @pytest.mark.asyncio
    async def test_get_profile_username_normalization(self):
        """Test username normalization"""
        with patch('mcp_server.tools.instagram_profiles.get_profile_by_username') as mock_get:
            mock_get.return_value = Mock(username="testuser", id=1)
            
            # Test with @ prefix
            await instagram_get_profile("@TestUser")
            
            # Should normalize to lowercase without @
            mock_get.assert_called_with("testuser")
    
    @pytest.mark.asyncio
    async def test_list_profiles_pagination(self):
        """Test profile listing with pagination"""
        mock_profiles = [
            Mock(id=i, username=f"user{i}", follower_count=1000-i)
            for i in range(5)
        ]
        
        with patch('mcp_server.tools.instagram_profiles.filter_profiles') as mock_filter:
            mock_filter.return_value = (mock_profiles, 10)
            
            result = await instagram_list_profiles(limit=5, offset=0)
            
            assert result["total"] == 10
            assert len(result["profiles"]) == 5
            assert result["has_more"] is True
            assert result["offset"] == 0
            assert result["limit"] == 5
    
    @pytest.mark.asyncio
    async def test_list_profiles_filters(self):
        """Test profile listing with filters"""
        with patch('mcp_server.tools.instagram_profiles.filter_profiles') as mock_filter:
            mock_filter.return_value = ([], 0)
            
            await instagram_list_profiles(
                is_verified=True,
                is_business=True,
                min_followers=1000,
                max_followers=10000
            )
            
            # Check filters passed correctly
            call_args = mock_filter.call_args[1]
            assert call_args["is_verified"] is True
            assert call_args["is_business"] is True
            assert call_args["follower_count__gte"] == 1000
            assert call_args["follower_count__lte"] == 10000
    
    @pytest.mark.asyncio
    async def test_search_profiles_validation(self):
        """Test search query validation"""
        # Too short query
        result = await instagram_search_profiles("a", limit=10)
        
        assert result["error_type"] == "validation_error"
        assert "at least 2 characters" in result["message"]
    
    @pytest.mark.asyncio
    async def test_search_profiles_success(self):
        """Test successful profile search"""
        mock_profiles = [
            Mock(id=1, username="fashion_blog", follower_count=5000, full_name="Fashion Blog")
        ]
        
        with patch('mcp_server.tools.instagram_profiles.search_profiles') as mock_search:
            mock_search.return_value = mock_profiles
            
            result = await instagram_search_profiles("fashion")
            
            assert result["query"] == "fashion"
            assert result["count"] == 1
            assert result["profiles"][0]["username"] == "fashion_blog"
    
    @pytest.mark.asyncio
    async def test_get_profile_statistics_success(self):
        """Test profile statistics retrieval"""
        mock_profile = Mock(
            id=1,
            username="testuser",
            follower_count=10000,
            following_count=500,
            post_count=50
        )
        mock_stats = {
            'total_posts': 50,
            'total_likes': 5000,
            'total_comments': 500,
            'avg_likes': 100.0,
            'avg_comments': 10.0
        }
        
        with patch('mcp_server.tools.instagram_profiles.get_profile_by_username') as mock_get_profile:
            with patch('mcp_server.tools.instagram_profiles.get_profile_statistics') as mock_get_stats:
                with patch('mcp_server.tools.instagram_profiles._calculate_growth_metrics') as mock_growth:
                    mock_get_profile.return_value = mock_profile
                    mock_get_stats.return_value = mock_stats
                    mock_growth.return_value = {
                        "new_posts": 0,
                        "likes_gained": 0,
                        "comments_gained": 0
                    }
                    
                    result = await instagram_get_profile_stats(username="testuser")
                    
                    assert result["profile"]["username"] == "testuser"
                    assert result["statistics"]["total_posts"] == 50
                    assert result["statistics"]["average_likes"] == 100.0
    
    @pytest.mark.asyncio
    async def test_get_profile_statistics_no_profile(self):
        """Test statistics for non-existent profile"""
        with patch('mcp_server.tools.instagram_profiles.get_profile_by_username') as mock_get_profile:
            mock_get_profile.return_value = None
            
            result = await instagram_get_profile_stats(username="unknown")
            
            assert result["error_type"] == "not_found"
            assert "not found" in result["message"]


class TestInstagramPostTools:
    """Test Instagram post tools"""
    
    @pytest.mark.asyncio
    async def test_get_posts_success(self):
        """Test successful posts retrieval"""
        mock_posts = [
            Mock(
                id=1,
                external_id="post1",
                content="Test post 1",
                post_type="photo",
                like_count=100,
                comment_count=10,
                share_count=5,
                view_count=500,
                posted_at=datetime.now(),
                post_url="https://instagram.com/p/test1",
                media=Mock(count=Mock(return_value=2)),
                hashtags=Mock(all=Mock(return_value=[Mock(name="test")]))
            )
        ]
        
        with patch('mcp_server.tools.instagram_posts.get_posts_by_username') as mock_get:
            mock_get.return_value = (mock_posts, 1)
            
            result = await instagram_get_posts(username="testuser", limit=10)
            
            assert result["username"] == "testuser"
            assert result["total"] == 1
            assert len(result["posts"]) == 1
            assert result["posts"][0]["external_id"] == "post1"
    
    
    @pytest.mark.asyncio
    async def test_get_post_details_success(self):
        """Test getting post details"""
        mock_post = Mock(
            id=1,
            external_id="post123",
            content="Test post",
            post_type="photo",
            post_url="https://instagram.com/p/test123",
            like_count=100,
            comment_count=10,
            share_count=5,
            view_count=500,
            posted_at=datetime.now(),
            location="Test Location",
            is_sponsored=False,
            created_at=datetime.now(),
            updated_at=datetime.now(),
            last_scraped_at=None,
            profile=Mock(
                id=1,
                username="testuser", 
                full_name="Test User",
                profile_pic_url="https://example.com/pic.jpg",
                follower_count=1000
            ),
            media=Mock(all=Mock(return_value=[])),
            hashtags=Mock(all=Mock(return_value=[]))
        )
        
        with patch('mcp_server.tools.instagram_posts.get_post_by_id') as mock_get:
            mock_get.return_value = mock_post
            
            result = await instagram_get_post_details(post_id=1)
            
            # Debug: Check if error occurred
            if "error_type" in result:
                print(f"Error result: {result}")
                assert False, f"Got error response: {result}"
            
            assert result["id"] == 1
            assert result["external_id"] == "post123"
            assert "profile" in result
            assert result["profile"]["username"] == "testuser"
    
    @pytest.mark.asyncio
    async def test_get_post_details_not_found(self):
        """Test post not found"""
        with patch('mcp_server.tools.instagram_posts.get_post_by_id') as mock_get:
            mock_get.return_value = None
            
            result = await instagram_get_post_details(post_id=999)
            
            assert result["error_type"] == "not_found"
            assert "not found" in result["message"]
    
    @pytest.mark.asyncio
    async def test_list_recent_posts_success(self):
        """Test listing trending posts"""
        mock_posts = [
            Mock(
                id=1,
                external_id="post1",
                content="Recent post",
                post_type="photo",
                post_url="https://instagram.com/p/test1",
                like_count=200,
                comment_count=20,
                posted_at=datetime.now(),
                profile=Mock(username="user1", follower_count=1000),
                media=Mock(count=Mock(return_value=1)),
                hashtags=Mock(all=Mock(return_value=[Mock(name="trending")])),
                engagement_rate=5.0
            )
        ]
        
        with patch('mcp_server.tools.instagram_posts.get_trending_posts_db') as mock_get:
            mock_get.return_value = mock_posts
            
            result = await instagram_get_trending_posts(limit=20, time_period="week")
            
            assert result["count"] == 1
            assert result["posts"][0]["id"] == 1
            assert result["time_period"] == "week"
            assert result["filters"]["min_followers"] == 1000


class TestInstagramMediaTools:
    """Test Instagram media tools"""
    
    @pytest.mark.asyncio
    async def test_get_media_success(self):
        """Test successful media retrieval"""
        mock_media = [
            Mock(
                id=1,
                media_type="photo",
                media_url="https://example.com/image.jpg",
                thumbnail_url="https://example.com/thumb.jpg",
                width=1080,
                height=1080,
                duration=None,
                position=0,
                is_downloaded=False,
                local_file=None,
                created_at=datetime.now()
            )
        ]
        
        with patch('mcp_server.tools.instagram_media.get_media_for_posts') as mock_get:
            mock_get.return_value = {1: mock_media}
            
            result = await instagram_get_post_media(post_id=1)
            
            assert result["post_id"] == 1
            assert result["media_count"] == 1
            assert result["media"][0]["media_type"] == "photo"
            assert result["media"][0]["media_url"] == "https://example.com/image.jpg"
    
    @pytest.mark.asyncio
    async def test_get_media_empty(self):
        """Test post with no media"""
        with patch('mcp_server.tools.instagram_media.get_media_for_posts') as mock_get:
            mock_get.return_value = {}
            
            result = await instagram_get_post_media(post_id=1)
            
            assert result["error_type"] == "not_found"
            assert "No media found" in result["message"]
    
    @pytest.mark.asyncio
    async def test_download_media_success(self):
        """Test successful media download"""
        mock_results = [
            {"media_id": 1, "status": "downloaded", "local_path": "/media/image1.jpg", "gcs_url": None},
            {"media_id": 2, "status": "downloaded", "local_path": "/media/image2.jpg", "gcs_url": None},
            {"media_id": 3, "status": "already_downloaded", "local_path": "/media/video1.mp4", "gcs_url": None}
        ]
        
        with patch('mcp_server.tools.instagram_media.download_media_files') as mock_download:
            mock_download.return_value = mock_results
            
            result = await instagram_download_media(post_id=1)
            
            assert result["post_id"] == 1
            assert result["summary"]["total"] == 3
            assert result["summary"]["downloaded"] == 2
            assert result["summary"]["already_downloaded"] == 1
            assert result["summary"]["failed"] == 0
            assert len(result["media"]) == 3
            mock_download.assert_called_once_with(1, None, False)
    
    @pytest.mark.asyncio
    async def test_download_media_with_gcs(self):
        """Test media download with GCS flag"""
        mock_results = [
            {"media_id": 1, "status": "downloaded", "local_path": None, "gcs_url": "gs://bucket/image1.jpg"}
        ]
        
        with patch('mcp_server.tools.instagram_media.download_media_files') as mock_download:
            mock_download.return_value = mock_results
            
            result = await instagram_download_media(post_id=1, save_to_gcs=True)
            
            assert result["save_to_gcs"] is True
            assert result["summary"]["downloaded"] == 1
            mock_download.assert_called_once_with(1, None, True)
    
    @pytest.mark.asyncio
    async def test_download_media_failure(self):
        """Test media download failure"""
        mock_results = [
            {"media_id": 1, "status": "failed", "error": "Network error"},
            {"media_id": 2, "status": "failed", "error": "File too large"}
        ]
        
        with patch('mcp_server.tools.instagram_media.download_media_files') as mock_download:
            mock_download.return_value = mock_results
            
            result = await instagram_download_media(post_id=1)
            
            assert result["summary"]["failed"] == 2
            assert result["summary"]["downloaded"] == 0
            assert result["media"][0]["status"] == "failed"
            assert result["media"][1]["error"] == "File too large"


class TestErrorHandling:
    """Test error handling in tools"""
    
    @pytest.mark.asyncio
    async def test_database_error_handling(self):
        """Test handling of database errors"""
        with patch('mcp_server.tools.instagram_profiles.get_profile_by_username') as mock_get:
            mock_get.side_effect = Exception("Database connection error")
            
            result = await instagram_get_profile("testuser")
            
            assert "error_type" in result
            assert result["error_type"] == "internal_error"
            assert "Database connection error" in result["message"]
    
    @pytest.mark.asyncio
    async def test_validation_error_handling(self):
        """Test validation error handling"""
        # Test invalid limit - expect PaginationParams validation to raise error
        # Since the pydantic validation raises an exception that gets caught as internal_error
        result = await instagram_list_profiles(limit=1001)
        
        assert result["error_type"] == "validation_error"
        assert "1001" in result["message"] or "parameters" in result["message"].lower()
        
        # Test invalid offset
        result = await instagram_list_profiles(offset=-1)
        
        assert result["error_type"] == "validation_error"
        assert "-1" in result["message"] or "parameters" in result["message"].lower()
    
    @pytest.mark.asyncio
    async def test_empty_username_handling(self):
        """Test empty username handling"""
        # Empty username is treated as a normal search that won't find anything
        with patch('mcp_server.tools.instagram_profiles.get_profile_by_username') as mock_get:
            mock_get.return_value = None
            
            result = await instagram_get_profile("")
            
            assert result["error_type"] == "not_found"
            assert "not found" in result["message"]
    
    @pytest.mark.asyncio
    async def test_invalid_profile_id_handling(self):
        """Test invalid profile ID handling"""
        # instagram_get_posts takes username, not profile_id
        # Testing with non-existent username
        with patch('mcp_server.tools.instagram_posts.get_posts_by_username') as mock_get:
            mock_get.return_value = ([], 0)
            
            result = await instagram_get_posts(username="nonexistent")
            
            assert result["error_type"] == "not_found"
            assert "No posts found" in result["message"]


class TestDataSerialization:
    """Test data serialization in tool responses"""
    
    @pytest.mark.asyncio
    async def test_datetime_serialization(self):
        """Test datetime fields are properly serialized"""
        mock_post = Mock(
            id=1,
            external_id="post1",
            post_type="photo",
            post_url="https://instagram.com/p/test1",
            content="Test post",
            like_count=100,
            comment_count=10,
            share_count=5,
            view_count=500,
            posted_at=datetime(2024, 1, 1, 12, 0, 0),
            created_at=datetime(2024, 1, 1, 10, 0, 0),
            updated_at=datetime(2024, 1, 1, 11, 0, 0),
            last_scraped_at=None,
            location=None,
            is_sponsored=False,
            profile=Mock(
                id=1,
                username="testuser",
                full_name="Test User",
                profile_pic_url="https://example.com/pic.jpg",
                follower_count=1000
            ),
            media=Mock(all=Mock(return_value=[])),
            hashtags=Mock(all=Mock(return_value=[]))
        )
        
        with patch('mcp_server.tools.instagram_posts.get_post_by_id') as mock_get:
            mock_get.return_value = mock_post
            
            result = await instagram_get_post_details(post_id=1)
            
            # Check datetime fields are strings
            assert isinstance(result["posted_at"], str)
            assert isinstance(result["created_at"], str)
            assert isinstance(result["updated_at"], str)
    
    @pytest.mark.asyncio
    async def test_none_value_handling(self):
        """Test None values are handled properly"""
        mock_profile = Mock(
            id=1,
            username="testuser",
            full_name="Test User",
            bio=None,
            external_url=None,
            profile_pic_url=None,
            external_id="12345",
            follower_count=100,
            following_count=50,
            post_count=10,
            is_verified=False,
            is_private=False,
            is_business=False,
            created_at=datetime.now(),
            updated_at=datetime.now(),
            last_scraped_at=None
        )
        
        with patch('mcp_server.tools.instagram_profiles.get_profile_by_username') as mock_get:
            with patch('mcp_server.tools.instagram_profiles.get_profile_statistics') as mock_stats:
                mock_get.return_value = mock_profile
                mock_stats.return_value = {
                    'total_posts': 0,
                    'total_likes': 0,
                    'total_comments': 0,
                    'avg_likes': 0,
                    'avg_comments': 0
                }
                
                result = await instagram_get_profile("testuser")
                
                # None values should be included as null
                assert result["bio"] is None
                assert result["external_url"] is None
                assert result["profile_pic_url"] is None
    
    @pytest.mark.asyncio
    async def test_boolean_serialization(self):
        """Test boolean fields are properly serialized"""
        mock_profile = Mock(
            id=1,
            username="testuser",
            is_verified=True,
            is_private=False,
            is_business=True
        )
        
        with patch('mcp_server.tools.instagram_profiles.get_profile_by_username') as mock_get:
            mock_get.return_value = mock_profile
            
            result = await instagram_get_profile("testuser")
            
            # Check boolean values
            assert result["is_verified"] is True
            assert result["is_private"] is False
            assert result["is_business"] is True