"""
Тесты для CommentService.
"""

import pytest
from unittest.mock import patch
from django.utils import timezone
from django.conf import settings

from instagram_manager.services.comment_service import CommentService
from instagram_manager.models import InstagramPost, InstagramProfile, InstagramComment
from instagram_manager.schemas.brightdata import InstagramCommentResponse


@pytest.fixture
def profile():
    """Создание тестового профиля."""
    return InstagramProfile.objects.create(
        profile_id="test_profile_123",
        username=settings.INSTAGRAM_TEST_USERNAME,
        full_name="Test User",
        follower_count=1000,
        following_count=500,
        post_count=50,
    )


@pytest.fixture
def post(profile):
    """Создание тестового поста."""
    return InstagramPost.objects.create(
        external_id="test_post_123",
        shortcode="DKZ7A0XpB1U",
        profile=profile,
        post_type="photo",
        post_url="https://instagram.com/p/DKZ7A0XpB1U/",
        caption="Test post caption",
        like_count=100,
        comment_count=10,
        posted_at=timezone.now(),
    )


@pytest.fixture
def comment_service():
    """Создание экземпляра CommentService."""
    return CommentService()


@pytest.fixture
def mock_brightdata_client():
    """Мок для BrightData клиента."""
    with patch("instagram_manager.services.comment_service.BrightDataClient") as mock:
        yield mock


@pytest.fixture
def sample_comment_responses():
    """Примеры ответов комментариев от API."""
    return [
        InstagramCommentResponse(
            comment_id="comment_1",
            comment="Great post!",
            comment_user="user1",
            comment_date=timezone.now(),
            likes_number=5,
            is_verified_user=False,
            has_replies=True,
            replies_number=2,
            is_pinned=False,
            is_hidden=False,
            profile_pic_url="https://example.com/pic1.jpg",
        ),
        InstagramCommentResponse(
            comment_id="comment_2",
            comment="Amazing!",
            comment_user="user2",
            comment_date=timezone.now(),
            likes_number=10,
            is_verified_user=True,
            has_replies=False,
            replies_number=0,
            is_pinned=True,
            is_hidden=False,
            profile_pic_url="https://example.com/pic2.jpg",
        ),
        InstagramCommentResponse(
            comment_id="comment_3",
            comment="Reply to comment 1",
            comment_user="user3",
            comment_date=timezone.now(),
            likes_number=2,
            is_verified_user=False,
            has_replies=False,
            replies_number=0,
            reply_to_comment_id="comment_1",
            is_pinned=False,
            is_hidden=False,
            profile_pic_url=None,
        ),
    ]


@pytest.mark.django_db
class TestCommentService:
    """Тесты для CommentService."""

    def setup_method(self):
        """Очистка комментариев перед каждым тестом."""
        # Clear any existing comments
        InstagramComment.objects.all().delete()

    def test_import_comments_for_post_success(
        self, comment_service, post, mock_brightdata_client, sample_comment_responses
    ):
        """Тест успешного импорта комментариев."""
        # Настраиваем мок
        mock_client_instance = mock_brightdata_client.return_value
        mock_client_instance.get_post_comments.return_value = sample_comment_responses
        comment_service.client = mock_client_instance

        # Импортируем комментарии
        result = comment_service.import_comments_for_post(post)

        # Проверяем результат
        assert len(result) == 3
        assert InstagramComment.objects.filter(post=post).count() == 3

        # Проверяем вызов API
        mock_client_instance.get_post_comments.assert_called_once_with(
            post_url=post.post_url, skip_validation_errors=True
        )

        # Проверяем иерархию
        parent_comment = InstagramComment.objects.get(external_id="comment_1")
        reply_comment = InstagramComment.objects.get(external_id="comment_3")
        assert reply_comment.reply_to == parent_comment

        # Проверяем закрепленный комментарий
        pinned_comment = InstagramComment.objects.get(external_id="comment_2")
        assert pinned_comment.is_pinned is True
        assert pinned_comment.author_is_verified is True

    def test_import_comments_with_limit(
        self, comment_service, post, mock_brightdata_client, sample_comment_responses
    ):
        """Тест импорта с лимитом."""
        mock_client_instance = mock_brightdata_client.return_value
        mock_client_instance.get_post_comments.return_value = sample_comment_responses
        comment_service.client = mock_client_instance

        # Импортируем только 2 комментария
        result = comment_service.import_comments_for_post(post, limit=2)

        assert len(result) == 2
        assert InstagramComment.objects.filter(post=post).count() == 2

    def test_import_comments_without_replies(
        self, comment_service, post, mock_brightdata_client, sample_comment_responses
    ):
        """Тест импорта без ответов."""
        mock_client_instance = mock_brightdata_client.return_value
        mock_client_instance.get_post_comments.return_value = sample_comment_responses
        comment_service.client = mock_client_instance

        # Импортируем без ответов
        result = comment_service.import_comments_for_post(post, include_replies=False)

        # Должны импортироваться только комментарии верхнего уровня
        assert len(result) == 2
        assert (
            InstagramComment.objects.filter(post=post, reply_to__isnull=True).count()
            == 2
        )
        assert (
            InstagramComment.objects.filter(post=post, reply_to__isnull=False).count()
            == 0
        )

    def test_import_comments_with_min_likes(
        self, comment_service, post, mock_brightdata_client, sample_comment_responses
    ):
        """Тест импорта с минимальным количеством лайков."""
        mock_client_instance = mock_brightdata_client.return_value
        mock_client_instance.get_post_comments.return_value = sample_comment_responses
        comment_service.client = mock_client_instance

        # Импортируем только комментарии с 5+ лайками
        result = comment_service.import_comments_for_post(post, min_likes=5)

        assert len(result) == 2
        for comment in InstagramComment.objects.filter(post=post):
            assert comment.like_count >= 5

    def test_import_comments_force_update(
        self, comment_service, post, mock_brightdata_client, sample_comment_responses
    ):
        """Тест обновления существующих комментариев."""
        # Создаем существующий комментарий
        existing = InstagramComment.objects.create(
            external_id="comment_1",
            post=post,
            author_username="olduser",
            author_external_id="user_1",
            text="Old text",
            like_count=0,
            commented_at=timezone.now(),
        )

        mock_client_instance = mock_brightdata_client.return_value
        mock_client_instance.get_post_comments.return_value = sample_comment_responses[
            :1
        ]
        comment_service.client = mock_client_instance

        # Импортируем с force_update
        comment_service.import_comments_for_post(post, force_update=True)

        # Проверяем обновление
        existing.refresh_from_db()
        assert existing.author_username == "user1"
        assert existing.text == "Great post!"
        assert existing.like_count == 5

    def test_import_comments_batch(
        self, comment_service, profile, mock_brightdata_client, sample_comment_responses
    ):
        """Тест массового импорта комментариев."""
        # Создаем несколько постов
        posts = []
        posts.append(
            InstagramPost.objects.create(
                post_id="post_1",
                shortcode="CNjqoQbsNd9",
                profile=profile,
                post_type="photo",
                post_url="https://instagram.com/p/CNjqoQbsNd9/",
                posted_at=timezone.now(),
            )
        )
        posts.append(
            InstagramPost.objects.create(
                post_id="post_2",
                shortcode="CNjqTXUsCPG",
                profile=profile,
                post_type="photo",
                post_url="https://instagram.com/p/CNjqTXUsCPG/",
                posted_at=timezone.now(),
            )
        )
        posts.append(
            InstagramPost.objects.create(
                post_id="post_3",
                shortcode="CLryFnNhILn",
                profile=profile,
                post_type="photo",
                post_url="https://instagram.com/p/CLryFnNhILn/",
                posted_at=timezone.now(),
            )
        )

        mock_client_instance = mock_brightdata_client.return_value
        mock_client_instance.get_post_comments.return_value = sample_comment_responses[
            :2
        ]
        comment_service.client = mock_client_instance

        # Массовый импорт
        result = comment_service.import_comments_batch(posts, max_comments_per_post=50)

        assert len(result) == 3
        for post in posts:
            assert post.post_id in result
            assert len(result[post.post_id]) == 2

    def test_build_comment_tree(self, comment_service, post):
        """Тест построения дерева комментариев."""
        # Создаем иерархию комментариев
        parent1 = InstagramComment.objects.create(
            comment_id="parent1",
            post=post,
            author_username="user1",
            author_external_id="uid1",
            text="Parent comment 1",
            like_count=10,
            commented_at=timezone.now(),
        )

        InstagramComment.objects.create(
            comment_id="parent2",
            post=post,
            author_username="user2",
            author_external_id="uid2",
            text="Parent comment 2",
            like_count=5,
            commented_at=timezone.now(),
        )

        InstagramComment.objects.create(
            comment_id="reply1",
            post=post,
            reply_to=parent1,
            author_username="user3",
            author_external_id="uid3",
            text="Reply to parent 1",
            like_count=2,
            commented_at=timezone.now(),
        )

        InstagramComment.objects.create(
            comment_id="reply2",
            post=post,
            reply_to=parent1,
            author_username="user4",
            author_external_id="uid4",
            text="Another reply to parent 1",
            like_count=1,
            commented_at=timezone.now(),
        )

        # Строим дерево
        tree = comment_service.build_comment_tree(post)

        assert len(tree) == 2  # Два родительских комментария

        # Проверяем первый родительский комментарий
        parent1_tree = next(t for t in tree if t["comment"].comment_id == "parent1")
        assert len(parent1_tree["replies"]) == 2

        # Проверяем второй родительский комментарий
        parent2_tree = next(t for t in tree if t["comment"].comment_id == "parent2")
        assert len(parent2_tree["replies"]) == 0

    def test_get_comment_analytics(self, comment_service, post):
        """Тест получения аналитики комментариев."""
        # Создаем комментарии
        for i in range(5):
            InstagramComment.objects.create(
                comment_id=f"comment_{i}",
                post=post,
                author_username=f"user{i % 2}",  # 2 разных автора
                author_external_id=f"uid{i}",
                text=f"Comment {i}" + ("?" if i % 2 == 0 else ""),
                like_count=i * 5,
                commented_at=timezone.now(),
                is_pinned=(i == 0),
            )

        # Добавляем ответ
        parent = InstagramComment.objects.first()
        InstagramComment.objects.create(
            comment_id="reply_1",
            post=post,
            reply_to=parent,
            author_username="replyuser",
            author_external_id="ruid",
            text="Reply",
            like_count=0,
            commented_at=timezone.now(),
        )

        # Получаем аналитику
        analytics = comment_service.get_comment_analytics(post)

        assert analytics["total_comments"] == 6
        assert analytics["top_level_comments"] == 5
        assert analytics["total_replies"] == 1
        assert analytics["pinned_comments"] == 1
        assert "engagement_rate" in analytics
        assert "popular_comments" in analytics
        assert len(analytics["top_commenters"]) > 0

    def test_update_comments(self, comment_service, post, mock_brightdata_client):
        """Тест обновления существующих комментариев."""
        # Создаем существующие комментарии
        existing1 = InstagramComment.objects.create(
            comment_id="comment_1",
            post=post,
            author_username="user1",
            author_external_id="uid1",
            text="Old text",
            like_count=5,
            commented_at=timezone.now(),
        )

        existing2 = InstagramComment.objects.create(
            comment_id="comment_2",
            post=post,
            author_username="user2",
            author_external_id="uid2",
            text="Text unchanged",
            like_count=10,
            is_hidden=False,
            commented_at=timezone.now(),
        )

        # Настраиваем мок с обновленными данными
        updated_responses = [
            InstagramCommentResponse(
                comment_id="comment_1",
                comment="Updated text",
                comment_user="user1",
                comment_date=timezone.now(),
                likes_number=15,  # Увеличилось
                is_verified_user=False,
                has_replies=False,
                replies_number=0,
                is_pinned=False,
                is_hidden=False,
            ),
            InstagramCommentResponse(
                comment_id="comment_2",
                comment="Text unchanged",
                comment_user="user2",
                comment_date=timezone.now(),
                likes_number=10,  # Не изменилось
                is_verified_user=False,
                has_replies=False,
                replies_number=0,
                is_pinned=False,
                is_hidden=True,  # Стал скрытым
            ),
            InstagramCommentResponse(
                comment_id="comment_3",
                comment="New comment",
                comment_user="user3",
                comment_date=timezone.now(),
                likes_number=0,
                is_verified_user=False,
                has_replies=False,
                replies_number=0,
                is_pinned=False,
                is_hidden=False,
            ),
        ]

        mock_client_instance = mock_brightdata_client.return_value
        mock_client_instance.get_post_comments.return_value = updated_responses
        comment_service.client = mock_client_instance

        # Обновляем комментарии
        result = comment_service.update_comments(post)

        assert result["updated"] == 2
        assert result["new"] == 1
        assert result["total"] == 3

        # Проверяем обновления
        existing1.refresh_from_db()
        assert existing1.text == "Updated text"
        assert existing1.like_count == 15

        existing2.refresh_from_db()
        assert existing2.is_hidden is True

        # Проверяем новый комментарий
        new_comment = InstagramComment.objects.get(external_id="comment_3")
        assert new_comment.text == "New comment"

    def test_delete_hidden_comments(self, comment_service, post):
        """Тест удаления скрытых комментариев."""
        # Создаем комментарии
        InstagramComment.objects.create(
            comment_id="visible",
            post=post,
            author_username="user1",
            author_external_id="uid1",
            text="Visible comment",
            like_count=5,
            is_hidden=False,
            commented_at=timezone.now(),
        )

        InstagramComment.objects.create(
            comment_id="hidden1",
            post=post,
            author_username="user2",
            author_external_id="uid2",
            text="Hidden comment 1",
            like_count=0,
            is_hidden=True,
            commented_at=timezone.now(),
        )

        InstagramComment.objects.create(
            comment_id="hidden2",
            post=post,
            author_username="user3",
            author_external_id="uid3",
            text="Hidden comment 2",
            like_count=0,
            is_hidden=True,
            commented_at=timezone.now(),
        )

        # Удаляем скрытые
        deleted_count = comment_service.delete_hidden_comments(post)

        assert deleted_count == 2
        assert InstagramComment.objects.filter(post=post).count() == 1
        assert InstagramComment.objects.filter(post=post, is_hidden=False).exists()
        assert not InstagramComment.objects.filter(post=post, is_hidden=True).exists()

    def test_import_comments_api_error(
        self, comment_service, post, mock_brightdata_client
    ):
        """Тест обработки ошибок API."""
        mock_client_instance = mock_brightdata_client.return_value
        mock_client_instance.get_post_comments.side_effect = Exception("API Error")
        comment_service.client = mock_client_instance

        # Проверяем, что исключение пробрасывается
        with pytest.raises(Exception) as exc_info:
            comment_service.import_comments_for_post(post)

        assert "API Error" in str(exc_info.value)

    def test_import_comments_empty_response(
        self, comment_service, post, mock_brightdata_client
    ):
        """Тест импорта при пустом ответе API."""
        mock_client_instance = mock_brightdata_client.return_value
        mock_client_instance.get_post_comments.return_value = []
        comment_service.client = mock_client_instance

        result = comment_service.import_comments_for_post(post)

        assert result == []
        assert InstagramComment.objects.filter(post=post).count() == 0
