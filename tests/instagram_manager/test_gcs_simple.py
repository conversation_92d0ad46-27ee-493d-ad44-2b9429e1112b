"""
Simple integration tests for GCS functionality.

Focuses on testing the actual implementation without complex mocking.
"""

from unittest.mock import Mock, patch
from django.test import TestCase, override_settings
from django.utils import timezone

from instagram_manager.models import InstagramProfile, InstagramPost, InstagramMedia


class TestGCSSimpleIntegration(TestCase):
    """Test GCS integration with actual components."""
    
    def setUp(self):
        """Set up test data."""
        self.profile = InstagramProfile.objects.create(
            profile_id="test123",
            username="testuser",
            full_name="Test User"
        )
        
        self.post = InstagramPost.objects.create(
            external_id="POST123",
            profile=self.profile,
            shortcode="ABC123",
            caption="Test post",
            posted_at=timezone.now()
        )
    
    @override_settings(GCS_BUCKET_NAME='test-bucket')
    def test_gcs_url_field_functionality(self):
        """Test that GCS URL field works correctly."""
        # Create media without GCS URL
        media = InstagramMedia.objects.create(
            external_id="MEDIA123",
            post=self.post,
            media_type="photo",
            media_url="https://instagram.com/photo.jpg"
        )
        
        # Verify display URL returns external URL
        self.assertEqual(media.get_display_url(), "https://instagram.com/photo.jpg")
        
        # Set GCS URL
        media.gcs_url = "https://storage.googleapis.com/test-bucket/media/MEDIA123.jpg"
        media.save()
        
        # Verify GCS URL takes priority
        self.assertEqual(media.get_display_url(), media.gcs_url)
        
        # Verify it persists in database
        media.refresh_from_db()
        self.assertEqual(media.gcs_url, "https://storage.googleapis.com/test-bucket/media/MEDIA123.jpg")
    
    @override_settings(GCS_BUCKET_NAME='test-bucket')
    @patch('core.storage.gcs_service.storage.Client')
    def test_gcs_service_upload(self, mock_storage_client):
        """Test GCS service upload functionality."""
        # Mock GCS client
        mock_client = Mock()
        mock_bucket = Mock()
        mock_blob = Mock()
        
        mock_storage_client.return_value = mock_client
        mock_client.bucket.return_value = mock_bucket
        mock_bucket.blob.return_value = mock_blob
        mock_blob.public_url = "https://storage.googleapis.com/test-bucket/media/test.jpg"
        
        # Create GCS service
        from core.storage.gcs_service import GCSService
        service = GCSService()
        
        # Test upload
        url = service.upload_file(
            file_content=b'test data',
            original_filename='test.jpg',
            content_type='image/jpeg',
            folder='media'
        )
        
        # Verify upload was called
        mock_blob.upload_from_string.assert_called_once_with(
            b'test data',
            content_type='image/jpeg'
        )
        
        self.assertEqual(url, "https://storage.googleapis.com/test-bucket/media/test.jpg")
    
    @override_settings(GCS_BUCKET_NAME='test-bucket')
    @patch('instagram_manager.instagram_api.data_handlers.media_handler.GCSService')
    def test_media_handler_with_gcs(self, mock_gcs_service_class):
        """Test MediaHandler integration with GCS."""
        # Mock GCS service
        mock_gcs_instance = Mock()
        mock_gcs_service_class.return_value = mock_gcs_instance
        mock_gcs_instance.upload_file.return_value = "https://storage.googleapis.com/test-bucket/media/MEDIA456.jpg"
        
        # Create media
        media = InstagramMedia.objects.create(
            external_id="MEDIA456",
            post=self.post,
            media_type="photo",
            media_url="https://instagram.com/photo456.jpg"
        )
        
        # Create MediaHandler with GCS enabled
        from instagram_manager.instagram_api.data_handlers.media_handler import MediaHandler
        handler = MediaHandler(save_to_gcs=True)
        
        # Inject our mocked GCS service
        handler._gcs_service = mock_gcs_instance
        
        # Mock the download method to simulate successful download
        with patch('requests.get') as mock_requests:
            mock_response = Mock()
            mock_response.status_code = 200
            mock_response.content = b'fake image data'
            mock_response.headers = {'content-type': 'image/jpeg'}
            mock_requests.return_value = mock_response
            
            # Mock the file system operations
            with patch('builtins.open', create=True):
                with patch('os.makedirs'):
                    with patch('os.path.exists', return_value=False):
                        # Process media
                        result = handler.download_media(media)
                        
                        self.assertTrue(result)
                        
                        # Verify GCS upload was called
                        mock_gcs_instance.upload_file.assert_called_once()
                        
                        # Check media was updated
                        media.refresh_from_db()
                        self.assertEqual(media.gcs_url, "https://storage.googleapis.com/test-bucket/media/MEDIA456.jpg")
    
    @override_settings(GCS_BUCKET_NAME='')
    def test_gcs_disabled_behavior(self):
        """Test behavior when GCS is not configured."""
        # Create media
        media = InstagramMedia.objects.create(
            external_id="MEDIA789",
            post=self.post,
            media_type="photo",
            media_url="https://instagram.com/photo789.jpg",
            is_downloaded=True
        )
        media.local_path.name = "media/instagram/MEDIA789.jpg"
        
        # Without GCS URL, should return local path
        display_url = media.get_display_url()
        self.assertIn("media/instagram/MEDIA789.jpg", display_url)
        self.assertNotIn("storage.googleapis.com", display_url)
    
    @override_settings(GCS_BUCKET_NAME='test-bucket')
    def test_media_priority_chain(self):
        """Test the priority chain: GCS > Local > External."""
        media = InstagramMedia.objects.create(
            external_id="MEDIA999",
            post=self.post,
            media_type="photo",
            media_url="https://instagram.com/external.jpg"
        )
        
        # Step 1: Only external URL
        self.assertEqual(media.get_display_url(), "https://instagram.com/external.jpg")
        
        # Step 2: Add local file
        media.is_downloaded = True
        media.local_path.name = "media/instagram/local.jpg"
        media.save()
        self.assertIn("local.jpg", media.get_display_url())
        
        # Step 3: Add GCS URL (should override)
        media.gcs_url = "https://storage.googleapis.com/test-bucket/gcs.jpg"
        media.save()
        self.assertEqual(media.get_display_url(), media.gcs_url)
        
        # Step 4: Remove GCS URL (should fall back to local)
        media.gcs_url = None
        media.save()
        self.assertIn("local.jpg", media.get_display_url())


class TestGCSFormIntegration(TestCase):
    """Test GCS checkbox in forms."""
    
    @override_settings(GCS_BUCKET_NAME='test-bucket')
    def test_form_has_gcs_checkbox(self):
        """Test that forms have GCS checkbox when configured."""
        from instagram_manager.forms import ProfileImportForm
        
        form = ProfileImportForm()
        
        # Check field exists
        self.assertIn('save_media_to_gcs', form.fields)
        
        # Check it's visible
        self.assertNotEqual(form.fields['save_media_to_gcs'].widget.input_type, 'hidden')
        
        # Check it has proper attributes
        widget = form.fields['save_media_to_gcs'].widget
        self.assertIn('data-depends-on', widget.attrs)
        self.assertEqual(widget.attrs['data-depends-on'], 'id_download_media')
    
    @override_settings(GCS_BUCKET_NAME='')
    def test_form_hides_gcs_when_disabled(self):
        """Test that GCS checkbox is hidden when not configured."""
        from instagram_manager.forms import ProfileImportForm
        
        form = ProfileImportForm()
        
        # Check field exists but is hidden
        self.assertIn('save_media_to_gcs', form.fields)
        self.assertEqual(form.fields['save_media_to_gcs'].widget.input_type, 'hidden')
        self.assertFalse(form.fields['save_media_to_gcs'].initial)