"""
Tests for GCS URL field in InstagramMedia model.
"""
from django.test import TestCase

from instagram_manager.models import InstagramMedia, InstagramPost, InstagramProfile


class TestInstagramMediaGCSField(TestCase):
    """Test cases for GCS URL field functionality."""
    
    def setUp(self):
        """Set up test data."""
        self.profile = InstagramProfile.objects.create(
            username="testuser",
            profile_id="12345",
            external_id="test_external_123"
        )
        
        from django.utils import timezone
        
        self.post = InstagramPost.objects.create(
            profile=self.profile,
            shortcode="test123",
            post_type="photo",
            external_id="post_external_123",
            posted_at=timezone.now()
        )
        
        self.media = InstagramMedia.objects.create(
            post=self.post,
            media_type="photo",
            media_url="https://instagram.com/test.jpg",
            external_id="media_external_123"
        )
    
    def test_gcs_url_field_exists(self):
        """Test that gcs_url field exists on InstagramMedia model."""
        self.assertTrue(hasattr(self.media, 'gcs_url'))
        
    def test_gcs_url_field_is_nullable(self):
        """Test that gcs_url can be null."""
        self.assertIsNone(self.media.gcs_url)
        
    def test_gcs_url_field_max_length(self):
        """Test that gcs_url supports up to 1024 characters."""
        long_url = "https://storage.googleapis.com/socialmanager-media-prod/" + "x" * 950
        self.media.gcs_url = long_url
        self.media.save()
        self.assertEqual(self.media.gcs_url, long_url)
        
    def test_get_display_url_returns_gcs_url_first(self):
        """Test that get_display_url prioritizes GCS URL."""
        # Set all three possible URLs
        self.media.gcs_url = "https://storage.googleapis.com/test/file.jpg"
        self.media.is_downloaded = True
        self.media.local_path = "instagram/media/test.jpg"
        
        # GCS URL should be returned first
        self.assertEqual(self.media.get_display_url(), self.media.gcs_url)
        
    def test_get_display_url_fallback_to_local(self):
        """Test that get_display_url falls back to local path when no GCS URL."""
        # Create media with local path
        from django.core.files.base import ContentFile
        
        media = InstagramMedia.objects.create(
            post=self.post,
            media_type="photo",
            media_url="https://instagram.com/test2.jpg",
            external_id="media_test_local",
            is_downloaded=True,
            gcs_url=None
        )
        # Save a dummy file
        media.local_path.save('test_local.jpg', ContentFile(b'test content'), save=True)
        
        # Should return local URL when no GCS URL
        display_url = media.get_display_url()
        self.assertIn('/media/', display_url)
        self.assertIn('test_local', display_url)
            
    def test_get_display_url_fallback_to_external(self):
        """Test that get_display_url falls back to external URL when no GCS or local."""
        # No GCS URL or local file
        self.media.gcs_url = None
        self.media.is_downloaded = False
        self.media.local_path = None
        
        self.assertEqual(self.media.get_display_url(), self.media.media_url)
        
    def test_gcs_url_persists_after_save(self):
        """Test that GCS URL is persisted in database."""
        gcs_url = "https://storage.googleapis.com/socialmanager-media-prod/instagram/test123.jpg"
        self.media.gcs_url = gcs_url
        self.media.save()
        
        # Reload from database
        reloaded_media = InstagramMedia.objects.get(pk=self.media.pk)
        self.assertEqual(reloaded_media.gcs_url, gcs_url)
        
    def test_blank_gcs_url_is_valid(self):
        """Test that blank GCS URL is valid."""
        self.media.gcs_url = ""
        self.media.save()
        self.assertEqual(self.media.gcs_url, "")


class TestInstagramMediaGCSIntegration(TestCase):
    """Integration tests for GCS URL with other model methods."""
    
    def setUp(self):
        """Set up test data."""
        self.profile = InstagramProfile.objects.create(
            username="testuser2",
            profile_id="67890",
            external_id="test_external_456"
        )
        
        from django.utils import timezone
        
        self.post = InstagramPost.objects.create(
            profile=self.profile,
            shortcode="test456",
            post_type="video",
            external_id="post_external_456",
            posted_at=timezone.now()
        )
    
    def test_media_str_representation_unchanged(self):
        """Test that __str__ method works correctly with GCS URL."""
        media = InstagramMedia.objects.create(
            post=self.post,
            media_type="video",
            media_url="https://instagram.com/test.mp4",
            external_id="media_external_456",
            gcs_url="https://storage.googleapis.com/test/video.mp4"
        )
        # __str__ should not be affected by GCS URL
        str_repr = str(media)
        self.assertIsInstance(str_repr, str)
        
    def test_multiple_media_with_different_storage(self):
        """Test multiple media items with different storage locations."""
        # Media 1: Only external URL
        media1 = InstagramMedia.objects.create(
            post=self.post,
            media_type="photo",
            media_url="https://instagram.com/photo1.jpg",
            external_id="media1",
            order_index=0
        )
        
        # Media 2: Local file
        media2 = InstagramMedia.objects.create(
            post=self.post,
            media_type="photo",
            media_url="https://instagram.com/photo2.jpg",
            external_id="media2",
            is_downloaded=True,
            local_path="instagram/media/photo2.jpg",
            order_index=1
        )
        
        # Media 3: GCS
        media3 = InstagramMedia.objects.create(
            post=self.post,
            media_type="photo",
            media_url="https://instagram.com/photo3.jpg",
            external_id="media3",
            gcs_url="https://storage.googleapis.com/test/photo3.jpg",
            order_index=2
        )
        
        # Each should return appropriate URL
        self.assertEqual(media1.get_display_url(), media1.media_url)
        self.assertTrue(media2.get_display_url().startswith("/") or media2.get_display_url() == "instagram/media/photo2.jpg")
        self.assertEqual(media3.get_display_url(), media3.gcs_url)