"""
Тесты для admin интерфейса комментариев.
"""

import pytest
from unittest.mock import Mock, patch
from django.contrib.admin.sites import AdminSite
from django.contrib.auth import get_user_model
from django.test import RequestFactory
from django.conf import settings
from django.utils import timezone

from instagram_manager.admin import InstagramCommentAdmin, InstagramPostAdmin
from instagram_manager.models import InstagramProfile, InstagramPost, InstagramComment

User = get_user_model()


@pytest.fixture
def admin_user():
    """Создание администратора."""
    return User.objects.create_superuser(
        username="admin", password="admin123", email="<EMAIL>"
    )


@pytest.fixture
def request_factory():
    """Фабрика запросов."""
    return RequestFactory()


@pytest.fixture
def admin_site():
    """Экземпляр админ-сайта."""
    return AdminSite()


@pytest.fixture
def comment_admin(admin_site):
    """Экземпляр CommentAdmin."""
    return InstagramCommentAdmin(InstagramComment, admin_site)


@pytest.fixture
def post_admin(admin_site):
    """Экземпляр PostAdmin."""
    return InstagramPostAdmin(InstagramPost, admin_site)


@pytest.fixture
def profile():
    """Тестовый профиль."""
    return InstagramProfile.objects.create(
        profile_id="test_profile",
        username=settings.INSTAGRAM_TEST_USERNAME,
        follower_count=1000,
    )


@pytest.fixture
def post(profile):
    """Тестовый пост."""
    return InstagramPost.objects.create(
        external_id="test_post",
        shortcode="DKZ7A0XpB1U",
        profile=profile,
        post_type="photo",
        post_url="https://instagram.com/p/DKZ7A0XpB1U/",
        posted_at="2024-01-01",
    )


@pytest.fixture
def comments(post):
    """Тестовые комментарии."""
    comments = []

    # Обычные комментарии
    for i in range(3):
        comments.append(
            InstagramComment.objects.create(
                external_id=f"comment_{i}",
                post=post,
                author_username=f"user{i}",
                author_external_id=f"uid{i}",
                text=f"Comment text {i}",
                like_count=i * 5,
                is_hidden=(i == 2),  # Третий комментарий скрыт
                commented_at=timezone.now(),
            )
        )

    # Ответ на первый комментарий
    comments.append(
        InstagramComment.objects.create(
            external_id="reply_1",
            post=post,
            reply_to=comments[0],
            author_username="replyuser",
            author_external_id="ruid",
            text="Reply to first comment",
            like_count=2,
            commented_at="2024-01-01",
        )
    )

    return comments


@pytest.mark.django_db
class TestInstagramCommentAdmin:
    """Тесты для InstagramCommentAdmin."""

    def test_short_text_display(self, comment_admin, comments):
        """Тест отображения короткого текста."""
        # Короткий текст
        short_text = comment_admin.short_text(comments[0])
        assert short_text == "Comment text 0"

        # Длинный текст
        long_comment = InstagramComment(text="A" * 60)  # Больше 50 символов
        short_text = comment_admin.short_text(long_comment)
        assert short_text == "A" * 50 + "..."

    def test_post_link_display(self, comment_admin, comments):
        """Тест отображения ссылки на пост."""
        post_link = comment_admin.post_link(comments[0])

        assert f"@{settings.INSTAGRAM_TEST_USERNAME} - DKZ7A0XpB1U" in post_link
        assert "href=" in post_link
        assert "instagram_manager/instagrampost" in post_link

    def test_parent_comment_link_display(self, comment_admin, comments):
        """Тест отображения ссылки на родительский комментарий."""
        # Комментарий без родителя
        parent_link = comment_admin.parent_comment_link(comments[0])
        assert parent_link == "-"

        # Ответ на комментарий
        reply_link = comment_admin.parent_comment_link(comments[3])
        assert "Reply to @user0" in reply_link
        assert "href=" in reply_link

    def test_has_replies_display(self, comment_admin, comments):
        """Тест отображения информации об ответах."""
        # Комментарий с ответом
        has_replies = comment_admin.has_replies(comments[0])
        assert "1 replies" in has_replies
        assert "color: green" in has_replies

        # Комментарий без ответов
        no_replies = comment_admin.has_replies(comments[1])
        assert "No replies" in no_replies
        assert "color: gray" in no_replies

    def test_hide_comments_action(
        self, comment_admin, request_factory, admin_user, comments
    ):
        """Тест действия скрытия комментариев."""
        request = request_factory.post("/")
        request.user = admin_user
        request._messages = Mock()

        # Выбираем видимые комментарии
        queryset = InstagramComment.objects.filter(is_hidden=False)

        # Выполняем действие
        comment_admin.hide_comments(request, queryset)

        # Проверяем результат
        assert (
            InstagramComment.objects.filter(is_hidden=True).count() == len(comments)
        )
        request._messages.add.assert_called_once()

    def test_unhide_comments_action(
        self, comment_admin, request_factory, admin_user, comments
    ):
        """Тест действия показа комментариев."""
        request = request_factory.post("/")
        request.user = admin_user
        request._messages = Mock()

        # Выбираем скрытые комментарии
        queryset = InstagramComment.objects.filter(is_hidden=True)

        # Выполняем действие
        comment_admin.unhide_comments(request, queryset)

        # Проверяем результат
        assert InstagramComment.objects.filter(is_hidden=True).count() == 0
        request._messages.add.assert_called_once()

    def test_import_replies_action(
        self, comment_admin, request_factory, admin_user, comments
    ):
        """Тест действия импорта ответов."""
        request = request_factory.post("/")
        request.user = admin_user
        request._messages = Mock()

        # Выбираем комментарии без ответов
        queryset = InstagramComment.objects.filter(
            external_id__in=["comment_1", "comment_2"]
        )

        # Выполняем действие
        with patch("instagram_manager.services.comment_service.CommentService"):
            comment_admin.import_replies(request, queryset)

        # Проверяем, что показаны информационные сообщения
        assert request._messages.add.call_count >= 2

    def test_inline_queryset_filter(self, admin_site, post):
        """Тест фильтрации комментариев в inline."""
        from instagram_manager.admin import InstagramCommentInline

        # Создаем комментарии разных уровней
        parent = InstagramComment.objects.create(
            external_id="parent",
            post=post,
            author_username="parent_user",
            author_external_id="puid",
            text="Parent comment",
            like_count=5,
            commented_at="2024-01-01",
        )

        InstagramComment.objects.create(
            external_id="reply",
            post=post,
            reply_to=parent,
            author_username="reply_user",
            author_external_id="ruid",
            text="Reply comment",
            like_count=1,
            commented_at="2024-01-01",
        )

        # Создаем inline
        inline = InstagramCommentInline(InstagramPost, admin_site)
        request = Mock()

        # Получаем queryset
        queryset = inline.get_queryset(request)

        # Проверяем, что показаны только комментарии верхнего уровня
        comment_ids = list(queryset.values_list("external_id", flat=True))
        assert "parent" in comment_ids
        assert "reply" not in comment_ids


@pytest.mark.django_db
class TestInstagramPostAdminCommentActions:
    """Тесты для действий с комментариями в PostAdmin."""

    @patch("instagram_manager.services.CommentService")
    def test_import_comments_action_success(
        self, mock_comment_service, post_admin, request_factory, admin_user, post
    ):
        """Тест успешного импорта комментариев для постов."""
        request = request_factory.post("/")
        request.user = admin_user
        request._messages = Mock()

        # Настраиваем мок
        mock_service_instance = mock_comment_service.return_value
        mock_comments = [Mock() for _ in range(5)]
        mock_service_instance.import_comments_for_post.return_value = mock_comments

        # Выполняем действие
        queryset = InstagramPost.objects.filter(id=post.id)
        post_admin.import_comments_action(request, queryset)

        # Проверяем вызов сервиса
        mock_service_instance.import_comments_for_post.assert_called_once_with(
            post=post, limit=100, include_replies=True
        )

        # Проверяем сообщение об успехе
        # Django messages.success вызывает messages.add с level=SUCCESS
        from django.contrib.messages import SUCCESS
        success_calls = [
            call for call in request._messages.add.call_args_list
            if call[0][0] == SUCCESS
        ]
        assert len(success_calls) == 1
        assert "Successfully imported 5 comments" in success_calls[0][0][1]

    @patch("instagram_manager.services.CommentService")
    def test_import_comments_action_with_errors(
        self, mock_comment_service, post_admin, request_factory, admin_user, profile
    ):
        """Тест импорта комментариев с ошибками."""
        request = request_factory.post("/")
        request.user = admin_user
        request._messages = Mock()

        # Создаем несколько постов
        posts = []

        posts.append(
            InstagramPost.objects.create(
                external_id="post_1",
                shortcode="CNjqTXUsCPG",
                profile=profile,
                post_type="photo",
                post_url="https://instagram.com/p/CNjqTXUsCPG/",
                posted_at=timezone.now(),
            )
        )

        posts.append(
            InstagramPost.objects.create(
                external_id="post_2",
                shortcode="CtAI5BwBdtg",
                profile=profile,
                post_type="photo",
                post_url="https://instagram.com/p/CtAI5BwBdtg/",
                posted_at=timezone.now(),
            )
        )

        posts.append(
            InstagramPost.objects.create(
                external_id="post_3",
                shortcode="CNjqoQbsNd9",
                profile=profile,
                post_type="photo",
                post_url="https://instagram.com/p/CNjqoQbsNd9/",
                posted_at=timezone.now(),
            )
        )

        # Настраиваем мок с ошибками
        mock_service_instance = mock_comment_service.return_value
        mock_service_instance.import_comments_for_post.side_effect = [
            [Mock(), Mock()],  # Успех для первого
            Exception("API Error"),  # Ошибка для второго
            [Mock()],  # Успех для третьего
        ]

        # Выполняем действие
        queryset = InstagramPost.objects.filter(id__in=[p.id for p in posts])
        post_admin.import_comments_action(request, queryset)

        # Проверяем вызовы
        assert mock_service_instance.import_comments_for_post.call_count == 3

        # Проверяем сообщения
        from django.contrib.messages import SUCCESS, ERROR, WARNING
        
        # Собираем сообщения по уровням
        success_messages = [
            call[0][1] for call in request._messages.add.call_args_list
            if call[0][0] == SUCCESS
        ]
        error_messages = [
            call[0][1] for call in request._messages.add.call_args_list
            if call[0][0] == ERROR
        ]
        warning_messages = [
            call[0][1] for call in request._messages.add.call_args_list
            if call[0][0] == WARNING
        ]

        # Должно быть сообщение об успехе
        assert any("Successfully imported 3 comments" in msg for msg in success_messages)

        # Должно быть сообщение об ошибке
        assert any("Error for post CtAI5BwBdtg" in msg for msg in error_messages)
        assert any("API Error" in msg for msg in error_messages)

        # Должно быть предупреждение о неудачных импортах
        assert any("Failed to import comments for 1 posts" in msg for msg in warning_messages)

    def test_comment_inline_display(self, post_admin, post, comments):
        """Тест отображения inline комментариев."""
        # Проверяем, что inline зарегистрирован
        from instagram_manager.admin import InstagramCommentInline

        assert InstagramCommentInline in post_admin.inlines

        # Проверяем настройки inline
        inline_instance = InstagramCommentInline(InstagramPost, post_admin.admin_site)
        assert inline_instance.model == InstagramComment
        assert inline_instance.extra == 0
        assert inline_instance.show_change_link is True
