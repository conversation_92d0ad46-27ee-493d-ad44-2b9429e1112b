import unittest
from unittest.mock import patch, MagicMock
from django.test import TestCase
from django.conf import settings
from instagram_manager.instagram_api import BrightDataClient, BrightDataAPIError


import pytest
class BrightDataTimeoutTest(TestCase):
    """Test BrightData client timeout configurations"""

    def setUp(self):
        """Set up test client"""
        self.client = BrightDataClient()

    def test_http_request_timeout(self):
        """Test that HTTP request timeout is set to 600 seconds"""
        with patch.object(self.client.session, "request") as mock_request:
            mock_response = MagicMock()
            mock_response.status_code = 200
            mock_response.json.return_value = {"snapshot_id": "test_123"}
            mock_request.return_value = mock_response

            # Make a test request
            self.client._make_request("GET", "test/endpoint")

            # Check that request was called with timeout=600
            mock_request.assert_called_once()
            args, kwargs = mock_request.call_args
            assert kwargs.get("timeout") == 600

    def test_get_posts_max_attempts(self):
        """Test that get_posts waits for up to 10 minutes (200 attempts * 3 seconds)"""
        with patch("instagram_manager.instagram_api.brightdata.clients.post.PostClient.trigger_snapshot") as mock_trigger:
            mock_trigger.return_value = "test_snapshot_123"

            with patch("instagram_manager.instagram_api.brightdata.clients.post.PostClient.wait_for_snapshot") as mock_wait:
                # Mock wait_for_snapshot to do nothing (it handles the wait internally)
                mock_wait.return_value = None

                with patch("instagram_manager.instagram_api.brightdata.clients.post.PostClient.get_snapshot_data") as mock_data:
                    # Return valid post data that will pass Pydantic validation
                    mock_data.return_value = [
                        {
                            "pk": "123",
                            "content_id": "ABC123",
                            "url": "https://instagram.com/p/DKZ7A0XpB1U/",
                            "timestamp": 1609459200,
                            "caption": "Test post",
                            "like_count": 10,
                            "comment_count": 5,
                        }
                    ]

                    # This should succeed
                    result = self.client.get_posts(settings.INSTAGRAM_TEST_USERNAME)

                    # Verify we got results
                    assert len(result) == 1
                    assert result[0].post_id == "123"

                    # Verify the mocks were called
                    mock_trigger.assert_called_once()
                    mock_wait.assert_called_once()
                    mock_data.assert_called_once()

    def test_get_posts_timeout_exceeded(self):
        """Test that get_posts raises timeout error after 10 minutes"""
        # Clear cache to ensure fresh test
        from django.core.cache import cache

        cache.clear()

        with patch("instagram_manager.instagram_api.brightdata.clients.post.PostClient.trigger_snapshot") as mock_trigger:
            mock_trigger.return_value = "test_snapshot_123"

            with patch("instagram_manager.instagram_api.brightdata.clients.post.PostClient.wait_for_snapshot") as mock_wait:
                # Simulate timeout by raising BrightDataAPIError
                mock_wait.side_effect = BrightDataAPIError(
                    "Snapshot timeout after 600 seconds",
                    snapshot_id="test_snapshot_123"
                )

                # This should raise timeout error
                with pytest.raises(BrightDataAPIError) as context:
                    self.client.get_posts("testuser_timeout_test")

                # Verify timeout error
                error = context.value
                error_str = str(error)
                assert "timeout" in error_str.lower()
                assert "test_snapshot_123" in error_str

    def test_get_profile_max_attempts(self):
        """Test that get_profile waits for up to 10 minutes (300 attempts * 2 seconds)"""
        with patch("instagram_manager.instagram_api.brightdata.clients.profile.ProfileClient.trigger_snapshot") as mock_trigger:
            mock_trigger.return_value = "test_snapshot_123"

            with patch("instagram_manager.instagram_api.brightdata.clients.profile.ProfileClient.wait_for_snapshot") as mock_wait:
                # Mock wait_for_snapshot to do nothing
                mock_wait.return_value = None

                with patch("instagram_manager.instagram_api.brightdata.clients.profile.ProfileClient.get_snapshot_data") as mock_data:
                    mock_data.return_value = [{"pk": "123", "username": "testuser"}]

                    # Need to mock DataValidator methods too
                    with patch("instagram_manager.instagram_api.brightdata.validators.DataValidator.extract_error_info") as mock_error:
                        mock_error.return_value = None  # No errors
                        
                        with patch("instagram_manager.instagram_api.brightdata.validators.DataValidator.prepare_profile_data") as mock_prepare:
                            mock_prepare.return_value = {"pk": "123", "username": "testuser"}
                            
                            with patch("instagram_manager.instagram_api.brightdata.validators.DataValidator.validate_single") as mock_validate:
                                from instagram_manager.schemas.brightdata import InstagramProfileResponse
                                mock_validate.return_value = InstagramProfileResponse(
                                    pk="123", 
                                    username="testuser", 
                                    profile_id="123",
                                    full_name="Test User",
                                    post_count=0,
                                    follower_count=0,
                                    following_count=0
                                )
                                
                                # This should succeed
                                result = self.client.get_profile("testuser")

                                # Verify we got results
                                assert result.profile_id == "123"
                                assert result.username == "testuser"

                                # Verify the mocks were called
                                mock_trigger.assert_called_once()
                                mock_wait.assert_called_once()
                                mock_data.assert_called_once()

    def test_all_methods_have_10_minute_timeout(self):
        """Test that all data fetching methods have 10-minute timeout"""
        # Test that the client is configured with 600 second timeout
        assert self.client.profile_client.timeout == 600
        assert self.client.post_client.timeout == 600
        assert self.client.comment_client.timeout == 600
        
        # Test that making a request respects the timeout
        with patch("instagram_manager.instagram_api.brightdata.clients.comment.CommentClient.trigger_snapshot") as mock_trigger:
            mock_trigger.return_value = "test_snapshot_123"
            
            with patch("instagram_manager.instagram_api.brightdata.clients.comment.CommentClient.wait_for_snapshot") as mock_wait:
                # Simulate timeout
                mock_wait.side_effect = BrightDataAPIError("Snapshot timeout after 600 seconds", snapshot_id="test_snapshot_123")
                
                with pytest.raises(BrightDataAPIError) as context:
                    self.client.get_post_comments("https://instagram.com/p/test")
                
                # Verify timeout error
                assert "timeout" in str(context.value).lower()

    def test_timeout_configuration_values(self):
        """Test that timeout values are correctly configured"""
        # Test that all clients have the correct timeout
        assert self.client.profile_client.timeout == 600
        assert self.client.post_client.timeout == 600  
        assert self.client.comment_client.timeout == 600
        assert hasattr(self.client, "hashtag_client") and self.client.hashtag_client.timeout == 600



if __name__ == "__main__":
    unittest.main()
