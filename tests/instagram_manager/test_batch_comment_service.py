"""
Tests for BatchCommentService to verify batch comment import functionality.
"""

from unittest.mock import patch

import pytest
from django.utils import timezone

from instagram_manager.models import InstagramComment, InstagramPost, InstagramProfile
from instagram_manager.schemas.brightdata import InstagramCommentResponse
from instagram_manager.services.batch_comment_service import BatchCommentService


@pytest.fixture
def profile():
    """Create test profile."""
    return InstagramProfile.objects.create(
        profile_id="test_profile_123",
        username="testuser",
        full_name="Test User",
        follower_count=1000,
    )


@pytest.fixture
def posts(profile):
    """Create test posts."""
    posts_list = []
    for i in range(3):
        post = InstagramPost.objects.create(
            external_id=f"post_{i}",
            shortcode=f"ABC{i}",
            profile=profile,
            post_type="photo",
            post_url=f"https://instagram.com/p/ABC{i}/",
            caption=f"Test post {i}",
            like_count=100 + i * 10,
            comment_count=5 + i,
            posted_at=timezone.now(),
        )
        posts_list.append(post)
    return posts_list


@pytest.fixture
def mock_brightdata_client():
    """Mock BrightData client."""
    with patch("instagram_manager.services.batch_comment_service.BrightDataClient") as mock:
        yield mock


@pytest.fixture
def sample_batch_comments():
    """Sample batch comment responses."""
    return {
        "https://instagram.com/p/ABC0/": [
            InstagramCommentResponse(
                id="comment_0_1",
                comment="Great post!",
                comment_user="user1",
                comment_date=timezone.now(),
                likes_number=5,
                is_verified_user=False,
                has_replies=False,
                replies_number=0,
                is_pinned=False,
                is_hidden=False,
            ),
            InstagramCommentResponse(
                id="comment_0_2",
                comment="Amazing!",
                comment_user="user2",
                comment_date=timezone.now(),
                likes_number=10,
                is_verified_user=True,
                has_replies=False,
                replies_number=0,
                is_pinned=True,
                is_hidden=False,
            ),
        ],
        "https://instagram.com/p/ABC1/": [
            InstagramCommentResponse(
                id="comment_1_1",
                comment="Nice!",
                comment_user="user3",
                comment_date=timezone.now(),
                likes_number=3,
                is_verified_user=False,
                has_replies=True,
                replies_number=1,
                is_pinned=False,
                is_hidden=False,
            ),
            InstagramCommentResponse(
                id="comment_1_2",
                comment="Reply to nice",
                comment_user="user4",
                comment_date=timezone.now(),
                likes_number=1,
                is_verified_user=False,
                has_replies=False,
                replies_number=0,
                reply_to_comment_id="comment_1_1",
                is_pinned=False,
                is_hidden=False,
            ),
        ],
        "https://instagram.com/p/ABC2/": [
            InstagramCommentResponse(
                id="comment_2_1",
                comment="Awesome!",
                comment_user="user5",
                comment_date=timezone.now(),
                likes_number=20,
                is_verified_user=False,
                has_replies=False,
                replies_number=0,
                is_pinned=False,
                is_hidden=False,
            ),
        ],
    }


@pytest.mark.django_db
class TestBatchCommentService:
    """Tests for BatchCommentService."""

    def setup_method(self):
        """Clear comments before each test."""
        InstagramComment.objects.all().delete()

    def test_batch_import_comments_success(
        self, posts, mock_brightdata_client, sample_batch_comments, caplog
    ):
        """Test successful batch comment import."""
        import logging
        caplog.set_level(logging.DEBUG)
        
        # Setup mock
        mock_client = mock_brightdata_client.return_value
        mock_client.get_batch_comments.return_value = sample_batch_comments
        
        # Create service
        service = BatchCommentService(mock_client)
        
        # Prepare post URLs
        post_urls = [post.post_url for post in posts]
        print(f"Test post URLs: {post_urls}")
        print(f"Mock response keys: {list(sample_batch_comments.keys())}")
        
        # Import comments
        try:
            results = service.import_comments_batch(
                post_urls=post_urls,
                limit_per_post=100,
                include_replies=True,
            )
        except Exception as e:
            print(f"Exception during import: {e}")
            import traceback
            traceback.print_exc()
            raise
        
        # Verify API was called once with all URLs
        mock_client.get_batch_comments.assert_called_once_with(
            post_urls=post_urls,
            skip_validation_errors=True,
        )
        
        # Verify results
        print(f"Results: {results}")
        print(f"Results keys: {list(results.keys())}")
        
        # Debug: Check if posts exist
        for url in post_urls:
            print(f"Checking URL: {url}")
            if url in results:
                print(f"  Result: {results[url]}")
            else:
                print("  URL not in results!")
        
        assert len(results) == 4  # 3 posts + _statistics
        assert "_statistics" in results
        
        # Check statistics
        stats = results["_statistics"]
        print(f"Stats: {stats}")
        
        # Check results detail
        print(f"All results: {results}")
        
        # Check comments in database
        print(f"Comments in DB: {InstagramComment.objects.count()}")
        for comment in InstagramComment.objects.all():
            print(f"  - Comment: {comment.external_id} - {comment.text}")
        
        # Check posts in database
        print(f"Posts in DB: {InstagramPost.objects.count()}")
        for post in InstagramPost.objects.all():
            print(f"  - Post: {post.shortcode} - {post.post_url}")
        
        # Remove debug assert - we now understand the issue
        
        assert stats["total_comments_received"] == 5
        assert stats["total_comments_imported"] == 5
        assert stats["posts_with_comments"] == 3
        assert stats["posts_without_comments"] == 0
        
        # Verify comments were saved
        assert InstagramComment.objects.count() == 5
        
        # Verify each post's results
        for post in posts:
            post_result = results[post.post_url]
            assert post_result["success"] is True
            assert post_result["comment_count"] > 0
        
        # Verify comment hierarchy
        parent_comment = InstagramComment.objects.get(external_id="comment_1_1")
        reply_comment = InstagramComment.objects.get(external_id="comment_1_2")
        assert reply_comment.reply_to == parent_comment

    def test_batch_import_with_filtering(
        self, posts, mock_brightdata_client, sample_batch_comments
    ):
        """Test batch import with min_likes filter."""
        # Setup mock
        mock_client = mock_brightdata_client.return_value
        mock_client.get_batch_comments.return_value = sample_batch_comments
        
        service = BatchCommentService(mock_client)
        post_urls = [post.post_url for post in posts]
        
        # Import with min_likes filter
        results = service.import_comments_batch(
            post_urls=post_urls,
            limit_per_post=100,
            min_likes=5,  # Filter out comments with less than 5 likes
        )
        
        # Check that low-like comments were filtered
        stats = results["_statistics"]
        assert stats["comments_filtered"] == 2  # comment_1_2 (1 like) and comment_1_1 (3 likes)
        assert stats["total_comments_imported"] == 3
        
        # Verify only high-like comments were saved
        for comment in InstagramComment.objects.all():
            assert comment.like_count >= 5

    def test_batch_import_without_replies(
        self, posts, mock_brightdata_client, sample_batch_comments
    ):
        """Test batch import excluding replies."""
        # Setup mock
        mock_client = mock_brightdata_client.return_value
        mock_client.get_batch_comments.return_value = sample_batch_comments
        
        service = BatchCommentService(mock_client)
        post_urls = [post.post_url for post in posts]
        
        # Import without replies
        results = service.import_comments_batch(
            post_urls=post_urls,
            include_replies=False,
        )
        
        # Check that replies were filtered
        stats = results["_statistics"]
        assert stats["comments_filtered"] == 1  # comment_1_2 is a reply
        assert stats["total_comments_imported"] == 4
        
        # Verify no replies were saved
        assert not InstagramComment.objects.filter(reply_to__isnull=False).exists()

    def test_batch_import_with_missing_post(
        self, posts, mock_brightdata_client
    ):
        """Test batch import when post is not found in database."""
        # Setup mock with extra URL not in database
        mock_client = mock_brightdata_client.return_value
        mock_client.get_batch_comments.return_value = {
            "https://instagram.com/p/MISSING/": [
                InstagramCommentResponse(
                    id="comment_missing",
                    comment="Comment for missing post",
                    comment_user="user",
                    comment_date=timezone.now(),
                    likes_number=5,
                    is_verified_user=False,
                    has_replies=False,
                    replies_number=0,
                    is_pinned=False,
                    is_hidden=False,
                ),
            ],
        }
        
        service = BatchCommentService(mock_client)
        
        # Import with URL that doesn't exist in DB
        results = service.import_comments_batch(
            post_urls=["https://instagram.com/p/MISSING/"],
        )
        
        # Verify result shows failure
        missing_result = results["https://instagram.com/p/MISSING/"]
        assert missing_result["success"] is False
        assert missing_result["error"] == "Post not found in database"
        assert missing_result["comment_count"] == 0
        
        # No comments should be saved
        assert InstagramComment.objects.count() == 0

    def test_batch_import_api_error(
        self, posts, mock_brightdata_client
    ):
        """Test batch import when API returns error."""
        # Setup mock to raise exception
        mock_client = mock_brightdata_client.return_value
        mock_client.get_batch_comments.side_effect = Exception("API Error")
        
        service = BatchCommentService(mock_client)
        post_urls = [post.post_url for post in posts]
        
        # Import should handle error gracefully
        results = service.import_comments_batch(post_urls=post_urls)
        
        # All posts should have error results
        for post_url in post_urls:
            assert results[post_url]["success"] is False
            assert "API Error" in results[post_url]["error"]
            assert results[post_url]["comment_count"] == 0

    def test_batch_import_empty_response(
        self, posts, mock_brightdata_client
    ):
        """Test batch import when API returns empty response."""
        # Setup mock with empty response
        mock_client = mock_brightdata_client.return_value
        mock_client.get_batch_comments.return_value = {}
        
        service = BatchCommentService(mock_client)
        post_urls = [post.post_url for post in posts]
        
        # Import with empty response
        results = service.import_comments_batch(post_urls=post_urls)
        
        # Check statistics
        stats = results["_statistics"]
        assert stats["total_comments_received"] == 0
        assert stats["total_comments_imported"] == 0
        assert stats["posts_with_comments"] == 0
        assert stats["posts_without_comments"] == 0
        
        # No comments should be saved
        assert InstagramComment.objects.count() == 0

    @patch("instagram_manager.services.batch_comment_service.logger")
    def test_batch_import_logging(
        self, mock_logger, posts, mock_brightdata_client, sample_batch_comments
    ):
        """Test that batch import logs correctly."""
        # Setup mock
        mock_client = mock_brightdata_client.return_value
        mock_client.get_batch_comments.return_value = sample_batch_comments
        
        service = BatchCommentService(mock_client)
        post_urls = [post.post_url for post in posts]
        
        # Import comments
        service.import_comments_batch(post_urls=post_urls)
        
        # Verify logging calls
        mock_logger.info.assert_any_call(
            "[BATCH COMMENTS] Starting batch comment import for 3 posts"
        )
        mock_logger.info.assert_any_call(
            "[BATCH COMMENTS] Post URLs sample: ['https://instagram.com/p/ABC0/', 'https://instagram.com/p/ABC1/', 'https://instagram.com/p/ABC2/']..."
        )
        mock_logger.info.assert_any_call(
            "[BATCH COMMENTS] Fetching comments from BrightData API with single batch request..."
        )
        mock_logger.info.assert_any_call(
            "[BATCH COMMENTS] Received response for 3 posts"
        )