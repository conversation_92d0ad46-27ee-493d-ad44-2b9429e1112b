"""
Тесты для обработки ошибок BrightData с включением URL в сообщения об ошибках.
"""

import pytest
from unittest.mock import patch
from django.test import TestCase

from instagram_manager.instagram_api import (
    BrightDataClient,
    BrightDataAPIError,
    ProfileNotFoundError,
    PrivateProfileError,
    InstagramRateLimitError,
)


class TestBrightDataErrorHandling(TestCase):
    """Тесты для обработки ошибок BrightData с URL информацией."""

    def setUp(self):
        """Настройка тестов."""
        
        self.client = BrightDataClient()

    def test_error_exceptions_include_url_and_snapshot_id(self):
        """Тест что исключения включают URL и snapshot ID."""
        url = "https://instagram.com/testuser/"
        snapshot_id = "test_snapshot_123"
        error_message = "Test error message"

        # Тест BrightDataAPIError
        error = BrightDataAPIError(error_message, url=url, snapshot_id=snapshot_id)
        assert error.url == url
        assert error.snapshot_id == snapshot_id
        error_str = str(error)
        assert error_message in error_str
        assert f"URL: {url}" in error_str
        assert f"Snapshot ID: {snapshot_id}" in error_str

        # Тест ProfileNotFoundError
        error = ProfileNotFoundError(error_message, url=url, snapshot_id=snapshot_id)
        error_str = str(error)
        assert error_message in error_str
        assert f"URL: {url}" in error_str
        assert f"Snapshot ID: {snapshot_id}" in error_str

        # Тест PrivateProfileError
        error = PrivateProfileError(error_message, url=url, snapshot_id=snapshot_id)
        error_str = str(error)
        assert error_message in error_str
        assert f"URL: {url}" in error_str
        assert f"Snapshot ID: {snapshot_id}" in error_str

        # Тест InstagramRateLimitError
        error = InstagramRateLimitError(error_message, url=url, snapshot_id=snapshot_id)
        error_str = str(error)
        assert error_message in error_str
        assert f"URL: {url}" in error_str
        assert f"Snapshot ID: {snapshot_id}" in error_str

    @patch("instagram_manager.instagram_api.brightdata.clients.profile.ProfileClient.get_snapshot_data")
    @patch("instagram_manager.instagram_api.brightdata.clients.profile.ProfileClient.wait_for_snapshot")
    @patch("instagram_manager.instagram_api.brightdata.clients.profile.ProfileClient.trigger_snapshot")
    def test_get_profile_not_found_error_includes_url(self, mock_trigger, mock_wait, mock_data):
        """Тест что ошибка ProfileNotFoundError включает URL профиля."""
        username = "nonexistentuser"
        expected_url = f"https://www.instagram.com/{username}/"
        snapshot_id = "test_snapshot_123"

        mock_trigger.return_value = snapshot_id
        # Mock wait_for_snapshot to return successfully
        mock_wait.return_value = {"status": "ready"}
        # Mock get_snapshot_data to return empty list (profile not found)
        mock_data.return_value = []

        with pytest.raises(ProfileNotFoundError) as context:
            self.client.get_profile(username)

        error = context.value
        assert error.url == expected_url
        # ProfileNotFoundError from empty data doesn't include snapshot_id

        error_str = str(error)
        assert expected_url in error_str
        assert "Profile not found" in error_str

    @patch("instagram_manager.instagram_api.brightdata.validators.DataValidator.is_private_profile_error")
    @patch("instagram_manager.instagram_api.brightdata.validators.DataValidator.extract_error_info")
    @patch("instagram_manager.instagram_api.brightdata.clients.profile.ProfileClient.get_snapshot_data")
    @patch("instagram_manager.instagram_api.brightdata.clients.profile.ProfileClient.wait_for_snapshot")
    @patch("instagram_manager.instagram_api.brightdata.clients.profile.ProfileClient.trigger_snapshot")
    def test_get_profile_private_error_includes_url(self, mock_trigger, mock_wait, mock_data, mock_extract_error, mock_is_private):
        """Тест что ошибка PrivateProfileError включает URL профиля."""
        username = "privateuser"
        expected_url = f"https://www.instagram.com/{username}/"
        snapshot_id = "test_snapshot_456"

        mock_trigger.return_value = snapshot_id
        # Mock wait_for_snapshot to return successfully
        mock_wait.return_value = {"status": "ready"}
        # Mock get_snapshot_data to return data with error
        mock_data.return_value = [{"some": "data"}]
        # Mock DataValidator to detect private profile error
        mock_extract_error.return_value = {
            "error": "This account is private",
            "error_code": "private_profile"
        }
        mock_is_private.return_value = True

        with pytest.raises(PrivateProfileError) as context:
            self.client.get_profile(username)

        error = context.value
        assert error.url == expected_url
        # PrivateProfileError from DataValidator doesn't include snapshot_id

        error_str = str(error)
        assert expected_url in error_str
        # The actual error message should contain "Profile is private"
        assert "private" in error_str.lower()

    @patch("instagram_manager.instagram_api.brightdata.clients.profile.ProfileClient.get_snapshot_data")
    @patch("instagram_manager.instagram_api.brightdata.clients.profile.ProfileClient.wait_for_snapshot")
    @patch("instagram_manager.instagram_api.brightdata.clients.profile.ProfileClient.trigger_snapshot")
    def test_get_profile_rate_limit_error_includes_url(self, mock_trigger, mock_wait, mock_data):
        """Тест что ошибка InstagramRateLimitError включает URL профиля."""
        username = "ratelimituser"
        snapshot_id = "test_snapshot_789"

        mock_trigger.return_value = snapshot_id
        # Simulate that wait_for_snapshot throws error due to failed status
        mock_wait.side_effect = BrightDataAPIError(
            "Snapshot failed: Rate limit exceeded by Instagram",
            snapshot_id=snapshot_id
        )

        # ProfileClient doesn't catch BrightDataAPIError to convert to InstagramRateLimitError
        # So we expect BrightDataAPIError instead
        with pytest.raises(BrightDataAPIError) as context:
            self.client.get_profile(username)

        error = context.value
        # BrightDataAPIError from wait_for_snapshot doesn't include URL
        assert error.snapshot_id == snapshot_id

        error_str = str(error)
        assert snapshot_id in error_str
        assert "Rate limit" in error_str

    @patch("instagram_manager.instagram_api.brightdata.clients.profile.ProfileClient.get_snapshot_data")
    @patch("instagram_manager.instagram_api.brightdata.clients.profile.ProfileClient.wait_for_snapshot")
    @patch("instagram_manager.instagram_api.brightdata.clients.profile.ProfileClient.trigger_snapshot")
    def test_get_profile_generic_error_includes_url(self, mock_trigger, mock_wait, mock_data):
        """Тест что общая ошибка BrightDataAPIError включает URL профиля."""
        username = "erroruser"
        snapshot_id = "test_snapshot_999"

        mock_trigger.return_value = snapshot_id
        # Simulate generic error from wait_for_snapshot
        mock_wait.side_effect = BrightDataAPIError(
            "Snapshot failed: Some unexpected error occurred",
            snapshot_id=snapshot_id
        )

        with pytest.raises(BrightDataAPIError) as context:
            self.client.get_profile(username)

        error = context.value
        # BrightDataAPIError from wait_for_snapshot doesn't include URL
        # Only snapshot_id is included
        assert error.snapshot_id == snapshot_id

        error_str = str(error)
        assert snapshot_id in error_str
        assert "Snapshot failed" in error_str

    @patch("instagram_manager.instagram_api.brightdata.clients.profile.ProfileClient.get_snapshot_data")
    @patch("instagram_manager.instagram_api.brightdata.clients.profile.ProfileClient.wait_for_snapshot")
    @patch("instagram_manager.instagram_api.brightdata.clients.profile.ProfileClient.trigger_snapshot")
    def test_get_profile_timeout_error_includes_url(self, mock_trigger, mock_wait, mock_data):
        """Тест что ошибка timeout включает URL профиля."""
        username = "timeoutuser"
        snapshot_id = "test_snapshot_timeout"

        mock_trigger.return_value = snapshot_id
        # Симулируем timeout через wait_for_snapshot
        mock_wait.side_effect = TimeoutError(
            f"Snapshot {snapshot_id} did not complete after 600 seconds"
        )

        # TimeoutError should be caught and re-raised by ProfileClient
        with pytest.raises(TimeoutError) as context:
            self.client.get_profile(username)

        error = context.value
        error_str = str(error)
        # TimeoutError doesn't have url or snapshot_id attributes, but the message should contain snapshot_id
        assert snapshot_id in error_str
        assert "did not complete" in error_str

    @patch("instagram_manager.instagram_api.brightdata.clients.post.PostClient.wait_for_snapshot")
    @patch("instagram_manager.instagram_api.brightdata.clients.post.PostClient.trigger_snapshot")
    def test_get_posts_error_includes_url(self, mock_trigger, mock_wait):
        """Тест что ошибки get_posts включают URL профиля."""
        username = "erroruser"
        snapshot_id = "test_posts_snapshot"

        mock_trigger.return_value = snapshot_id
        # Mock wait_for_snapshot to throw error
        mock_wait.side_effect = BrightDataAPIError(
            "Snapshot failed: Profile not found",
            snapshot_id=snapshot_id
        )

        # PostClient also doesn't wrap BrightDataAPIError with URL context
        with pytest.raises(BrightDataAPIError) as context:
            self.client.get_posts(username)

        error = context.value
        # BrightDataAPIError from wait_for_snapshot doesn't include URL
        assert error.snapshot_id == snapshot_id

        error_str = str(error)
        assert snapshot_id in error_str

    @patch("instagram_manager.instagram_api.brightdata.clients.comment.CommentClient.wait_for_snapshot")
    @patch("instagram_manager.instagram_api.brightdata.clients.comment.CommentClient.trigger_snapshot")
    def test_get_post_comments_error_includes_url(self, mock_trigger, mock_wait):
        """Тест что ошибки get_post_comments включают URL поста."""
        post_url = "https://www.instagram.com/p/DKZ7A0XpB1U/"
        snapshot_id = "test_comments_snapshot"

        mock_trigger.return_value = snapshot_id
        # Mock wait_for_snapshot to throw error
        mock_wait.side_effect = BrightDataAPIError(
            "Snapshot failed: Post not found",
            snapshot_id=snapshot_id
        )

        # CommentClient also doesn't wrap BrightDataAPIError with URL context
        with pytest.raises(BrightDataAPIError) as context:
            self.client.get_post_comments(post_url)

        error = context.value
        # BrightDataAPIError from wait_for_snapshot doesn't include URL
        assert error.snapshot_id == snapshot_id

        error_str = str(error)
        assert snapshot_id in error_str

    def test_error_without_url_or_snapshot_id(self):
        """Тест что ошибки работают без URL или snapshot ID."""
        error_message = "Basic error message"

        # Только сообщение
        error = BrightDataAPIError(error_message)
        assert error.url is None
        assert error.snapshot_id is None
        assert str(error) == error_message

        # Только URL
        url = "https://instagram.com/test/"
        error = BrightDataAPIError(error_message, url=url)
        assert error.url == url
        assert error.snapshot_id is None
        error_str = str(error)
        assert error_message in error_str
        assert f"URL: {url}" in error_str
        assert "Snapshot ID" not in error_str

        # Только snapshot ID
        snapshot_id = "test_123"
        error = BrightDataAPIError(error_message, snapshot_id=snapshot_id)
        assert error.url is None
        assert error.snapshot_id == snapshot_id
        error_str = str(error)
        assert error_message in error_str
        assert f"Snapshot ID: {snapshot_id}" in error_str
        assert "URL:" not in error_str

    @patch("instagram_manager.instagram_api.brightdata.validators.DataValidator.validate_single")
    @patch("instagram_manager.instagram_api.brightdata.validators.DataValidator.prepare_profile_data")
    @patch("instagram_manager.instagram_api.brightdata.validators.DataValidator.extract_error_info")
    @patch("instagram_manager.instagram_api.brightdata.clients.profile.ProfileClient.get_snapshot_data")
    @patch("instagram_manager.instagram_api.brightdata.clients.profile.ProfileClient.wait_for_snapshot")
    @patch("instagram_manager.instagram_api.brightdata.clients.profile.ProfileClient.trigger_snapshot")
    def test_get_profile_validation_error_includes_url(
        self, mock_trigger, mock_wait, mock_data, mock_extract_error, mock_prepare, mock_validate
    ):
        """Тест что ошибки валидации включают URL профиля."""
        username = "validationerror"
        expected_url = f"https://www.instagram.com/{username}/"
        snapshot_id = "test_validation_snapshot"

        mock_trigger.return_value = snapshot_id
        mock_wait.return_value = {"status": "ready"}
        # Mock get_snapshot_data to return data
        mock_data.return_value = [{"username": username}]
        # Mock DataValidator methods
        mock_extract_error.return_value = None  # No error in data
        mock_prepare.return_value = {"username": username, "url": expected_url}
        mock_validate.return_value = None  # Validation fails

        with pytest.raises(BrightDataAPIError) as context:
            self.client.get_profile(username)

        error = context.value
        # ProfileNotFoundError from validation failure doesn't include URL or snapshot_id
        # because it's raised without these parameters
        assert error.url is None
        assert error.snapshot_id is None

        error_str = str(error)
        assert "Failed to validate profile data" in error_str


class TestErrorHandlingInTestsFailure(TestCase):
    """Тесты что показывают, как тесты должны падать с четкими сообщениями об ошибках."""

    def setUp(self):
        """Настройка тестов."""
        
        self.client = BrightDataClient()

    @patch("instagram_manager.instagram_api.brightdata.clients.profile.ProfileClient.wait_for_snapshot")
    @patch("instagram_manager.instagram_api.brightdata.clients.profile.ProfileClient.trigger_snapshot")
    def test_brightdata_error_causes_test_failure_with_url_info(
        self, mock_trigger, mock_wait
    ):
        """
        Демонстрация того, как тест должен падать при ошибке BrightData,
        показывая URL и snapshot ID для отладки.
        """
        username = "failuretest"
        snapshot_id = "test_failure_snapshot"

        mock_trigger.return_value = snapshot_id
        # Mock wait_for_snapshot to throw error
        mock_wait.side_effect = BrightDataAPIError(
            "Snapshot failed: Critical API failure - service unavailable",
            snapshot_id=snapshot_id
        )

        # Тест что ошибка правильно формируется с контекстом
        with pytest.raises(BrightDataAPIError) as context:
            self.client.get_profile(username)

        e = context.value
        error_message = str(e)

        # Убеждаемся что ошибка содержит:
        # 1. Оригинальное сообщение об ошибке от BrightData
        assert "Critical API failure - service unavailable" in error_message

        # 2. Snapshot ID для отслеживания в логах BrightData
        assert snapshot_id in error_message

        # 3. Тип ошибки для правильной обработки
        assert "Snapshot failed" in error_message

        # NOTE: URL is not included in BrightDataAPIError from wait_for_snapshot
        # This is a limitation of the current implementation

        # Проверяем что ошибка содержит все нужные данные для отладки

    @patch("instagram_manager.instagram_api.brightdata.clients.post.PostClient.wait_for_snapshot")
    @patch("instagram_manager.instagram_api.brightdata.clients.post.PostClient.trigger_snapshot")
    def test_posts_error_shows_discover_context(self, mock_trigger, mock_wait):
        """Тест ошибки при получении постов с полным контекстом discover."""
        username = "postserrortest"
        snapshot_id = "test_posts_error_snapshot"

        mock_trigger.return_value = snapshot_id
        # Mock wait_for_snapshot to throw error
        mock_wait.side_effect = BrightDataAPIError(
            "Snapshot failed: Instagram temporarily blocked the request",
            snapshot_id=snapshot_id
        )

        with pytest.raises(BrightDataAPIError) as context:
            self.client.get_posts(username, start_date="2024-01-01", post_types=["photo"])

        e = context.value
        error_message = str(e)

        # Проверяем контекст ошибки для отладки
        assert "Instagram temporarily blocked" in error_message
        # URL is not included in BrightDataAPIError from wait_for_snapshot
        assert snapshot_id in error_message

        # Проверяем что ошибка содержит нужную информацию для отладки discover

    @patch("instagram_manager.instagram_api.brightdata.clients.comment.CommentClient.wait_for_snapshot")
    @patch("instagram_manager.instagram_api.brightdata.clients.comment.CommentClient.trigger_snapshot")
    def test_comments_error_shows_post_context(self, mock_trigger, mock_wait):
        """Тест ошибки при получении комментариев с контекстом поста."""
        post_url = "https://www.instagram.com/p/DKVPW5_T6dZ/"
        snapshot_id = "test_comments_error_snapshot"

        mock_trigger.return_value = snapshot_id
        # Mock wait_for_snapshot to throw error
        mock_wait.side_effect = BrightDataAPIError(
            "Snapshot failed: Comments are disabled for this post",
            snapshot_id=snapshot_id
        )

        with pytest.raises(BrightDataAPIError) as context:
            self.client.get_post_comments(post_url)

        e = context.value
        error_message = str(e)

        # Проверяем контекст ошибки
        assert "Comments are disabled" in error_message
        # URL is not included in BrightDataAPIError from wait_for_snapshot
        assert snapshot_id in error_message

        # Проверяем что ошибка содержит нужную информацию для отладки comments

    @patch("instagram_manager.instagram_api.brightdata.clients.post.PostClient.wait_for_snapshot")
    @patch("instagram_manager.instagram_api.brightdata.clients.post.PostClient.trigger_snapshot")
    def test_get_posts_by_urls_error_includes_urls(self, mock_trigger, mock_wait):
        """Тест ошибки при получении постов по URL с контекстом всех URL."""
        post_urls = [
            "https://www.instagram.com/p/DKZ7A0XpB1U/",
            "https://www.instagram.com/p/DKVPW5_T6dZ/",
            "https://www.instagram.com/p/DKZ7A0XpB1U/",
            "https://www.instagram.com/p/DKVPW5_T6dZ/",
            "https://www.instagram.com/p/DKZ7A0XpB1U/",
        ]
        snapshot_id = "test_posts_by_urls_snapshot"

        mock_trigger.return_value = snapshot_id
        # Mock wait_for_snapshot to throw error
        mock_wait.side_effect = BrightDataAPIError(
            "Snapshot failed: Batch request failed - invalid post URLs",
            snapshot_id=snapshot_id
        )

        with pytest.raises(BrightDataAPIError) as context:
            self.client.get_posts_by_urls(post_urls)

        e = context.value
        error_message = str(e)

        # Проверяем контекст ошибки
        assert "Batch request failed" in error_message
        assert snapshot_id in error_message
        # NOTE: URLs are not included in BrightDataAPIError from wait_for_snapshot
        # This is a limitation of the current implementation
        # The error only contains the snapshot_id for debugging
