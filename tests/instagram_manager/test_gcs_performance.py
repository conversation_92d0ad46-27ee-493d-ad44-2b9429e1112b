"""
Performance benchmark tests for GCS functionality.

Measures and compares performance between local storage and GCS uploads.
"""

import time
import gc
from concurrent.futures import ThreadPoolExecutor, as_completed
from unittest.mock import Mock, patch

from django.test import TestCase, override_settings
from django.utils import timezone

from instagram_manager.models import InstagramProfile, InstagramPost, InstagramMedia
from instagram_manager.instagram_api.data_handlers.media_handler import MediaHandler
from core.storage.gcs_service import GCSService


class PerformanceBenchmarkMixin:
    """Mixin for performance measurement utilities."""
    
    def measure_time(self, func, *args, **kwargs):
        """Measure execution time of a function."""
        gc.collect()  # Clear garbage before measurement
        start_time = time.perf_counter()
        result = func(*args, **kwargs)
        end_time = time.perf_counter()
        return result, end_time - start_time
    
    def measure_memory(self, func, *args, **kwargs):
        """Measure memory usage of a function."""
        import tracemalloc
        
        gc.collect()
        tracemalloc.start()
        
        result = func(*args, **kwargs)
        
        current, peak = tracemalloc.get_traced_memory()
        tracemalloc.stop()
        
        return result, current, peak
    
    def generate_test_image(self, size_mb: float) -> bytes:
        """Generate test image data of specified size."""
        size_bytes = int(size_mb * 1024 * 1024)
        # Generate simple JPEG-like data
        data = bytearray(size_bytes)
        # Add JPEG header
        data[0:2] = b'\xff\xd8'  # JPEG SOI marker
        data[-2:] = b'\xff\xd9'  # JPEG EOI marker
        return bytes(data)
    
    def generate_test_video(self, size_mb: float) -> bytes:
        """Generate test video data of specified size."""
        size_bytes = int(size_mb * 1024 * 1024)
        # Generate simple MP4-like data
        data = bytearray(size_bytes)
        # Add MP4 header (simplified)
        data[4:8] = b'ftyp'
        return bytes(data)


class TestGCSUploadPerformance(TestCase, PerformanceBenchmarkMixin):
    """Test upload performance for GCS vs local storage."""
    
    def setUp(self):
        """Set up test data."""
        self.profile = InstagramProfile.objects.create(
            profile_id="perf123",
            username="perftest",
            full_name="Performance Test"
        )
        
        self.post = InstagramPost.objects.create(
            external_id="PERFPOST",
            profile=self.profile,
            shortcode="PERF123",
            caption="Performance test post",
            posted_at=timezone.now()
        )
    
    @override_settings(GCS_BUCKET_NAME='test-bucket')
    @patch('core.storage.gcs_service.storage.Client')
    def test_single_file_upload_performance(self, mock_storage_client):
        """Compare single file upload performance."""
        # Mock GCS client
        mock_client = Mock()
        mock_bucket = Mock()
        mock_blob = Mock()
        
        mock_storage_client.return_value = mock_client
        mock_client.bucket.return_value = mock_bucket
        mock_bucket.blob.return_value = mock_blob
        
        # Simulate network latency (50ms)
        def upload_with_delay(content, content_type):
            time.sleep(0.05)  # 50ms network latency
        
        mock_blob.upload_from_string.side_effect = upload_with_delay
        mock_blob.public_url = "https://storage.googleapis.com/test-bucket/test.jpg"
        
        # Test different file sizes
        file_sizes = [0.5, 1, 5, 10]  # MB
        results = []
        
        for size_mb in file_sizes:
            # Generate test data
            test_data = self.generate_test_image(size_mb)
            
            # Test local storage
            media_local = InstagramMedia.objects.create(
                external_id=f"LOCAL_{size_mb}",
                post=self.post,
                media_type="photo",
                media_url="https://instagram.com/photo.jpg"
            )
            
            # Measure local save
            with patch('django.core.files.storage.default_storage.save') as mock_save:
                mock_save.return_value = f"media/test_{size_mb}.jpg"
                
                handler_local = MediaHandler(save_to_gcs=False)
                _, local_time = self.measure_time(
                    handler_local._save_locally,
                    test_data,
                    f"test_{size_mb}.jpg",
                    media_local
                )
            
            # Test GCS upload
            InstagramMedia.objects.create(
                external_id=f"GCS_{size_mb}",
                post=self.post,
                media_type="photo",
                media_url="https://instagram.com/photo.jpg"
            )
            
            gcs_service = GCSService()
            _, gcs_time = self.measure_time(
                gcs_service.upload_file,
                test_data,
                f"test_{size_mb}.jpg",
                "image/jpeg",
                "test"
            )
            
            results.append({
                "size_mb": size_mb,
                "local_time": local_time,
                "gcs_time": gcs_time,
                "gcs_overhead": gcs_time - local_time,
                "overhead_percent": ((gcs_time - local_time) / local_time * 100) if local_time > 0.001 else 0
            })
            
            # Print results for manual inspection
            print(f"\nFile size: {size_mb} MB")
            print(f"Local storage: {local_time:.4f}s")
            print(f"GCS upload: {gcs_time:.4f}s")
            print(f"Overhead: {gcs_time - local_time:.4f}s ({results[-1]['overhead_percent']:.1f}%)")
        
        # Performance assertions
        for result in results:
            # GCS will have network latency overhead
            # We expect ~50ms network latency per upload
            self.assertLess(result["gcs_time"], 0.5)  # Max 500ms per upload
            
            # For larger files (>5MB), throughput matters more than latency
            if result["size_mb"] >= 5:
                throughput = result["size_mb"] / result["gcs_time"]
                self.assertGreater(throughput, 10)  # At least 10 MB/s
    
    @override_settings(GCS_BUCKET_NAME='test-bucket')
    @patch('core.storage.gcs_service.storage.Client')
    def test_batch_upload_performance(self, mock_storage_client):
        """Test performance of batch uploads."""
        # Mock GCS
        mock_client = Mock()
        mock_bucket = Mock()
        mock_storage_client.return_value = mock_client
        mock_client.bucket.return_value = mock_bucket
        
        def create_mock_blob():
            mock_blob = Mock()
            mock_blob.upload_from_string = Mock()
            mock_blob.make_public = Mock()
            mock_blob.public_url = "https://storage.googleapis.com/test-bucket/test.jpg"
            return mock_blob
        
        mock_bucket.blob.side_effect = lambda name: create_mock_blob()
        
        # Create batch of media files
        batch_sizes = [10, 50, 100]
        
        for batch_size in batch_sizes:
            # Create media objects
            media_items = []
            for i in range(batch_size):
                media = InstagramMedia.objects.create(
                    external_id=f"BATCH_{batch_size}_{i}",
                    post=self.post,
                    media_type="photo",
                    media_url=f"https://instagram.com/batch_{i}.jpg"
                )
                media_items.append(media)
            
            # Generate test data (0.5MB per image)
            test_data = self.generate_test_image(0.5)
            
            # Test sequential upload
            handler = MediaHandler(save_to_gcs=True)
            handler._gcs_service = GCSService()
            
            start_time = time.perf_counter()
            
            for media in media_items:
                with patch('requests.get') as mock_get:
                    mock_response = Mock()
                    mock_response.status_code = 200
                    mock_response.content = test_data
                    mock_response.headers = {'content-type': 'image/jpeg'}
                    mock_get.return_value = mock_response
                    
                    # Simulate network I/O delay
                    time.sleep(0.01)  # 10ms per upload
                    handler.download_media(media)
            
            sequential_time = time.perf_counter() - start_time
            
            # Test concurrent upload (simulate)
            start_time = time.perf_counter()
            
            def upload_media(media):
                with patch('requests.get') as mock_get:
                    mock_response = Mock()
                    mock_response.status_code = 200
                    mock_response.content = test_data
                    mock_response.headers = {'content-type': 'image/jpeg'}
                    mock_get.return_value = mock_response
                    
                    # Simulate network I/O delay
                    time.sleep(0.01)  # 10ms per upload
                    handler.download_media(media)
            
            # Simulate concurrent uploads with ThreadPoolExecutor
            with ThreadPoolExecutor(max_workers=5) as executor:
                futures = [executor.submit(upload_media, media) for media in media_items]
                for future in as_completed(futures):
                    future.result()
            
            concurrent_time = time.perf_counter() - start_time
            
            print(f"\nBatch size: {batch_size}")
            print(f"Sequential upload: {sequential_time:.2f}s")
            print(f"Concurrent upload: {concurrent_time:.2f}s")
            print(f"Speedup: {sequential_time/concurrent_time:.2f}x")
            
            # Concurrent should be faster for larger batches
            if batch_size >= 50:
                self.assertLess(concurrent_time, sequential_time)
    
    @override_settings(GCS_BUCKET_NAME='test-bucket')
    @patch('core.storage.gcs_service.storage.Client')
    def test_memory_usage_comparison(self, mock_storage_client):
        """Compare memory usage between local and GCS storage."""
        # Mock GCS
        mock_client = Mock()
        mock_bucket = Mock()
        mock_blob = Mock()
        
        mock_storage_client.return_value = mock_client
        mock_client.bucket.return_value = mock_bucket
        mock_bucket.blob.return_value = mock_blob
        mock_blob.public_url = "https://storage.googleapis.com/test-bucket/test.jpg"
        
        # Test with large file (20MB)
        large_file = self.generate_test_image(20)
        
        # Create media
        media = InstagramMedia.objects.create(
            external_id="MEMTEST",
            post=self.post,
            media_type="photo",
            media_url="https://instagram.com/large.jpg"
        )
        
        # Test local storage memory usage
        handler_local = MediaHandler(save_to_gcs=False)
        
        with patch('django.core.files.storage.default_storage.save') as mock_save:
            mock_save.return_value = "media/large.jpg"
            
            _, local_current, local_peak = self.measure_memory(
                handler_local._save_locally,
                large_file,
                "large.jpg",
                media
            )
        
        # Test GCS memory usage
        gcs_service = GCSService()
        _, gcs_current, gcs_peak = self.measure_memory(
            gcs_service.upload_file,
            large_file,
            "large.jpg",
            "image/jpeg",
            "test"
        )
        
        print("\nMemory usage for 20MB file:")
        print(f"Local storage - Current: {local_current/1024/1024:.2f}MB, Peak: {local_peak/1024/1024:.2f}MB")
        print(f"GCS upload - Current: {gcs_current/1024/1024:.2f}MB, Peak: {gcs_peak/1024/1024:.2f}MB")
        
        # GCS shouldn't use significantly more memory
        self.assertLess(gcs_peak, local_peak * 2)  # Max 2x memory usage
    
    @override_settings(GCS_BUCKET_NAME='test-bucket')
    @patch('core.storage.gcs_service.storage.Client')
    def test_different_media_types_performance(self, mock_storage_client):
        """Test performance with different media types."""
        # Mock GCS
        mock_client = Mock()
        mock_bucket = Mock()
        mock_blob = Mock()
        
        mock_storage_client.return_value = mock_client
        mock_client.bucket.return_value = mock_bucket
        mock_bucket.blob.return_value = mock_blob
        mock_blob.public_url = "https://storage.googleapis.com/test-bucket/test.jpg"
        
        # Test cases: (type, size_mb, content_type)
        test_cases = [
            ("photo_small", 0.5, "image/jpeg"),
            ("photo_large", 5, "image/jpeg"),
            ("video_small", 10, "video/mp4"),
            ("video_large", 50, "video/mp4"),
        ]
        
        results = []
        
        for media_type, size_mb, content_type in test_cases:
            # Generate test data
            if "photo" in media_type:
                test_data = self.generate_test_image(size_mb)
            else:
                test_data = self.generate_test_video(size_mb)
            
            # Create media
            InstagramMedia.objects.create(
                external_id=f"TYPE_{media_type}",
                post=self.post,
                media_type="photo" if "photo" in media_type else "video",
                media_url=f"https://instagram.com/{media_type}.jpg"
            )
            
            # Measure GCS upload
            gcs_service = GCSService()
            _, upload_time = self.measure_time(
                gcs_service.upload_file,
                test_data,
                f"{media_type}.jpg",
                content_type,
                "test"
            )
            
            # Calculate throughput (MB/s)
            throughput = size_mb / upload_time if upload_time > 0 else 0
            
            results.append({
                "type": media_type,
                "size_mb": size_mb,
                "upload_time": upload_time,
                "throughput_mbps": throughput
            })
            
            print(f"\n{media_type} ({size_mb}MB):")
            print(f"Upload time: {upload_time:.2f}s")
            print(f"Throughput: {throughput:.2f} MB/s")
        
        # Performance expectations
        for result in results:
            # Minimum throughput should be reasonable
            if result["size_mb"] >= 1:
                self.assertGreater(result["throughput_mbps"], 0.1)  # Min 0.1 MB/s


class TestGCSFailureRecovery(TestCase, PerformanceBenchmarkMixin):
    """Test performance impact of failure scenarios."""
    
    def setUp(self):
        """Set up test data."""
        self.profile = InstagramProfile.objects.create(
            profile_id="fail123",
            username="failtest",
            full_name="Failure Test"
        )
        
        self.post = InstagramPost.objects.create(
            external_id="FAILPOST",
            profile=self.profile,
            shortcode="FAIL123",
            posted_at=timezone.now()
        )
    
    @override_settings(GCS_BUCKET_NAME='test-bucket')
    @patch('core.storage.gcs_service.storage.Client')
    def test_retry_performance_impact(self, mock_storage_client):
        """Test performance impact of retries."""
        # Mock GCS with failures
        mock_client = Mock()
        mock_bucket = Mock()
        mock_blob = Mock()
        
        mock_storage_client.return_value = mock_client
        mock_client.bucket.return_value = mock_bucket
        mock_bucket.blob.return_value = mock_blob
        
        # Simulate transient failures
        attempt_count = 0
        
        def upload_with_failures(content, content_type):
            nonlocal attempt_count
            attempt_count += 1
            if attempt_count < 3:  # Fail first 2 attempts
                raise Exception("Network error")
            time.sleep(0.05)  # Successful upload
        
        mock_blob.upload_from_string.side_effect = upload_with_failures
        mock_blob.public_url = "https://storage.googleapis.com/test-bucket/test.jpg"
        
        # Test data
        test_data = self.generate_test_image(1)  # 1MB
        
        # Create media
        media = InstagramMedia.objects.create(
            external_id="RETRY_TEST",
            post=self.post,
            media_type="photo",
            media_url="https://instagram.com/retry.jpg"
        )
        
        # Test with retry logic
        handler = MediaHandler(save_to_gcs=True)
        handler._gcs_service = GCSService()
        
        start_time = time.perf_counter()
        
        # Simulate retry logic
        max_retries = 3
        for attempt in range(max_retries):
            try:
                handler._try_gcs_upload(
                    test_data,
                    "retry.jpg",
                    "image/jpeg",
                    media
                )
                break
            except Exception:
                if attempt == max_retries - 1:
                    raise
                time.sleep(0.1 * (attempt + 1))  # Exponential backoff
        
        total_time = time.perf_counter() - start_time
        
        print("\nRetry performance:")
        print(f"Total attempts: {attempt_count}")
        print(f"Total time with retries: {total_time:.2f}s")
        print(f"Average time per attempt: {total_time/attempt_count:.2f}s")
        
        # Should complete within reasonable time even with retries
        self.assertLess(total_time, 5)  # Max 5 seconds with retries
    
    @override_settings(GCS_BUCKET_NAME='test-bucket')
    def test_fallback_to_local_performance(self):
        """Test performance of fallback to local storage."""
        # Create media
        media = InstagramMedia.objects.create(
            external_id="FALLBACK_TEST",
            post=self.post,
            media_type="photo",
            media_url="https://instagram.com/fallback.jpg"
        )
        
        # Test data
        test_data = self.generate_test_image(2)  # 2MB
        
        # Mock failed GCS but successful local storage
        handler = MediaHandler(save_to_gcs=True)
        
        # Mock GCS service to fail
        mock_gcs = Mock()
        mock_gcs.upload_file.side_effect = Exception("GCS unavailable")
        handler._gcs_service = mock_gcs
        
        # Mock successful local save
        with patch('django.core.files.storage.default_storage.save') as mock_save:
            mock_save.return_value = "media/fallback.jpg"
            
            with patch('requests.get') as mock_get:
                mock_response = Mock()
                mock_response.status_code = 200
                mock_response.content = test_data
                mock_response.headers = {'content-type': 'image/jpeg'}
                mock_get.return_value = mock_response
                
                # Mock _save_locally to simulate successful local save
                def mock_save_locally(content, filename, media_obj):
                    # Simulate what _save_locally does
                    media_obj.is_downloaded = True
                    media_obj.file_size = len(content)
                    media_obj.save()
                    return True
                
                with patch.object(handler, '_save_locally', side_effect=mock_save_locally):
                    # Measure fallback performance
                    start_time = time.perf_counter()
                    result = handler.download_media(media)
                    fallback_time = time.perf_counter() - start_time
        
        print("\nFallback to local storage:")
        print(f"Total time (including GCS attempt): {fallback_time:.2f}s")
        
        # Should still complete successfully
        self.assertTrue(result)
        
        # Reload media from database to get updated values
        media.refresh_from_db()
        self.assertTrue(media.is_downloaded)
        self.assertIsNone(media.gcs_url)  # GCS failed
        
        # Fallback should be reasonably fast
        self.assertLess(fallback_time, 2)  # Max 2 seconds


class TestGCSScalability(TestCase, PerformanceBenchmarkMixin):
    """Test GCS scalability with various load patterns."""
    
    @override_settings(GCS_BUCKET_NAME='test-bucket')
    @patch('core.storage.gcs_service.storage.Client')
    def test_concurrent_upload_scalability(self, mock_storage_client):
        """Test how GCS handles concurrent uploads."""
        # Mock GCS
        mock_client = Mock()
        mock_bucket = Mock()
        mock_storage_client.return_value = mock_client
        mock_client.bucket.return_value = mock_bucket
        
        # Track concurrent uploads
        concurrent_uploads = 0
        max_concurrent = 0
        
        def create_mock_blob():
            nonlocal concurrent_uploads, max_concurrent
            
            mock_blob = Mock()
            
            def upload_with_tracking(content, content_type):
                nonlocal concurrent_uploads, max_concurrent
                concurrent_uploads += 1
                max_concurrent = max(max_concurrent, concurrent_uploads)
                time.sleep(0.1)  # Simulate upload time
                concurrent_uploads -= 1
            
            mock_blob.upload_from_string = upload_with_tracking
            mock_blob.make_public = Mock()
            mock_blob.public_url = "https://storage.googleapis.com/test-bucket/test.jpg"
            return mock_blob
        
        mock_bucket.blob.side_effect = lambda name: create_mock_blob()
        
        # Test different concurrency levels
        concurrency_levels = [1, 5, 10, 20]
        results = []
        
        for num_workers in concurrency_levels:
            # Reset counters
            concurrent_uploads = 0
            max_concurrent = 0
            
            # Create test data
            num_files = 50
            test_data = self.generate_test_image(0.5)  # 0.5MB each
            
            # Create GCS service
            gcs_service = GCSService()
            
            # Measure upload time
            start_time = time.perf_counter()
            
            def upload_file(i):
                return gcs_service.upload_file(
                    test_data,
                    f"concurrent_{i}.jpg",
                    "image/jpeg",
                    "test"
                )
            
            with ThreadPoolExecutor(max_workers=num_workers) as executor:
                futures = [executor.submit(upload_file, i) for i in range(num_files)]
                for future in as_completed(futures):
                    future.result()
            
            total_time = time.perf_counter() - start_time
            
            results.append({
                "workers": num_workers,
                "total_time": total_time,
                "files_per_second": num_files / total_time,
                "max_concurrent": max_concurrent
            })
            
            print(f"\nConcurrency level: {num_workers}")
            print(f"Total time: {total_time:.2f}s")
            print(f"Throughput: {num_files / total_time:.2f} files/s")
            print(f"Max concurrent uploads: {max_concurrent}")
        
        # Verify scalability
        # Higher concurrency should improve throughput
        for i in range(1, len(results)):
            if results[i]["workers"] <= 10:  # Reasonable concurrency
                self.assertGreater(
                    results[i]["files_per_second"],
                    results[i-1]["files_per_second"] * 0.8  # At least 80% improvement
                )