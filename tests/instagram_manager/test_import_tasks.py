"""Tests for Instagram import tasks."""

from unittest.mock import Mock, patch
from django.test import TestCase
from django.utils import timezone

from instagram_manager.tasks.import_tasks import (
    ImportBatchPostsTask,
    ImportInstagramProfileTask,
    ImportInstagramPostsTask,
    ImportInstagramCommentsTask,
)
from instagram_manager.models import InstagramProfile, InstagramPost, InstagramComment
from core.tasks.exceptions import TaskValidationError


class TestImportBatchPostsTask(TestCase):
    """Test ImportBatchPostsTask."""
    
    def setUp(self):
        """Set up test data."""
        self.task = ImportBatchPostsTask()
        
        # Create test profiles
        self.profile1 = InstagramProfile.objects.create(
            username="testuser1",
            full_name="Test User 1",
            profile_id="123456"
        )
        self.profile2 = InstagramProfile.objects.create(
            username="testuser2",
            full_name="Test User 2",
            profile_id="789012"
        )
        
    def test_validate_params_success(self):
        """Test successful parameter validation."""
        params = self.task.validate_params(
            usernames=["testuser1", "@testuser2"],
            limit=10,
            skip_media_download=True
        )
        
        self.assertEqual(params["usernames"], ["testuser1", "testuser2"])
        self.assertEqual(params["limit"], 10)
        self.assertTrue(params["skip_media_download"])
        
    def test_validate_params_empty_usernames(self):
        """Test validation with empty usernames."""
        with self.assertRaises(TaskValidationError) as cm:
            self.task.validate_params(usernames=[])
            
        self.assertIn("At least one username is required", str(cm.exception))
        
    @patch("instagram_manager.services.batch_post_service.BatchPostService")
    @patch("instagram_manager.services.batch_profile_service.BatchProfileService")
    def test_execute_task_success(self, mock_profile_service_class, mock_post_service_class):
        """Test successful task execution."""
        # Mock profile service
        mock_profile_service = Mock()
        mock_profile_service.validate_and_create_profiles.return_value = (
            [self.profile1, self.profile2], []
        )
        mock_profile_service_class.return_value = mock_profile_service
        
        # Mock post service
        mock_post_service = Mock()
        mock_post_service.import_posts_batch.return_value = {
            "testuser1": {
                "success": True,
                "post_count": 1,
                "total_found": 1,
                "posts": []
            },
            "testuser2": {
                "success": True,
                "post_count": 1,
                "total_found": 1,
                "posts": []
            },
            "_statistics": {
                "total_posts": 2
            }
        }
        mock_post_service_class.return_value = mock_post_service
        
        # Execute
        result = self.task.execute_task(
            usernames=["testuser1", "testuser2"],
            batch_size=10,
            limit=10
        )
        
        # Verify
        self.assertEqual(result["status"], "success")
        self.assertEqual(result["profiles_processed"], 2)
        self.assertEqual(result["total_posts"], 2)
        self.assertEqual(len(result["profiles_failed"]), 0)
        
    @patch("instagram_manager.services.batch_profile_service.BatchProfileService")
    def test_execute_task_api_error(self, mock_profile_service_class):
        """Test task execution with API error."""
        # Mock profile service to raise error
        mock_profile_service = Mock()
        mock_profile_service.validate_and_create_profiles.side_effect = Exception("API Error")
        mock_profile_service_class.return_value = mock_profile_service
        
        # Execute - should not raise but return error result
        result = self.task.execute_task(usernames=["testuser1"], batch_size=10)
        
        # Verify error result
        self.assertEqual(result["status"], "failed")
        self.assertIn("API Error", str(result["errors"]))
        

class TestImportInstagramProfileTask(TestCase):
    """Test ImportInstagramProfileTask."""
    
    @patch("instagram_manager.tasks.import_tasks.BrightDataClient")
    def setUp(self, mock_client_class):
        """Set up test data."""
        self.mock_client_class = mock_client_class
        self.mock_client = Mock()
        self.mock_client_class.return_value = self.mock_client
        self.task = ImportInstagramProfileTask()
        
    def test_validate_params_success(self):
        """Test successful parameter validation."""
        params = self.task.validate_params(
            username="testuser",
            import_posts=True,
            posts_limit=20
        )
        
        self.assertEqual(params["username"], "testuser")
        self.assertTrue(params["import_posts"])
        self.assertEqual(params["posts_limit"], 20)
        
    def test_validate_params_empty_username(self):
        """Test validation with empty username."""
        with self.assertRaises(TaskValidationError) as cm:
            self.task.validate_params(username="")
            
        self.assertIn("Username is required", str(cm.exception))
        
    def test_execute_task_success(self):
        """Test successful profile import."""
        # Setup mock client return values
        self.mock_client.get_profile.return_value = {
            "username": "testuser",
            "follower_count": 1000,
            "following_count": 500,
            "post_count": 100,
            "full_name": "Test User",
            "bio": "Test bio",
            "profile_pic_url": "https://example.com/pic.jpg",
            "is_private": False,
            "is_verified": True,
            "profile_id": "123456"
        }
        
        # Mock ProfileHandler
        with patch("instagram_manager.instagram_api.data_handlers.profile_handler.ProfileHandler.process_profile_data") as mock_handler:
            # Create mock profile (but don't save to DB so task thinks it's new)
            mock_profile = Mock(spec=InstagramProfile)
            mock_profile.id = 123
            mock_profile.username = "testuser"
            mock_profile.profile_id = "123456"
            mock_profile.follower_count = 1000
            mock_profile.post_count = 50
            mock_handler.return_value = mock_profile
            
            # Execute
            result = self.task.execute_task(
                username="testuser",
                import_posts=False
            )
            
            # Verify
            self.assertEqual(result["status"], "success")
            self.assertEqual(result["username"], "testuser")
            self.assertEqual(result["profile_id"], mock_profile.id)
            
            self.mock_client.get_profile.assert_called_once_with("testuser")
        
    @patch("instagram_manager.instagram_api.data_handlers.profile_handler.ProfileHandler.process_profile_data")
    @patch("instagram_manager.instagram_api.data_handlers.post_handler.PostHandler.process_post_data")
    def test_execute_task_with_posts(self, mock_process_post, mock_process_profile):
        """Test profile import with posts."""
        # Setup mock client return values
        self.mock_client.get_profile.return_value = {
            "username": "testuser",
            "profile_id": "123456"
        }
        self.mock_client.get_posts.return_value = [
            {"post_id": "1", "shortcode": "ABC1"},
            {"post_id": "2", "shortcode": "ABC2"}
        ]
        
        # Mock profile handler
        mock_profile = Mock(spec=InstagramProfile)
        mock_profile.username = "testuser"
        mock_profile.id = 1
        mock_process_profile.return_value = mock_profile
        
        # Mock post handler
        mock_post = Mock(spec=InstagramPost)
        mock_process_post.return_value = mock_post
        
        # Execute
        result = self.task.execute_task(
            username="testuser",
            import_posts=True,
            posts_limit=20
        )
        
        # Verify
        self.assertEqual(result["status"], "success")
        self.assertEqual(result["posts_imported"], 2)
        
        self.mock_client.get_profile.assert_called_once_with("testuser")
        self.mock_client.get_posts.assert_called_once_with("testuser", limit=20)
        

class TestImportInstagramPostsTask(TestCase):
    """Test ImportInstagramPostsTask."""
    
    def setUp(self):
        """Set up test data."""
        # Create test profile
        self.profile = InstagramProfile.objects.create(
            username="testuser",
            full_name="Test User",
            profile_id="123456"
        )
        
        # Create task without mocking since it doesn't create BrightDataClient in __init__
        self.task = ImportInstagramPostsTask()
        
    def test_validate_params_success(self):
        """Test successful parameter validation."""
        params = self.task.validate_params(
            profile_id=self.profile.id,
            limit=50,
            import_comments=True
        )
        
        self.assertEqual(params["profile_id"], self.profile.id)
        self.assertEqual(params["limit"], 50)
        self.assertTrue(params["import_comments"])
        
    def test_validate_params_with_nonexistent_profile(self):
        """Test validation with non-existent profile ID."""
        # validate_params doesn't check if profile exists, execute_task does
        params = self.task.validate_params(profile_id=9999)
        self.assertEqual(params["profile_id"], 9999)
        self.assertEqual(params["limit"], 50)
        
    @patch("instagram_manager.tasks.import_tasks.BrightDataClient")
    @patch("instagram_manager.instagram_api.data_handlers.post_handler.PostHandler.process_post_data")
    def test_execute_task_success(self, mock_process_post, mock_client_class):
        """Test successful posts import."""
        # Mock client
        mock_client = Mock()
        mock_client.get_posts.return_value = [
            {"post_id": "1", "shortcode": "ABC1"},
            {"post_id": "2", "shortcode": "ABC2"}
        ]
        mock_client_class.return_value = mock_client
        
        # Mock post handler
        mock_post = Mock(spec=InstagramPost)
        mock_post.comment_count = 0
        mock_post.media = Mock()
        mock_post.media.exists.return_value = False
        mock_process_post.return_value = mock_post
        
        # Execute
        result = self.task.execute_task(
            profile_id=self.profile.id,
            limit=50
        )
        
        # Verify
        self.assertEqual(result["status"], "success")
        self.assertEqual(result["posts_imported"], 2)
        self.assertEqual(result["profile_id"], self.profile.id)
        
        mock_client.get_posts.assert_called_once()
        

class TestImportInstagramCommentsTask(TestCase):
    """Test ImportInstagramCommentsTask."""
    
    def setUp(self):
        """Set up test data."""
        self.task = ImportInstagramCommentsTask()
        
        # Create test data
        self.profile = InstagramProfile.objects.create(
            username="testuser",
            profile_id="123456"
        )
        self.post = InstagramPost.objects.create(
            profile=self.profile,
            shortcode="ABC123",
            caption="Test post",
            posted_at=timezone.now()
        )
        
    def test_validate_params_with_post_ids(self):
        """Test validation with post_ids."""
        params = self.task.validate_params(
            post_ids=[self.post.id],
            import_replies=True
        )
        
        self.assertEqual(params["post_ids"], [self.post.id])
        self.assertTrue(params["import_replies"])
        
    def test_validate_params_empty_post_ids(self):
        """Test validation with empty post_ids."""
        with self.assertRaises(TaskValidationError) as cm:
            self.task.validate_params(post_ids=[])
            
        self.assertIn("Post IDs are required", str(cm.exception))
        
    def test_validate_params_no_post_ids(self):
        """Test validation without post_ids."""
        with self.assertRaises(TaskValidationError) as cm:
            self.task.validate_params()
            
        self.assertIn("Post IDs are required", str(cm.exception))
        
    @patch("instagram_manager.services.comment_service.CommentService.import_comments_for_post")
    def test_execute_task_success(self, mock_import):
        """Test successful comments import."""
        # Create mock comments
        mock_comment = Mock(spec=InstagramComment)
        mock_comment.parent_comment_id = None
        mock_reply = Mock(spec=InstagramComment)
        mock_reply.parent_comment_id = 123
        
        # Mock import result - returns list of comments
        mock_import.return_value = [mock_comment, mock_reply]
        
        # Execute
        result = self.task.execute_task(
            post_ids=[self.post.id],
            import_replies=True
        )
        
        # Verify
        self.assertEqual(result["status"], "success")
        self.assertEqual(result["posts_processed"], 1)
        self.assertEqual(result["comments_imported"], 2)
        self.assertEqual(result["replies_imported"], 1)
        
        mock_import.assert_called_once()