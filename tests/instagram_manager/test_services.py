"""
Tests for Instagram services.
"""

from datetime import datetime, timezone as dt_timezone
from django.test import TestCase
from django.utils import timezone
import pytest
from django.conf import settings
from unittest.mock import Mock, patch

from instagram_manager.models import (
    InstagramProfile,
    InstagramPost,
    InstagramMedia,
)
from instagram_manager.services import ProfileService, PostService, MediaService


class ProfileServiceTest(TestCase):
    """Tests for ProfileService"""

    def setUp(self):
        self.service = ProfileService()

        # Create test profile
        self.profile = InstagramProfile.objects.create(
            profile_id="123456789",
            username=settings.INSTAGRAM_TEST_USERNAME,
            full_name="Test User",
            follower_count=1000,
        )

    @patch("instagram_manager.services.profile_service.ProfileHandler")
    def test_import_profile(self, mock_handler):
        """Test importing profile from API"""
        # Mock the client on the service instance
        mock_client = Mock()
        self.service.client = mock_client

        # Mock API response
        mock_api_data = {
            "id": "*********",
            "username": "apiuser",
            "full_name": "API User",
            "biography": "Test bio",
            "follower_count": 5000,
            "following_count": 300,
            "media_count": 50,
            "is_verified": True,
            "is_business_account": True,
            "is_private": False,
            "profile_pic_url": "https://example.com/pic.jpg",
            "profile_pic_url_hd": "https://example.com/pic_hd.jpg",
            "external_url": "https://example.com",
            "category_name": "Creator",
        }

        mock_client.get_profile.return_value = mock_api_data

        # Mock ProfileHandler
        mock_handler.validate_profile_data.return_value = True
        mock_profile = InstagramProfile(
            profile_id="*********",
            username="apiuser",
            full_name="API User",
            bio="Test bio",
            follower_count=5000,
            following_count=300,
            post_count=50,
            is_verified=True,
            is_business=True,
            is_private=False,
        )
        mock_handler.process_profile_data.return_value = mock_profile

        profile = self.service.import_profile("apiuser")

        assert profile.profile_id == "*********"
        assert profile.username == "apiuser"
        assert profile.bio == "Test bio"
        assert profile.follower_count == 5000
        assert profile.is_verified
        assert profile.is_business

        # Verify API was called
        mock_client.get_profile.assert_called_once_with("apiuser")

    def test_get_profile_stats(self):
        """Test getting profile statistics"""
        # Create some posts for the profile
        for i in range(5):
            InstagramPost.objects.create(
                external_id=f"POST{i}",
                profile=self.profile,
                shortcode=f"CODE{i}",
                post_type="photo",
                like_count=100 * (i + 1),
                comment_count=10 * (i + 1),
                posted_at=timezone.now(),
            )

        stats = self.service.get_profile_stats(self.profile)

        assert stats["total_posts"] == 5
        assert stats["total_likes"] == 1500  # 100+200+300+400+500
        assert stats["total_comments"] == 150  # 10+20+30+40+50
        assert stats["avg_likes_per_post"] == 300
        assert stats["avg_comments_per_post"] == 30

        # Check engagement rate calculation
        assert "engagement_rate" in stats
        expected_rate = (
            (300 + 30) / 1000
        ) * 100  # (avg_likes + avg_comments) / followers * 100
        assert stats["engagement_rate"] == pytest.approx(expected_rate, abs=10**-2)

    def test_search_profiles(self):
        """Test searching profiles"""
        # Create additional profiles
        InstagramProfile.objects.create(
            profile_id="999999999", username="searchuser", full_name="Search Test User"
        )

        # Search by username
        results = self.service.search_profiles("search")
        assert len(results) == 1
        assert results[0].username == "searchuser"

        # Search by full name
        results = self.service.search_profiles("Test User")
        self.assertGreaterEqual(
            len(results), 1
        )  # Should find at least the test profile


class PostServiceTest(TestCase):
    """Tests for PostService"""

    def setUp(self):
        self.service = PostService()

        # Create test profile
        self.profile = InstagramProfile.objects.create(
            profile_id="123456789", username=settings.INSTAGRAM_TEST_USERNAME, follower_count=1000
        )

    @patch("instagram_manager.services.post_service.settings")
    @patch("instagram_manager.services.post_service.InstagramScrapingTask")
    def test_import_posts_for_profile(self, mock_task_class, mock_settings):
        """Test importing posts for profile"""
        # Mock settings
        mock_settings.BRIGHTDATA_DATASETS = {"instagram_posts": "test_dataset"}

        # Mock task creation
        mock_task = Mock()
        mock_task_class.objects.create.return_value = mock_task

        # Mock the client on the service instance
        mock_client = Mock()
        self.service.client = mock_client

        # Mock API response
        mock_posts_data = [
            {
                "id": "POST1",
                "shortcode": "DKZ7A0XpB1U",
                "post_type": "photo",
                "caption": "Post 1 #test",
                "like_count": 100,
                "comment_count": 10,
                "timestamp": datetime.now(dt_timezone.utc).isoformat(),
                "media": [
                    {
                        "id": "MEDIA1",
                        "type": "photo",
                        "url": "https://example.com/1.jpg",
                        "width": 1080,
                        "height": 1080,
                    }
                ],
            },
            {
                "id": "POST2",
                "shortcode": "DKVPW5_T6dZ",
                "post_type": "video",
                "caption": "Post 2",
                "like_count": 200,
                "comment_count": 20,
                "timestamp": datetime.now(dt_timezone.utc).isoformat(),
                "video_view_count": 1000,
                "media": [
                    {
                        "id": "MEDIA2",
                        "type": "video",
                        "url": "https://example.com/2.mp4",
                        "thumbnail_url": "https://example.com/2_thumb.jpg",
                        "width": 1920,
                        "height": 1080,
                        "duration": 30,
                    }
                ],
            },
        ]

        mock_client.get_posts.return_value = mock_posts_data

        # Mock PostHandler
        with patch(
            "instagram_manager.services.post_service.PostHandler"
        ) as mock_handler_class:
            # Create mock instance
            mock_handler_instance = mock_handler_class.return_value
            
            mock_posts = []
            for i, post_data in enumerate(mock_posts_data):
                post = InstagramPost(
                    external_id=post_data["id"],
                    profile=self.profile,
                    shortcode=post_data["shortcode"],
                    post_type=post_data["post_type"],
                    caption=post_data["caption"],
                    like_count=post_data["like_count"],
                    comment_count=post_data["comment_count"],
                    posted_at=timezone.now(),
                )
                mock_posts.append(post)

            # Mock the instance method
            mock_handler_instance.process_post_data.side_effect = mock_posts

            posts = self.service.import_posts_for_profile(self.profile, limit=10)

            assert len(posts) == 2
            assert posts[0].external_id == "POST1"
            assert posts[1].external_id == "POST2"

            # Verify API was called
            mock_client.get_posts.assert_called_once_with(
                settings.INSTAGRAM_TEST_USERNAME, start_date="", end_date="", post_types=None
            )




class MediaServiceTest(TestCase):
    """Tests for MediaService"""

    def setUp(self):
        self.service = MediaService()

        # Create test data
        profile = InstagramProfile.objects.create(
            profile_id="123456789", username="testuser"
        )

        self.post = InstagramPost.objects.create(
            external_id="POST123",
            profile=profile,
            shortcode="DKZ7A0XpB1U",
            post_type="carousel",
            posted_at=timezone.now(),
        )

        self.media = InstagramMedia.objects.create(
            external_id="MEDIA123",
            post=self.post,
            media_type="photo",
            media_url="https://example.com/photo.jpg",
            width=1080,
            height=1080,
            is_downloaded=False,
        )

    def test_download_media_for_post(self):
        """Test downloading media for a post"""
        # Mock the existing MediaHandler instance
        with patch.object(self.service, '_media_handler') as mock_handler:
            # Mock successful download
            mock_handler.download_media.return_value = True

            stats = self.service.download_media_for_post(self.post)

            assert stats["downloaded"] == 1
            assert stats["failed"] == 0

            # Verify download was attempted
            mock_handler.download_media.assert_called_once_with(self.media)

    def test_download_media_for_profile(self):
        """Test downloading media for a profile"""
        # Create additional media
        InstagramMedia.objects.create(
            external_id="MEDIA2",
            post=self.post,
            media_type="video",
            media_url="https://example.com/video.mp4",
            width=1920,
            height=1080,
            is_downloaded=False,
        )

        # Mock the existing MediaHandler instance
        with patch.object(self.service, '_media_handler') as mock_handler:
            # Mock successful downloads
            mock_handler.download_media.return_value = True
            mock_handler.download_thumbnail.return_value = True

            stats = self.service.download_media_for_profile(
                self.post.profile, download_photos=True, download_videos=True
            )

            assert stats["downloaded"] == 2
            assert stats["failed"] == 0

            # Verify thumbnail download was called for video
            assert mock_handler.download_thumbnail.call_count == 1

    def test_get_media_stats(self):
        """Test getting media statistics"""
        # Create more test media
        InstagramMedia.objects.create(
            external_id="MEDIA2",
            post=self.post,
            media_type="video",
            media_url="https://example.com/video.mp4",
            width=1920,
            height=1080,
            is_downloaded=True,
        )

        InstagramMedia.objects.create(
            external_id="MEDIA3",
            post=self.post,
            media_type="photo",
            media_url="https://example.com/photo2.jpg",
            width=1080,
            height=1080,
            is_downloaded=False,
            download_error="Failed to download",
        )

        stats = self.service.get_media_stats()

        assert stats["total_media"] == 3
        assert stats["downloaded"] == 1
        assert stats["pending"] == 2
        assert stats["photos"] == 2
        assert stats["videos"] == 1
        assert "download_percentage" in stats

    def test_get_failed_downloads(self):
        """Test getting failed downloads"""
        # Add download error to media
        self.media.download_error = "Connection timeout"
        self.media.save()

        failed = self.service.get_failed_downloads()

        assert len(failed) == 1
        assert failed[0].external_id == "MEDIA123"
        assert failed[0].download_error == "Connection timeout"

    def test_retry_failed_downloads(self):
        """Test retrying failed downloads"""
        # Add download error to media
        self.media.download_error = "Connection timeout"
        self.media.save()

        # Mock the existing MediaHandler instance
        with patch.object(self.service, '_media_handler') as mock_handler:
            # Mock successful retry
            mock_handler.download_media.return_value = True

            stats = self.service.retry_failed_downloads()

            assert stats["downloaded"] == 1
            assert stats["failed"] == 0

            # Verify error was cleared
            self.media.refresh_from_db()
            assert self.media.download_error == ""
