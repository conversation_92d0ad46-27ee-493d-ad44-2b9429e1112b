"""Tests for RoastService."""

from unittest.mock import Mock, patch
from django.test import TestCase
from django.utils import timezone

from instagram_manager.models import InstagramProfile, InstagramPost, InstagramMedia


class TestRoastService(TestCase):
    """Test RoastService functionality."""
    
    def setUp(self):
        """Set up test data."""
        # Create test profile
        self.profile = InstagramProfile.objects.create(
            username="testuser",
            full_name="Test User",
            profile_id="123456",
            follower_count=1000,
            post_count=50
        )
        
        # Create test posts with media
        self.posts = []
        for i in range(5):
            post = InstagramPost.objects.create(
                profile=self.profile,
                shortcode=f"ABC{i}",
                caption=f"Test post {i}",
                posted_at=timezone.now(),
                like_count=100 + i*10
            )
            
            # Create photos for post
            for j in range(2):
                InstagramMedia.objects.create(
                    post=post,
                    media_type="image",
                    media_url=f"https://instagram.com/p/{post.shortcode}/media/{j}.jpg",
                    thumbnail_url=f"https://instagram.com/p/{post.shortcode}/thumb/{j}.jpg",
                    width=1080,
                    height=1080
                )
            
            # Create one video (should be ignored)
            InstagramMedia.objects.create(
                post=post,
                media_type="video",
                media_url=f"https://instagram.com/p/{post.shortcode}/video.mp4",
                thumbnail_url=f"https://instagram.com/p/{post.shortcode}/video_thumb.jpg"
            )
            
            self.posts.append(post)
    
    def test_service_initialization(self):
        """Test service can be initialized."""
        # Import will fail until service is implemented
        # This test will be updated when service is created
        try:
            from instagram_manager.services.roast_service import RoastService
            service = RoastService()
            self.assertIsNotNone(service)
        except ImportError:
            # Expected until service is implemented
            pass
    
    @patch("instagram_manager.services.profile_service.ProfileService")
    @patch("instagram_manager.instagram_api.data_handlers.media_handler.MediaHandler")
    def test_process_profile_existing_data(self, mock_media_handler_class, mock_profile_service_class):
        """Test processing profile with existing data."""
        # This test demonstrates expected behavior
        # Will need updates when actual service is implemented
        
        # Mock media handler
        mock_media_handler = Mock()
        mock_media_handler.download_media.return_value = True
        mock_media_handler_class.return_value = mock_media_handler
        
        # Expected result structure
        
        # Test will be implemented when service is created
        pass
    
    @patch("instagram_manager.services.profile_service.ProfileService")
    @patch("instagram_manager.services.batch_post_service.BatchPostService")
    def test_process_profile_import_needed(self, mock_batch_service_class, mock_profile_service_class):
        """Test processing profile that needs to be imported."""
        # Mock profile service to import new profile
        mock_profile_service = Mock()
        mock_profile_service.import_profile.return_value = self.profile
        mock_profile_service_class.return_value = mock_profile_service
        
        # Mock batch post service to import posts
        mock_batch_service = Mock()
        mock_batch_service.import_posts_batch.return_value = {
            "testuser": {
                "success": True,
                "post_count": 5,
                "posts": self.posts
            }
        }
        mock_batch_service_class.return_value = mock_batch_service
        
        # Test will be implemented when service is created
        pass
    
    def test_process_profile_mixed_media_no_import(self):
        """Test that service doesn't import when profile has enough total posts.
        
        This tests the fix for the bug where the service would import posts
        from BrightData even when the profile already had enough posts
        (but not all were photos).
        """
        from instagram_manager.services.roast_service import RoastService
        
        # Create profile with 10 posts: 5 photos, 5 videos
        profile = InstagramProfile.objects.create(
            username="igorkishik",
            full_name="Igor Kishik",
            profile_id="987654",
            follower_count=500,
            post_count=10
        )
        
        # Create 5 posts with photos
        for i in range(5):
            post = InstagramPost.objects.create(
                profile=profile,
                shortcode=f"PHOTO{i}",
                caption=f"Photo post {i}",
                posted_at=timezone.now(),
                like_count=100 + i*10,
                comment_count=10 + i,
                external_id=f"photo_post_{i}"
            )
            InstagramMedia.objects.create(
                post=post,
                media_id=f"photo_{i}",
                media_type="photo",
                media_url=f"https://instagram.com/p/{post.shortcode}/photo.jpg",
                width=1080,
                height=1080,
                order_index=0
            )
        
        # Create 5 posts with videos
        for i in range(5):
            post = InstagramPost.objects.create(
                profile=profile,
                shortcode=f"VIDEO{i}",
                caption=f"Video post {i}",
                posted_at=timezone.now(),
                like_count=200 + i*10,
                comment_count=20 + i,
                external_id=f"video_post_{i}"
            )
            InstagramMedia.objects.create(
                post=post,
                media_id=f"video_{i}",
                media_type="video",
                media_url=f"https://instagram.com/p/{post.shortcode}/video.mp4",
                thumbnail_url=f"https://instagram.com/p/{post.shortcode}/thumb.jpg",
                duration=30,
                width=1080,
                height=1920,
                order_index=0
            )
        
        service = RoastService()
        
        # Mock batch_post_service to ensure it's not called
        with patch.object(service.batch_post_service, 'import_posts_batch') as mock_import:
            # Process profile requesting 10 posts
            result = service.process_profile(
                username="igorkishik",
                post_count=10
            )
            
            # Verify that import_posts_batch was NOT called
            # because we already have 10 posts (even though only 5 are photos)
            mock_import.assert_not_called()
            
            # Verify result contains the 5 photos we have
            self.assertEqual(result["username"], "igorkishik")
            self.assertEqual(result["post_count"], 5)  # Only 5 posts with photos
            self.assertEqual(result["statistics"]["total_posts_analyzed"], 5)
            self.assertEqual(result["statistics"]["photos_found"], 5)
    
    def test_process_profile_needs_import_few_posts(self):
        """Test that service imports when there aren't enough total posts."""
        from instagram_manager.services.roast_service import RoastService
        
        # Create profile with only 3 posts
        profile = InstagramProfile.objects.create(
            username="fewposts",
            full_name="Few Posts User",
            profile_id="333444",
            follower_count=100,
            post_count=3
        )
        
        # Create 3 posts with photos
        for i in range(3):
            post = InstagramPost.objects.create(
                profile=profile,
                shortcode=f"FEW{i}",
                caption=f"Few post {i}",
                posted_at=timezone.now(),
                like_count=50 + i*5,
                comment_count=5 + i,
                external_id=f"few_{i}"
            )
            InstagramMedia.objects.create(
                post=post,
                media_id=f"few_media_{i}",
                media_type="photo",
                media_url=f"https://instagram.com/p/{post.shortcode}/photo.jpg",
                width=1080,
                height=1080,
                order_index=0
            )
        
        service = RoastService()
        
        # Mock batch_post_service
        with patch.object(service.batch_post_service, 'import_posts_batch') as mock_import:
            mock_import.return_value = {"fewposts": {"success": True}}
            
            # Process profile requesting 10 posts
            result = service.process_profile(
                username="fewposts",
                post_count=10
            )
            
            # Verify that import_posts_batch WAS called
            # because we only have 3 posts total (less than requested 10)
            mock_import.assert_called_once()
            call_args = mock_import.call_args
            self.assertEqual(len(call_args[1]["profiles"]), 1)
            self.assertEqual(call_args[1]["profiles"][0].username, "fewposts")
            self.assertEqual(call_args[1]["limit"], 10)
            self.assertTrue(call_args[1]["save_media_to_gcs"])
    
    def test_process_profile_no_photos(self):
        """Test processing profile with no photos."""
        # Create profile with posts but no photos
        profile_no_photos = InstagramProfile.objects.create(
            username="nophotos",
            full_name="No Photos User",
            profile_id="789012"
        )
        
        # Create posts with only videos
        for i in range(3):
            post = InstagramPost.objects.create(
                profile=profile_no_photos,
                shortcode=f"VID{i}",
                caption=f"Video post {i}",
                posted_at=timezone.now()
            )
            
            InstagramMedia.objects.create(
                post=post,
                media_type="video",
                media_url=f"https://instagram.com/p/{post.shortcode}/video.mp4"
            )
        
        # Expected result with no photos
        
        # Test will be implemented when service is created
        pass
    
    @patch("instagram_manager.instagram_api.data_handlers.media_handler.MediaHandler")
    def test_process_profile_gcs_upload_error(self, mock_media_handler_class):
        """Test handling GCS upload errors."""
        # Mock media handler to fail on some uploads
        mock_media_handler = Mock()
        mock_media_handler.download_media.side_effect = [
            True, False, True, False, True, True
        ]
        mock_media_handler_class.return_value = mock_media_handler
        
        # Test will be implemented when service is created
        pass
    
    def test_process_profile_with_progress_callback(self):
        """Test progress callback functionality."""
        progress_updates = []
        
        def mock_progress_callback(current, total, message):
            progress_updates.append({
                "current": current,
                "total": total,
                "message": message
            })
        
        # Test will be implemented when service is created
        # Expected progress updates:
        # 1. "Checking profile"
        # 2. "Fetching posts"
        # 3. "Processing post X/Y"
        # 4. "Uploading to GCS"
        # 5. "Complete"
        pass
    
    @patch("instagram_manager.instagram_api.data_handlers.media_handler.MediaHandler")
    def test_update_posts_with_gcs_urls(self, mock_media_handler_class):
        """Test updating posts with GCS URLs."""
        # Mock successful GCS uploads
        gcs_urls = [
            "https://storage.googleapis.com/bucket/post1_photo1.jpg",
            "https://storage.googleapis.com/bucket/post1_photo2.jpg",
            "https://storage.googleapis.com/bucket/post2_photo1.jpg",
            "https://storage.googleapis.com/bucket/post2_photo2.jpg"
        ]
        
        mock_media_handler = Mock()
        
        def mock_download(media):
            # Simulate setting GCS URL on media
            idx = list(InstagramMedia.objects.filter(media_type="image")).index(media)
            if idx < len(gcs_urls):
                media.gcs_url = gcs_urls[idx]
                media.is_downloaded = True
                media.save()
            return True
            
        mock_media_handler.download_media.side_effect = mock_download
        mock_media_handler_class.return_value = mock_media_handler
        
        # Test will be implemented when service is created
        pass