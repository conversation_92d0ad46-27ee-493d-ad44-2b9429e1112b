"""
Tests for Instagram repositories.
"""

from datetime import timed<PERSON><PERSON>
from django.conf import settings
from django.test import TestCase
from django.utils import timezone

from instagram_manager.models import (
    InstagramProfile,
    InstagramPost,
    InstagramMedia,
    InstagramComment,
    InstagramHashtag,
    InstagramFollower,
    InstagramScrapingTask,
)
from instagram_manager.repositories import (
    InstagramProfileRepository,
    InstagramPostRepository,
    InstagramMediaRepository,
    InstagramCommentRepository,
    InstagramHashtagRepository,
    InstagramFollowerRepository,
    InstagramScrapingTaskRepository,
)


class InstagramProfileRepositoryTest(TestCase):
    """Tests for InstagramProfileRepository"""

    def setUp(self):
        self.repository = InstagramProfileRepository()

        # Create test profiles
        self.profile1 = InstagramProfile.objects.create(
            profile_id="*********",
            username=f"{settings.INSTAGRAM_TEST_USERNAME}1",
            full_name="Test User 1",
            follower_count=1000,
            is_business=True,
            is_verified=False,
            is_private=False,
        )

        self.profile2 = InstagramProfile.objects.create(
            profile_id="987654321",
            username=f"{settings.INSTAGRAM_TEST_USERNAME}2",
            full_name="Test User 2",
            follower_count=50000,
            is_business=False,
            is_verified=True,
            is_private=True,
        )

    def test_get_by_username(self):
        """Test getting profile by username"""
        profile = self.repository.get_by_username(f"{settings.INSTAGRAM_TEST_USERNAME}1")
        assert profile.profile_id == "*********"

        # Test non-existent
        profile = self.repository.get_by_username("nonexistent")
        assert profile is None

    def test_get_by_profile_id(self):
        """Test getting profile by profile_id"""
        profile = self.repository.get_by_profile_id("*********")
        assert profile.username == f"{settings.INSTAGRAM_TEST_USERNAME}1"

        # Test non-existent
        profile = self.repository.get_by_profile_id("*********")
        assert profile is None

    def test_filter_business_accounts(self):
        """Test filtering business accounts"""
        business_accounts = list(self.repository.filter_business_accounts())
        assert len(business_accounts) == 1
        assert business_accounts[0].username == f"{settings.INSTAGRAM_TEST_USERNAME}1"

    def test_filter_verified_accounts(self):
        """Test filtering verified accounts"""
        verified = list(self.repository.filter_verified_accounts())
        assert len(verified) == 1
        assert verified[0].username == f"{settings.INSTAGRAM_TEST_USERNAME}2"

    def test_filter_public_accounts(self):
        """Test filtering public accounts"""
        public = list(self.repository.filter_public_accounts())
        assert len(public) == 1
        assert public[0].username == f"{settings.INSTAGRAM_TEST_USERNAME}1"

    def test_filter_by_follower_count(self):
        """Test filtering by follower count"""
        # Min followers
        profiles = self.repository.filter_by_follower_count(min_followers=10000)
        assert len(profiles) == 1
        assert profiles[0].username == f"{settings.INSTAGRAM_TEST_USERNAME}2"

        # Max followers
        profiles = self.repository.filter_by_follower_count(max_followers=5000)
        assert len(profiles) == 1
        assert profiles[0].username == f"{settings.INSTAGRAM_TEST_USERNAME}1"

        # Range
        profiles = self.repository.filter_by_follower_count(
            min_followers=500, max_followers=100000
        )
        assert len(profiles) == 2

    def test_search(self):
        """Test profile search"""
        # Test search_profiles method instead
        results = list(self.repository.search_profiles(f"{settings.INSTAGRAM_TEST_USERNAME}1"))
        assert len(results) == 1

        # By full name
        results = list(self.repository.search_profiles("Test User"))
        assert len(results) == 2

    def test_get_profiles_needing_update(self):
        """Test getting profiles needing update"""
        # Set last_scraped_at for profile1
        self.profile1.last_scraped_at = timezone.now() - timedelta(hours=48)
        self.profile1.save()

        profiles = list(self.repository.get_profiles_needing_update(hours=24))
        assert len(profiles) == 2  # profile1 (old) and profile2 (never)

    def test_bulk_create_or_update(self):
        """Test bulk create or update"""
        profiles_data = [
            {
                "profile_id": "*********",  # Existing
                "username": f"{settings.INSTAGRAM_TEST_USERNAME}1_updated",
                "follower_count": 1500,
            },
            {
                "profile_id": "111111111",  # New
                "username": "newuser",
                "full_name": "New User",
                "follower_count": 100,
            },
        ]

        created, updated = self.repository.bulk_create_or_update_profiles(profiles_data)

        assert created == 1
        assert updated == 1

        # Verify updates
        profile1 = InstagramProfile.objects.get(profile_id="*********")
        assert profile1.username == f"{settings.INSTAGRAM_TEST_USERNAME}1_updated"
        assert profile1.follower_count == 1500

        # Verify creation
        new_profile = InstagramProfile.objects.get(profile_id="111111111")
        assert new_profile.username == "newuser"


class InstagramPostRepositoryTest(TestCase):
    """Tests for InstagramPostRepository"""

    def setUp(self):
        self.repository = InstagramPostRepository()

        # Create test profile
        self.profile = InstagramProfile.objects.create(
            profile_id="*********", username=settings.INSTAGRAM_TEST_USERNAME
        )

        # Create test posts
        self.post1 = InstagramPost.objects.create(
            external_id="POST123",
            profile=self.profile,
            shortcode="DKZ7A0XpB1U",
            post_type="photo",
            caption="Test post 1",
            like_count=100,
            comment_count=10,
            posted_at=timezone.now() - timedelta(days=1),
        )

        self.post2 = InstagramPost.objects.create(
            external_id="POST456",
            profile=self.profile,
            shortcode="DKVPW5_T6dZ",
            post_type="video",
            caption="Test post 2 #hashtag",
            like_count=200,
            comment_count=20,
            posted_at=timezone.now(),
            is_sponsored=True,
        )

    def test_get_by_external_id(self):
        """Test getting post by external_id"""
        post = self.repository.get_by_external_id("POST123")
        assert post.shortcode == "DKZ7A0XpB1U"

        # Test non-existent
        post = self.repository.get_by_external_id("NONEXISTENT")
        assert post is None

    def test_get_by_shortcode(self):
        """Test getting post by shortcode"""
        post = self.repository.get_by_shortcode("DKZ7A0XpB1U")
        assert post.external_id == "POST123"

    def test_filter_by_profile(self):
        """Test filtering posts by profile"""
        posts = list(self.repository.filter_by_profile(self.profile))
        assert len(posts) == 2
        # Should be ordered by posted_at desc
        assert posts[0].external_id == "POST456"
        assert posts[1].external_id == "POST123"

    def test_filter_by_type(self):
        """Test filtering posts by type"""
        photos = list(self.repository.filter_by_type("photo"))
        assert len(photos) == 1
        assert photos[0].external_id == "POST123"

        videos = list(self.repository.filter_by_type("video"))
        assert len(videos) == 1
        assert videos[0].external_id == "POST456"

    def test_filter_by_date_range(self):
        """Test filtering posts by date range"""
        # Last 12 hours
        recent = list(
            self.repository.filter_posts_by_date(
                date_from=timezone.now() - timedelta(hours=12)
            )
        )
        assert len(recent) == 1
        assert recent[0].external_id == "POST456"

        # All posts
        all_posts = list(
            self.repository.filter_posts_by_date(
                date_from=timezone.now() - timedelta(days=7)
            )
        )
        assert len(all_posts) == 2


    def test_search_by_caption(self):
        """Test searching posts by caption"""
        results = list(self.repository.search_by_caption("hashtag"))
        assert len(results) == 1
        assert results[0].external_id == "POST456"

    def test_get_top_posts(self):
        """Test getting top posts"""
        # By likes
        top_likes = self.repository.get_top_posts(limit=1, metric="likes")
        assert len(top_likes) == 1
        assert top_likes[0].external_id == "POST456"

        # By comments
        top_comments = self.repository.get_top_posts(limit=1, metric="comments")
        assert len(top_comments) == 1
        assert top_comments[0].external_id == "POST456"

        # By engagement
        top_engagement = self.repository.get_top_posts(limit=1, metric="engagement")
        assert len(top_engagement) == 1
        assert top_engagement[0].external_id == "POST456"

    def test_get_engagement_stats(self):
        """Test getting engagement stats"""
        stats = self.repository.get_engagement_stats(self.profile)

        assert stats["total_posts"] == 2
        assert stats["total_likes"] == 300
        assert stats["total_comments"] == 30
        assert stats["avg_likes"] == 150
        assert stats["avg_comments"] == 15
        assert stats["avg_engagement"] > 0


class InstagramMediaRepositoryTest(TestCase):
    """Tests for InstagramMediaRepository"""

    def setUp(self):
        self.repository = InstagramMediaRepository()

        # Create test profile and post
        self.profile = InstagramProfile.objects.create(
            profile_id="*********", username=settings.INSTAGRAM_TEST_USERNAME
        )

        self.post = InstagramPost.objects.create(
            external_id="POST123",
            profile=self.profile,
            shortcode="DKZ7A0XpB1U",
            post_type="carousel",
            posted_at=timezone.now(),
        )

        # Create test media
        self.media1 = InstagramMedia.objects.create(
            external_id="MEDIA123",
            post=self.post,
            media_type="photo",
            media_url="https://example.com/photo1.jpg",
            width=1080,
            height=1080,
            order_index=0,
            is_downloaded=True,
            local_path="instagram/media/photo1.jpg",
        )

        self.media2 = InstagramMedia.objects.create(
            external_id="MEDIA456",
            post=self.post,
            media_type="video",
            media_url="https://example.com/video1.mp4",
            thumbnail_url="https://example.com/thumb1.jpg",
            width=1920,
            height=1080,
            duration=30,
            order_index=1,
            is_downloaded=False,
        )

    def test_get_by_external_id(self):
        """Test getting media by external_id"""
        media = self.repository.get_by_external_id("MEDIA123")
        assert media.media_type == "photo"

        # Test non-existent
        media = self.repository.get_by_external_id("NONEXISTENT")
        assert media is None

    def test_filter_by_post(self):
        """Test filtering media by post"""
        media_list = list(self.repository.filter_by_post(self.post))
        assert len(media_list) == 2
        # Should be ordered by order_index
        assert media_list[0].external_id == "MEDIA123"
        assert media_list[1].external_id == "MEDIA456"

    def test_filter_by_type(self):
        """Test filtering media by type"""
        photos = list(self.repository.filter_by_type("photo"))
        assert len(photos) == 1
        assert photos[0].external_id == "MEDIA123"

        videos = list(self.repository.filter_by_type("video"))
        assert len(videos) == 1
        assert videos[0].external_id == "MEDIA456"

    def test_filter_downloaded(self):
        """Test filtering downloaded media"""
        downloaded = list(self.repository.filter_downloaded())
        assert len(downloaded) == 1
        assert downloaded[0].external_id == "MEDIA123"

    def test_filter_not_downloaded(self):
        """Test filtering not downloaded media"""
        not_downloaded = list(self.repository.filter_not_downloaded())
        assert len(not_downloaded) == 1
        assert not_downloaded[0].external_id == "MEDIA456"

    def test_mark_as_downloaded(self):
        """Test marking media as downloaded"""
        self.repository.mark_as_downloaded(self.media2, "instagram/media/video1.mp4")

        self.media2.refresh_from_db()
        assert self.media2.is_downloaded
        assert self.media2.local_path.name == "instagram/media/video1.mp4"
        assert self.media2.download_error == ""

    def test_mark_download_failed(self):
        """Test marking download as failed"""
        error_msg = "Connection timeout"
        self.repository.mark_download_failed(self.media2, error_msg)

        self.media2.refresh_from_db()
        assert not self.media2.is_downloaded
        assert self.media2.download_error == error_msg

    def test_get_storage_stats(self):
        """Test getting storage statistics"""
        stats = self.repository.get_storage_stats()

        assert stats["total_media"] == 2
        assert stats["downloaded"] == 1
        assert stats["pending"] == 1
        assert stats["failed"] == 0
        assert stats["download_rate"] == 50.0
        assert stats["video_stats"]["total"] == 1
        assert stats["photo_stats"]["downloaded"] == 1


class InstagramCommentRepositoryTest(TestCase):
    """Tests for InstagramCommentRepository"""

    def setUp(self):
        self.repository = InstagramCommentRepository()

        # Create test data
        self.profile = InstagramProfile.objects.create(
            profile_id="*********", username=settings.INSTAGRAM_TEST_USERNAME
        )

        self.post = InstagramPost.objects.create(
            external_id="POST123",
            profile=self.profile,
            shortcode="DKZ7A0XpB1U",
            post_type="photo",
            posted_at=timezone.now(),
        )

        # Create comments
        self.comment1 = InstagramComment.objects.create(
            external_id="COMMENT123",
            post=self.post,
            author_username="user1",
            author_external_id="111111",
            text="Great post!",
            like_count=5,
            commented_at=timezone.now() - timedelta(hours=2),
        )

        self.comment2 = InstagramComment.objects.create(
            external_id="COMMENT456",
            post=self.post,
            author_username="user2",
            author_external_id="222222",
            author_is_verified=True,
            text="Amazing! How did you do this?",
            like_count=15,
            commented_at=timezone.now() - timedelta(hours=1),
        )

        # Reply to comment1
        self.reply1 = InstagramComment.objects.create(
            external_id="REPLY123",
            post=self.post,
            author_username="user3",
            author_external_id="333333",
            text="I agree!",
            like_count=2,
            reply_to=self.comment1,
            commented_at=timezone.now(),
        )

    def test_get_by_external_id(self):
        """Test getting comment by external_id"""
        comment = self.repository.get_by_external_id("COMMENT123")
        assert comment.text == "Great post!"

        # Test non-existent
        comment = self.repository.get_by_external_id("NONEXISTENT")
        assert comment is None

    def test_filter_by_post(self):
        """Test filtering comments by post"""
        comments = list(self.repository.filter_by_post(self.post))
        assert len(comments) == 3
        # Should be ordered by commented_at desc
        assert comments[0].external_id == "REPLY123"

    def test_filter_top_level_comments(self):
        """Test filtering top level comments"""
        top_level = list(self.repository.filter_top_level_comments(self.post))
        assert len(top_level) == 2
        # No replies
        comment_ids = [c.external_id for c in top_level]
        assert "REPLY123" not in comment_ids

    def test_filter_replies(self):
        """Test filtering replies"""
        replies = list(self.repository.filter_replies(self.comment1))
        assert len(replies) == 1
        assert replies[0].external_id == "REPLY123"

    def test_filter_verified_authors(self):
        """Test filtering comments from verified authors"""
        verified = list(self.repository.filter_verified_authors())
        assert len(verified) == 1
        assert verified[0].external_id == "COMMENT456"

    def test_get_popular_comments(self):
        """Test getting popular comments"""
        popular = list(self.repository.get_popular_comments(self.post, min_likes=10))
        assert len(popular) == 1
        assert popular[0].external_id == "COMMENT456"

    def test_get_comment_stats(self):
        """Test getting comment statistics"""
        stats = self.repository.get_comment_stats(self.post)

        assert stats["total_comments"] == 3
        assert stats["total_replies"] == 1
        assert stats["top_level_comments"] == 2
        assert stats["verified_authors"] == 1
        assert stats["avg_likes"] > 0


class InstagramHashtagRepositoryTest(TestCase):
    """Tests for InstagramHashtagRepository"""

    def setUp(self):
        self.repository = InstagramHashtagRepository()

        # Create test hashtags
        self.hashtag1 = InstagramHashtag.objects.create(
            name="photography", post_count=1000
        )

        self.hashtag2 = InstagramHashtag.objects.create(name="travel", post_count=5000)

        self.hashtag3 = InstagramHashtag.objects.create(name="food", post_count=50)

        # Create test posts
        profile = InstagramProfile.objects.create(
            profile_id="*********", username=settings.INSTAGRAM_TEST_USERNAME
        )

        self.post1 = InstagramPost.objects.create(
            external_id="POST123",
            profile=profile,
            shortcode="DKZ7A0XpB1U",
            post_type="photo",
            posted_at=timezone.now(),
        )

        # Associate hashtags with posts
        self.hashtag1.posts.add(self.post1)
        self.hashtag2.posts.add(self.post1)

    def test_get_by_name(self):
        """Test getting hashtag by name"""
        # With #
        hashtag = self.repository.get_by_name("#photography")
        assert hashtag.name == "photography"

        # Without #
        hashtag = self.repository.get_by_name("travel")
        assert hashtag.name == "travel"

        # Case insensitive
        hashtag = self.repository.get_by_name("FOOD")
        assert hashtag.name == "food"

    def test_get_or_create_hashtag(self):
        """Test get or create hashtag"""
        # Existing
        hashtag, created = self.repository.get_or_create_hashtag("#photography")
        assert not created
        assert hashtag.name == "photography"

        # New
        hashtag, created = self.repository.get_or_create_hashtag("newhashtag")
        assert created
        assert hashtag.name == "newhashtag"

    def test_filter_popular(self):
        """Test filtering popular hashtags"""
        popular = list(self.repository.filter_popular(min_posts=100))
        assert len(popular) == 2
        # Should be ordered by post_count desc
        assert popular[0].name == "travel"
        assert popular[1].name == "photography"

    def test_search(self):
        """Test hashtag search"""
        results = list(self.repository.search_hashtags("photo"))
        assert len(results) == 1
        assert results[0].name == "photography"

    def test_get_related_hashtags(self):
        """Test getting related hashtags"""
        related = list(self.repository.get_related_hashtags(self.hashtag1, limit=10))
        # Should find hashtag2 as it's used in the same post
        assert len(related) == 1
        assert related[0].name == "travel"

    def test_extract_and_save_hashtags(self):
        """Test extracting hashtags from text"""
        text = "Beautiful sunset! #sunset #nature #photography #Travel"

        profile = InstagramProfile.objects.create(
            profile_id="*********", username=f"{settings.INSTAGRAM_TEST_USERNAME}2"
        )

        post = InstagramPost.objects.create(
            external_id="POST999",
            profile=profile,
            shortcode="XYZ999",
            post_type="photo",
            caption=text,
            posted_at=timezone.now(),
        )

        hashtags = self.repository.extract_and_save_hashtags(text, post)

        assert len(hashtags) == 4
        hashtag_names = [h.name for h in hashtags]
        assert "sunset" in hashtag_names
        assert "nature" in hashtag_names
        assert "photography" in hashtag_names
        assert "travel" in hashtag_names  # Lowercase


class InstagramFollowerRepositoryTest(TestCase):
    """Tests for InstagramFollowerRepository"""

    def setUp(self):
        self.repository = InstagramFollowerRepository()

        # Create test profile
        self.profile = InstagramProfile.objects.create(
            profile_id="*********",
            username=settings.INSTAGRAM_TEST_USERNAME,
            follower_count=3,
        )

        # Create followers
        self.follower1 = InstagramFollower.objects.create(
            profile=self.profile,
            follower_username="follower1",
            follower_id="111111",
            follower_full_name="Follower One",
            is_verified=True,
            followed_at=timezone.now() - timedelta(days=7),
        )

        self.follower2 = InstagramFollower.objects.create(
            profile=self.profile,
            follower_username="follower2",
            follower_id="222222",
            follower_full_name="Follower Two",
            is_verified=False,
            followed_at=timezone.now() - timedelta(hours=12),
        )

    def test_get_follower(self):
        """Test getting specific follower"""
        follower = self.repository.get_follower(self.profile, "follower1")
        assert follower.follower_id == "111111"

        # Test non-existent
        follower = self.repository.get_follower(self.profile, "nonexistent")
        assert follower is None

    def test_filter_by_profile(self):
        """Test filtering followers by profile"""
        followers = list(self.repository.filter_by_profile(self.profile))
        assert len(followers) == 2
        # Should be ordered by followed_at desc
        assert followers[0].follower_username == "follower2"

    def test_filter_verified_followers(self):
        """Test filtering verified followers"""
        verified = list(self.repository.filter_verified_followers(self.profile))
        assert len(verified) == 1
        assert verified[0].follower_username == "follower1"

    def test_get_new_followers(self):
        """Test getting new followers"""
        new_followers = list(self.repository.get_new_followers(self.profile, hours=24))
        assert len(new_followers) == 1
        assert new_followers[0].follower_username == "follower2"

    def test_detect_unfollowers(self):
        """Test detecting unfollowers"""
        current_followers = ["follower2"]  # follower1 unfollowed

        unfollowers = list(
            self.repository.detect_unfollowers(self.profile, current_followers)
        )
        assert len(unfollowers) == 1
        assert unfollowers[0].follower_username == "follower1"

    def test_get_follower_stats(self):
        """Test getting follower statistics"""
        stats = self.repository.get_follower_stats(self.profile)

        assert stats["total_followers"] == 2
        assert stats["verified_followers"] == 1
        assert stats["verified_percentage"] == 50.0
        assert "new_followers_1d" in stats
        assert "new_followers_7d" in stats


class InstagramScrapingTaskRepositoryTest(TestCase):
    """Tests for InstagramScrapingTaskRepository"""

    def setUp(self):
        self.repository = InstagramScrapingTaskRepository()

        # Create test tasks
        self.task1 = InstagramScrapingTask.objects.create(
            task_id="repo_task1",
            task_type="profile",
            target_identifier=settings.INSTAGRAM_TEST_USERNAME,
            status="completed",
            brightdata_dataset_id="dataset123",
            items_scraped=100,
            started_at=timezone.now() - timedelta(hours=2),
            completed_at=timezone.now() - timedelta(hours=1),
        )

        self.task2 = InstagramScrapingTask.objects.create(
            task_id="repo_task2",
            task_type="posts",
            target_identifier=settings.INSTAGRAM_TEST_USERNAME,
            status="in_progress",
            brightdata_dataset_id="dataset123",
            started_at=timezone.now() - timedelta(minutes=10),
        )

        self.task3 = InstagramScrapingTask.objects.create(
            task_id="repo_task3",
            task_type="followers",
            target_identifier=settings.INSTAGRAM_TEST_USERNAME,
            status="pending",
            brightdata_dataset_id="dataset123",
        )

        self.task4 = InstagramScrapingTask.objects.create(
            task_id="repo_task4",
            task_type="posts",
            target_identifier="anotheruser",
            status="failed",
            brightdata_dataset_id="dataset123",
            error_message="Rate limit exceeded",
            completed_at=timezone.now() - timedelta(minutes=30),
        )

    def test_filter_by_status(self):
        """Test filtering tasks by status"""
        completed = list(self.repository.filter_by_status("completed"))
        assert len(completed) == 1
        assert completed[0].task_type == "profile"

        in_progress = list(self.repository.filter_by_status("in_progress"))
        assert len(in_progress) == 1

        pending = list(self.repository.filter_by_status("pending"))
        assert len(pending) == 1

    def test_get_pending_tasks(self):
        """Test getting pending tasks"""
        pending = list(self.repository.get_pending_tasks(limit=10))
        assert len(pending) == 1
        assert pending[0].task_type == "followers"

    def test_get_stuck_tasks(self):
        """Test getting stuck tasks"""
        # Create a stuck task
        stuck_task = InstagramScrapingTask.objects.create(
            task_id="stuck_task",
            task_type="comments",
            target_identifier="post123",
            status="in_progress",
            brightdata_dataset_id="dataset123",
            started_at=timezone.now() - timedelta(hours=1),
        )

        stuck = list(self.repository.get_stuck_tasks(timeout_minutes=30))
        assert len(stuck) == 1
        assert stuck[0].id == stuck_task.id

    def test_check_duplicate_task(self):
        """Test checking for duplicate tasks"""
        # Should find duplicate
        is_duplicate = self.repository.check_duplicate_task(
            "posts", settings.INSTAGRAM_TEST_USERNAME, hours=24
        )
        assert is_duplicate

        # Should not find duplicate (different target)
        is_duplicate = self.repository.check_duplicate_task(
            "posts", "newuser", hours=24
        )
        assert not is_duplicate

        # Should not find duplicate (failed task)
        is_duplicate = self.repository.check_duplicate_task(
            "posts", "anotheruser", hours=24
        )
        assert not is_duplicate

    def test_task_lifecycle(self):
        """Test task lifecycle methods"""
        task = InstagramScrapingTask.objects.create(
            task_id="lifecycle_task",
            task_type="hashtag",
            target_identifier="photography",
            status="pending",
            brightdata_dataset_id="dataset123",
        )

        # Start task
        self.repository.start_task(task)
        task.refresh_from_db()
        assert task.status == "in_progress"
        assert task.started_at is not None

        # Complete task
        self.repository.complete_task(task, items_scraped=50)
        task.refresh_from_db()
        assert task.status == "completed"
        assert task.completed_at is not None
        assert task.items_scraped == 50

    def test_fail_task(self):
        """Test failing a task"""
        task = InstagramScrapingTask.objects.create(
            task_id="fail_task",
            task_type="comments",
            target_identifier="post456",
            status="in_progress",
            brightdata_dataset_id="dataset123",
        )

        error_msg = "Authentication failed"
        self.repository.fail_task(task, error_msg)

        task.refresh_from_db()
        assert task.status == "failed"
        assert task.completed_at is not None
        assert task.error_message == error_msg

    def test_get_task_stats(self):
        """Test getting task statistics"""
        stats = self.repository.get_task_stats(hours=24)

        assert stats["total_tasks"] == 4
        assert stats["completed_tasks"] == 1
        assert stats["failed_tasks"] == 1
        assert stats["pending_tasks"] == 1
        assert stats["in_progress_tasks"] == 1
        assert stats["success_rate"] == 25.0
        assert "by_type" in stats
        assert "avg_duration_seconds" in stats
