"""
Tests for MediaHandler with GCS integration.
"""
from unittest.mock import Mock, patch
from django.test import TestCase, override_settings

from instagram_manager.models import InstagramMedia, InstagramPost, InstagramProfile
from instagram_manager.instagram_api.data_handlers.media_handler import MediaHandler


class TestMediaHandlerGCS(TestCase):
    """Test MediaHandler with GCS integration."""
    
    def setUp(self):
        """Set up test data."""
        self.profile = InstagramProfile.objects.create(
            username="testuser",
            profile_id="12345",
            external_id="test_external_123"
        )
        
        from django.utils import timezone
        
        self.post = InstagramPost.objects.create(
            profile=self.profile,
            shortcode="test123",
            post_type="photo",
            external_id="post_external_123",
            posted_at=timezone.now()
        )
        
        self.media = InstagramMedia.objects.create(
            post=self.post,
            media_type="photo",
            media_url="https://instagram.com/test.jpg",
            external_id="media_external_123"
        )
    
    @override_settings(GCS_BUCKET_NAME='test-bucket')
    @patch('instagram_manager.instagram_api.data_handlers.media_handler.GCSService')
    @patch('requests.get')
    def test_download_with_gcs_success(self, mock_get, mock_gcs_class):
        """Test successful GCS upload."""
        # Setup mocks
        mock_response = Mock()
        mock_response.content = b'image data'
        mock_response.headers = {'content-type': 'image/jpeg'}
        mock_response.raise_for_status = Mock()
        mock_get.return_value = mock_response
        
        mock_gcs = Mock()
        mock_gcs.upload_file.return_value = 'https://storage.googleapis.com/test-bucket/instagram/file.jpg'
        mock_gcs_class.return_value = mock_gcs
        
        # Mock the static method get_folder_by_media_type
        mock_gcs_class.get_folder_by_media_type = Mock(return_value='images')
        
        # Test
        handler = MediaHandler(save_to_gcs=True)
        result = handler.download_media(self.media)
        
        # Assertions
        self.assertTrue(result)
        
        # Reload media from database
        self.media.refresh_from_db()
        self.assertEqual(self.media.gcs_url, 'https://storage.googleapis.com/test-bucket/instagram/file.jpg')
        self.assertTrue(self.media.is_downloaded)
        
        # GCS should be called
        mock_gcs.upload_file.assert_called_once()
        call_args = mock_gcs.upload_file.call_args
        self.assertEqual(call_args[1]['file_content'], b'image data')
        self.assertEqual(call_args[1]['content_type'], 'image/jpeg')
        self.assertEqual(call_args[1]['folder'], 'images')
    
    @override_settings(GCS_BUCKET_NAME='test-bucket')
    @patch('instagram_manager.instagram_api.data_handlers.media_handler.GCSService')
    @patch('requests.get')
    def test_download_with_gcs_fallback(self, mock_get, mock_gcs_class):
        """Test fallback to local when GCS fails."""
        # Setup mocks
        mock_response = Mock()
        mock_response.content = b'image data'
        mock_response.headers = {'content-type': 'image/jpeg'}
        mock_response.raise_for_status = Mock()
        mock_get.return_value = mock_response
        
        # GCS fails
        mock_gcs = Mock()
        mock_gcs.upload_file.side_effect = Exception("GCS Error")
        mock_gcs_class.return_value = mock_gcs
        
        # Test
        handler = MediaHandler(save_to_gcs=True)
        result = handler.download_media(self.media)
        
        # Should fallback to local
        self.assertTrue(result)
        
        # Reload media from database
        self.media.refresh_from_db()
        self.assertTrue(self.media.is_downloaded)
        self.assertIsNone(self.media.gcs_url)  # GCS URL should not be set
        self.assertTrue(self.media.local_path.name)  # Local path should be set
    
    @override_settings(GCS_BUCKET_NAME='')
    def test_download_without_gcs_settings(self):
        """Test that GCS is not initialized without bucket name."""
        handler = MediaHandler(save_to_gcs=True)
        self.assertIsNone(handler._gcs_service)
    
    def test_download_without_gcs_flag(self):
        """Test normal download without GCS."""
        handler = MediaHandler(save_to_gcs=False)
        self.assertIsNone(handler._gcs_service)
    
    @patch('requests.get')
    def test_download_local_only(self, mock_get):
        """Test download with local storage only."""
        # Setup mocks
        mock_response = Mock()
        mock_response.content = b'image data'
        mock_response.headers = {'content-type': 'image/jpeg'}
        mock_response.raise_for_status = Mock()
        mock_get.return_value = mock_response
        
        # Test
        handler = MediaHandler(save_to_gcs=False)
        result = handler.download_media(self.media)
        
        # Assertions
        self.assertTrue(result)
        
        # Reload media from database
        self.media.refresh_from_db()
        self.assertTrue(self.media.is_downloaded)
        self.assertIsNone(self.media.gcs_url)  # GCS URL should not be set
        self.assertTrue(self.media.local_path.name)  # Local path should be set
    
    def test_download_already_downloaded(self):
        """Test that already downloaded media is skipped."""
        self.media.is_downloaded = True
        self.media.save()
        
        handler = MediaHandler()
        result = handler.download_media(self.media)
        
        self.assertTrue(result)  # Should return True without downloading
    
    @override_settings(GCS_BUCKET_NAME='test-bucket')
    @patch('instagram_manager.instagram_api.data_handlers.media_handler.GCSService')
    def test_gcs_initialization_failure(self, mock_gcs_class):
        """Test graceful handling of GCS initialization failure."""
        # GCS initialization fails
        mock_gcs_class.side_effect = Exception("GCS Init Error")
        
        # Should not raise, just log warning
        handler = MediaHandler(save_to_gcs=True)
        self.assertIsNone(handler._gcs_service)
    
    @patch('requests.get')
    def test_download_network_error(self, mock_get):
        """Test handling of network errors during download."""
        # Setup mock to raise exception
        mock_get.side_effect = Exception("Network error")
        
        handler = MediaHandler()
        result = handler.download_media(self.media)
        
        self.assertFalse(result)
        
        # Reload media from database
        self.media.refresh_from_db()
        self.assertFalse(self.media.is_downloaded)
        self.assertEqual(self.media.download_error, "Network error")
    
    def test_generate_filename(self):
        """Test filename generation."""
        handler = MediaHandler()
        
        # Test different content types
        filename = handler._generate_filename(self.media, 'image/jpeg')
        self.assertEqual(filename, f"{self.media.external_id}.jpg")
        
        filename = handler._generate_filename(self.media, 'image/png')
        self.assertEqual(filename, f"{self.media.external_id}.png")
        
        filename = handler._generate_filename(self.media, 'video/mp4')
        self.assertEqual(filename, f"{self.media.external_id}.mp4")
        
        # Test unknown content type
        filename = handler._generate_filename(self.media, 'application/octet-stream')
        self.assertEqual(filename, f"{self.media.external_id}.jpg")  # Default to .jpg
    
    @override_settings(GCS_BUCKET_NAME='test-bucket')
    @patch('instagram_manager.instagram_api.data_handlers.media_handler.GCSService')
    @patch('requests.get')
    def test_download_thumbnail_with_gcs(self, mock_get, mock_gcs_class):
        """Test thumbnail download with GCS."""
        # Setup video media with thumbnail
        self.media.media_type = "video"
        self.media.thumbnail_url = "https://instagram.com/thumb.jpg"
        self.media.save()
        
        # Setup mocks
        mock_response = Mock()
        mock_response.content = b'thumbnail data'
        mock_response.headers = {'content-type': 'image/jpeg'}
        mock_response.raise_for_status = Mock()
        mock_get.return_value = mock_response
        
        mock_gcs = Mock()
        mock_gcs.upload_file.return_value = 'https://storage.googleapis.com/test-bucket/instagram/thumb.jpg'
        mock_gcs_class.return_value = mock_gcs
        
        # Test
        handler = MediaHandler(save_to_gcs=True)
        result = handler.download_thumbnail(self.media)
        
        # Assertions
        self.assertTrue(result)
        
        # GCS should be called for thumbnail
        self.assertEqual(mock_gcs.upload_file.call_count, 1)


class TestMediaHandlerBackwardCompatibility(TestCase):
    """Test backward compatibility of MediaHandler."""
    
    def setUp(self):
        """Set up test data."""
        self.profile = InstagramProfile.objects.create(
            username="testuser",
            profile_id="12345",
            external_id="test_external_123"
        )
        
        from django.utils import timezone
        
        self.post = InstagramPost.objects.create(
            profile=self.profile,
            shortcode="test123",
            post_type="photo",
            external_id="post_external_123",
            posted_at=timezone.now()
        )
        
        self.media = InstagramMedia.objects.create(
            post=self.post,
            media_type="photo",
            media_url="https://instagram.com/test.jpg",
            external_id="media_external_123"
        )
    
    @patch('requests.get')
    def test_instance_usage_for_backward_compatibility(self, mock_get):
        """Test that code using MediaHandler as instance works (new usage pattern)."""
        # Setup mocks
        mock_response = Mock()
        mock_response.content = b'image data'
        mock_response.headers = {'content-type': 'image/jpeg'}
        mock_response.raise_for_status = Mock()
        mock_get.return_value = mock_response
        
        # New usage pattern - create instance
        handler = MediaHandler(save_to_gcs=False)
        result = handler.download_media(self.media)
        
        self.assertTrue(result)
        
        # Reload media from database
        self.media.refresh_from_db()
        self.assertTrue(self.media.is_downloaded)
        self.assertTrue(self.media.local_path.name)