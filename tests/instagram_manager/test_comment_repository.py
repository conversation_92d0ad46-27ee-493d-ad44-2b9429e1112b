"""
Тесты для обновленного CommentRepository.
"""

import pytest
from django.utils import timezone
from django.conf import settings

from instagram_manager.models import InstagramProfile, InstagramPost, InstagramComment
from instagram_manager.repositories.comment_repository import InstagramCommentRepository


@pytest.fixture
def profile():
    """Создание тестового профиля."""
    return InstagramProfile.objects.create(
        profile_id="test_profile_123",
        username=settings.INSTAGRAM_TEST_USERNAME,
        full_name="Test User",
        follower_count=1000,
    )


@pytest.fixture
def post(profile):
    """Создание тестового поста."""
    return InstagramPost.objects.create(
        external_id="test_post_123",
        shortcode="DKZ7A0XpB1U",
        profile=profile,
        post_type="photo",
        post_url="https://instagram.com/p/DKZ7A0XpB1U/",
        posted_at=timezone.now(),
    )


@pytest.fixture
def comment_repo():
    """Создание экземпляра репозитория."""
    return InstagramCommentRepository()


@pytest.fixture
def sample_comments(post):
    """Создание тестовых комментариев с иерархией."""
    # Комментарии верхнего уровня
    parent1 = InstagramComment.objects.create(
        external_id="parent1",
        post=post,
        author_username="user1",
        author_external_id="uid1",
        text="Parent comment 1",
        like_count=10,
        is_pinned=True,
        is_hidden=False,
        commented_at=timezone.now(),
    )

    parent2 = InstagramComment.objects.create(
        external_id="parent2",
        post=post,
        author_username="user2",
        author_external_id="uid2",
        text="Parent comment 2",
        like_count=5,
        is_pinned=False,
        is_hidden=False,
        commented_at=timezone.now(),
    )

    hidden_parent = InstagramComment.objects.create(
        external_id="hidden_parent",
        post=post,
        author_username="user3",
        author_external_id="uid3",
        text="Hidden comment",
        like_count=0,
        is_pinned=False,
        is_hidden=True,
        commented_at=timezone.now(),
    )

    # Ответы
    reply1 = InstagramComment.objects.create(
        external_id="reply1",
        post=post,
        reply_to=parent1,
        author_username="user4",
        author_external_id="uid4",
        text="Reply to parent 1",
        like_count=2,
        is_hidden=False,
        commented_at=timezone.now(),
    )

    reply2 = InstagramComment.objects.create(
        external_id="reply2",
        post=post,
        reply_to=parent1,
        author_username="user5",
        author_external_id="uid5",
        text="Another reply to parent 1",
        like_count=15,
        is_hidden=False,
        commented_at=timezone.now(),
    )

    hidden_reply = InstagramComment.objects.create(
        external_id="hidden_reply",
        post=post,
        reply_to=parent2,
        author_username="user6",
        author_external_id="uid6",
        text="Hidden reply",
        like_count=0,
        is_hidden=True,
        commented_at=timezone.now(),
    )

    return {
        "parent1": parent1,
        "parent2": parent2,
        "hidden_parent": hidden_parent,
        "reply1": reply1,
        "reply2": reply2,
        "hidden_reply": hidden_reply,
    }


@pytest.mark.django_db
class TestCommentRepository:
    """Тесты для обновленных методов CommentRepository."""

    def setup_method(self):
        """Очистка данных перед каждым тестом."""
        pass

    def test_filter_by_post_without_hidden(self, comment_repo, post, sample_comments):
        """Тест фильтрации комментариев поста без скрытых."""
        comments = comment_repo.filter_by_post(post, include_hidden=False)

        assert len(comments) == 4  # Только видимые комментарии
        comment_ids = [c.external_id for c in comments]
        assert "hidden_parent" not in comment_ids
        assert "hidden_reply" not in comment_ids

    def test_filter_by_post_with_hidden(self, comment_repo, post, sample_comments):
        """Тест фильтрации комментариев поста со скрытыми."""
        comments = comment_repo.filter_by_post(post, include_hidden=True)

        assert len(comments) == 6  # Все комментарии
        comment_ids = [c.external_id for c in comments]
        assert "hidden_parent" in comment_ids
        assert "hidden_reply" in comment_ids

    def test_filter_top_level_comments(self, comment_repo, post, sample_comments):
        """Тест фильтрации комментариев верхнего уровня."""
        # Без скрытых
        top_comments = comment_repo.filter_top_level_comments(
            post, include_hidden=False
        )
        assert len(top_comments) == 2
        assert all(c.reply_to is None for c in top_comments)
        assert all(not c.is_hidden for c in top_comments)

        # Со скрытыми
        all_top_comments = comment_repo.filter_top_level_comments(
            post, include_hidden=True
        )
        assert len(all_top_comments) == 3

    def test_filter_replies(self, comment_repo, sample_comments):
        """Тест фильтрации ответов на комментарий."""
        parent1 = sample_comments["parent1"]
        parent2 = sample_comments["parent2"]

        # Ответы на parent1 (без скрытых)
        replies1 = comment_repo.filter_replies(parent1, include_hidden=False)
        assert len(replies1) == 2
        assert all(r.reply_to == parent1 for r in replies1)

        # Ответы на parent2 (со скрытыми)
        replies2_visible = comment_repo.filter_replies(parent2, include_hidden=False)
        assert len(replies2_visible) == 0

        replies2_all = comment_repo.filter_replies(parent2, include_hidden=True)
        assert len(replies2_all) == 1
        assert replies2_all[0].is_hidden

    def test_get_popular_comments(self, comment_repo, post, sample_comments):
        """Тест получения популярных комментариев."""
        # С минимальным порогом 10 лайков
        popular = comment_repo.get_popular_comments(
            post, min_likes=10, include_hidden=False
        )
        assert len(popular) == 2  # parent1 и reply2
        assert all(c.like_count >= 10 for c in popular)
        assert (
            popular[0].like_count >= popular[1].like_count
        )  # Отсортированы по убыванию

        # Включая скрытые
        popular_all = comment_repo.get_popular_comments(
            post, min_likes=0, include_hidden=True
        )
        assert len(popular_all) == 6

    def test_get_comment_tree(self, comment_repo, post, sample_comments):
        """Тест построения дерева комментариев."""
        # Без скрытых
        tree = comment_repo.get_comment_tree(post, include_hidden=False)

        assert len(tree) == 2  # Два видимых родительских комментария

        # Проверяем структуру первого
        parent1_tree = next(t for t in tree if t["comment"].external_id == "parent1")
        assert len(parent1_tree["replies"]) == 2
        assert all(r.reply_to.external_id == "parent1" for r in parent1_tree["replies"])

        # Проверяем структуру второго
        parent2_tree = next(t for t in tree if t["comment"].external_id == "parent2")
        assert len(parent2_tree["replies"]) == 0  # Скрытый ответ не включен

        # Со скрытыми
        tree_all = comment_repo.get_comment_tree(post, include_hidden=True)
        assert len(tree_all) == 3  # Включая скрытый родительский

        parent2_tree_all = next(
            t for t in tree_all if t["comment"].external_id == "parent2"
        )
        assert len(parent2_tree_all["replies"]) == 1  # Теперь включен скрытый ответ

    def test_get_comment_thread(self, comment_repo, sample_comments):
        """Тест получения всей ветки комментариев."""
        parent1 = sample_comments["parent1"]

        # Получаем всю ветку начиная с parent1
        thread = comment_repo.get_comment_thread(parent1)

        assert len(thread) == 3  # parent1 + 2 ответа
        assert thread[0] == parent1
        assert all(c.external_id in ["parent1", "reply1", "reply2"] for c in thread)

    def test_get_comments_by_authors(self, comment_repo, post, sample_comments):
        """Тест получения комментариев от конкретных авторов."""
        # Комментарии от user1 и user4
        authors = ["user1", "user4"]
        comments = comment_repo.get_comments_by_authors(authors, post)

        assert len(comments) == 2
        assert all(c.author_username in authors for c in comments)
        assert all(not c.is_hidden for c in comments)  # Только видимые

        # Без фильтра по посту
        all_comments = comment_repo.get_comments_by_authors(authors)
        assert len(all_comments) == 2

    def test_get_pinned_comments(self, comment_repo, post, sample_comments):
        """Тест получения закрепленных комментариев."""
        pinned = comment_repo.get_pinned_comments(post)

        assert len(pinned) == 1
        assert pinned[0].external_id == "parent1"
        assert pinned[0].is_pinned
        assert not pinned[0].is_hidden

    def test_get_engagement_comments(self, comment_repo, post):
        """Тест получения комментариев, способствующих вовлечению."""
        # Создаем комментарии с вопросами
        InstagramComment.objects.create(
            external_id="question1",
            post=post,
            author_username="user_q1",
            author_external_id="uid_q1",
            text="Как ты это сделал?",
            like_count=5,
            commented_at=timezone.now(),
        )

        InstagramComment.objects.create(
            external_id="question2",
            post=post,
            author_username="user_q2",
            author_external_id="uid_q2",
            text="Где это было снято?",
            like_count=3,
            commented_at=timezone.now(),
        )

        InstagramComment.objects.create(
            external_id="regular",
            post=post,
            author_username="user_r",
            author_external_id="uid_r",
            text="Красиво!",
            like_count=1,
            commented_at=timezone.now(),
        )

        # Получаем engagement комментарии
        engagement = comment_repo.get_engagement_comments(post)

        assert len(engagement) >= 2
        # Проверяем, что найдены комментарии с вопросами
        texts = [c.text for c in engagement]
        assert any("?" in text for text in texts)

    def test_bulk_create_or_update(self, comment_repo, post):
        """Тест массового создания/обновления комментариев."""
        comments_data = [
            {
                "external_id": "bulk1",
                "author_username": "bulk_user1",
                "author_external_id": "bulk_uid1",
                "text": "Bulk comment 1",
                "like_count": 5,
                "commented_at": timezone.now(),
            },
            {
                "external_id": "bulk2",
                "author_username": "bulk_user2",
                "author_external_id": "bulk_uid2",
                "text": "Bulk comment 2",
                "like_count": 10,
                "commented_at": timezone.now(),
                "reply_to": "bulk1",  # Ответ на первый
            },
        ]

        # Первый вызов - создание
        created, updated = comment_repo.bulk_create_or_update(comments_data, post)
        assert created == 2
        assert updated == 0

        # Изменяем данные
        comments_data[0]["like_count"] = 15
        comments_data[1]["text"] = "Updated bulk comment 2"

        # Второй вызов - обновление
        created, updated = comment_repo.bulk_create_or_update(comments_data, post)
        assert created == 0
        assert updated == 2

        # Проверяем обновления
        comment1 = InstagramComment.objects.get(external_id="bulk1")
        assert comment1.like_count == 15

        comment2 = InstagramComment.objects.get(external_id="bulk2")
        assert comment2.text == "Updated bulk comment 2"
        assert comment2.reply_to == comment1

    def test_get_comment_stats(self, comment_repo, post, sample_comments):
        """Тест получения статистики комментариев."""
        stats = comment_repo.get_comment_stats(post)

        assert stats["total_comments"] == 6
        assert stats["top_level_comments"] == 3
        assert stats["total_replies"] == 3
        assert stats["verified_authors"] == 0
        assert "avg_likes" in stats
        assert "top_commenters" in stats

        # Проверяем топ комментаторов
        top_commenters = stats["top_commenters"]
        assert len(top_commenters) > 0
        assert all(
            "author_username" in c and "comment_count" in c for c in top_commenters
        )
