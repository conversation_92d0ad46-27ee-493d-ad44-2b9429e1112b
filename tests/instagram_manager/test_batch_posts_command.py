"""
Tests for instagram_batch_posts management command
"""

from unittest.mock import Mock, patch
from django.test import TestCase
from django.core.management import call_command
from instagram_manager.models import (
    InstagramProfile,
    InstagramScrapingTask,
)
import io


class TestInstagramBatchPostsCommand(TestCase):
    """Test batch posts import command"""

    def setUp(self):
        """Create test profiles"""
        self.profile1 = InstagramProfile.objects.create(
            profile_id="123456", username="testuser1", full_name="Test User 1"
        )
        self.profile2 = InstagramProfile.objects.create(
            profile_id="789012", username="testuser2", full_name="Test User 2"
        )
        self.profile3 = InstagramProfile.objects.create(
            profile_id="345678", username="testuser3", full_name="Test User 3"
        )

    @patch(
        "instagram_manager.management.commands.instagram_batch_posts.BatchPostService"
    )
    @patch(
        "instagram_manager.management.commands.base_instagram_command.BrightDataClient"
    )
    def test_batch_import_success(self, mock_client_class, mock_service_class):
        """Test successful batch import"""
        # Setup mocks
        mock_client = Mock()
        mock_client_class.return_value = mock_client

        mock_service = Mock()
        mock_service_class.return_value = mock_service

        # Mock successful import results
        mock_service.import_posts_batch.return_value = {
            "testuser1": {"success": True, "post_count": 10, "posts": []},
            "testuser2": {"success": True, "post_count": 15, "posts": []},
        }

        # Run command
        out = io.StringIO()
        call_command(
            "instagram_batch_posts",
            "testuser1",
            "testuser2",
            "--batch-size",
            "2",
            stdout=out,
        )

        # Check output
        output = out.getvalue()
        assert "Processing 2 profiles" in output
        assert "testuser1: 10 posts imported" in output
        assert "testuser2: 15 posts imported" in output
        assert "Total posts imported: 25" in output

        # Check service was called correctly
        mock_service.import_posts_batch.assert_called_once()
        call_args = mock_service.import_posts_batch.call_args
        assert len(call_args[1]["profiles"]) == 2

        # Check task was created
        task = InstagramScrapingTask.objects.get(
            task_type="posts", batch_identifiers__isnull=False
        )
        assert task.batch_identifiers == ["testuser1", "testuser2"]
        assert task.status == "completed"
        assert task.items_scraped == 25

    @patch(
        "instagram_manager.management.commands.instagram_batch_posts.BatchPostService"
    )
    @patch(
        "instagram_manager.management.commands.base_instagram_command.BrightDataClient"
    )
    def test_batch_import_partial_failure(self, mock_client_class, mock_service_class):
        """Test batch import with some profiles failing"""
        # Setup mocks
        mock_client = Mock()
        mock_client_class.return_value = mock_client

        mock_service = Mock()
        mock_service_class.return_value = mock_service

        # Mock mixed results
        mock_service.import_posts_batch.return_value = {
            "testuser1": {"success": True, "post_count": 10, "posts": []},
            "testuser2": {
                "success": False,
                "post_count": 0,
                "error": "Private profile",
            },
        }

        # Run command
        out = io.StringIO()
        call_command("instagram_batch_posts", "testuser1", "testuser2", stdout=out)

        # Check output
        output = out.getvalue()
        assert "testuser1: 10 posts imported" in output
        assert "testuser2: Private profile" in output
        assert "Failed profiles (1): testuser2" in output

    def test_profile_creation(self):
        """Test that missing profiles are created automatically"""
        # Run command with non-existent profile
        out = io.StringIO()
        with patch(
            "instagram_manager.services.batch_profile_service.BatchProfileService"
        ) as mock_profile_service_class:
            with patch(
                "instagram_manager.management.commands.instagram_batch_posts.BatchPostService"
            ) as mock_service_class:
                with patch(
                    "instagram_manager.management.commands.base_instagram_command.BrightDataClient"
                ) as _:
                    # Mock BatchProfileService to create a new profile
                    mock_profile_service = Mock()
                    mock_profile_service_class.return_value = mock_profile_service
                    new_profile = InstagramProfile(
                        profile_id="999999", 
                        username="newuser", 
                        full_name="New User"
                    )
                    mock_profile_service.validate_and_create_profiles.return_value = (
                        [self.profile1, new_profile],  # One existing, one new
                        []  # No failed profiles
                    )
                    
                    # Mock other services
                    mock_service = Mock()
                    mock_service_class.return_value = mock_service
                    mock_service.import_posts_batch.return_value = {
                        "testuser1": {"success": True, "post_count": 5, "posts": []},
                        "newuser": {"success": True, "post_count": 10, "posts": []}
                    }
                    
                    call_command(
                        "instagram_batch_posts",
                        "testuser1",
                        "newuser",
                        stdout=out,
                    )

        # Check output
        output = out.getvalue()
        # Should process both profiles
        assert "Processing 2 profiles" in output
        assert "testuser1: 5 posts imported" in output
        assert "newuser: 10 posts imported" in output

    @patch(
        "instagram_manager.services.batch_profile_service.BatchProfileService"
    )
    @patch(
        "instagram_manager.management.commands.instagram_batch_posts.BatchPostService"
    )
    @patch(
        "instagram_manager.management.commands.base_instagram_command.BrightDataClient"
    )
    def test_profile_not_found(self, mock_client_class, mock_service_class, mock_profile_service_class):
        """Test handling of non-existent profiles"""
        # Setup mocks
        mock_client = Mock()
        mock_client_class.return_value = mock_client

        mock_service = Mock()
        mock_service_class.return_value = mock_service
        mock_service.import_posts_batch.return_value = {
            "testuser1": {"success": True, "posts_count": 5, "posts": []}
        }
        
        # Mock BatchProfileService
        mock_profile_service = Mock()
        mock_profile_service_class.return_value = mock_profile_service
        # Return only existing profile, simulating nonexistent1 was not found
        mock_profile_service.validate_and_create_profiles.return_value = (
            [self.profile1],  # Only testuser1 found
            ["nonexistent1"]  # Failed to find/create nonexistent1
        )

        out = io.StringIO()
        call_command("instagram_batch_posts", "nonexistent1", "testuser1", stdout=out)

        output = out.getvalue()
        assert "Failed to create profiles: nonexistent1" in output
        assert "Processing 1 profiles" in output

    @patch(
        "instagram_manager.management.commands.instagram_batch_posts.BatchPostService"
    )
    @patch(
        "instagram_manager.management.commands.base_instagram_command.BrightDataClient"
    )
    def test_batch_size_handling(self, mock_client_class, mock_service_class):
        """Test that profiles are processed in correct batch sizes"""
        # Create more profiles
        for i in range(4, 8):  # Changed from 3 to 4 to avoid duplicate
            InstagramProfile.objects.create(
                profile_id=f"99999{i}",
                username=f"testuser{i}",
                full_name=f"Test User {i}",
            )

        # Setup mocks
        mock_client = Mock()
        mock_client_class.return_value = mock_client

        mock_service = Mock()
        mock_service_class.return_value = mock_service
        mock_service.import_posts_batch.return_value = {
            f"testuser{i}": {"success": True, "posts_count": 5, "posts": []}
            for i in range(1, 8)
        }

        # Run command with batch size 3
        out = io.StringIO()
        call_command(
            "instagram_batch_posts",
            *[f"testuser{i}" for i in range(1, 8)],
            "--batch-size",
            "3",
            stdout=out,
        )

        # Should be called 3 times (7 profiles / 3 batch size = 3 batches)
        assert mock_service.import_posts_batch.call_count == 3

        # Check batch sizes
        call_args_list = mock_service.import_posts_batch.call_args_list
        assert len(call_args_list[0][1]["profiles"]) == 3  # First batch: 3
        assert len(call_args_list[1][1]["profiles"]) == 3  # Second batch: 3
        assert len(call_args_list[2][1]["profiles"]) == 1  # Third batch: 1

    @patch(
        "instagram_manager.management.commands.instagram_batch_posts.BatchPostService"
    )
    @patch(
        "instagram_manager.management.commands.base_instagram_command.BrightDataClient"
    )
    def test_filters_passed_correctly(self, mock_client_class, mock_service_class):
        """Test that filter parameters are passed to service"""
        # Setup mocks
        mock_client = Mock()
        mock_client_class.return_value = mock_client

        mock_service = Mock()
        mock_service_class.return_value = mock_service
        mock_service.import_posts_batch.return_value = {
            "testuser1": {"success": True, "posts_count": 5, "posts": []}
        }

        # Run command with filters
        call_command(
            "instagram_batch_posts",
            "testuser1",
            "--limit",
            "50",
            "--start-date",
            "2024-01-01",
            "--end-date",
            "2024-12-31",
            "--post-types",
            "photo",
        )

        # Check service call
        call_args = mock_service.import_posts_batch.call_args[1]
        assert call_args["limit"] == 50
        assert call_args["start_date"] == "2024-01-01"
        assert call_args["end_date"] == "2024-12-31"
        assert call_args["post_types"] == ["photo"]
