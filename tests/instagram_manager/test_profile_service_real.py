from django.test import TransactionTestCase
from unittest.mock import Mock, patch

from instagram_manager.services.profile_service import ProfileService
from instagram_manager.models import InstagramScrapingTask
from tests.factories import InstagramProfileFactory, InstagramPostFactory


import pytest
class TestProfileServiceIntegration(TransactionTestCase):
    """Интеграционные тесты для ProfileService"""
    
    def setUp(self):
        self.service = ProfileService()
        # Мокаем API клиент чтобы не делать реальные запросы
        self.mock_client = Mock()
        self.service.client = self.mock_client
    
    @patch('instagram_manager.instagram_api.data_handlers.profile_handler.ProfileHandler.process_profile_data')
    def test_import_profile_success(self, mock_process):
        """Тест успешного импорта профиля"""
        # Arrange
        username = "test_user"
        mock_profile = InstagramProfileFactory.build(username=username)
        
        # Мокаем ответ API
        api_response = {
            'id': '12345678',
            'username': username,
            'full_name': 'Test User',
            'biography': 'Test bio',
            'edge_followed_by': {'count': 1000},
            'edge_follow': {'count': 500},
            'edge_owner_to_timeline_media': {'count': 50},
            'is_verified': True,
            'profile_pic_url_hd': 'https://example.com/pic.jpg'
        }
        
        self.mock_client.get_profile.return_value = api_response
        mock_process.return_value = mock_profile
        mock_profile.save = Mock()  # Мокаем save чтобы не сохранять в БД
        
        # Act
        result = self.service.import_profile(username)
        
        # Assert
        assert result == mock_profile
        self.mock_client.get_profile.assert_called_once_with(username)
        mock_process.assert_called_once_with(api_response)
    
    def test_import_profile_with_task_tracking(self):
        """Тест импорта профиля с отслеживанием задачи"""
        # Arrange
        username = "test_user"
        task = InstagramScrapingTask.objects.create(
            task_type='profile',
            target_identifier=username,
            status='pending'
        )
        
        # Мокаем ProfileHandler
        with patch('instagram_manager.instagram_api.data_handlers.profile_handler.ProfileHandler.process_profile_data') as mock_handler:
            profile = InstagramProfileFactory.create(username=username)
            mock_handler.return_value = profile
            
            self.mock_client.get_profile.return_value = {'username': username}
            
            # Act
            self.service.import_profile(username, task=task)
            
            # Assert
            task.refresh_from_db()
            assert task.status == 'completed'
            assert task.started_at is not None
            assert task.completed_at is not None
            assert task.items_scraped == 1
    
    @patch('instagram_manager.services.post_service.PostService')
    def test_import_profile_with_posts(self, mock_post_service_class):
        """Тест импорта профиля с постами"""
        # Arrange
        username = "test_user"
        posts_limit = 10
        
        # Создаем мок для сервиса постов
        mock_post_service = Mock()
        mock_post_service_class.return_value = mock_post_service
        mock_post_service.import_posts_for_profile.return_value = [
            Mock(id=i) for i in range(posts_limit)
        ]
        
        # Мокаем ProfileHandler
        with patch('instagram_manager.instagram_api.data_handlers.profile_handler.ProfileHandler.process_profile_data') as mock_handler:
            profile = InstagramProfileFactory.create(username=username)
            mock_handler.return_value = profile
            
            self.mock_client.get_profile.return_value = {'username': username}
            
            # Act
            result = self.service.import_profile(
                username, 
                import_posts=True, 
                posts_limit=posts_limit
            )
            
            # Assert
            assert result == profile
            mock_post_service.import_posts_for_profile.assert_called_once()
            call_args = mock_post_service.import_posts_for_profile.call_args
            assert call_args[1]['profile'] == profile
            assert call_args[1]['limit'] == posts_limit
    
    def test_import_profile_api_error(self):
        """Тест обработки ошибки API при импорте"""
        # Arrange
        username = "test_user"
        self.mock_client.get_profile.side_effect = Exception("API Error")
        
        # Act & Assert
        with pytest.raises(Exception) as context:
            self.service.import_profile(username)
        
        assert "API Error" in str(context.value)
    
    def test_update_profile(self):
        """Тест обновления существующего профиля"""
        # Arrange
        profile = InstagramProfileFactory.create(
            username="test_user",
            follower_count=1000
        )
        
        # Мокаем обновленные данные
        updated_api_data = {
            'username': profile.username,
            'edge_followed_by': {'count': 2000}
        }
        
        self.mock_client.get_profile.return_value = updated_api_data
        
        with patch('instagram_manager.instagram_api.data_handlers.profile_handler.ProfileHandler.process_profile_data') as mock_handler:
            # Создаем обновленный профиль
            updated_profile = InstagramProfileFactory.build(
                username=profile.username,
                follower_count=2000
            )
            mock_handler.return_value = updated_profile
            updated_profile.save = Mock()
            
            # Act
            result = self.service.update_profile(profile)
            
            # Assert
            assert result == updated_profile
            self.mock_client.get_profile.assert_called_once_with(profile.username)
    
    def test_get_profile_stats(self):
        """Тест получения статистики профиля"""
        # Arrange
        profile = InstagramProfileFactory.create(follower_count=10000)
        
        # Создаем посты с метриками
        posts = []
        for i in range(5):
            post = InstagramPostFactory.create(
                profile=profile,
                like_count=1000 + i * 100,
                comment_count=50 + i * 10,
                view_count=5000 if i % 2 == 0 else 0,  # Только для видео
                post_type='video' if i % 2 == 0 else 'photo'
            )
            posts.append(post)
        
        # Act
        stats = self.service.get_profile_stats(profile)
        
        # Assert
        assert stats['total_posts'] == 5
        # Подсчитаем реальные значения на основе созданных постов
        expected_likes = sum(post.like_count for post in posts)
        expected_comments = sum(post.comment_count for post in posts)
        expected_views = sum(post.view_count or 0 for post in posts if post.post_type == 'video')
        
        assert stats['total_likes'] == expected_likes
        assert stats['total_comments'] == expected_comments
        assert stats['total_views'] == expected_views
        assert stats['engagement_rate'] > 0
        assert stats['avg_likes_per_post'] == expected_likes // 5
        assert stats['avg_comments_per_post'] == expected_comments // 5
    
    def test_search_profiles(self):
        """Тест поиска профилей"""
        # Arrange
        # Создаем несколько профилей
        InstagramProfileFactory.create(username="fashion_blogger", full_name="Fashion Blogger")
        InstagramProfileFactory.create(username="tech_guru", full_name="Tech Fashion Expert")
        InstagramProfileFactory.create(username="food_lover", full_name="Food Lover")
        
        # Act
        results = self.service.search_profiles("fashion")
        
        # Assert
        assert len(results) == 2
        usernames = [p.username for p in results]
        assert "fashion_blogger" in usernames
        assert "tech_guru" in usernames
        assert "food_lover" not in usernames
    
    def test_import_profile_with_skip_media_download(self):
        """Тест импорта профиля с пропуском загрузки медиа"""
        # Arrange
        username = "test_user"
        
        with patch('instagram_manager.instagram_api.data_handlers.profile_handler.ProfileHandler.process_profile_data') as mock_handler:
            profile = InstagramProfileFactory.create(username=username)
            mock_handler.return_value = profile
            
            self.mock_client.get_profile.return_value = {'username': username}
            
            # Act
            result = self.service.import_profile(
                username,
                skip_media_download=True
            )
            
            # Assert
            assert result == profile
            
            # Проверяем что задача для постов создается с флагом skip_media_download
            if InstagramScrapingTask.objects.filter(task_type='posts').exists():
                posts_task = InstagramScrapingTask.objects.get(task_type='posts')
                assert posts_task.skip_media_download
    
    @patch('instagram_manager.services.profile_service.logger')
    def test_import_profile_with_logging(self, mock_logger):
        """Тест логирования при импорте профиля"""
        # Arrange
        username = "test_user"
        
        with patch('instagram_manager.instagram_api.data_handlers.profile_handler.ProfileHandler.process_profile_data') as mock_handler:
            profile = InstagramProfileFactory.create(username=username)
            mock_handler.return_value = profile
            
            self.mock_client.get_profile.return_value = {'username': username}
            
            # Act
            self.service.import_profile(username, posts_limit=10)
            
            # Assert
            mock_logger.info.assert_any_call(
                f"Fetching profile data for {username} with posts_limit=10"
            )
    
    def test_profile_stats_empty_profile(self):
        """Тест статистики для профиля без постов"""
        # Arrange
        profile = InstagramProfileFactory.create(follower_count=1000)
        
        # Act
        stats = self.service.get_profile_stats(profile)
        
        # Assert
        assert stats['total_posts'] == 0
        assert stats['total_likes'] == 0
        assert stats['total_comments'] == 0
        assert stats['total_views'] == 0
        assert stats['engagement_rate'] == 0.0
        assert stats['avg_likes_per_post'] == 0
        assert stats['avg_comments_per_post'] == 0


if __name__ == '__main__':
    import pytest
    pytest.main([__file__])