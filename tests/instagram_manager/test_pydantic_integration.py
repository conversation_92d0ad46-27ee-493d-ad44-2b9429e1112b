"""Integration tests for Pydantic implementation."""

import pytest
from unittest.mock import Mock, patch
from datetime import datetime
from django.test import TestCase

from instagram_manager.instagram_api import (
    BrightDataClient,
    ProfileNotFoundError,
)
from instagram_manager.services.profile_service import ProfileService
from instagram_manager.services.post_service import PostService
from instagram_manager.models import InstagramProfile, InstagramPost
from instagram_manager.schemas.brightdata import (
    InstagramProfileResponse,
    InstagramPostResponse,
)


class TestPydanticIntegration(TestCase):
    """Test Pydantic integration with services."""

    def setUp(self):
        """Set up test dependencies."""
        self.mock_client = Mock(spec=BrightDataClient)

    @patch(
        "instagram_manager.instagram_api.data_handlers.profile_handler.ProfileHandler.process_profile_data"
    )
    @patch("instagram_manager.services.profile_service.InstagramScrapingTask")
    @patch("instagram_manager.services.profile_service.BrightDataClient")
    def test_profile_service_with_pydantic(
        self, mock_client_class, mock_task_model, mock_process_profile
    ):
        """Test ProfileService works with Pydantic models."""
        # Mock the task model to avoid database access
        mock_task_model.objects.filter.return_value.order_by.return_value.first.return_value = (
            None
        )

        # Prepare mock response
        mock_profile_response = InstagramProfileResponse(
            pk="123456789",
            username="testuser",
            name="Test User",
            bio="Test bio",
            followers=1000,
            following=500,
            media_count=50,
            is_verified=True,
            is_private=False,
            profile_pic_url_hd="https://example.com/pic.jpg",
        )

        # Configure mock client
        mock_client_instance = Mock()
        mock_client_instance.get_profile.return_value = mock_profile_response
        mock_client_class.return_value = mock_client_instance

        # Mock profile processing to return a mock InstagramProfile
        mock_profile = Mock(spec=InstagramProfile)
        mock_profile.username = "testuser"
        mock_profile.profile_id = "123456789"
        mock_profile.follower_count = 1000
        mock_profile.is_verified = True
        mock_process_profile.return_value = mock_profile

        # Test service
        service = ProfileService()
        profile = service.import_profile("testuser")

        # Assertions
        assert profile == mock_profile
        assert profile.username == "testuser"
        assert profile.profile_id == "123456789"
        assert profile.follower_count == 1000
        assert profile.is_verified is True

        # Verify client was called correctly
        mock_client_instance.get_profile.assert_called_once_with("testuser")
        mock_process_profile.assert_called_once_with(mock_profile_response)

    @patch("instagram_manager.services.post_service.settings")
    @patch(
        "instagram_manager.instagram_api.data_handlers.post_handler.PostHandler.process_post_data"
    )
    @patch("instagram_manager.services.post_service.InstagramScrapingTask")
    @patch("instagram_manager.services.post_service.BrightDataClient")
    def test_post_service_with_pydantic(
        self, mock_client_class, mock_task_model, mock_process_post, mock_settings
    ):
        """Test PostService works with Pydantic models."""
        # Mock settings
        mock_settings.BRIGHTDATA_DATASETS = {"instagram_posts": "test_dataset_id"}

        # Mock the task model
        mock_task_instance = Mock()
        mock_task_model.objects.create.return_value = mock_task_instance

        # Create mock profile
        profile = Mock(spec=InstagramProfile)
        profile.username = "testuser"
        profile.profile_id = "123456789"
        profile.full_name = "Test User"
        profile.follower_count = 1000

        # Prepare mock response
        mock_posts = [
            InstagramPostResponse(
                pk="post1",
                content_id="ABC123",
                caption="Test post #test @friend",
                like_count=100,
                comment_count=10,
                timestamp=datetime.now(),
                url="https://instagram.com/p/DKZ7A0XpB1U/",
                media=[
                    {"media_id": "m1", "media_type": "photo", "url": "https://example.com/1.jpg"}
                ],
            ),
            InstagramPostResponse(
                pk="post2",
                content_id="DEF456",
                caption="Another post #coding",
                like_count=200,
                comment_count=20,
                timestamp=datetime.now(),
                url="https://instagram.com/p/DKVPW5_T6dZ/",
                is_video=True,
                video_duration=30,
            ),
        ]

        # Configure mock
        mock_client_instance = Mock()
        mock_client_instance.get_posts.return_value = mock_posts
        mock_client_class.return_value = mock_client_instance

        # Mock post processing to return mock InstagramPost objects
        mock_post1 = Mock(spec=InstagramPost)
        mock_post1.shortcode = "DKZ7A0XpB1U"
        mock_post1.caption = "Test post #test @friend"
        mock_post1.like_count = 100
        mock_post1.post_type = "image"
        mock_hashtag1 = Mock()
        mock_hashtag1.filter.return_value.exists.return_value = True
        mock_post1.hashtags = mock_hashtag1

        mock_post2 = Mock(spec=InstagramPost)
        mock_post2.shortcode = "DKVPW5_T6dZ"
        mock_post2.caption = "Another post #coding"
        mock_post2.like_count = 200
        mock_post2.post_type = "video"
        mock_hashtag2 = Mock()
        mock_hashtag2.filter.return_value.exists.return_value = True
        mock_post2.hashtags = mock_hashtag2

        mock_process_post.side_effect = [mock_post1, mock_post2]

        # Test service
        service = PostService()
        posts = service.import_posts_for_profile(profile, limit=2)

        # Assertions
        assert len(posts) == 2
        assert posts[0] == mock_post1
        assert posts[1] == mock_post2

        # Check first post
        assert posts[0].shortcode == "DKZ7A0XpB1U"
        assert posts[0].caption == "Test post #test @friend"
        assert posts[0].like_count == 100
        assert posts[0].hashtags.filter("name", "test").exists()

        # Check second post
        assert posts[1].shortcode == "DKVPW5_T6dZ"
        assert posts[1].post_type == "video"
        assert posts[1].hashtags.filter("name", "coding").exists()

        # Verify client was called correctly
        mock_client_instance.get_posts.assert_called_once_with(
            profile.username, start_date="", end_date="", post_types=None
        )

        # Verify post processing was called
        assert mock_process_post.call_count == 2
        # Check that the calls were made with the right arguments
        # We need to check the actual calls made rather than exact object comparison
        calls = mock_process_post.call_args_list
        assert len(calls) == 2
        # First call should be with first post and profile
        assert calls[0][0][0].post_id == "post1"
        assert calls[0][0][1] == profile
        # Second call should be with second post and profile  
        assert calls[1][0][0].post_id == "post2"
        assert calls[1][0][1] == profile

    def test_error_handling_with_pydantic(self):
        """Test error handling with Pydantic validation."""
        # Test invalid data that would fail Pydantic validation
        from pydantic import ValidationError

        # Missing required fields
        with pytest.raises(ValidationError) as cm:
            InstagramProfileResponse(username="test")  # Missing pk

        errors = cm.value.errors()
        assert any(e["type"] == "missing" for e in errors)

        # Invalid data types
        with pytest.raises(ValidationError) as cm:
            InstagramPostResponse(
                pk="123",
                content_id="ABC",
                date_posted="invalid-date",  # Invalid date format
                url="https://test.com",
            )

        errors = cm.value.errors()
        assert any("date_posted" in str(e) for e in errors)

    @patch("instagram_manager.services.profile_service.InstagramScrapingTask")
    @patch("instagram_manager.services.profile_service.BrightDataClient")
    def test_api_error_handling(self, mock_client_class, mock_task_model):
        """Test handling of API errors with Pydantic."""
        # Mock the task model to avoid database access
        mock_task_model.objects.filter.return_value.order_by.return_value.first.return_value = (
            None
        )

        # Configure mock to raise errors
        mock_client_instance = Mock()
        mock_client_instance.get_profile.side_effect = ProfileNotFoundError(
            "Profile not found"
        )
        mock_client_class.return_value = mock_client_instance

        # Test service handles error correctly
        service = ProfileService()

        with pytest.raises(ProfileNotFoundError):
            service.import_profile("nonexistent_user")

    def test_data_transformation(self):
        """Test data transformation through Pydantic models."""
        # Test complex data transformation
        raw_post_data = {
            "pk": "123",
            "content_id": "ABC",
            "caption": "Check out this post! #amazing #python @friend1 @friend2",
            "like_count": "1500",  # String that should be converted to int
            "comment_count": 50,
            "timestamp": 1609459200,  # Unix timestamp
            "location": ["New York", "USA"],  # List that should be joined
            "media": [
                {
                    "id": "m1",
                    "type": "PHOTO",
                    "url": "https://1.jpg",
                },  # Type should be lowercase
                {
                    "id": "m2",
                    "type": "VIDEO",
                    "url": "https://2.mp4",
                    "duration": "45.5",
                },
            ],
            "url": "https://instagram.com/p/ABC/",
        }

        # Create model from raw data
        post = InstagramPostResponse(**raw_post_data)

        # Verify transformations
        assert post.likes == 1500  # Converted from string
        assert isinstance(post.date_posted, datetime)  # Converted from timestamp
        assert post.location == "New York, USA"  # Joined from list
        assert post.hashtags == ["amazing", "python"]  # Extracted from caption
        assert post.mentions == ["friend1", "friend2"]  # Extracted from caption
        assert len(post.post_content) == 2
        assert post.post_content[0].type == "photo"  # Normalized to lowercase
        assert post.post_content[1].duration == 45  # Converted to int
        # Since one of the media items is a video, content_type should be "Video"
        assert post.content_type == "Video"  # Determined from media content


class TestPydanticPerformance(TestCase):
    """Test performance implications of Pydantic."""

    def test_batch_validation_performance(self):
        """Test performance of validating large batches of data."""
        import time

        # Create large batch of test data
        batch_size = 1000
        raw_posts = [
            {
                "pk": f"post{i}",
                "content_id": f"CODE{i}",
                "caption": f"Test post {i} #test{i}",
                "like_count": i * 10,
                "comment_count": i,
                "timestamp": 1609459200 + i,
                "url": "https://instagram.com/p/CNjqoQbsNd9/",
            }
            for i in range(batch_size)
        ]

        # Measure validation time
        start_time = time.time()
        validated_posts = [InstagramPostResponse(**post) for post in raw_posts]
        validation_time = time.time() - start_time

        # Assertions
        assert len(validated_posts) == batch_size
        assert all(isinstance(p, InstagramPostResponse) for p in validated_posts)
        assert validation_time < 1.0  # Should be fast even for 1000 items

        print(f"Validated {batch_size} posts in {validation_time:.3f} seconds")


class TestBackwardCompatibility(TestCase):
    """Test backward compatibility with old code."""

    def test_model_dump_compatibility(self):
        """Test converting Pydantic models back to dicts for legacy code."""
        # Create Pydantic model
        profile = InstagramProfileResponse(
            pk="123", username="test", followers=1000, following=500, media_count=50
        )

        # Convert to dict for legacy code
        profile_dict = profile.model_dump(by_alias=True)

        # Verify structure matches old format
        assert profile_dict["pk"] == "123"
        assert profile_dict["username"] == "test"
        assert profile_dict["followers"] == 1000
        assert "profile_id" not in profile_dict  # Uses alias 'pk'
