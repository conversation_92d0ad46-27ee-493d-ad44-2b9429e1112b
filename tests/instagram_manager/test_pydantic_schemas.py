"""Tests for Instagram Pydantic schemas."""

from datetime import UTC, datetime

import pytest
from pydantic import ValidationError

from core.schemas.validators import CommonValidators, InstagramValidators
from instagram_manager.schemas.brightdata import (
    BrightDataError,
    InstagramCommentResponse,
    InstagramPostResponse,
    InstagramProfileResponse,
    MediaItem,
)


class TestMediaItem:
    """Test MediaItem model."""

    def test_valid_media_item(self):
        """Test creating valid media item."""
        media = MediaItem(
            id="123",
            type="photo",
            url="https://example.com/photo.jpg",
            width=1080,
            height=1920,
        )

        assert media.media_id == "123"  # Actual attribute name
        assert media.type == "photo"  # Using backward compatibility property
        assert media.url == "https://example.com/photo.jpg"
        assert media.width == 1080
        assert media.height == 1920
        assert media.thumbnail_url is None
        assert media.duration is None

    def test_video_media_item(self):
        """Test video media item with duration."""
        media = MediaItem(
            id="456",
            type="video",
            url="https://example.com/video.mp4",
            thumbnail_url="https://example.com/thumb.jpg",  # Correct field name
            duration="30.5",
            width=720,
            height=1280,
        )

        assert media.type == "video"
        assert media.duration == 30  # Should be converted to int
        assert media.thumbnail_url == "https://example.com/thumb.jpg"

    def test_duration_parsing(self):
        """Test various duration formats."""
        # String duration
        media1 = MediaItem(id="1", type="video", url="https://test.com", duration="45.7")  # type: ignore
        assert media1.duration == 45

        # Int duration
        media2 = MediaItem(id="2", type="video", url="https://test.com", duration=60)
        assert media2.duration == 60

        # Invalid duration
        media3 = MediaItem(
            id="3", type="video", url="https://test.com", duration="invalid"  # type: ignore
        )
        assert media3.duration is None

    def test_type_normalization(self):
        """Test media type normalization."""
        media = MediaItem(id="123", type="VIDEO", url="https://test.com")
        assert media.type == "video"  # Using backward compatibility property


class TestInstagramProfileResponse:
    """Test InstagramProfileResponse model."""

    def test_valid_profile(self):
        """Test creating valid profile."""
        profile = InstagramProfileResponse(
            pk="12345",
            username="testuser",
            name="Test User",
            bio="Test bio",
            followers=1000,
            following=500,
            media_count=50,
            is_verified=True,
            is_private=False,
            profile_pic_url_hd="https://example.com/pic.jpg",
        )

        assert profile.profile_id == "12345"
        assert profile.username == "testuser"
        assert profile.full_name == "Test User"
        assert profile.biography == "Test bio"
        assert profile.follower_count == 1000
        assert profile.following_count == 500
        assert profile.post_count == 50
        assert profile.is_verified is True
        assert profile.is_private is False

    def test_required_fields(self):
        """Test that required fields are validated."""
        # Missing username
        with pytest.raises(ValidationError) as exc_info:
            InstagramProfileResponse(pk="12345")

        errors = exc_info.value.errors()
        assert any(e["loc"] == ("username",) for e in errors)

        # Empty username
        with pytest.raises(ValidationError) as exc_info:
            InstagramProfileResponse(pk="12345", username="")

        errors = exc_info.value.errors()
        assert any("Cannot be empty" in str(e) for e in errors)

    def test_count_conversion(self):
        """Test count fields conversion."""
        profile = InstagramProfileResponse(
            pk="123",
            username="test",
            followers="1.5K",
            following="2M",
            media_count="100",
        )

        assert profile.follower_count == 1500  # "1.5K" = 1500
        assert profile.following_count == 2000000  # "2M" = 2000000
        assert profile.post_count == 100

    def test_default_values(self):
        """Test default values for optional fields."""
        profile = InstagramProfileResponse(pk="123", username="test")

        assert profile.full_name == ""
        assert profile.biography == ""
        assert profile.follower_count == 0
        assert profile.is_verified is False
        assert profile.is_business is False
        assert profile.external_url is None


class TestInstagramPostResponse:
    """Test InstagramPostResponse model."""

    def test_valid_post(self):
        """Test creating valid post."""
        post = InstagramPostResponse(
            pk="post123",
            shortcode="DKZ7A0XpB1U",  # Using shortcode directly
            caption="Test post #test @user",
            like_count=100,
            comment_count=10,
            timestamp=1609459200,  # 2021-01-01 00:00:00 UTC
            url="https://instagram.com/p/DKZ7A0XpB1U/",
        )

        assert post.post_id == "post123"
        assert post.shortcode == "DKZ7A0XpB1U"
        assert post.description == "Test post #test @user"
        assert post.likes == 100
        assert post.num_comments == 10
        assert isinstance(post.date_posted, datetime)
        assert post.hashtags == ["test"]  # Extracted from caption
        assert post.mentions == ["user"]  # Extracted from caption

    def test_timestamp_parsing(self):
        """Test various timestamp formats."""
        # Unix timestamp
        post1 = InstagramPostResponse(
            pk="1", shortcode="A", timestamp=1609459200, url="https://test.com"
        )
        assert post1.date_posted.year == 2021

        # ISO string
        post2 = InstagramPostResponse(
            pk="2",
            shortcode="B",
            timestamp="2021-01-01T00:00:00Z",
            url="https://test.com",
        )
        assert post2.date_posted.year == 2021

        # Already datetime
        now = datetime.now()
        post3 = InstagramPostResponse(
            pk="3", shortcode="C", timestamp=now, url="https://test.com"
        )
        assert post3.date_posted == now

    def test_location_parsing(self):
        """Test location field parsing."""
        # String location
        post1 = InstagramPostResponse(
            pk="1",
            shortcode="A",
            timestamp=1234567890,
            location="New York, USA",
            url="https://test.com",
        )
        assert post1.location == "New York, USA"

        # List location
        post2 = InstagramPostResponse(
            pk="2",
            shortcode="B",
            timestamp=1234567890,
            location=["New York", "USA"],
            url="https://test.com",
        )
        assert post2.location == "New York, USA"

        # Dict location
        post3 = InstagramPostResponse(
            pk="3",
            shortcode="C",
            timestamp=1234567890,
            location={"name": "Times Square"},
            url="https://test.com",
        )
        assert post3.location == "Times Square"

    def test_media_parsing(self):
        """Test post_content/media parsing."""
        # List of media
        post = InstagramPostResponse(
            pk="1",
            shortcode="A",
            timestamp=1234567890,
            media=[
                {"media_id": "m1", "media_type": "photo", "url": "https://test1.jpg"},
                {"media_id": "m2", "media_type": "video", "url": "https://test2.mp4"},
            ],
            url="https://test.com",
        )
        assert len(post.post_content) == 2
        assert all(isinstance(m, MediaItem) for m in post.post_content)

    def test_content_type_determination(self):
        """Test automatic content type determination."""
        # Video post
        post1 = InstagramPostResponse(
            pk="1",
            shortcode="A",
            timestamp=1234567890,
            is_video=True,
            url="https://test.com",
        )
        assert post1.content_type == "Video"

        # Carousel post
        post2 = InstagramPostResponse(
            pk="2",
            shortcode="B",
            timestamp=1234567890,
            media=[
                {"media_id": "m1", "media_type": "photo", "url": "https://1.jpg"},
                {"media_id": "m2", "media_type": "photo", "url": "https://2.jpg"},
            ],
            url="https://test.com",
        )
        assert post2.content_type == "Carousel"


class TestInstagramCommentResponse:
    """Test InstagramCommentResponse model."""

    def test_valid_comment(self):
        """Test creating valid comment."""
        comment = InstagramCommentResponse(
            comment_id="comment123",  # Required field
            comment="Great post!",  # Using alias for 'text'
            comment_user="commenter",  # Using alias for 'author_username'
            comment_date=1609459200,  # Using alias for 'date_created'
            user_id="user456",
        )

        assert comment.comment_id == "comment123"
        assert comment.text == "Great post!"
        assert comment.author_username == "commenter"
        assert isinstance(comment.date_created, datetime)
        assert comment.user_id == "user456"
        assert comment.like_count == 0  # Default
        assert comment.is_verified_author is False  # Default
        assert comment.has_replies is False  # Default
        assert comment.replies_count == 0  # Default
        assert comment.is_pinned is False  # Default
        assert comment.is_hidden is False  # Default
        assert comment.parent_comment_id is None  # Default
        assert comment.user_profile_pic_url is None  # Default

    def test_comment_with_all_fields(self):
        """Test comment with all fields populated."""
        comment = InstagramCommentResponse(
            comment_id="comment789",  # Required field
            comment="This is a pinned comment with replies",  # Using alias for 'text'
            comment_user="verified_user",  # Using alias for 'author_username'
            comment_date="2024-01-15T10:30:00Z",  # Using alias for 'date_created'
            user_id="user123",
            likes_number=150,  # Using alias for 'like_count'
            is_verified_user=True,  # Using alias for 'is_verified_author'
            has_replies=True,
            replies_number=10,  # Using alias for 'replies_count'
            reply_to_comment_id="parent123",  # Using alias
            is_pinned=True,
            is_hidden=False,
            profile_pic_url="https://example.com/avatar.jpg",  # Using alias
        )

        assert comment.comment_id == "comment789"
        assert comment.comment_id == "comment789"
        assert comment.text == "This is a pinned comment with replies"
        assert comment.like_count == 150
        assert comment.is_verified_author is True
        assert comment.has_replies is True
        assert comment.replies_count == 10
        assert comment.parent_comment_id == "parent123"
        assert comment.is_pinned is True
        assert comment.is_hidden is False
        assert comment.user_profile_pic_url == "https://example.com/avatar.jpg"

    def test_comment_reply_hierarchy(self):
        """Test comment reply with parent reference."""
        reply = InstagramCommentResponse(
            comment_id="reply456",  # Required field
            comment="Thanks for your comment!",  # Using alias for 'text'
            comment_user="post_author",  # Using alias for 'author_username'
            comment_date=datetime.now(),  # Using alias for 'date_created'
            user_id="author123",
            reply_to_comment_id="comment123",  # Using alias for 'parent_comment_id'
            is_verified_user=True,  # Using alias for 'is_verified_author'
        )

        assert reply.parent_comment_id == "comment123"
        assert reply.is_verified_author is True

    def test_hidden_comment(self):
        """Test hidden/deleted comment."""
        hidden = InstagramCommentResponse(
            comment_id="hidden123",  # Required field
            comment="[Comment deleted]",  # Using alias for 'text'
            comment_user="deleted",  # Using alias for 'author_username'
            comment_date=1609459200,  # Using alias for 'date_created'
            user_id="deleted_user",
            is_hidden=True,
            likes_number=0,  # Using alias for 'like_count'
        )

        assert hidden.is_hidden is True
        assert hidden.text == "[Comment deleted]"

    def test_timestamp_parsing(self):
        """Test various timestamp formats."""
        # Unix timestamp
        comment1 = InstagramCommentResponse(
            comment_id="1",  # Required field
            comment="Test",  # Using alias for 'text'
            comment_user="user1",  # Using alias for 'author_username'
            comment_date=1609459200,  # Using alias for 'date_created'
            user_id="u1",
        )
        assert isinstance(comment1.date_created, datetime)

        # ISO format with Z
        comment2 = InstagramCommentResponse(
            comment_id="2",  # Required field
            comment="Test",  # Using alias for 'text'
            comment_user="user2",  # Using alias for 'author_username'
            comment_date="2024-01-15T10:30:00Z",  # Using alias for 'date_created'
            user_id="u2",
        )
        assert isinstance(comment2.date_created, datetime)

        # Datetime object
        now = datetime.now()
        comment3 = InstagramCommentResponse(
            comment_id="3",  # Required field
            comment="Test",  # Using alias for 'text'
            comment_user="user3",  # Using alias for 'author_username'
            comment_date=now,  # Using alias for 'date_created'
            user_id="u3",
        )
        assert comment3.date_created == now

    def test_comment_validation_errors(self):
        """Test validation errors for comments."""
        # Missing required fields
        with pytest.raises(ValidationError) as exc_info:
            InstagramCommentResponse(
                user_id="u1",
                comment_user="user1",  # Using alias
                comment_date=1609459200,  # Using alias
            )
        assert "id" in str(exc_info.value)  # Missing 'id' (comment_id) field

        # Invalid timestamp - should use fallback to current time
        comment_with_invalid_date = InstagramCommentResponse(
            id="123",  # Required field
            comment="Invalid timestamp",  # Using alias for 'text'
            comment_user="user1",  # Using alias for 'author_username'
            comment_date="not-a-timestamp",  # Using alias for 'date_created'
            user_id="u1",
        )
        # The schema has a fallback that returns current time if parsing fails
        assert comment_with_invalid_date.date_created is not None
        # The date should be close to now (within 1 minute)
        from datetime import datetime
        now = datetime.now(UTC)
        assert abs((comment_with_invalid_date.date_created - now).total_seconds()) < 60

    def test_comment_field_aliases(self):
        """Test that field aliases work correctly."""
        data = {
            "comment_id": "comment123",  # Required field
            "comment": "Test comment",  # Using alias for 'text'
            "comment_user": "testuser",  # Using alias for 'author_username'
            "comment_date": 1609459200,  # Using alias for 'date_created'
            "user_id": "user456",
            "reply_to_comment_id": "parent789",  # Using alias for 'parent_comment_id'
            "profile_pic_url": "https://example.com/pic.jpg",
        }

        comment = InstagramCommentResponse(**data)

        # Check that aliases are properly mapped
        assert comment.comment_id == "comment123"
        assert comment.text == "Test comment"
        assert comment.parent_comment_id == "parent789"
        assert comment.user_profile_pic_url == "https://example.com/pic.jpg"

        # Check model_dump with aliases
        dumped = comment.model_dump(by_alias=True)
        assert dumped["comment"] == "Test comment"
        assert dumped["reply_to_comment_id"] == "parent789"
        assert dumped["profile_pic_url"] == "https://example.com/pic.jpg"


class TestValidationHelpers:
    """Test validation helper functions."""

    def test_clean_text(self):
        """Test text cleaning."""
        assert CommonValidators.clean_text("  Hello  World  ") == "Hello World"
        assert CommonValidators.clean_text("Line1\n\nLine2", preserve_newlines=True) == "Line1\n\nLine2"  # Preserves line breaks
        assert CommonValidators.clean_text(None) == ""
        assert CommonValidators.clean_text("") == ""

    def test_extract_hashtags(self):
        """Test hashtag extraction."""
        text = "Check out #python and #Django! #python is great"
        hashtags = CommonValidators.extract_hashtags(text)

        # Should convert to lowercase and remove duplicates
        assert hashtags == ["python", "django"]  # lowercase
        assert len(hashtags) == 2

    def test_extract_mentions(self):
        """Test mention extraction."""
        text = "Thanks @user1 and @user2! @user1 is awesome"
        mentions = CommonValidators.extract_mentions(text)

        # Should convert to lowercase and remove duplicates
        assert mentions == ["user1", "user2"]
        assert len(mentions) == 2

    def test_validate_username(self):
        """Test Instagram username validation."""
        # Valid usernames (returns lowercase)
        assert InstagramValidators.validate_username("valid_user.123") == "valid_user.123"
        assert (
            InstagramValidators.validate_username("@Username") == "username"
        )  # Remove @ and lowercase

        # Invalid usernames
        with pytest.raises(ValueError):
            InstagramValidators.validate_username("")

        with pytest.raises(ValueError):
            InstagramValidators.validate_username("user-name")  # Hyphen not allowed

        with pytest.raises(ValueError):
            InstagramValidators.validate_username("a" * 31)  # Too long

    def test_safe_int_conversion(self):
        """Test safe integer conversion."""
        assert CommonValidators.safe_int_conversion(123) == 123
        assert CommonValidators.safe_int_conversion("456") == 456
        assert CommonValidators.safe_int_conversion("1.5k") == 1500
        assert CommonValidators.safe_int_conversion("2M") == 2000000
        assert CommonValidators.safe_int_conversion("1,234") == 1234
        assert CommonValidators.safe_int_conversion(None) == 0
        assert CommonValidators.safe_int_conversion("invalid") == 0


class TestBrightDataError:
    """Test BrightDataError model."""

    def test_error_creation(self):
        """Test creating error model."""
        error = BrightDataError(
            error="ValidationError", message="Invalid data", code="400"
        )

        assert error.error == "ValidationError"
        assert error.message == "Invalid data"
        assert error.code == "400"

    def test_from_exception(self):
        """Test creating from exception."""
        try:
            raise ValueError("Test error")
        except Exception as e:
            error = BrightDataError.from_exception(e)

            assert error.error == "ValueError"
            assert error.message == "Test error"
            assert error.details is not None
