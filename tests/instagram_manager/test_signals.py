"""
Тесты для signals Instagram Manager.

Проверяет корректность удаления файлов при удалении моделей.
"""

import os
import tempfile
from django.test import TestCase
from django.core.files.base import ContentFile
from django.utils import timezone
from datetime import datetime

from instagram_manager.models import (
    InstagramProfile,
    InstagramPost,
    InstagramMedia,
)
from instagram_manager.signals import cleanup_empty_directories


class InstagramSignalsTestCase(TestCase):
    """Тесты для проверки signals при удалении объектов."""
    
    def setUp(self):
        """Создание тестовых данных."""
        # Создаем временную директорию для медиа файлов
        self.temp_media_root = tempfile.mkdtemp()
        
        # Создаем профиль
        self.profile = InstagramProfile.objects.create(
            profile_id="test_profile_123",
            username="testuser",
            full_name="Test User",
            follower_count=100,
            following_count=50,
            post_count=10,
        )
        
        # Создаем пост
        self.post = InstagramPost.objects.create(
            external_id="test_post_123",
            profile=self.profile,
            shortcode="ABC123",
            post_type="photo",
            caption="Test post",
            like_count=50,
            comment_count=5,
            posted_at=timezone.make_aware(datetime(2024, 1, 1, 12, 0, 0)),
            post_url="https://instagram.com/p/ABC123/",
        )
    
    def test_delete_media_removes_files(self):
        """Тест удаления файлов при удалении InstagramMedia."""
        with self.settings(MEDIA_ROOT=self.temp_media_root):
            # Создаем медиа с файлом
            media = InstagramMedia.objects.create(
                external_id="test_media_123",
                post=self.post,
                media_type="photo",
                media_url="https://example.com/photo.jpg",
                width=1080,
                height=1080,
                order_index=0,
            )
            
            # Создаем тестовый файл
            test_content = b"Test image content"
            media.local_path.save(
                'instagram/media/test_photo.jpg',
                ContentFile(test_content)
            )
            media.is_downloaded = True
            media.save()
            
            # Проверяем, что файл существует
            file_path = media.local_path.path
            assert os.path.exists(file_path)
            
            # Удаляем медиа
            media.delete()
            
            # Проверяем, что файл был удален
            assert not os.path.exists(file_path)
    
    def test_delete_video_removes_thumbnail(self):
        """Тест удаления миниатюры при удалении видео."""
        with self.settings(MEDIA_ROOT=self.temp_media_root):
            # Создаем видео медиа
            media = InstagramMedia.objects.create(
                external_id="test_video_123",
                post=self.post,
                media_type="video",
                media_url="https://example.com/video.mp4",
                thumbnail_url="https://example.com/thumb.jpg",
                width=1080,
                height=1920,
                duration=30,
                order_index=0,
            )
            
            # Создаем файлы видео и миниатюры
            media.local_path.save(
                'instagram/media/test_video.mp4',
                ContentFile(b"Video content")
            )
            media.is_downloaded = True
            media.save()
            
            # Создаем миниатюру и устанавливаем путь в модели
            # Создаем миниатюру через модель
            thumbnail_content = ContentFile(b"Thumbnail content")
            media.local_thumbnail_path.save('test_video_thumbnail.jpg', thumbnail_content)
            
            # Сохраняем пути для проверки
            video_path = media.local_path.path
            thumbnail_path = media.local_thumbnail_path.path
            
            # Проверяем существование файлов
            assert os.path.exists(video_path)
            assert os.path.exists(thumbnail_path)
            
            # Удаляем медиа
            media.delete()
            
            # Проверяем, что оба файла удалены
            assert not os.path.exists(video_path)
            assert not os.path.exists(thumbnail_path)
    
    def test_delete_post_cascades_to_media(self):
        """Тест каскадного удаления медиа при удалении поста."""
        # Создаем несколько медиа для поста
        InstagramMedia.objects.create(
            external_id="media_1",
            post=self.post,
            media_type="photo",
            media_url="https://example.com/1.jpg",
            width=1080,
            height=1080,
            order_index=0,
        )
        
        InstagramMedia.objects.create(
            external_id="media_2",
            post=self.post,
            media_type="photo",
            media_url="https://example.com/2.jpg",
            width=1080,
            height=1080,
            order_index=1,
        )
        
        # Проверяем, что медиа существуют
        assert self.post.media.count() == 2
        
        # Удаляем пост
        self.post.delete()
        
        # Проверяем, что медиа тоже удалены
        # Используем другой подход, так как пост уже удален
        self.assertEqual(
            InstagramMedia.objects.filter(external_id__in=["media_1", "media_2"]).count(),
            0
        )
    
    def test_delete_profile_cascades_to_posts_and_media(self):
        """Тест каскадного удаления постов и медиа при удалении профиля."""
        # Создаем дополнительный пост с медиа
        post2 = InstagramPost.objects.create(
            external_id="test_post_456",
            profile=self.profile,
            shortcode="DEF456",
            post_type="video",
            caption="Another test post",
            like_count=100,
            comment_count=10,
            posted_at=timezone.make_aware(datetime(2024, 1, 2, 12, 0, 0)),
            post_url="https://instagram.com/p/DEF456/",
        )
        
        # Создаем медиа для обоих постов
        InstagramMedia.objects.create(
            external_id="media_post1",
            post=self.post,
            media_type="photo",
            media_url="https://example.com/p1.jpg",
            width=1080,
            height=1080,
        )
        
        InstagramMedia.objects.create(
            external_id="media_post2",
            post=post2,
            media_type="video",
            media_url="https://example.com/p2.mp4",
            width=1080,
            height=1920,
            duration=60,
        )
        
        # Проверяем начальное состояние
        assert self.profile.posts.count() == 2
        self.assertEqual(
            InstagramMedia.objects.filter(post__profile=self.profile).count(),
            2
        )
        
        # Удаляем профиль
        profile_id = self.profile.profile_id
        self.profile.delete()
        
        # Проверяем каскадное удаление
        self.assertEqual(
            InstagramPost.objects.filter(profile__profile_id=profile_id).count(),
            0
        )
        self.assertEqual(
            InstagramMedia.objects.filter(
                post__profile__profile_id=profile_id
            ).count(),
            0
        )
    
    def test_cleanup_empty_directories(self):
        """Тест очистки пустых директорий."""
        with self.settings(MEDIA_ROOT=self.temp_media_root):
            # Создаем структуру директорий
            base_dir = os.path.join(self.temp_media_root, 'instagram/media')
            empty_dir1 = os.path.join(base_dir, 'empty1')
            empty_dir2 = os.path.join(base_dir, 'empty2/nested')
            
            os.makedirs(empty_dir1, exist_ok=True)
            os.makedirs(empty_dir2, exist_ok=True)
            
            # Проверяем, что директории существуют
            assert os.path.exists(empty_dir1)
            assert os.path.exists(empty_dir2)
            
            # Очищаем пустые директории
            cleaned = cleanup_empty_directories()
            
            # Проверяем, что пустые директории удалены
            assert not os.path.exists(empty_dir1)
            assert not os.path.exists(empty_dir2)
            assert cleaned > 0
    
    def tearDown(self):
        """Очистка после тестов."""
        # Удаляем временную директорию
        import shutil
        if hasattr(self, 'temp_media_root') and os.path.exists(self.temp_media_root):
            shutil.rmtree(self.temp_media_root)