"""
Tests for GCS-related functionality in InstagramMedia model.
"""

import pytest
from django.test import TestCase
from django.utils import timezone

from instagram_manager.models import InstagramMedia, InstagramPost, InstagramProfile


class TestInstagramMediaGCS(TestCase):
    """Test GCS-related functionality in InstagramMedia."""
    
    def setUp(self):
        """Set up test data."""
        # Create profile and post
        self.profile = InstagramProfile.objects.create(
            profile_id="test123",
            username="testuser",
            full_name="Test User"
        )
        
        self.post = InstagramPost.objects.create(
            external_id="post123",
            profile=self.profile,
            shortcode="ABC123",
            caption="Test post",
            posted_at=timezone.now()
        )
    
    def test_gcs_url_field_exists(self):
        """Test that gcs_url field exists."""
        media = InstagramMedia()
        assert hasattr(media, 'gcs_url')
        assert media.gcs_url is None
    
    def test_get_display_url_priority(self):
        """Test URL priority: GCS > Local > External."""
        media = InstagramMedia(
            external_id='123',
            post=self.post,
            media_url='https://instagram.com/photo.jpg'
        )
        
        # Only external URL
        assert media.get_display_url() == 'https://instagram.com/photo.jpg'
        
        # Add local file
        media.is_downloaded = True
        media.local_path.name = 'media/instagram/123.jpg'
        assert 'media/instagram/123.jpg' in media.get_display_url()
        
        # Add GCS URL (should take priority)
        media.gcs_url = 'https://storage.googleapis.com/bucket/123.jpg'
        assert media.get_display_url() == 'https://storage.googleapis.com/bucket/123.jpg'
    
    @pytest.mark.django_db
    def test_gcs_url_saves_correctly(self):
        """Test that gcs_url can be saved to database."""
        media = InstagramMedia.objects.create(
            external_id='test123',
            post=self.post,
            media_url='https://instagram.com/test.jpg',
            gcs_url='https://storage.googleapis.com/bucket/test.jpg'
        )
        
        # Reload from DB
        media_from_db = InstagramMedia.objects.get(id=media.id)
        assert media_from_db.gcs_url == 'https://storage.googleapis.com/bucket/test.jpg'
    
    def test_gcs_url_field_max_length(self):
        """Test that gcs_url field has appropriate max length."""
        media = InstagramMedia()
        field = media._meta.get_field('gcs_url')
        assert field.max_length == 1024
    
    def test_gcs_url_blank_and_null(self):
        """Test that gcs_url field allows blank and null values."""
        media = InstagramMedia()
        field = media._meta.get_field('gcs_url')
        assert field.blank is True
        assert field.null is True
    
    def test_get_display_url_with_no_urls(self):
        """Test get_display_url when no URLs are available."""
        media = InstagramMedia(
            external_id='123',
            post=self.post
        )
        
        # No URLs set - returns empty string
        assert media.get_display_url() == ''
    
    def test_get_display_url_prefers_gcs_over_local(self):
        """Test that GCS URL is preferred over local file."""
        media = InstagramMedia(
            external_id='123',
            post=self.post,
            media_url='https://instagram.com/photo.jpg',
            is_downloaded=True,
            gcs_url='https://storage.googleapis.com/bucket/123.jpg'
        )
        media.local_path.name = 'media/instagram/123.jpg'
        
        # GCS should be preferred
        assert media.get_display_url() == 'https://storage.googleapis.com/bucket/123.jpg'
        
        # Remove GCS URL - should fall back to local
        media.gcs_url = None
        assert 'media/instagram/123.jpg' in media.get_display_url()
    
    def test_multiple_media_with_gcs_urls(self):
        """Test multiple media objects with different GCS URLs."""
        # Create multiple media
        media1 = InstagramMedia.objects.create(
            external_id='media1',
            post=self.post,
            media_url='https://instagram.com/1.jpg',
            gcs_url='https://storage.googleapis.com/bucket/1.jpg'
        )
        
        media2 = InstagramMedia.objects.create(
            external_id='media2',
            post=self.post,
            media_url='https://instagram.com/2.jpg',
            gcs_url='https://storage.googleapis.com/bucket/2.jpg'
        )
        
        # Verify each has correct GCS URL
        assert media1.gcs_url == 'https://storage.googleapis.com/bucket/1.jpg'
        assert media2.gcs_url == 'https://storage.googleapis.com/bucket/2.jpg'
        
        # Verify get_display_url returns correct URLs
        assert media1.get_display_url() == 'https://storage.googleapis.com/bucket/1.jpg'
        assert media2.get_display_url() == 'https://storage.googleapis.com/bucket/2.jpg'