"""
Tests for Instagram models.
"""

from django.test import TestCase
from django.utils import timezone
import pytest
from django.conf import settings

from instagram_manager.models import (
    InstagramProfile,
    InstagramPost,
    InstagramMedia,
    InstagramComment,
    InstagramHashtag,
    InstagramFollower,
    InstagramScrapingTask,
)


class InstagramProfileModelTest(TestCase):
    """Tests for InstagramProfile model"""

    def test_profile_creation(self):
        """Test creating an Instagram profile"""
        profile = InstagramProfile.objects.create(
            profile_id="*********",
            username=settings.INSTAGRAM_TEST_USERNAME,
            full_name="Test User",
            bio="Test bio",
            follower_count=1000,
            following_count=500,
            post_count=50,
            is_verified=True,
            is_business=False,
            is_private=False,
            profile_pic_url="https://example.com/pic.jpg",
            external_url="https://example.com",
            category_name="Personal",
        )

        assert profile.profile_id == "*********"
        assert profile.username == settings.INSTAGRAM_TEST_USERNAME
        assert profile.follower_count == 1000
        assert profile.is_verified
        assert not profile.is_private

    def test_profile_str_method(self):
        """Test profile string representation"""
        profile = InstagramProfile.objects.create(
            profile_id="*********", username=settings.INSTAGRAM_TEST_USERNAME
        )
        assert str(profile) == f"@{settings.INSTAGRAM_TEST_USERNAME}"

    def test_profile_unique_constraints(self):
        """Test profile unique constraints"""
        InstagramProfile.objects.create(
            profile_id="*********", username=settings.INSTAGRAM_TEST_USERNAME
        )

        # Test profile_id uniqueness
        with pytest.raises(Exception):
            InstagramProfile.objects.create(
                profile_id="*********", username="different_user"
            )

        # Test username uniqueness
        with pytest.raises(Exception):
            InstagramProfile.objects.create(
                profile_id="987654321", username=settings.INSTAGRAM_TEST_USERNAME
            )


class InstagramPostModelTest(TestCase):
    """Tests for InstagramPost model"""

    def setUp(self):
        self.profile = InstagramProfile.objects.create(
            profile_id="*********", username=settings.INSTAGRAM_TEST_USERNAME
        )

    def test_post_creation(self):
        """Test creating an Instagram post"""
        post = InstagramPost.objects.create(
            external_id="POST123",
            profile=self.profile,
            shortcode="DKZ7A0XpB1U",
            post_type="photo",
            caption="Test caption #test",
            like_count=100,
            comment_count=10,
            posted_at=timezone.now(),
            location="New York",
            location_id="123456",
            is_sponsored=False,
            is_comments_disabled=False,
            post_url="https://instagram.com/p/DKZ7A0XpB1U/",
        )

        assert post.external_id == "POST123"
        assert post.post_type == "photo"
        assert post.like_count == 100
        assert post.location == "New York"

    def test_post_str_method(self):
        """Test post string representation"""
        post = InstagramPost.objects.create(
            external_id="POST123",
            profile=self.profile,
            shortcode="DKZ7A0XpB1U",
            post_type="photo",
            posted_at=timezone.now(),
        )
        assert str(post) == f"{settings.INSTAGRAM_TEST_USERNAME} - DKZ7A0XpB1U"

    def test_post_type_choices(self):
        """Test post type choices"""
        valid_types = ["photo", "video", "carousel", "reel"]

        for post_type in valid_types:
            post = InstagramPost.objects.create(
                external_id=f"POST_{post_type}",
                profile=self.profile,
                shortcode=f"CODE_{post_type}",
                post_type=post_type,
                posted_at=timezone.now(),
            )
            assert post.post_type == post_type

    def test_post_ordering(self):
        """Test post ordering by posted_at"""
        # Create posts with different dates
        InstagramPost.objects.create(
            external_id="POST1",
            profile=self.profile,
            shortcode="DKZ7A0XpB1U",
            post_type="photo",
            posted_at=timezone.now() - timezone.timedelta(days=2),
        )

        InstagramPost.objects.create(
            external_id="POST2",
            profile=self.profile,
            shortcode="DKVPW5_T6dZ",
            post_type="photo",
            posted_at=timezone.now(),
        )

        posts = list(InstagramPost.objects.all())
        # Should be ordered by posted_at descending
        assert posts[0].external_id == "POST2"
        assert posts[1].external_id == "POST1"


class InstagramMediaModelTest(TestCase):
    """Tests for InstagramMedia model"""

    def setUp(self):
        profile = InstagramProfile.objects.create(
            profile_id="*********", username=settings.INSTAGRAM_TEST_USERNAME
        )

        self.post = InstagramPost.objects.create(
            external_id="POST123",
            profile=profile,
            shortcode="DKZ7A0XpB1U",
            post_type="carousel",
            posted_at=timezone.now(),
        )

    def test_media_creation(self):
        """Test creating Instagram media"""
        media = InstagramMedia.objects.create(
            external_id="MEDIA123",
            post=self.post,
            media_type="photo",
            media_url="https://example.com/photo.jpg",
            thumbnail_url="https://example.com/thumb.jpg",
            width=1080,
            height=1080,
            order_index=0,
            is_downloaded=False,
        )

        assert media.external_id == "MEDIA123"
        assert media.media_type == "photo"
        assert media.width == 1080
        assert not media.is_downloaded

    def test_video_media_fields(self):
        """Test video-specific media fields"""
        media = InstagramMedia.objects.create(
            external_id="VIDEO123",
            post=self.post,
            media_type="video",
            media_url="https://example.com/video.mp4",
            thumbnail_url="https://example.com/video_thumb.jpg",
            audio_url="https://example.com/audio.mp3",
            duration=30,
            width=1920,
            height=1080,
            order_index=0,
        )

        assert media.duration == 30
        assert media.audio_url is not None


    def test_media_ordering(self):
        """Test media ordering by post and order_index"""
        InstagramMedia.objects.create(
            external_id="MEDIA1",
            post=self.post,
            media_type="photo",
            media_url="https://example.com/1.jpg",
            width=1080,
            height=1080,
            order_index=1,
        )

        InstagramMedia.objects.create(
            external_id="MEDIA2",
            post=self.post,
            media_type="photo",
            media_url="https://example.com/2.jpg",
            width=1080,
            height=1080,
            order_index=0,
        )

        media_list = list(InstagramMedia.objects.filter(post=self.post))
        # Should be ordered by order_index
        assert media_list[0].external_id == "MEDIA2"
        assert media_list[1].external_id == "MEDIA1"


class InstagramCommentModelTest(TestCase):
    """Tests for InstagramComment model"""

    def setUp(self):
        profile = InstagramProfile.objects.create(
            profile_id="*********", username=settings.INSTAGRAM_TEST_USERNAME
        )

        self.post = InstagramPost.objects.create(
            external_id="POST123",
            profile=profile,
            shortcode="DKZ7A0XpB1U",
            post_type="photo",
            posted_at=timezone.now(),
        )

    def test_comment_creation(self):
        """Test creating an Instagram comment"""
        comment = InstagramComment.objects.create(
            external_id="COMMENT123",
            post=self.post,
            author_username="commenter1",
            author_external_id="999999",
            author_profile_pic="https://example.com/pic.jpg",
            author_is_verified=True,
            text="Great post!",
            like_count=5,
            commented_at=timezone.now(),
        )

        assert comment.external_id == "COMMENT123"
        assert comment.text == "Great post!"
        assert comment.author_is_verified
        assert comment.like_count == 5

    def test_comment_reply_hierarchy(self):
        """Test comment reply hierarchy"""
        parent = InstagramComment.objects.create(
            external_id="PARENT123",
            post=self.post,
            author_username="user1",
            author_external_id="111111",
            text="Parent comment",
            commented_at=timezone.now(),
        )

        reply = InstagramComment.objects.create(
            external_id="REPLY123",
            post=self.post,
            author_username="user2",
            author_external_id="222222",
            text="Reply to parent",
            reply_to=parent,
            commented_at=timezone.now(),
        )

        assert reply.reply_to == parent
        assert parent.replies.count() == 1
        assert parent.replies.first() == reply

    def test_comment_ordering(self):
        """Test comment ordering by commented_at"""
        InstagramComment.objects.create(
            external_id="COMMENT1",
            post=self.post,
            author_username="user1",
            author_external_id="111111",
            text="First comment",
            commented_at=timezone.now() - timezone.timedelta(hours=1),
        )

        InstagramComment.objects.create(
            external_id="COMMENT2",
            post=self.post,
            author_username="user2",
            author_external_id="222222",
            text="Second comment",
            commented_at=timezone.now(),
        )

        comments = list(InstagramComment.objects.filter(post=self.post))
        # Should be ordered by commented_at descending
        assert comments[0].external_id == "COMMENT2"
        assert comments[1].external_id == "COMMENT1"


class InstagramHashtagModelTest(TestCase):
    """Tests for InstagramHashtag model"""

    def test_hashtag_creation(self):
        """Test creating an Instagram hashtag"""
        hashtag = InstagramHashtag.objects.create(name="photography", post_count=10000)

        assert hashtag.name == "photography"
        assert hashtag.post_count == 10000
        assert str(hashtag) == "#photography"

    def test_hashtag_post_relationship(self):
        """Test hashtag-post many-to-many relationship"""
        hashtag1 = InstagramHashtag.objects.create(name="travel")
        hashtag2 = InstagramHashtag.objects.create(name="nature")

        profile = InstagramProfile.objects.create(
            profile_id="*********", username=settings.INSTAGRAM_TEST_USERNAME
        )

        post = InstagramPost.objects.create(
            external_id="POST123",
            profile=profile,
            shortcode="DKZ7A0XpB1U",
            post_type="photo",
            posted_at=timezone.now(),
        )

        # Add hashtags to post
        post.hashtags.add(hashtag1, hashtag2)

        assert post.hashtags.count() == 2
        assert hashtag1 in post.hashtags.all()
        assert hashtag2 in post.hashtags.all()

        # Check reverse relation
        assert post in hashtag1.posts.all()
        assert post in hashtag2.posts.all()

    def test_hashtag_unique_constraint(self):
        """Test hashtag name uniqueness"""
        InstagramHashtag.objects.create(name="unique")

        with pytest.raises(Exception):
            InstagramHashtag.objects.create(name="unique")


class InstagramFollowerModelTest(TestCase):
    """Tests for InstagramFollower model"""

    def setUp(self):
        self.profile = InstagramProfile.objects.create(
            profile_id="*********", username=settings.INSTAGRAM_TEST_USERNAME
        )

    def test_follower_creation(self):
        """Test creating an Instagram follower"""
        follower = InstagramFollower.objects.create(
            profile=self.profile,
            follower_username="follower1",
            follower_id="999999",
            follower_full_name="Follower One",
            is_verified=True,
            profile_pic_url="https://example.com/pic.jpg",
            followed_at=timezone.now(),
        )

        assert follower.follower_username == "follower1"
        assert follower.is_verified
        assert follower.profile == self.profile

    def test_follower_unique_constraint(self):
        """Test unique constraint on profile and follower_id"""
        InstagramFollower.objects.create(
            profile=self.profile, follower_username="follower1", follower_id="999999"
        )

        # Same follower_id for same profile should fail
        with pytest.raises(Exception):
            InstagramFollower.objects.create(
                profile=self.profile,
                follower_username="different_username",
                follower_id="999999",
            )

    def test_follower_profile_relationship(self):
        """Test follower-profile relationship"""
        follower1 = InstagramFollower.objects.create(
            profile=self.profile, follower_username="follower1", follower_id="111111"
        )

        follower2 = InstagramFollower.objects.create(
            profile=self.profile, follower_username="follower2", follower_id="222222"
        )

        assert self.profile.followers.count() == 2
        assert follower1 in self.profile.followers.all()
        assert follower2 in self.profile.followers.all()


class InstagramScrapingTaskModelTest(TestCase):
    """Tests for InstagramScrapingTask model"""

    def test_task_creation(self):
        """Test creating a scraping task"""
        task = InstagramScrapingTask.objects.create(
            task_id="task_001",
            task_type="profile",
            target_identifier=settings.INSTAGRAM_TEST_USERNAME,
            status="pending",
            brightdata_dataset_id="dataset123",
        )

        assert task.task_type == "profile"
        assert task.status == "pending"
        assert task.items_scraped == 0
        assert task.created_at is not None

    def test_task_type_choices(self):
        """Test task type choices"""
        valid_types = ["profile", "posts", "followers", "comments", "hashtag"]

        for task_type in valid_types:
            task = InstagramScrapingTask.objects.create(
                task_id=f"task_{task_type}",
                task_type=task_type,
                target_identifier="test",
                brightdata_dataset_id="dataset123",
            )
            assert task.task_type == task_type

    def test_task_status_choices(self):
        """Test task status choices"""
        valid_statuses = ["pending", "in_progress", "completed", "failed"]

        for status in valid_statuses:
            task = InstagramScrapingTask.objects.create(
                task_id=f"task_status_{status}",
                task_type="profile",
                target_identifier=f"test_{status}",
                status=status,
                brightdata_dataset_id="dataset123",
            )
            assert task.status == status

    def test_task_with_results(self):
        """Test task with results"""
        task = InstagramScrapingTask.objects.create(
            task_id="task_results",
            task_type="posts",
            target_identifier=settings.INSTAGRAM_TEST_USERNAME,
            status="completed",
            brightdata_dataset_id="dataset123",
            brightdata_snapshot_id="snapshot456",
            items_scraped=50,
            started_at=timezone.now() - timezone.timedelta(hours=1),
            completed_at=timezone.now(),
        )

        assert task.items_scraped == 50
        assert task.brightdata_snapshot_id is not None
        assert task.started_at is not None
        assert task.completed_at is not None

    def test_task_with_error(self):
        """Test task with error"""
        error_msg = "Rate limit exceeded"
        task = InstagramScrapingTask.objects.create(
            task_id="task_error",
            task_type="followers",
            target_identifier=settings.INSTAGRAM_TEST_USERNAME,
            status="failed",
            brightdata_dataset_id="dataset123",
            error_message=error_msg,
            completed_at=timezone.now(),
        )

        assert task.status == "failed"
        assert task.error_message == error_msg
        assert task.completed_at is not None
