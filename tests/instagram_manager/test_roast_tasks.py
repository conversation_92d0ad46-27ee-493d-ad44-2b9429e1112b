"""Tests for Instagram roast tasks."""

from unittest.mock import Mock, patch
from django.test import TestCase
from django.utils import timezone

from instagram_manager.models import InstagramProfile, InstagramPost, InstagramMedia


class TestRoastProfileTask(TestCase):
    """Test RoastProfileTask."""
    
    def setUp(self):
        """Set up test data."""
        # Create test profile
        self.profile = InstagramProfile.objects.create(
            username="testuser",
            full_name="Test User",
            profile_id="123456",
            follower_count=1000,
            post_count=50
        )
        
        # Create test posts with media
        self.posts = []
        for i in range(3):
            post = InstagramPost.objects.create(
                profile=self.profile,
                shortcode=f"ABC{i}",
                caption=f"Test post {i}",
                posted_at=timezone.now(),
                like_count=100 + i*10,
                comment_count=10 + i
            )
            
            # Create media for post
            for j in range(2):  # 2 photos per post
                InstagramMedia.objects.create(
                    post=post,
                    media_type="image",
                    media_url=f"https://instagram.com/p/{post.shortcode}/media/{j}.jpg",
                    thumbnail_url=f"https://instagram.com/p/{post.shortcode}/thumb/{j}.jpg",
                    width=1080,
                    height=1080
                )
            
            self.posts.append(post)
        
    @patch("instagram_manager.tasks.roast_tasks.RoastProfileTask")
    def test_task_initialization(self, mock_task_class):
        """Test task can be initialized."""
        # Create mock task instance
        mock_task = Mock()
        mock_task.task_id = "test-task-id"
        mock_task_class.return_value = mock_task
        
        # Import and create task
        from instagram_manager.tasks.roast_tasks import RoastProfileTask
        task = RoastProfileTask()
        
        # Verify task was created
        self.assertEqual(task.task_id, "test-task-id")
    
    def test_validate_params_success(self):
        """Test successful parameter validation."""
        # Since task isn't implemented yet, we'll test expected behavior
        # This test will need updates when actual task is implemented
        pass
    
    def test_validate_params_invalid_username(self):
        """Test validation with invalid username."""
        # Since task isn't implemented yet, we'll test expected behavior
        # This test will need updates when actual task is implemented
        pass
    
    @patch("instagram_manager.services.roast_service.RoastService")
    def test_execute_task_success(self, mock_service_class):
        """Test successful task execution."""
        # Mock service
        mock_service = Mock()
        mock_service.process_profile.return_value = {
            "profile_url": f"https://www.instagram.com/{self.profile.username}/",
            "username": self.profile.username,
            "post_count": 3,
            "media_urls": [
                "https://storage.googleapis.com/bucket/image1.jpg",
                "https://storage.googleapis.com/bucket/image2.jpg",
                "https://storage.googleapis.com/bucket/image3.jpg",
                "https://storage.googleapis.com/bucket/image4.jpg",
                "https://storage.googleapis.com/bucket/image5.jpg",
                "https://storage.googleapis.com/bucket/image6.jpg"
            ],
            "statistics": {
                "total_posts_analyzed": 3,
                "photos_found": 6,
                "photos_uploaded": 6,
                "upload_duration_seconds": 2.5
            }
        }
        mock_service_class.return_value = mock_service
        
        # This test demonstrates expected behavior
        # Will need updates when actual task is implemented
        pass
    
    @patch("instagram_manager.services.roast_service.RoastService")
    def test_execute_task_profile_not_found(self, mock_service_class):
        """Test task execution with non-existent profile."""
        # Mock service to raise error
        mock_service = Mock()
        mock_service.process_profile.side_effect = ValueError("Profile not found")
        mock_service_class.return_value = mock_service
        
        # This test demonstrates expected behavior
        # Will need updates when actual task is implemented
        pass
    
    @patch("instagram_manager.services.roast_service.RoastService")
    def test_execute_task_no_posts(self, mock_service_class):
        """Test task execution when profile has no posts."""
        # Mock service
        mock_service = Mock()
        mock_service.process_profile.return_value = {
            "profile_url": f"https://www.instagram.com/{self.profile.username}/",
            "username": self.profile.username,
            "post_count": 0,
            "media_urls": [],
            "statistics": {
                "total_posts_analyzed": 0,
                "photos_found": 0,
                "photos_uploaded": 0
            }
        }
        mock_service_class.return_value = mock_service
        
        # This test demonstrates expected behavior
        # Will need updates when actual task is implemented
        pass
    
    @patch("instagram_manager.services.roast_service.RoastService")
    def test_execute_task_with_progress_tracking(self, mock_service_class):
        """Test task execution with progress updates."""
        # Mock service with progress callback
        mock_service = Mock()
        
        def process_with_progress(*args, **kwargs):
            # Simulate progress updates
            progress_callback = kwargs.get('progress_callback')
            if progress_callback:
                progress_callback(1, 3, "Processing post 1...")
                progress_callback(2, 3, "Processing post 2...")
                progress_callback(3, 3, "Uploading to GCS...")
            
            return {
                "profile_url": f"https://www.instagram.com/{self.profile.username}/",
                "username": self.profile.username,
                "post_count": 3,
                "media_urls": ["url1", "url2", "url3"],
                "statistics": {
                    "total_posts_analyzed": 3,
                    "photos_found": 3,
                    "photos_uploaded": 3
                }
            }
        
        mock_service.process_profile.side_effect = process_with_progress
        mock_service_class.return_value = mock_service
        
        # This test demonstrates expected behavior
        # Will need updates when actual task is implemented
        pass
    
    @patch("instagram_manager.services.roast_service.RoastService")
    def test_execute_task_gcs_upload_error(self, mock_service_class):
        """Test task execution with GCS upload error."""
        # Mock service to raise GCS error
        mock_service = Mock()
        mock_service.process_profile.side_effect = Exception("Failed to upload to GCS")
        mock_service_class.return_value = mock_service
        
        # This test demonstrates expected behavior
        # Will need updates when actual task is implemented
        pass
    
    def test_task_type_registration(self):
        """Test that RoastProfileTask is properly registered."""
        # This test will verify task registration in task_registry
        # Will need updates when task registration is implemented
        pass
    
    @patch("instagram_manager.instagram_api.BrightDataClient")
    @patch("instagram_manager.services.batch_post_service.BatchPostService")
    def test_post_counting_logic_mixed_media(self, mock_batch_service_class, mock_client_class):
        """Test that task correctly counts total posts vs posts with photos.
        
        This test verifies the fix for the bug where the task would import
        posts from BrightData even when the profile already had enough posts
        (but not all were photos).
        """
        # Create profile
        profile = InstagramProfile.objects.create(
            username="igorkishik",
            full_name="Igor Kishik",
            profile_id="987654",
            follower_count=500,
            post_count=10
        )
        
        # Create 10 posts: 5 with photos, 5 with videos
        for i in range(5):
            # Create post with photo
            post = InstagramPost.objects.create(
                profile=profile,
                shortcode=f"PHOTO{i}",
                caption=f"Photo post {i}",
                posted_at=timezone.now(),
                like_count=100 + i*10,
                comment_count=10 + i
            )
            InstagramMedia.objects.create(
                post=post,
                media_id=f"photo_{i}",
                media_type="photo",
                media_url=f"https://instagram.com/p/{post.shortcode}/photo.jpg",
                width=1080,
                height=1080,
                order_index=0
            )
        
        for i in range(5):
            # Create post with video
            post = InstagramPost.objects.create(
                profile=profile,
                shortcode=f"VIDEO{i}",
                caption=f"Video post {i}",
                posted_at=timezone.now(),
                like_count=200 + i*10,
                comment_count=20 + i
            )
            InstagramMedia.objects.create(
                post=post,
                media_id=f"video_{i}",
                media_type="video",
                media_url=f"https://instagram.com/p/{post.shortcode}/video.mp4",
                thumbnail_url=f"https://instagram.com/p/{post.shortcode}/thumb.jpg",
                duration=30,
                width=1080,
                height=1920,
                order_index=0
            )
        
        # Mock the clients
        mock_client = Mock()
        mock_batch_service = Mock()
        mock_client_class.return_value = mock_client
        mock_batch_service_class.return_value = mock_batch_service
        
        # Create task and run inline implementation
        from instagram_manager.tasks.roast_tasks import RoastProfileTask
        task = RoastProfileTask()
        task.task_id = "test-task-123"
        task.task_result = Mock()
        task.update_progress = Mock()
        
        # Run the inline implementation
        result = task._execute_inline(
            username="igorkishik",
            post_count=10,  # Request 10 posts
            profile_url="https://www.instagram.com/igorkishik/"
        )
        
        # Verify that BatchPostService.import_posts_batch was NOT called
        # because we already have 10 posts (even though only 5 are photos)
        mock_batch_service.import_posts_batch.assert_not_called()
        
        # Verify result contains the 5 photos we have
        self.assertEqual(len(result["media_urls"]), 0)  # No GCS URLs since we didn't upload
        self.assertEqual(result["username"], "igorkishik")
        self.assertEqual(result["statistics"]["total_posts_analyzed"], 5)  # Only analyzed photos
        self.assertEqual(result["statistics"]["photos_found"], 0)  # No GCS URLs
    
    @patch("instagram_manager.instagram_api.BrightDataClient")
    @patch("instagram_manager.services.batch_post_service.BatchPostService")
    def test_post_counting_logic_needs_import(self, mock_batch_service_class, mock_client_class):
        """Test that task correctly imports when there aren't enough total posts."""
        # Create profile with only 3 posts
        profile = InstagramProfile.objects.create(
            username="testuser2",
            full_name="Test User 2",
            profile_id="111222",
            follower_count=100,
            post_count=3
        )
        
        # Create 3 posts with photos
        for i in range(3):
            post = InstagramPost.objects.create(
                profile=profile,
                shortcode=f"POST{i}",
                caption=f"Post {i}",
                posted_at=timezone.now(),
                like_count=50 + i*5,
                comment_count=5 + i
            )
            InstagramMedia.objects.create(
                post=post,
                media_id=f"media_{i}",
                media_type="photo",
                media_url=f"https://instagram.com/p/{post.shortcode}/photo.jpg",
                width=1080,
                height=1080,
                order_index=0
            )
        
        # Mock the clients
        mock_client = Mock()
        mock_batch_service = Mock()
        mock_client_class.return_value = mock_client
        mock_batch_service_class.return_value = mock_batch_service
        
        # Create task and run inline implementation
        from instagram_manager.tasks.roast_tasks import RoastProfileTask
        task = RoastProfileTask()
        task.task_id = "test-task-456"
        task.task_result = Mock()
        task.update_progress = Mock()
        
        # Run the inline implementation
        result = task._execute_inline(
            username="testuser2",
            post_count=10,  # Request 10 posts but only have 3
            profile_url="https://www.instagram.com/testuser2/"
        )
        
        # Verify that BatchPostService.import_posts_batch WAS called
        # because we only have 3 posts total (less than requested 10)
        mock_batch_service.import_posts_batch.assert_called_once()
        
        # Verify the call parameters
        call_args = mock_batch_service.import_posts_batch.call_args
        self.assertEqual(len(call_args[1]["profiles"]), 1)
        self.assertEqual(call_args[1]["profiles"][0].username, "testuser2")
        self.assertEqual(call_args[1]["limit"], 10)
        self.assertTrue(call_args[1]["save_media_to_gcs"])