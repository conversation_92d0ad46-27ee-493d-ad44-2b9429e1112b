"""
Test settings for Instagram manager tests.
"""

# Test database settings
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.sqlite3',
        'NAME': ':memory:',
    }
}

# Instagram specific test settings
INSTAGRAM_AUTO_DOWNLOAD_MEDIA = False  # Disable auto-download in tests
INSTAGRAM_AUTO_DOWNLOAD_MAX_SIZE_MB = 10
INSTAGRAM_AUTO_DOWNLOAD_VIDEO_MAX_DURATION = 60

# BrightData test settings (use mock credentials)
BRIGHTDATA_CUSTOMER_ID = 'test_customer'
BRIGHTDATA_API_TOKEN = 'test_token'

# Media settings for tests
MEDIA_ROOT = '/tmp/test_media/'
MEDIA_URL = '/media/'

# Cache disabled in tests