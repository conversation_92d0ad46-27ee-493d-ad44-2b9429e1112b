"""
Тесты для интеграции PostService с функционалом комментариев.
"""

from unittest.mock import Mock, patch
from django.test import TestCase
from django.utils import timezone
from django.conf import settings

from instagram_manager.models import InstagramProfile, InstagramPost, InstagramComment
from instagram_manager.services import PostService
from instagram_manager.schemas.brightdata import InstagramPostResponse, MediaItem


class TestPostServiceCommentsIntegration(TestCase):
    """Тесты интеграции PostService с комментариями."""
    
    def setUp(self):
        """Настройка тестовых данных."""
        # Создание тестового профиля
        self.profile = InstagramProfile.objects.create(
            profile_id='test_profile_123',
            username=settings.INSTAGRAM_TEST_USERNAME,
            full_name='Test User',
            follower_count=1000
        )
        
        # Создание экземпляра PostService
        self.post_service = PostService()
        
        # Примеры данных постов от API
        self.sample_posts_data = [
            InstagramPostResponse(
                pk='post_1',
                shortcode='DKZ7A0XpB1U',
                content_type='Image',
                caption='Post with many comments',
                like_count=100,
                comment_count=50,  # Много комментариев
                timestamp=timezone.now(),
                url='https://instagram.com/p/DKZ7A0XpB1U/',
                post_content=[MediaItem(id='m1', media_type='photo', url='https://example.com/1.jpg')]
            ),
            InstagramPostResponse(
                pk='post_2',
                shortcode='DKVPW5_T6dZ',
                content_type='Video',
                caption='Post with few comments',
                like_count=50,
                comment_count=5,  # Мало комментариев
                timestamp=timezone.now(),
                url='https://instagram.com/p/DKVPW5_T6dZ/',
                is_video=True,
                post_content=[MediaItem(id='m2', media_type='video', url='https://example.com/2.mp4')]
            ),
            InstagramPostResponse(
                pk='post_3',
                shortcode='CODE3',
                content_type='Image',
                caption='Post without comments',
                like_count=200,
                comment_count=0,  # Нет комментариев
                timestamp=timezone.now(),
                url='https://instagram.com/p/CODE3/',
                post_content=[MediaItem(id='m3', media_type='photo', url='https://example.com/3.jpg')]
            )
        ]
    
    @patch('instagram_manager.instagram_api.data_handlers.post_handler.PostHandler.process_post_data')
    @patch('instagram_manager.services.comment_service.CommentService')
    @patch('instagram_manager.services.post_service.BrightDataClient')
    def test_import_posts_with_comments_enabled(self, mock_brightdata_client, mock_comment_service, mock_process_post):
        """Тест импорта постов с включенным импортом комментариев."""
        # Настраиваем мок клиента
        mock_client_instance = mock_brightdata_client.return_value
        self.post_service.client = mock_client_instance
        mock_client_instance.get_posts.return_value = self.sample_posts_data
        
        # Мокаем обработку постов
        created_posts = []
        for i, post_data in enumerate(self.sample_posts_data):
            post = InstagramPost.objects.create(
                external_id=post_data.external_id,
                profile=self.profile,
                shortcode=post_data.shortcode,
                post_type='photo' if not post_data.is_video else 'video',
                caption=post_data.description,
                comment_count=post_data.num_comments,
                like_count=post_data.likes,
                posted_at=post_data.date_posted,
                post_url=post_data.url
            )
            created_posts.append(post)
        mock_process_post.side_effect = created_posts
        
        # Настраиваем мок сервиса комментариев
        mock_comment_service_instance = mock_comment_service.return_value
        mock_comments = [Mock(spec=InstagramComment) for _ in range(10)]
        mock_comment_service_instance.import_comments_for_post.return_value = mock_comments
        
        # Импортируем посты с комментариями
        posts = self.post_service.import_posts_for_profile(
            profile=self.profile,
            import_comments=True,
            import_comments_limit=100
        )
        
        # Проверяем, что посты импортированы
        assert len(posts) == 3
        
        # Проверяем, что CommentService был создан (2 раза - для каждого поста с комментариями)
        assert mock_comment_service.call_count == 2
        
        # Проверяем, что импорт комментариев вызван только для постов с комментариями
        assert mock_comment_service_instance.import_comments_for_post.call_count == 2
        
        # Проверяем аргументы вызовов
        calls = mock_comment_service_instance.import_comments_for_post.call_args_list
        
        # Первый вызов - для поста с 50 комментариями
        first_call = calls[0]
        assert first_call.kwargs['post'].shortcode == 'DKZ7A0XpB1U'
        assert first_call.kwargs['limit'] == 100
        assert first_call.kwargs['include_replies']
        
        # Второй вызов - для поста с 5 комментариями
        second_call = calls[1]
        assert second_call.kwargs['post'].shortcode == 'DKVPW5_T6dZ'
        assert second_call.kwargs['limit'] == 100
    
    @patch('instagram_manager.instagram_api.data_handlers.post_handler.PostHandler.process_post_data')
    @patch('instagram_manager.services.comment_service.CommentService')
    @patch('instagram_manager.services.post_service.BrightDataClient')
    def test_import_posts_with_comments_disabled(self, mock_brightdata_client, mock_comment_service, mock_process_post):
        """Тест импорта постов без импорта комментариев."""
        mock_client_instance = mock_brightdata_client.return_value
        self.post_service.client = mock_client_instance
        mock_client_instance.get_posts.return_value = self.sample_posts_data
        
        # Мокаем обработку постов
        created_posts = []
        for post_data in self.sample_posts_data:
            post = InstagramPost.objects.create(
                external_id=post_data.external_id,
                profile=self.profile,
                shortcode=post_data.shortcode,
                post_type='photo' if not post_data.is_video else 'video',
                caption=post_data.description,
                comment_count=post_data.num_comments,
                like_count=post_data.likes,
                posted_at=post_data.date_posted,
                post_url=post_data.url
            )
            created_posts.append(post)
        mock_process_post.side_effect = created_posts
        
        # Импортируем посты без комментариев
        posts = self.post_service.import_posts_for_profile(
            profile=self.profile,
            import_comments=False
        )
        
        # Проверяем, что посты импортированы
        assert len(posts) == 3
        
        # Проверяем, что CommentService не создавался
        mock_comment_service.assert_not_called()
    
    @patch('instagram_manager.instagram_api.data_handlers.post_handler.PostHandler.process_post_data')
    @patch('instagram_manager.services.comment_service.CommentService')
    @patch('instagram_manager.services.post_service.BrightDataClient')
    def test_import_posts_with_custom_comment_limit(self, mock_brightdata_client, mock_comment_service, mock_process_post):
        """Тест импорта постов с кастомным лимитом комментариев."""
        mock_client_instance = mock_brightdata_client.return_value
        self.post_service.client = mock_client_instance
        mock_client_instance.get_posts.return_value = self.sample_posts_data[:1]  # Только первый пост
        
        # Мокаем обработку поста
        post = InstagramPost.objects.create(
            external_id=self.sample_posts_data[0].post_id,
            profile=self.profile,
            shortcode=self.sample_posts_data[0].shortcode,
            post_type='photo',
            caption=self.sample_posts_data[0].description,
            comment_count=self.sample_posts_data[0].num_comments,
            like_count=self.sample_posts_data[0].likes,
            posted_at=self.sample_posts_data[0].date_posted,
            post_url=self.sample_posts_data[0].url
        )
        mock_process_post.return_value = post
        
        mock_comment_service_instance = mock_comment_service.return_value
        mock_comments = [Mock(spec=InstagramComment) for _ in range(5)]
        mock_comment_service_instance.import_comments_for_post.return_value = mock_comments
        
        # Импортируем с лимитом 50 комментариев
        self.post_service.import_posts_for_profile(
            profile=self.profile,
            import_comments=True,
            import_comments_limit=50
        )
        
        # Проверяем передачу лимита
        mock_comment_service_instance.import_comments_for_post.assert_called_once()
        call_args = mock_comment_service_instance.import_comments_for_post.call_args
        assert call_args.kwargs['limit'] == 50
    
    @patch('instagram_manager.instagram_api.data_handlers.post_handler.PostHandler.process_post_data')
    @patch('instagram_manager.services.comment_service.CommentService')
    @patch('instagram_manager.services.post_service.BrightDataClient')
    def test_import_posts_comment_error_handling(self, mock_brightdata_client, mock_comment_service, mock_process_post):
        """Тест обработки ошибок при импорте комментариев."""
        mock_client_instance = mock_brightdata_client.return_value
        self.post_service.client = mock_client_instance
        mock_client_instance.get_posts.return_value = self.sample_posts_data[:2]  # Два поста
        
        # Мокаем обработку постов
        posts = []
        for i in range(2):
            post = InstagramPost.objects.create(
                external_id=self.sample_posts_data[i].post_id,
                profile=self.profile,
                shortcode=self.sample_posts_data[i].shortcode,
                post_type='photo' if not self.sample_posts_data[i].is_video else 'video',
                caption=self.sample_posts_data[i].description,
                comment_count=self.sample_posts_data[i].num_comments,
                like_count=self.sample_posts_data[i].likes,
                posted_at=self.sample_posts_data[i].date_posted,
                post_url=self.sample_posts_data[i].url
            )
            posts.append(post)
        mock_process_post.side_effect = posts
        
        # Настраиваем ошибку для первого поста
        mock_comment_service_instance = mock_comment_service.return_value
        mock_comment_service_instance.import_comments_for_post.side_effect = [
            Exception("Comment API Error"),  # Ошибка для первого
            [Mock(spec=InstagramComment)]  # Успех для второго
        ]
        
        # Импортируем посты
        posts = self.post_service.import_posts_for_profile(
            profile=self.profile,
            import_comments=True
        )
        
        # Проверяем, что посты всё равно импортированы
        assert len(posts) == 2
        
        # Проверяем, что была попытка импорта для обоих
        assert mock_comment_service_instance.import_comments_for_post.call_count == 2
    
    @patch('instagram_manager.instagram_api.data_handlers.post_handler.PostHandler.process_post_data')
    @patch('instagram_manager.services.profile_service.ProfileService')
    @patch('instagram_manager.services.comment_service.CommentService')
    @patch('instagram_manager.services.post_service.BrightDataClient')
    def test_import_posts_by_username_with_comments(self, mock_brightdata_client, 
                                                    mock_comment_service, mock_profile_service, mock_process_post):
        """Тест импорта постов по username с комментариями."""
        mock_client_instance = mock_brightdata_client.return_value
        self.post_service.client = mock_client_instance
        mock_client_instance.get_posts.return_value = self.sample_posts_data
        
        # Мокаем обработку постов
        posts = []
        for i, post_data in enumerate(self.sample_posts_data):
            post = InstagramPost.objects.create(
                external_id=post_data.external_id,
                profile=self.profile,
                shortcode=post_data.shortcode,
                post_type='photo' if not post_data.is_video else 'video',
                caption=post_data.description,
                comment_count=post_data.num_comments,
                like_count=post_data.likes,
                posted_at=post_data.date_posted,
                post_url=post_data.url
            )
            posts.append(post)
        mock_process_post.side_effect = posts
        
        mock_comment_service_instance = mock_comment_service.return_value
        mock_comments = [Mock(spec=InstagramComment) for _ in range(5)]
        mock_comment_service_instance.import_comments_for_post.return_value = mock_comments
        
        # Мокаем ProfileService для import_posts_by_username
        mock_profile_service_instance = mock_profile_service.return_value
        mock_profile_service_instance.import_profile.return_value = self.profile
        
        # Импортируем по username
        posts = self.post_service.import_posts_by_username(
            username=settings.INSTAGRAM_TEST_USERNAME,
            limit=10
        )
        
        # Проверяем результат
        assert len(posts) == 3
    
    @patch('instagram_manager.instagram_api.data_handlers.post_handler.PostHandler.process_post_data')
    @patch('instagram_manager.services.post_service.BrightDataClient')
    def test_post_comments_count_update(self, mock_brightdata_client, mock_process_post):
        """Тест обновления счетчика комментариев в посте."""
        mock_client_instance = mock_brightdata_client.return_value
        self.post_service.client = mock_client_instance
        mock_client_instance.get_posts.return_value = self.sample_posts_data[:1]
        
        # Мокаем обработку поста
        post = InstagramPost.objects.create(
            external_id=self.sample_posts_data[0].post_id,
            profile=self.profile,
            shortcode=self.sample_posts_data[0].shortcode,
            post_type='photo',
            caption=self.sample_posts_data[0].description,
            comment_count=self.sample_posts_data[0].num_comments,
            like_count=self.sample_posts_data[0].likes,
            posted_at=self.sample_posts_data[0].date_posted,
            post_url=self.sample_posts_data[0].url
        )
        mock_process_post.return_value = post
        
        # Импортируем пост
        posts = self.post_service.import_posts_for_profile(
            profile=self.profile,
            import_comments=False
        )
        
        # Проверяем начальное количество
        post = posts[0]
        assert post.comment_count == 50
        
        # Теперь тестируем, что при импорте комментариев через PostService счетчик обновляется
        with patch('instagram_manager.services.comment_service.CommentService') as mock_comment_service:
            mock_comment_service_instance = mock_comment_service.return_value
            # Возвращаем пустой список комментариев
            mock_comment_service_instance.import_comments_for_post.return_value = []
            
            # Вызываем приватный метод PostService для импорта комментариев
            self.post_service._import_comments_for_post(post)
            
            # Проверяем, что CommentService был вызван с правильными параметрами
            mock_comment_service_instance.import_comments_for_post.assert_called_once_with(
                post=post,
                limit=100,
                include_replies=True
            )
    
    @patch('instagram_manager.instagram_api.data_handlers.post_handler.PostHandler.process_post_data')
    @patch('instagram_manager.services.comment_service.CommentService')
    @patch('instagram_manager.services.post_service.BrightDataClient')
    def test_import_posts_filters_with_comments(self, mock_brightdata_client, mock_comment_service, mock_process_post):
        """Тест импорта постов с фильтрами и комментариями."""
        # Создаем посты разных типов
        video_post = InstagramPostResponse(
            pk='video_1',
            content_id='VIDEO1',
            content_type='Video',
            description='Video post',
            likes=100,
            num_comments=25,
            date_posted=timezone.now(),
            url='https://instagram.com/p/VIDEO1/',
            is_video=True,
            post_content=[MediaItem(id='v1', media_type='video', url='https://example.com/v1.mp4')]
        )
        
        mock_client_instance = mock_brightdata_client.return_value
        self.post_service.client = mock_client_instance
        mock_client_instance.get_posts.return_value = [video_post]
        
        # Мокаем обработку поста
        post = InstagramPost.objects.create(
            external_id=video_post.post_id,
            profile=self.profile,
            shortcode=video_post.shortcode,
            post_type='video',
            caption=video_post.description,
            comment_count=video_post.num_comments,
            like_count=video_post.likes,
            posted_at=video_post.date_posted,
            post_url=video_post.url
        )
        mock_process_post.return_value = post
        
        mock_comment_service_instance = mock_comment_service.return_value
        mock_comments = [Mock(spec=InstagramComment) for _ in range(5)]
        mock_comment_service_instance.import_comments_for_post.return_value = mock_comments
        
        # Импортируем только видео с комментариями
        posts = self.post_service.import_posts_for_profile(
            profile=self.profile,
            post_types=['video'],
            import_comments=True,
            import_comments_limit=200
        )
        
        # Проверяем фильтрацию
        assert len(posts) == 1
        assert posts[0].post_type == 'video'
        
        # Проверяем импорт комментариев
        mock_comment_service_instance.import_comments_for_post.assert_called_once()
        call_args = mock_comment_service_instance.import_comments_for_post.call_args
        assert call_args.kwargs['limit'] == 200
    
    def test_real_integration_comment_import(self):
        """Тест реальной интеграции без моков (используя фейковые данные)."""
        # Создаем пост
        post = InstagramPost.objects.create(
            external_id='real_post',
            shortcode='REAL123',
            profile=self.profile,
            post_type='photo',
            post_url='https://instagram.com/p/REAL123/',
            comment_count=10,
            posted_at=timezone.now()
        )
        
        # Создаем реальный PostService
        post_service = PostService()
        
        # Мокаем только API вызовы
        with patch.object(post_service, '_import_comments_for_post') as mock_import:
            # Симулируем, что _import_comments_for_post создает комментарии
            def create_comments(post, limit=100):
                for i in range(3):
                    InstagramComment.objects.create(
                        external_id=f'comment_{i}',
                        post=post,
                        author_username=f'user{i}',
                        author_external_id=f'uid{i}',
                        text=f'Comment {i}',
                        like_count=i,
                        commented_at=timezone.now()
                    )
            
            mock_import.side_effect = create_comments
            
            # Вызываем метод
            post_service._import_comments_for_post(post)
            
            # Проверяем результат
            assert post.comments.count() == 3
            assert post.comments.filter(author_username='user0').exists()
            assert post.comments.filter(author_username='user1').exists()
            assert post.comments.filter(author_username='user2').exists()