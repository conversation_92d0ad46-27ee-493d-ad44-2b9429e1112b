"""
Тесты для management команды instagram_comments.
"""

import pytest
from unittest.mock import patch, MagicMock
from io import StringIO
from django.core.management import call_command
from django.utils import timezone

from instagram_manager.models import InstagramProfile, InstagramPost
from core.models import TaskResult


@pytest.fixture
def profile():
    """Создание тестового профиля."""
    return InstagramProfile.objects.create(
        username='testuser',
        external_id='12345',
        profile_id='profile_12345',
        full_name='Test User',
        bio='Test bio',
        is_verified=False,
        follower_count=100,
        following_count=50,
        post_count=10
    )


@pytest.fixture
def posts(profile):
    """Создание тестовых постов."""
    posts = []
    for i in range(3):
        posts.append(InstagramPost.objects.create(
            profile=profile,
            external_id=f'post_{i}',
            shortcode=f'CODE{i}',
            caption=f'Test post {i}',
            posted_at=timezone.now(),
            post_url=f'https://instagram.com/p/CODE{i}/',
            comment_count=10 + i,
            like_count=100 + i * 10
        ))
    return posts


@pytest.fixture
def mock_celery_task():
    """Мок для Celery задачи."""
    with patch('core.tasks.celery_integration.execute_base_task.delay') as mock:
        # Создаем мок для celery result
        mock_result = MagicMock()
        mock_result.id = 'test-celery-task-id'
        mock_result.status = 'PENDING'
        mock.return_value = mock_result
        yield mock


@pytest.mark.django_db
class TestInstagramCommentsCommand:
    """Тесты для команды instagram_comments с async архитектурой."""
    
    def test_command_with_post_url(self, posts, mock_celery_task):
        """Тест что команда с параметром --post-url создает задачу."""
        # Мокируем BrightData API на уровне задачи
        with patch('instagram_manager.instagram_api.brightdata.clients.comment.CommentClient.get_post_comments', return_value=[]):
            # Вызываем команду
            out = StringIO()
            call_command(
                'instagram_comments',
                '--post-url', 'https://instagram.com/p/CODE0/',
                stdout=out
            )
            
            # Проверяем что задача была создана через Celery
            mock_celery_task.assert_called_once()
            
            # Проверяем параметры задачи
            call_args = mock_celery_task.call_args
            assert 'ImportBatchCommentsTask' in call_args[0][0]
            
            # Проверяем что параметры содержат правильный post_url
            task_params = call_args[1]
            assert 'post_urls' in task_params
            assert len(task_params['post_urls']) == 1
            assert task_params['post_urls'][0] == 'https://www.instagram.com/p/CODE0/'
            
            # Проверяем вывод
            output = out.getvalue()
            assert 'Task started successfully via Celery!' in output
            assert 'Task ID:' in output
    
    def test_command_with_post_id(self, posts, mock_celery_task):
        """Тест команды с параметром --post-id."""
        with patch('instagram_manager.instagram_api.brightdata.clients.comment.CommentClient.get_post_comments', return_value=[]):
            out = StringIO()
            call_command(
                'instagram_comments',
                '--post-id', 'post_1',
                '--limit', '50',
                stdout=out
            )
            
            # Проверяем создание задачи
            mock_celery_task.assert_called_once()
            
            # Проверяем параметры
            task_params = mock_celery_task.call_args[1]
            assert 'post_urls' in task_params
            assert len(task_params['post_urls']) == 1
            assert task_params['post_urls'][0] == 'https://www.instagram.com/p/CODE1/'
            assert task_params['limit_per_post'] == 50
    
    def test_command_with_profile(self, posts, mock_celery_task):
        """Тест команды с параметром --profile."""
        with patch('instagram_manager.instagram_api.brightdata.clients.comment.CommentClient.get_post_comments', return_value=[]):
            out = StringIO()
            call_command(
                'instagram_comments',
                '--profile', 'testuser',
                stdout=out
            )
            
            # Проверяем создание задачи
            mock_celery_task.assert_called_once()
            
            # Проверяем что все посты включены
            task_params = mock_celery_task.call_args[1]
            assert 'post_urls' in task_params
            assert len(task_params['post_urls']) == 3
            expected_urls = {f'https://www.instagram.com/p/CODE{i}/' for i in range(3)}
            assert set(task_params['post_urls']) == expected_urls
    
    def test_command_with_min_likes_filter(self, posts, mock_celery_task):
        """Тест команды с фильтром --min-likes."""
        with patch('instagram_manager.instagram_api.brightdata.clients.comment.CommentClient.get_post_comments', return_value=[]):
            out = StringIO()
            call_command(
                'instagram_comments',
                '--post-url', 'https://instagram.com/p/CODE0/',
                '--min-likes', '10',
                stdout=out
            )
            
            # Проверяем параметры задачи
            task_params = mock_celery_task.call_args[1]
            assert task_params.get('min_likes') == 10
    
    def test_command_with_update_existing(self, posts, mock_celery_task):
        """Тест команды с параметром --update-existing."""
        with patch('instagram_manager.instagram_api.brightdata.clients.comment.CommentClient.get_post_comments', return_value=[]):
            out = StringIO()
            call_command(
                'instagram_comments',
                '--post-url', 'https://instagram.com/p/CODE0/',
                '--update-existing',
                stdout=out
            )
            
            # Проверяем параметры задачи
            task_params = mock_celery_task.call_args[1]
            assert task_params.get('update_existing') is True
    
    def test_command_without_parameters(self):
        """Тест команды без параметров выводит сообщение об ошибке."""
        out = StringIO()
        call_command('instagram_comments', stdout=out)
        
        # Проверяем сообщение об ошибке
        output = out.getvalue()
        assert 'Необходимо указать один из параметров' in output
    
    def test_command_with_invalid_post_url(self, mock_celery_task):
        """Тест команды с невалидным URL поста."""
        out = StringIO()
        call_command(
            'instagram_comments',
            '--post-url', 'https://invalid-url.com/something',
            stdout=out
        )
        
        # Задача не должна быть создана
        mock_celery_task.assert_not_called()
        
        # Проверяем сообщение об ошибке
        output = out.getvalue()
        assert 'Неверный формат URL поста' in output
    
    def test_command_post_not_found(self, mock_celery_task):
        """Тест команды когда пост не найден."""
        out = StringIO()
        call_command(
            'instagram_comments',
            '--post-url', 'https://instagram.com/p/NONEXISTENT/',
            stdout=out
        )
        
        # Задача не должна быть создана
        mock_celery_task.assert_not_called()
        
        # Проверяем сообщение
        output = out.getvalue()
        assert 'не найден' in output
    
    def test_command_profile_not_found(self, mock_celery_task):
        """Тест команды когда профиль не найден."""
        out = StringIO()
        call_command(
            'instagram_comments',
            '--profile', 'nonexistent',
            stdout=out
        )
        
        # Задача не должна быть создана
        mock_celery_task.assert_not_called()
        
        # Проверяем сообщение об ошибке
        output = out.getvalue()
        assert 'Профиль nonexistent не найден' in output
    
    def test_command_profile_without_posts(self, profile, mock_celery_task):
        """Тест команды для профиля без постов."""
        out = StringIO()
        call_command(
            'instagram_comments',
            '--profile', 'testuser',
            stdout=out
        )
        
        # Задача не должна быть создана
        mock_celery_task.assert_not_called()
        
        # Проверяем сообщение
        output = out.getvalue()
        assert 'нет постов' in output
    
    def test_command_with_error_handling(self, posts, mock_celery_task):
        """Тест обработки ошибок при создании задачи."""
        # Мокируем ошибку при создании задачи
        mock_celery_task.side_effect = Exception("Test error")
        
        with patch('instagram_manager.instagram_api.brightdata.clients.comment.CommentClient.get_post_comments', return_value=[]):
            out = StringIO()
            with pytest.raises(Exception):
                call_command(
                    'instagram_comments',
                    '--profile', 'testuser',
                    stdout=out
                )
    
    def test_command_output_statistics(self, posts, mock_celery_task):
        """Тест что команда создает задачу с правильными параметрами."""
        with patch('instagram_manager.instagram_api.brightdata.clients.comment.CommentClient.get_post_comments', return_value=[]):
            out = StringIO()
            call_command(
                'instagram_comments',
                '--post-url', 'https://instagram.com/p/CODE0/',
                stdout=out
            )
            
            # Проверяем что TaskResult создан
            task_results = TaskResult.objects.filter(task_type='instagram.import_batch_comments')
            assert task_results.count() == 1
            
            task_result = task_results.first()
            assert task_result.status == 'pending'
            assert task_result.celery_task_id == 'test-celery-task-id'
            assert 'post_urls' in task_result.parameters