"""Test Instagram profile tools functions"""

import pytest
from unittest.mock import Mock, patch

# Test the underlying database functions directly
@pytest.mark.asyncio
async def test_get_profile_by_username():
    """Test get_profile_by_username database function"""
    from mcp_server.tools.instagram_profiles import get_profile_by_username
    
    # Mock the repository
    mock_repo = Mock()
    mock_profile = Mock()
    mock_profile.id = 1
    mock_profile.username = "testuser"
    mock_repo.get_by_username.return_value = mock_profile
    
    with patch('instagram_manager.repositories.ProfileRepository', return_value=mock_repo):
        # Call the wrapped async function
        result = await get_profile_by_username("testuser")
        
    assert result.id == 1
    assert result.username == "testuser"
    mock_repo.get_by_username.assert_called_once_with("testuser")

@pytest.mark.asyncio
async def test_filter_profiles():
    """Test filter_profiles database function"""
    from mcp_server.tools.instagram_profiles import filter_profiles
    
    # Mock repository and queryset
    mock_repo = Mock()
    mock_queryset = Mock()
    mock_queryset.count.return_value = 2
    mock_queryset.order_by.return_value = mock_queryset
    
    # Create mock profiles
    mock_profiles = [
        Mock(id=1, username="user1"),
        Mock(id=2, username="user2")
    ]
    # Mock slicing behavior
    def mock_getitem(index):
        if isinstance(index, slice):
            return mock_profiles[index]
        return mock_profiles[index]
    mock_queryset.__getitem__ = Mock(side_effect=mock_getitem)
    mock_repo.filter.return_value = mock_queryset
    
    with patch('instagram_manager.repositories.ProfileRepository', return_value=mock_repo):
        profiles, total = await filter_profiles(is_verified=True, limit=10, offset=0, order_by="-follower_count")
        
    assert len(profiles) == 2
    assert total == 2
    assert profiles[0].username == "user1"
    mock_repo.filter.assert_called_once_with(is_verified=True)
    mock_queryset.order_by.assert_called_once_with("-follower_count")

@pytest.mark.asyncio
async def test_search_profiles_function():
    """Test search_profiles database function"""
    from mcp_server.tools.instagram_profiles import search_profiles
    
    # Mock the model queryset
    mock_queryset = Mock()
    mock_profiles = [
        Mock(id=1, username="testuser", full_name="Test User")
    ]
    mock_queryset.filter.return_value = mock_queryset
    mock_queryset.order_by.return_value = mock_queryset
    # Mock slicing for [:limit]
    def mock_getitem(index):
        if isinstance(index, slice):
            return mock_profiles[:index.stop] if index.stop else mock_profiles
        return mock_profiles[index]
    mock_queryset.__getitem__ = Mock(side_effect=mock_getitem)
    
    with patch('instagram_manager.models.InstagramProfile.objects', mock_queryset):
        result = await search_profiles("test", limit=10)
        
    assert len(result) == 1
    assert result[0].username == "testuser"

@pytest.mark.asyncio
async def test_get_profile_statistics():
    """Test get_profile_statistics database function"""
    from mcp_server.tools.instagram_profiles import get_profile_statistics
    
    # Mock aggregation result
    mock_stats = {
        'total_posts': 50,
        'total_likes': 5000,
        'total_comments': 500,
        'avg_likes': 100.0,
        'avg_comments': 10.0
    }
    
    mock_queryset = Mock()
    mock_queryset.filter.return_value = mock_queryset
    mock_queryset.aggregate.return_value = mock_stats
    
    with patch('instagram_manager.models.InstagramPost.objects', mock_queryset):
        result = await get_profile_statistics(1)
        
    assert result['total_posts'] == 50
    assert result['total_likes'] == 5000
    assert result['avg_likes'] == 100.0

# Test error handling
@pytest.mark.asyncio
async def test_profile_not_found():
    """Test handling when profile is not found"""
    from mcp_server.tools.instagram_profiles import get_profile_by_username
    
    mock_service = Mock()
    mock_service.get_by_username.return_value = None
    
    with patch('instagram_manager.services.ProfileService', return_value=mock_service):
        result = await get_profile_by_username("nonexistent")
        
    assert result is None

# Test validation functions
@pytest.mark.asyncio
async def test_profile_validation():
    """Test profile input validation"""
    from mcp_server.tools.instagram_profiles import instagram_search_profiles
    
    # FunctionTool objects have different attributes than regular functions
    # Check that it's a FunctionTool instance with the correct name
    assert hasattr(instagram_search_profiles, 'name')
    assert hasattr(instagram_search_profiles, 'description')
    
    # Verify the tool is properly named
    assert instagram_search_profiles.name == "instagram_search_profiles"
    
    # Verify it has a description
    assert instagram_search_profiles.description is not None
    assert len(instagram_search_profiles.description) > 0