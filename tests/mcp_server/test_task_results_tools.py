"""
Tests for task results retrieval tools
"""

import pytest
from unittest.mock import Mock, patch
from datetime import datetime, timedelta
from django.utils import timezone
import uuid
import json
import base64
import zipfile
import io

from mcp_server.errors import ErrorTypes
from core.models import TaskResult


@pytest.mark.django_db
class TestTaskResultsTools:
    """Test task results MCP tools"""
    
    @pytest.fixture
    def mock_completed_task(self):
        """Create a mock completed TaskResult"""
        task = Mock(spec=TaskResult)
        task.task_id = str(uuid.uuid4())
        task.task_type = "instagram.profile"
        task.task_name = "instagram.profile_import"
        task.status = "completed"
        task.priority = 0
        task.progress_percentage = 100.0
        task.progress_message = "Task completed"
        task.parameters = {"username": "testuser"}
        task.result = {
            "username": "testuser",
            "profile_id": "12345",
            "imported": True,
            "updated": False,
            "statistics": {
                "posts": 50,
                "followers": 1000,
                "following": 500
            }
        }
        task.error_message = ""
        task.created_at = timezone.now() - timedelta(hours=2)
        task.updated_at = timezone.now()
        task.started_at = timezone.now() - timedelta(hours=2)
        task.completed_at = timezone.now()
        task.progress = 100
        task.error = None
        return task
    
    @pytest.fixture
    def mock_failed_task(self):
        """Create a mock failed TaskResult"""
        task = Mock(spec=TaskResult)
        task.task_id = str(uuid.uuid4())
        task.task_type = "instagram.posts"
        task.status = "failed"
        task.parameters = {"username": "testuser", "limit": 50}
        task.result = {
            "error_type": "api_error",
            "error_details": {"status_code": 404}
        }
        task.error_message = "Profile not found"  # Use error_message instead of error
        task.created_at = timezone.now() - timedelta(hours=1)
        task.started_at = timezone.now() - timedelta(hours=1)
        task.completed_at = timezone.now() - timedelta(minutes=30)
        task.progress_percentage = 0.0
        return task
    
    @pytest.mark.asyncio
    async def test_task_get_result_success(self, mock_completed_task):
        """Test getting single task result successfully"""
        from mcp_server.tools.task_results import task_get_result
        
        with patch('core.models.TaskResult.objects.filter') as mock_filter:
            mock_qs = Mock()
            mock_qs.first.return_value = mock_completed_task
            mock_filter.return_value = mock_qs
            
            # Call the underlying function
            result = await task_get_result.fn(
                task_id=mock_completed_task.task_id,
                include_raw=True,
                include_metadata=True
            )
        
        assert result["task_id"] == mock_completed_task.task_id
        assert result["task_type"] == "instagram.profile"
        assert result["status"] == "completed"
        assert "metadata" in result
        assert "result" in result
        assert result["result"]["profile"]["username"] == "testuser"
        assert "_raw" in result["result"]  # include_raw=True
    
    @pytest.mark.asyncio
    async def test_task_get_result_not_found(self):
        """Test getting non-existent task result"""
        from mcp_server.tools.task_results import task_get_result
        
        with patch('core.models.TaskResult.objects.filter') as mock_filter:
            mock_qs = Mock()
            mock_qs.first.return_value = None
            mock_filter.return_value = mock_qs
            
            result = await task_get_result.fn(task_id="invalid-id")
        
        assert result["error_type"] == ErrorTypes.NOT_FOUND
        assert "not found" in result["message"]
    
    @pytest.mark.asyncio
    async def test_task_get_result_still_running(self):
        """Test getting result for still running task"""
        from mcp_server.tools.task_results import task_get_result
        
        running_task = Mock(spec=TaskResult)
        running_task.status = "running"
        
        with patch('core.models.TaskResult.objects.filter') as mock_filter:
            mock_qs = Mock()
            mock_qs.first.return_value = running_task
            mock_filter.return_value = mock_qs
            
            result = await task_get_result.fn(task_id="test-id")
        
        assert result["error_type"] == ErrorTypes.INVALID_STATE
        assert "still running" in result["message"]
    
    @pytest.mark.asyncio
    async def test_task_get_batch_results(self, mock_completed_task, mock_failed_task):
        """Test getting batch task results"""
        from mcp_server.tools.task_results import task_get_batch_results
        
        with patch('core.models.TaskResult.objects.filter') as mock_filter:
            mock_qs = Mock()
            mock_qs.order_by.return_value = [mock_completed_task, mock_failed_task]
            mock_filter.return_value = mock_qs
            
            result = await task_get_batch_results.fn(
                task_ids=[mock_completed_task.task_id, mock_failed_task.task_id],
                summary_only=True
            )
        
        assert result["requested"] == 2
        assert result["found"] == 2
        assert len(result["results"]) == 2
        
        # Check summary format
        completed_result = result["results"][0]
        assert "summary" in completed_result
        assert completed_result["summary"]["username"] == "testuser"
        
        failed_result = result["results"][1]
        assert "error" in failed_result
        assert failed_result["error"] == "Profile not found"
    
    @pytest.mark.asyncio
    async def test_task_get_batch_results_validation(self):
        """Test batch results validation"""
        from mcp_server.tools.task_results import task_get_batch_results
        
        # Test empty list
        result = await task_get_batch_results.fn(task_ids=[])
        assert result["error_type"] == ErrorTypes.VALIDATION_ERROR
        
        # Test too many tasks
        result = await task_get_batch_results.fn(task_ids=[str(uuid.uuid4()) for _ in range(51)])
        assert result["error_type"] == ErrorTypes.VALIDATION_ERROR
        assert "Maximum 50 tasks" in result["message"]
    
    @pytest.mark.asyncio
    async def test_task_export_results_json(self, mock_completed_task):
        """Test exporting task results as JSON"""
        from mcp_server.tools.task_results import task_export_results
        
        with patch('core.models.TaskResult.objects.filter') as mock_filter:
            mock_qs = Mock()
            mock_qs.order_by.return_value = [mock_completed_task]
            mock_filter.return_value = mock_qs
            
            result = await task_export_results.fn(
                task_ids=[mock_completed_task.task_id],
                format="json",
                include_metadata=True,
                compress=False
            )
        
        assert result["format"] == "json"
        assert result["compressed"] is False
        assert result["tasks_count"] == 1
        assert "data" in result
        
        # Decode and verify JSON
        decoded = base64.b64decode(result["data"])
        data = json.loads(decoded)
        assert len(data) == 1
        assert data[0]["task_id"] == mock_completed_task.task_id
        assert "metadata" in data[0]
        assert data[0]["result"] == mock_completed_task.result
    
    @pytest.mark.asyncio
    async def test_task_export_results_compressed(self, mock_completed_task):
        """Test exporting task results with compression"""
        from mcp_server.tools.task_results import task_export_results
        
        with patch('core.models.TaskResult.objects.filter') as mock_filter:
            mock_qs = Mock()
            mock_qs.order_by.return_value = [mock_completed_task]
            mock_filter.return_value = mock_qs
            
            result = await task_export_results.fn(
                task_ids=[mock_completed_task.task_id],
                format="json",
                compress=True
            )
        
        assert result["compressed"] is True
        
        # Decode and verify ZIP
        decoded = base64.b64decode(result["data"])
        with zipfile.ZipFile(io.BytesIO(decoded)) as zf:
            assert "task_results.json" in zf.namelist()
            with zf.open("task_results.json") as f:
                data = json.load(f)
                assert len(data) == 1
    
    @pytest.mark.asyncio
    async def test_task_search_results(self, mock_completed_task):
        """Test searching task results"""
        from mcp_server.tools.task_results import task_search_results
        
        with patch('core.models.TaskResult.objects.all') as mock_all:
            mock_qs = Mock()
            mock_qs.filter.return_value = mock_qs
            mock_qs.order_by.return_value = [mock_completed_task]
            mock_all.return_value = mock_qs
            
            result = await task_search_results.fn(
                query="testuser",
                task_type="instagram.profile",
                status="completed",
                limit=10
            )
        
        assert result["query"] == "testuser"
        assert result["count"] == 1
        assert len(result["results"]) == 1
        
        search_result = result["results"][0]
        assert search_result["task_id"] == mock_completed_task.task_id
        assert "relevance" in search_result
        assert "snippet" in search_result
    
    @pytest.mark.asyncio
    async def test_task_search_results_date_range(self):
        """Test searching with date range"""
        from mcp_server.tools.task_results import task_search_results
        
        with patch('core.models.TaskResult.objects.all') as mock_all:
            mock_qs = Mock()
            mock_qs.filter.return_value = mock_qs
            mock_qs.order_by.return_value = []
            mock_all.return_value = mock_qs
            
            date_from = (datetime.now() - timedelta(days=7)).isoformat()
            date_to = datetime.now().isoformat()
            
            result = await task_search_results.fn(
                query="test",
                date_from=date_from,
                date_to=date_to
            )
        
        assert result["filters"]["date_range"]["from"] == date_from
        assert result["filters"]["date_range"]["to"] == date_to
    
    @pytest.mark.asyncio
    async def test_task_aggregate_results(self):
        """Test task results aggregation"""
        from mcp_server.tools.task_aggregation import task_aggregate_results
        
        # Create mock tasks for aggregation
        tasks = []
        base_time = timezone.now() - timedelta(days=3)
        
        for i in range(10):
            task = Mock(spec=TaskResult)
            task.created_at = base_time + timedelta(hours=i * 6)
            task.started_at = task.created_at
            task.completed_at = task.created_at + timedelta(minutes=30)
            task.status = "completed" if i % 3 != 0 else "failed"
            task.result = {"total_posts": 50} if task.status == "completed" else {}
            tasks.append(task)
        
        with patch('core.models.TaskResult.objects.filter') as mock_filter:
            mock_qs = Mock()
            mock_qs.filter.return_value = mock_qs
            mock_qs.order_by.return_value = tasks
            mock_filter.return_value = mock_qs
            
            result = await task_aggregate_results.fn(
                task_type="instagram.posts",
                aggregation="daily"
            )
        
        assert result["task_type"] == "instagram.posts"
        assert result["aggregation"] == "daily"
        assert len(result["data"]) > 0
        
        # Check aggregation data
        for period_data in result["data"]:
            assert "period" in period_data
            assert "total" in period_data
            assert "completed" in period_data
            assert "failed" in period_data
            assert "success_rate" in period_data
            assert "avg_duration" in period_data
    
    @pytest.mark.asyncio
    async def test_result_processing_by_type(self):
        """Test result processing for different task types"""
        from mcp_server.tools.task_results import _process_task_result
        
        # Test Instagram profile
        profile_result = await _process_task_result(
            task_type="instagram.profile",
            result={"username": "test", "profile_id": "123", "imported": True},
            include_raw=False
        )
        assert "profile" in profile_result
        assert profile_result["profile"]["username"] == "test"
        
        # Test Instagram posts
        posts_result = await _process_task_result(
            task_type="instagram.posts",
            result={"total_posts": 100, "new_posts": 50},
            include_raw=False
        )
        assert "summary" in posts_result
        assert posts_result["summary"]["total_posts"] == 100
        
        # Test Telegram messages
        messages_result = await _process_task_result(
            task_type="telegram.messages",
            result={"messages_imported": 200, "chat_id": 123},
            include_raw=False
        )
        assert messages_result["messages_imported"] == 200
        assert messages_result["chat_id"] == 123
    
    @pytest.mark.asyncio
    async def test_relevance_scoring(self):
        """Test search relevance scoring"""
        from mcp_server.tools.task_results import _calculate_relevance
        
        task = Mock(spec=TaskResult)
        task.task_type = "instagram.profile"  # Use task_type instead of task_name
        task.parameters = {"username": "testuser"}
        task.result = {"username": "testuser", "posts": 100}
        
        # Test case 1: Search for "instagram" - matches task_type
        score1 = _calculate_relevance(task, "instagram")
        
        # Test case 2: Search for "testuser" - matches in parameters and result
        score2 = _calculate_relevance(task, "testuser")
        
        # Both should have positive scores
        assert score1 > 0  # "instagram" in task_type
        assert score2 > 0  # "testuser" in parameters and result
        
        # Test case 3: No match
        score3 = _calculate_relevance(task, "nomatch")
        assert score3 == 0.0
    
    @pytest.mark.asyncio
    async def test_snippet_extraction(self):
        """Test snippet extraction for search results"""
        from mcp_server.tools.task_results import _extract_snippet
        
        task = Mock(spec=TaskResult)
        task.parameters = {"username": "exampleuser", "limit": 50}
        task.result = {"total_posts": 100, "username": "exampleuser"}
        
        snippet = _extract_snippet(task, "exampleuser")
        assert "exampleuser" in snippet
        assert "..." in snippet  # Should have context markers
    
    @pytest.mark.asyncio
    async def test_invalid_export_format(self):
        """Test export with invalid format"""
        from mcp_server.tools.task_results import task_export_results
        
        result = await task_export_results.fn(
            task_ids=["test-id"],
            format="invalid"
        )
        
        assert result["error_type"] == ErrorTypes.VALIDATION_ERROR
        assert "Invalid format" in result["message"]
    
    @pytest.mark.asyncio
    async def test_duration_calculation(self):
        """Test task duration calculation"""
        from mcp_server.tools.task_results import _calculate_duration
        
        task = Mock()
        task.started_at = datetime.now() - timedelta(minutes=30)
        task.completed_at = datetime.now()
        
        duration = _calculate_duration(task)
        assert duration is not None
        assert 1700 < duration < 1900  # Around 30 minutes
        
        # Test with missing times
        task.started_at = None
        duration = _calculate_duration(task)
        assert duration is None