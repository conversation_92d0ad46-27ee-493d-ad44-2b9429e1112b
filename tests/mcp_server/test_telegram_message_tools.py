"""Test Telegram message tools functions"""

import pytest
from unittest.mock import Mock, patch
from datetime import datetime, timedelta
from django.utils import timezone

# Test the underlying database functions directly

@pytest.mark.asyncio
async def test_get_messages_from_chat_db():
    """Test get_messages_from_chat_db database function"""
    from mcp_server.tools.telegram_messages import get_messages_from_chat_db
    
    # Mock messages
    mock_message1 = Mock()
    mock_message1.message_id = 1
    mock_message1.chat_id = 123456
    mock_message1.text = "Test message 1"
    mock_message1.date = timezone.now()
    mock_message1.from_user_id = 789
    mock_message1.media_type = ""
    mock_message1.is_reply = False
    mock_message1.is_forward = False
    mock_message1.views = 100
    
    # Mock user
    mock_user = Mock()
    mock_user.user_id = 789
    mock_user.username = "testuser"
    mock_user.first_name = "Test"
    mock_user.last_name = "User"
    mock_message1.from_user = mock_user
    
    mock_message2 = Mock()
    mock_message2.message_id = 2
    mock_message2.chat_id = 123456
    mock_message2.text = ""
    mock_message2.date = timezone.now() - timedelta(hours=1)
    mock_message2.from_user_id = 790
    mock_message2.media_type = "photo"
    mock_message2.is_reply = True
    mock_message2.is_forward = False
    mock_message2.photo = {"id": "photo123"}
    
    mock_queryset = Mock()
    mock_queryset.filter.return_value = mock_queryset
    mock_queryset.select_related.return_value = mock_queryset
    mock_queryset.order_by.return_value = mock_queryset
    mock_queryset.count.return_value = 2
    
    # Mock slicing behavior for pagination
    def mock_getitem(key):
        if isinstance(key, slice):
            return [mock_message1, mock_message2]
        return [mock_message1, mock_message2][key]
    
    mock_queryset.__getitem__ = Mock(side_effect=mock_getitem)
    
    with patch('telegram_manager.models.TelegramMessage.objects', mock_queryset):
        messages, total = await get_messages_from_chat_db(
            chat_id=123456,
            limit=50,
            offset=0,
            start_date="2025-01-10",
            end_date="2025-01-11",
            from_user_id=789,
            message_type="text"
        )
        
    assert len(messages) == 2
    assert total == 2
    assert messages[0].message_id == 1
    assert messages[0].text == "Test message 1"
    
    # Verify filters were applied
    mock_queryset.filter.assert_any_call(chat_id=123456)
    mock_queryset.select_related.assert_called_with('from_user', 'reply_to')


@pytest.mark.asyncio
async def test_search_messages_db():
    """Test search_messages_db function"""
    from mcp_server.tools.telegram_messages import search_messages_db
    
    # Mock search results
    mock_message1 = Mock()
    mock_message1.message_id = 1
    mock_message1.chat_id = 123
    mock_message1.text = "Python is awesome"
    mock_message1.date = timezone.now()
    
    mock_chat1 = Mock()
    mock_chat1.chat_id = 123
    mock_chat1.title = "Python Chat"
    mock_message1.chat = mock_chat1
    
    mock_user1 = Mock()
    mock_user1.username = "pythonfan"
    mock_message1.from_user = mock_user1
    
    mock_message2 = Mock()
    mock_message2.message_id = 2
    mock_message2.chat_id = 456
    mock_message2.text = "Learning Python today"
    mock_message2.date = timezone.now() - timedelta(hours=2)
    
    mock_chat2 = Mock()
    mock_chat2.chat_id = 456
    mock_chat2.title = "Dev Chat"
    mock_message2.chat = mock_chat2
    
    mock_user2 = Mock()
    mock_user2.username = "developer"
    mock_message2.from_user = mock_user2
    
    mock_queryset = Mock()
    mock_queryset.filter.return_value = mock_queryset
    mock_queryset.select_related.return_value = mock_queryset
    mock_queryset.order_by.return_value = mock_queryset
    
    # Mock slicing behavior
    def mock_getitem(key):
        if isinstance(key, slice):
            return [mock_message1, mock_message2]
        return [mock_message1, mock_message2][key]
    
    mock_queryset.__getitem__ = Mock(side_effect=mock_getitem)
    
    with patch('telegram_manager.models.TelegramMessage.objects', mock_queryset):
        results = await search_messages_db(
            query="python",
            chat_ids=[123, 456],
            limit=50
        )
        
    assert len(results) == 2
    assert results[0].text == "Python is awesome"
    # Verify search filter was applied
    mock_queryset.filter.assert_any_call(text__icontains="python")


@pytest.mark.asyncio
async def test_get_message_statistics_db():
    """Test get_message_statistics_db function"""
    from mcp_server.tools.telegram_messages import get_message_statistics_db
    
    # Mock aggregate statistics
    mock_stats = {
        'total_count': 1000,
        'avg_length': 45.5
    }
    
    # Mock hourly distribution
    mock_hourly = [
        {'hour': 9, 'count': 50},
        {'hour': 10, 'count': 100},
        {'hour': 14, 'count': 150},
        {'hour': 20, 'count': 80}
    ]
    
    # Mock media stats
    mock_media_stats = [
        {'media_type': 'photo', 'count': 300},
        {'media_type': 'video', 'count': 100},
        {'media_type': '', 'count': 600}  # text messages
    ]
    
    # Mock top users
    mock_top_users = [
        {
            'from_user__user_id': 1,
            'from_user__username': 'activeuser',
            'from_user__first_name': 'Active',
            'from_user__last_name': 'User',
            'message_count': 200
        }
    ]
    
    mock_queryset = Mock()
    mock_queryset.filter.return_value = mock_queryset
    mock_queryset.aggregate.return_value = mock_stats
    mock_queryset.values.return_value = mock_queryset
    mock_queryset.annotate.return_value = mock_queryset
    mock_queryset.order_by.return_value = mock_queryset
    
    # Mock for extra() chain (hourly stats)
    mock_extra_qs = Mock()
    mock_extra_qs.values.return_value = mock_extra_qs
    mock_extra_qs.annotate.return_value = mock_extra_qs
    mock_extra_qs.order_by.return_value = mock_hourly
    mock_queryset.extra.return_value = mock_extra_qs
    
    # Different return values for different chains
    call_count = 0
    def mock_order_by_side_effect(*args):
        nonlocal call_count
        call_count += 1
        if call_count == 1:  # First call - media stats
            return mock_media_stats
        elif call_count == 2:  # Second call - top users
            return mock_top_users[:10]  # Simulate slicing
        return mock_queryset
    
    mock_queryset.order_by.side_effect = mock_order_by_side_effect
    
    # Mock slicing
    def mock_getitem(key):
        if isinstance(key, slice) and key.stop == 10:
            return mock_top_users
        return mock_top_users[key] if isinstance(key, int) else mock_top_users
    
    mock_queryset.__getitem__ = Mock(side_effect=mock_getitem)
    
    with patch('telegram_manager.models.TelegramMessage.objects', mock_queryset):
        with patch('django.utils.timezone.now', return_value=datetime(2025, 1, 11, 12, 0, 0)):
            result = await get_message_statistics_db(123456, "week", "hour")
    
    assert result['total_messages'] == 1000
    assert result['avg_message_length'] == 45.5
    assert len(result['hourly_distribution']) == 4
    assert result['hourly_distribution'][0]['hour'] == 9
    assert result['media_breakdown']['photo'] == 300
    assert result['media_breakdown']['text'] == 600
    assert len(result['top_users']) == 1
    assert result['top_users'][0]['from_user__username'] == 'activeuser'


# Test helper functions

def test_parse_date_filter():
    """Test _parse_date_filter helper function"""
    from mcp_server.tools.telegram_messages import _parse_date_filter
    
    # Test valid date string
    date_str = "2025-01-11"
    parsed = _parse_date_filter(date_str)
    assert parsed is not None
    assert parsed.year == 2025
    assert parsed.month == 1
    assert parsed.day == 11
    
    # Test None
    assert _parse_date_filter(None) is None
    
    # Test invalid date
    assert _parse_date_filter("invalid-date") is None
    
    # Test datetime string with time
    date_str = "2025-01-11T10:30:00"
    parsed = _parse_date_filter(date_str)
    assert parsed is not None
    assert parsed.hour == 10
    assert parsed.minute == 30


def test_format_message_for_response():
    """Test _format_message_for_response helper function"""
    from mcp_server.tools.telegram_messages import _format_message_for_response
    
    # Mock message with user
    mock_user = Mock()
    mock_user.user_id = 123
    mock_user.username = "testuser"
    mock_user.first_name = "Test"
    mock_user.last_name = "User"
    
    mock_message = Mock()
    mock_message.message_id = 1
    mock_message.chat_id = 456
    mock_message.text = "Hello world"
    mock_message.date = datetime(2025, 1, 11, 10, 0, 0)
    mock_message.from_user = mock_user
    mock_message.from_user_id = 123
    mock_message.media_type = "photo"
    mock_message.is_reply = True
    mock_message.is_forward = False
    mock_message.is_edited = True
    mock_message.views = 100
    mock_message.forwards = 5
    
    formatted = _format_message_for_response(mock_message)
    
    assert formatted['message_id'] == 1
    assert formatted['chat_id'] == 456
    assert formatted['text'] == "Hello world"
    assert formatted['date'] == "2025-01-11T10:00:00"
    assert formatted['from_user']['user_id'] == 123
    assert formatted['from_user']['username'] == "testuser"
    assert formatted['media_type'] == "photo"
    assert formatted['is_reply'] is True
    assert formatted['is_forward'] is False
    assert formatted['is_edited'] is True
    assert formatted['views'] == 100
    assert formatted['forwards'] == 5


def test_calculate_statistics_period():
    """Test _calculate_statistics_period helper function"""
    from mcp_server.tools.telegram_messages import _calculate_statistics_period
    
    # Mock current time
    current_time = datetime(2025, 1, 11, 12, 0, 0)
    
    with patch('django.utils.timezone.now', return_value=current_time):
        # Test day period
        start, end = _calculate_statistics_period("day")
        assert (end - start).days == 1
        
        # Test week period
        start, end = _calculate_statistics_period("week")
        assert (end - start).days == 7
        
        # Test month period
        start, end = _calculate_statistics_period("month")
        assert (end - start).days >= 28
        
        # Test year period
        start, end = _calculate_statistics_period("year")
        assert (end - start).days >= 365
        
        # Test invalid period (defaults to week)
        start, end = _calculate_statistics_period("invalid")
        assert (end - start).days == 7


# Test tool registration
def test_tools_are_registered():
    """Test that all Telegram message tools are properly registered"""
    from mcp_server.tools.telegram_messages import (
        telegram_get_messages,
        telegram_search_messages,
        telegram_get_message_statistics
    )
    
    # Check that tools have required attributes
    tools = [
        telegram_get_messages,
        telegram_search_messages,
        telegram_get_message_statistics
    ]
    
    for tool in tools:
        assert hasattr(tool, 'name')
        assert hasattr(tool, 'description')
        assert tool.name is not None
        assert tool.description is not None