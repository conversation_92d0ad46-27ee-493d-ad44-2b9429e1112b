"""Test Instagram media tools functions"""

import pytest
from unittest.mock import Mock, patch

# Test the underlying database functions
@pytest.mark.asyncio
async def test_get_media_for_posts():
    """Test get_media_for_posts database function"""
    from mcp_server.tools.instagram_media import get_media_for_posts
    
    # Mock media items
    mock_media1 = Mock()
    mock_media1.id = 1
    mock_media1.post_id = 1
    mock_media1.media_type = "photo"
    mock_media1.media_url = "https://example.com/photo1.jpg"
    mock_media1.position = 0
    
    mock_media2 = Mock()
    mock_media2.id = 2
    mock_media2.post_id = 1
    mock_media2.media_type = "photo"
    mock_media2.media_url = "https://example.com/photo2.jpg"
    mock_media2.position = 1
    
    mock_media3 = Mock()
    mock_media3.id = 3
    mock_media3.post_id = 2
    mock_media3.media_type = "video"
    mock_media3.media_url = "https://example.com/video.mp4"
    mock_media3.position = 0
    
    # Mock queryset
    mock_queryset = Mock()
    mock_queryset.filter.return_value = mock_queryset
    mock_queryset.select_related.return_value = mock_queryset
    mock_queryset.order_by.return_value = [mock_media1, mock_media2, mock_media3]
    
    with patch("instagram_manager.models.InstagramMedia.objects", mock_queryset):
        media_by_post = await get_media_for_posts([1, 2])
    
    assert len(media_by_post) == 2
    assert len(media_by_post[1]) == 2
    assert len(media_by_post[2]) == 1
    assert media_by_post[1][0].id == 1
    assert media_by_post[1][1].id == 2
    assert media_by_post[2][0].id == 3

@pytest.mark.asyncio
async def test_download_media_files():
    """Test download_media_files database function"""
    from mcp_server.tools.instagram_media import download_media_files
    
    # Mock post with media
    mock_media1 = Mock()
    mock_media1.id = 1
    mock_media1.media_type = "photo"
    mock_media1.media_url = "https://example.com/photo.jpg"
    mock_media1.is_downloaded = False
    mock_media1.local_file = None
    mock_media1.save = Mock()
    
    mock_media2 = Mock()
    mock_media2.id = 2
    mock_media2.media_type = "video"
    mock_media2.media_url = "https://example.com/video.mp4"
    mock_media2.is_downloaded = True
    mock_media2.local_file = Mock(url="/media/video.mp4")
    
    mock_post = Mock()
    mock_post.id = 1
    mock_post.external_id = "ext123"
    mock_post.media = Mock()
    mock_post.media.all.return_value = [mock_media1, mock_media2]
    
    # Mock InstagramPost.objects
    mock_post_objects = Mock()
    mock_post_objects.prefetch_related.return_value = mock_post_objects
    mock_post_objects.get.return_value = mock_post
    
    # Mock MediaService
    mock_service = Mock()
    mock_service.download_media_for_post.return_value = {
        "downloaded": 1,
        "failed": 0
    }
    
    # Mock MediaHandler for GCS
    mock_handler = Mock()
    mock_handler.download_media.return_value = True
    
    with patch("instagram_manager.models.InstagramPost.objects", mock_post_objects):
        with patch("instagram_manager.services.MediaService", return_value=mock_service):
            with patch("instagram_manager.instagram_api.data_handlers.media_handler.MediaHandler", return_value=mock_handler):
                results = await download_media_files(1, None, True)
    
    assert len(results) == 2
    assert results[0]["status"] == "downloaded"
    assert results[1]["status"] == "already_downloaded"

@pytest.mark.asyncio
async def test_download_media_with_filter():
    """Test download_media_files with media type filter"""
    from mcp_server.tools.instagram_media import download_media_files
    
    # Mock post with mixed media
    mock_photo = Mock()
    mock_photo.id = 1
    mock_photo.media_type = "photo"
    mock_photo.media_url = "https://example.com/photo.jpg"
    mock_photo.is_downloaded = False
    mock_photo.local_file = None
    mock_photo.save = Mock()
    
    mock_video = Mock()
    mock_video.id = 2
    mock_video.media_type = "video"
    mock_video.media_url = "https://example.com/video.mp4"
    mock_video.is_downloaded = False
    mock_video.local_file = None
    
    mock_post = Mock()
    mock_post.id = 1
    mock_post.external_id = "ext123"
    mock_post.media = Mock()
    mock_post.media.all.return_value = [mock_photo, mock_video]
    
    # Mock InstagramPost.objects
    mock_post_objects = Mock()
    mock_post_objects.prefetch_related.return_value = mock_post_objects
    mock_post_objects.get.return_value = mock_post
    
    # Mock MediaService
    mock_service = Mock()
    mock_service.download_media_for_post.return_value = {
        "downloaded": 1,
        "failed": 0
    }
    
    with patch("instagram_manager.models.InstagramPost.objects", mock_post_objects):
        with patch("instagram_manager.services.MediaService", return_value=mock_service):
            # Only download photos
            results = await download_media_files(1, ["photo"], False)
    
    assert len(results) == 1  # Only photo should be processed
    assert results[0]["media_id"] == 1
    assert results[0]["status"] == "downloaded"

@pytest.mark.asyncio
async def test_get_media_statistics():
    """Test get_media_statistics database function"""
    from mcp_server.tools.instagram_media import get_media_statistics
    
    # Mock posts with annotations
    mock_post1 = Mock()
    mock_post1.id = 1
    mock_post1.profile = Mock(username="user1")
    mock_post1.total_media = 3
    mock_post1.photo_count = 2
    mock_post1.video_count = 1
    mock_post1.downloaded_count = 2
    
    mock_post2 = Mock()
    mock_post2.id = 2
    mock_post2.profile = Mock(username="user2")
    mock_post2.total_media = 1
    mock_post2.photo_count = 0
    mock_post2.video_count = 1
    mock_post2.downloaded_count = 0
    
    # Mock queryset
    mock_queryset = Mock()
    mock_queryset.filter.return_value = mock_queryset
    mock_queryset.annotate.return_value = mock_queryset
    mock_queryset.select_related.return_value = [mock_post1, mock_post2]
    
    with patch("instagram_manager.models.InstagramPost.objects", mock_queryset):
        stats = await get_media_statistics([1, 2])
    
    assert len(stats) == 2
    assert stats[0]["post_id"] == 1
    assert stats[0]["total_media"] == 3
    assert stats[0]["download_percentage"] == 66.67  # 2/3 * 100
    assert stats[1]["post_id"] == 2
    assert stats[1]["download_percentage"] == 0.0

# Test download error handling
@pytest.mark.asyncio
async def test_download_media_error():
    """Test download_media_files error handling"""
    from mcp_server.tools.instagram_media import download_media_files
    
    # Mock media that fails to download
    mock_media = Mock()
    mock_media.id = 1
    mock_media.media_type = "photo"
    mock_media.media_url = "https://example.com/photo.jpg"
    mock_media.is_downloaded = False
    mock_media.local_file = None
    
    mock_post = Mock()
    mock_post.id = 1
    mock_post.external_id = "ext123"
    mock_post.media = Mock()
    mock_post.media.all.return_value = [mock_media]
    
    # Mock InstagramPost.objects
    mock_post_objects = Mock()
    mock_post_objects.prefetch_related.return_value = mock_post_objects
    mock_post_objects.get.return_value = mock_post
    
    # Mock MediaService that raises error
    mock_service = Mock()
    mock_service.download_media_for_post.side_effect = Exception("Download failed")
    
    with patch("instagram_manager.models.InstagramPost.objects", mock_post_objects):
        with patch("instagram_manager.services.MediaService", return_value=mock_service):
            results = await download_media_files(1, None, False)
    
    assert len(results) == 1
    assert results[0]["status"] == "failed"
    assert "Download failed" in results[0]["error"]

# Test tool validation
def test_media_type_validation():
    """Test media type validation in tools"""
    from mcp_server.tools.instagram_media import instagram_download_media
    
    # Check that the tool is properly defined
    assert hasattr(instagram_download_media, "name")
    assert instagram_download_media.name == "instagram_download_media"
    assert hasattr(instagram_download_media, "description")

def test_bulk_download_validation():
    """Test bulk download validation"""
    from mcp_server.tools.instagram_media import instagram_bulk_download_media
    
    # Check that the tool is properly defined
    assert hasattr(instagram_bulk_download_media, "name")
    assert instagram_bulk_download_media.name == "instagram_bulk_download_media"
    assert hasattr(instagram_bulk_download_media, "description")