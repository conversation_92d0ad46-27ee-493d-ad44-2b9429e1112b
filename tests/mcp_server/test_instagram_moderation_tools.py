"""
Tests for Instagram comment moderation tools
"""

from unittest.mock import Mock, patch

import pytest

from mcp_server.tools.instagram_moderation import (
    analyze_comment_sentiment,
    bulk_delete_comments,
    detect_spam_comments,
    get_top_commenters,
)


class TestSpamDetection:
    """Test spam detection functionality"""

    @pytest.mark.asyncio
    async def test_detect_spam_comments(self):
        """Test spam comment detection"""
        mock_comments = [
            Mock(
                id=1,
                comment_id="comment_1",
                author_username="spammer1",
                text="Check my profile for free followers!!!",
                commented_at=Mock(isoformat=Mock(return_value="2024-01-15T10:00:00")),
            ),
            Mock(
                id=2,
                comment_id="comment_2",
                author_username="user2",
                text="Beautiful photo!",
                commented_at=Mock(isoformat=Mock(return_value="2024-01-15T11:00:00")),
            ),
            <PERSON><PERSON>(
                id=3,
                comment_id="comment_3",
                author_username="spammer2",
                text="DM for collaboration 💰💰💰💰💰 https://spam.com https://spam2.com",
                commented_at=Mock(isoformat=Mock(return_value="2024-01-15T12:00:00")),
            ),
        ]

        class MockQuerySet:
            def __init__(self, items):
                self.items = items

            def count(self):
                return len(self.items)

            def __iter__(self):
                return iter(self.items)

        with patch("instagram_manager.services.CommentService") as mock_service_class:
            mock_service = Mock()
            mock_service.repository.get_for_post.return_value = MockQuerySet(mock_comments)
            mock_service_class.return_value = mock_service

            result = await detect_spam_comments(post_id=123, threshold=0.5)

            assert result["post_id"] == 123
            assert result["total_comments"] == 3
            assert len(result["spam_comments"]) == 2
            assert result["clean_comments"] == 1
            assert result["spam_percentage"] == 66.67

            # Check spam detection reasons
            if len(result["spam_comments"]) > 0:
                spam1 = result["spam_comments"][0]
                print(f"Spam 1: {spam1}")
                
            # Let's check what was detected as spam
            for spam in result["spam_comments"]:
                print(f"Detected spam: {spam['username']} - Score: {spam['spam_score']} - Reasons: {spam['reasons']}")

    @pytest.mark.asyncio
    async def test_detect_spam_with_patterns(self):
        """Test spam detection with various patterns"""
        mock_comments = [
            Mock(
                id=1,
                comment_id="comment_1",
                author_username="user1",
                text="Follow for follow! F4F",
                commented_at=Mock(isoformat=Mock(return_value="2024-01-15T10:00:00")),
            ),
            Mock(
                id=2,
                comment_id="comment_2",
                author_username="user2",
                text="aaaaaaaaaaaaa",  # Repetitive characters
                commented_at=Mock(isoformat=Mock(return_value="2024-01-15T11:00:00")),
            ),
            Mock(
                id=3,
                comment_id="comment_3",
                author_username="user3",
                text="@user1 @user2 @user3 @user4 @user5",  # Excessive mentions
                commented_at=Mock(isoformat=Mock(return_value="2024-01-15T12:00:00")),
            ),
        ]

        class MockQuerySet:
            def __init__(self, items):
                self.items = items

            def count(self):
                return len(self.items)

            def __iter__(self):
                return iter(self.items)

        with patch("instagram_manager.services.CommentService") as mock_service_class:
            mock_service = Mock()
            mock_service.repository.get_for_post.return_value = MockQuerySet(mock_comments)
            mock_service_class.return_value = mock_service

            result = await detect_spam_comments(post_id=123, threshold=0.3)

            assert len(result["spam_comments"]) >= 2
            # F4F should be detected
            assert any(c["username"] == "user1" for c in result["spam_comments"])
            # Excessive mentions should be detected
            assert any(c["username"] == "user3" for c in result["spam_comments"])


class TestTopCommenters:
    """Test top commenters functionality"""

    @pytest.mark.asyncio
    async def test_get_top_commenters(self):
        """Test getting top commenters for a profile"""
        # Create mock sample comments queryset
        class MockSlice:
            def __init__(self, items):
                self.items = items
            
            def values(self, *fields):
                return self.items
        
        class MockFilteredQuerySet:
            def __init__(self):
                self.items = [
                    {"text": "Love this!", "like_count": 100},
                    {"text": "Amazing!", "like_count": 50},
                    {"text": "Great work!", "like_count": 25},
                ]
            
            def order_by(self, field):
                return self
            
            def values(self, *fields):
                return self.items
            
            def __getitem__(self, key):
                if isinstance(key, slice):
                    # Return a mock that has a values() method
                    return MockSlice(self.items[key])
                return self.items[key]
        
        # Mock main comments queryset
        mock_comments = Mock()
        mock_comments.exclude.return_value = mock_comments
        mock_comments.values.return_value = mock_comments

        # Mock annotated results - needs to use author_username, not username
        mock_top_commenters = [
            {"author_username": "fan1", "comment_count": 50, "unique_posts": 30},
            {"author_username": "fan2", "comment_count": 40, "unique_posts": 35},
            {"author_username": "fan3", "comment_count": 30, "unique_posts": 20},
        ]
        mock_comments.annotate.return_value = mock_comments
        mock_comments.order_by.return_value = mock_top_commenters[:2]  # Limit to 2

        # Mock filter return value with our sliceable queryset
        mock_filtered_qs = MockFilteredQuerySet()
        mock_comments.filter.return_value = mock_filtered_qs

        # Mock distinct count
        mock_distinct = Mock()
        mock_distinct.count.return_value = 150

        with patch("instagram_manager.models.InstagramComment") as mock_comment_model:
            mock_comment_model.objects.filter.return_value = mock_comments
            mock_comments.values.return_value.distinct.return_value = mock_distinct

            result = await get_top_commenters(profile_username="testprofile", limit=2)

            assert result["profile"] == "testprofile"
            assert len(result["top_commenters"]) == 2
            assert result["top_commenters"][0]["username"] == "fan1"
            assert result["top_commenters"][0]["comment_count"] == 50
            assert result["top_commenters"][0]["engagement_rate"] == 60.0  # 30/50 * 100
            assert result["total_unique_commenters"] == 150


class TestBulkOperations:
    """Test bulk operations"""

    @pytest.mark.asyncio
    async def test_bulk_delete_comments(self):
        """Test bulk comment deletion"""
        mock_comments = [
            Mock(id=1, is_hidden=False, raw_data={}),
            Mock(id=2, is_hidden=False, raw_data={}),
            None,  # Comment not found
        ]

        with patch("instagram_manager.services.CommentService") as mock_service_class:
            mock_service = Mock()
            mock_service.repository.get_by_id.side_effect = mock_comments
            mock_service_class.return_value = mock_service

            result = await bulk_delete_comments(comment_ids=[1, 2, 3], reason="Spam")

            assert result["total_requested"] == 3
            assert result["deleted"] == 2
            assert len(result["errors"]) == 1
            assert result["errors"][0]["comment_id"] == 3
            assert result["reason"] == "Spam"

            # Verify comments were marked as hidden
            assert mock_comments[0].is_hidden is True
            assert mock_comments[0].raw_data["deletion_reason"] == "Spam"
            assert mock_comments[1].is_hidden is True


class TestSentimentAnalysis:
    """Test sentiment analysis functionality"""

    @pytest.mark.asyncio
    async def test_analyze_comment_sentiment(self):
        """Test basic sentiment analysis"""
        mock_comments = [
            Mock(
                id=1,
                author_username="user1",
                text="I love this! It's amazing! ❤️😍",
            ),
            Mock(
                id=2,
                author_username="user2",
                text="This is terrible. I hate it. 😠",
            ),
            Mock(
                id=3,
                author_username="user3",
                text="It's okay, nothing special.",
            ),
            Mock(
                id=4,
                author_username="user4",
                text="Beautiful and wonderful! Best post ever! 💕✨",
            ),
        ]

        class MockQuerySet:
            def __init__(self, items):
                self.items = items

            def __iter__(self):
                return iter(self.items)

        with patch("instagram_manager.services.CommentService") as mock_service_class:
            mock_service = Mock()
            mock_service.repository.get_for_post.return_value = MockQuerySet(mock_comments)
            mock_service_class.return_value = mock_service

            result = await analyze_comment_sentiment(post_id=123)

            assert result["post_id"] == 123
            assert result["total_comments"] == 4
            assert result["sentiment_distribution"]["positive"] == 2
            assert result["sentiment_distribution"]["negative"] == 1
            assert result["sentiment_distribution"]["neutral"] == 1
            assert result["sentiment_percentages"]["positive"] == 50.0
            assert result["sentiment_percentages"]["negative"] == 25.0
            assert result["sentiment_percentages"]["neutral"] == 25.0


class TestParameterValidation:
    """Test parameter validation in moderation functions"""

    @pytest.mark.asyncio
    async def test_detect_spam_threshold_validation(self):
        """Test spam detection threshold validation"""
        # Test invalid thresholds directly in detect_spam_comments function
        with pytest.raises(ValueError, match="Threshold must be between 0.0 and 1.0"):
            await detect_spam_comments(post_id=123, threshold=1.5)

        with pytest.raises(ValueError, match="Threshold must be between 0.0 and 1.0"):
            await detect_spam_comments(post_id=123, threshold=-0.1)

    @pytest.mark.asyncio
    async def test_bulk_delete_validation(self):
        """Test bulk delete parameter validation"""
        # Test empty list directly in bulk_delete_comments function
        with pytest.raises(ValueError, match="At least one comment ID is required"):
            await bulk_delete_comments(comment_ids=[])

        # Test too many comments
        with pytest.raises(ValueError, match="Cannot delete more than 100 comments at once"):
            await bulk_delete_comments(comment_ids=list(range(101)))

    @pytest.mark.asyncio
    async def test_top_commenters_validation(self):
        """Test top commenters parameter validation"""
        # Test empty username directly in get_top_commenters function
        with pytest.raises(ValueError, match="Profile username is required"):
            await get_top_commenters(profile_username="")

        # Test limit clamping - the function clamps internally
        with patch("instagram_manager.models.InstagramComment.objects.filter") as mock_filter:
            # Setup mock chain
            mock_qs = Mock()
            mock_qs.exclude.return_value = mock_qs
            mock_qs.values.return_value = mock_qs
            mock_qs.annotate.return_value = mock_qs
            mock_qs.order_by.return_value = []
            mock_qs.distinct.return_value.count.return_value = 0
            mock_filter.return_value = mock_qs
            
            # Call with limit > 100
            result = await get_top_commenters(profile_username="test", limit=150)
            
            # Function should work but internally clamp limit
            assert result["profile"] == "test"