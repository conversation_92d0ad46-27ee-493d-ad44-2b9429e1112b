"""Test database decorators and helpers"""

import pytest
from unittest.mock import Mock, patch

from mcp_server.decorators.database import (
    async_db_operation,
    track_sync_calls,
    transactional
)
from mcp_server.database.helpers import (
    get_instagram_profile
)

@pytest.mark.asyncio
async def test_async_db_operation():
    """Test async database operation decorator"""
    
    @async_db_operation
    @track_sync_calls
    def mock_db_operation():
        return {"result": "success"}
    
    # Should return coroutine
    result = await mock_db_operation()
    assert result == {"result": "success"}

@pytest.mark.asyncio
async def test_database_helpers():
    """Test database helper functions"""
    
    # Mock Django model
    with patch('instagram_manager.models.InstagramProfile') as mock_model:
        mock_profile = Mock()
        mock_profile.username = "testuser"
        mock_model.objects.filter.return_value.first.return_value = mock_profile
        
        # Test get profile
        profile = await get_instagram_profile("testuser")
        assert profile is not None

def test_performance_tracking():
    """Test performance metrics tracking"""
    from mcp_server.monitoring.performance import (
        track_query,
        get_performance_metrics,
        reset_metrics
    )
    
    reset_metrics()
    
    # Track some queries
    with track_query("test_operation"):
        pass  # Simulate work
    
    metrics = get_performance_metrics()
    assert metrics["total_queries"] == 1
    assert metrics["slow_queries"] == 0

def test_transactional_decorator():
    """Test transactional decorator"""
    
    @transactional
    def db_operation():
        # This would be wrapped in transaction
        return True
    
    result = db_operation()
    assert result is True