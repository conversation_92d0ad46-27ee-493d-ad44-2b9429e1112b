"""Test Telegram chat tools functions"""

import pytest
from unittest.mock import Mock, patch

# Test the underlying database functions directly

@pytest.mark.asyncio
async def test_get_telegram_chat_by_id():
    """Test get_telegram_chat_by_id database function"""
    from mcp_server.tools.telegram_chats import get_telegram_chat_by_id
    
    # Mock the model
    mock_chat = Mock()
    mock_chat.chat_id = 123456
    mock_chat.title = "Test Chat"
    mock_chat.username = "testchat"
    
    with patch('telegram_manager.models.TelegramChat.objects.filter') as mock_filter:
        mock_filter.return_value.first.return_value = mock_chat
        
        result = await get_telegram_chat_by_id(123456)
        
    assert result.chat_id == 123456
    assert result.title == "Test Chat"
    mock_filter.assert_called_once_with(chat_id=123456)

@pytest.mark.asyncio
async def test_list_telegram_chats_db():
    """Test list_telegram_chats_db with filters"""
    from mcp_server.tools.telegram_chats import list_telegram_chats_db
    
    # Mock chats
    mock_chat1 = Mock()
    mock_chat1.chat_id = 1
    mock_chat1.title = "Chat 1"
    mock_chat1.broadcast = False
    mock_chat1.megagroup = False
    mock_chat1.left = False
    mock_chat1.participants_count = 100
    
    mock_chat2 = Mock()
    mock_chat2.chat_id = 2
    mock_chat2.title = "Channel 2"
    mock_chat2.broadcast = True
    mock_chat2.megagroup = False
    mock_chat2.left = False
    mock_chat2.participants_count = 5000
    
    mock_queryset = Mock()
    mock_queryset.all.return_value = mock_queryset
    mock_queryset.filter.return_value = mock_queryset
    mock_queryset.count.return_value = 2
    mock_queryset.order_by.return_value = mock_queryset
    # Mock slicing behavior for pagination
    def mock_getitem(key):
        if isinstance(key, slice):
            return [mock_chat1, mock_chat2]
        return [mock_chat1, mock_chat2][key]
    
    mock_queryset.__getitem__ = Mock(side_effect=mock_getitem)
    
    with patch('telegram_manager.models.TelegramChat.objects', mock_queryset):
        chats, total = await list_telegram_chats_db(
            limit=10, 
            offset=0,
            chat_type="channel",
            is_active=True,
            min_members=1000
        )
        
    assert len(chats) == 2
    assert total == 2
    # Verify filters were applied
    mock_queryset.filter.assert_any_call(broadcast=True)
    mock_queryset.filter.assert_any_call(left=False)
    mock_queryset.filter.assert_any_call(participants_count__gte=1000)

@pytest.mark.asyncio
async def test_get_chat_statistics_db():
    """Test get_chat_statistics_db function"""
    from mcp_server.tools.telegram_chats import get_chat_statistics_db
    
    # Mock aggregate results
    mock_stats = {
        'total_messages': 1000,
        'unique_users': 50,
        'avg_message_length': 45.5,
        'total_replies': 250
    }
    
    # Mock active users
    mock_active_users = [
        {
            'from_user__user_id': 1,
            'from_user__username': 'user1',
            'from_user__first_name': 'User',
            'from_user__last_name': 'One',
            'message_count': 100
        }
    ]
    
    mock_queryset = Mock()
    mock_queryset.filter.return_value = mock_queryset
    mock_queryset.aggregate.return_value = mock_stats
    mock_queryset.values.return_value = mock_queryset
    mock_queryset.annotate.return_value = mock_queryset
    mock_queryset.order_by.return_value = mock_active_users
    
    # Mock for extra() method chain
    mock_extra_qs = Mock()
    mock_extra_qs.values.return_value = mock_extra_qs
    mock_extra_qs.annotate.return_value = mock_extra_qs
    mock_extra_qs.order_by.return_value = [
        {'hour': 10, 'count': 50},
        {'hour': 14, 'count': 100}
    ]
    mock_queryset.extra.return_value = mock_extra_qs
    # Mock slicing behavior
    def mock_getitem(key):
        if isinstance(key, slice):
            return mock_active_users
        return mock_active_users[key]
    
    mock_queryset.__getitem__ = Mock(side_effect=mock_getitem)
    
    with patch('telegram_manager.models.TelegramMessage.objects', mock_queryset):
        result = await get_chat_statistics_db(123456, "week")
        
    assert result['total_messages'] == 1000
    assert result['unique_users'] == 50
    assert result['avg_message_length'] == 45.5
    assert result['total_replies'] == 250
    assert len(result['active_users']) == 1
    assert result['active_users'][0]['username'] == 'user1'

@pytest.mark.asyncio
async def test_search_telegram_chats_db():
    """Test search_telegram_chats_db function"""
    from mcp_server.tools.telegram_chats import search_telegram_chats_db
    
    # Mock search results
    mock_chat1 = Mock()
    mock_chat1.chat_id = 1
    mock_chat1.title = "Python Developers"
    mock_chat1.username = "pythondev"
    
    mock_chat2 = Mock()
    mock_chat2.chat_id = 2
    mock_chat2.title = "Python Learning"
    mock_chat2.username = None
    
    mock_queryset = Mock()
    mock_queryset.filter.return_value = mock_queryset
    mock_queryset.order_by.return_value = mock_queryset
    # Mock slicing behavior for pagination
    def mock_getitem(key):
        if isinstance(key, slice):
            return [mock_chat1, mock_chat2]
        return [mock_chat1, mock_chat2][key]
    
    mock_queryset.__getitem__ = Mock(side_effect=mock_getitem)
    
    with patch('telegram_manager.models.TelegramChat.objects', mock_queryset):
        results = await search_telegram_chats_db("python", 20)
        
    assert len(results) == 2
    assert results[0].title == "Python Developers"
    # Verify Q objects were used for search
    assert mock_queryset.filter.called

# Test the MCP tools attributes (not calling them directly)





# Test helper functions

def test_get_chat_type():
    """Test _get_chat_type helper function"""
    from mcp_server.tools.telegram_chats import _get_chat_type
    
    # Test channel
    chat = Mock(broadcast=True, megagroup=False)
    assert _get_chat_type(chat) == "channel"
    
    # Test supergroup
    chat = Mock(broadcast=False, megagroup=True)
    assert _get_chat_type(chat) == "supergroup"
    
    # Test regular group
    chat = Mock(broadcast=False, megagroup=False)
    assert _get_chat_type(chat) == "group"

def test_calculate_relevance_score():
    """Test _calculate_relevance_score helper function"""
    from mcp_server.tools.telegram_chats import _calculate_relevance_score
    
    # Test exact username match
    chat = Mock(username="python", title="Some Title", verified=False, participants_count=1000)
    score = _calculate_relevance_score(chat, "python")
    assert score >= 1.0
    
    # Test partial title match
    chat = Mock(username=None, title="Python Developers", verified=True, participants_count=5000)
    score = _calculate_relevance_score(chat, "python")
    assert score > 0.5
    
    # Test no match
    chat = Mock(username="javascript", title="JS Group", verified=False, participants_count=100)
    score = _calculate_relevance_score(chat, "python")
    assert score < 0.5

# Test tool registration
def test_tools_are_registered():
    """Test that all Telegram chat tools are properly registered"""
    from mcp_server.tools.telegram_chats import (
        telegram_list_chats,
        telegram_get_chat_details,
        telegram_get_chat_statistics,
        telegram_search_chats
    )
    
    # Check that tools have required attributes
    tools = [
        telegram_list_chats,
        telegram_get_chat_details,
        telegram_get_chat_statistics,
        telegram_search_chats
    ]
    
    for tool in tools:
        assert hasattr(tool, 'name')
        assert hasattr(tool, 'description')
        assert tool.name is not None
        assert tool.description is not None