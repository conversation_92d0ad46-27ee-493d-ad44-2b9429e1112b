"""Test parameter validation"""

import pytest
from datetime import datetime
from pydantic import ValidationError

from mcp_server.validators.instagram import (
    InstagramProfileTaskParams,
    InstagramBatchPostsTaskParams
)
from mcp_server.validators.telegram import TelegramMessagesTaskParams

def test_instagram_profile_validation():
    """Test Instagram profile parameter validation"""
    # Valid params
    params = InstagramProfileTaskParams(username="@TestUser")
    assert params.username == "testuser"  # Normalized
    
    # Invalid - empty username
    with pytest.raises(ValidationError):
        InstagramProfileTaskParams(username="")
    
    # Invalid - too long username
    with pytest.raises(ValidationError):
        InstagramProfileTaskParams(username="a" * 31)

def test_instagram_batch_validation():
    """Test Instagram batch parameter validation"""
    # Valid params
    params = InstagramBatchPostsTaskParams(
        usernames=["@user1", "USER2", "user1"],  # Duplicate
        batch_size=5
    )
    assert len(params.usernames) == 2  # Deduplicated
    assert params.usernames == ["user1", "user2"]
    
    # Invalid - empty list
    with pytest.raises(ValidationError):
        InstagramBatchPostsTaskParams(usernames=[])
    
    # Invalid - too many usernames
    with pytest.raises(ValidationError):
        InstagramBatchPostsTaskParams(
            usernames=[f"user{i}" for i in range(51)]
        )

def test_telegram_messages_validation():
    """Test Telegram messages parameter validation"""
    # Valid - all messages
    params = TelegramMessagesTaskParams(category="all")
    assert params.category == "all"
    
    # Valid - custom range
    params = TelegramMessagesTaskParams(
        category="custom",
        date_from=datetime(2024, 1, 1),
        date_to=datetime(2024, 1, 31)
    )
    assert params.date_from is not None
    
    # Invalid - custom without dates
    with pytest.raises(ValidationError):
        TelegramMessagesTaskParams(category="custom", date_from=datetime(2024, 1, 1))
    
    with pytest.raises(ValidationError):
        TelegramMessagesTaskParams(category="custom", date_to=datetime(2024, 1, 31))
    
    # Invalid - invalid category
    with pytest.raises(ValidationError):
        TelegramMessagesTaskParams(category="invalid")

def test_error_response_format():
    """Test error response formatting"""
    from mcp_server.errors import create_error_response, ErrorTypes
    
    error = create_error_response(
        error_type=ErrorTypes.VALIDATION_ERROR,
        message="Test error",
        details={"field": "username", "issue": "too short"}
    )
    
    assert error["error_type"] == "validation_error"
    assert error["message"] == "Test error"
    assert "timestamp" in error
    assert error["details"]["field"] == "username"