"""Test Instagram roast tools functions"""

import pytest
from unittest.mock import Mock, patch



@pytest.mark.asyncio
async def test_roast_instagram_profile_success():
    """Test successful roast profile task creation"""
    # Mock validator
    mock_validator = Mo<PERSON>()
    mock_validator.extract_username.return_value = "testuser"
    
    # Mock UUID generation
    mock_uuid = Mock()
    mock_uuid.hex = "test-uuid-123"
    mock_uuid.__str__ = Mock(return_value="test-uuid-123")
    
    # Mock task instance and class
    mock_task_instance = Mock()
    mock_celery_result = Mock(id="celery-task-id")
    mock_task_instance.run_async.return_value = mock_celery_result
    
    # Mock task class
    mock_task_class = Mock(return_value=mock_task_instance)
    
    # Mock TASK_REGISTRY
    mock_registry = {"instagram.roast_profile": mock_task_class}
    
    with patch('mcp_server.tools.instagram_roast.RoastProfileTaskParams', return_value=mock_validator):
        with patch('uuid.uuid4', return_value=mock_uuid):
            with patch('mcp_server.main.TASK_REGISTRY', mock_registry):
                # Import after patching
                from mcp_server.tools.instagram_roast import roast_instagram_profile
                
                # Call the underlying function directly (bypass MCP wrapper)
                result = await roast_instagram_profile.fn(
                    profile_url="https://www.instagram.com/testuser/",
                    post_count=10,
                    priority="normal"
                )
    
    # Verify result
    assert result["task_id"] == "test-uuid-123"
    assert result["status"] == "created"
    assert result["message"] == "Task created successfully"
    assert result["profile_url"] == "https://www.instagram.com/testuser/"
    assert result["username"] == "testuser"
    assert result["post_count"] == 10
    
    # Verify task class was instantiated with task_id
    mock_task_class.assert_called_once_with(task_id="test-uuid-123")
    
    # Verify task was called with correct params
    mock_task_instance.run_async.assert_called_once_with(
        username="testuser",
        post_count=10,
        profile_url="https://www.instagram.com/testuser/",
        _priority="normal"
    )


@pytest.mark.asyncio
async def test_roast_instagram_profile_validation_error():
    """Test roast profile with invalid URL"""
    # First let's test the actual validation by passing invalid URL directly
    from mcp_server.validators.roast import RoastProfileTaskParams
    from pydantic import ValidationError
    
    # This should raise ValidationError due to pattern mismatch
    with pytest.raises(ValidationError):
        RoastProfileTaskParams(
            profile_url="invalid-url",
            post_count=10
        )
    
    # Now test the tool function itself
    from mcp_server.tools.instagram_roast import roast_instagram_profile
    
    # Call the .fn to bypass MCP wrapper but keep error handler
    result = await roast_instagram_profile.fn(
        profile_url="invalid-url",
        post_count=10
    )
    
    # The @handle_tool_errors decorator returns error as dict with error_type
    assert "error_type" in result
    assert result["error_type"] == "internal_error"
    assert "Invalid parameters" in result["message"]


@pytest.mark.asyncio
async def test_roast_get_result_completed():
    """Test getting completed roast result"""
    # Mock completed task result
    mock_task_result = Mock()
    mock_task_result.task_id = "test-task-id"
    mock_task_result.status = "completed"
    mock_task_result.result = {
        "profile_url": "https://www.instagram.com/testuser/",
        "username": "testuser",
        "post_count": 10,
        "media_urls": [
            "https://storage.googleapis.com/bucket/image1.jpg",
            "https://storage.googleapis.com/bucket/image2.jpg",
            "https://storage.googleapis.com/bucket/image3.jpg"
        ],
        "statistics": {
            "total_posts_analyzed": 10,
            "photos_found": 15,
            "photos_uploaded": 15
        }
    }
    
    with patch('mcp_server.tools.instagram_roast.TaskResult.objects.get', return_value=mock_task_result):
        from mcp_server.tools.instagram_roast import roast_get_result
        result = await roast_get_result.fn("test-task-id")
    
    # Verify result structure
    assert result["task_id"] == "test-task-id"
    assert result["status"] == "completed"
    assert result["result"]["profile_url"] == "https://www.instagram.com/testuser/"
    assert result["result"]["username"] == "testuser"
    assert result["result"]["post_count"] == 10
    assert len(result["result"]["media_urls"]) == 3
    assert result["result"]["statistics"]["photos_uploaded"] == 15


@pytest.mark.asyncio
async def test_roast_get_result_running():
    """Test getting running roast result"""
    from mcp_server.tools.instagram_roast import roast_get_result
    
    # Mock running task result
    mock_task_result = Mock()
    mock_task_result.task_id = "test-task-id"
    mock_task_result.status = "running"
    mock_task_result.processed_items = 5
    mock_task_result.total_items = 10
    mock_task_result.progress_message = "Uploading photos to GCS..."
    
    with patch('mcp_server.tools.instagram_roast.TaskResult.objects.get', return_value=mock_task_result):
        result = await roast_get_result.fn("test-task-id")
    
    # Verify progress info
    assert result["task_id"] == "test-task-id"
    assert result["status"] == "running"
    assert result["progress"]["processed"] == 5
    assert result["progress"]["total"] == 10
    assert result["progress"]["percentage"] == 50
    assert result["progress"]["message"] == "Uploading photos to GCS..."


@pytest.mark.asyncio
async def test_roast_get_result_failed():
    """Test getting failed roast result"""
    from mcp_server.tools.instagram_roast import roast_get_result
    
    # Mock failed task result
    mock_task_result = Mock()
    mock_task_result.task_id = "test-task-id"
    mock_task_result.status = "failed"
    mock_task_result.error_message = "Profile not found"
    
    with patch('mcp_server.tools.instagram_roast.TaskResult.objects.get', return_value=mock_task_result):
        result = await roast_get_result.fn("test-task-id")
    
    # Verify error info
    assert result["task_id"] == "test-task-id"
    assert result["status"] == "failed"
    assert result["error"] == "Profile not found"


@pytest.mark.asyncio
async def test_roast_get_result_not_found():
    """Test getting non-existent task result"""
    # Import the function first
    from mcp_server.tools.instagram_roast import roast_get_result
    
    # Create a mock DoesNotExist exception
    mock_does_not_exist = type('DoesNotExist', (Exception,), {})
    
    # Create mock objects that raises DoesNotExist
    mock_objects = Mock()
    mock_objects.get.side_effect = mock_does_not_exist("Task matching query does not exist.")
    
    # Patch both TaskResult.objects and TaskResult.DoesNotExist
    with patch('mcp_server.tools.instagram_roast.TaskResult.objects', mock_objects):
        with patch('mcp_server.tools.instagram_roast.TaskResult.DoesNotExist', mock_does_not_exist):
            result = await roast_get_result.fn("non-existent-task-id")
    
    # The @handle_tool_errors decorator returns error as dict with error_type
    assert "error_type" in result
    assert result["error_type"] == "internal_error"  # ValueError becomes internal_error
    assert "Task not found" in result["message"]