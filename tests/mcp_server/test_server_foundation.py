"""Test server foundation setup"""

import pytest
import sys
from pathlib import Path

# Add project to path
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

def test_imports():
    """Test all required imports work"""
    import importlib.util
    
    # Test that modules can be imported
    modules_to_test = [
        "mcp_server.main",
        "mcp_server.task_registry",
        "mcp_server.django_integration",
    ]
    
    for module_name in modules_to_test:
        spec = importlib.util.find_spec(module_name)
        if spec is None:
            pytest.fail(f"Module {module_name} not found")
    
    # Test specific imports from modules
    try:
        # Actually import to test specific attributes
        from mcp_server import main
        from mcp_server import task_registry
        from mcp_server import django_integration
        
        # Check that specific attributes exist
        assert hasattr(main, "mcp")
        assert hasattr(main, "TASK_REGISTRY")
        assert hasattr(task_registry, "get_task_class")
        assert hasattr(django_integration, "setup_django")
    except ImportError as e:
        pytest.fail(f"Import failed: {e}")
    except AttributeError as e:
        pytest.fail(f"Attribute check failed: {e}")

def test_task_registry():
    """Test task registry is properly configured"""
    from mcp_server.task_registry import TASK_REGISTRY, list_task_types
    
    # Check registry not empty
    assert len(TASK_REGISTRY) > 0
    
    # Check all tasks are classes
    for task_type, task_class in TASK_REGISTRY.items():
        assert isinstance(task_class, type)
    
    # Check list function
    task_types = list_task_types()
    assert len(task_types) == len(TASK_REGISTRY)

def test_django_setup():
    """Test Django can be setup"""
    from mcp_server.django_integration import setup_django
    
    try:
        setup_django()
        # If we get here, setup worked
        assert True
    except Exception as e:
        pytest.fail(f"Django setup failed: {e}")

def test_decorators():
    """Test async/sync decorators"""
    from mcp_server.decorators import async_db_operation, track_sync_calls
    
    # Test decorator can be applied
    @async_db_operation
    def test_func():
        return "test"
    
    @track_sync_calls
    def sync_func():
        return "sync"
    
    assert callable(test_func)
    assert callable(sync_func)

def test_context_tracking():
    """Test context variable tracking"""
    from mcp_server.decorators.async_sync import (
        get_db_stats, 
        reset_db_stats,
        db_call_count
    )
    
    # Reset stats
    reset_db_stats()
    
    # Get initial stats
    stats = get_db_stats()
    assert stats["call_count"] == 0
    assert stats["total_time"] == 0.0
    
    # Manually increment counter
    db_call_count.set(5)
    
    # Check updated stats
    stats = get_db_stats()
    assert stats["call_count"] == 5