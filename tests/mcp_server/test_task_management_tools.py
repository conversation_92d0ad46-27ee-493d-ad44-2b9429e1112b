"""
Tests for task management tools
"""

import pytest
from unittest.mock import Mock, patch
from datetime import datetime, timedelta
from django.utils import timezone
import uuid

from core.models import TaskResult


@pytest.mark.django_db
class TestTaskManagementTools:
    """Test task management MCP tools"""
    
    @pytest.fixture
    def mock_task_result(self):
        """Create a mock TaskResult"""
        task = Mock(spec=TaskResult)
        task.task_id = str(uuid.uuid4())
        task.task_type = "instagram.profile"
        task.task_name = "instagram.profile_import"
        task.status = "pending"
        task.priority = 0
        task.progress_percentage = 0.0
        task.progress_message = "Task created"
        task.parameters = {"username": "testuser"}
        task.result = {}
        task.error_message = ""
        task.created_at = timezone.now()
        task.updated_at = timezone.now()
        task.started_at = None
        task.completed_at = None
        task.retry_count = 0
        task.max_retries = 3
        task.celery_task_id = None
        task.celery_status = None
        task.total_items = 0
        task.processed_items = 0
        task.failed_items = 0
        task.can_retry.return_value = True
        task.revoke_celery_task.return_value = True
        return task
    
    @pytest.mark.asyncio
    async def test_get_task_class(self):
        """Test getting task class from registry"""
        from mcp_server.tools.task_management import get_task_class
        
        # Test valid task type
        task_class = await get_task_class("instagram.profile")
        assert task_class is not None
        
        # Test invalid task type
        with pytest.raises(ValueError, match="Unknown task type"):
            await get_task_class("invalid.type")
    
    @pytest.mark.asyncio
    async def test_validate_task_parameters(self):
        """Test task parameter validation"""
        from mcp_server.validators.tasks import validate_task_parameters
        from pydantic import ValidationError
        
        # Test valid parameters
        valid_params = validate_task_parameters(
            "instagram.profile",
            {"username": "testuser"}
        )
        assert valid_params["username"] == "testuser"
        
        # Test missing required parameter
        with pytest.raises(ValidationError) as exc_info:
            validate_task_parameters("instagram.profile", {})
        assert "username" in str(exc_info.value)
    
    @pytest.mark.asyncio
    async def test_get_task_by_id(self, mock_task_result):
        """Test getting task by ID from database"""
        from mcp_server.tools.task_management import get_task_by_id
        
        with patch('core.models.TaskResult.objects.filter') as mock_filter:
            mock_qs = Mock()
            mock_qs.first.return_value = mock_task_result
            mock_filter.return_value = mock_qs
            
            result = await get_task_by_id(mock_task_result.task_id)
        
        assert result == mock_task_result
        mock_filter.assert_called_once_with(task_id=mock_task_result.task_id)
    
    @pytest.mark.asyncio
    async def test_list_tasks(self):
        """Test listing tasks from database"""
        from mcp_server.tools.task_management import list_tasks
        
        # Create mock queryset
        mock_task = Mock(spec=TaskResult)
        mock_task.task_id = "test-123"
        mock_task.status = "running"
        
        with patch('core.models.TaskResult.objects.all') as mock_all:
            mock_qs = Mock()
            mock_qs.filter.return_value = mock_qs
            mock_qs.order_by.return_value = mock_qs
            mock_qs.count.return_value = 1
            mock_qs.__getitem__ = Mock(return_value=[mock_task])
            mock_all.return_value = mock_qs
            
            tasks, total = await list_tasks(
                filters={"status": "running"},
                limit=10,
                offset=0
            )
        
        assert total == 1
        assert len(tasks) == 1
        assert tasks[0].task_id == "test-123"
    
    @pytest.mark.asyncio
    async def test_update_task_status(self):
        """Test updating task status"""
        from mcp_server.tools.task_management import update_task_status
        
        with patch('core.models.TaskResult.objects.filter') as mock_filter:
            mock_update = Mock()
            mock_filter.return_value.update = mock_update
            
            await update_task_status("test-123", "cancelled")
        
        mock_filter.assert_called_once_with(task_id="test-123")
        mock_update.assert_called_once()
        update_args = mock_update.call_args[1]
        assert update_args["status"] == "cancelled"
        assert "updated_at" in update_args
    
    @pytest.mark.asyncio
    async def test_get_queue_statistics(self):
        """Test getting queue statistics"""
        from mcp_server.tools.task_management import get_queue_statistics
        
        with patch('core.models.TaskResult.objects.values') as mock_values:
            # Mock status counts
            status_annotate = Mock()
            status_annotate.order_by.return_value = [
                {"status": "pending", "count": 5},
                {"status": "running", "count": 2}
            ]
            mock_values.return_value.annotate.return_value = status_annotate
            
            with patch('core.models.TaskResult.objects.filter') as mock_filter:
                mock_filter.return_value.count.side_effect = [10, 1]  # completed, failed
                
                with patch('core.models.TaskResult.objects.filter') as mock_filter2:
                    mock_aggregate = Mock()
                    mock_aggregate.aggregate.return_value = {"avg_duration": timedelta(seconds=120)}
                    mock_filter2.return_value.annotate.return_value = mock_aggregate
                    
                    result = await get_queue_statistics()
        
        assert result["total_pending"] == 5
        assert result["total_running"] == 2
        assert result["average_duration"] == 120.0
    
    @pytest.mark.asyncio
    async def test_submit_task(self):
        """Test submitting task for execution"""
        from mcp_server.tools.task_management import submit_task
        
        # Mock task class and instance
        mock_task_class = Mock()
        mock_task_instance = Mock()
        mock_task_class.return_value = mock_task_instance
        
        mock_celery_result = Mock()
        mock_celery_result.id = "celery-123"
        mock_task_instance.run_async.return_value = mock_celery_result
        
        result = await submit_task(
            task_class=mock_task_class,
            task_id="test-123",
            parameters={"username": "testuser"}
        )
        
        assert result == mock_celery_result
        # Check that task_class was called with task_id parameter
        mock_task_class.assert_called_once_with(task_id="test-123")
        mock_task_instance.run_async.assert_called_once_with(username="testuser")
    
    @pytest.mark.asyncio
    async def test_estimate_task_duration(self):
        """Test task duration estimation"""
        from mcp_server.tools.task_management import _estimate_task_duration
        
        # Test Instagram profile
        duration = _estimate_task_duration("instagram.profile", {})
        assert duration == "30-60 seconds"
        
        # Test Instagram posts with limit
        duration = _estimate_task_duration("instagram.posts", {"limit": 100})
        assert duration == "200 seconds"
        
        # Test batch posts
        duration = _estimate_task_duration(
            "instagram.batch_posts",
            {"usernames": ["user1", "user2", "user3"]}
        )
        assert duration == "180 seconds"
        
        # Test unknown type
        duration = _estimate_task_duration("unknown.type", {})
        assert duration == "Unknown"
    
    @pytest.mark.asyncio
    async def test_parameter_normalization(self):
        """Test username parameter normalization"""
        from mcp_server.validators.tasks import (
            InstagramProfileTaskParams,
            InstagramBatchPostsTaskParams
        )
        
        # Test single username normalization
        params = InstagramProfileTaskParams(username="@TestUser")
        assert params.username == "testuser"
        
        # Test batch username normalization
        batch_params = InstagramBatchPostsTaskParams(
            usernames=["@User1", "USER2", "user3"]
        )
        assert batch_params.usernames == ["user1", "user2", "user3"]
    
    @pytest.mark.asyncio
    async def test_task_registry_has_all_types(self):
        """Test that task registry contains all expected task types"""
        from mcp_server.main import TASK_REGISTRY
        
        expected_types = [
            "instagram.profile",
            "instagram.posts",
            "instagram.batch_posts",
            "instagram.comments",
            "telegram.chats",
            "telegram.messages",
            "telegram.users"
        ]
        
        for task_type in expected_types:
            assert task_type in TASK_REGISTRY
            assert TASK_REGISTRY[task_type] is not None
    
    @pytest.mark.asyncio
    async def test_date_range_validation(self):
        """Test date range validation in task parameters"""
        from mcp_server.validators.tasks import TelegramMessagesTaskParams
        from pydantic import ValidationError
        
        # Test valid date range
        params = TelegramMessagesTaskParams(
            chat_id=123,
            start_date=datetime(2024, 1, 1),
            end_date=datetime(2024, 1, 31)
        )
        assert params.start_date < params.end_date
        
        # Test invalid date range
        with pytest.raises(ValidationError) as exc_info:
            TelegramMessagesTaskParams(
                chat_id=123,
                start_date=datetime(2024, 1, 31),
                end_date=datetime(2024, 1, 1)
            )
        assert "end_date must be after start_date" in str(exc_info.value)
    
    @pytest.mark.asyncio  
    async def test_task_parameter_defaults(self):
        """Test default values for task parameters"""
        from mcp_server.validators.tasks import (
            InstagramPostsTaskParams,
            TelegramChatsTaskParams,
            TelegramMessagesTaskParams
        )
        
        # Test Instagram posts defaults
        posts_params = InstagramPostsTaskParams(username="testuser")
        assert posts_params.limit == 50
        assert posts_params.include_comments is True
        assert posts_params.include_media is True
        
        # Test Telegram chats defaults
        chats_params = TelegramChatsTaskParams()
        assert chats_params.limit == 50
        assert chats_params.include_channels is True
        assert chats_params.include_groups is True
        assert chats_params.include_private is False
        assert chats_params.only_active is True
        
        # Test Telegram messages defaults
        messages_params = TelegramMessagesTaskParams(chat_id=123)
        assert messages_params.limit == 100
        assert messages_params.skip_user_fetch is False
    