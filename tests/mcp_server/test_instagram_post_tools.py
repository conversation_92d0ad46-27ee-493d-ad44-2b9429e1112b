"""Test Instagram post tools functions"""

import pytest
from datetime import datetime, timezone
from unittest.mock import Mock, patch

# Test the underlying database functions directly
@pytest.mark.asyncio
async def test_get_posts_by_username():
    """Test get_posts_by_username database function"""
    from mcp_server.tools.instagram_posts import get_posts_by_username
    
    # Mock profile and posts
    mock_profile = Mock()
    mock_profile.username = "testuser"
    
    mock_post1 = Mock()
    mock_post1.id = 1
    mock_post1.content = "Test post 1"
    mock_post1.post_type = "photo"
    mock_post1.like_count = 100
    mock_post1.comment_count = 10
    mock_post1.posted_at = datetime.now(timezone.utc)
    mock_post1.media = Mock()
    mock_post1.media.count.return_value = 1
    mock_post1.hashtags = Mock()
    mock_post1.hashtags.all.return_value = []
    
    mock_post2 = Mock()
    mock_post2.id = 2
    mock_post2.content = "Test post 2"
    mock_post2.post_type = "video"
    mock_post2.like_count = 200
    mock_post2.comment_count = 20
    mock_post2.posted_at = datetime.now(timezone.utc)
    mock_post2.media = Mock()
    mock_post2.media.count.return_value = 1
    mock_post2.hashtags = Mock()
    mock_post2.hashtags.all.return_value = []
    
    # Create a mock queryset that supports method chaining and slicing
    class MockQuerySet:
        def __init__(self, items):
            self.items = items
        
        def count(self):
            return len(self.items)
        
        def select_related(self, *args):
            return self
        
        def prefetch_related(self, *args):
            return self
        
        def filter(self, **kwargs):
            return self
        
        def order_by(self, *args):
            return self
        
        def __getitem__(self, key):
            return self.items[key]
    
    mock_queryset = MockQuerySet([mock_post1, mock_post2])
    
    # Mock InstagramPost.objects
    mock_post_objects = Mock()
    mock_post_objects.filter.return_value = mock_queryset
    
    # Mock InstagramProfile.objects
    mock_profile_objects = Mock()
    mock_profile_objects.get.return_value = mock_profile
    
    with patch("instagram_manager.models.InstagramProfile.objects", mock_profile_objects):
        with patch("instagram_manager.models.InstagramPost.objects", mock_post_objects):
            posts, total = await get_posts_by_username(
                username="testuser",
                filters={"order_by": "-posted_at"},
                limit=10,
                offset=0
            )
    
    assert len(posts) == 2
    assert total == 2
    assert posts[0].id == 1
    assert posts[1].id == 2
    mock_profile_objects.get.assert_called_once_with(username="testuser")

@pytest.mark.asyncio
async def test_get_post_by_id():
    """Test get_post_by_id database function"""
    from mcp_server.tools.instagram_posts import get_post_by_id
    
    # Mock post with relations
    mock_post = Mock()
    mock_post.id = 1
    mock_post.content = "Test post"
    mock_post.profile = Mock(username="testuser")
    mock_post.media = Mock()
    mock_post.media.all.return_value = []
    mock_post.hashtags = Mock()
    mock_post.hashtags.all.return_value = []
    
    # Mock queryset
    mock_queryset = Mock()
    mock_queryset.select_related.return_value = mock_queryset
    mock_queryset.prefetch_related.return_value = mock_queryset
    mock_queryset.filter.return_value = mock_queryset
    mock_queryset.first.return_value = mock_post
    
    with patch("instagram_manager.models.InstagramPost.objects", mock_queryset):
        result = await get_post_by_id(1)
    
    assert result.id == 1
    assert result.content == "Test post"
    mock_queryset.filter.assert_called_once_with(id=1)

@pytest.mark.asyncio
async def test_get_trending_posts_db():
    """Test get_trending_posts_db database function"""
    from mcp_server.tools.instagram_posts import get_trending_posts_db
    
    # Mock post with engagement
    mock_post = Mock()
    mock_post.id = 1
    mock_post.profile = Mock()
    mock_post.profile.username = "testuser"
    mock_post.profile.follower_count = 1000
    mock_post.like_count = 100
    mock_post.comment_count = 10
    mock_post.engagement_rate = 11.0  # (100+10)/1000 * 100
    mock_post.posted_at = datetime.now(timezone.utc)
    mock_post.content = "Trending post"
    mock_post.post_type = "photo"
    mock_post.post_url = "https://instagram.com/p/123"
    mock_post.media = Mock()
    mock_post.media.count.return_value = 1
    mock_post.hashtags = Mock()
    mock_post.hashtags.all.return_value = []
    
    # Mock queryset with annotate
    mock_queryset = Mock()
    mock_queryset.annotate.return_value = mock_queryset
    mock_queryset.filter.return_value = mock_queryset
    mock_queryset.select_related.return_value = mock_queryset
    mock_queryset.prefetch_related.return_value = mock_queryset
    mock_queryset.order_by.return_value = mock_queryset
    
    # Mock slicing
    mock_queryset.__getitem__ = Mock(return_value=[mock_post])
    
    with patch("instagram_manager.models.InstagramPost.objects", mock_queryset):
        posts = await get_trending_posts_db({}, 10)
    
    assert len(posts) == 1
    assert posts[0].id == 1
    assert posts[0].engagement_rate == 11.0
    mock_queryset.annotate.assert_called_once()
    mock_queryset.order_by.assert_called_with("-engagement_rate")

@pytest.mark.asyncio
async def test_search_posts_db():
    """Test _search_posts_db database function"""
    from mcp_server.tools.instagram_posts import _search_posts_db
    
    # Mock post
    mock_post = Mock()
    mock_post.id = 1
    mock_post.content = "Test search content"
    mock_post.profile = Mock()
    mock_post.profile.username = "testuser"
    mock_post.hashtags = Mock()
    mock_post.hashtags.all.return_value = []
    
    # Mock queryset
    mock_queryset = Mock()
    mock_queryset.select_related.return_value = mock_queryset
    mock_queryset.prefetch_related.return_value = mock_queryset
    mock_queryset.filter.return_value = mock_queryset
    mock_queryset.distinct.return_value = mock_queryset
    mock_queryset.order_by.return_value = mock_queryset
    
    # Mock slicing
    mock_queryset.__getitem__ = Mock(return_value=[mock_post])
    
    with patch("instagram_manager.models.InstagramPost.objects", mock_queryset):
        posts = await _search_posts_db("test", 10, "content")
    
    assert len(posts) == 1
    assert posts[0].id == 1
    mock_queryset.filter.assert_called_once()

# Test error handling
@pytest.mark.asyncio
async def test_posts_not_found():
    """Test handling when no posts found"""
    from mcp_server.tools.instagram_posts import get_posts_by_username
    
    # Mock profile not found
    mock_profile_objects = Mock()
    mock_profile_objects.get.side_effect = Exception("DoesNotExist")
    
    with patch("instagram_manager.models.InstagramProfile.objects", mock_profile_objects):
        with patch("instagram_manager.models.InstagramProfile.DoesNotExist", Exception):
            posts, total = await get_posts_by_username(
                username="nonexistent",
                filters={},
                limit=10,
                offset=0
            )
    
    assert posts == []
    assert total == 0

# Test post type validation
def test_post_type_validation():
    """Test post type is validated"""
    from mcp_server.tools.instagram_posts import instagram_get_posts
    
    # Check that the tool is properly defined
    assert hasattr(instagram_get_posts, "name")
    assert instagram_get_posts.name == "instagram_get_posts"
    assert hasattr(instagram_get_posts, "description")

# Test engagement rate calculation
def test_engagement_rate_calculation():
    """Test _calculate_engagement_rate helper"""
    from mcp_server.tools.instagram_posts import _calculate_engagement_rate
    
    # Mock post with engagement
    mock_post = Mock()
    mock_post.profile = Mock()
    mock_post.profile.follower_count = 1000
    mock_post.like_count = 100
    mock_post.comment_count = 10
    
    rate = _calculate_engagement_rate(mock_post)
    assert rate == 11.0  # (100+10)/1000 * 100
    
    # Test zero followers
    mock_post.profile.follower_count = 0
    rate = _calculate_engagement_rate(mock_post)
    assert rate == 0.0

# Test query highlighting
def test_highlight_query():
    """Test _highlight_query helper"""
    from mcp_server.tools.instagram_posts import _highlight_query
    
    content = "This is a test content with search term in the middle"
    query = "search"
    
    result = _highlight_query(content, query, context_length=10)
    assert "search" in result
    assert "..." in result  # Should have context indicators
    
    # Test no match
    result = _highlight_query(content, "notfound", context_length=10)
    assert len(result) <= 103  # 100 chars + "..."
    
    # Test empty content
    result = _highlight_query("", query)
    assert result == ""