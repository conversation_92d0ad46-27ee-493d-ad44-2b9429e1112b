"""Test Instagram roast validators"""

import pytest
from pydantic import ValidationError

from mcp_server.validators.roast import RoastProfileTaskParams


class TestRoastProfileTaskParams:
    """Test RoastProfileTaskParams validator"""
    
    def test_valid_params(self):
        """Test valid parameters"""
        # Basic valid params
        params = RoastProfileTaskParams(
            profile_url="https://www.instagram.com/testuser/"
        )
        assert params.profile_url == "https://www.instagram.com/testuser/"
        assert params.post_count == 10  # Default
        assert params.extract_username() == "testuser"
        
        # Without trailing slash
        params = RoastProfileTaskParams(
            profile_url="https://www.instagram.com/testuser"
        )
        assert params.profile_url == "https://www.instagram.com/testuser"
        assert params.extract_username() == "testuser"
        
        # With custom post count
        params = RoastProfileTaskParams(
            profile_url="https://www.instagram.com/test_user/",
            post_count=20
        )
        assert params.post_count == 20
        assert params.extract_username() == "test_user"
        
        # With underscore and numbers
        params = RoastProfileTaskParams(
            profile_url="https://www.instagram.com/test_user123/"
        )
        assert params.extract_username() == "test_user123"
    
    def test_invalid_url_format(self):
        """Test invalid URL formats"""
        # Wrong domain
        with pytest.raises(ValidationError) as exc_info:
            RoastProfileTaskParams(
                profile_url="https://www.facebook.com/testuser/"
            )
        assert "String should match pattern" in str(exc_info.value)
        
        # No https
        with pytest.raises(ValidationError) as exc_info:
            RoastProfileTaskParams(
                profile_url="http://www.instagram.com/testuser/"
            )
        assert "String should match pattern" in str(exc_info.value)
        
        # No www
        with pytest.raises(ValidationError) as exc_info:
            RoastProfileTaskParams(
                profile_url="https://instagram.com/testuser/"
            )
        assert "String should match pattern" in str(exc_info.value)
        
        # Just username
        with pytest.raises(ValidationError) as exc_info:
            RoastProfileTaskParams(profile_url="testuser")
        assert "String should match pattern" in str(exc_info.value)
        
        # Empty URL
        with pytest.raises(ValidationError) as exc_info:
            RoastProfileTaskParams(profile_url="")
        assert "String should match pattern" in str(exc_info.value)
    
    def test_invalid_post_count(self):
        """Test invalid post count values"""
        # Zero posts
        with pytest.raises(ValidationError) as exc_info:
            RoastProfileTaskParams(
                profile_url="https://www.instagram.com/testuser/",
                post_count=0
            )
        assert "Input should be greater than or equal to 1" in str(exc_info.value)
        
        # Negative posts
        with pytest.raises(ValidationError) as exc_info:
            RoastProfileTaskParams(
                profile_url="https://www.instagram.com/testuser/",
                post_count=-5
            )
        assert "Input should be greater than or equal to 1" in str(exc_info.value)
        
        # Too many posts
        with pytest.raises(ValidationError) as exc_info:
            RoastProfileTaskParams(
                profile_url="https://www.instagram.com/testuser/",
                post_count=101
            )
        assert "Input should be less than or equal to 100" in str(exc_info.value)
    
    def test_extract_username_edge_cases(self):
        """Test username extraction edge cases"""
        # With dots in username
        params = RoastProfileTaskParams(
            profile_url="https://www.instagram.com/test.user/"
        )
        assert params.extract_username() == "test.user"
        
        # URL with query params (should still work)
        params = RoastProfileTaskParams(
            profile_url="https://www.instagram.com/testuser/"
        )
        assert params.extract_username() == "testuser"
        
        # Mixed case (Instagram usernames are case-insensitive)
        params = RoastProfileTaskParams(
            profile_url="https://www.instagram.com/TestUser/"
        )
        assert params.extract_username() == "testuser"
    
    def test_url_validation_and_extraction(self):
        """Test that URL validation preserves original URL"""
        # URL should be preserved as-is after validation
        url = "https://www.instagram.com/TestUser/"
        params = RoastProfileTaskParams(profile_url=url)
        assert params.profile_url == url  # Original preserved
        assert params.extract_username() == "testuser"  # Extracted lowercase