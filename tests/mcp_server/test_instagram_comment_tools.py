"""
Tests for Instagram comment management tools
"""

from unittest.mock import Mock, patch

import pytest

from mcp_server.tools.instagram_comments import (
    get_comment_analytics,
    get_comments_for_post,
    post_bulk_comments,
    post_single_comment,
    search_comments,
)


class TestCommentRetrieval:
    """Test comment retrieval functionality"""

    @pytest.mark.asyncio
    async def test_get_comments_for_post(self):
        """Test getting comments for a specific post"""
        # Mock data
        mock_post = Mock(id=123)
        mock_comments = [
            Mock(
                id=1,
                comment_id="comment_1",
                author_username="user1",
                text="Great post!",
                like_count=10,
                commented_at=Mock(isoformat=Mock(return_value="2024-01-15T10:00:00")),
                reply_to=None,
                reply_to_id=None,
                replies=Mock(count=Mock(return_value=2)),
            ),
            <PERSON><PERSON>(
                id=2,
                comment_id="comment_2",
                author_username="user2",
                text="Love this!",
                like_count=5,
                commented_at=Mock(isoformat=Mock(return_value="2024-01-15T11:00:00")),
                reply_to=Mock(id=1),
                reply_to_id=1,
                replies=Mock(count=Mock(return_value=0)),
            ),
        ]

        # Create mock queryset
        class MockQuerySet:
            def __init__(self, items):
                self.items = items

            def filter(self, **kwargs):
                if "reply_to__isnull" in kwargs and kwargs["reply_to__isnull"]:
                    self.items = [c for c in self.items if c.reply_to_id is None]
                return self

            def order_by(self, field):
                return self

            def count(self):
                return len(self.items)

            def __getitem__(self, key):
                return self.items[key]

        mock_queryset = MockQuerySet(mock_comments)

        with patch("instagram_manager.services.PostService") as mock_post_service_class, patch(
            "instagram_manager.services.CommentService"
        ) as mock_comment_service_class:
            # Configure mocks
            mock_post_service = Mock()
            mock_post_service.get_by_id.return_value = mock_post
            mock_post_service_class.return_value = mock_post_service

            mock_comment_service = Mock()
            mock_comment_service.repository.get_for_post.return_value = mock_queryset
            mock_comment_service_class.return_value = mock_comment_service

            # Execute
            result = await get_comments_for_post(post_id=123, limit=50, offset=0, include_replies=True)

            # Assert
            assert result["post_id"] == 123
            assert result["total"] == 2
            assert len(result["comments"]) == 2
            assert result["comments"][0]["username"] == "user1"
            assert result["comments"][0]["like_count"] == 10
            assert result["comments"][1]["is_reply"] is True

    @pytest.mark.asyncio
    async def test_search_comments(self):
        """Test searching comments"""
        mock_comments = [
            Mock(
                id=1,
                comment_id="comment_1",
                author_username="user1",
                text="Amazing product!",
                post_id=123,
                post=Mock(code="ABC123"),
                like_count=20,
                commented_at=Mock(isoformat=Mock(return_value="2024-01-15T10:00:00")),
            ),
        ]

        class MockQuerySet:
            def __init__(self, items):
                self.items = items

            def filter(self, *args, **kwargs):
                return self

            def count(self):
                return len(self.items)

            def __getitem__(self, key):
                if isinstance(key, slice):
                    return self.items[key]
                return self.items[key]

        with patch("instagram_manager.services.CommentService") as mock_service_class:
            mock_service = Mock()
            mock_service.repository.all.return_value = MockQuerySet(mock_comments)
            mock_service_class.return_value = mock_service

            result = await search_comments(query="amazing", limit=50)

            assert result["query"] == "amazing"
            assert result["total"] == 1
            assert len(result["results"]) == 1
            assert result["results"][0]["text"] == "Amazing product!"


class TestCommentPosting:
    """Test comment posting functionality"""

    @pytest.mark.asyncio
    async def test_post_single_comment(self):
        """Test posting a single comment"""
        with patch(
            "instagram_manager.management.commands.instagram_post_comment.Command"
        ) as mock_command_class:
            mock_command = Mock()
            mock_command.handle.return_value = None
            mock_command_class.return_value = mock_command

            result = await post_single_comment(
                username="testuser",
                password="testpass",
                post_url="https://instagram.com/p/test",
                comment_text="Nice post!",
            )

            assert result["status"] == "success"
            assert result["post_url"] == "https://instagram.com/p/test"
            assert result["comment_text"] == "Nice post!"

            # Verify command was called with correct options
            mock_command.handle.assert_called_once()
            call_args = mock_command.handle.call_args[1]
            assert call_args["username"] == "testuser"
            assert call_args["post_url"] == "https://instagram.com/p/test"
            assert call_args["comment_text"] == "Nice post!"

    @pytest.mark.asyncio
    async def test_post_bulk_comments(self):
        """Test bulk comment posting"""
        comments = [
            {"post_url": "https://instagram.com/p/1", "comment_text": "Great!"},
            {"post_url": "https://instagram.com/p/2", "comment_text": "Nice!"},
        ]

        mock_results = [
            {"status": "success", "post_url": "https://instagram.com/p/1"},
            {"status": "success", "post_url": "https://instagram.com/p/2"},
        ]

        with patch("instagram_manager.services.BatchCommentService") as mock_service_class:
            mock_service = Mock()
            mock_service.post_comments_batch.return_value = mock_results
            mock_service_class.return_value = mock_service

            result = await post_bulk_comments(
                username="testuser", password="testpass", comments=comments, delay_seconds=30
            )

            assert result["status"] == "completed"
            assert result["total_comments"] == 2
            assert result["successful"] == 2
            assert result["failed"] == 0


class TestCommentAnalytics:
    """Test comment analytics functionality"""

    @pytest.mark.asyncio
    async def test_get_comment_analytics(self):
        """Test getting comment analytics"""
        mock_comments = [
            Mock(
                id=1,
                author_username="user1",
                text="Amazing!",
                like_count=20,
                reply_to=None,
            ),
            Mock(
                id=2,
                author_username="user2",
                text="Great work!",
                like_count=15,
                reply_to=Mock(id=1),
            ),
            Mock(
                id=3,
                author_username="user1",
                text="Thanks!",
                like_count=5,
                reply_to=None,
            ),
        ]

        class MockQuerySet:
            def __init__(self, items):
                self.items = items
                self._chain_state = None  # Track if we're in a values().annotate() chain

            def aggregate(self, **kwargs):
                return {
                    "total_comments": 3,
                    "total_replies": 1,
                    "avg_likes": 13.33,
                    "max_likes": 20,
                    "min_likes": 5,
                    "unique_commenters": 2,
                }

            def values(self, field):
                # Mark that we're starting a values() chain
                new_qs = MockQuerySet(self.items)
                new_qs._chain_state = "values"
                return new_qs

            def annotate(self, **kwargs):
                # Continue the chain
                new_qs = MockQuerySet(self.items)
                new_qs._chain_state = self._chain_state
                return new_qs

            def order_by(self, field):
                # If we're in a values().annotate() chain, return dictionaries
                if self._chain_state == "values":
                    return [{"author_username": "user1", "comment_count": 2}, {"author_username": "user2", "comment_count": 1}]
                else:
                    # Otherwise, return the actual comment objects
                    return self

            def __getitem__(self, key):
                if isinstance(key, slice):
                    return self.items[key]
                return self.items[key]

        with patch("instagram_manager.services.CommentService") as mock_service_class:
            mock_service = Mock()
            mock_service.repository.get_for_post.return_value = MockQuerySet(mock_comments)
            mock_service_class.return_value = mock_service

            result = await get_comment_analytics(post_id=123)

            assert result["post_id"] == 123
            assert result["statistics"]["total_comments"] == 3
            assert result["statistics"]["total_replies"] == 1
            assert result["statistics"]["average_likes"] == 13.33
            assert len(result["top_commenters"]) == 2
            assert len(result["most_liked_comments"]) == 3


class TestValidation:
    """Test parameter validation"""

    @pytest.mark.asyncio
    async def test_comment_filter_params_validation(self):
        """Test CommentFilterParams validation"""
        from mcp_server.validators.instagram import CommentFilterParams
        from pydantic import ValidationError
        
        # Test invalid limit
        with pytest.raises(ValidationError, match="Input should be less than or equal to 100"):
            CommentFilterParams(post_id=123, limit=150)
        
        # Test invalid offset
        with pytest.raises(ValidationError, match="Input should be greater than or equal to 0"):
            CommentFilterParams(post_id=123, offset=-1)
        
        # Test invalid post_id
        with pytest.raises(ValidationError, match="Input should be greater than 0"):
            CommentFilterParams(post_id=0)

    @pytest.mark.asyncio
    async def test_comment_params_validation(self):
        """Test CommentParams validation"""
        from mcp_server.validators.instagram import CommentParams
        from pydantic import ValidationError
        
        # Test comment text too long
        with pytest.raises(ValidationError, match="String should have at most 2200 characters"):
            CommentParams(
                username="user",
                password="pass",
                post_url="https://instagram.com/p/test",
                comment_text="x" * 2201,
            )
        
        # Test empty username
        with pytest.raises(ValidationError, match="String should have at least 1 character"):
            CommentParams(
                username="",
                password="pass",
                post_url="https://instagram.com/p/test",
                comment_text="Nice!",
            )

    @pytest.mark.asyncio
    async def test_search_validation(self):
        """Test search query validation in tool function"""
        # Test short query directly in search_comments function
        with pytest.raises(ValueError, match="Search query must be at least 2 characters"):
            await search_comments(query="a")