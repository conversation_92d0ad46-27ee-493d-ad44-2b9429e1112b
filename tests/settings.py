"""
Test settings for running Django tests.
"""
# Database and password hashers are already configured in settings.test
# No need to override them here
# Allow migrations to run for tests
# This ensures database schema is created properly
# Disable structlog during tests to reduce noise
import os

import structlog

from SocialManager.settings.test import *  # noqa: F403

# MCP Server configuration for tests
MCP_HOST = os.getenv("MCP_HOST", "localhost")
MCP_PORT = int(os.getenv("MCP_PORT", 8001))
MCP_BASE_PATH = os.getenv("MCP_BASE_PATH", "/mcp")
MCP_URL = f"http://{MCP_HOST}:{MCP_PORT}{MCP_BASE_PATH}"

structlog.configure(
    processors=[
        structlog.stdlib.add_logger_name,
        structlog.stdlib.add_log_level,
        structlog.processors.StackInfoRenderer(),
        structlog.processors.format_exc_info,
        structlog.processors.JSONRenderer(),
    ],
    context_class=dict,
    logger_factory=structlog.stdlib.LoggerFactory(),
    wrapper_class=structlog.stdlib.BoundLogger,
    cache_logger_on_first_use=True,
)

# Test configuration for Telegram API
TELEGRAM_API_ID = 123456  # Test API ID
TELEGRAM_API_HASH = "test_hash_for_testing"  # Test API Hash  
TELEGRAM_SESSION_NAME = "test_session"  # Test session name

# Test configuration for BrightData API
BRIGHTDATA_API_KEY = "test_brightdata_key"
BRIGHTDATA_DATASET_ID = "test_dataset_id"
BRIGHTDATA_TIMEOUT = 600  # 10 minutes as expected by tests

# Celery configuration for tests
CELERY_TASK_ALWAYS_EAGER = True  # Execute tasks synchronously
CELERY_TASK_EAGER_PROPAGATES = True  # Propagate exceptions in eager mode
CELERY_BROKER_URL = "memory://"  # Use in-memory broker for tests
CELERY_RESULT_BACKEND = "cache+memory://"  # Use in-memory result backend

# Use standard Django test runner (no need for custom cleaning with in-memory DB)
TEST_RUNNER = "django.test.runner.DiscoverRunner"

# Suppress all non-critical logs during tests
LOGGING = {
    "version": 1,
    "disable_existing_loggers": True,  # Disable all existing loggers
    "formatters": {
        "simple": {
            "format": "{levelname} {message}",
            "style": "{",
        },
        "test": {
            "format": "TEST ERROR: {message}",
            "style": "{",
        },
    },
    "handlers": {
        "null": {
            "class": "logging.NullHandler",
        },
        "test_console": {
            "class": "logging.StreamHandler",
            "level": "ERROR",
            "formatter": "test",
        },
    },
    "loggers": {
        # Completely suppress all expected test errors
        "core": {
            "handlers": ["null"],
            "level": "CRITICAL",
            "propagate": False,
        },
        "django": {
            "handlers": ["null"],
            "level": "CRITICAL",
            "propagate": False,
        },
        "instagram_manager": {
            "handlers": ["null"],
            "level": "CRITICAL",
            "propagate": False,
        },
        "telegram_manager": {
            "handlers": ["null"],
            "level": "CRITICAL", 
            "propagate": False,
        },
        # Suppress all test mock errors
        "unittest": {
            "handlers": ["null"],
            "level": "CRITICAL",
            "propagate": False,
        },
    },
    "root": {
        "handlers": ["null"],
        "level": "CRITICAL",
    },
}

# Increase test timeout to 10 minutes (600 seconds)
# This is especially useful for tests that interact with external APIs
import sys  # noqa: E402

if "test" in sys.argv:
    # Set socket timeout for tests
    import socket
    socket.setdefaulttimeout(600)
    
    # Set default test timeouts
    DEFAULT_TEST_TIMEOUT = 600  # 10 minutes