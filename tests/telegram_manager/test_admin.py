"""
Tests for the Telegram admin interface
"""
from django.test import TestCase, Client
from django.urls import reverse
from django.contrib.auth.models import User
from django.utils import timezone
from telegram_manager.models import TelegramChat, TelegramUser, TelegramMessage


class TelegramAdminTest(TestCase):
    """Tests for the TelegramChat admin interface"""
    
    def setUp(self):
        """Set up the test environment"""
        # Create a superuser
        self.admin_user = User.objects.create_superuser(
            username='admin',
            email='<EMAIL>',
            password='adminpassword'
        )
        self.client = Client()
        self.client.login(username='admin', password='adminpassword')
        
        # Create a test chat
        self.chat = TelegramChat.objects.create(
            chat_id=-1001234567890,
            title="Test Chat",
            username="testchat",
            broadcast=True
        )
    
    def test_admin_chat_list(self):
        """Test the chat list view in the admin"""
        url = reverse('admin:telegram_manager_telegramchat_changelist')
        response = self.client.get(url)
        assert response.status_code == 200
        self.assertContains(response, "Test Chat")
    
    def test_admin_chat_detail(self):
        """Test the chat detail view in the admin"""
        url = reverse('admin:telegram_manager_telegramchat_change', args=[self.chat.chat_id])
        response = self.client.get(url)
        assert response.status_code == 200
        self.assertContains(response, "Test Chat")
        self.assertContains(response, "-1001234567890")  # chat_id
    
    def test_import_from_telegram_form(self):
        """Test the 'Import from Telegram' form view"""
        url = reverse('telegram_manager:telegram_manager_telegramchat_import-from-telegram')
        response = self.client.get(url)
        assert response.status_code == 200
        self.assertContains(response, 'Import Chats from Telegram')


class TelegramMessageAdminTest(TestCase):
    """Tests for the TelegramMessage admin interface"""
    
    def setUp(self):
        """Set up the test environment"""
        # Create a superuser
        self.admin_user = User.objects.create_superuser(
            username='admin',
            email='<EMAIL>',
            password='adminpassword'
        )
        self.client = Client()
        self.client.login(username='admin', password='adminpassword')
        
        # Create a test chat
        self.chat = TelegramChat.objects.create(
            chat_id=-1001234567890,
            title="Test Chat",
            username="testchat"
        )
        
        # Create a test user
        self.user = TelegramUser.objects.create(
            user_id=123456789,
            username="testuser",
            first_name="Test",
            last_name="User"
        )
        
        # Create a test message
        self.message = TelegramMessage.objects.create(
            message_id=1000,
            chat=self.chat,
            from_user=self.user,
            from_id=self.user.user_id,
            date=timezone.now(),
            text="This is a test message"
        )
    
    def test_admin_message_list(self):
        """Test the message list view in the admin"""
        url = reverse('admin:telegram_manager_telegrammessage_changelist')
        response = self.client.get(url)
        assert response.status_code == 200
        self.assertContains(response, "This is a test message")
    
    def test_admin_message_detail(self):
        """Test the message detail view in the admin"""
        url = reverse('admin:telegram_manager_telegrammessage_change', args=[self.message.pk])
        response = self.client.get(url)
        assert response.status_code == 200
        self.assertContains(response, "This is a test message")
        self.assertContains(response, "Test Chat")  # chat name
    
    def test_admin_import_messages_form(self):
        """Test the import messages form view"""
        url = reverse('telegram_manager:telegram_manager_telegrammessage_import-messages')
        response = self.client.get(url)
        assert response.status_code == 200
        self.assertContains(response, "Import Messages")
        self.assertContains(response, "category")
        self.assertContains(response, "message_limit")