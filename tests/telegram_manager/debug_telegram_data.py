#!/usr/bin/env python3
import asyncio
import json
import os
import sys

from decouple import config

from telegram_manager.telegram_api.telegram_manager import TelegramManager

# Add the project root to the Python path
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.append(project_root)

async def main():
    # Get API credentials
    api_id = config("API_ID", cast=int)
    api_hash = config("API_HASH")
    session_name = config("SESSION_NAME")
    
    # Use absolute path for session file in the sessions directory
    sessions_dir = os.path.join(project_root, "telegram_manager", "sessions")
    
    # Make sure sessions directory exists
    if not os.path.exists(sessions_dir):
        os.makedirs(sessions_dir, exist_ok=True)
    
    session_path = os.path.join(sessions_dir, session_name)
    
    print(f"Connecting to Telegram with session: {session_path}")
    
    # Create TelegramManager instance
    manager = TelegramManager(api_id, api_hash, session_path)
    
    try:
        # Connect to Telegram
        await manager.connect()
        print("Successfully connected to Telegram!")
        
        # Get chat info - use one of your actual chat IDs
        chat_id = -1001271343429  # Replace with actual chat ID from your data
        print(f"Getting entity for chat ID: {chat_id}")
        
        try:
            chat_entity = await manager.client.get_entity(chat_id)
            print(f"Chat type: {type(chat_entity).__name__}")
            print(f"Chat attributes: {dir(chat_entity)}")
            print(f"Chat title: {getattr(chat_entity, 'title', 'No title')}")
            print(f"Is broadcast: {getattr(chat_entity, 'broadcast', False)}")
        except Exception as e:
            print(f"Error getting chat entity: {e}")
        
        # Get some recent messages
        print(f"\nFetching 2 messages from {chat_id}...")
        messages = await manager.get_messages_by_date(chat_id, limit=2)
        
        # Print raw message data
        print("\nRaw message data (first message):")
        if messages:
            print(json.dumps(messages[0], indent=2, default=str))
            print("\nSender ID info:", messages[0].get("from_id"))
            
            # Check if the error is in get_messages_by_date
            print("\nRetrieving message directly...")
            try:
                # Get the first message directly to examine raw Telethon message
                msg_id = messages[0]["id"]
                raw_message = await manager.client.get_messages(chat_id, ids=msg_id)
                
                print("Raw Telethon message data:")
                print(f"- ID: {raw_message.id}")
                print(f"- From ID: {getattr(raw_message, 'from_id', None)}")
                if hasattr(raw_message, "from_id"):
                    print(f"  Type of from_id: {type(raw_message.from_id).__name__}")
                    print(f"  Attributes of from_id: {dir(raw_message.from_id)}")
                    # If from_id has user_id attribute, print it
                    if hasattr(raw_message.from_id, "user_id"):
                        print(f"  User ID: {raw_message.from_id.user_id}")
                    
                print(f"- Sender: {getattr(raw_message, 'sender', None)}")
                if raw_message.sender:
                    print(f"  Sender ID: {raw_message.sender.id}")
                    print(f"  Sender type: {type(raw_message.sender).__name__}")
                    print(f"  Sender username: {getattr(raw_message.sender, 'username', None)}")
                
                print(f"- Post author: {getattr(raw_message, 'post_author', None)}")
                print(f"- Is Channel: {getattr(chat_entity, 'broadcast', False)}")
            except Exception as e:
                print(f"Error getting raw message: {e}")
            
        else:
            print("No messages found")
    
    finally:
        # Always disconnect
        if hasattr(manager, "client") and manager.client.is_connected():
            await manager.client.disconnect()
            print("Disconnected from Telegram")

if __name__ == "__main__":
    asyncio.run(main())