"""
Tests for Telegram message saving and importing functionality
"""

from django.test import Test<PERSON>ase
from unittest.mock import patch, MagicMock
from django.utils import timezone
from datetime import timed<PERSON><PERSON>
from telegram_manager.models import TelegramChat, TelegramUser, TelegramMessage
from telegram_manager.telegram_api.managers import TelegramManager


class MessageSaveTest(TestCase):
    """Tests for basic message saving functionality"""

    def setUp(self):
        self.chat = TelegramChat.objects.create(
            chat_id=-1001234567890, title="Test Chat"
        )

        self.user = TelegramUser.objects.create(
            user_id=123456789, username="testuser", first_name="Test", last_name="User"
        )

    def test_basic_message_save(self):
        """Test saving a basic message"""
        message, created = TelegramMessage.objects.update_or_create(
            message_id=1000,
            chat=self.chat,
            defaults={
                "from_user": self.user,
                "from_id": self.user.user_id,
                "date": timezone.now(),
                "text": "This is a test message",
            },
        )

        assert created
        assert message.message_id == 1000
        assert message.from_user == self.user
        assert message.text == "This is a test message"

        # Test update
        message, created = TelegramMessage.objects.update_or_create(
            message_id=1000,
            chat=self.chat,
            defaults={"text": "This is an updated message"},
        )

        assert not created
        assert message.text == "This is an updated message"

    def test_message_with_media(self):
        """Test saving a message with media"""
        message, created = TelegramMessage.objects.update_or_create(
            message_id=2000,
            chat=self.chat,
            defaults={
                "from_user": self.user,
                "from_id": self.user.user_id,
                "date": timezone.now(),
                "text": "Message with photo",
                "media_type": "photo",
                "photo": {"file_id": "abc123", "width": 800, "height": 600},
            },
        )

        assert created
        assert message.media_type == "photo"
        assert message.photo["file_id"] == "abc123"

    def test_message_with_forward(self):
        """Test saving a message with forward info"""
        message, created = TelegramMessage.objects.update_or_create(
            message_id=3000,
            chat=self.chat,
            defaults={
                "from_user": self.user,
                "from_id": self.user.user_id,
                "date": timezone.now(),
                "text": "Forwarded message",
                "is_forward": True,
                "fwd_date": timezone.now() - timedelta(days=1),
                "fwd_from_user": self.user,
            },
        )

        assert created
        assert message.is_forward
        assert message.fwd_date is not None
        assert message.fwd_from_user == self.user


class TelegramManagerTest(TestCase):
    """Tests for TelegramManager class"""

    @patch("telegram_manager.telegram_api.telegram_client.TelegramAPIClient")
    def setUp(self, mock_telegram_client):
        # Mock the client with MagicMock
        self.mock_client = MagicMock()
        mock_telegram_client.return_value = self.mock_client
        self.manager = TelegramManager(
            api_id=123, api_hash="abc", session_path="test_session"
        )

    def test_telegram_manager_creation(self):
        """Test TelegramManager initialization"""
        assert self.manager is not None
        # In our new design, client is initialized asynchronously, so it might not exist yet
        # We'll just check the manager itself exists

    def test_structure_of_get_dialogs(self):
        """Test that the manager has necessary attributes and methods"""
        # Simply check that the manager has the expected attributes and methods
        assert hasattr(self.manager, "user_manager")
        assert hasattr(self.manager, "chat_manager")
        assert hasattr(self.manager, "message_manager")
        assert hasattr(self.manager, "connect")
        assert hasattr(self.manager, "disconnect")
        assert hasattr(self.manager, "get_dialogs")

        # We don't need to call any async methods directly in this test
        assert self.manager is not None
