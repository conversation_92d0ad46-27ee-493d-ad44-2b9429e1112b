"""
Tests for the Telegram models
"""
from django.test import TestCase
from django.utils import timezone
from telegram_manager.models import TelegramChat, TelegramUser, TelegramMessage


class TelegramChatModelTest(TestCase):
    """Tests for the TelegramChat model"""
    
    def setUp(self):
        self.chat = TelegramChat.objects.create(
            chat_id=-1001234567890,
            title="Test Chat",
            username="testchat",
            broadcast=True,  # Channel
            participants_count=1000
        )
    
    def test_chat_creation(self):
        assert self.chat.title == "Test Chat"
        assert self.chat.username == "testchat"
        assert self.chat.broadcast
        assert self.chat.participants_count == 1000
    
    def test_chat_str_method(self):
        assert str(self.chat) == "Test Chat"
        
    def test_chat_photo_deletion(self):
        """Test that chat photo is deleted when chat is deleted."""
        from django.core.files.base import ContentFile
        from unittest.mock import patch
        
        # Create a chat with a photo
        chat = TelegramChat.objects.create(
            chat_id=-1001987654321,
            title="Chat with Photo"
        )
        
        # Add a dummy photo
        chat.photo.save("test_photo.jpg", ContentFile(b"dummy photo content"))
        photo_path = chat.photo.path
        
        # Mock os.path.isfile to return True (file exists)
        # Mock os.remove for the signal handler
        with patch('telegram_manager.signals.os.path.isfile', return_value=True), \
             patch('telegram_manager.signals.os.remove') as mock_remove:
            chat.delete()
            # Verify os.remove was called with the correct path
            mock_remove.assert_called_once_with(photo_path)


class TelegramUserModelTest(TestCase):
    """Tests for the TelegramUser model"""
    
    def setUp(self):
        self.user = TelegramUser.objects.create(
            user_id=123456789,
            username="testuser",
            first_name="Test",
            last_name="User"
        )
    
    def test_user_creation(self):
        assert self.user.username == "testuser"
        assert self.user.first_name == "Test"
        assert self.user.last_name == "User"
        
        # Default values for new fields
        assert not self.user.is_bot
        assert not self.user.is_verified
        assert not self.user.is_restricted
        assert not self.user.is_deleted
        assert not self.user.is_premium
        
    def test_user_str_method(self):
        assert str(self.user) == "@testuser"
        
        # Test with no username
        user2 = TelegramUser.objects.create(
            user_id=987654321,
            first_name="No",
            last_name="Username"
        )
        assert str(user2) == "No Username"
        
        # Test with no username or name
        user3 = TelegramUser.objects.create(
            user_id=11111111
        )
        assert str(user3) == f"User #{user3.user_id}"
        
    def test_user_extended_fields(self):
        """Test the new extended fields in the TelegramUser model"""
        from django.utils import timezone
        
        # Create a user with all fields populated
        now = timezone.now()
        extended_user = TelegramUser.objects.create(
            user_id=555555555,
            username="complete_user",
            first_name="Complete",
            last_name="User",
            phone="+1234567890",
            is_bot=True,
            is_verified=True,
            is_restricted=False,
            is_deleted=False,
            is_premium=True,
            profile_photo_id=987654321,
            lang_code="en",
            last_seen_date=now
        )
        
        # Test all fields
        assert extended_user.username == "complete_user"
        assert extended_user.phone == "+1234567890"
        assert extended_user.is_bot
        assert extended_user.is_verified
        assert not extended_user.is_restricted
        assert not extended_user.is_deleted
        assert extended_user.is_premium
        assert extended_user.profile_photo_id == 987654321
        assert extended_user.lang_code == "en"
        assert extended_user.last_seen_date == now
        
        # Check that created_at field is automatically set
        assert extended_user.created_at is not None


class TelegramMessageModelTest(TestCase):
    """Tests for the TelegramMessage model"""
    
    def setUp(self):
        self.chat = TelegramChat.objects.create(
            chat_id=-1001234567890,
            title="Test Chat"
        )
        
        self.user = TelegramUser.objects.create(
            user_id=123456789,
            username="testuser",
            first_name="Test",
            last_name="User"
        )
        
        self.message = TelegramMessage.objects.create(
            message_id=1000,
            chat=self.chat,
            from_user=self.user,
            from_id=self.user.user_id,
            date=timezone.now(),
            text="This is a test message"
        )
    
    def test_message_creation(self):
        assert self.message.message_id == 1000
        assert self.message.chat == self.chat
        assert self.message.from_user == self.user
        assert self.message.text == "This is a test message"
    
    def test_message_str_method(self):
        expected = f"Message {self.message.message_id} from {self.user} in {self.chat}"
        assert str(self.message) == expected
    
    def test_message_media_fields(self):
        """Test message with media fields"""
        media_message = TelegramMessage.objects.create(
            message_id=1001,
            chat=self.chat,
            from_user=self.user,
            from_id=self.user.user_id,
            date=timezone.now(),
            text="Message with photo",
            media_type="photo",
            photo={"file_id": "abc123", "width": 800, "height": 600}
        )
        
        assert media_message.media_type == "photo"
        assert media_message.photo is not None
        assert media_message.photo["file_id"] == "abc123"
    
            
    def test_user_deletion_when_orphaned(self):
        """Test that users are deleted when all related messages are deleted."""
        # Create message for a new test user that will be completely orphaned
        orphaned_user = TelegramUser.objects.create(
            user_id=888888,
            username="orphaned_user",
            first_name="Orphaned",
            last_name="User"
        )
        
        orphaned_message = TelegramMessage.objects.create(
            message_id=3000,
            chat=self.chat,
            from_user=orphaned_user,
            from_id=orphaned_user.user_id,
            date=timezone.now(),
            text="I'll be deleted completely"
        )
        
        # Store the reference message for our main test user
        TelegramMessage.objects.create(
            message_id=3001,
            chat=self.chat,
            from_user=self.user,
            from_id=self.user.user_id,
            date=timezone.now(),
            text="Main user message"
        )
        
        # Verify users exist before deletion
        assert TelegramUser.objects.count() == 2  # orphaned_user and self.user
        
        # Delete only the orphaned user's message
        orphaned_message.delete()
        
        # Orphaned user should be deleted by signal handlers
        assert TelegramUser.objects.count() == 1  # Only self.user remains
        assert not TelegramUser.objects.filter(user_id=orphaned_user.user_id).exists()
        assert TelegramUser.objects.filter(user_id=self.user.user_id).exists()
        
    def test_user_not_deleted_when_still_referenced(self):
        """Test that users are not deleted when they're still referenced by some messages."""
        # Create a user just for this test
        test_user = TelegramUser.objects.create(
            user_id=777777,
            username="test_referenced_user",
            first_name="Referenced",
            last_name="User"
        )
        
        # Create multiple messages for this test user
        message1 = TelegramMessage.objects.create(
            message_id=4001,
            chat=self.chat,
            from_user=test_user,
            from_id=test_user.user_id,
            date=timezone.now(),
            text="First message from referenced user"
        )
        
        message2 = TelegramMessage.objects.create(
            message_id=4002,
            chat=self.chat,
            from_user=test_user,
            from_id=test_user.user_id,
            date=timezone.now(),
            text="Second message from referenced user"
        )
        
        # Delete only one message
        message1.delete()
        
        # The user should still exist as message2 still references them
        assert TelegramUser.objects.filter(user_id=test_user.user_id).exists()
        
        # Now delete the second message
        message2.delete()
        
        # Now the user should be deleted by signal handlers
        assert not TelegramUser.objects.filter(user_id=test_user.user_id).exists()