"""Tests for Telegram import tasks."""

from unittest.mock import Mock, patch

from django.test import TestCase

from telegram_manager.models import Tel<PERSON><PERSON><PERSON><PERSON>, TelegramUser
from telegram_manager.tasks.import_tasks import (
    ImportTelegramChatsTask,
    ImportTelegramMessagesTask,
    ImportTelegramUsersTask,
)


class TestImportTelegramChatsTask(TestCase):
    """Test ImportTelegramChatsTask."""
    
    def setUp(self):
        """Set up test data."""
        self.task = ImportTelegramChatsTask()
        
    def test_validate_params_default(self):
        """Test parameter validation with defaults."""
        params = self.task.validate_params({})
        
        self.assertEqual(params["chat_limit"], "all")
        self.assertIsNone(params["custom_limit"])
        self.assertTrue(params["include_private"])
        self.assertTrue(params["include_groups"])
        self.assertTrue(params["include_supergroups"])
        self.assertTrue(params["include_channels"])
        
    def test_validate_params_custom_limit(self):
        """Test validation with custom limit."""
        params = self.task.validate_params({
            "chat_limit": "custom",
            "custom_limit": 50,
            "include_private": False
        })
        
        self.assertEqual(params["chat_limit"], "custom")
        self.assertEqual(params["custom_limit"], 50)
        self.assertFalse(params["include_private"])
        
    def test_validate_params_invalid_custom_limit(self):
        """Test validation with invalid custom limit."""
        with self.assertRaises(ValueError) as cm:
            self.task.validate_params({
                "chat_limit": "custom",
                "custom_limit": 0
            })
            
        self.assertIn("Custom limit must be a positive integer", str(cm.exception))
        
    @patch("telegram_manager.tasks.import_tasks.TelegramClient")
    @patch("telegram_manager.tasks.import_tasks.TelegramChatService")
    def test_execute_task_success(self, mock_service_class, mock_client_class):
        """Test successful chat import."""
        # Mock client
        mock_client = Mock()
        mock_client.is_connected.return_value = True
        mock_dialogs = [
            {
                "id": 123,
                "title": "Test Group",
                "type": "group",
                "participants_count": 10
            },
            {
                "id": 456,
                "title": "Test Channel",
                "type": "channel",
                "participants_count": 1000
            }
        ]
        mock_client.get_dialogs = Mock(return_value=mock_dialogs)
        mock_client_class.return_value = mock_client
        
        # Mock service
        mock_service = Mock()
        mock_service.import_chat_from_api = Mock(side_effect=[
            (Mock(id=1, title="Test Group"), True),
            (Mock(id=2, title="Test Channel"), True)
        ])
        mock_service_class.return_value = mock_service
        
        # Execute
        with patch("asyncio.run") as mock_run:
            mock_run.side_effect = lambda coro: coro
            
            result = self.task.execute_task(
                chat_limit="all",
                include_groups=True,
                include_channels=True
            )
        
        # Verify
        self.assertEqual(result["total_chats"], 2)
        self.assertEqual(result["created"], 2)
        self.assertEqual(result["updated"], 0)
        self.assertEqual(len(result["errors"]), 0)
        
        self.assertEqual(mock_service.import_chat_from_api.call_count, 2)
        
    @patch("telegram_manager.tasks.import_tasks.TelegramClient")
    def test_execute_task_connection_error(self, mock_client_class):
        """Test task execution with connection error."""
        mock_client = Mock()
        mock_client.connect = Mock()
        mock_client.get_dialogs = Mock(return_value=[])  # Return empty list
        mock_client.disconnect = Mock()
        mock_client_class.return_value = mock_client
        
        # Execute task - should handle empty dialogs
        result = self.task.execute_task()
        
        # Verify result shows no chats imported
        self.assertEqual(result["total_chats"], 0)
        self.assertEqual(result["created"], 0)
        self.assertEqual(result["updated"], 0)
        

class TestImportTelegramUsersTask(TestCase):
    """Test ImportTelegramUsersTask."""
    
    def setUp(self):
        """Set up test data."""
        self.task = ImportTelegramUsersTask()
        
        # Create test chat
        self.chat = TelegramChat.objects.create(
            chat_id=123456,
            title="Test Chat",
            megagroup=False,
            broadcast=False
        )
        
        # Patch TelegramClient at module level
        patcher = patch("telegram_manager.tasks.import_tasks.TelegramClient")
        self.mock_client_class = patcher.start()
        self.addCleanup(patcher.stop)
        
        # Mock client instance
        self.mock_client = Mock()
        self.mock_client.connect = Mock()
        self.mock_client.disconnect = Mock()
        self.mock_client_class.return_value = self.mock_client
        
        # Patch TelegramManager to prevent initialization errors
        patcher2 = patch("telegram_manager.telegram_api.managers.TelegramManager")
        self.mock_manager_class = patcher2.start()
        self.addCleanup(patcher2.stop)
        self.mock_manager = Mock()
        self.mock_manager_class.return_value = self.mock_manager
        
    def test_validate_params_with_chat_id(self):
        """Test validation with from_chat parameter."""
        params = self.task.validate_params({
            "from_chat": self.chat.chat_id,
            "limit": 100
        })
        
        self.assertEqual(params["from_chat"], self.chat.chat_id)
        self.assertEqual(params["limit"], 100)
        self.assertFalse(params["extract_from_messages"])
        
    def test_validate_params_import_all(self):
        """Test validation for importing from all chats."""
        params = self.task.validate_params({
            "limit": 50
        })
        
        self.assertIsNone(params["from_chat"])
        self.assertFalse(params["extract_from_messages"])
        self.assertEqual(params["limit"], 50)
        
    def test_validate_params_extract_from_messages(self):
        """Test validation for extracting from messages."""
        params = self.task.validate_params({
            "extract_from_messages": True,
            "limit": 200
        })
        
        self.assertTrue(params["extract_from_messages"])
        self.assertIsNone(params["from_chat"])
        self.assertEqual(params["limit"], 200)
        
    @patch("telegram_manager.tasks.import_tasks.TelegramClient")
    @patch("telegram_manager.tasks.import_tasks.TelegramUserService")
    def test_execute_task_single_chat(self, mock_service_class, mock_client_class):
        """Test importing users from single chat."""
        # Mock service
        mock_service = Mock()
        mock_service.update_or_create = Mock(return_value=(Mock(), True))
        mock_service.get_or_create = Mock(return_value=(Mock(), True))
        mock_service_class.return_value = mock_service
        
        # Mock client for the import inside the method
        mock_client = Mock()
        # Return list directly, not async function
        mock_client.get_chat_participants = Mock(return_value=[
            {"id": 1, "username": "user1", "first_name": "User", "last_name": "One"},
            {"id": 2, "username": "user2", "first_name": "User", "last_name": "Two"},
        ] * 13)  # 26 users
        mock_client.connect = Mock()
        mock_client.disconnect = Mock()
        mock_client_class.return_value = mock_client
        
        # Execute
        result = self.task.execute_task(
            from_chat=self.chat.chat_id,
            limit=100
        )
        
        # Verify
        self.assertEqual(result["total_users"], 26)
        self.assertEqual(result["created"], 26)  # All are created in test
        self.assertEqual(result["updated"], 0)
        self.assertEqual(len(result["errors"]), 0)
        
    @patch("telegram_manager.tasks.import_tasks.TelegramClient")
    @patch("telegram_manager.tasks.import_tasks.TelegramUserService")
    def test_execute_task_all_chats(self, mock_service_class, mock_client_class):
        """Test importing users from all chats."""
        # Create additional chat
        TelegramChat.objects.create(
            chat_id=789012,
            title="Test Chat 2",
            megagroup=True,
            broadcast=False
        )
        
        # Mock service
        mock_service = Mock()
        mock_service.update_or_create = Mock(return_value=(Mock(), True))
        mock_service.get_or_create = Mock(return_value=(Mock(), True))
        mock_service_class.return_value = mock_service
        
        # Mock client for the import inside the method
        mock_client = Mock()
        def mock_get_participants(chat_id, *args, **kwargs):
            if chat_id == 123456:
                return [{"id": i, "username": f"user{i}"} for i in range(1, 11)]
            else:
                return [{"id": i, "username": f"user{i}"} for i in range(11, 26)]
        mock_client.get_chat_participants = Mock(side_effect=mock_get_participants)
        mock_client.connect = Mock()
        mock_client.disconnect = Mock()
        mock_client_class.return_value = mock_client
        
        # Execute
        result = self.task.execute_task(
            limit=50
        )
        
        # Verify
        self.assertEqual(result["total_users"], 25)
        self.assertEqual(result["created"], 25)
        self.assertEqual(result["updated"], 0)  # All created in test
        self.assertEqual(len(result["errors"]), 0)
        
        # Should have called get_or_create for users
        self.assertTrue(mock_service.get_or_create.called or mock_service.update_or_create.called)
        

class TestImportTelegramMessagesTask(TestCase):
    """Test ImportTelegramMessagesTask."""
    
    def setUp(self):
        """Set up test data."""
        self.task = ImportTelegramMessagesTask()
        
        # Create test data
        self.chat = TelegramChat.objects.create(
            chat_id=123456,
            title="Test Chat",
            megagroup=False,
            broadcast=False
        )
        self.user = TelegramUser.objects.create(
            user_id=111222,
            username="testuser",
            first_name="Test"
        )
        
        # Patch TelegramClient at module level
        patcher = patch("telegram_manager.tasks.import_tasks.TelegramClient")
        self.mock_client_class = patcher.start()
        self.addCleanup(patcher.stop)
        
        # Mock client instance
        self.mock_client = Mock()
        self.mock_client.connect = Mock()
        self.mock_client.disconnect = Mock()
        self.mock_client_class.return_value = self.mock_client
        
    def test_validate_params_default(self):
        """Test validation with default parameters."""
        params = self.task.validate_params({})
        
        self.assertEqual(params["category"], "all")
        self.assertEqual(params["message_limit"], 1000)
        self.assertFalse(params["skip_user_fetch"])
        
    def test_validate_params_invalid_limit(self):
        """Test validation with invalid message limit."""
        with self.assertRaises(ValueError) as cm:
            self.task.validate_params({
                "message_limit": 200000  # Too high
            })
            
        self.assertIn("Message limit must be between 1 and 100000", str(cm.exception))
        
    @patch("telegram_manager.tasks.import_tasks.TelegramUserService")
    @patch("telegram_manager.tasks.import_tasks.TelegramMessageService")
    def test_execute_task_success(self, mock_message_service_class, mock_user_service_class):
        """Test successful message import."""
        # Mock message service
        mock_message_service = Mock()
        mock_message_service.import_message_from_api = Mock(return_value=(Mock(), True))
        mock_message_service_class.return_value = mock_message_service
        
        # Mock user service
        mock_user_service = Mock()
        mock_user_service.update_or_create = Mock(return_value=(Mock(), True))
        mock_user_service_class.return_value = mock_user_service
        
        # Mock client to return messages
        self.mock_client.get_messages = Mock(return_value=[
            {"id": 1, "from_user_id": 123, "text": "Message 1"},
            {"id": 2, "from_user_id": 456, "text": "Message 2"},
            {"id": 3, "from_user_id": 123, "text": "Message 3"},
        ])
        self.mock_client.get_user = Mock(return_value={"id": 123, "username": "user123"})
        
        # Execute
        result = self.task.execute_task(
            message_limit=200
        )
        
        # Verify
        self.assertEqual(result["total_messages"], 3)
        self.assertEqual(result["created"], 3)
        self.assertEqual(result["updated"], 0)
        self.assertEqual(result["users_created"], 2)  # 2 unique users
        self.assertEqual(len(result["errors"]), 0)
        
        # Should have called get_messages for the chat
        self.mock_client.get_messages.assert_called()
        
    @patch("telegram_manager.tasks.import_tasks.TelegramUserService")
    @patch("telegram_manager.tasks.import_tasks.TelegramMessageService")
    def test_execute_task_with_errors(self, mock_message_service_class, mock_user_service_class):
        """Test message import with some errors."""
        # Mock message service
        mock_message_service = Mock()
        # First message succeeds, second fails, third succeeds
        mock_message_service.import_message_from_api = Mock(side_effect=[
            (Mock(), True),
            Exception("Import error"),
            (Mock(), True)
        ])
        mock_message_service_class.return_value = mock_message_service
        
        # Mock user service
        mock_user_service = Mock()
        mock_user_service_class.return_value = mock_user_service
        
        # Mock client to return messages
        self.mock_client.get_messages = Mock(return_value=[
            {"id": 1, "from_user_id": 123, "text": "Message 1"},
            {"id": 2, "from_user_id": 456, "text": "Message 2"},
            {"id": 3, "from_user_id": 123, "text": "Message 3"},
        ])
        
        # Execute
        result = self.task.execute_task(
            message_limit=100,
            skip_user_fetch=True  # Skip user fetching to simplify test
        )
        
        # Verify
        self.assertEqual(result["total_messages"], 2)  # Only 2 succeeded
        self.assertEqual(result["created"], 2)
        self.assertEqual(result["updated"], 0)
        self.assertEqual(result["users_created"], 0)  # Skipped user fetch
        self.assertEqual(len(result["errors"]), 1)  # 1 error