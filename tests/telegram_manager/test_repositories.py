"""
Tests for Telegram repositories
"""
from django.test import TestCase
from django.utils import timezone

from telegram_manager.models import TelegramChat, TelegramUser, TelegramMessage
from telegram_manager.repositories.chat_repository import TelegramChatRepository
from telegram_manager.repositories.user_repository import TelegramUserRepository
from telegram_manager.repositories.message_repository import TelegramMessageRepository


class TelegramChatRepositoryTest(TestCase):
    """Tests for TelegramChatRepository"""
    
    def setUp(self):
        self.repository = TelegramChatRepository()
        
        # Create test data
        self.chat1 = TelegramChat.objects.create(
            chat_id=-1001234567890,
            title="Test Chat 1",
            username="testchat1",
            broadcast=True,
            left=False,
            participants_count=1000
        )
        
        self.chat2 = TelegramChat.objects.create(
            chat_id=-1009876543210,
            title="Test Chat 2",
            username="testchat2",
            broadcast=False,
            megagroup=True,
            left=True,
            participants_count=500
        )
    
    def test_get_by_chat_id(self):
        """Test getting chat by chat_id"""
        chat = self.repository.get_by_chat_id(-1001234567890)
        assert chat.chat_id == -1001234567890
        assert chat.title == "Test Chat 1"
        
        # Test non-existent chat
        chat = self.repository.get_by_chat_id(-9999999999)
        assert chat is None
    
    def test_filter_active_chats(self):
        """Test filtering active chats"""
        active_chats = list(self.repository.filter_active_chats())
        assert len(active_chats) == 1
        assert active_chats[0].chat_id == self.chat1.chat_id
    
    def test_filter_by_type(self):
        """Test filtering chats by type"""
        # Channels
        channels = list(self.repository.filter_by_type(channels=True))
        assert len(channels) == 1
        assert channels[0].chat_id == self.chat1.chat_id
        
        # Supergroups
        supergroups = list(self.repository.filter_by_type(supergroups=True))
        assert len(supergroups) == 1
        assert supergroups[0].chat_id == self.chat2.chat_id
        
        # Both
        all_chats = list(self.repository.filter_by_type(channels=True, supergroups=True))
        assert len(all_chats) == 2
    
    def test_bulk_create_or_update(self):
        """Test bulk create or update"""
        chats_data = [
            {
                'chat_id': -1001234567890,  # Existing
                'title': 'Updated Chat 1',
                'participants_count': 1500
            },
            {
                'chat_id': -1003333333333,  # New
                'title': 'New Chat 3',
                'username': 'newchat3',
                'broadcast': True
            }
        ]
        
        created, updated = self.repository.bulk_create_or_update(chats_data)
        
        assert created == 1
        assert updated == 1
        
        # Verify updates
        chat1 = TelegramChat.objects.get(chat_id=-1001234567890)
        assert chat1.title == 'Updated Chat 1'
        assert chat1.participants_count == 1500
        
        # Verify creation
        chat3 = TelegramChat.objects.get(chat_id=-1003333333333)
        assert chat3.title == 'New Chat 3'
        assert chat3.username == 'newchat3'


class TelegramUserRepositoryTest(TestCase):
    """Tests for TelegramUserRepository"""
    
    def setUp(self):
        self.repository = TelegramUserRepository()
        
        # Create test data
        self.user1 = TelegramUser.objects.create(
            user_id=123456789,
            username="testuser1",
            first_name="Test",
            last_name="User1",
            is_bot=False
        )
        
        self.user2 = TelegramUser.objects.create(
            user_id=987654321,
            username="testbot",
            first_name="Test",
            last_name="Bot",
            is_bot=True
        )
    
    def test_get_by_user_id(self):
        """Test getting user by user_id"""
        user = self.repository.get_by_user_id(123456789)
        assert user.user_id == 123456789
        assert user.username == "testuser1"
        
        # Test non-existent user
        user = self.repository.get_by_user_id(999999999)
        assert user is None
    
    def test_filter_bots(self):
        """Test filtering bots"""
        bots = list(self.repository.filter_bots())
        assert len(bots) == 1
        assert bots[0].user_id == self.user2.user_id
    
    def test_filter_active_users(self):
        """Test filtering active users"""
        # Create deleted user
        deleted_user = TelegramUser.objects.create(
            user_id=111111111,
            username="deleted",
            is_deleted=True
        )
        
        active_users = list(self.repository.filter_active_users())
        assert len(active_users) == 2  # Only non-deleted users
        
        user_ids = [u.user_id for u in active_users]
        assert self.user1.user_id in user_ids
        assert self.user2.user_id in user_ids
        assert deleted_user.user_id not in user_ids
    
    def test_bulk_create_or_update(self):
        """Test bulk create or update"""
        users_data = [
            {
                'user_id': 123456789,  # Existing
                'username': 'updated_user1',
                'is_premium': True
            },
            {
                'user_id': 555555555,  # New
                'username': 'newuser',
                'first_name': 'New',
                'last_name': 'User'
            }
        ]
        
        created, updated = self.repository.bulk_create_or_update(users_data)
        
        assert created == 1
        assert updated == 1
        
        # Verify updates
        user1 = TelegramUser.objects.get(user_id=123456789)
        assert user1.username == 'updated_user1'
        assert user1.is_premium
        
        # Verify creation
        new_user = TelegramUser.objects.get(user_id=555555555)
        assert new_user.username == 'newuser'
        assert new_user.first_name == 'New'


class TelegramMessageRepositoryTest(TestCase):
    """Tests for TelegramMessageRepository"""
    
    def setUp(self):
        self.repository = TelegramMessageRepository()
        
        # Create test data
        self.chat = TelegramChat.objects.create(
            chat_id=-1001234567890,
            title="Test Chat"
        )
        
        self.user = TelegramUser.objects.create(
            user_id=123456789,
            username="testuser"
        )
        
        self.message1 = TelegramMessage.objects.create(
            message_id=1000,
            chat=self.chat,
            from_user=self.user,
            from_id=self.user.user_id,
            date=timezone.now() - timezone.timedelta(hours=2),
            text="Test message 1"
        )
        
        self.message2 = TelegramMessage.objects.create(
            message_id=1001,
            chat=self.chat,
            from_user=self.user,
            from_id=self.user.user_id,
            date=timezone.now() - timezone.timedelta(hours=1),
            text="Test message 2",
            media_type="photo"
        )
    
    def test_get_by_message_and_chat_id(self):
        """Test getting message by message_id and chat"""
        message = self.repository.get_by_message_and_chat_id(1000, self.chat.chat_id)
        assert message.message_id == 1000
        assert message.text == "Test message 1"
        
        # Test non-existent message
        message = self.repository.get_by_message_and_chat_id(9999, self.chat.chat_id)
        assert message is None
    
    def test_filter_by_chat(self):
        """Test filtering messages by chat"""
        # Create message in another chat
        other_chat = TelegramChat.objects.create(
            chat_id=-1009999999999,
            title="Other Chat"
        )
        
        TelegramMessage.objects.create(
            message_id=2000,
            chat=other_chat,
            from_user=self.user,
            from_id=self.user.user_id,
            date=timezone.now(),
            text="Other chat message"
        )
        
        messages = list(self.repository.filter_by_chat(self.chat.chat_id))
        assert len(messages) == 2
        
        message_ids = [m.message_id for m in messages]
        assert 1000 in message_ids
        assert 1001 in message_ids
        assert 2000 not in message_ids
    
    def test_filter_by_date_range(self):
        """Test filtering messages by date range"""
        now = timezone.now()
        
        # Messages in last 90 minutes
        messages = list(self.repository.filter_by_date_range(
            date_from=now - timezone.timedelta(minutes=90),
            date_to=now
        ))
        assert len(messages) == 1
        assert messages[0].message_id == 1001
        
        # All messages
        messages = list(self.repository.filter_by_date_range(
            date_from=now - timezone.timedelta(hours=3),
            date_to=now
        ))
        assert len(messages) == 2
    
    def test_filter_by_media_type(self):
        """Test filtering messages by media type"""
        photo_messages = list(self.repository.filter_by_media_type("photo"))
        assert len(photo_messages) == 1
        assert photo_messages[0].message_id == 1001
        
        # Test non-existent media type
        video_messages = list(self.repository.filter_by_media_type("video"))
        assert len(video_messages) == 0
    
    def test_bulk_create_or_update(self):
        """Test bulk create or update"""
        messages_data = [
            {
                'message_id': 1000,
                'chat_id': self.chat.chat_id,
                'text': 'Updated message 1',
                'is_edited': True
            },
            {
                'message_id': 1002,
                'chat_id': self.chat.chat_id,
                'from_id': self.user.user_id,
                'date': timezone.now(),
                'text': 'New message 3'
            }
        ]
        
        created, updated = self.repository.bulk_create_or_update(messages_data)
        
        assert created == 1
        assert updated == 1
        
        # Verify updates
        message1 = TelegramMessage.objects.get(message_id=1000, chat=self.chat)
        assert message1.text == 'Updated message 1'
        assert message1.is_edited
        
        # Verify creation
        message3 = TelegramMessage.objects.get(message_id=1002, chat=self.chat)
        assert message3.text == 'New message 3'
    
    def test_count_user_messages(self):
        """Test counting user messages"""
        count = self.repository.count_user_messages(self.user.user_id)
        assert count == 2
        
        # Test non-existent user
        count = self.repository.count_user_messages(999999999)
        assert count == 0
    
    def test_get_latest_message_date(self):
        """Test getting latest message date for chat"""
        latest_date = self.repository.get_latest_message_date(self.chat.chat_id)
        assert latest_date == self.message2.date
        
        # Test non-existent chat
        latest_date = self.repository.get_latest_message_date(-9999999999)
        assert latest_date is None