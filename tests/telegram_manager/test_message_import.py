"""
Integration tests for the message import functionality
"""
from datetime import timed<PERSON><PERSON>
from django.test import TestCase
from django.utils import timezone
from telegram_manager.models import Telegram<PERSON><PERSON>, TelegramUser, TelegramMessage
from telegram_manager.forms import MessageImportForm


class MessageImportFormTest(TestCase):
    """Tests for the MessageImportForm"""
    
    def test_form_validation_all_category(self):
        """Test form validation with 'all' category"""
        form_data = {
            'category': 'all',
            'message_limit': 100,
            'execution_mode': 'async'
        }
        form = MessageImportForm(data=form_data)
        assert form.is_valid()
    
    def test_form_validation_custom_dates(self):
        """Test form validation with custom date range and valid dates"""
        # Since we're not actually validating that custom requires dates in the form,
        # we'll just test that the form accepts dates correctly
        form_data = {
            'category': 'custom',
            'date_from': '2025-01-01T00:00',
            'date_to': '2025-01-31T23:59',
            'message_limit': 100,
            'execution_mode': 'async'
        }
        form = MessageImportForm(data=form_data)
        assert form.is_valid()


class MessageImportProcessTest(TestCase):
    """Integration tests for the message import process"""
    
    def setUp(self):
        """Set up test data"""
        # Create a chat
        self.chat = TelegramChat.objects.create(
            chat_id=-1001234567890,
            title="Test Chat"
        )
        
        # Create a user
        self.user = TelegramUser.objects.create(
            user_id=12345,
            username="testuser",
            first_name="Test",
            last_name="User"
        )
        
        # Create sample message data
        self.sample_messages = [
            {
                'id': 1000,
                'date': timezone.now(),
                'text': 'Test message 1',
                'from_id': 12345,
                'from_name': 'Test User',
                'media_type': '',
                'is_reply': False,
                'is_forward': False,
                'reply_to_msg_id': None,
                'entities': None,
                'views': 100
            },
            {
                'id': 1001,
                'date': timezone.now(),
                'text': 'Test message with photo',
                'from_id': 12345,
                'from_name': 'Test User',
                'media_type': 'photo',
                'photo': {'file_id': 'photo123', 'width': 800, 'height': 600},
                'is_reply': False,
                'is_forward': False,
                'entities': None,
                'views': 200
            },
            {
                'id': 1002,
                'date': timezone.now(),
                'text': 'Forwarded message',
                'from_id': 67890,
                'from_name': 'Another User',
                'media_type': '',
                'is_reply': False,
                'is_forward': True,
                'fwd_from': {
                    'date': (timezone.now() - timedelta(days=1)).isoformat(),
                    'from_id': 54321,
                    'from_name': 'Original User',
                    'channel_post': None,
                    'post_author': 'Author'
                },
                'entities': None,
                'views': 300
            }
        ]
    
    def test_message_creation_from_data(self):
        """Test creating messages from Telegram data"""
        # Process each message
        for msg_data in self.sample_messages:
            # Get or create user if needed
            user = None
            if msg_data['from_id']:
                user, created = TelegramUser.objects.get_or_create(
                    user_id=msg_data['from_id'],
                    defaults={
                        'username': msg_data.get('username', ''),
                        'first_name': msg_data.get('from_name', ''),
                    }
                )
            
            # Create message
            message, created = TelegramMessage.objects.update_or_create(
                message_id=msg_data['id'],
                chat=self.chat,
                defaults={
                    'from_user': user,
                    'from_id': msg_data['from_id'],
                    'date': msg_data['date'],
                    'text': msg_data['text'],
                    'media_type': msg_data.get('media_type', ''),
                    'is_reply': msg_data.get('is_reply', False),
                    'is_forward': msg_data.get('is_forward', False),
                    'photo': msg_data.get('photo'),
                    'views': msg_data.get('views'),
                }
            )
            assert created
        
        # Check that messages were created
        messages_in_db = TelegramMessage.objects.filter(chat=self.chat)
        assert messages_in_db.count() == 3
        
        # Check specific message attributes
        photo_message = TelegramMessage.objects.get(message_id=1001, chat=self.chat)
        assert photo_message.media_type == 'photo'
        assert photo_message.photo['file_id'] == 'photo123'
        
        forward_message = TelegramMessage.objects.get(message_id=1002, chat=self.chat)
        assert forward_message.is_forward