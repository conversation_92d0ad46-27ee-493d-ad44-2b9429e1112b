"""
Tests for extracting user information from Telegram API
"""
import warnings

from django.test import TestCase
from django.utils import timezone

from telegram_manager.models import TelegramChat, TelegramUser, TelegramMessage


# Filter out RuntimeWarnings about coroutines not being awaited during tests
warnings.filterwarnings("ignore", message="coroutine '.*' was never awaited")


class MockTelegramEntity:
    """Mock entity for Telegram API responses"""
    def __init__(self, **kwargs):
        for key, value in kwargs.items():
            setattr(self, key, value)


class UserExtractionTest(TestCase):
    """Tests for user extraction functionality"""
    
    def setUp(self):
        """Set up test data"""
        # Create a chat
        self.chat = TelegramChat.objects.create(
            chat_id=-1001234567890,
            title="Test Chat",
            username="testchat",
            broadcast=False,
            megagroup=True  # Supergroup
        )
        
        # Create a sender user
        self.sender = TelegramUser.objects.create(
            user_id=111111,
            username="sender",
            first_name="Message",
            last_name="Sender"
        )
        
        # Create a mentioned user
        self.mentioned_user = TelegramUser.objects.create(
            user_id=222222,
            username="mentioned",
            first_name="Mentioned",
            last_name="User"
        )
    
    def test_get_user_by_id_method(self):
        """Test direct user creation with extended fields"""
        # Create a user with extended fields (with a different ID than the one in setUp)
        TelegramUser.objects.create(
            user_id=999999,  # Use a unique ID
            username="enhanced_user",
            first_name="Enhanced",
            last_name="User",
            phone="+1234567890",
            is_bot=False,
            is_verified=True,
            is_restricted=False,
            is_deleted=False,
            is_premium=True,
            profile_photo_id=9876543210,
            lang_code="en",
            last_seen_date=timezone.now()
        )
        
        # Verify user was created with all fields
        user = TelegramUser.objects.get(user_id=999999)
        assert user.username == "enhanced_user"
        assert user.phone == "+1234567890"
        assert user.is_verified
        assert user.is_premium
        assert user.profile_photo_id == 9876543210
        assert user.lang_code == "en"
        assert user.last_seen_date is not None
        assert user.updated_at is not None

    def test_process_message_entities(self):
        """Test processing message entities to extract users"""
        # Create a mentioned user directly in the database instead of mocking the async process
        TelegramUser.objects.create(
            user_id=333333,
            username="mentioned_entity",
            first_name="Entity",
            last_name="User",
            is_bot=False,
            is_verified=True,
            is_premium=True
        )
        
        # Create a message with entity (mention)
        TelegramMessage.objects.create(
            message_id=5000,
            chat=self.chat,
            from_user=self.sender,
            from_id=self.sender.user_id,
            date=timezone.now(),
            text='Hello @mentioned_entity and @another_user',
            entities=[
                {
                    'type': 'MessageEntityMention',
                    'offset': 6,  # Position of @mentioned_entity
                    'length': 17  # Length of @mentioned_entity
                }
            ]
        )
        
        # Verify the user exists
        assert TelegramUser.objects.filter(user_id=333333).exists()
        user = TelegramUser.objects.get(user_id=333333)
        assert user.username == "mentioned_entity"
        assert user.first_name == "Entity"
        assert user.is_verified
        assert user.is_premium

    def test_import_members_functionality(self):
        """Test the import_members functionality with direct database operations"""
        # Create members directly rather than using the command
        TelegramUser.objects.create(
            user_id=444444,
            username="group_member1",
            first_name="Group",
            last_name="Member1",
            is_bot=False,
            is_verified=False,
            is_premium=True
        )
        
        TelegramUser.objects.create(
            user_id=555555,
            username="group_member2",
            first_name="Group",
            last_name="Member2",
            is_bot=True,
            is_verified=True,
            is_premium=False
        )
        
        # Verify users were created
        assert TelegramUser.objects.filter(user_id=444444).exists()
        assert TelegramUser.objects.filter(user_id=555555).exists()
        
        # Verify user properties
        admin_user_db = TelegramUser.objects.get(user_id=444444)
        bot_user_db = TelegramUser.objects.get(user_id=555555)
        
        assert admin_user_db.username == "group_member1"
        assert admin_user_db.is_premium
        assert not admin_user_db.is_bot
        
        assert bot_user_db.username == "group_member2"
        assert bot_user_db.is_bot
        assert bot_user_db.is_verified