"""
Tests for the new Telegram managers
"""
from datetime import datetime, timezone
import pytest
from unittest.mock import <PERSON><PERSON>, AsyncMock, patch
from django.test import TestCase

from telegram_manager.telegram_api.managers import (
    TelegramUserManager,
    TelegramChatManager,
    TelegramMessageManager,
    TelegramManager
)
from core.exceptions import APIError, NotFoundError


class TelegramUserManagerTest(TestCase):
    """Tests for TelegramUserManager"""
    
    def setUp(self):
        self.mock_client = AsyncMock()
        self.manager = TelegramUserManager(self.mock_client)
    
    async def test_get_user_by_id(self):
        """Test getting user by ID"""
        expected_user = {
            'id': 123456789,
            'username': 'testuser',
            'first_name': 'Test'
        }
        self.mock_client.get_user.return_value = expected_user
        
        result = await self.manager.get_user_by_id(123456789)
        
        assert result == expected_user
        self.mock_client.get_user.assert_called_once_with(123456789)
    
    async def test_get_chat_participants(self):
        """Test getting chat participants"""
        expected_participants = [
            {'id': 111111, 'username': 'user1'},
            {'id': 222222, 'username': 'user2'}
        ]
        self.mock_client.get_chat_participants.return_value = expected_participants
        
        result = await self.manager.get_chat_participants(-1001234567890, limit=50)
        
        assert result == expected_participants
        self.mock_client.get_chat_participants.assert_called_once_with(-1001234567890, 50)
    
    async def test_search_users_not_implemented(self):
        """Test that user search raises NotImplementedError"""
        with pytest.raises(NotImplementedError):
            await self.manager.search_users("test query")


class TelegramChatManagerTest(TestCase):
    """Tests for TelegramChatManager"""
    
    def setUp(self):
        self.mock_client = AsyncMock()
        self.manager = TelegramChatManager(self.mock_client)
    
    async def test_get_dialogs(self):
        """Test getting dialogs"""
        mock_dialogs = [
            {
                'entity': {
                    'id': -1001234567890,
                    'title': 'Channel',
                    'broadcast': True,
                    'megagroup': False
                }
            },
            {
                'entity': {
                    'id': -1009876543210,
                    'title': 'Supergroup',
                    'broadcast': False,
                    'megagroup': True
                }
            }
        ]
        self.mock_client.get_dialogs.return_value = mock_dialogs
        
        # Test getting all dialogs
        await self.manager.get_dialogs(limit=100)
        
        self.mock_client.get_dialogs.assert_called_once_with(
            limit=100,
            include_private=True,
            include_groups=True,
            include_channels=True
        )
    
    async def test_get_dialogs_filter_supergroups(self):
        """Test filtering dialogs by type"""
        mock_dialogs = [
            {
                'entity': {
                    'id': -1001234567890,
                    'title': 'Channel',
                    'broadcast': True,
                    'megagroup': False
                }
            },
            {
                'entity': {
                    'id': -1009876543210,
                    'title': 'Supergroup',
                    'broadcast': False,
                    'megagroup': True
                }
            }
        ]
        self.mock_client.get_dialogs.return_value = mock_dialogs
        
        # Test getting only channels (not supergroups)
        result = await self.manager.get_dialogs(
            include_channels=True,
            include_supergroups=False
        )
        
        assert len(result) == 1
        assert result[0]['entity']['title'] == 'Channel'
        
        # Test getting only supergroups
        self.mock_client.get_dialogs.return_value = mock_dialogs
        result = await self.manager.get_dialogs(
            include_channels=False,
            include_supergroups=True
        )
        
        assert len(result) == 1
        assert result[0]['entity']['title'] == 'Supergroup'
    
    async def test_get_chat_by_id(self):
        """Test getting chat by ID"""
        mock_entity = Mock()
        mock_entity.id = -1001234567890
        mock_entity.title = 'Test Chat'
        mock_entity.username = 'testchat'
        mock_entity.broadcast = True
        mock_entity.megagroup = False
        mock_entity.verified = True
        mock_entity.restricted = False
        mock_entity.participants_count = 1000
        mock_entity.date = datetime.now(timezone.utc)
        
        self.mock_client.client = AsyncMock()
        self.mock_client.client.get_entity.return_value = mock_entity
        
        result = await self.manager.get_chat_by_id(-1001234567890)
        
        assert result['id'] == -1001234567890
        assert result['title'] == 'Test Chat'
        assert result['broadcast']
        assert not result['megagroup']
    
    async def test_get_chat_by_id_not_found(self):
        """Test getting non-existent chat"""
        self.mock_client.client = AsyncMock()
        self.mock_client.client.get_entity.side_effect = Exception("Chat not found")
        
        with pytest.raises(NotFoundError):
            await self.manager.get_chat_by_id(-9999999999)


class TelegramMessageManagerTest(TestCase):
    """Tests for TelegramMessageManager"""
    
    def setUp(self):
        self.mock_client = AsyncMock()
        self.manager = TelegramMessageManager(self.mock_client)
    
    async def test_get_messages(self):
        """Test getting messages"""
        expected_messages = [
            {'id': 1000, 'message': 'Test 1'},
            {'id': 1001, 'message': 'Test 2'}
        ]
        self.mock_client.get_posts.return_value = expected_messages
        
        result = await self.manager.get_messages(-1001234567890, limit=100)
        
        assert result == expected_messages
        self.mock_client.get_posts.assert_called_once_with(-1001234567890, 100)
    
    async def test_get_messages_by_date(self):
        """Test getting messages by date range"""
        date_from = datetime(2024, 1, 1, tzinfo=timezone.utc)
        date_to = datetime(2024, 1, 31, tzinfo=timezone.utc)
        
        expected_messages = [
            {'id': 1000, 'message': 'Test 1', 'date': date_from}
        ]
        self.mock_client.get_posts.return_value = expected_messages
        
        result = await self.manager.get_messages_by_date(
            -1001234567890,
            limit=50,
            date_from=date_from,
            date_to=date_to
        )
        
        assert result == expected_messages
        self.mock_client.get_posts.assert_called_once_with(
            -1001234567890,
            50,
            date_from,
            date_to
        )
    
    async def test_search_messages(self):
        """Test searching messages"""
        mock_message1 = Mock()
        mock_message1.id = 1000
        mock_message1.message = "Found: test query"
        
        mock_message2 = Mock()
        mock_message2.id = 1001
        mock_message2.message = "Another test query match"
        
        # Mock the client's iter_messages
        async def mock_iter_messages(chat_id, search=None, limit=None):
            for msg in [mock_message1, mock_message2]:
                yield msg
        
        self.mock_client.client = Mock()
        self.mock_client.client.iter_messages = mock_iter_messages
        self.mock_client._message_to_dict = lambda msg: {
            'id': msg.id,
            'message': msg.message
        }
        
        result = await self.manager.search_messages(
            -1001234567890,
            "test query",
            limit=100
        )
        
        assert len(result) == 2
        assert result[0]['id'] == 1000
        assert "test query" in result[0]['message']
    
    async def test_search_messages_error(self):
        """Test search messages error handling"""
        self.mock_client.client = Mock()
        self.mock_client.client.iter_messages = AsyncMock(
            side_effect=Exception("Search failed")
        )
        
        with pytest.raises(APIError):
            await self.manager.search_messages(-1001234567890, "test")
    
    async def test_download_message_media_not_implemented(self):
        """Test that media download raises NotImplementedError"""
        with pytest.raises(NotImplementedError):
            await self.manager.download_message_media({'id': 1000})


class TelegramManagerTest(TestCase):
    """Tests for the main TelegramManager"""
    
    @patch('telegram_manager.telegram_api.managers.TelegramAPIClient')
    def test_initialization(self, mock_client_class):
        """Test manager initialization"""
        mock_client_instance = Mock()
        mock_client_class.return_value = mock_client_instance
        
        manager = TelegramManager(12345, "test_hash", "/tmp/session")
        
        # Verify client was created
        mock_client_class.assert_called_once_with(12345, "test_hash", "/tmp/session")
        
        # Verify sub-managers were created
        assert isinstance(manager.user_manager, TelegramUserManager)
        assert isinstance(manager.chat_manager, TelegramChatManager)
        assert isinstance(manager.message_manager, TelegramMessageManager)
    
    @patch('telegram_manager.telegram_api.managers.TelegramAPIClient')
    async def test_connect(self, mock_client_class):
        """Test connecting to Telegram"""
        mock_client_instance = AsyncMock()
        mock_client_class.return_value = mock_client_instance
        
        manager = TelegramManager(12345, "test_hash", "/tmp/session")
        await manager.connect()
        
        mock_client_instance.authenticate.assert_called_once_with({})
    
    @patch('telegram_manager.telegram_api.managers.TelegramAPIClient')
    async def test_disconnect(self, mock_client_class):
        """Test disconnecting from Telegram"""
        mock_client_instance = AsyncMock()
        mock_client_class.return_value = mock_client_instance
        
        manager = TelegramManager(12345, "test_hash", "/tmp/session")
        await manager.disconnect()
        
        mock_client_instance.close.assert_called_once()
    
    @patch('telegram_manager.telegram_api.managers.TelegramAPIClient')
    async def test_proxy_methods(self, mock_client_class):
        """Test proxy methods for backward compatibility"""
        mock_client_instance = Mock()
        mock_client_class.return_value = mock_client_instance
        
        manager = TelegramManager(12345, "test_hash", "/tmp/session")
        
        # Mock sub-managers
        manager.user_manager = AsyncMock()
        manager.chat_manager = AsyncMock()
        manager.message_manager = AsyncMock()
        
        # Test get_user_by_id
        expected_user = {'id': 123456789, 'username': 'test'}
        manager.user_manager.get_user_by_id.return_value = expected_user
        
        result = await manager.get_user_by_id(123456789)
        assert result == expected_user
        manager.user_manager.get_user_by_id.assert_called_once_with(123456789)
        
        # Test get_dialogs
        expected_dialogs = [{'entity': {'id': -1001234567890}}]
        manager.chat_manager.get_dialogs.return_value = expected_dialogs
        
        result = await manager.get_dialogs(limit=50)
        assert result == expected_dialogs
        manager.chat_manager.get_dialogs.assert_called_once_with(limit=50)
        
        # Test get_messages
        expected_messages = [{'id': 1000}]
        manager.message_manager.get_messages.return_value = expected_messages
        
        result = await manager.get_messages(-1001234567890, limit=100)
        assert result == expected_messages
        manager.message_manager.get_messages.assert_called_once_with(-1001234567890, 100)
        
        # Test get_messages_by_date
        date_from = datetime.now(timezone.utc)
        manager.message_manager.get_messages_by_date.return_value = expected_messages
        
        result = await manager.get_messages_by_date(
            -1001234567890,
            date_from=date_from
        )
        assert result == expected_messages
        manager.message_manager.get_messages_by_date.assert_called_once()
        
        # Test get_chat_participants
        expected_participants = [{'id': 111111}]
        manager.user_manager.get_chat_participants.return_value = expected_participants
        
        result = await manager.get_chat_participants(-1001234567890, limit=50)
        assert result == expected_participants
        manager.user_manager.get_chat_participants.assert_called_once_with(-1001234567890, 50)