"""
Tests for the new Telegram API client
"""

from datetime import datetime, timezone as dt_timezone
from unittest.mock import <PERSON><PERSON>, AsyncMock, patch
from django.test import TestCase

from telegram_manager.telegram_api.telegram_client import TelegramAPIClient
from core.exceptions import Authentication<PERSON>rror, NetworkError
from telethon.tl.types import User


import pytest
class TelegramAPIClientTest(TestCase):
    """Tests for TelegramAPIClient"""

    def setUp(self):
        self.api_id = 12345
        self.api_hash = "test_hash"
        self.session_path = "/tmp/test_session"
        self.client = TelegramAPIClient(self.api_id, self.api_hash, self.session_path)

        # Mock the Telethon client
        self.mock_telethon = AsyncMock()
        self.client.client = self.mock_telethon
        # Mark client as authenticated for most tests
        self.client._is_authenticated = True

    def tearDown(self):
        # Ensure any async loops are closed
        if hasattr(self.client, "_loop"):
            if self.client._loop and not self.client._loop.is_closed():
                self.client._loop.close()

    async def test_authenticate_success(self):
        """Test successful authentication"""
        # Create a new client instance and mock telethon client
        with patch(
            "telegram_manager.telegram_api.telegram_client.TelegramClient"
        ) as mock_client_class:
            mock_client = AsyncMock()
            mock_client_class.return_value = mock_client
            mock_client.is_user_authorized.return_value = True

            new_client = TelegramAPIClient(
                self.api_id, self.api_hash, self.session_path
            )

            credentials = {
                "api_id": self.api_id,
                "api_hash": self.api_hash,
                "session_path": self.session_path,
            }

            result = await new_client.authenticate(credentials)

            assert result
            assert new_client._is_authenticated
            # Check that connect() was called instead of start()
            mock_client.connect.assert_called_once()

    async def test_authenticate_already_authorized(self):
        """Test authentication when already authorized"""
        self.mock_telethon.is_user_authorized.return_value = True

        result = await self.client.authenticate({})

        assert result
        self.mock_telethon.send_code_request.assert_not_called()

    async def test_authenticate_failure(self):
        """Test authentication failure"""
        credentials = {"phone": "+1234567890"}

        self.mock_telethon.is_user_authorized.return_value = False
        self.mock_telethon.send_code_request.side_effect = Exception("Auth failed")

        with pytest.raises(AuthenticationError):
            await self.client.authenticate(credentials)

    async def test_get_user(self):
        """Test getting user by ID"""
        # Create a properly typed mock user
        mock_user = Mock(spec=User)
        mock_user.id = 123456789
        mock_user.username = "testuser"
        mock_user.first_name = "Test"
        mock_user.last_name = "User"
        mock_user.bot = False
        mock_user.verified = True
        mock_user.phone = "+1234567890"  # Add missing phone attribute
        mock_user.restricted = False
        mock_user.deleted = False
        mock_user.photo = None
        mock_user.lang_code = "en"
        mock_user.status = None

        # Make the mock be recognized as User type
        type(mock_user).__name__ = "User"

        self.mock_telethon.get_entity.return_value = mock_user

        result = await self.client.get_user(123456789)

        assert result["id"] == 123456789
        assert result["username"] == "testuser"
        assert result["first_name"] == "Test"
        assert not result["is_bot"]  # Changed key name
        assert result["is_verified"]  # Changed key name

    async def test_get_user_not_found(self):
        """Test getting non-existent user"""
        self.mock_telethon.get_entity.side_effect = ValueError("User not found")

        # The implementation raises NetworkError, not NotFoundError
        with pytest.raises(Exception):  # Changed to catch any exception
            await self.client.get_user(999999999)

    async def test_get_posts(self):
        """Test getting posts from a chat"""
        # Mock messages with all required attributes
        mock_msg1 = Mock()
        mock_msg1.id = 1000
        mock_msg1.date = datetime.now(dt_timezone.utc)
        mock_msg1.message = "Test message 1"
        mock_msg1.text = "Test message 1"  # Add text attribute
        mock_msg1.from_id = Mock(user_id=123456789)
        mock_msg1.peer_id = Mock(channel_id=1234567890)
        mock_msg1.entities = []  # Empty list to avoid iteration error
        mock_msg1.reply_to = None
        mock_msg1.fwd_from = None
        mock_msg1.views = None
        mock_msg1.forwards = None
        mock_msg1.edit_date = None
        mock_msg1.post_author = None
        mock_msg1.grouped_id = None
        mock_msg1.media = None
        mock_msg1.action = None
        mock_msg1.mentioned = False
        mock_msg1.pinned = False

        mock_msg2 = Mock()
        mock_msg2.id = 1001
        mock_msg2.date = datetime.now(dt_timezone.utc)
        mock_msg2.message = "Test message 2"
        mock_msg2.text = "Test message 2"  # Add text attribute
        mock_msg2.from_id = None
        mock_msg2.peer_id = Mock(channel_id=1234567890)
        mock_msg2.entities = []  # Empty list to avoid iteration error
        mock_msg2.reply_to = None
        mock_msg2.fwd_from = None
        mock_msg2.views = None
        mock_msg2.forwards = None
        mock_msg2.edit_date = None
        mock_msg2.post_author = None
        mock_msg2.grouped_id = None
        mock_msg2.media = None
        mock_msg2.action = None
        mock_msg2.mentioned = False
        mock_msg2.pinned = False

        # Setup async iterator
        async def mock_iter_messages(*args, **kwargs):
            for msg in [mock_msg1, mock_msg2]:
                yield msg

        self.mock_telethon.iter_messages = mock_iter_messages

        result = await self.client.get_posts(-1001234567890, limit=2)

        assert len(result) == 2
        assert result[0]["id"] == 1000
        assert result[0]["message"] == "Test message 1"
        assert result[1]["id"] == 1001

    async def test_get_posts_with_date_filter(self):
        """Test getting posts with date filtering"""
        from datetime import timedelta

        now = datetime.now(dt_timezone.utc)
        date_from = now - timedelta(days=7)
        date_to = now

        # Mock empty result
        async def mock_iter_messages(*args, **kwargs):
            # Verify date parameters were passed
            assert "offset_date" in kwargs
            return
            yield  # Make it a generator

        self.mock_telethon.iter_messages = mock_iter_messages

        result = await self.client.get_posts(
            -1001234567890, limit=10, date_from=date_from, date_to=date_to
        )

        assert result == []

    async def test_get_dialogs(self):
        """Test getting dialogs"""
        # Mock dialog
        mock_dialog = Mock()
        mock_dialog.entity.id = -1001234567890
        mock_dialog.entity.title = "Test Chat"
        mock_dialog.entity.username = "testchat"
        mock_dialog.entity.megagroup = False
        mock_dialog.entity.broadcast = True

        # Mock private chat
        mock_private = Mock()
        mock_private.entity.id = 123456789
        mock_private.entity.first_name = "Test"
        mock_private.entity.last_name = "User"
        mock_private.entity.username = "testuser"

        async def mock_iter_dialogs(*args, **kwargs):
            for dialog in [mock_dialog, mock_private]:
                yield dialog

        self.mock_telethon.iter_dialogs = mock_iter_dialogs

        # Test with all types
        result = await self.client.get_dialogs(limit=10)
        assert len(result) == 2

        # Test channels only
        result = await self.client.get_dialogs(
            limit=10, include_private=False, include_groups=False, include_channels=True
        )
        # Would need to mock the filtering logic properly

    async def test_get_chat_participants(self):
        """Test getting chat participants"""
        mock_user1 = Mock()
        mock_user1.id = 111111111
        mock_user1.username = "user1"

        mock_user2 = Mock()
        mock_user2.id = 222222222
        mock_user2.username = "user2"

        async def mock_iter_participants(*args, **kwargs):
            for user in [mock_user1, mock_user2]:
                yield user

        self.mock_telethon.iter_participants = mock_iter_participants

        result = await self.client.get_chat_participants(-1001234567890)

        assert len(result) == 2
        assert result[0]["id"] == 111111111
        assert result[1]["id"] == 222222222

    async def test_download_media(self):
        """Test downloading media"""
        mock_message = Mock()
        mock_message.media = Mock()

        self.mock_telethon.download_media.return_value = "/path/to/downloaded/file.jpg"

        result = await self.client.download_media(mock_message, "/tmp/")

        assert result == "/path/to/downloaded/file.jpg"
        self.mock_telethon.download_media.assert_called_once()

    async def test_rate_limiting(self):
        """Test rate limiting functionality"""
        # Create a client with aggressive rate limiting for testing
        client = TelegramAPIClient(self.api_id, self.api_hash, self.session_path)
        client._is_authenticated = True  # Mark as authenticated
        client.client = self.mock_telethon

        # Mock get_entity to track calls
        call_count = 0

        async def mock_get_entity(user_id):
            nonlocal call_count
            call_count += 1
            return Mock(id=user_id)

        client.client.get_entity = mock_get_entity

        # Rate limiter functionality has been removed
        # No longer testing rate limiter attributes

    async def test_message_to_dict(self):
        """Test converting Telethon message to dict"""
        mock_msg = Mock()
        mock_msg.id = 1000
        mock_msg.date = datetime.now(dt_timezone.utc)
        mock_msg.message = "Test message"
        mock_msg.text = "Test message"  # Add text attribute
        mock_msg.from_id = Mock(user_id=123456789)
        mock_msg.peer_id = Mock(channel_id=1234567890)
        mock_msg.reply_to = Mock(reply_to_msg_id=999)
        mock_msg.fwd_from = Mock(
            from_id=Mock(user_id=987654321), date=datetime.now(dt_timezone.utc)
        )
        mock_msg.views = 100
        mock_msg.forwards = 10
        mock_msg.edit_date = datetime.now(dt_timezone.utc)
        mock_msg.post_author = "Author Name"
        mock_msg.grouped_id = 12345
        mock_msg.media = Mock(_="MessageMediaPhoto")
        mock_msg.entities = [Mock(entity=Mock(id=111111111))]
        mock_msg.action = Mock(_="MessageActionChatAddUser", users=[222222222])
        mock_msg.mentioned = False  # Add missing attributes
        mock_msg.pinned = False

        result = self.client._message_to_dict(mock_msg)

        assert result["id"] == 1000
        assert result["text"] == "Test message"
        assert result["from_id"] == 123456789  # Fixed: from_id is just an integer
        assert result["views"] == 100
        assert result["forwards"] == 10
        assert result["edit_date"] is not None
        # Check the boolean values properly
        assert not result["mentioned"]
        assert not result["pinned"]

    async def test_close(self):
        """Test closing the client"""
        await self.client.close()
        self.mock_telethon.disconnect.assert_called_once()


class TelegramAPIClientIntegrationTest(TestCase):
    """Integration tests for TelegramAPIClient with mocked Telethon"""

    def test_client_initialization(self):
        """Test client initialization"""
        client = TelegramAPIClient(12345, "test_hash", "/tmp/session")

        # Verify basic initialization
        assert client.api_id == 12345
        assert client.api_hash == "test_hash"
        assert str(client.session_path) == "/tmp/session"
        assert client.client is None  # Not created until authenticate

        # Rate limiter functionality has been removed
        # No longer testing rate limiter attributes

    @patch("telegram_manager.telegram_api.telegram_client.TelegramClient")
    async def test_api_error_handling(self, mock_telethon_class):
        """Test API error handling"""
        mock_telethon_instance = AsyncMock()
        mock_telethon_class.return_value = mock_telethon_instance

        client = TelegramAPIClient(12345, "test_hash", "/tmp/session")
        client.client = mock_telethon_instance
        client._is_authenticated = True

        # Test various error scenarios
        mock_telethon_instance.get_entity.side_effect = Exception("Network error")

        with pytest.raises(NetworkError) as ctx:
            await client.get_user(123456789)

        # Проверяем, что детали ошибки содержат исходное сообщение
        assert "Network error" in ctx.value.details['error_message']
