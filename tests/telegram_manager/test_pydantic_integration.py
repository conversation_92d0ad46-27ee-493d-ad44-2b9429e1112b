"""Integration tests for Telegram Pydantic implementation."""

import pytest
from unittest.mock import Mock, patch, MagicMock
from datetime import datetime
from django.test import TestCase

from telegram_manager.services.chat_service import TelegramChatService
from telegram_manager.services.message_service import TelegramMessageService
from telegram_manager.services.user_service import TelegramUserService
from telegram_manager.models import TelegramChat, TelegramUser, TelegramMessage
from telegram_manager.schemas.telegram import (
    TelegramUserData,
    TelegramChatData,
    TelegramMessageData,
)


class TestTelegramPydanticIntegration(TestCase):
    """Test Pydantic integration with Telegram services."""
    
    def setUp(self):
        """Set up test dependencies."""
        # Don't use spec to avoid async method issues
        self.mock_client = Mock()
        
    @patch('telegram_manager.services.chat_service.ChatHandler', new_callable=MagicMock)
    def test_chat_service_with_pydantic(self, mock_handler):
        """Test ChatService works with Pydantic models."""
        # Prepare mock response
        mock_chat_data = TelegramChatData(
            id=123456789,
            title="Test Chat",
            username="testchat",
            type="supergroup",
            verified=True,
            participants_count=1000,
            description="Test chat description"
        )
        
        # Configure mocks
        self.mock_client.get_chat.return_value = mock_chat_data
        
        # Create mock chat
        mock_chat = TelegramChat(
            chat_id=123456789,
            title="Test Chat",
            username="testchat",
            megagroup=True,
            verified=True,
            participants_count=1000
        )
        mock_handler.process_chat_data.return_value = mock_chat
        
        # Test service
        service = TelegramChatService()
        chat = service.import_chat(self.mock_client, 123456789)
        
        # Assertions
        assert isinstance(chat, TelegramChat)
        assert chat.chat_id == 123456789
        assert chat.title == "Test Chat"
        assert chat.verified is True
        
        # Verify client was called correctly
        self.mock_client.get_chat.assert_called_once_with(123456789)
        mock_handler.process_chat_data.assert_called_once_with(mock_chat_data)
    
    def test_user_service_with_pydantic(self):
        """Test UserService works with Pydantic models."""
        # Prepare mock response
        mock_user_data = TelegramUserData(
            id="987654321",
            username="testuser",
            first_name="Test",
            last_name="User",
            verified=True,
            premium=True,
            bot=False
        )
        
        # Configure mocks
        self.mock_client.get_user.return_value = mock_user_data
        
        # Create mock user
        mock_user = TelegramUser(
            user_id=987654321,
            username="testuser",
            first_name="Test",
            last_name="User",
            is_verified=True,
            is_premium=True
        )
        
        # Manually patch UserHandler with a regular mock
        with patch('telegram_manager.services.user_service.UserHandler') as mock_handler:
            # Ensure it's not an AsyncMock
            mock_handler.process_user_data = Mock(return_value=mock_user)
            
            # Test service
            service = TelegramUserService()
            user = service.import_user(self.mock_client, "testuser")
            
            # Assertions
            assert isinstance(user, TelegramUser)
            assert user.username == "testuser"
            assert user.is_verified is True
            assert user.is_premium is True
            
            # Verify client was called correctly
            self.mock_client.get_user.assert_called_once_with("testuser")
            mock_handler.process_user_data.assert_called_once_with(mock_user_data)
    
    @patch('telegram_manager.services.message_service.MessageHandler', new_callable=MagicMock)
    @patch('telegram_manager.services.message_service.UserHandler', new_callable=MagicMock)
    def test_message_service_with_pydantic(self, mock_user_handler, mock_msg_handler):
        """Test MessageService works with Pydantic models."""
        # Create mock chat
        chat = Mock(spec=TelegramChat)
        chat.chat_id = 123456789
        chat.title = "Test Chat"
        
        # Prepare mock messages
        mock_messages = [
            TelegramMessageData(
                id="1",
                date=datetime.now(),
                message="Test message #test @user",
                from_id=987654321,
                entities=[
                    {"type": "hashtag", "offset": 13, "length": 5},
                    {"type": "mention", "offset": 19, "length": 5, "user_id": 111}
                ]
            ),
            TelegramMessageData(
                id="2",
                date=datetime.now(),
                message="Another message",
                from_id=987654321,
                telegram_media={"type": "photo", "id": 123},
                action={"type": "chatJoinedByLink", "users": [222]}
            )
        ]
        
        # Configure mocks
        self.mock_client.get_posts.return_value = mock_messages
        
        # Create mock processed messages
        msg1 = Mock(spec=TelegramMessage)
        msg1.message_id = 1
        msg1.chat = chat
        msg1.text = "Test message #test @user"
        
        msg2 = Mock(spec=TelegramMessage)
        msg2.message_id = 2
        msg2.chat = chat
        msg2.text = "Another message"
        
        mock_processed = [msg1, msg2]
        mock_msg_handler.batch_process_messages.return_value = mock_processed
        
        # Test service
        service = TelegramMessageService()
        messages = service.import_messages(
            self.mock_client,
            chat,
            limit=2,
            import_users=False
        )
        
        # Assertions
        assert len(messages) == 2
        assert all(isinstance(msg, TelegramMessage) for msg in messages)
        
        # Verify client was called correctly
        self.mock_client.get_posts.assert_called_once_with(
            123456789,
            limit=2,
            date_from=None,
            date_to=None
        )
    
    def test_error_handling_with_pydantic(self):
        """Test error handling with Pydantic validation."""
        from pydantic import ValidationError
        
        # Test invalid user data
        with pytest.raises(ValidationError) as exc_info:
            TelegramUserData(username="test")  # Missing required id
        
        errors = exc_info.value.errors()
        assert any(e["type"] == "missing" for e in errors)
        
        # Test invalid chat data - missing required id
        with pytest.raises(ValidationError) as exc_info:
            TelegramChatData(
                title="Test Chat",
                type="channel"
            )  # Missing required id field
        
        # Test invalid message data
        with pytest.raises(ValidationError) as exc_info:
            TelegramMessageData(
                id=123,
                date="invalid-date",  # Invalid timestamp
                message="test"
            )
        
        errors = exc_info.value.errors()
        assert any("date" in str(e) for e in errors)
    
    def test_data_transformation(self):
        """Test data transformation through Pydantic models."""
        # Test user data transformation
        user_data = TelegramUserData(
            id="123",
            username="test",
            first_name="Test ",  # Extra space
            last_name=" User",  # Extra space
            bot=True,
            deleted=True
        )
        
        # Verify transformations
        assert user_data.first_name == "Deleted"  # Changed due to deleted status
        assert user_data.last_name == "Account"  # Changed due to deleted status
        assert user_data.username is None  # Cleared due to deleted status
        
        # Test chat type normalization
        chat_data = TelegramChatData(
            id=456,
            title="Test Channel",
            type="channel",
            broadcast=True
        )
        
        assert chat_data.chat_type == "channel"
        
        # Test message entity extraction
        message_data = TelegramMessageData(
            id="789",
            date=datetime.now(),
            message="Check this #amazing #python code @friend1 @friend2!",
            entities=[
                {"type": "hashtag", "offset": 11, "length": 8},
                {"type": "hashtag", "offset": 20, "length": 7},
                {"type": "mention", "offset": 33, "length": 8, "user_id": 111},
                {"type": "mention", "offset": 42, "length": 8, "user_id": 222}
            ]
        )
        
        assert message_data.hashtags == ["amazing", "python"]
        assert message_data.mentions == ["friend1", "friend2"]


class TestTelegramBackwardCompatibility(TestCase):
    """Test backward compatibility with old code."""
    
    def test_v2_services_default(self):
        """Test that v2 services are the default import."""
        # Import services (should get v2 versions)
        from telegram_manager.services import (
            TelegramChatService,
            TelegramUserService,
            TelegramMessageService
        )
        
        # Should be v2 instances
        chat_service = TelegramChatService()
        user_service = TelegramUserService()
        message_service = TelegramMessageService()
        
        assert isinstance(chat_service, TelegramChatService)
        assert isinstance(user_service, TelegramUserService)
        assert isinstance(message_service, TelegramMessageService)
    
    def test_model_dump_compatibility(self):
        """Test converting Pydantic models back to dicts for legacy code."""
        # Create Pydantic model
        user = TelegramUserData(
            id="123",
            username="test",
            first_name="Test",
            verified=True
        )
        
        # Convert to dict for legacy code
        user_dict = user.model_dump(by_alias=True)
        
        # Verify structure matches old format
        assert user_dict["id"] == 123
        assert user_dict["username"] == "test"
        assert user_dict["verified"] is True
        assert "user_id" not in user_dict  # Uses alias 'id'
        
        # Test chat model
        chat = TelegramChatData(
            id=456,
            title="Test Chat",
            type="supergroup",
            megagroup=True
        )
        
        chat_dict = chat.model_dump(by_alias=True)
        assert chat_dict["id"] == 456
        assert chat_dict["type"] == "supergroup"