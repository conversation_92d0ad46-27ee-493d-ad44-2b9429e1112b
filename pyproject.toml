[project]
name = "socialmanager"
version = "0.1.0"
description = "Django-based social media manager for Telegram and Instagram"
requires-python = ">=3.13"
dependencies = [
    # Web framework
    "django>=5.2.1",
    # API clients
    "telethon>=1.40.0",
    "requests>=2.32.3",
    # Data processing
    "pydantic>=2.11.5",
    "python-dateutil>=2.9.0.post0",
    "pillow>=11.2.1",
    # Browser automation
    "playwright>=1.52.0",
    "playwright-stealth>=1.0.6",
    # Security & configuration
    "cryptography>=45.0.3",
    "python-decouple>=3.8",
    "dj-database-url>=3.0.0",
    # Logging
    "structlog>=25.4.0",
    "sentry-sdk>=1.40.0",
    "python-json-logger>=2.0.7",
    # Type annotations
    "annotated-types==0.7.0",
    "psycopg2-binary>=2.9.10",
    "celery>=5.5.3",
    "redis>=6.2.0",
    "flower>=2.0.1",
    "celery-stubs==0.1.3",
    "django-celery-beat>=2.8.1",
    # ASGI server
    "uvicorn[standard]>=0.30.0",
    "httpx>=0.27.0", # для async HTTP запросов
    # Для async обработки файлов
    "aiofiles>=23.2.1",
    # Google Cloud Storage для хранения медиафайлов
    "google-cloud-storage>=3.2.0",
    # MCP Server dependencies
    "fastmcp>=0.3.0",
    "asyncpg>=0.29.0",
    "python-multipart>=0.0.9",
    "pytest>=8.4.1",
    "pytest-django>=4.11.1",
    "pytest-asyncio>=1.0.0",
    "psutil>=7.0.0",
    # AST manipulation for migrations
    "astor>=0.8.1",
    "pytest-cov>=6.2.1",
]

[dependency-groups]
dev = [
    # Code formatting
    "black>=25.1.0",
    "ruff>=0.8.7",
    # Type checking
    "mypy>=1.16.0",
    "django-stubs>=5.2.0",
    "django-stubs-ext>=5.2.0",
    "types-cryptography>=********",
    "types-pillow>=10.2.0.20240822",
    "types-python-dateutil>=2.9.0.20250516",
    "types-requests>=2.32.0.20250602",
    "factory-boy>=3.3.3",
]

test = [
    # Testing framework
    "pytest>=8.4.0",
    "pytest-django>=4.11.1",
    "pytest-timeout>=2.4.0",
    "pytest-cov>=6.0.0",
    "pytest-xdist>=3.6.1",
    "pytest-sugar>=1.0.0",
    "pytest-asyncio>=0.21.0",
    "pytest-mock>=3.12.0",
    "factory-boy>=3.3.0",
    "faker>=30.0.0",
    "freezegun>=1.2.2",
    # Performance monitoring
    "psutil>=5.9.0",
]

[tool.mypy]
python_version = "3.13"
warn_return_any = true
warn_unused_configs = true
plugins = ["mypy_django_plugin.main", "pydantic.mypy"]

# Start with lenient settings
ignore_missing_imports = true
check_untyped_defs = false
disallow_untyped_defs = false
no_implicit_optional = false
strict_optional = false

# Temporarily disable problematic error codes
disable_error_code = ["misc", "override", "attr-defined"]
allow_untyped_decorators = true
allow_redefinition = true

# Exclude paths
exclude = [
    "migrations/",
    "activate/",
    ".venv/",
    "__pycache__/",
    "manage.py",
    "tests/",
    "test_*.py",
    "check_posts.py",
    "run_tests.py",
    "docs/",
]

# Django settings
[tool.django-stubs]
django_settings_module = "SocialManager.settings"
strict_settings = false
# Allow nullable fields without Optional
allow_any_generics = true

# Third-party libraries without stubs
[[tool.mypy.overrides]]
module = "telethon.*"
ignore_missing_imports = true

[[tool.mypy.overrides]]
module = "playwright.*"
ignore_missing_imports = true

[[tool.mypy.overrides]]
module = "playwright_stealth.*"
ignore_missing_imports = true

[[tool.mypy.overrides]]
module = "decouple.*"
ignore_missing_imports = true

# Django apps - gradual strictness
[[tool.mypy.overrides]]
module = "core.*"
check_untyped_defs = true

[[tool.mypy.overrides]]
module = "telegram_manager.*"
check_untyped_defs = true

[[tool.mypy.overrides]]
module = "instagram_manager.*"
check_untyped_defs = true

# Override for telegram schemas to ignore pydantic plugin error
[[tool.mypy.overrides]]
module = "telegram_manager.schemas.telegram"
ignore_errors = true

# Pydantic plugin configuration
[tool.pydantic-mypy]
init_forbid_extra = true
init_typed = true
warn_required_dynamic_aliases = true

# Coverage configuration
[tool.coverage.run]
source = ["."]
omit = [
    "*/migrations/*",
    "*/tests/*",
    "*/__pycache__/*",
    "*/venv/*",
    "*/.venv/*",
    "manage.py",
    "*/admin.py",
    "*/apps.py",
    "*/urls.py",
    "*/wsgi.py",
    "*/asgi.py",
    "run_tests.py",
    "scripts/*",
]
branch = true

[tool.coverage.report]
precision = 2
show_missing = true
skip_covered = false
skip_empty = true
sort = "cover"
exclude_lines = [
    # Стандартные исключения
    "pragma: no cover",
    "def __repr__",
    "def __str__",
    "raise AssertionError",
    "raise NotImplementedError",
    "if __name__ == .__main__.:",
    "if TYPE_CHECKING:",
    "if typing.TYPE_CHECKING:",
    "@abstractmethod",
    "@abc.abstractmethod",
    # Django specific
    "def get_absolute_url",
    "class Meta:",
]

[tool.coverage.html]
directory = "htmlcov"

[tool.coverage.xml]
output = "coverage.xml"
