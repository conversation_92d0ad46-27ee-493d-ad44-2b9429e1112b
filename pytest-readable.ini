[pytest]
DJANGO_SETTINGS_MODULE = tests.settings
# Match all Django test discovery patterns
python_files = test_*.py *_test.py *_tests.py tests.py
# Include all directories with tests
testpaths = 
    tests
    core
    telegram_manager
    instagram_manager
    monitoring
    .
timeout = 600
timeout_method = thread

# Readable output configuration with progress indication
addopts = 
    --tb=short
    --strict-markers
    --disable-warnings
    -p no:warnings
    --color=yes
    --create-db
    # Show test progress with module names
    -v
    # Show failed tests summary at the end
    -rf
    # Show error tests summary
    -rE
    # Show skipped tests summary
    -rs
    # Show xfailed tests summary
    -rx
    # Show durations of 10 slowest tests
    --durations=10
    # Capture output but show only on failures
    --capture=fd
    # Better assertion introspection
    --assert=plain

# Console output settings
console_output_style = progress

# Logging configuration - capture logs but don't show them unless test fails
log_cli = false
log_cli_level = CRITICAL
log_file_level = CRITICAL
log_capture = true
log_cli_format = %(asctime)s [%(levelname)8s] %(name)s: %(message)s
log_cli_date_format = %H:%M:%S

# Filter warnings
filterwarnings =
    ignore::DeprecationWarning
    ignore::django.utils.deprecation.RemovedInDjango50Warning
    ignore::django.utils.deprecation.RemovedInDjango51Warning
    ignore:.*CacheKeyWarning.*
    ignore::UserWarning
    ignore::RuntimeWarning
    ignore:DateTimeField .* received a naive datetime:RuntimeWarning:django.db.models.fields
    # Show important warnings
    default::Warning

# Markers
markers =
    django_db: mark a test as needing the database
    asyncio: mark a test as using asyncio
    slow: mark a test as slow running
    integration: mark a test as integration test
    unit: mark a test as unit test
    skip: skip test
    xfail: mark test as expected to fail

# Test discovery patterns
norecursedirs = .git .tox .env .venv __pycache__ htmlcov *.egg-info